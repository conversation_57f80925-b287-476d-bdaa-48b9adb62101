<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
	<include resource="org/springframework/boot/logging/logback/base.xml" />
 
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder 
			by default
		<encoder>
			<pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%c{1}][%t] - %m%n)</pattern>
		</encoder>
	</appender>

	<appender name="INFO_FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>./logs/info.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>./logs/info-%d{yyyyMMdd}.log.%i
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>20MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>2</maxHistory>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%c{1}][%t] - %m%n)
			</Pattern>
		</layout>
	</appender>

	<appender name="ERROR_FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<File>./logs/error.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>./logs/error-%d{yyyyMMdd}.log.%i
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>50MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>2</maxHistory>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%c{1}][%t] - %m%n)</Pattern>
		</layout>
	</appender>

	<!-- hibernate日志输入 -->
	<!-- <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" 
		/> <logger name="org.hibernate.type.descriptor.sql.BasicExtractor" level="TRACE" 
		/> <logger name="org.hibernate.SQL" level="INFO" /> <logger name="org.hibernate.engine.QueryParameters" 
		level="INFO" /> <logger name="org.hibernate.engine.query.HQLQueryPlan" level="INFO" 
		/> -->
    <logger level="debug" additivity="true" name="com.zte.uedm.battery" />
	<root level="DEBUG">
		<!-- <appender-ref ref="STDOUT" /> -->
		<appender-ref ref="INFO_FILE" />
		<appender-ref ref="ERROR_FILE" />
	</root>

	<!-- <logger name="org.springframework" level="DEBUG" /> -->


</configuration>