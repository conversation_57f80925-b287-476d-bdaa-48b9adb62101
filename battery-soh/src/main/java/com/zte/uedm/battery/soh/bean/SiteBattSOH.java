package com.zte.uedm.battery.soh.bean;

import java.util.List;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@EqualsAndHashCode
public class SiteBattSOH {
	//站点oid
	private String oid;
	//站点位置
	private String location;
	//站点名称
	private String siteName;
	//供电场景
	private String powerSupplyScene;
	private String siteGrade;
	//电极材质 :铅酸,铁锂
	private String electrodeMaterial;
	//电池组数量
	private Integer battCounts;
	private Integer healthCounts;
	private Integer subHealthCounts;
	private Integer faultCounts;

}
