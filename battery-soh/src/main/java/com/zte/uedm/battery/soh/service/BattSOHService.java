package com.zte.uedm.battery.soh.service;

import java.util.List;
import java.util.Map;

import com.zte.uedm.battery.soh.bean.BattSOHDetail;
import com.zte.uedm.battery.soh.bean.BattSOHPageView;
import com.zte.uedm.common.exception.UedmException;

public interface BattSOHService
{
    /**
     * 获取电池健康页面的统计信息
     * 
     * @param oids
     *            分组oid
     * @param conditions
     *            过滤站点的条件
     * @return 页面详情的统计信息
     * @throws UedmException
     */
    public BattSOHPageView getBattSOHPageView(List<String> oids, Map<String, List<String>> conditions,
            String languageOption) throws UedmException;

    /**
     * 根据站点siteId获取站点下所有电池的健康信息
     * 
     * @param siteId
     *            站点siteId
     * @return 所有电池的健康信息
     * @throws UedmException
     */
    public List<BattSOHDetail> getBattsSOHDetailBySiteId(String siteId, String languageOption) throws UedmException;

    public default void forCI(){}
}
