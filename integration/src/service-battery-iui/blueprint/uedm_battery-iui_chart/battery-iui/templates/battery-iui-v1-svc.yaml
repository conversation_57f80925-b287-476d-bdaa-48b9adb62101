apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: MsbServiceSlice
metadata:
  name: {{ .Release.Name }}-battery-iui-v1
  namespace: {{ .Values.serviceImage.tenant }}
  labels:
    msb.zte.com.cn/service-name: uedm-battery
    msb.zte.com.cn/service-version: v1
    msb.zte.com.cn/release: {{ .Values.release }}
spec:
  selector:
    # 用于选择实际提供服务的实例，需要和 Deployment 配置中的spec.template.metadata.labels匹配 (key和value均一致)
    name: uedm-battery
  ports:
    - name: ui
      protocol: HTTP
      targetPort: {{ .Values.ports.battery_iui }}
  serviceInfo:
    url: /iui/uedm-battery
    networkPlaneType: net_api