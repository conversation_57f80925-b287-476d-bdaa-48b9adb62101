apiVersion: {{ template "oki.pbc.apiVersion" . }}
kind: MsbApi
metadata:
  name: {{ include "oki.fullname" . }}battery-iui-v1
  namespace: {{.Values.serviceImage.tenant}}
  labels:
    componentName: {{ include "oki.fullname" . }}battery-iui-pbc
    pbc.apiServiceName: battery-iui-v1
spec:
  httpRules:
    - match:
        path: /iui/uedm-battery
        protocol: UI
        rewriteTarget: /iui/uedm-battery
      backend:
        service:
          name: {{ .Release.Name }}-battery-iui-v1
          portName: ui
      advancedConfig:
        lbPolicy: round-robin