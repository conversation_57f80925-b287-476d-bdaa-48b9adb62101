#!/bin/sh

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME
 
if [ -f "/home/<USER>/initGlobalEnv.sh" ]; then
. "/home/<USER>/initGlobalEnv.sh"
else
echo "can not found /home/<USER>/initGlobalEnv.sh"
fi

if [ -f "$RUNHOME/setenv.sh" ]; then
. "$RUNHOME/setenv.sh"
else
echo "can not found $RUNHOME/setenv.sh"
fi

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME


if [ -f "$RUNHOME/setenv.sh" ]; then
  . "$RUNHOME/setenv.sh"
else
echo "can not found $RUNHOME/setenv.sh"
fi

if [ -f "$RUNHOME/esightenv.sh" ]; then
  . "$RUNHOME/esightenv.sh"
else
echo "can not found $RUNHOME/esightenv.sh"
fi

echo ================== ENV_INFO  =============================================
echo @RUNHOME@  $RUNHOME
echo @JAVA_BASE@  $JAVA_BASE
echo @Main_Class@  $Main_Class
echo @APP_INFO@  $APP_INFO
echo @Main_Conf@ $Main_Conf
echo ==========================================================================

echo start $APP_INFO ...

JAVA="$JAVA_HOME/bin/java"
JAVA_OPTS="$JAVA_OPTS -Xms$MIN_DUMP_SIZE -Xmx$MAX_DUMP_SIZE $JAVA_GLOBAL_OPTS $JVM_GC_OPTS"
JAVA_OPTS="$JAVA_OPTS -DMS_APP_NAME=JMS_DEMO_MICROSERVICENAME -DBoot_Process_Duration=900 -Ddb_version=$db_version -Dscene=$scene"

port=8777
#JAVA_OPTS="$JAVA_OPTS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$port,server=y,suspend=n"
CLASS_PATH="$LIB_DIRS:$RUNHOME/:$RUNHOME/$Main_JAR"
echo ================== RUN_INFO  =============================================
echo @JAVA_HOME@ $JAVA_HOME
echo @JAVA@ $JAVA
echo @JAVA_OPTS@ $JAVA_OPTS
echo @CLASS_PATH@ $CLASS_PATH
echo @LIB_DIRS@ $LIB_DIRS
echo ==========================================================================

echo @JAVA@ $JAVA
echo @JAVA_CMD@
"$JAVA" $JAVA_OPTS -classpath "$CLASS_PATH" $Main_Class

