ALTER TABLE IF EXISTS file_upload_record_temp ADD COLUMN IF NOT EXISTS integrity_code text NOT NULL DEFAULT '';
ALTER TABLE IF EXISTS file_upload_record ADD COLUMN IF NOT EXISTS integrity_code text NOT NULL DEFAULT '';

ALTER TABLE IF EXISTS peak_shift_task ALTER COLUMN  "name" TYPE text USING "name"::text;


--错峰设备属性配置表
CREATE TABLE IF NOT EXISTS peak_shift_config_detail
(
    device_id text not null primary key,
    strategy_type text not null,
    strategy_code text,
    gmt_create text,
    creator text,
    gmt_modified text,
    updater text
);
COMMENT ON TABLE peak_shift_config_detail is '错峰设备属性配置表';
COMMENT ON COLUMN peak_shift_config_detail.device_id is '采集器id';
COMMENT ON COLUMN peak_shift_config_detail.strategy_type is '错峰策略类型';
COMMENT ON COLUMN peak_shift_config_detail.strategy_code is '错峰策略code值';
COMMENT ON COLUMN peak_shift_config_detail.gmt_create is '创建时间';
COMMENT ON COLUMN peak_shift_config_detail.creator is '创建人';
COMMENT ON COLUMN peak_shift_config_detail.gmt_modified is '更新时间';
COMMENT ON COLUMN peak_shift_config_detail.updater is '更新人';