--修改batt_risk_rule_source表测点
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_001' where mapping_id ='battery.cell.volt.battery_001';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_002' where mapping_id ='battery.cell.volt.battery_002';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_003' where mapping_id ='battery.cell.volt.battery_003';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_004' where mapping_id ='battery.cell.volt.battery_004';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_005' where mapping_id ='battery.cell.volt.battery_005';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_006' where mapping_id ='battery.cell.volt.battery_006';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_007' where mapping_id ='battery.cell.volt.battery_007';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_008' where mapping_id ='battery.cell.volt.battery_008';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_009' where mapping_id ='battery.cell.volt.battery_009';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_010' where mapping_id ='battery.cell.volt.battery_0010';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_011' where mapping_id ='battery.cell.volt.battery_011';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_012' where mapping_id ='battery.cell.volt.battery_0012';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_013' where mapping_id ='battery.cell.volt.battery_013';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_014' where mapping_id ='battery.cell.volt.battery_0014';
update batt_risk_rule_source set mapping_id = 'battery.cellvoltage.battery_015' where mapping_id ='battery.cell.volt.battery_015';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_001' where mapping_id ='battery.cell.temp.battery_001';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_002' where mapping_id ='battery.cell.temp.battery_002';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_003' where mapping_id ='battery.cell.temp.battery_003';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_004' where mapping_id ='battery.cell.temp.battery_004';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_005' where mapping_id ='battery.cell.temp.battery_005';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_006' where mapping_id ='battery.cell.temp.battery_006';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_007' where mapping_id ='battery.cell.temp.battery_007';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_008' where mapping_id ='battery.cell.temp.battery_008';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_009' where mapping_id ='battery.cell.temp.battery_009';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_010' where mapping_id ='battery.cell.temp.battery_010';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_011' where mapping_id ='battery.cell.temp.battery_011';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_012' where mapping_id ='battery.cell.temp.battery_012';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_013' where mapping_id ='battery.cell.temp.battery_013';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_014' where mapping_id ='battery.cell.temp.battery_014';
update batt_risk_rule_source set mapping_id = 'battery.celltemp.battery_015' where mapping_id ='battery.cell.temp.battery_015';
update batt_risk_rule_source set mapping_id = 'battery.voltage' where mapping_id ='battery.volt';
update batt_risk_rule_source set mapping_id = 'battery.sohai' where mapping_id ='battery.health';
update batt_risk_rule_source set mapping_id = 'battery.current' where mapping_id ='battery.curr';
update batt_risk_rule_source set mapping_id = 'battery.remainingsoc' where mapping_id ='battery.prst.soc';
update batt_risk_rule_source set mapping_id = 'battery.cycletimes' where mapping_id ='battery.accum.cycle.times';
update batt_risk_rule_source set mapping_id = 'battery.temperature' where mapping_id ='battery.temp';
update batt_risk_rule_source set mapping_id = 'battery.totalvoltage' where mapping_id ='battery.cells.volt';

--修改battery_base_info表结构
ALTER TABLE battery_base_info ALTER COLUMN start_date TYPE character varying(50);
ALTER TABLE battery_base_info ALTER COLUMN maintenance_period TYPE character varying(50);