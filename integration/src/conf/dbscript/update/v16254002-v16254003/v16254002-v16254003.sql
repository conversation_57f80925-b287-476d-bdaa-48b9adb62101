-- 虚拟电厂维度表
CREATE TABLE if not exists frequency_modulation_overview_dimensions
(
        id character varying(30) NOT NULL,
        user_name character varying(50) NOT NULL,
        sequence INTEGER NOT NULL,
        name text,
        enable BOOLEAN,
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT frequency_modulation_overview_dimensions_id_user_name_key PRIMARY KEY (id,user_name)
);

COMMENT ON TABLE frequency_modulation_overview_dimensions IS '虚拟电厂维度表';

COMMENT ON COLUMN frequency_modulation_overview_dimensions.id IS '虚拟电厂维度id';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.user_name IS '用户名';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.sequence IS '顺序';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.name IS '维度名称-json中英文';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.enable IS '是否启用';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.creator IS '创建者';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.gmt_create IS '创建时间';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.updater IS '更新者';
COMMENT ON COLUMN frequency_modulation_overview_dimensions.gmt_modified IS '更新时间';

-- 虚拟电厂调频记录表
CREATE TABLE if not exists frequency_record
(
        device_log_id character varying(50) NOT NULL,
        mo_id character varying(50) NOT NULL,
        device_id character varying(50) NULL,
        start_time timestamptz NULL,
        end_time timestamptz NULL,
        work_model character varying(50) NULL,
        response_time character varying(50) NULL,
        create_time timestamptz NULL,
        update_time timestamptz NULL,
        frequency text NULL,
        CONSTRAINT frequency_record_device_log_id_key PRIMARY KEY (device_log_id)
);

COMMENT ON TABLE frequency_record IS '虚拟电厂调频记录表';

COMMENT ON COLUMN frequency_record.device_log_id IS '事件唯一标识id';
COMMENT ON COLUMN frequency_record.mo_id IS '监控对象id';
COMMENT ON COLUMN frequency_record.device_id IS '监控设备id';
COMMENT ON COLUMN frequency_record.start_time IS '调频开始时间';
COMMENT ON COLUMN frequency_record.end_time IS '调频结束时间';
COMMENT ON COLUMN frequency_record.work_model IS '工作模式';
COMMENT ON COLUMN frequency_record.response_time IS '设备响应时间';
COMMENT ON COLUMN frequency_record.create_time IS '创建时间';
COMMENT ON COLUMN frequency_record.update_time IS '更新时间';
COMMENT ON COLUMN frequency_record.frequency IS '调节功率';

-- 虚拟电厂实际功率调频记录表
CREATE TABLE if not exists frequency_std_record
(
	record_time timestamptz NOT NULL,
	mo_id varchar(50) NOT NULL,
	value varchar(50) NOT NULL,
	CONSTRAINT frequency_std_record_key PRIMARY KEY (record_time, mo_id)
);

COMMENT ON TABLE frequency_std_record IS '虚拟电厂实际功率调频记录表';

COMMENT ON COLUMN frequency_std_record.record_time IS '调频时间';
COMMENT ON COLUMN frequency_std_record.mo_id IS '监控对象id';
COMMENT ON COLUMN frequency_std_record.value IS '实际调频功率';

-- Started by AICoder, pid:248e28eeb064762149ae0ab8028ae5125e00cc8b
-- 错峰设备实时配置文件表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_device_file" (
                                                                 "device_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                 "file_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                 "status" bool NOT NULL,
                                                                 "creator" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                 "gmt_create" varchar(19) COLLATE "pg_catalog"."default" NOT NULL,
                                                                 "updater" varchar(50) COLLATE "pg_catalog"."default",
                                                                 "gmt_modified" varchar(19) COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."peak_shift_device_file"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."peak_shift_device_file"."file_id" IS '文件id';
COMMENT ON COLUMN "public"."peak_shift_device_file"."status" IS '状态';
COMMENT ON COLUMN "public"."peak_shift_device_file"."creator" IS '创建者';
COMMENT ON COLUMN "public"."peak_shift_device_file"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."peak_shift_device_file"."updater" IS '更新者';
COMMENT ON COLUMN "public"."peak_shift_device_file"."gmt_modified" IS '更新时间';
COMMENT ON TABLE "public"."peak_shift_device_file" IS 'Backup Type: basicTableData 错峰设备实时配置文件表';

ALTER TABLE IF EXISTS "public"."peak_shift_device_file" DROP CONSTRAINT IF EXISTS "peak_shift_device_file_key";
ALTER TABLE IF EXISTS "public"."peak_shift_device_file" ADD CONSTRAINT "peak_shift_device_file_key" PRIMARY KEY ("device_id");

-- 错峰设备索引实时启停状态表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_device_index_status" (
                                                                         "index" int4 NOT NULL,
                                                                         "device_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                                         "status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                                         "gmt_create" timestamptz(6) NOT NULL,
                                                                         "gmt_modified" timestamptz(6)
);

COMMENT ON COLUMN "public"."peak_shift_device_index_status"."index" IS '索引';
COMMENT ON COLUMN "public"."peak_shift_device_index_status"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."peak_shift_device_index_status"."status" IS '状态(启用/停用/未知)';
COMMENT ON COLUMN "public"."peak_shift_device_index_status"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."peak_shift_device_index_status"."gmt_modified" IS '更新时间';
COMMENT ON TABLE "public"."peak_shift_device_index_status" IS 'Backup Type: basicTableData 错峰设备索引实时启停状态表';

ALTER TABLE IF EXISTS "public"."peak_shift_device_index_status" DROP CONSTRAINT IF EXISTS "peak_shift_device_index_status_key";
ALTER TABLE IF EXISTS "public"."peak_shift_device_index_status" ADD CONSTRAINT "peak_shift_device_index_status_key" PRIMARY KEY ("index", "device_id");

-- 错峰设备实时启停状态表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_device_status" (
                                                                   "id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "gmt_create" timestamptz(6) NOT NULL,
                                                                   "gmt_modified" timestamptz(6)
);

COMMENT ON COLUMN "public"."peak_shift_device_status"."id" IS '设备id';
COMMENT ON COLUMN "public"."peak_shift_device_status"."status" IS '状态(启用/停用/未知/部分启停)';
COMMENT ON COLUMN "public"."peak_shift_device_status"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."peak_shift_device_status"."gmt_modified" IS '更新时间';
COMMENT ON TABLE "public"."peak_shift_device_status" IS 'Backup Type: basicTableData 错峰设备实时启停状态表';

ALTER TABLE IF EXISTS "public"."peak_shift_device_status" DROP CONSTRAINT IF EXISTS "peak_shift_device_status_key";
ALTER TABLE IF EXISTS "public"."peak_shift_device_status" ADD CONSTRAINT "peak_shift_device_status_key" PRIMARY KEY ("id");

-- 错峰设备策略关联表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_device_strategy_detail" (
                                                                            "id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                            "template_num" int4 NOT NULL,
                                                                            "mode" int4 NOT NULL,
                                                                            "holiday_mode" int4 NOT NULL,
                                                                            "version" text COLLATE "pg_catalog"."default",
                                                                            "holiday_date_str" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                            "dates_and_temp" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                            "template_detail_str" text COLLATE "pg_catalog"."default" NOT NULL
);

COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."id" IS 'id';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."template_num" IS '策略模板个数';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."mode" IS '策略模式（0：日，1：周，2：月）';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."holiday_mode" IS '假期采用模板';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."version" IS '配置模板版本';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."holiday_date_str" IS '假期日期';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."dates_and_temp" IS '编号对应模板';
COMMENT ON COLUMN "public"."peak_shift_device_strategy_detail"."template_detail_str" IS '模板详情str';
COMMENT ON TABLE "public"."peak_shift_device_strategy_detail" IS 'Backup Type: basicTableData 设备与策略的关联表';

ALTER TABLE IF EXISTS "public"."peak_shift_device_strategy_detail" DROP CONSTRAINT IF EXISTS "peak_shift_device_strategy_detail_pkey";
ALTER TABLE IF EXISTS "public"."peak_shift_device_strategy_detail" ADD CONSTRAINT "peak_shift_device_strategy_detail_pkey" PRIMARY KEY ("id");

-- 错峰设备与策略的关联表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_device_strategy" (
                                                                     "id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                     "device_id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                     "interval_strategy_id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                     "version" int4 NOT NULL,
                                                                     "start_date" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                     "end_date" text COLLATE "pg_catalog"."default" NOT NULL
);

COMMENT ON COLUMN "public"."peak_shift_device_strategy"."id" IS 'id';
COMMENT ON COLUMN "public"."peak_shift_device_strategy"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."peak_shift_device_strategy"."interval_strategy_id" IS '策略id';
COMMENT ON COLUMN "public"."peak_shift_device_strategy"."version" IS '版本号';
COMMENT ON COLUMN "public"."peak_shift_device_strategy"."start_date" IS '开始时间';
COMMENT ON COLUMN "public"."peak_shift_device_strategy"."end_date" IS '结束时间';
COMMENT ON TABLE "public"."peak_shift_device_strategy" IS 'Backup Type: basicTableData 设备与策略的关联表';

ALTER TABLE IF EXISTS "public"."peak_shift_device_strategy" DROP CONSTRAINT IF EXISTS "peak_shift_device_strategy_pkey";
ALTER TABLE IF EXISTS "public"."peak_shift_device_strategy" ADD CONSTRAINT "peak_shift_device_strategy_pkey" PRIMARY KEY ("id");

-- 错峰策略模板文件表
CREATE TABLE IF NOT EXISTS "public"."peak_shift_template_file" (
                                                                   "id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "name" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "version" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "file_id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "description" text COLLATE "pg_catalog"."default",
                                                                   "creator" text COLLATE "pg_catalog"."default" NOT NULL,
                                                                   "updater" text COLLATE "pg_catalog"."default",
                                                                   "gmt_create" timestamptz(6) NOT NULL,
                                                                   "gmt_modified" timestamptz(6)
);

COMMENT ON COLUMN "public"."peak_shift_template_file"."id" IS '模板id';
COMMENT ON COLUMN "public"."peak_shift_template_file"."name" IS '模板名称';
COMMENT ON COLUMN "public"."peak_shift_template_file"."version" IS '版本号';
COMMENT ON COLUMN "public"."peak_shift_template_file"."file_id" IS '文件id';
COMMENT ON COLUMN "public"."peak_shift_template_file"."description" IS '描述';
COMMENT ON COLUMN "public"."peak_shift_template_file"."creator" IS '创建者';
COMMENT ON COLUMN "public"."peak_shift_template_file"."updater" IS '更新者';
COMMENT ON COLUMN "public"."peak_shift_template_file"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."peak_shift_template_file"."gmt_modified" IS '更新时间';
COMMENT ON TABLE "public"."peak_shift_template_file" IS 'Backup Type: basicTableData 错峰策略模板文件表';

ALTER TABLE IF EXISTS "public"."peak_shift_template_file" DROP CONSTRAINT IF EXISTS "peak_shift_template_file_pkey";
ALTER TABLE IF EXISTS "public"."peak_shift_template_file" ADD CONSTRAINT "peak_shift_template_file_pkey" PRIMARY KEY ("id");

-- 错峰监控维度表
CREATE TABLE IF NOT EXISTS "public"."peakshift_monitor_dimensions" (
                                                                       "id" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                                                       "user_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                       "sequence" int4 NOT NULL,
                                                                       "name" text COLLATE "pg_catalog"."default",
                                                                       "enable" bool,
                                                                       "creator" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                       "gmt_create" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                                       "updater" varchar(50) COLLATE "pg_catalog"."default",
                                                                       "gmt_modified" varchar(50) COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."id" IS '错峰监控维度id';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."user_name" IS '用户名';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."sequence" IS '顺序';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."name" IS '维度名称-json中英文';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."enable" IS '是否启用';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."creator" IS '创建者';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."updater" IS '更新者';
COMMENT ON COLUMN "public"."peakshift_monitor_dimensions"."gmt_modified" IS '更新时间';

ALTER TABLE IF EXISTS "public"."peakshift_monitor_dimensions" DROP CONSTRAINT IF EXISTS "peakshift_monitor_dimensions_id_user_name_key";
ALTER TABLE IF EXISTS "public"."peakshift_monitor_dimensions" ADD CONSTRAINT "peakshift_monitor_dimensions_id_user_name_key" PRIMARY KEY ("id", "user_name");

-- 错峰监控记录表
CREATE TABLE IF NOT EXISTS "public"."up_download_file" (
                                                           "id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "name" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "original_name" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "file_path" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "file_suffix" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "file_type" int4 NOT NULL,
                                                           "module" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "file_size" text COLLATE "pg_catalog"."default",
                                                           "file_length" int4,
                                                           "operator" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "gmt_create" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "gmt_modified" text COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."up_download_file"."id" IS '主键id';
COMMENT ON COLUMN "public"."up_download_file"."name" IS '文件名';
COMMENT ON COLUMN "public"."up_download_file"."original_name" IS '原始文件名';
COMMENT ON COLUMN "public"."up_download_file"."file_path" IS '文件路径';
COMMENT ON COLUMN "public"."up_download_file"."file_suffix" IS '后缀名';
COMMENT ON COLUMN "public"."up_download_file"."file_type" IS '业务类型';
COMMENT ON COLUMN "public"."up_download_file"."module" IS '功能模块';
COMMENT ON COLUMN "public"."up_download_file"."file_size" IS '文件大小:前端展示';
COMMENT ON COLUMN "public"."up_download_file"."file_length" IS '文件二进制长度';
COMMENT ON COLUMN "public"."up_download_file"."operator" IS '操作人';
COMMENT ON COLUMN "public"."up_download_file"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "public"."up_download_file"."gmt_modified" IS '修改时间';
COMMENT ON TABLE "public"."up_download_file" IS 'Backup Type: basicTableData 导入导出文件表';

ALTER TABLE IF EXISTS "public"."up_download_file" DROP CONSTRAINT IF EXISTS "up_download_file_pkey";
ALTER TABLE IF EXISTS "public"."up_download_file" ADD CONSTRAINT "up_download_file_pkey" PRIMARY KEY ("id");

-- 错峰导入导出任务表
CREATE TABLE IF NOT EXISTS "public"."up_download_task" (
                                                           "id" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "name" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "task_type" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "task_param" text COLLATE "pg_catalog"."default",
                                                           "state" int4 NOT NULL,
                                                           "progress" int4 NOT NULL,
                                                           "file_id" text COLLATE "pg_catalog"."default",
                                                           "create_user" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "gmt_create" text COLLATE "pg_catalog"."default" NOT NULL,
                                                           "gmt_modify" text COLLATE "pg_catalog"."default" NOT NULL
);

COMMENT ON COLUMN "public"."up_download_task"."id" IS '主键id';
COMMENT ON COLUMN "public"."up_download_task"."name" IS '任务名称';
COMMENT ON COLUMN "public"."up_download_task"."task_type" IS '任务类型';
COMMENT ON COLUMN "public"."up_download_task"."task_param" IS '任务参数';
COMMENT ON COLUMN "public"."up_download_task"."state" IS '任务状态';
COMMENT ON COLUMN "public"."up_download_task"."progress" IS '任务执行进度';
COMMENT ON COLUMN "public"."up_download_task"."file_id" IS '文件id';
COMMENT ON COLUMN "public"."up_download_task"."create_user" IS '创建任务的用户';
COMMENT ON COLUMN "public"."up_download_task"."gmt_create" IS '任务创建时间';
COMMENT ON COLUMN "public"."up_download_task"."gmt_modify" IS '任务更新时间';
COMMENT ON TABLE "public"."up_download_task" IS 'Backup Type: basicTableData 导入导出任务表';

ALTER TABLE IF EXISTS "public"."up_download_task" DROP CONSTRAINT IF EXISTS "up_download_task_pkey";
ALTER TABLE IF EXISTS "public"."up_download_task" ADD CONSTRAINT "up_download_task_pkey" PRIMARY KEY ("id");
-- Ended by AICoder, pid:248e28eeb064762149ae0ab8028ae5125e00cc8b