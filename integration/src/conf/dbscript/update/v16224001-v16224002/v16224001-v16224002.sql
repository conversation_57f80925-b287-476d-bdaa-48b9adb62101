CREATE TABLE if not exists price_strategy
(
    id 						text NOT NULL,
    strategy_type 			smallint NOT NULL,
    scope_strategy_id 		text,
    status 					smallint not null,
    effective_time  		text NOT NULL,
    expiration_time 		text,
    price 					text,
    gmt_create 				text NOT NULL,
    gmt_modified 			text,
    primary key(id)
);
COMMENT ON TABLE price_strategy IS '电价策略表';
COMMENT ON COLUMN price_strategy.id IS '主键id';
COMMENT ON COLUMN price_strategy.strategy_type IS '0:低谷 1：平 2：峰 3：尖峰';
COMMENT ON COLUMN price_strategy.scope_strategy_id IS '范围策略ID';
COMMENT ON COLUMN price_strategy.status IS '0：待生效 1：生效中 2：已结束';
COMMENT ON COLUMN price_strategy.effective_time IS '生效时间：启用时间 yyyy-mm-dd';
COMMENT ON COLUMN price_strategy.expiration_time IS '失效时间：停用时间 yyyy-mm-dd';
COMMENT ON COLUMN price_strategy.price IS '电价';
COMMENT ON COLUMN price_strategy.gmt_create IS '生成时间';
COMMENT ON COLUMN price_strategy.gmt_modified IS '修改时间';

truncate table price_strategy;
insert into price_strategy values
('49b64c92-add2-4b1e-a580-12be066b8718',0,null,1,'2022-07-28',null,'1','2022-07-28 00:00:00',null)
,('dde1507c-d3fd-40b9-bf86-4af582e2f2cb',1,null,1,'2022-07-28',null,'2','2022-07-28 00:00:00',null)
,('5f9a026d-0c72-4bb2-80e7-aa9e9ed58533',2,null,1,'2022-07-28',null,'3','2022-07-28 00:00:00',null)
,('cd445862-c92a-4970-bcb3-2d7e94023eac',3,null,1,'2022-07-28',null,'4','2022-07-28 00:00:00',null);

CREATE TABLE if not exists power_system_kwh_gain
(
	monitor_device_id		text,
    power_system_id 		text	 NOT NULL,
    date_str 				text	 NOT NULL,
    month_str 				text	 NOT NULL,
    year_str 				text	 NOT NULL,
    site_id  				text	 NOT NULL,
    charge 					text	 NOT NULL,
    discharge 				text	 NOT NULL,
    charge_delta			text	 NOT NULL,
    discharge_delta			text	 NOT NULL,
    gain_charge_delta		text	 NOT NULL,
    gain_discharge_delta	text	 NOT NULL,
    price 					text	 NOT NULL,
    strategy_type 			smallint not null
);
COMMENT ON TABLE power_system_kwh_gain IS '电价策略表';
COMMENT ON COLUMN power_system_kwh_gain.monitor_device_id IS '电源系统关联的监控设备ID';
COMMENT ON COLUMN power_system_kwh_gain.power_system_id IS '电源系统ID';
COMMENT ON COLUMN power_system_kwh_gain.strategy_type IS '0:低谷 1：平 2：峰 3：尖峰';
COMMENT ON COLUMN power_system_kwh_gain.date_str IS '统计日期，yyyy-mm-dd';
COMMENT ON COLUMN power_system_kwh_gain.month_str IS '月份，yyyy-mm';
COMMENT ON COLUMN power_system_kwh_gain.year_str IS '年，yyyy';
COMMENT ON COLUMN power_system_kwh_gain.site_id IS '失效时间：停用时间 yyyy-mm-dd';
COMMENT ON COLUMN power_system_kwh_gain.charge IS '充电量累积量(kwh)';
COMMENT ON COLUMN power_system_kwh_gain.discharge IS '放电量累积量(kwh)';
COMMENT ON COLUMN power_system_kwh_gain.charge_delta IS '增量充电量(kwh)';
COMMENT ON COLUMN power_system_kwh_gain.discharge_delta IS '增量放电量(kwh)';
COMMENT ON COLUMN power_system_kwh_gain.gain_charge_delta IS '充电费用';
COMMENT ON COLUMN power_system_kwh_gain.gain_discharge_delta IS '放电价值';
COMMENT ON COLUMN power_system_kwh_gain.price IS '电价';
CREATE INDEX if not exists power_system_kwh_gain_date_str ON power_system_kwh_gain(date_str);

CREATE TABLE if not exists power_system_daily_gain
(
	monitor_device_id		text,
    power_system_id 		text NOT NULL,
    date_str 				text NOT NULL,
    gain_total		 		text,
    gain_delta		 		text,
    charge					text,
    discharge				text,
    primary key(power_system_id,date_str)
);
COMMENT ON TABLE power_system_daily_gain IS '电价策略表';
COMMENT ON COLUMN power_system_daily_gain.monitor_device_id IS '电源系统关联的监控设备ID';
COMMENT ON COLUMN power_system_daily_gain.power_system_id IS '电源系统ID';
COMMENT ON COLUMN power_system_daily_gain.date_str IS '统计日期，yyyy-mm-dd';
COMMENT ON COLUMN power_system_daily_gain.gain_total IS '收益累积量';
COMMENT ON COLUMN power_system_daily_gain.gain_delta IS '收益每日增量';
COMMENT ON COLUMN power_system_daily_gain.charge IS '充电量累计值';
COMMENT ON COLUMN power_system_daily_gain.discharge IS '放电量累计值';


ALTER TABLE public.batt_health_status_eval ADD IF NOT EXISTS pre_unknown_reason text ;
COMMENT ON COLUMN batt_health_status_eval.pre_unknown_reason IS '上次无法评估原因';
ALTER TABLE public.batt_health_status_eval ADD IF NOT EXISTS status_modified_time timestamptz ;
COMMENT ON COLUMN batt_health_status_eval.status_modified_time IS '状态变更时间';

CREATE TABLE if not exists batt_test_record_dimensions
(
    id character varying(50) NOT NULL,
    user_name character varying(50) NOT NULL,
    selected character varying(50) NOT NULL,
    show_sequence INTEGER not null,
    creator character varying(50) NOT NULL,
    updater character varying(50),
    gmt_create timestamptz NOT NULL,
    gmt_modified timestamptz,
    CONSTRAINT batt_test_record_dimensions_key PRIMARY KEY (id, user_name)
);
create index if not exists user_name_index on batt_test_record_dimensions(user_name);

COMMENT ON TABLE batt_test_record_dimensions IS '测试记录维度表';
COMMENT ON COLUMN batt_test_record_dimensions.id IS '维度id';
COMMENT ON COLUMN batt_test_record_dimensions.user_name IS '用户名';
COMMENT ON COLUMN batt_test_record_dimensions.selected IS '是否勾选';
COMMENT ON COLUMN batt_test_record_dimensions.show_sequence IS '展示顺序';
COMMENT ON COLUMN batt_test_record_dimensions.creator IS '创建者';
COMMENT ON COLUMN batt_test_record_dimensions.updater IS '更新者';
COMMENT ON COLUMN batt_test_record_dimensions.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_record_dimensions.gmt_modified IS '更新时间';

--设备测试记录表 batt_test_record
CREATE TABLE if not exists batt_test_record
(
        id character varying(50) NOT NULL,
        device_id character varying(50) NOT NULL,
        cause_time character varying(50) NOT NULL,
        test_cause character varying(50) NOT NULL,
        task_id character varying(50),
        test_status character varying(20) NOT NULL,
        test_time_start character varying(50),
        test_time_end character varying(50),
        pre_backup_power_status character varying(20),
        pre_health_status character varying(20),
        aft_backup_power_status character varying(20),
        aft_health_status character varying(20),
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT batt_test_record_key PRIMARY KEY (id)
);

COMMENT ON TABLE batt_test_record IS '设备测试记录表';

COMMENT ON COLUMN batt_test_record.id IS '记录id';
COMMENT ON COLUMN batt_test_record.device_id IS '设备id';
COMMENT ON COLUMN batt_test_record.cause_time IS '触发时间';
COMMENT ON COLUMN batt_test_record.test_cause IS '测试引起方式';
COMMENT ON COLUMN batt_test_record.task_id IS '任务id';
COMMENT ON COLUMN batt_test_record.test_status IS '测试状态';
COMMENT ON COLUMN batt_test_record.test_time_start IS '测试起时间';
COMMENT ON COLUMN batt_test_record.test_time_end IS '测试止时间';
COMMENT ON COLUMN batt_test_record.pre_backup_power_status IS '测试前备电状态';
COMMENT ON COLUMN batt_test_record.pre_health_status IS '测试前健康状态';
COMMENT ON COLUMN batt_test_record.aft_backup_power_status IS '测试后备电状态';
COMMENT ON COLUMN batt_test_record.aft_health_status IS '测试后健康状态';
COMMENT ON COLUMN batt_test_record.creator IS '创建者';
COMMENT ON COLUMN batt_test_record.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_record.updater IS '更新者';
COMMENT ON COLUMN batt_test_record.gmt_modified IS '更新时间';

alter table batt_test_record add unique(device_id,cause_time);


--设备测试关联数据记录表 batt_test_relation_data_record
CREATE TABLE if not exists batt_test_relation_data_record
(
        id character varying(50) NOT NULL,
        loop_id character varying(50) NOT NULL,
        record_id character varying(50) NOT NULL,
        data text NOT NULL,

        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT batt_test_relation_data_record_key PRIMARY KEY (id)
);

COMMENT ON TABLE batt_test_relation_data_record IS '设备测试关联数据记录表';

COMMENT ON COLUMN batt_test_relation_data_record.id IS 'id';
COMMENT ON COLUMN batt_test_relation_data_record.loop_id IS '回路id';
COMMENT ON COLUMN batt_test_relation_data_record.record_id IS '记录id';
COMMENT ON COLUMN batt_test_relation_data_record.data IS '数据';

COMMENT ON COLUMN batt_test_relation_data_record.creator IS '创建者';
COMMENT ON COLUMN batt_test_relation_data_record.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_relation_data_record.updater IS '更新者';
COMMENT ON COLUMN batt_test_relation_data_record.gmt_modified IS '更新时间';

alter table batt_test_relation_data_record add unique(loop_id,record_id);

alter table if exists peak_shift_battery_charge_discharge_detail drop constraint if exists "peak_shift_battery_charge_discharge_detail_pkey";



-- 电池测试任务表
CREATE TABLE if not exists batt_test_task
(
        id character varying(50) NOT NULL,
        name character varying(50) NOT NULL,
        period INTEGER NOT NULL,
        status character varying(20) NOT NULL,
        start_time timestamptz NOT NULL,
        remark text ,
        internal boolean NOT NULL,
        creator character varying(100) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(100),
        gmt_modified timestamptz,
        CONSTRAINT batt_test_task_key PRIMARY KEY (id)
);
COMMENT ON TABLE batt_test_task IS '电池测试任务表';

COMMENT ON COLUMN batt_test_task.id IS 'id';
COMMENT ON COLUMN batt_test_task.name IS '名称';
COMMENT ON COLUMN batt_test_task.period IS '执行频度';
COMMENT ON COLUMN batt_test_task.status IS '状态';
COMMENT ON COLUMN batt_test_task.start_time IS '开始时间';
COMMENT ON COLUMN batt_test_task.remark IS '备注';
COMMENT ON COLUMN batt_test_task.internal IS '是否内置';
COMMENT ON COLUMN batt_test_task.creator IS '创建者';
COMMENT ON COLUMN batt_test_task.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_task.updater IS '更新者';
COMMENT ON COLUMN batt_test_task.gmt_modified IS '更新时间';
alter table batt_test_task add unique(name);
create index if not exists status_index on batt_test_task(status);


-- 电池测试任务设备表
CREATE TABLE  if not exists batt_test_task_devices
(
        id character varying(50) NOT NULL,
        task_id character varying(50) NOT NULL,
        need_retry boolean  NOT NULL,
        retry_reason character varying(50)  ,
        creator character varying(100) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(100),
        gmt_modified timestamptz,
        CONSTRAINT batt_test_task_devices_key PRIMARY KEY (id)
);
COMMENT ON TABLE batt_test_task_devices IS '电池测试任务设备表';

COMMENT ON COLUMN batt_test_task_devices.id IS '设备id';
COMMENT ON COLUMN batt_test_task_devices.task_id IS '任务id';
COMMENT ON COLUMN batt_test_task_devices.need_retry IS '是否需要重试';
COMMENT ON COLUMN batt_test_task_devices.retry_reason IS '重试原因';
COMMENT ON COLUMN batt_test_task_devices.creator IS '创建者';
COMMENT ON COLUMN batt_test_task_devices.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_task_devices.updater IS '更新者';
COMMENT ON COLUMN batt_test_task_devices.gmt_modified IS '更新时间';
create index if not exists task_id_index on batt_test_task_devices(task_id);

--电池测试任务关联设备临时表
CREATE TABLE if not exists batt_test_task_tem_devices
(
        id character varying(50) NOT NULL,
        user_name text NOT NULL,
        task_id character varying(50) NOT NULL,
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(100),
        gmt_modified timestamptz,
        CONSTRAINT batt_test_task_tem_devices_key PRIMARY KEY (id,user_name)
);

COMMENT ON TABLE batt_test_task_tem_devices IS '电池测试任务关联设备临时表';

COMMENT ON COLUMN batt_test_task_tem_devices.id IS '设备id';
COMMENT ON COLUMN batt_test_task_tem_devices.user_name IS '用户名';
COMMENT ON COLUMN batt_test_task_tem_devices.task_id IS '任务id';
COMMENT ON COLUMN batt_test_task_tem_devices.creator IS '创建者';
COMMENT ON COLUMN batt_test_task_tem_devices.gmt_create IS '创建时间';
COMMENT ON COLUMN batt_test_task_tem_devices.updater IS '更新者';
COMMENT ON COLUMN batt_test_task_tem_devices.gmt_modified IS '更新时间';
create index if not exists task_id_index on batt_test_task_tem_devices(task_id);

ALTER TABLE public.peak_shift_task ADD IF NOT EXISTS status text NOT NULL DEFAULT '';
COMMENT ON COLUMN peak_shift_task.status IS '任务状态';
ALTER TABLE public.peak_shift_task ADD IF NOT EXISTS effective_date text NOT NULL DEFAULT '';
COMMENT ON COLUMN peak_shift_task.effective_date IS '生效日期';
ALTER TABLE public.peak_shift_task ADD IF NOT EXISTS expiration_date text NOT NULL DEFAULT '';
COMMENT ON COLUMN peak_shift_task.expiration_date IS '失效日期';

ALTER TABLE public.peak_shift_task_detail ADD IF NOT EXISTS exec_time text;
COMMENT ON COLUMN peak_shift_task_detail.exec_time IS '实际执行时间';

ALTER TABLE public.peak_shift_operation_record ADD IF NOT EXISTS exec_time text;
COMMENT ON COLUMN peak_shift_operation_record.exec_time IS '实际执行时间';

--更新亚健康的名称
update batt_health_status_threshold set name='{"zh_CN":"亚健康","en_US": "Subhealthy"}' where id='subhealth';