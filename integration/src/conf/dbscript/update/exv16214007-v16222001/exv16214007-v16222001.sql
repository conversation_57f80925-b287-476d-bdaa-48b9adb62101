

create table if not exists peak_shift_strategy
(
	id 					text not null,
	name 				text not null,
	effective_scope 	text not null,
	mode				smallint not null,
	status 				smallint not null,
	effective_time		text,
	expiration_time		text,
	remark				text,
	start_date          text not null,
	end_date            text not null,
	gmt_create			text not null,
	gmt_modified		text,
	primary key(id)
);
COMMENT ON TABLE peak_shift_strategy 					 is '错峰用电策略表';
COMMENT ON COLUMN peak_shift_strategy.id 				 IS '主键id，自动生成的UUID';
COMMENT ON COLUMN peak_shift_strategy.name 				 IS '策略名称';
COMMENT ON COLUMN peak_shift_strategy.effective_scope 	 IS '作用范围，为list<string>的json格式，存放各个分组或者站点的ID';
COMMENT ON COLUMN peak_shift_strategy.mode 				 IS '策略的模式，0-日模式 1-周模式 2-月模式';
COMMENT ON COLUMN peak_shift_strategy.status 			 IS '0-拟制中 1-启用 2-停用';
COMMENT ON COLUMN peak_shift_strategy.effective_time 	 IS '生效时间：启用时间';
COMMENT ON COLUMN peak_shift_strategy.expiration_time 	 IS '失效时间：停用时间';
COMMENT ON COLUMN peak_shift_strategy.start_date 	     IS '作用时间：开始时间';
COMMENT ON COLUMN peak_shift_strategy.end_date   	     IS '作用时间：结束时间';
COMMENT ON COLUMN peak_shift_strategy.remark			 IS '备注';

create table if not exists 	peak_shift_strategy_detail
(
	id 					text not null,
	strategy_id  		text not null,
	period_start		text,
	period_end			text,
	detail 				text not null,
	gmt_create			text not null,
	gmt_modified		text,
	primary key(id)
);
COMMENT ON TABLE peak_shift_strategy_detail 					is '错峰用电策略详情表';
COMMENT ON COLUMN peak_shift_strategy_detail.id 				is '主键，UUID';
COMMENT ON COLUMN peak_shift_strategy_detail.strategy_id		is '策略ID';
COMMENT ON COLUMN peak_shift_strategy_detail.period_start	    is '周期开始';
COMMENT ON COLUMN peak_shift_strategy_detail.period_end		 	is '周期结束';
COMMENT ON COLUMN peak_shift_strategy_detail.detail				is '策略详情，json格式';

create table if not exists peak_shift_battery_charge_discharge_statistics
(
	site_id			    		text	not null,
	id 							text 	not null,
	discharge_times 			int 	not null,
	discharge_duration			numeric(20,2) not null,
	discharge_duration_max		numeric(20,2) not null,
	charge_times				int 	not null,
	charge_duration				numeric(20,2) not null,
	free_duration				numeric(20,2) not null,
	llvd1_times					int 	not null,
	charge_value				numeric(20,2) not null,
	discharge_value				numeric(20,2) not null,
	strategy_type				smallint 	not null,
	datestr						text        not null,
	strategy_id                 text        not null,
    strategy_mode               int         not null,
    strategy_period_start		text,
    strategy_period_end			text,
	gmt_create					text	    not null,
	gmt_modified				text,
	primary key(id,datestr,strategy_type)
);
COMMENT ON TABLE peak_shift_battery_charge_discharge_statistics is '充放电统计表';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.site_id is '站点id';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.id is '电源系统(开关电源或者电池组，电池向上有开关电源就是开关电源)ID';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.discharge_times is '放电次数';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.discharge_duration is '放电持续时间';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.discharge_duration_max is '最大的一次放电持续时长';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.charge_times is '充电次数';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.charge_duration is '充电时长';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.free_duration is '非错峰时段';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.llvd1_times is '一次下电次数';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.charge_value is '充电量';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.discharge_value is '放电量';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.strategy_type is '策略类型，0:低谷 1：普通 2：高峰 3：最高';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.datestr is '日期，yyyy-mm-dd';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.strategy_id is '策略ID';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.strategy_mode is '策略模式';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.strategy_period_start is '策略周期开始';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.strategy_period_end is '策略周期结束';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.gmt_create is '数据生成时间';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_statistics.gmt_modified is '数据更新时间';

ALTER TABLE peak_shift_battery_charge_discharge_statistics ALTER COLUMN site_id DROP NOT NULL;


create table if not exists peak_shift_battery_charge_discharge_detail
(
	site_id				text not null,
	batt_id				text not null,
	id 						text not null,
	type 					text not null,
	start_time				text not null,
	end_time				text not null,
	start_voltage			numeric(20,2),
	end_voltage				numeric(20,2),
	llvd1					boolean not null,
	gmt_create				text not null,
	gmt_modified			text,
	primary key(batt_id,start_time,type)
);
COMMENT ON table peak_shift_battery_charge_discharge_detail is '充放电详情表';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.site_id is '站点id';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.batt_id is '电池组ID';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.id is '电源系统ID';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.type is '记录类型,充电0，放电1';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.start_time is '开始时间';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.end_time is '结束时间';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.start_voltage is '电池组充放电起始电压';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.end_voltage is '电池组充放电结束电压';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.llvd1 is '一次下电是否发生,true-发生，false-未发生';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.gmt_create is '数据生成时间';
COMMENT ON COLUMN peak_shift_battery_charge_discharge_detail.gmt_modified is '数据更新时间';

create table if not exists sp_or_battpack_data_after
(
	parent_id 	text,
	id 			text,
	power		numeric,
	init_volt	numeric,
	final_volt	numeric,
	type		text,
	start_time	text,
	end_time	text,
	gmt_create	text
);

COMMENT ON table sp_or_battpack_data_after is '电池充放电数据向上汇聚结果记录表';
COMMENT ON COLUMN sp_or_battpack_data_after.parent_id is '电源系统(开关电源或者电池组，电池向上有开关电源就是开关电源)ID';
COMMENT ON COLUMN sp_or_battpack_data_after.power is '汇聚之后按时长占比分割的充放电量';
COMMENT ON COLUMN sp_or_battpack_data_after.type is '0：充电，1：放电';
COMMENT ON COLUMN sp_or_battpack_data_after.start_time is '分割汇聚之后的开始时间';
COMMENT ON COLUMN sp_or_battpack_data_after.end_time is '分割汇聚之后的结束时间';
COMMENT ON COLUMN sp_or_battpack_data_after.gmt_create is '分割汇聚之前的第一条记录数据的入库时间';



CREATE TABLE if not exists peak_shift_task
(
        id character varying(50) NOT NULL ,
        name character varying(50) NOT NULL UNIQUE,
        file_id character varying(50) NOT NULL,
        description text,
        creator character varying(50) NOT NULL,
        gmt_create character varying(19) NOT NULL,
        updater character varying(50),
        gmt_modified character varying(19),
        CONSTRAINT peak_shift_task_key PRIMARY KEY (id)
);

COMMENT ON TABLE peak_shift_task IS '错峰任务表';

COMMENT ON COLUMN peak_shift_task.id IS '任务id';
COMMENT ON COLUMN peak_shift_task.name IS '任务名称';
COMMENT ON COLUMN peak_shift_task.file_id IS '文件id';
COMMENT ON COLUMN peak_shift_task.description IS '备注';
COMMENT ON COLUMN peak_shift_task.creator IS '创建者';
COMMENT ON COLUMN peak_shift_task.gmt_create IS '创建时间';
COMMENT ON COLUMN peak_shift_task.updater IS '更新者';
COMMENT ON COLUMN peak_shift_task.gmt_modified IS '更新时间';

CREATE TABLE if not exists peak_shift_task_detail
(
        id character varying(50) NOT NULL ,
        task_id character varying(50) NOT NULL,
        device_id character varying(50) NOT NULL,
        status character varying(20) NOT NULL,
        creator character varying(50) NOT NULL,
        gmt_create character varying(19) NOT NULL,
        updater character varying(50),
        gmt_modified character varying(19),
        CONSTRAINT peak_shift_task_detail_key PRIMARY KEY (id),
        UNIQUE(task_id, device_id)
);
        CREATE INDEX if not exists peak_shift_task_detail_task_id ON peak_shift_task_detail (task_id);


COMMENT ON TABLE peak_shift_task_detail IS '错峰任务详情表';

COMMENT ON COLUMN peak_shift_task_detail.id IS 'id';
COMMENT ON COLUMN peak_shift_task_detail.task_id IS '任务id';
COMMENT ON COLUMN peak_shift_task_detail.device_id IS '设备id';
COMMENT ON COLUMN peak_shift_task_detail.status IS '状态';
COMMENT ON COLUMN peak_shift_task_detail.creator IS '创建者';
COMMENT ON COLUMN peak_shift_task_detail.gmt_create IS '创建时间';
COMMENT ON COLUMN peak_shift_task_detail.updater IS '更新者';
COMMENT ON COLUMN peak_shift_task_detail.gmt_modified IS '更新时间';