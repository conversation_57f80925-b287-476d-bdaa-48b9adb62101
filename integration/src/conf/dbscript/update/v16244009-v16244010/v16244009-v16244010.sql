-- Started by <PERSON><PERSON><PERSON><PERSON>, pid:b5689n7bbbo44e41469f0833b01e0406e0318f1c
--电子钥匙表
CREATE TABLE IF NOT EXISTS ekey_history_record (
	id text not null primary key,
	ekey text not null,
	is_curr bool not null default true,
	ekey_sha256 text,
	gmt_create text,
	creator text
);
CREATE INDEX IF NOT EXISTS ekey_history_record_id_index ON public.ekey_history_record USING btree (id);
COMMENT ON TABLE ekey_history_record is '电池防盗电子钥匙表';
COMMENT ON COLUMN ekey_history_record.id is 'id';
COMMENT ON COLUMN ekey_history_record.ekey is '电子密钥';
COMMENT ON COLUMN ekey_history_record.is_curr is '是否为当前密钥';
COMMENT ON COLUMN ekey_history_record.ekey_sha256 is '电子钥匙sha256格式';
COMMENT ON COLUMN ekey_history_record.gmt_create is '创建时间';
COMMENT ON COLUMN ekey_history_record.creator is '创建人';

--电池关联电子钥匙表
CREATE TABLE IF NOT EXISTS battery_ekey_relation (
	mo_id text not null primary key,
	ekey text not null,
	gmt_create text,
	creator text,
	gmt_modified text,
	updater text
);
CREATE INDEX IF NOT EXISTS battery_ekey_relation_mo_id_index ON public.battery_ekey_relation USING btree (mo_id);
COMMENT ON TABLE battery_ekey_relation is '电池关联电子钥匙表';
COMMENT ON COLUMN battery_ekey_relation.mo_id is '电池id';
COMMENT ON COLUMN battery_ekey_relation.ekey is '电子密钥';
COMMENT ON COLUMN battery_ekey_relation.gmt_create is '创建时间';
COMMENT ON COLUMN battery_ekey_relation.creator is '创建人';
COMMENT ON COLUMN battery_ekey_relation.gmt_modified is '更新时间';
COMMENT ON COLUMN battery_ekey_relation.updater is '更新人';
-- Ended by AICoder, pid:b5689n7bbbo44e41469f0833b01e0406e0318f1c

-- R321电池充放电历史记录表
CREATE TABLE IF NOT EXISTS battery_charge_discharge_history_r321
(
    device_id           TEXT        NOT NULL,                               -- 采集器id
    battery_seq         TEXT        NOT NULL,                               -- 电池序列号
    charge_value        TEXT,                                               -- 当前累计放电量
    discharge_value     TEXT,                                               -- 当前累计充电量
    inc_charge_value    TEXT,                                               -- 充电量增量
    inc_discharge_value TEXT,                                               -- 放电量增量
    record_time         TEXT        NOT NULL,                               -- 记录时间
    gmt_create          TIMESTAMPTZ NOT NULL,                               -- 数据入库时间
    gmt_modified        TIMESTAMPTZ,                                        -- 数据修改时间
    CONSTRAINT uq_battery_seq_record_time UNIQUE (battery_seq, record_time) -- 唯一约束：battery_seq + record_time
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_device_id ON battery_charge_discharge_history_r321 (device_id);

-- 表注释
COMMENT ON TABLE battery_charge_discharge_history_r321 IS '电池充放电历史记录表';

-- 字段注释
COMMENT ON COLUMN battery_charge_discharge_history_r321.device_id IS '采集器ID';
COMMENT ON COLUMN battery_charge_discharge_history_r321.battery_seq IS '电池序列号';
COMMENT ON COLUMN battery_charge_discharge_history_r321.charge_value IS '当前累计放电量';
COMMENT ON COLUMN battery_charge_discharge_history_r321.discharge_value IS '当前累计充电量';
COMMENT ON COLUMN battery_charge_discharge_history_r321.inc_charge_value IS '充电量增量';
COMMENT ON COLUMN battery_charge_discharge_history_r321.inc_discharge_value IS '放电量增量';
COMMENT ON COLUMN battery_charge_discharge_history_r321.record_time IS '记录时间';
COMMENT ON COLUMN battery_charge_discharge_history_r321.gmt_create IS '数据入库时间';
COMMENT ON COLUMN battery_charge_discharge_history_r321.gmt_modified IS '数据修改时间';
