
--下电原始测点记录表 disconnect_original_point_record
CREATE TABLE if not exists disconnect_original_point_record
(
    id character varying(50) not null,
    start_time timestamptz not null,
    end_time timestamptz,
    device_id character varying(50) not null,
    omp_id character varying(50) NOT NULL,
    omp_index character varying(50) NOT NULL,
    omp_value character varying(50) NOT NULL,
    creator character varying(50) not null,
    gmt_create timestamptz not null,
    updater character varying(50),
    gmt_modified timestamptz,
    CONSTRAINT disconnect_original_point_record_id_key PRIMARY KEY (id)
);
COMMENT ON TABLE disconnect_original_point_record IS 'Backup Type: largeTableData; 下电原始测点记录表';
COMMENT ON COLUMN disconnect_original_point_record.id IS '记录id';
COMMENT ON COLUMN disconnect_original_point_record.start_time IS '生效开始时间';
COMMENT ON COLUMN disconnect_original_point_record.end_time IS '生效结束时间';
COMMENT ON COLUMN disconnect_original_point_record.device_id IS '设备id';
COMMENT ON COLUMN disconnect_original_point_record.omp_id IS '原始测点id';
COMMENT ON COLUMN disconnect_original_point_record.omp_index IS '原始测点index';
COMMENT ON COLUMN disconnect_original_point_record.omp_value IS '原始测点value';
COMMENT ON COLUMN disconnect_original_point_record.creator IS '创建者';
COMMENT ON COLUMN disconnect_original_point_record.gmt_create IS '创建时间';
COMMENT ON COLUMN disconnect_original_point_record.updater IS '更新者';
COMMENT ON COLUMN disconnect_original_point_record.gmt_modified IS '更新时间';