
-- 电池资产维度表
CREATE TABLE if not exists battery_asset_dimensions
(
        id character varying(30) NOT NULL,
        user_name character varying(50) NOT NULL,
        sequence INTEGER NOT NULL,
        name text,
        enable BOOLEAN,
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT battery_asset_dimensions_id_user_name_key PRIMARY KEY (id,user_name)
);

COMMENT ON TABLE battery_asset_dimensions IS '电池资产维度表';

COMMENT ON COLUMN battery_asset_dimensions.id IS '资产维度id';
COMMENT ON COLUMN battery_asset_dimensions.user_name IS '用户名';
COMMENT ON COLUMN battery_asset_dimensions.sequence IS '顺序';
COMMENT ON COLUMN battery_asset_dimensions.name IS '维度名称 - json中英文';
COMMENT ON COLUMN battery_asset_dimensions.enable IS '是否启用';
COMMENT ON COLUMN battery_asset_dimensions.creator IS '创建者';
COMMENT ON COLUMN battery_asset_dimensions.gmt_create IS '创建时间';
COMMENT ON COLUMN battery_asset_dimensions.updater IS '更新者';
COMMENT ON COLUMN battery_asset_dimensions.gmt_modified IS '更新时间';

-- 电池工况维度表
CREATE TABLE if not exists battery_work_condition_dimensions
(
        id character varying(30) NOT NULL,
        user_name character varying(50) NOT NULL,
        sequence INTEGER NOT NULL,
        name text,
        enable BOOLEAN,
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT battery_work_condition_dimensions_id_user_name_key PRIMARY KEY (id,user_name)
);

COMMENT ON TABLE battery_work_condition_dimensions IS '电池工况维度表';

COMMENT ON COLUMN battery_work_condition_dimensions.id IS '资产维度id';
COMMENT ON COLUMN battery_work_condition_dimensions.user_name IS '用户名';
COMMENT ON COLUMN battery_work_condition_dimensions.sequence IS '顺序';
COMMENT ON COLUMN battery_work_condition_dimensions.name IS '维度名称 - json中英文';
COMMENT ON COLUMN battery_work_condition_dimensions.enable IS '是否启用';
COMMENT ON COLUMN battery_work_condition_dimensions.creator IS '创建者';
COMMENT ON COLUMN battery_work_condition_dimensions.gmt_create IS '创建时间';
COMMENT ON COLUMN battery_work_condition_dimensions.updater IS '更新者';
COMMENT ON COLUMN battery_work_condition_dimensions.gmt_modified IS '更新时间';

-- 电池概览维度表
CREATE TABLE if not exists battery_overview_dimensions
(
        id character varying(30) NOT NULL,
        user_name character varying(50) NOT NULL,
        sequence INTEGER NOT NULL,
        name text,
        enable BOOLEAN,
        creator character varying(50) NOT NULL,
        gmt_create timestamptz NOT NULL,
        updater character varying(50),
        gmt_modified timestamptz,
        CONSTRAINT battery_overview_dimensions_id_user_name_key PRIMARY KEY (id,user_name)
);

COMMENT ON TABLE battery_overview_dimensions IS '电池概览维度表';

COMMENT ON COLUMN battery_overview_dimensions.id IS '资产维度id';
COMMENT ON COLUMN battery_overview_dimensions.user_name IS '用户名';
COMMENT ON COLUMN battery_overview_dimensions.sequence IS '顺序';
COMMENT ON COLUMN battery_overview_dimensions.name IS '维度名称 - json中英文';
COMMENT ON COLUMN battery_overview_dimensions.enable IS '是否启用';
COMMENT ON COLUMN battery_overview_dimensions.creator IS '创建者';
COMMENT ON COLUMN battery_overview_dimensions.gmt_create IS '创建时间';
COMMENT ON COLUMN battery_overview_dimensions.updater IS '更新者';
COMMENT ON COLUMN battery_overview_dimensions.gmt_modified IS '更新时间';