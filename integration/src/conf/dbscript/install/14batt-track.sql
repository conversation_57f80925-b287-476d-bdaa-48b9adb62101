-- 新增实时表
DROP TABLE IF EXISTS battery_realtime_location;
CREATE TABLE public.battery_realtime_location (
	battery_id text NOT NULL,
	longitude text NOT NULL,
	latitude text NOT NULL,
	record_time text null,
	CONSTRAINT battery_realtime_location_pkey PRIMARY KEY (battery_id)
);

COMMENT ON TABLE "public"."battery_realtime_location" IS 'Backup Type: basicTableData 电池位置实时表';
COMMENT ON COLUMN "public"."battery_realtime_location"."battery_id" IS '电池id';
COMMENT ON COLUMN "public"."battery_realtime_location"."longitude" IS '经度';
COMMENT ON COLUMN "public"."battery_realtime_location"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."battery_realtime_location"."record_time" IS '记录时间';

-- 新增历史表
DROP TABLE IF EXISTS battery_history_location;
CREATE TABLE public.battery_history_location (
    battery_id text NOT NULL,
	longitude text NOT NULL,
	latitude text NOT NULL,
	first_record_time text NOT null,
    last_record_time text null,
	CONSTRAINT battery_history_location_pkey PRIMARY KEY (battery_id,first_record_time)
);

COMMENT ON TABLE "public"."battery_history_location" IS 'Backup Type: basicTableData 电池位置历史表';
COMMENT ON COLUMN "public"."battery_history_location"."battery_id" IS '电池id';
COMMENT ON COLUMN "public"."battery_history_location"."longitude" IS '经度';
COMMENT ON COLUMN "public"."battery_history_location"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."battery_history_location"."first_record_time" IS '记录时间';

create index if not exists operation_log_resource_id_index on operation_log(resource_id);