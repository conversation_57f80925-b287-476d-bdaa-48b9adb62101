-- ------------------------------------
-- Table structure for `batt`
-- ------------------------------------

DROP   
    TABLE IF EXISTS batt_cfg_info;
CREATE
    TABLE batt_cfg_info(
         id serial NOT NULL
        ,mo_id text NOT NULL
        ,birthday timestamptz
        ,opening_date timestamptz
        ,brand text
        ,rated_capacity integer
        ,electrode_material text
        ,CONSTRAINT batt_cfg_info_pkey PRIMARY KEY (id)
    );
COMMENT ON TABLE batt_cfg_info IS 'Backup Type: basicTableData; batt_cfg_info';

DROP 
    TABLE IF EXISTS batt_life_cfg;
CREATE
	TABLE batt_life_cfg(
	     id serial NOT NULL
        ,mo_id text NOT NULL
        ,expected_life integer
        ,cycLifeOfPct_d5 integer
        ,cycLifeOfPct_d10 integer
        ,cycLifeOfPct_d15 integer
        ,cycLifeOfPct_d20 integer
        ,cycLifeOfPct_d25 integer
        ,cycLifeOfPct_d30 integer
        ,cycLifeOfPct_d35 integer
        ,cycLifeOfPct_d40 integer
        ,cycLifeOfPct_d45 integer
        ,cycLifeOfPct_d50 integer
        ,cycLifeOfPct_d55 integer
        ,cycLifeOfPct_d60 integer
        ,cycLifeOfPct_d65 integer
        ,cycLifeOfPct_d70 integer
        ,cycLifeOfPct_d75 integer
        ,cycLifeOfPct_d80 integer
        ,cycLifeOfPct_d85 integer
        ,cycLifeOfPct_d90 integer
        ,cycLifeOfPct_d95 integer
        ,cycLifeOfPct_d100 integer
	);
COMMENT ON TABLE batt_life_cfg IS 'Backup Type: basicTableData; batt_life_cfg';
insert into batt_life_cfg(mo_id,expected_life,cycLifeOfPct_d5 
        ,cycLifeOfPct_d10 
        ,cycLifeOfPct_d15 
        ,cycLifeOfPct_d20 
        ,cycLifeOfPct_d25 
        ,cycLifeOfPct_d30 
        ,cycLifeOfPct_d35 
        ,cycLifeOfPct_d40 
        ,cycLifeOfPct_d45 
        ,cycLifeOfPct_d50 
        ,cycLifeOfPct_d55 
        ,cycLifeOfPct_d60 
        ,cycLifeOfPct_d65 
        ,cycLifeOfPct_d70 
        ,cycLifeOfPct_d75 
        ,cycLifeOfPct_d80 
        ,cycLifeOfPct_d85 
        ,cycLifeOfPct_d90 
        ,cycLifeOfPct_d95 
        ,cycLifeOfPct_d100)values('leadAcid',72,7000,6800,6000,5200,4600,4150,3700,3200,2900,2600,2250,2000,1800,1400,1150,1000,800,650,500,400);
        
insert into batt_life_cfg(mo_id,expected_life,cycLifeOfPct_d5 
        ,cycLifeOfPct_d10 
        ,cycLifeOfPct_d15 
        ,cycLifeOfPct_d20 
        ,cycLifeOfPct_d25 
        ,cycLifeOfPct_d30 
        ,cycLifeOfPct_d35 
        ,cycLifeOfPct_d40 
        ,cycLifeOfPct_d45 
        ,cycLifeOfPct_d50 
        ,cycLifeOfPct_d55 
        ,cycLifeOfPct_d60 
        ,cycLifeOfPct_d65 
        ,cycLifeOfPct_d70 
        ,cycLifeOfPct_d75 
        ,cycLifeOfPct_d80 
        ,cycLifeOfPct_d85 
        ,cycLifeOfPct_d90 
        ,cycLifeOfPct_d95 
        ,cycLifeOfPct_d100)values('li',96,10000,8000,7000,6000,5000,4300,3650,3200,2800,2500,2200,2000,1820,1700,1590,1500,1430,1350,1290,1250); 

DROP 
    TABLE IF EXISTS batt_life_cfg_his;
CREATE
	TABLE batt_life_cfg_his(
	     id serial NOT NULL
        ,mo_id text NOT NULL
        ,expected_life integer
        ,cycLifeOfPct_d5 integer
        ,cycLifeOfPct_d10 integer
        ,cycLifeOfPct_d15 integer
        ,cycLifeOfPct_d20 integer
        ,cycLifeOfPct_d25 integer
        ,cycLifeOfPct_d30 integer
        ,cycLifeOfPct_d35 integer
        ,cycLifeOfPct_d40 integer
        ,cycLifeOfPct_d45 integer
        ,cycLifeOfPct_d50 integer
        ,cycLifeOfPct_d55 integer
        ,cycLifeOfPct_d60 integer
        ,cycLifeOfPct_d65 integer
        ,cycLifeOfPct_d70 integer
        ,cycLifeOfPct_d75 integer
        ,cycLifeOfPct_d80 integer
        ,cycLifeOfPct_d85 integer
        ,cycLifeOfPct_d90 integer
        ,cycLifeOfPct_d95 integer
        ,cycLifeOfPct_d100 integer
	);
COMMENT ON TABLE batt_life_cfg_his IS 'Backup Type: basicTableData; batt_life_cfg_his';

DROP 
    TABLE IF EXISTS batt_life_cfg_threshold;
CREATE
	TABLE batt_life_cfg_threshold(
	     id serial NOT NULL
        ,mo_id text NOT NULL
        ,expected_life integer
        ,cycLifeOfPct_thres integer
	);
COMMENT ON TABLE batt_life_cfg_threshold IS 'Backup Type: basicTableData; batt_life_cfg_threshold';

DROP 
    TABLE IF EXISTS batt_life_cfg_threshold_his;
CREATE
	TABLE batt_life_cfg_threshold_his(
	     id serial NOT NULL
        ,mo_id text NOT NULL
        ,expected_life integer
        ,cycLifeOfPct_thres integer
	);
COMMENT ON TABLE batt_life_cfg_threshold_his IS 'Backup Type: basicTableData; batt_life_cfg_threshold_his';