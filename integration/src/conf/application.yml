battery:
  health:
    evaluate_period: 4
    status:
      healthy:
        name_zh: 健康
        name_en: Healthy
        threshold:
          min: 70
          containMin: true
          max: 100
          containMax: true
      subhealth:
        name_zh: 亚健康
        name_en: Subhealthy
        threshold:
          min: 50
          containMin: true
          max: 70
          containMax: false
      exception:
        name_zh: 异常
        name_en: Exception
        threshold:
          min: 0
          containMin: true
          max: 50
          containMax: false
      unEvaluate:
        name_zh: 无法评估
        name_en: UnEvaluate
        threshold: null
  life:
    evaluate_period: 1
    level:
      0:
        name_zh: 0 ~ 3 个月
        name_en: 0 ~ 3 Months
        threshold:
          min: 0
          containMin: true
          max: 3
          containMax: false
          unit: m
        show_sequence: 1
      1:
        name_zh: 3 ~ 6 个月
        name_en: 3 ~ 6 Months
        threshold:
          min: 3
          containMin: true
          max: 6
          containMax: false
          unit: m
        show_sequence: 2
      2:
        name_zh: 6 ~ 9个月
        name_en: 6 ~ 9 Months
        threshold:
          min: 6
          containMin: true
          max: 9
          containMax: false
          unit: m
        show_sequence: 3
      3:
        name_zh: 9 ~ 12个月
        name_en: 9 ~ 12 Months
        threshold:
          min: 9
          containMin: true
          max: 12
          containMax: false
          unit: m
        show_sequence: 4
      4:
        name_zh: 大于12个月
        name_en: Over 12 Months
        threshold:
          min: 12
          containMin: true
          max: 10000
          containMax: false
          unit: m
        show_sequence: 5
      Unknown:
        name_zh: 无法评估
        name_en: UnEvaluate
        threshold: null
        show_sequence: 6
  risk:
    eval_thread: 10
    eval_batt_number: 50
    query_batt_number: 10
  # 电池默认使用寿命（月）
  use-life:
    type: month
    lead_acid: 60
    lithium_iron: 96
  # 电池默认理论循环次数（80%）
  cycles:
    lead_acid: 2600
    lithium_iron: 3500
  # 备电时长告警算法查询范围（天）
  backupPowerAlarm:
    llvdTimeRange: 7
    acPowerOffTimeRange: 3
  # 下电剩余发电时长配置
  llvd:
    soc: 30
    k: 0.95
    invalidTime: 0.5 #失效时长，单位小时
filter:
  paramLengthLimit:
    #通用校验长度,默认为2000
    commonParamLength: 2000
  #yml配置拦截器的开关，默认false，用于控制整个拦截器是否执行
  switchEnable: false
  paramChar:
    specialCharacter: "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？_]"
timeout:
  #太阳能收益汇聚，定时任务获取锁超时时间
  job: 10
  kafka: 10
  other: 5
maximum:
  compensation:
    #手动补偿太阳能收益汇聚最大数量
    num: 40000
solar:
  collect:
    hour: 3
  recollection:
    ability:
      name: 累计光伏发电量
      point: "801E00380101"
  statistics:
    capacity:
      factory: 200
    distance: 400
uedm:
  file:
    export-limit-number: 10000
peak-shift:
  ftp-path: "/battery/peak/"
  bcuaTemplatePath: "/home/<USER>/uedm-app/conf/file/PeakShiftTemplate.xls"
  bcuaTemplateName: "PeakShiftTemplate.xls"
  bcuaTemplateStrategyPath: "/home/<USER>/uedm-app/conf/file/PeakShiftTemplate.zip"
  bcuaTemplateStrategyName: "PeakShiftTemplate.zip"