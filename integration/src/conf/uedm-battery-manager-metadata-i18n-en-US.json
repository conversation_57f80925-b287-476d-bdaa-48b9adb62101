{"battery": "UEDM BATTERY Service", "battery-manager": "UEDM BATTERY MANAGE", "uedm-battery-manage-root": "uedm battery manage root", "uedm-peakshift-manager-root": "Peak Shift config", "strategy-model-config": "Peak Shift strategy config", "Auto-peak-shift-strategy-time": "Peak shift automatic strategy timed task time", "Auto-peak-shift-strategy-time.desc": "Cron expression. Eg: '0 15 0 * * ?', means at 0:15 every day.", "uedm-solar-manage-param": "uedm solar manage param", "history-energy-storage-interval": "history energy storage interval(min)", "uedm-batt-test-param": "Discharge test parameter configuration", "battset-batt-test-soc": "Battery pack (independent monitoring) discharge test termination SOC (%)", "battery-theft-config": "battery anti-theft config", "battery-theft-scene": "battery anti-theft scene", "network-level-anti-theft": "network level anti-theft", "gps-anti-theft": "GPS anti-theft", "battery-theft-status-schedule": "battery theft status time task", "battery-theft-status-schedule-time": "battery theft status time", "battery-theft-status-schedule-desc": "for example: six, means that it is executed every 6 hours", "every-one-hour": "execute once every one hour", "every-two-hours": "execute once every two hours", "every-six-hours": "execute once every six hours", "every-twelve-hours": "execute once every twelve hours", "every-twenty-four-hours": "execute once every twenty four hours", "every-forty-eight-hours": "execute once every forty eight hours", "battery-protect-state-schedule": "battery protect state delivery ekey time task", "battery-protect-state-schedule-desc": "for example: six, means that it is executed every 6 hours"}