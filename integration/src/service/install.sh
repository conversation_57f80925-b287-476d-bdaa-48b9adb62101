#!/bin/bash
#上传zenap-ume服务到paas软件仓库
set -e
DIRNAME=`dirname $0`
WORK_HOME=`cd $DIRNAME/; pwd`
ZARTCLI=/root/zartcli/zartcli
VERSION_NO=@image.version@
SERVICE_NAME=@service.name@

log_file=install-$SERVICE_NAME.log
log_path=$LOG_DIRECTORY
IS_RETRY="yes"
if [ $# -eq 1 ]; then
    IS_RETRY=$1
fi

if [ -z $TENANTS ];then
	TENANTS=uedm
fi

if [ -z $DEPLOYTYPE ];then
   DEPLOYTYPE=service
fi
# ============================================================================
# log log_text
# ============================================================================
function log(){
	if [ -z "$log_path" ]; then
		log_path=/var/log/$SERVICE_NAME
	fi

	if [ ! -d $log_path ]; then
		mkdir -p $log_path
	fi

	local target=$log_path/$log_file
	if [ ! -f $target ]; then
		touch $target
	fi

	log_text="`date +%Y-%m-%d\ %T` [$SERVICE_NAME] $1"

	echo "$log_text" >> $target
	echo "$log_text"
}

uploadServiceBP() {
	local serviceBPPath=$WORK_HOME/blueprint/uedm_"$SERVICE_NAME"_service
	local servicePBCPath=$serviceBPPath/pbc-"$SERVICE_NAME".yaml

    if [ ! -d ${serviceBPPath} ]
    then
        echo "Service blueprint dir doesnot exist,path:${serviceBPPath},please check."
	    exit 2
    fi
	if [ -f $servicePBCPath ]; then
            mv $servicePBCPath $servicePBCPath.bak
    fi

	$ZARTCLI -o delete -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO || true >/dev/null
	$ZARTCLI -o upload -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO -b yes -p $serviceBPPath


	if [ -f $servicePBCPath.bak ]; then
          mv $servicePBCPath.bak $servicePBCPath
	fi
}

checkImageState() {
	if [ $# -ne 2 ]
	then
		echo usage:checkImageState IMAGE_NAME IMAGE_NO
		exit 1
	fi
	local IMAGE_NAME=$1
	local IMAGE_NO=$2
	local image_state=""
	for i in {1..120}
	do
		image_state=`$ZARTCLI -o query -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO | grep '"status":' | awk '{print $2}'`
		image_state=`echo $image_state | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g'`
		if [ $image_state = "available" ];then
			break
		else
			sleep 3
			continue
		fi
	done
	echo $image_state
}

buildImage() {
	if [ $# -ne 2 ]
	then
		echo usage:buildImage IMAGE_NAME IMAGE_NO
		exit 1
	fi
	local IMAGE_NAME=$1
	local IMAGE_NO=$2
	local IMAGE_STAT=""
	local dockerFilePath=$WORK_HOME/"$IMAGE_NAME"
	  IMAGE_NAME=`echo $IMAGE_NAME | sed 's/uedm-//'`

    if [ ! -d ${dockerFilePath} ]
    then
        echo "The directory for Dockerfile doesnot exist,path:${dockerFilePath},please check."
	    exit 2
    fi

    for i in {1..2}; do
        if [ "$IS_RETRY" = "no" ]; then
            image_state=`$ZARTCLI -o query -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO | grep '"status":' | awk '{print $2}'`
			image_state=`echo $image_state | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g'`
            if [ "$image_state" = "available" ]; then
                log "status of image $IMAGE_NAME: $image_state,no need to build"
                break
            fi

        fi
        $ZARTCLI -o delete -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO || true >/dev/null
        $ZARTCLI -o build -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO -b yes -p $dockerFilePath
        IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_NO)
        if [ "$IMAGE_STAT" = "available" ]; then
            log "status of image $IMAGE_NAME: $IMAGE_STAT, build success."
            break
        elif [ "$IMAGE_STAT" = "unavailable" ]; then
            if [ $i -eq 1 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, retry..."
                continue
            fi
            if [ $i -eq 2 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, build failed."
                exit 1
            fi
        else
            log "status of image $IMAGE_NAME: $IMAGE_STAT, unknown error."
            exit 1
        fi
    done
}

#Begin
echo "================================"
echo @WORK_HOME@ $WORK_HOME
echo @VERSION_NO@ $VERSION_NO
echo @TENANTS@ $TENANTS
echo "================================"

echo "=========Begin Upload $SERVICE_NAME========="
cd $WORK_HOME
#构建组件的基础镜像，无dependency的组件可以删除此段
#for dir in $(ls)
#do
#	if [ -d $dir ] && [ $dir == "$TENANTS"-"$SERVICE_NAME"-dependency ];then
#		ms=${dir#"$TENANTS"-}
#		buildImage $ms $VERSION_NO
#	fi
#done
#构建组件的微服务镜像和上传微服务蓝图
for ms in $(ls)
do
	if [ -d $ms ] && [ $ms != blueprint ] && [ $ms != "$SERVICE_NAME"-dependency ];then
		echo $ms
		echo $SERVICE_NAME
		buildImage $ms $VERSION_NO

	fi
done
echo "=========Begin Upload $SERVICE_NAME========="
#上传组件服务蓝图
uploadServiceBP

exit 0
