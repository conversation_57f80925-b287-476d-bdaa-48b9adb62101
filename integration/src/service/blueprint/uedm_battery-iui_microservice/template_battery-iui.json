{
  "kind": "Template",
  "apiVersion": "v1",
  "namespace": "${NAMESPACE}",
  "metadata": {
    "labels": {
      "name": "@ms.iui@"
    },
    "name": "@ms.iui@"
  },
  "objects": [
    {
      "spec": {
        "replicas": 1,
        "selector": {
          "name": "@ms.iui@"
        },
        "template": {
          "metadata": {
            "labels": {
              "name": "@ms.iui@"
            },
            "annotations": {
            }
          },
          "spec": {
            "containers": [
              {
                "name": "@ms.iui@",
                "image": "/@tenant.id@/@ms.iui@:@image.version@",
                "imagePullPolicy": "Always",
                "tty": false,
                "securityContext": {
                  "privileged": false
                },
                "stdin": false,
                "command": [
                ],
                "env": [
                  {
                    "name": "TZ",
                    "value": "${TZ}"
                  }
                ],
                "ports": [
                  {
                    "containerPort": @iui.port@,
                    "protocol": "TCP"
                  }
                ],
                "volumeMounts": [
                ],
                "resources": {
                  "requests": {
                    "cpu": 0.05,
                    "memory": "128Mi"
                  },
                  "limits": {
                    "cpu": 1,
                    "memory": "128Mi"
                  }
                }
              }
            ],
            "restartPolicy": "Always",
            "volumes": [
            ]
          }
        }
      },
      "kind": "DeploymentConfig",
      "apiVersion": "v1",
      "metadata": {
        "name": "@ms.iui@",
        "namespace": "${NAMESPACE}"
      }
    }
  ],
  "parameters": [
    {
      "name": "NAMESPACE",
      "displayName": "",
      "description": "",
      "value": "openshift",
      "section": "None"
    },
    {
      "name": "@service.name@",
      "displayName": "",
      "description": "",
      "value": "@service.name@",
      "section": "route"
    },
    {
      "name": "TZ",
      "displayName": "",
      "description": "",
      "value": "Asia/Shanghai",
      "section": "env"
    }
  ],
  "vnpm_param": {
    "vnpm_object": [
      {
        "name": "@ms.iui@",
        "route_list": [
          {
            "serviceName": "${@service.name@}",
            "version": "v1",
            "url": "/iui/@service.name@",
            "protocol": "UI",
            "path": "",
            "lb_policy": "round-robin",
            "port": "@iui.port@",
            "visualRange": "0",
            "network_plane_type": "net_api"
          }
        ],
        "networks": {
          "ports": [
            {
              "attach_to_network": "lan",
              "attributes": {
                "nic_name": "eth0",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false"
              }
            },
            {
              "attach_to_network": "net_api",
              "attributes": {
                "nic_name": "eth1",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false"
              }
            }
          ]
        }
      }
    ]
  },
  "eps_param": {
    "replicasMin": "1",
    "replicasMax": "1",
    "auto_policy": {
      "@ms.iui@": [
      ]
    }
  }
}
