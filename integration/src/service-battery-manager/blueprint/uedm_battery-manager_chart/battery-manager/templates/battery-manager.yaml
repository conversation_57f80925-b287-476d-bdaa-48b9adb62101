apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    appm: {{ .Release.Name }}
  name: &msName {{ include "oki.fullname" . }}-deploy
spec:
  # 云原生应用 弹缩最大最小副本数
  replicas: {{ .Values.replicas.battery_manager.init }}
  selector:
    matchLabels:
      name: {{ .Release.Name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      # 云原生应用 挂载网络
      annotations:
      {{- toYaml .Values.annotations.battery_manager | nindent 8 }}
      labels:
        name: {{ .Release.Name }}
    spec:
      # 云原生应用 labelselector
      {{- if .Values.global.INIT_CONTAINER_ENABLED }}
      initContainers:
      - name: &serverInitContainerName {{ .Release.Name }}-init-nonroot
        # 云原生应用 镜像规范
        {{- if .Values.serviceImage.tenant }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- else }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- end }}
        imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
        securityContext:
          privileged: false
          allowPrivilegeEscalation: false
        tty: false
        stdin: false
        command: [ "/home/<USER>/chmod_file.sh" ]
        env:
          - name: openpalette_ms_bpname
            value: *msName
          - name: openpalette_container_name
            value: *serverInitContainerName
          {{- range .Values.envs.init }}
            {{- include "oki.containerEnvs" . | nindent 10 }}
          {{- end }}
        # 云原生应用 volume 定义和使用
        volumeMounts:
          {{- range .Values.volumes.init }}
          - name: {{ .name }}
            mountPath: {{ .mountPath }}
            readOnly: {{ .readOnly }}
            subPath: {{ .subPath }}
          {{- end }}
        # 云原生应用 容器资源定义
        {{- if .Values.resources.init }}
        resources: {{- toYaml .Values.resources.init | nindent 10 }}
        {{- end }}
      {{- end }}
      # 云原生应用 labelselector
      containers:
      - name: &serverContainerName {{ .Release.Name }}
        env:
          # 微服务自己的部署参数
          # 云原生应用 global 属性  --- 注入公共属性和 msb信息
        - name: TZ
          value: {{ .Values.global.TZ }}
        - name: OPENPALETTE_MSB_IP
          value: {{ .Values.global.OPENPALETTE_MSB_IP }}
        - name: OPENPALETTE_MSB_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
        - name: OPENPALETTE_MSB_ROUTER_IP
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP }}
        - name: OPENPALETTE_MSB_ROUTER_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}
        - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}
        - name: OPENPALETTE_NAMESPACE
          value: {{ .Values.global.OPENPALETTE_NAMESPACE }}
        # 云原生应用使用 使用第三方公共服务  方式一； 同时也采用方式二配置了
        # BP蓝图中使用sercet卷好像是为了支持修改密码，chart蓝图不知道是否支持此功能
        - name: OPENPALETTE_PG_ADDRESS
          value: {{ .Values.pgConfig.OPENPALETTE_PG_ADDRESS }}
        - name: OPENPALETTE_PG_PORT
          value: {{ .Values.pgConfig.OPENPALETTE_PG_PORT | quote }}
        - name: OPENPALETTE_PG_DBNAME
          value: {{ .Values.pgConfig.OPENPALETTE_PG_DBNAME }}
        - name: OPENPALETTE_PG_USERNAME
          value: {{ .Values.pgConfig.OPENPALETTE_PG_USERNAME }}
        - name: OPENPALETTE_PG_PASSWORD
          value: {{ .Values.pgConfig.OPENPALETTE_PG_PASSWORD }}
        - name: OPENPALETTE_KAFKA_ADDRESS
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ADDRESS }}
        - name: OPENPALETTE_KAFKA_PORT
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_PORT | quote }}
        # pgcache
        - name: OPENPALETTE_PGCACHE_ADDRESS
          value: {{ .Values.pgcacheConfig.OPENPALETTE_PGCACHE_ADDRESS | quote }}
        - name: OPENPALETTE_PGCACHE_PORT
          value: {{ .Values.pgcacheConfig.OPENPALETTE_PGCACHE_PORT | quote }}
        - name: OPENPALETTE_PGCACHE_DBNAME
          value: {{ .Values.pgcacheConfig.OPENPALETTE_PGCACHE_DBNAME | quote }}
        - name: OPENPALETTE_PGCACHE_USERNAME
          value: {{ .Values.pgcacheConfig.OPENPALETTE_PGCACHE_USERNAME | quote }}
        - name: OPENPALETTE_PGCACHE_PASSWORD
          value: {{ .Values.pgcacheConfig.OPENPALETTE_PGCACHE_PASSWORD | quote }}
        #redis配置
        - name: OPENPALETTE_REDIS_ADDRESS
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_ADDRESS }}
        - name: OPENPALETTE_REDIS_PORT
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PORT | quote }}
        - name: OPENPALETTE_REDIS_PASSWORD
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PASSWORD }}
        - name: OPENPALETTE_REDIS_SENTINEL_ADDRESS
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_ADDRESS | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_PORT
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_PORT | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_MASTERNAME | quote }}
        - name: OPENPALETTE_REDIS_ADDRESS_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_ADDRESS_1 }}
        - name: OPENPALETTE_REDIS_PORT_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PORT_1 | quote }}
        - name: OPENPALETTE_REDIS_PASSWORD_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PASSWORD_1 }}
        - name: OPENPALETTE_REDIS_SENTINEL_ADDRESS_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_ADDRESS_1 | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_PORT_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_PORT_1 | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_MASTERNAME_1
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_MASTERNAME_1 | quote }}
        - name: OPENPALETTE_FTPSERVICECONFIG_USERNAME
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_USERNAME }}
        - name: OPENPALETTE_FTPSERVICECONFIG_USERPASSWORD
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_USERPASSWORD }}
        - name: OPENPALETTE_FTPSERVICECONFIG_FTPSERVERADDRESS
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_FTPSERVERADDRESS }}
        - name: OPENPALETTE_FTPSERVICECONFIG_FTPSSERVERADDRESS
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_FTPSSERVERADDRESS }}
        - name: OPENPALETTE_FTPSERVICECONFIG_SFTPSERVERADDRESS
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_SFTPSERVERADDRESS }}
        - name: OPENPALETTE_FTPSERVICECONFIG_FTPSERVERPORT
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_FTPSERVERPORT | quote }}
        - name: OPENPALETTE_FTPSERVICECONFIG_FTPSSERVERPORT
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_FTPSSERVERPORT | quote }}
        - name: OPENPALETTE_FTPSERVICECONFIG_SFTPSERVERPORT
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_SFTPSERVERPORT | quote }}
        - name: OPENPALETTE_FTPSERVICECONFIG_FTPSERVERIP
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_FTPSERVERIP }}
        - name: OPENPALETTE_FTPSERVICECONFIG_RELATEDUSER
          value: {{ .Values.ftpsConfig.OPENPALETTE_FTPSERVICECONFIG_RELATEDUSER }}
        - name: openpalette_ms_bpname
          value: *msName
        - name: openpalette_container_name
          value: *serverContainerName
        {{- range .Values.envs.battery_manager }}
          {{- include "oki.containerEnvs" . | nindent 8 }}
        {{- end }}
        # 云原生应用 镜像规范
        {{- if .Values.serviceImage.tenant }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- else }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- end }}
        imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
        # 云原生应用 安全上下文
        securityContext:
        {{- toYaml .Values.securityContext | nindent 10 }}
        ports:
          - containerPort: {{ .Values.ports.battery_manager }}
            protocol: TCP
        # 云原生应用 容器资源定义
        {{- if .Values.resources.battery_manager }}
        resources: {{- toYaml .Values.resources.battery_manager | nindent 10 }}
        {{- end }}
        # 云原生应用 volume 定义和使用
        volumeMounts:
          {{- range .Values.volumes.battery_manager }}
          - name: {{ .name}}
            mountPath: {{ .mountPath}}
            readOnly: {{ .readOnly}}
            subPath: {{ .subPath }}
          {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      enableServiceLinks: false
      # 云原生应用 volume 定义和使用
      volumes:
        {{- range .Values.volumes.battery_manager }}
        - name: {{ .name}}
          {{ .volumeType }}:
          {{ .volumeKey | indent 2 }}: {{ .volumeValue }}
          {{- if or (eq .volumeType "configMap") (eq .volumeType "secret") }}
          defaultMode: {{ .defaultMode }}
          {{- end }}
        {{- end }}