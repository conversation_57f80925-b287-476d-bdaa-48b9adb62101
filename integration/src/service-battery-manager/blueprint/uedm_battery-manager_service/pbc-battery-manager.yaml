apiVersion: {{ template "oki.pbc.apiVersion" . }}
kind: Component
metadata:
  #组件名，即微服务实例名，要求全局唯一；对于云原生组件要求使用宏变量；
  #对于非云原生（VNPM）组件，配置成常量，取值来自于spd文件中的service_list数组第一个元素service_name
  name: {{ include "oki.fullname" . }}-pbc
  #命名空间即租户，要求使用宏变量
  namespace: {{ .Release.Namespace | quote }}
  #K8S元数据中的资源标签，用于筛选资源
  labels:
    componentName: {{ .Release.Name }}
spec:
  # 组件服务化级别
  level: L3
  # k8s标签选择器
  selector:
    matchLabels:
      componentName: {{ .Release.Name }}
  #组件类型，格式：产品ID_分组包ID, 即分组包名称去掉版本号部分
  #产品ID和分组包ID需要和"iDN平台领域规划组件清单"上的ProductID和组件名称一致
  type: "UEDM_BATTERY-MANAGER"
  version: {{ .Chart.AppVersion }}
  description:
    zh-CN : "UEDM BATTERY业务应用"
    en-US : "Battery Service Application"
  owners: []
  coreFunction:
    exposedAPIs:
      - name: battery-manager-v1
        #API swagger文件访问路径,统一放到https://idn.zte.com.cn/apis 网站
        specification: "https://idn.zte.com.cn/apis/UEDM-BATTERY/battery-manager-v1/swagger.json"
        #API后端实现服务，如MSB服务，格式为releaseName-serviceName
        implementation: {{ .Release.Name }}-battery-manager-v1
        #API服务的发布路径,条件必选，系统间服务必填，系统内服务不填
        path: /api/battery-manager/v1
        #存放开发者文档，如API接口规范等，路径规则为path+'/docs'
        developerUI: /api/battery-manager/v1/docs
        #服务端口
        port: 29117
    #依赖的API，组件对运行环境中其它组件的依赖通过API调用的方式完成
    dependentAPIs: []
  #事件上报功能，上报功能描述组件对外发布和订阅的消息通知事件
  eventNotification:
    publishedEvents:
      #消息主题
      - topic: UEDM_BATTERY_MANAGER
        #消息/事件schema文件的访问路径，填写schema文件的存放目录即可，路径不需要到具体.schema.json文件
        #event的schema文件的实际url = 'specification url' + '/' + 'event name' + '.schema.json'
        specification: https://idn.zte.com.cn/apis/batterymanager/r1/notification
        events:
          #name字段填写消息名/事件名
          - name: ActiveAlarmBody
          - name: BatterySohPredictionDto
          - name: HistoryDataQueryDto
          - name: ManufacturerSearchDto
          - name: SupplierSearchDto
          - name: OverviewAssetConditionPo
          - name: BatteryBaseInfoVo
          - name: ConfigCenterDeleteItemBean
          - name: MonitorDeviceObjectRelationBean
          - name: LogicIdAndMocQueryBean
          - name: SiteBean
          - name: GetRelationDeviceMoListDto
          - name: TreeAssetAuthorityQueryVo
          - name: RecordIndexBean
          - name: GetMoByConditionDto
          - name: SiteSearchDto
          - name: SelectByTypeCodeDto
          - name: MonitorDeviceSfBean
          - name: RecollectionCapabilityQueryDto
          - name: RealGroupBean
          - name: IntervalStrategyQueryBean
          - name: HistoryAlarmBody
          - name: RcdReqBody
          - name: AllRecordsByMocRequestMsg
          - name: AllRecordsByMocRequestMsg
          - name: AllRecordsByMocQueryMeg
    subscribedEvents: []
  #安全功能，描述组件接口的接入控制、接入用户身份认证、功能授权等
  security:
    securitySchemes:
      #访问API时采用的认证方式，如JWT等。需要同时支持两种认证方式的场景使用逗号分隔。
      authtype:
  #组件管理运维功能
  management: []
    #是否支持重生，填true或false
    #reborn: true
    #是否支持升级，填true或false
    #upgrade: true
    #是否支持不中断主备倒换，填true或false
    #nsr: false
    #是否支持不中断升级，填true或false
    #issu: false
  #是否支持热补丁，填true或false
  #hotpatch: false