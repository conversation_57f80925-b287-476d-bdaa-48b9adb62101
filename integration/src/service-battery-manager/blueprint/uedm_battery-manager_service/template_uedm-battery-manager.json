{"kind": "Template", "apiVersion": "v1", "namespace": "${NAMESPACE}", "metadata": {"name": "@ms.manager@", "labels": {"name": "@ms.manager@"}}, "objects": [{"spec": {"replicas": "${battery-manager_replica_init}", "selector": {"name": "@ms.manager@"}, "template": {"metadata": {"labels": {"name": "@ms.manager@"}, "annotations": {"pod.beta.kubernetes.io/init-containers": "[]"}}, "spec": {"containers": [{"name": "@ms.manager@", "image": "/${NAMESPACE}/@ms.manager@:@image.version@", "imagePullPolicy": "Always", "tty": false, "stdin": false, "securityContext": {"runAsNonRoot": "#{runAsNonRoot}", "runAsUser": "#{runAsUser}", "runAsGroup": "#{runAsGroup}", "allowPrivilegeEscalation": false, "privileged": false, "capabilities": {"drop": ["NET_RAW"]}}, "command": [], "env": [{"name": "pvc_type", "value": "${pvc_type}"}, {"name": "OPENPALETTE_PG_ADDRESS", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_ADDRESS]"}, {"name": "OPENPALETTE_PG_PORT", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_PORT]"}, {"name": "OPENPALETTE_PG_DBNAME", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_DBNAME]"}, {"name": "OPENPALETTE_PG_USERNAME", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_USERNAME]"}, {"name": "OPENPALETTE_PG_PASSWORD", "value": "get_property:[${postgresql-0},OPENPALETTE_PG_PASSWORD]"}, {"name": "OPENPALETTE_KAFKA_ADDRESS", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_PORT", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_PORT]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_PORT", "value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ZOOKEEPER_PORT]"}, {"name": "OPENPALETTE_PGCACHE_ADDRESS", "value": "get_property:[${pgcache-0},OPENPALETTE_PGCACHE_ADDRESS]"}, {"name": "OPENPALETTE_PGCACHE_PORT", "value": "get_property:[${pgcache-0},OPENPALETTE_PGCACHE_PORT]"}, {"name": "OPENPALETTE_PGCACHE_DBNAME", "value": "get_property:[${pgcache-0},OPENPALETTE_PGCACHE_DBNAME]"}, {"name": "OPENPALETTE_PGCACHE_USERNAME", "value": "get_property:[${pgcache-0},OPENPALETTE_PGCACHE_USERNAME]"}, {"name": "OPENPALETTE_PGCACHE_PASSWORD", "value": "get_property:[${pgcache-0},OPENPALETTE_PGCACHE_PASSWORD]"}, {"name": "OPENPALETTE_REDIS_ADDRESS", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_PORT", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_PORT]"}, {"name": "OPENPALETTE_REDIS_PASSWORD", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_PASSWORD]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_PORT", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_PORT]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME", "value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"}, {"name": "OPENPALETTE_REDIS_ADDRESS_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_PORT_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_PORT]"}, {"name": "OPENPALETTE_REDIS_PASSWORD_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_PASSWORD]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_PORT_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_SENTINEL_PORT]"}, {"name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME_1", "value": "get_property:[${redis-1},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"}, {"name": "TZ", "value": "${TZ}"}, {"name": "db_version", "value": "${db_version}"}, {"name": "scene", "value": "${scene}"}], "ports": [{"containerPort": 29117, "protocol": "TCP"}], "volumeMounts": [{"name": "volume-for-battery", "mountPath": "/data/battery"}, {"name": "volume-for-logs", "mountPath": "/home/<USER>/logs/"}, {"name": "volume-for-dumps", "mountPath": "/home/<USER>/dump/"}, {"name": "volume-for-gclogs", "mountPath": "/home/<USER>/gclog/"}, {"name": "redis-password", "mountPath": "/etc/secrets/oes/redis", "readOnly": false}, {"name": "postgresql-password", "mountPath": "/etc/secrets/oes/pg", "readOnly": false}, {"name": "pgcache-password", "mountPath": "/etc/secrets/oes/pgcache", "readOnly": false}], "resources": {"requests": {"pod.alpha.kubernetes.io/opaque-int-resource-affinityCPU": "${battery-manager_affinityCPU_request}", "pod.alpha.kubernetes.io/opaque-int-resource-hugepage": "${battery-manager_hugepage_request}", "cpu": "${battery-manager_cpu_request}", "memory": "${battery-manager_mem_request}"}, "limits": {"cpu": "${battery-manager_cpu_limit}", "memory": "${battery-manager_mem_limit}"}}}], "initContainers": [{"name": "uedm-battery-manager-init-nonroot", "image": "/${NAMESPACE}/@ms.manager@:@image.version@", "imagePullPolicy": "Always", "tty": false, "securityContext": {"privileged": false, "allowPrivilegeEscalation": false}, "stdin": false, "command": ["/home/<USER>/chmod_file.sh"], "env": [{"name": "pvc_type", "value": "${pvc_type}"}, {"name": "LOG_FILE_LOG", "value": "/home/<USER>"}, {"name": "LOG_FILE_GCLOG", "value": "/home/<USER>/gclog"}, {"name": "OES_CHOWN_DIR_GCLOG", "value": "/home/<USER>/gclog"}, {"name": "OES_CHOWN_DIR_LOGS", "value": "/home/<USER>/logs/"}, {"name": "OES_CHOWN_DIR_DUMP", "value": "/home/<USER>/dump/"}, {"name": "OES_CHOWN_DIR_BATTERY", "value": "/data/battery"}, {"name": "OES_CHOWN_DIR_LOG", "value": "/home/<USER>"}], "ports": [], "volumeMounts": [{"name": "volume-for-battery", "mountPath": "/data/battery"}, {"name": "volume-for-logs", "mountPath": "/home/<USER>/logs/"}, {"name": "volume-for-dumps", "mountPath": "/home/<USER>/dump"}, {"name": "volume-for-gclogs", "mountPath": "/home/<USER>/gclog"}], "resources": {"requests": {"pod.alpha.kubernetes.io/opaque-int-resource-affinityCPU": "${battery-manager_affinityCPU_request}", "pod.alpha.kubernetes.io/opaque-int-resource-hugepage": "${battery-manager_hugepage_request}", "cpu": "${battery-manager_cpu_request}", "memory": "${battery-manager_mem_request}"}, "limits": {"cpu": "${battery-manager_cpu_limit}", "memory": "${battery-manager_mem_limit}"}}}], "restartPolicy": "Always", "volumes": [{"name": "volume-for-battery", "hostPath": {"path": "/data/battery"}}, {"name": "volume-for-logs", "${volume-for-logs_type}": {"${volume-for-logs_key}": "${volume-for-logs_value}"}}, {"name": "volume-for-dumps", "${volume-for-dumps_type}": {"${volume-for-dumps_key}": "${volume-for-dumps_value}"}}, {"name": "volume-for-gclogs", "${volume-for-gclogs_type}": {"${volume-for-gclogs_key}": "${volume-for-gclogs_value}"}}, {"name": "redis-password", "${redis-password_type}": {"${redis-password_key}": "${redis-password_value}"}}, {"name": "postgresql-password", "${postgresql-password_type}": {"${postgresql-password_key}": "${postgresql-password_value}"}}, {"name": "pgcache-password", "${pgcache-password_type}": {"${pgcache-password_key}": "${pgcache-password_value}"}}], "terminationGracePeriodSeconds": 30}}, "strategy": {"type": "Rolling", "rollingParams": {"timeoutSeconds": "600", "maxUnavailable": "25%", "maxSurge": "25%"}}}, "kind": "DeploymentConfig", "apiVersion": "v1", "metadata": {"name": "@ms.manager@", "namespace": "${NAMESPACE}"}}], "parameters": [{"name": "battery-manager_replica_init", "displayName": "battery-manager_replica_init", "value": "1", "section": "other", "type": "string", "description": "battery-manager_replica_init"}, {"name": "battery-manager_replica_min", "displayName": "battery-manager_replica_min", "value": "1", "section": "other", "type": "string", "description": "battery-manager_replica_min"}, {"name": "battery-manager_replica_max", "displayName": "battery-manager_replica_max", "value": "1", "section": "other", "type": "string", "description": "battery-manager_replica_max"}, {"name": "battery-manager_mem_limit", "displayName": "battery-manager_mem_limit", "description": "battery-manager_mem_limit", "section": "other", "value": "4Gi", "type": "string"}, {"name": "battery-manager_cpu_limit", "displayName": "battery-manager_cpu_limit", "description": "battery-manager_cpu_limit", "section": "other", "value": "2", "type": "string"}, {"name": "battery-manager_cpu_request", "displayName": "battery-manager_cpu_request", "description": "battery-manager_cpu_request", "section": "other", "value": "0.1", "type": "string"}, {"name": "battery-manager_mem_request", "displayName": "battery-manager_mem_request", "description": "battery-manager_mem_request", "section": "other", "value": "0.1Gi", "type": "string"}, {"name": "battery-manager_affinityCPU_request", "displayName": "battery-manager_affinityCPU_request", "description": "battery-manager_affinityCPU_request", "section": "other", "value": "0", "type": "string"}, {"name": "battery-manager_hugepage_request", "displayName": "battery-manager_hugepage_request", "description": "battery-manager_hugepage_request", "section": "other", "value": "0", "type": "string"}, {"displayName": "", "name": "pvc_type", "section": "env", "value": "nfs", "type": "string", "description": ""}, {"name": "runAsNonRoot", "displayName": "runAsNonRoot", "value": false, "section": "other", "type": "boolean", "description": ""}, {"name": "runAsUser", "displayName": "runAsUser", "value": 0, "section": "other", "type": "int", "description": ""}, {"name": "runAsGroup", "displayName": "runAsGroup", "value": 0, "section": "other", "type": "int", "description": ""}, {"name": "NAMESPACE", "displayName": "", "description": "", "value": "openshift", "section": "None"}, {"name": "@service.name@", "displayName": "", "description": "", "value": "@service.name@", "section": "route"}, {"name": "@ms.manager@", "displayName": "", "description": "", "value": "@ms.manager@", "section": "route"}, {"name": "postgresql-0", "displayName": "postgresql-0", "description": "postgresql-0", "value": "", "section": "commonService", "subSection": "PostgreSQL"}, {"name": "kafka-0", "displayName": "kafka-0", "description": "kafka-0", "value": "", "section": "commonService", "subSection": "Kafka"}, {"name": "pgcache-0", "displayName": "pgcache-0", "description": "pgcache-0", "value": "", "section": "commonService", "subSection": "PostgreSQLCACHE"}, {"name": "redis-0", "displayName": "redis-0", "description": "redis-0", "value": "", "section": "commonService", "subSection": "redis", "type": "string"}, {"name": "redis-1", "displayName": "redis-1", "description": "redis-1", "value": "", "section": "commonService", "subSection": "redis", "type": "string"}, {"name": "TZ", "displayName": "TZ", "description": "", "value": "Asia/Shanghai", "section": "env"}, {"name": "db_version", "displayName": "db_version", "description": "", "value": "", "section": "env", "type": "string"}, {"name": "volume-for-logs_type", "displayName": "volume-for-logs", "description": "", "value": "hostPath", "section": "volumes", "type": "string"}, {"name": "volume-for-logs_key", "displayName": "volume-for-logs", "description": "", "value": "path", "section": "volumes", "type": "string"}, {"name": "volume-for-logs_value", "displayName": "volume-for-logs", "description": "", "value": "/paasdata/op-tenant/uedm/otcp/logs/uedm-battery", "section": "volumes", "type": "string"}, {"name": "volume-for-dumps_type", "displayName": "volume-for-dumps", "description": "", "value": "hostPath", "section": "volumes", "type": "string"}, {"name": "volume-for-dumps_key", "displayName": "volume-for-dumps", "description": "", "value": "path", "section": "volumes", "type": "string"}, {"name": "volume-for-dumps_value", "displayName": "volume-for-dumps", "description": "", "value": "/paasdata/op-tenant/uedm/otcp/dumps/uedm-battery", "section": "volumes", "type": "string"}, {"name": "volume-for-gclogs_type", "displayName": "volume-for-gclogs", "description": "", "value": "hostPath", "section": "volumes", "type": "string"}, {"name": "volume-for-gclogs_key", "displayName": "volume-for-gclogs", "description": "", "value": "path", "section": "volumes", "type": "string"}, {"name": "volume-for-gclogs_value", "displayName": "volume-for-gclogs", "description": "", "value": "/paasdata/op-tenant/uedm/otcp/gclogs/uedm-battery", "section": "volumes", "type": "string"}, {"name": "scene", "displayName": "scene", "description": "", "value": "", "section": "env", "type": "string"}, {"name": "redis-password_type", "displayName": "redis-password", "description": "", "value": "secret", "section": "volumes", "type": "string"}, {"name": "redis-password_key", "displayName": "redis-password", "description": "", "value": "secretName", "section": "volumes", "type": "string"}, {"name": "redis-password_value", "displayName": "redis-password", "description": "", "value": "get_property:[${redis-0},openpalette_secret_name]", "section": "volumes", "type": "string"}, {"name": "postgresql-password_type", "displayName": "postgresql-password", "description": "", "value": "secret", "section": "volumes", "type": "string"}, {"name": "postgresql-password_key", "displayName": "postgresql-password", "description": "", "value": "secretName", "section": "volumes", "type": "string"}, {"name": "postgresql-password_value", "displayName": "postgresql-password", "description": "", "value": "get_property:[${postgresql-0},openpalette_secret_name]", "section": "volumes", "type": "string"}, {"name": "pgcache-password_type", "displayName": "pgcache-password", "description": "", "value": "secret", "section": "volumes", "type": "string"}, {"name": "pgcache-password_key", "displayName": "pgcache-password", "description": "", "value": "secretName", "section": "volumes", "type": "string"}, {"name": "pgcache-password_value", "displayName": "pgcache-password", "description": "", "value": "get_property:[${pgcache-0},openpalette_secret_name]", "section": "volumes", "type": "string"}, {"name": "ftp_0", "displayName": "ftp_0", "description": "ftp_0", "value": "", "section": "commonService", "subSection": "FTP", "type": "string"}], "vnpm_param": {"vnpm_object": [{"name": "@ms.manager@", "route_list": [{"serviceName": "${@ms.manager@}", "version": "v1", "url": "/api/@ms.manager@/v1", "protocol": "REST", "enable_tls": false, "path": "", "lb_policy": "round-robin", "port": "29117", "visualRange": "0", "network_plane_type": "net_api", "enable_client_verify": false}], "common_service": [{"logicName": "${postgresql-0}"}, {"logicName": "${kafka-0}"}, {"logicName": "${pgcache-0}"}, {"logicName": "${redis-0}"}, {"logicName": "${redis-1}"}], "networks": {"ports": [{"attach_to_network": "net_api", "attributes": {"nic_name": "eth0", "function": "std", "nic_type": "normal", "accelerate": "false"}}, {"attach_to_network": "lan", "attributes": {"nic_name": "eth1", "function": "std", "nic_type": "normal", "accelerate": "false"}}]}, "cluster_info": {"cluster_type": "", "labelselector": []}, "isUseServiceDiscovery": true}]}, "eps_param": {"replicasPara_list": [{"ms_name": "@ms.manager@", "replicasMin": "${battery-manager_replica_min}", "replicasMax": "${battery-manager_replica_max}"}], "auto_policy": {"@ms.manager@": {"@ms.manager@": []}}}}