<?xml version="1.0" encoding="UTF-8"?>
<service>
  <common-service display_name="Kafka" orch_name="commsrv_kafka_bp">
    <instance name="zenap_kafka" />
  </common-service>
  <common-service display_name="PostgreSQL" orch_name="commsrv_pg_bp">
    <instance name="<EMAIL>@" />
  </common-service>
  <common-service display_name="Redis" orch_name="commsrv_internal_redis_bp">
    <instance name="zenap_redis" />
  </common-service>
  <common-service display_name="uedm_@uedm_redis@" orch_name="@uedm_orch_name@">
    <instance name="uedm_@uedm_redis@" />
  </common-service>
  <common-service display_name="PostgreSQLCACHE" orch_name="commsrv_pgcache_bp">
    <instance name="zenap_pg_cache"/>
  </common-service>
  <common-service display_name="uedm_redis1" orch_name="commsrv_inner_redis_cluster_bp">
  <instance name="uedm_redis1" />
  </common-service>
  <common-service display_name="FTP/SFTP/FTPS" orch_name="commsrv_zenap_ftp_bp">
    <instance name="zenap_ftp_ftps_sftp" username="ict"/>
  </common-service>
</service>
