<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zte.uedm</groupId>
		<artifactId>uedm-battery</artifactId>
		<version>${revision}</version>
	</parent>
	<artifactId>uedm-battery-iui</artifactId>
	<name>电池管理前端</name>
	<packaging>pom</packaging>
 <build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources-blueprint</id>
						<phase>process-resources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>target/iui</outputDirectory>
							<resources>
								<resource>
									<directory>src/iui</directory>
									<filtering>false</filtering>
									<includes>
										<include>**/*</include>
									</includes>
								</resource>
							</resources>
							<overwrite>true</overwrite>
						</configuration>
					</execution>

				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>distribution</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target name="distribution">
								<tar destfile="target/${project.artifactId}-${project.version}.tar.gz"
									longfile="posix" compression="gzip">
									<tarfileset dir="target" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes">
										<include name="iui/**" />
									</tarfileset>
								</tar>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>attach-artifact</goal>
						</goals>
						<configuration>
							<artifacts>
								<artifact>
									<file>target/${project.artifactId}-${project.version}.tar.gz</file>
									<type>tar.gz</type>
								</artifact>
							</artifacts>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>