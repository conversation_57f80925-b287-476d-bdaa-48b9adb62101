<template>
    <el-popover
        ref="filterPopover"
        :visible="popoverVisible"
        placement="bottom-end"
        trigger="manual"
        popper-class="filter-popover"
        :visible-arrow="false"
        popper-style="width:334px;inset: 0px 15px auto auto;"
    >
        <template #reference>
            <span @click="showPopoverHandler">
                <slot><span class="popover-trigger-button plx-ico-col-16"></span></slot>
            </span>
        </template>
        <div class="check-control__row">
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
                {{ $t('button.selectall') }}
            </el-checkbox>
            <el-button type="text" class="text-operation-button" @click="reset">
                {{ $t('button.reset') }}
            </el-button>
            <span class="checked-item">
                {{ `${$t('button.selected')}/${$t('button.selectAble')}:` }}
                {{ checkedCount }}/{{ checkAbleSum }}
            </span>
        </div>
        <div class="search-input__row">
            <el-input
                v-model="searchStr"
                clearable
                size="small"
                :prefix-icon="ElIconSearch"
                :placeholder="$t('placeholder.input')"
                @input="inputDebounceChangeHandler"
            ></el-input>
        </div>
        <el-checkbox-group v-model="checkedList" class="check-box__group" @change="handleCheckboxChange">
            <div class="listContent" :style="listStyle">
                <el-checkbox
                    v-for="(option, index) in filteredOptions"
                    :key="`${option.prop}`"
                    :label="option.prop"
                    :disabled="option.disabled"
                    class="check-box__row"
                >
                    {{ option.label }}
                    <span v-show="showSort" class="operation-arrow" @click="stopDefaultEvent">
                        <el-icon
                            v-show="index > 0"
                            class="el-icon-top arrow-icon arrow-icon__up"
                            @click="e => moveUpHandler(index, option)"
                        >
                            <el-icon-top />
                        </el-icon>
                        <el-icon
                            v-show="index < filteredSum - 1"
                            class="el-icon-bottom arrow-icon arrow-icon__down"
                            @click="e => moveDownHandler(index, option)"
                        >
                            <el-icon-bottom />
                        </el-icon>
                    </span>
                </el-checkbox>
            </div>
        </el-checkbox-group>
        <div class="popover-footer">
            <el-button type="primary" @click="confirm">
                {{ $t('button.confirm') }}
            </el-button>
            <el-button @click="cancel">
                {{ $t('button.cancel') }}
            </el-button>
        </div>
    </el-popover>
</template>

<script>
import { Top as ElIconTop, Bottom as ElIconBottom, Search as ElIconSearch } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../utils/gogocodeTransfer';
import map from 'lodash/map';
import filter from 'lodash/filter';
import debounce from 'lodash/debounce';
import differenceBy from 'lodash/differenceBy';

const DEBOUNCE_TIME = 100;

export default {
    data() {
        return {
            popoverVisible: false,
            searchStr: '',
            isIndeterminate: true,
            checkAll: false,
            showSort: true,
            checkedList: [],
            allOptions: [],
            filteredOptions: [],
            ElIconSearch,
        };
    },
    components: {
        ElIconTop,
        ElIconBottom,
    },
    props: {
        list: {
            type: Array,
            default: function () {
                return [];
            },
        },
        maxHeight: {
            type: Number,
            default: 340,
        },
    },
    computed: {
        checkedCount() {
            return this.checkedList.length;
        },
        checkAbleSum() {
            return this.allOptions.length;
        },
        filteredSum() {
            return this.filteredOptions.length;
        },
        listStyle() {
            return {
                'max-height': this.maxHeight + 'px',
            };
        },
    },
    watch: {
        popoverVisible(val) {
            if (val) {
                this.initOptions();
            }
        },
    },
    created() {
        this.inputDebounceChangeHandler = debounce(this.filterOptionsHandler, DEBOUNCE_TIME);
        this.tmpCheckedList = [];
    },
    methods: {
        common() {
            const checkedCount = this.checkedList.length;
            this.checkAll = checkedCount === this.allOptions.length;
            this.isIndeterminate = checkedCount > 0 && !this.checkAll;
        },
        resetSort() {
            this.searchStr = '';
            this.showSort = true;
        },
        /* Started by AICoder, pid:******************************** */
        initOptions() {
            this.checkedList = [];
            this.allOptions = this.list.map(item => {
                if (item.enable) {
                    this.checkedList.push(item.id);
                }
                return {
                    prop: item.id,
                    label: item.name,
                    disabled: item.defaultFixed,
                    sequence: item.sequence,
                };
            });
            this.filteredOptions = [...this.allOptions];
            this.common();
        },
        reset() {
            /* Started by AICoder, pid:4c7fc89b0e8e684146340b7da00282252706b8dd */
            this.resetSort();
            this.checkedList = [];
            this.allOptions = this.list.map(item => {
                if (item.defaultEnable) {
                    this.checkedList.push(item.id);
                }
                return {
                    prop: item.id,
                    label: item.name,
                    disabled: item.defaultFixed,
                    sequence: item.sequence,
                    defaultIndex: item.defaultIndex,
                };
            });

            this.filteredOptions = [...this.allOptions];

            // 按照defaultIndex进行排序
            this.filteredOptions.sort((a, b) => a.defaultIndex - b.defaultIndex);

            // 遍历排序后的数组，将defaultIndex的值赋给sequence
            this.filteredOptions.forEach(item => {
                item.sequence = item.defaultIndex;
            });

            this.common();
            /* Ended by AICoder, pid:4c7fc89b0e8e684146340b7da00282252706b8dd */
        },
        /* Ended by AICoder, pid:******************************** */
        showPopoverHandler() {
            this.popoverVisible = !this.popoverVisible;
            if (!this.popoverVisible) {
                this.checkedList = map(this.tmpCheckedList, it => it.prop);
            }
        },
        handleCheckAllChange(val) {
            this.checkedList = val ? map(this.filteredOptions, option => option.prop) : [];
            if (!val) {
                this.list.forEach(item => {
                    if (item.defaultFixed) {
                        this.checkedList.push(item.id);
                    }
                });
            }
            this.common();
        },
        handleCheckboxChange(val) {
            const checkedCount = val.length;
            this.checkAll = checkedCount === this.allOptions.length;
            this.isIndeterminate = checkedCount > 0 && !this.checkAll;
        },
        filterOptionsHandler() {
            const searchStr = this.searchStr.trim().toLowerCase();
            this.showSort = !searchStr.length;
            if (!searchStr) {
                this.filteredOptions = [...this.allOptions];
                return;
            }
            this.filteredOptions = filter(this.allOptions, option => option.label.toLowerCase().includes(searchStr));
        },
        stopDefaultEvent(e) {
            e.stopPropagation && e.stopPropagation();
            e.preventDefault && e.preventDefault();
        },
        moveUpHandler(index) {
            if (index <= 0) {
                // 如果是第一个元素，则不进行操作
                return;
            }
            const prevIndex = index - 1;
            const current = this.filteredOptions[index];
            const prev = this.filteredOptions[prevIndex];

            // 交换sequence值
            [current.sequence, prev.sequence] = [prev.sequence, current.sequence];
            this.moveHandler(index, 'up');
        },
        moveDownHandler(index) {
            if (index >= this.filteredOptions.length - 1) {
                // 如果是最后一个元素，则不进行操作
                return;
            }
            const nextIndex = index + 1;
            const current = this.filteredOptions[index];
            const next = this.filteredOptions[nextIndex];

            // 交换sequence值
            [current.sequence, next.sequence] = [next.sequence, current.sequence];
            this.moveHandler(index, 'down');
        },
        moveHandler(index, type = 'up') {
            const targetIndex = type === 'up' ? index - 1 : index + 1;
            const targetItem = this.filteredOptions.splice(index, 1);
            this.filteredOptions.splice(targetIndex, 0, targetItem[0]);
            this.sortAllOptions();
        },
        sortAllOptions() {
            const deferenceList = differenceBy(this.allOptions, this.filteredOptions, 'prop');
            this.allOptions = [...this.filteredOptions, ...deferenceList];
        },
        confirm() {
            const checkedSortList = filter(this.allOptions, option => this.checkedList.includes(option.prop));
            const resultList = (this.allOptions || []).map(item => {
                const selected = this.checkedList.includes(item.prop);
                return {
                    ...item,
                    enable: selected,
                };
            });
            this.resetSort();
            $emit(this, 'filterOk', resultList);
            this.tmpCheckedList = checkedSortList;
            this.popoverVisible = false;
        },
        cancel() {
            this.resetSort();
            this.popoverVisible = false;
            this.checkedList = map(this.tmpCheckedList, it => it.prop);
        },
    },
    emits: ['filterOk'],
};
</script>

<style scoped>
.popover-trigger-button {
    padding: 5px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    vertical-align: middle;
    cursor: pointer;
}
html.dark .popover-trigger-button {
    border: 1px solid #474a59;
}
.search-input__row {
    margin: 8px 0;
}
.check-box__row {
    position: relative;
    width: 100%;
    height: 30px;
    margin: 0;
    line-height: 30px;
}
.check-box__row:hover {
    background: #f5f7fa;
}
html.dark .filter-popover .check-box__group .check-box__row {
    border: unset;
}
html.dark .check-box__row:hover {
    background: rgba(49, 66, 81, 0.5);
}
.check-box__row:hover .operation-arrow {
    visibility: visible;
}
.operation-arrow {
    position: absolute;
    right: 16px;
    visibility: hidden;
    float: right;
    cursor: pointer;
}
.arrow-icon {
    font-size: 12px;
    color: #606266;
}
.arrow-icon:hover {
    color: #1993ff;
}
.arrow-icon__up {
    margin-right: 20px;
}
.text-operation-button {
    margin-left: 10px;
    padding: 0;
    font-size: 14px;
}
.checked-item {
    position: absolute;
    right: 20px;
}
.popover-footer {
    margin-top: 30px;
    text-align: right;
}
.listContent {
    min-height: 200px;
    overflow: auto;
}
::v-deep .check-box__row .el-checkbox__label {
    width: 210px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.check-control__row {
    display: flex;
    align-items: center;
}
::v-deep .el-popper.is-light {
    inset: 5px 55px auto auto !important;
}
</style>
