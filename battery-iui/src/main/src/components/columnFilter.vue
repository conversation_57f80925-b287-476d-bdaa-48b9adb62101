<template>
    <el-tooltip v-if="tip" effect="dark" :content="tip" placement="top">
        <el-popover
            ref="columnFilter"
            v-model:visible="filterVisible"
            placement="bottom"
            trigger="manual"
            class="column-filter"
            :class="{ dark: isDark }"
            :width="326"
            :popper-class="popperClass"
            :visible-arrow="false"
            :teleported="false"
        >
            <div class="wrap">
                <!-- 顶部工具栏 -->
                <div class="tool-bar">
                    <span class="label">
                        {{ `${$t('button.selected')}/${$t('button.selectAble')}:` }}
                        {{ checkedNumber }}/{{ checkAllNumber }}
                    </span>
                    <el-checkbox
                        v-model="checkAll"
                        :disabled="isDisabledCheckAll"
                        :indeterminate="isIndeterminate"
                        @change="handleCheckAllChange"
                    >
                        {{ $t('button.selectall') }}
                    </el-checkbox>
                    <el-link
                        type="primary"
                        class="reset"
                        :underline="false"
                        :disabled="!columns.length"
                        @click="handleReset"
                    >
                        {{ $t('button.reset') }}
                    </el-link>
                </div>
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <el-input
                        v-model="searchTxt"
                        clearable
                        size="small"
                        :prefix-icon="Search"
                        :placeholder="$t('placeholder.input')"
                    ></el-input>
                </div>
                <!-- 选项内容 -->
                <div v-loading="loading" class="content" :style="{ maxHeight: FixedHeight + 'px' }">
                    <el-checkbox-group v-model="checkedList">
                        <template v-for="col in currentList">
                            <div
                                v-if="isVisible(col)"
                                :key="col.id"
                                class="item"
                                :class="{ hideArrow: searchTxt.trim() }"
                            >
                                <el-checkbox
                                    :label="col.id"
                                    :title="col.name"
                                    :disabled="col.defaultFixed"
                                    @change="handleCheckedItemChange($event, col)"
                                >
                                    {{ col.name }}
                                </el-checkbox>
                                <span v-if="!searchTxt.trim()" class="operation-arrow">
                                    <el-icon class="arrow-icon arrow-icon__up" @click="e => moveUp(col)">
                                        <Top />
                                    </el-icon>
                                    <el-icon class="arrow-icon arrow-icon__down" @click="e => moveDown(col)">
                                        <Bottom />
                                    </el-icon>
                                </span>
                            </div>
                        </template>
                    </el-checkbox-group>
                    <div
                        v-show="!currentList.length"
                        class="no-data"
                        :style="{ height: 100 + 'px', 'line-height': 100 + 'px' }"
                    >
                        <span>{{ $t('common.noData') }}</span>
                    </div>
                </div>
                <!-- 底部按钮栏 -->
                <div class="bottom-bar">
                    <el-button type="primary" :disabled="!columns.length" @click="handleSave">
                        {{ $t('button.confirm') }}
                    </el-button>
                    <el-button @click="handleCancel">
                        {{ $t('button.cancel') }}
                    </el-button>
                </div>
            </div>
            <template v-slot:reference>
                <span class="icon-button plx-ico-col-16" @click="handleShow"></span>
            </template>
        </el-popover>
    </el-tooltip>
</template>

<script>
import { Top, Bottom, Search } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../utils/gogocodeTransfer';
export default {
    data() {
        return {
            loading: false,
            timer: null,
            // 弹出窗口是否显示
            filterVisible: false,
            // 全选勾选
            checkAll: false,
            // 全选复选框半勾选状态
            isIndeterminate: false,
            // 搜索框文本
            searchTxt: '',
            // 已选列表
            checkedList: [],
            // 所有选项列
            columns: [],
            // 当前展示选项列
            currentList: [],
            sequenceMin: -1,
            sequenceMax: -1,
            Search,
            Top,
            Bottom,
        };
    },
    // components: {
    //     ElIconTop,
    //     ElIconBottom,
    // },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        height: {
            type: Number,
            default: 450,
        },
        tip: {
            // 展示按钮图标提示信息
            type: String,
            default: '',
        },
        isFixedSort: {
            // 不可用字段是否可以参与排序,true为参与排序，false不参与
            type: Boolean,
            default: true,
        },
        value: {
            type: Array,
            default: () => {
                return [
                    // 必须字段
                    // {
                    //     defaultEnable: true,
                    //     defaultFixed: true,
                    //     defaultIndex: 1,
                    //     enable: true,
                    //     id: '',
                    //     name: '',
                    //     sequence: 1,
                    // }
                ];
            },
        },
    },
    created() {
        // document.addEventListener('click', this.documentClick);
    },
    mounted() {
        this.init();
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        popperClass() {
            let cssStr = 'column-filter-pop';
            if (this.$store.getters.getIsDark) {
                cssStr = 'column-filter-pop dark';
            }
            return cssStr;
        },
        FixedHeight() {
            let h = this.height - 162;
            if (!h) {
                h = 200;
            }
            return h;
        },
        checkedNumber() {
            // 已选个数
            let num = 0;
            if (this.columns.length) {
                this.columns.forEach(d => {
                    if (!d.defaultFixed && d.enable && this.isVisible(d)) {
                        num++;
                    }
                });
            }
            return num;
        },
        checkAllNumber() {
            // 可选总数(不包含defaultFixed属性的选项)
            let num = 0;
            if (this.columns.length) {
                this.columns.forEach(d => {
                    if (!d.defaultFixed && this.isVisible(d)) {
                        num++;
                    }
                });
            }
            return num;
        },
        isDisabledCheckAll() {
            // 全选按钮是否不可用
            let tag = true;
            if (this.currentList.length) {
                this.currentList.forEach(d => {
                    if (!d.defaultFixed && this.isVisible(d)) {
                        tag = false;
                    }
                });
            }
            return tag;
        },
        alarmRights() {
            return this.rights['operation.fm.currentAlarm'];
        },
    },
    watch: {
        filterVisible(val) {
            if (val) {
                this.init();
            }
        },
        searchTxt() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            if (!this.loading) {
                this.loading = true;
            }
            this.timer = setTimeout(() => {
                this.filterList();
            }, 500);
        },
        value: {
            deep: true,
            handler() {
                this.init();
            },
        },
    },
    beforeUnmount() {
        // document.removeEventListener('click', this.documentClick);
    },
    methods: {
        init() {
            this.searchTxt = '';
            this.checkedList = [];
            this.columns = [];
            this.currentList = [];
            this.sequenceMin = -1;
            this.sequenceMax = -1;
            if (Array.isArray(this.value) && this.value.length) {
                this.columns = JSON.parse(JSON.stringify(this.value));
                this.columns.sort((a, b) => a.sequence - b.sequence);
                this.currentList = this.columns;
                this.updataListCheck();
                this.columns.forEach(col => {
                    if (
                        (this.isFixedSort || !col.defaultFixed) &&
                        (col.id !== 'alarm' || this.alarmRights) &&
                        col.assetAttributeShow
                    ) {
                        if (this.sequenceMin === -1) {
                            this.sequenceMin = col.sequence;
                        }
                        this.sequenceMax = col.sequence;
                    }
                });
            }
        },
        isVisible(d) {
            // 是否可见项
            return (
                (d.id !== 'alarm' || this.alarmRights) &&
                (!Object.prototype.hasOwnProperty.call(d, 'assetAttributeShow') || d.assetAttributeShow)
            );
        },
        // 更新全选和单个复选框状态
        updataListCheck() {
            this.checkedList = [];
            let checkN = 0;
            let checkedN = 0;
            this.currentList.forEach(d => {
                if (d.enable) {
                    this.checkedList.push(d.id);
                    if (!d.defaultFixed && this.isVisible(d)) {
                        checkedN++;
                    }
                }
                if (!d.defaultFixed && this.isVisible(d)) {
                    checkN++;
                }
            });
            this.checkAll = false;
            if (checkedN && checkN === checkedN) {
                this.checkAll = true;
            }
            this.isIndeterminate = checkedN > 0 && checkedN < checkN;
        },
        // 全选改变
        handleCheckAllChange(val) {
            this.checkedList = [];
            this.isIndeterminate = false;
            if (val) {
                // 全选
                if (this.currentList.length) {
                    this.currentList.forEach(d => {
                        if (!d.defaultFixed && this.isVisible(d)) {
                            d.enable = true;
                        }
                        if (d.enable && this.isVisible(d)) {
                            this.checkedList.push(d.id);
                        }
                    });
                }
            } else {
                // 全空
                if (this.currentList.length) {
                    this.currentList.forEach(d => {
                        if (!d.defaultFixed && this.isVisible(d)) {
                            d.enable = false;
                        }
                        if (d.enable && this.isVisible(d)) {
                            this.checkedList.push(d.id);
                        }
                    });
                }
            }
        },
        // 单个复选框勾选
        handleCheckedItemChange(val, item) {
            if (val) {
                item.enable = true;
                this.selectedNumber++;
            } else {
                item.enable = false;
                this.selectedNumber--;
            }
            this.updataListCheck();
        },
        // 选项过滤
        filterList() {
            if (this.searchTxt.trim()) {
                this.currentList = [];
                this.columns.forEach(d => {
                    if ((d.name || '').toLowerCase().includes(this.searchTxt.trim().toLowerCase())) {
                        this.currentList.push(d);
                    }
                });
            } else {
                // 没有搜索条件时
                this.currentList = this.columns;
            }
            if (this.loading) {
                this.loading = false;
            }
            this.updataListCheck();
            $emit(this, 'search', this.searchTxt.trim());
        },
        moveUp(clickItem) {
            let moveIndex = clickItem.sequence;
            if (moveIndex === this.sequenceMin || this.columns.length === 0) {
                return;
            }
            let isMove = false;
            let prevItem = {};
            this.columns.forEach(item => {
                if (this.isVisible(item) && (this.isFixedSort || !item.defaultFixed)) {
                    if (item.id === clickItem.id) {
                        isMove = true;
                        item.sequence = prevItem.sequence;
                        prevItem.sequence = moveIndex;
                    } else {
                        if (!isMove) {
                            prevItem = item;
                        }
                    }
                }
            });
            this.columns.sort((a, b) => a.sequence - b.sequence);
        },
        moveDown(clickItem) {
            let moveIndex = clickItem.sequence;
            if (moveIndex === this.sequenceMax || this.columns.length === 0) {
                return;
            }
            let isMove = false;
            let moveItem = {};
            this.columns.forEach(item => {
                if (this.isVisible(item) && (this.isFixedSort || !item.defaultFixed)) {
                    if (item.id === clickItem.id) {
                        isMove = true;
                        moveItem = item;
                    } else {
                        if (isMove) {
                            isMove = false;
                            moveItem.sequence = item.sequence;
                            item.sequence = moveIndex;
                        }
                    }
                }
            });
            this.columns.sort((a, b) => a.sequence - b.sequence);
        },
        // 重置
        handleReset() {
            this.searchTxt = '';
            this.checkedList = [];
            this.columns = [];
            this.currentList = [];
            if (Array.isArray(this.value) && this.value.length) {
                this.columns = JSON.parse(JSON.stringify(this.value));
                this.columns.forEach(d => {
                    d.enable = d.defaultEnable;
                    d.sequence = d.defaultIndex;
                });
                this.columns.sort((a, b) => a.sequence - b.sequence);
                this.currentList = this.columns;
                this.updataListCheck();
            }
        },
        handleShow() {
            this.filterVisible = !this.filterVisible;
            $emit(this, 'show', this.filterVisible);
            // this.$refs.columnFilter.doToggle();
        },
        hide() {
            if (this.$refs.columnFilter) {
                this.$refs.columnFilter.hide();
            }
        },
        documentClick() {
            this.hide();
        },
        handleSave() {
            let list = [];
            if (this.columns.length) {
                this.columns.forEach((d, index) => {
                    d.sequence = index + 1;
                    list.push(d);
                });
            }
            $emit(this, 'save', list);
            this.hide();
        },
        handleCancel() {
            this.hide();
        },
    },
    emits: ['search', 'show', 'save', 'update:value'],
};
</script>

<style lang="scss" scoped>
.column-filter {
    .icon-button {
        display: inline-block;
        font-size: 14px;
        width: 32px;
        height: 32px;
        line-height: 13px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        padding: 8px;
        background: none;
        border: 1px solid #d9d9d9;
        color: #595959;
        cursor: pointer;
        border-radius: 4px;
        vertical-align: middle;
        &:hover {
            border-color: #1993ff;
            color: #1993ff;
        }
    }
    &.dark {
        .icon-button {
            border-color: #474a59;
            color: #d9d9d9;
            &:hover {
                border-color: #1993ff;
                color: #1993ff;
            }
        }
    }
}
</style>

<style lang="scss" scoped>
.column-filter-pop {
    .wrap {
        width: 300px;
        font-size: 14px;
    }
    .tool-bar {
        height: 20px;
        line-height: 20px;
        .reset {
            margin-left: 10px;
            vertical-align: top;
        }
        .label {
            float: right;
            font-size: 14px;
        }
    }
    .search-bar {
        padding: 8px 0;
    }
    .content {
        overflow: auto;
        .item {
            padding-right: 66px;
            position: relative;
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            &:hover {
                background-color: #f5f7fa;
                .operation-arrow {
                    visibility: visible;
                }
            }
            &.hideArrow {
                padding-right: 0px;
            }
        }
        .el-checkbox {
            display: block;
            margin: 0;
        }
        .el-checkbox__input.is-checked + .el-checkbox__label {
            color: #404040;
            max-width: calc(100% - 25px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: middle;
            margin-top: -2px;
        }
        .el-checkbox.is-disabled .el-checkbox__label,
        .el-checkbox.is-disabled.is-checked .el-checkbox__label {
            color: #bfbfbf;
        }
        .operation-arrow {
            position: absolute;
            right: 16px;
            top: 0;
            cursor: pointer;
            visibility: hidden;
        }
        .arrow-icon {
            font-size: 12px;
            height: 16px;
            line-height: 16px;
            width: 16px;
            text-align: center;
            vertical-align: middle;
            &:hover {
                color: #1993ff;
            }
        }
        .arrow-icon + .arrow-icon {
            margin-left: 16px;
        }
    }
    .bottom-bar {
        text-align: right;
        padding: 24px 12px 12px 12px;
    }
    &.dark {
        color: #d9d9d9;
        .el-checkbox__input.is-checked + .el-checkbox__label {
            color: #d9d9d9;
        }
        .el-checkbox.is-disabled .el-checkbox__label,
        .el-checkbox.is-disabled.is-checked .el-checkbox__label {
            color: #666;
        }
        .content {
            .item {
                &:hover {
                    background-color: rgba(49, 66, 81, 0.5);
                }
            }
            .arrow-icon {
                color: #a4a7b3;
                &:hover {
                    color: #1993ff;
                }
            }
        }
    }
}
</style>
