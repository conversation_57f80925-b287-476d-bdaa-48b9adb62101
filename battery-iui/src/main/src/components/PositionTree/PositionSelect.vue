<template>
    <el-popover
        v-model="visible"
        placement="bottom"
        :width="popoverWidth"
        trigger="click"
        @show="handleShow"
        @hide="handleHide"
    >
        <div class="treeWrap" :style="{ maxHeight: maxHeight + 'px' }">
            <position-tree
                ref="positionTreeRef"
                :scene-type="sceneType"
                :check-mode="checkMode"
                @selected="handleSelected"
            ></position-tree>
        </div>
        <template v-slot:reference>
            <el-select
                v-model="selectedNodesId"
                multiple
                collapse-tags
                clearable
                popper-class="hide-select-popper-tree"
                @remove-tag="handleRemoveTag"
                @clear="handleClearable"
            >
                <el-option v-for="item in selectedNodes" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </template>
    </el-popover>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer';
import PositionTree from './PositionSelectTree';
export default {
    components: {
        'position-tree': PositionTree
    },
    props: {
        popoverWidth: {
            type: Number,
            default: 196,
        },
        maxHeight: {
            type: Number,
            default: 300,
        },
        showLabelNumber: {
            // 显示的数据项数量：默认显示1个，其他的以计数项显示。
            type: Number,
            default: 1,
        },
        isValidate: {
            type: Boolean,
            default: false,
        },
        inputWidth: {
            type: Number,
            default: 200,
        },
        sceneType: {
            type: String,
            default: '',
        },
        // 树勾选模式：realtimeExport-实时导出；historySite-站点历史导出
        checkMode: {
            type: String,
            default: 'realtimeExport',
        },
    },
    data() {
        return {
            visible: false,
            inputClearable: false,
            opened: false,
            treeProp: {
                label: 'name',
                children: 'children',
                isLeaf: 'isLeaf',
            },
            selectedNodes: [], // 树节点选中值,
            treeInstance: null,
            searchTreeInstance: null,
            showErrorMessge: false,
        };
    },
    created() {},
    mounted() {
        this.$nextTick(() => {
            this.treeInstance = this.$refs.positionTreeRef.$refs.tree;
            this.searchTreeInstance = this.$refs.positionTreeRef.$refs.searchTree;
        });
    },
    watch: {
        isValidate(val) {
            if (!val) {
                this.showErrorMessge = false;
            }
        },
        selectedNodes(val) {
            if (this.isValidate) {
                if (val.length <= 0 || val.length > 50) {
                    this.showErrorMessge = true;
                } else {
                    this.showErrorMessge = false;
                }
            }
            this.changeInputHeight(val.length);
        },
    },
    computed: {
        blankTips() {
            let tips = this.$t('placeholder.select');
            if (this.selectedNodes.length > 0) {
                tips = '';
            }
            return tips;
        },
        selectedNodesId: {
            get() {
                return this.selectedNodes.map(item => {
                    return {
                        label: item.name,
                        value: item.id,
                    };
                });
            },
            set() {},
        },
        errorMessage() {
            let tips = this.$t('tipMessage.pleaseSelect', {
                filedName: this.$t('monitor.batchExportMonitoring.position'),
            });
            if (this.selectedNodes.length > 50) {
                tips = this.$t('monitor.batchExportMonitoring.numberLTNum', { num: 50 });
            }
            return tips;
        },
        filterMocList() {
            let result = [];
            if (this.sceneType == 'DC') {
                result = [
                    'r32.uedm.dc',
                    '32.uedm.building',
                    'r32.uedm.floor',
                    'r32.uedm.room',
                    'r32.uedm.ashelters',
                    'r32.uedm.realgroup',
                    'r32.uedm.commmdc',
                    'r32.uedm.mdc',
                    'r32.uedm.bpodevicevirtual',
                    'r32.uedm.ctfavirtual',
                    'r32.uedm.psdvirtual',
                ];
            } else if (this.sceneType == 'Power') {
                result = ['r32.uedm.realgroup', 'r32.uedm.site'];
            }
            return result;
        },
    },
    methods: {
        handleShow() {
            // 下拉框点击弹出选项树
            this.opened = true;
        },
        handleHide() {
            // 下拉框隐藏不显示选项树
            this.opened = false;
        },
        handleSelected(checkedNodes) {
            this.selectedNodes = checkedNodes;
        },
        handleRemoveTag(val) {
            // 单个删除tag
            this.removeTag(val.value);
        },
        removeTag(id) {
            // 删除指定一个节点
            this.selectedNodes = this.selectedNodes.filter(node => {
                return node.id != id;
            });
            this.$refs.positionTreeRef.uncheckNode(id);
        },
        handleClearable() {
            // 整个下拉清除
            this.$refs.positionTreeRef.handleClear();
            this.selectedNodes = [];
            $emit(this, 'cleared');
        },
        inputMouseOver() {
            this.inputClearable = true;
        },
        inputMouseLeave() {
            this.inputClearable = false;
        },
        hideSelect() {
            if (this.visible) {
                this.visible = false;
            }
        },
        handleInit() {
            this.selectedNodes = [];
            this.$refs.positionTreeRef.clearHandler();
        },
        clearNodes() {
            this.handleClearable();
        },
        validateChange() {
            if (this.isValidate) {
                let length = this.selectedNodes.length;
                if (0 < length && length < 51) {
                    this.showErrorMessge = false;
                } else {
                    this.showErrorMessge = true;
                }
            }
            return this.showErrorMessge;
        },
        changeInputHeight(length, className = '.custom-position-select') {
            this.$nextTick(() => {
                let inputHeight = 32;
                if (length > 1) {
                    let tagElem = document.querySelector(`${className} .el-select__tags`);
                    let height = tagElem && tagElem.clientHeight;
                    inputHeight = height > 32 ? 58 : 32;
                }
                let inputElem = document.querySelector(`${className} .el-input input`);
                if (inputElem && inputElem.style.height !== inputHeight + 'px') {
                    inputElem.style.height = inputHeight + 'px';
                }
            });
        },
    },
    emits: ['cleared'],
};
</script>

<style lang="scss" scoped>
.treeWrap {
    overflow: auto;
    min-height: 100px;
}
.treeWrap :deep(.el-tree) > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.treeWrap :deep(.el-tree-node__content) > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
.el-select .el-input .error-border,
.el-select .el-input .error-border:focus {
    border-color: #f56c6c;
}
.custom-position-select :deep(.el-tag--info) .el-select__tags-text {
    display: inline-block;
    max-width: 232px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
}
</style>
