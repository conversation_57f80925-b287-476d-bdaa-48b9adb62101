import { Loader } from '@uedm/common/util/googleMapsLoader.js';
import MAP_STYLING_JSON from './MapStyling.json';
export class GoogleMap {
    _apiKey = '';
    _language = '';
    _googleOptions = {}; // google loader的配置内容
    _mapOptions = {}; // 地图本身的配置项内容

    _google = null; // google对象
    _gmap = null; // : google.maps.Map
    _mapContainer = null; // googlemap的容器对象，原生dom对象
    _markers = []; // : Array[google.maps.Marker] 存储当前所有的marker
    _lastMarker = null; // : google.maps.Marker 存储上一次鼠标点击的marker，用于marker的颜色恢复

    constructor({
        apiKey = '',
        container = null,
        language = 'en',
        center = { lat: 20, lng: 10 },
        zoom = 3,
        googleOptions = {},
    }) {
        this._apiKey = apiKey;
        this._language = language;
        this._mapContainer = container;

        /**
         * _mapOptions模板：
         * {
         *      center: object, // { lat: number, lng: number }
         *      zoom: number,
         * }
         */
        this._mapOptions = {
            // 使用goolemap所提供的默认位置
            center,
            zoom,

            language,
            scaleControl: true,

            // 不用ctrl+scroll进行缩放
            options: {
                gestureHandling: 'greedy',
            },
        };
        // 强制默认配置
        Object.assign(this._mapOptions, {
            maxZoom: 19,
            minZoom: 3,
            disableDefaultUI: true, // 取消googlemap默认UI
            keyboardShortcuts: false, // 取消googlemap默认快捷键

            // 隐去不必要的marker
            styles: [
                {
                    featureType: 'administrative',
                    elementType: 'geometry',
                    stylers: [{ visibility: 'off' }],
                },
                {
                    featureType: 'poi',
                    stylers: [{ visibility: 'off' }],
                },
                {
                    featureType: 'road',
                    elementType: 'labels.icon',
                    stylers: [{ visibility: 'off' }],
                },
                {
                    featureType: 'transit',
                    stylers: [{ visibility: 'off' }],
                },
            ],
        });
        this._googleOptions = googleOptions;
    }

    // 初始化google对象，必须在使用实例前调用
    async initGoogle(cb, eventCb) {
        let google = null;

        let apiKey = this._apiKey;
        let googleOptions = this._googleOptions;
        googleOptions['language'] = this._language;

        if (!apiKey) {
            throw new Error('Needs an api key for google map');
        }
        if (window.google) {
            google = window.google;
        } else {
            let loader = new Loader(apiKey, googleOptions);
            // googlemap的loader为异步生成
            google = await loader.load();
        }

        this._google = google;

        // 初始化函数
        this.init(google, cb, eventCb);
    }

    // 必须在google对象初始化后调用
    init(google, cb, eventCb) {
        let mapContainer = this._mapContainer;
        let mapOptions = this._mapOptions;

        this._gmap = new google.maps.Map(mapContainer, mapOptions);
        this._gmap.setOptions({ styles: MAP_STYLING_JSON });
        cb && cb();

        // 监听地图的事件
        google.maps.event.addListener(this._gmap, 'click', () => {
            eventCb && eventCb('click');
        });
        google.maps.event.addListener(this._gmap, 'zoom_changed', () => {
            eventCb && eventCb('zoom_changed');
        });
        google.maps.event.addListener(this._gmap, 'drag', () => {
            eventCb && eventCb('drag');
        });
    }
}
