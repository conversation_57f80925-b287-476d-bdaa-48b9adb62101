// 获取地图选项
import { queryMapOptions } from '@/components/Map/utils/mapOptions.js';

// 用于统计当前所包含的所有瓦片路径规则类型
const tileUrlTypesArr = ['Shui<PERSON><PERSON>zhu', 'TMS'];

class Offline {
    constructor(google, mapType, options) {
        let opts = options || {};
        this.tileSize = opts.size || new google.maps.Size(256, 256); // 瓦片大小
        this.minZoom = opts.min || 1; // 最小级别
        this.maxZoom = opts.max || 19; // 最大级别
        this.name = 'comit-map'; // 显示在地图类型控件上的名字

        this.mapType = mapType || 'ShuiJing';
    }

    /**
     *
     * @param {google.maps.Ponit} coord
     * @param {Number} zoom
     * @param {Document} ownerDocument
     */
    getTile(coord, zoom, ownerDocument) {
        let div = ownerDocument.createElement('div');
        div.style.width = this.tileSize.width + 'px';
        div.style.height = this.tileSize.height + 'px';

        div.style.backgroundImage = 'url("' + this.getTileUrl(coord, zoom) + '")';
        _setUnselected(div);
        return div;
    }

    getTileUrl(coord, zoom) {
        let translatedY = coord.y; // 瓦片坐标y
        let translatedX = coord.x; // 瓦片坐标x
        let translatedZoom = zoom; // 瓦片坐标z(zoom值)

        // 构建瓦片服务器url请求地址
        return generateCoordUrl(this.mapType, translatedY, translatedX, translatedZoom);
    }
}

function generateCoordUrl(type, y, x, z) {
    // 瓦片发布url路径的root地址
    // TODO:针对uedm的url路径校验规则
    const BASE_URL = './map';
    // const BASE_URL = "http://localhost:4040";

    // 不同地区可能有不同的地图url路径搜索规则
    let tileUrlTypes = {
        // 构造结果举例：http://localhost:4040/L02/000002-000001.png
        ShuiJingzhu: `${BASE_URL}/L${digitInteger(z + 1, 2)}/${digitInteger(y, 6)}-${digitInteger(x, 6)}.png`,

        // 构造结构举例：http://localhost:4040/2/2/1.png
        TMS: `${BASE_URL}/${z}/${x}/${y}.png`,
    };

    // 检查当前的瓦片路径类型是否都已实例化
    tileUrlTypesArr.forEach(d => {
        if (typeof tileUrlTypes[d] === 'undefined') {
            throw new Error(d + ' tile url type is not defined');
        }
    });

    if (tileUrlTypes[type]) {
        return tileUrlTypes[type];
    }
}

// 在数字之前添加ditigalSize个数的数字零
function digitInteger(num, digitalSize = 2) {
    let numStr = num + '';
    while (numStr.length < digitalSize) {
        numStr = '0' + numStr;
    }
    return numStr;
}

// 设置瓦片不可选
function _setUnselected(a) {
    if (a.style && a.style.MozUserSelect) {
        a.style.MozUserSelect = 'none';
    } else if (a.style && a.style.WebkitUserSelect) {
        a.style.WebkitUserSelect = 'none';
    } else if (a.unselectable) {
        a.unselectable = 'on';
        a.onselectstart = function () {
            return false;
        };
    }
}

// 判断是否执行离线地图相关代码
function getMapStatus() {
    return new Promise(resolve => {
        // 只需获取"地图状态", "瓦片路径类型"两个配置量
        queryMapOptions(['status', 'tileType']).then(options => {
            // 根据接口判断当前是否使用离线地图
            let mapStatus = options.status; // 在线地图值：“1”, 离线地图值：“2”
            let tileType = options.tileType; // 水经注:"ShuiJingzhu", TMS: "TMS"

            // 地图状态为离线地图,并且地图瓦片类型合法
            if (mapStatus === '2' && tileUrlTypesArr.indexOf(tileType) + 1) {
                // 如果需要执行离线地图, 则返回瓦片类型内容
                resolve(tileType);
            } else {
                // 否则,返回false
                resolve(false);
            }
        });
    });
}

/** ********************************************* 对外暴露的函数 ************************************************** */
/** begin **/

/**
 * 直接插入maptype构建离线地图,其余部分地图瓦片直接不加载
 * @param {google实例} google
 * @param {google.maps} map
 */
export function initializeMapType(google, map) {
    getMapStatus().then(tileType => {
        if (tileType) {
            map.setMapTypeId('myMap');
            let myMap = new Offline(google, tileType);
            map.mapTypes.set('myMap', myMap);

            // 测试代码，测试坐标是否精准 8.960372, 38.71153
            // var marker = new google.maps.Marker({
            //     position: new google.maps.LatLng(8.960372, 38.71153),
            //     map: map
            // });
        } else {
            // 不执行代码
            return;
        }
    });
}

/**
 * 使用overlayMapTypes,在其余部分地图瓦片仍使用在线地图的前提下,使用离线地图瓦片
 * @param {google实例} google
 * @param {google.maps} map
 */
export function initializeOverlay(google, map) {
    getMapStatus().then(tileType => {
        if (tileType) {
            map.overlayMapTypes.insertAt(0, new Offline(google, tileType, { size: new google.maps.Size(256, 256) }));
        } else {
            // 不执行代码
            return;
        }
    });
}

/** end **/
/** ********************************************* 对外暴露的函数 ************************************************** */
