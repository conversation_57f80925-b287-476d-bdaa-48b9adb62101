<template>
    <el-popover
        placement="bottom"
        :width="popWidth - 24"
        :teleported="false"
        trigger="click"
        @show="showPop = true"
        @hide="showPop = false"
    >
        <div>
            <el-tree
                ref="tree"
                class="tree"
                node-key="id"
                :load="loadNode"
                :lazy="true"
                :props="defaultProps"
                show-checkbox
                @check-change="handleCheckChange"
            ></el-tree>
        </div>

        <template v-slot:reference>
            <div id="areaInput" class="el-input el-input--suffix">
                <div class="el-input__wrapper">
                    <input
                        type="text"
                        readonly="readonly"
                        autocomplete="off"
                        :placeholder="$t('placeholder.select')"
                        class="el-input__inner"
                        :style="{ width: popWidth + 'px' }"
                    />
                    <div class="el-select__tags tags">
                        <span v-if="selectedNode.length > 0" class="el-tag el-tag--info">
                            <span>{{ selectedNode[0].displayname }}</span>
                            <el-icon class="el-tag__close" @click.stop="clearNode(selectedNode[0])">
                                <el-icon-close />
                            </el-icon>
                        </span>
                        <span v-if="selectedNode.length > 1" class="el-tag el-tag--info">
                            {{ '+' + (selectedNode.length - 1) }}
                        </span>
                    </div>

                    <span class="el-input__suffix">
                        <el-icon v-if="showPop" class="el-input__icon"><el-icon-arrow-up /></el-icon>
                        <el-icon v-else class="el-input__icon"><el-icon-arrow-down /></el-icon>
                    </span>
                </div>
            </div>
        </template>
    </el-popover>
</template>

<script>
import { Close as ElIconClose, ArrowUp as ElIconArrowUp, ArrowDown as ElIconArrowDown } from '@element-plus/icons-vue';
import { $emit } from '../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';

export default {
    components: {
        ElIconClose,
        ElIconArrowUp,
        ElIconArrowDown,
    },
    props: ['popoverWidth', 'nodeTypes', 'onlyDg'],
    data() {
        return {
            // tree
            defaultProps: {
                label: 'name',
            },
            checkedNodes: [],
            showPop: false,
            selectedNode: [],
        };
    },
    created() {
        this.getTree();
    },
    computed: {
        popWidth() {
            return this.popoverWidth ? this.popoverWidth : 220;
        },
    },
    methods: {
        getTree(id, callback) {
            let url = this.onlyDg ? 'getChildWithDg' : 'getChild';
            HTTP.request(url, {
                urlParam: {
                    id: id,
                },
                complete: data => {
                    if (data.code == 0 && typeof callback === 'function') {
                        callback(data.data);
                    }
                },
            });
        },
        loadNode(node, resolve) {
            let id = '';
            if (node.data) {
                id = node.data.id;
            }
            let nodeTypes = this.nodeTypes || [];
            this.getTree(id, data => {
                let treeData = data.filter(item => nodeTypes.indexOf(item.resourceType) > -1); // RealGroup, Site, CoreSite
                resolve(treeData);
            });
        },
        handleCheckChange() {
            let checkedNodes = this.$refs.tree.getCheckedNodes();
            // console.log('-- checkedNodes ----', checkedNodes);
            this.getTreeCheckNode(checkedNodes);
        },

        getTreeCheckNode(data) {
            this.selectedNode = [];
            data.forEach(d => {
                if (!this.isContained(d) && !this.isParentContain(d)) {
                    this.selectedNode.push(d);
                }
            });
            $emit(this, 'treeNodeChecked', this.selectedNode);
        },
        isContained(node) {
            let flag = false;
            this.selectedNode.forEach(n => {
                if (n.id == node.id) {
                    flag = true;
                }
            });
            return flag;
        },
        isParentContain(node) {
            let flag = false;
            this.selectedNode.forEach(n => {
                if (n.id == node.parentId) {
                    flag = true;
                }
            });
            return flag;
        },
        clearNode(item) {
            this.selectedNode = this.selectedNode.filter(d => {
                return d.id != item.id;
            });
            this.$refs.tree.setCheckedNodes(this.selectedNode);
        },
        clearAll() {
            this.$refs.tree.setCheckedNodes([]);
        },
    },
    emits: ['treeNodeChecked'],
};
</script>

<style lang="scss" scoped>
.tags {
    position: absolute;
    left: 4px;
}
::v-deep(#areaInput) {
    height: 32px;
    overflow-y: auto;
    border-radius: 5px;
    // border: 1px solid #dcdfe6;
    // padding-left: 6px;
    box-sizing: border-box;
    font-size: 12px;
    .el-input__inner {
        width: 279px;
        height: 32px;
        // border: none;
        outline: none;
        position: absolute;
    }
    .el-input__icon {
        font-size: 14px;
        line-height: 32px;
    }
    .el-tag {
        margin: 4px;
        height: 24px;
        line-height: 24px;
        .el-icon-close {
            background-color: #c0c4cc;
            transform: scale(0.8);
            color: #fff;
            border-radius: 50%;
            text-align: center;
            position: relative;
            cursor: pointer;
            font-size: 12px;
            height: 16px;
            width: 16px;
            line-height: 16px;
            vertical-align: middle;
            top: -1px;
            right: -5px;
            &:hover {
                background-color: #909399;
            }
        }
    }
}
</style>
