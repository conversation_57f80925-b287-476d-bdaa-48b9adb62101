@font-face {
    font-family: 'iconfont'; /* Project id 1919746 */
    src: url('iconfont.woff2?t=1649597353600') format('woff2'), url('iconfont.woff?t=1649597353600') format('woff'),
        url('iconfont.ttf?t=1649597353600') format('truetype');
}

.iconfont {
    font-family: 'iconfont' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icona-uparrow:before {
    content: '\e62c';
}

.icona-downarrow:before {
    content: '\e62d';
}

.iconoverdue:before {
    content: '\e62e';
}

.icontab-base-monitor:before {
    content: '\e62a';
}

.icontab-view-monitor:before {
    content: '\e62b';
}

.iconnormal:before {
    content: '\e627';
}

.iconbreak:before {
    content: '\e628';
}

.iconmd-tree-root:before {
    content: '\e629';
}

.iconmo-tree-building:before {
    content: '\e61e';
}

.iconmo-tree-device:before {
    content: '\e61f';
}

.iconmo-tree-mdc:before {
    content: '\e620';
}

.iconmo-tree-realgroup:before {
    content: '\e621';
}

.iconmo-tree-room:before {
    content: '\e622';
}

.iconmo-tree-site:before {
    content: '\e623';
}

.iconmo-tree-monitorobject:before {
    content: '\e624';
}

.iconmo-tree-floor:before {
    content: '\e625';
}

.iconmo-tree-datacenter:before {
    content: '\e626';
}
