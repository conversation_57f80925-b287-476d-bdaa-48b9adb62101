<template>
    <div class="battery-global-map">
        <span class="filter-img filterIconBtn" @click="filterFormShowFn"></span>
        <el-container
            v-loading.fullscreen.lock="loadingMap"
            class="main-map-container"
            :element-loading-text="$t('tipMessage.loadingMsg')"
        >
            <component
                :is="currMapType"
                :marker-data="markerData"
                :collapse-batt-coord="collapseBattCoord"
                :show-collapse="showCollapse"
                @changeClickedMarkerData="changeClickedMarkerData"
            ></component>
        </el-container>
        <!-- 按钮：进入电池列表统计页面 -->
        <div class="list-statistics elem-container list-statistics-icon" @click="changeToForm">
            <el-icon><List /></el-icon>
            {{ $t('batteryMonitor.btnLabels.tableStatistics') }}
        </div>
        <!-- dark-filter 深色模式下适应浅色地图 -->
        <div
            v-if="filterFormShow"
            :class="{ 'dark-filter': $store.getters.getIsDark }"
            class="battery-filter-condition elem-container"
        >
            <battery-statistic-filter @filterData="filterData"></battery-statistic-filter>
        </div>
        <div v-if="showCollapse" class="collapse-container">
            <div class="collapse-container-close-btn" @click="closeCollapse">
                <span class="icon-button-close plx-ico-close-16"></span>
            </div>
            <battery-info-collapse
                :batt-collapse-data="battCollapseData"
                :collapse-batt-loc="collapseBattLoc"
                :battery-trail-type="batteryTrailType"
            ></battery-info-collapse>
        </div>
        <location-info></location-info>
    </div>
</template>

<script>
import BatteryStatisticFilter from '../BatteryForm/BatteryStatisticFilter';
import BatteryInfoCollapse from './BatteryInfoCollapse';
import zgisMap from './zgisMap/index';
import LocationInfo from './LocationInfo';
import HTTP from '@/util/httpService.js';
import { coorTransform } from '../utils.js';
import { apiMidBattTrackSearch } from '../ApiMiddleWare.js'; // 接口数据获取后处理的中间件，用于处理接口字段，结构变化的紧急情况
import { queryMapOptions } from '@/components/Map/utils/mapOptions.js';
import { mapMutations } from 'vuex';
import { queryConfigByKey, BATTERY_TRAIL_TRACKING_KEY, MAPTYPE_ZGIS_STRING } from '../zgis/js/zgisUtils';
import { handelErrMessage } from '@/util/common.js';

export default {
    name: 'BatteryGlobalMap',
    components: {
        'battery-statistic-filter': BatteryStatisticFilter,
        'battery-info-collapse': BatteryInfoCollapse,
        'location-info': LocationInfo,
    },
    props: {
        batteryTrailType: {
            type: String,
            default: 'default',
        },
    },
    data() {
        return {
            filterFormShow: true,
            markerData: [],
            oriMarkerData: [],
            idList: [],
            battCollapseData: [], // 左侧折叠列表对应的数据
            showCollapse: false, // 是否显示左上折叠框
            collapseBattCoord: '',
            collapseBattLoc: '',
            loadingMap: true,
            currMapType: null,
            currMapTypeStr: '',
            interval: null
        };
    },

    async mounted() {
        // 首次进入页面进行默认筛选一次。通过使用异步，达到等待地图初始化结束的目的
        setTimeout(() => this.filterData(), 2000);
        this.initMapComponent(MAPTYPE_ZGIS_STRING);
    },
    beforeUnmount() {
        this.clearInterval();
    },
    methods: {
        initMapComponent(type) {
            this.currMapType = zgisMap;
            this.currMapTypeStr = type;
            this.setStoreMapType(type);
        },
        ...mapMutations({
            setStoreMapType: 'battMonitor/setMapType',
        }),
        filterFormShowFn() {
            this.filterFormShow = !this.filterFormShow;
        },

        // 关闭collapse页面
        closeCollapse() {
            this.showCollapse = false;
        },

        // 跳转到电池列表页面
        changeToForm() {
            this.$emit('changePage', 'BatteryForm');
        },

        // collapse数据的获取
        /* Started by AICoder, pid:e7a24z1831i377a14c8608f2c05a2510489129cf */
        changeClickedMarkerData(data, flag) {
            if (flag) {
                this.showCollapse = false;
                this.$nextTick(() => {
                    this.battCollapseData = data;
                    this.showCollapse = true;
                });
            }
            this.battCollapseData = data;
            this.showCollapse = true;
        },
        /* Ended by AICoder, pid:e7a24z1831i377a14c8608f2c05a2510489129cf */

        // 监听数据筛选交互
        filterData(filterOptions) {
            this.showCollapse = false;

            let filterOptionsInGlobalMap = filterOptions || {
                realGroupIds: [], // 区域
                siteName: '', // 站点名称
                motionState: '', // 运动状态
                fortificationState: '', // 设防状态
                communicationStatus: '', // 通讯状态
                siteLevel: [], // 站点等级
            };

            this.queryData(filterOptionsInGlobalMap);
        },
        clearInterval() {
            this.interval && clearInterval(this.interval);
            this.interval = null;
        },
        // 请求数据
        queryData(filterOptions) {
            this.loadingMap = true;
            // 清空collapse数据,让collpase组件消失
            this.battCollapseData = [];
            let _this = this;
            this.clearInterval();
            HTTP.request('batterySearch', {
                method: 'post',
                // urlParam: fitlerOptions,
                data: filterOptions,
                complete: data => {
                    this.loadingMap = false;
                    if (_this.currMapTypeStr === MAPTYPE_ZGIS_STRING) {
                        let sourceData = apiMidBattTrackSearch(data.data);
                        _this.markerData = sourceData.batteryList;
                        this.oriMarkerData = sourceData.batteryList;
                        if (_this.markerData.length) {
                            this.idList = _this.markerData.map(item => item.moId);
                        }
                        this.getRealtionTrack();
                        this.interval = setInterval(() => {
                            this.getRealtionTrack(true);
                        }, 20000);

                    } else {
                        processData(data.data);
                    }
                    if (data.data.totalNumbers == 0) {
                        // 提示信息与图标消失同步展示
                        setTimeout(() => {
                            this.$message({
                                message: this.$t('batteryMonitor.message.emptyData'),
                                type: 'warning',
                            });
                        }, 2000);
                    }
                },
                error: data => {
                    this.loadingMap = false;
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });

            function processData(data) {
                let sourceData = apiMidBattTrackSearch(data);
                let markerData = sourceData.batteryList;

                // 火星坐标偏移修正
                markerData.forEach(d => {
                    d.originalLatitude = d.latitude;
                    d.originalLongitude = d.longitude;

                    if (d.latitude && d.longitude) {
                        let { lat, lng } = coorTransform(
                            {
                                lat: +d.latitude,
                                lng: +d.longitude,
                            },
                            _this.currMapTypeStr
                        );
                        d.latitude = lat;
                        d.longitude = lng;
                    }
                });

                _this.markerData = markerData;
            }
        },
        /* Started by AICoder, pid:vbe2cg01afr35f014ed80bc8f0fd6432bbd80c6e */
        // 电池实时轨迹查询
        getRealtionTrack(isInterval) {
            this.battCollapseData = [];
            HTTP.request('batteryLocationInfo', {
                method: 'post',
                data: {
                    ids: this.idList,
                },
                complete: data => {
                    if (data.code === 0) {
                        const batteryData = data.data || [];
                        if (this.oriMarkerData.length && batteryData.length) {
                            this.oriMarkerData.forEach(marker => {
                                batteryData.forEach(data => {
                                    if( marker.moId === data.batteryId ) {
                                        marker.latitude = +data.latitude;
                                        marker.longitude = +data.longitude;
                                        marker.isInterval = isInterval;
                                        marker.motionState = data.status;
                                        marker.batteryName = data.batteryName;
                                    }
                                });
                            });
                            try {
                                this.markerData = JSON.parse(JSON.stringify(this.oriMarkerData));
                            } catch (error) {
                                // ignore
                            }
                        }
                    } else {
                        let msg = handelErrMessage(data.data); // 统一错误码处理
                        this.$message({
                            message: msg || this.$t('tipMessage.requestError'),
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        /* Ended by AICoder, pid:vbe2cg01afr35f014ed80bc8f0fd6432bbd80c6e */
    },
};
</script>

<style lang="scss" scoped>
.battery-global-map :deep() .locationTime-formItem .el-date-editor:nth-child(1) {
    margin-bottom: 8px;
}
.elem-container {
    margin: 10px;
    box-shadow:
        2px 2px 2px rgb(173, 173, 173),
        inset 1px 1px 1px gainsboro;
    border-radius: 5px;
    background-color: white;
}

.main-map-container {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 0;
}
.filter-img {
    position: absolute;
    z-index: 1999;
    width: 30px;
    padding: 0;
    background-color: white;
    left: 10px;
    top: 5px;
    cursor: pointer;
}
.battery-filter-condition {
    position: relative;
    z-index: 999;
    top: 30px;
    width: 362px;
    .filter-container {
        background-color: rgb(243, 243, 243);
    }
    :deep() .el-form-item--small .el-form-item {
        margin-right: 0;
    }
    :deep() .el-form-item--mini.el-form-item {
        margin-right: 0;
    }
    :deep() .el-form-item__label {
        width: 127px !important;
        text-align: right;
    }
    :deep() .condition input.area {
        background: #fff;
        width: 200px;
    }
    :deep() .el-form {
        padding: 16px 0;
    }
    :deep() .el-form--inline .el-button--primary {
        margin-left: 126px !important;
    }
    :deep() .date-picker-bar {
        display: none;
    }
    :deep() .locationTime-formItem .el-form-item__content {
        width: 200px;
    }
}
.dark-filter {
    :deep() #areaInput {
        border: unset;
    }
    :deep() .condition input.area {
        background: transparent;
        border: 1px solid #474a59 !important;
    }
    :deep() .el-input__inner,
    body.theme-dark .el-textarea__inner {
        border-color: #474a59;
    }
    :deep() .el-button--default {
        border-color: #474a59;
    }
    .filter-container {
        background: #191919;
    }
}

/* “列表统计”按钮的鼠标悬浮样式 */
.list-statistics:hover {
    background-color: rgb(245, 245, 245);
    cursor: pointer;
}

/* “列表统计”按钮的点击样式 */
.list-statistics:active {
    cursor: pointer;
    background-color: gainsboro;
    box-shadow:
        2px 2px 2px rgb(173, 173, 173),
        inset 2px 2px 2px gainsboro;
}

/* “列表统计”按钮的基础样式 */
.list-statistics {
    position: relative;
    z-index: 999;
    background-color: white;
    float: right;
    width: 80px;
    height: 32px;
    line-height: 35px;
    color: #409eff;

    /* 按钮内容水平居中 */
    text-align: center;
}
.list-statistics-icon {
    font-size: 12px;
    font-style: normal;

    .el-icon {
        vertical-align: text-top;
    }
}

.collapse-container-close-btn {
    position: absolute;
    top: 0;
    right: -46px;
    cursor: pointer;
    box-shadow: unset;
    background: transparent;
    width: 30px;
    height: 30px;
    font-size: 16px;
    text-align: center;
    line-height: 1.8;
    z-index: 1000;
    &:hover {
        color: #1993ff;
    }
}

.collapse-container {
    width: 350px;
    position: relative;
    z-index: 999;
    margin-top: 39px;
}
html.dark {
    .filter-img {
        background-color: #141414;
    }
    .collapse-container .collapse-container {
        background-color: #1d1d1d;
    }
    .collapse-container-close-btn {
        color: #a4a7b3;
        &:hover {
            color: #1993ff;
        }
    }
    .list-statistics {
        color: #c0c4cc;
        box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
        background-color: #141414;;

        &:hover {
            background-color: #191919;
            cursor: pointer;
            color: #1993ff;
        }
        &:active {
            background-color: #191919;
            cursor: pointer;
            color: #1993ff;
        }
    }
    .elem-container {
        background-color: #141414;
        box-shadow: none;
    }
}
</style>
