<template>
    <div id="zgisMapContainer" class="gis-map"></div>
</template>

<script>
import { Loader } from '../../zgis/js/zgisMapLoader';
import {
    handleZgisPosition,
    BLUE_ICON_URL,
    GREEN_ICON_URL,
    CLUSTER_ICON_URL,
    EXTENT_LIMIT,
    OSMOFFLINE_EXTENT_LIMIT,
} from '../../zgis/js/zgisUtils';
import { createBlinkCircle } from '../../zgis/js/zgisBlinkCircle';
import { mapActions } from 'vuex';

export default {
    name: 'ZgisMap',
    props: {
        markerData: {
            type: Array,
            default: () => [],
        },
        showCollapse: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        this.animations = []; // 存储运动的点
        return {
            maxZoom: 15, // 离线地图默认最高下载15级
            lastClickedFeatureIds: [],
        };
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
    },
    watch: {
        markerData: {
            handler() {
                let filteredData = this.markerData.filter(
                    item => !isNaN(parseFloat(item.latitude)) && !isNaN(parseFloat(item.longitude))
                );
                this.processData = filteredData;
                /* Started by AICoder, pid:c2fcei63c3681ab14bae0b42a005f40fc16697bd */
                let isInterval = this.processData.length && this.processData[0].isInterval;
                if (isInterval) {
                    this.updateMarker(false);
                } else {
                    this.updateMarker();
                }
                /* Ended by AICoder, pid:c2fcei63c3681ab14bae0b42a005f40fc16697bd */
            },
        },
    },
    mounted() {
        this.mapContainer = document.getElementById('zgisMapContainer');
        this.processData = {};
        this.blinckCircles = [];
        this.zMapApp = null;
        this.zMapAppMarkers = [];
        this.popup = null;
        this.lastMarker = null;
        this.loadZmap();
        window._zMap_close_popup = () => {
            this.removePopup();
        };
    },
    methods: {
        async loadZmap() {
            try {
                if (!window.zmaps) {
                    let loader = new Loader();
                    await loader.load();
                }
            } catch (error) {
                console.log('loadZmap error', error);
            }
            if (window.zmaps) {
                this.$nextTick(() => {
                    this.initMap();
                });
            }
        },

        initMap() {
            let userName = 'uedm';
            let pageName = 'uedmMap';
            let hostIP = '';
            let mapOptions = {
                element: this.mapContainer,
                zoomLevel: 3, // 默认为3
                mapTheme: this.isDark ? 'blue' : '', // 支持:darkgreen,blue,reversal,black,gray. ''表示地图原始样式
                center: [0, 0], // 默认[0, 0]
                minZoomLevel: 1, // 最小级别, 取值3~17
                numZoomLevels: 20, // 最大级别, 取值3~17;
                hideLegend: true, // 是否隐藏图例面板，默认值false
                hideTools: true, // 是否隐藏工具栏，默认值false
                hideZoomBar: true, // 是否否隐藏缩放条，默认值false
                hidesatelliteMap: true, // 是否隐藏卫星视图按钮，默认值false
                callback: application => {
                    this.zMapApp = application;
                    this.limitExtent();
                    window.zMapApp = application;
                    this.zgisOfflineMapWarning();
                    this.updateMarker();
                    this.updateMaxZoom();
                    this.bindMarkerEvents();
                    this.bindMapEvents();
                },
            };
            if (window.zmaps) {
                // eslint-disable-next-line new-cap
                window.zmaps.InitMap(userName, pageName, hostIP, mapOptions);
            }
        },
        updateMaxZoom() {
            if (this.zMapApp) {
                this.maxZoom = this.zMapApp.zmap.map.getView().getMaxZoom();
            }
        },
        limitExtent() {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let extent = mapEngine === 'OSMOffLineMap' ? OSMOFFLINE_EXTENT_LIMIT : EXTENT_LIMIT;
            this.zMapApp.zmap.setExtentLimit(extent);
        },
        bindMarkerEvents() {
            if (this.zMapApp) {
                this.zMapApp.EXClustersAPI.bindEvents({
                    click: feature => {
                        this.curClickedFeature = feature;
                        let curZoom = this.zMapApp.zmap.map.getView().getZoom();
                        // 达到最大级别展不开的聚合点，显示列表项
                        let childFeatures = feature.get('features'); // 聚合点数据
                        if ((childFeatures && curZoom >= this.maxZoom) || feature.attributes) {
                            this.showMarkerPopup(feature);
                            this.showCollapsedInfo(feature);
                            this.updateLastMarker(feature);
                        }
                    },
                    clickout: () => {
                        this.removePopup();
                    },
                });
            }
        },
        // 保存上一次点击的ID
        /* Started by AICoder, pid:o4d5af7901l5d6b143da0806f0ca3709dd13bb6a */
        handleLastFeatureData(param) {
            this.lastClickedFeatureIds = param.map(item => item.moId);
        },
        /* Ended by AICoder, pid:o4d5af7901l5d6b143da0806f0ca3709dd13bb6a */
        showCollapsedInfo(feature) {
            // 先展示折叠框
            const markerData = feature.get('features');
            let param = [];
            if (markerData) {
                param = markerData.flatMap(item => item.attributes.data);
            } else {
                param = feature.attributes.data;
            }
            if (!param.length) {
                return;
            }
            this.handleLastFeatureData(param);
            // 更新左侧电池信息
            param.forEach(item => {
                item.location = this.$t('common.loading');
            });
            this.setMarkerInfoData(param);
            // 显示左侧电池信息
            this.$emit('changeClickedMarkerData', param, true);
            // 再查询地址并更新
            const { lng, lat } = param[0];
            this.queryCollapsedPosition([lng, lat].join(' '), param);
        },
        /* Started by AICoder, pid:l6e6c4b3d4696371407e090e90dd3405eed8d656 */
        upDateCollapsedInfo(param) {
            // 更新左侧电池信息
            param.forEach(item => {
                item.location = this.$t('common.loading');
            });
            this.setMarkerInfoData(param);
            // 显示左侧电池信息
            this.$emit('changeClickedMarkerData', param, false);
            const { lng, lat } = param[0];
            this.queryCollapsedPosition([lng, lat].join(' '), param);
        },
        /* Ended by AICoder, pid:l6e6c4b3d4696371407e090e90dd3405eed8d656 */
        queryCollapsedPosition(lonlat, param) {
            if (window.zmaps && window.zmaps.SearchPoint && lonlat) {
                // eslint-disable-next-line new-cap
                window.zmaps.SearchPoint(lonlat, res => {
                    const { currRegion } = handleZgisPosition(res.data);
                    param.forEach(item => {
                        item.location = currRegion || '--';
                    });
                    let newParam = JSON.parse(JSON.stringify(param));
                    // 更新左侧电池信息
                    this.setMarkerInfoData(newParam);
                    // 显示左侧电池信息
                    this.$emit('changeClickedMarkerData', newParam, false);
                });
            }
        },
        bindMapEvents() {
            if (this.zMapApp) {
                // zgis事件监听（作为api保留注释）
                // this.zMapApp.zmap.map.getView().on('change:resolution', (e) => {
                //     this.removePopup();
                // });
                this.zMapApp.zmap.bindClickEvent(lonlat => {
                    // console.log('map:click', lonlat);
                    let lonlatString = Array.isArray(lonlat) ? lonlat.join(' ') : '';
                    setTimeout(() => {
                        // 处理osm-多次调用无返回问题（临时方案）
                        this.queryMapClickedPosition(lonlatString);
                    }, 2000);
                });
                // BlinkCircle
                // 注意this.zMapApp.zmap.map.getView().on('change:resolution'...)会与bindMoveEvent冲突
                this.zMapApp.zmap.bindMoveEvent(() => {
                    this.handleBlinkCircle();
                    this.removePopup();
                });
            }
        },
        handleBlinkCircle() {
            if (this.animations.length) {
                this.zMapApp.zmap.removeAnimations(this.animations);
                this.animations = [];
            }

            const features = this.zMapApp.EXClustersAPI.getClusterFeatures();
            for (let i = 0; i < features.length; i++) {
                const feature = features[i];
                const featureFeatures = feature.get('features');
                if (featureFeatures) {
                    for (let j = 0; j < featureFeatures.length; j++) {
                        const featureItem = featureFeatures[j];
                        if (featureItem?.attributes?.motionState === 1) {
                            // 1表示运动
                            const type = featureFeatures.length > 1 ? 'clusterMarker' : 'marker';
                            const circle = createBlinkCircle(type);
                            this.animations.push(this.zMapApp.zmap.featureAnimate(feature, circle));
                            break; // 找到一个就跳出循环，避免不必要的迭代
                        }
                    }
                }
            }
        },
        queryMapClickedPosition(lonlat) {
            if (window.zmaps && window.zmaps.SearchPoint && lonlat) {
                // eslint-disable-next-line new-cap
                window.zmaps.SearchPoint(lonlat, res => {
                    // console.log('queryPosition res', res);
                    // 按原来逻辑，在store中处理data
                    this.changeLocInfoData(res.data);
                });
            }
        },
        removeMarker() {
            if (this.zMapApp) {
                this.zMapApp.EXClustersAPI.clear();
            }
            // Started by AICoder, pid:ebb7b1c42bbe48418b1a194d9b5494e5
            if (this.animations.length) {
                this.zMapApp.zmap.removeAnimations(this.animations);
                this.animations = [];
            }
            // Ended by AICoder, pid:ebb7b1c42bbe48418b1a194d9b5494e5
        },
        /* Started by AICoder, pid:8db20kd008yf336143850a045007940db5986f08 */
        updataCollapsed(markerParams) {
            if (!this.showCollapse || !this.lastClickedFeatureIds.length) {
                return;
            }

            const markers = markerParams.filter(item => this.lastClickedFeatureIds.includes(item.attributes.moId));
            const collapInfo = markers.map(item => item.attributes.data[0]);

            this.upDateCollapsedInfo(collapInfo);
        },
        /* Ended by AICoder, pid:8db20kd008yf336143850a045007940db5986f08 */
        updateMarker(centerAndZoom = true) {
            this.removePopup();
            this.removeMarker();
            if (!this.zMapApp) {
                return;
            }
            let markerOriginData = this.processData;
            const DEFAULT_MARKER_ICON_OPTIONS = {
                markerIcon: {
                    url: GREEN_ICON_URL,
                },
                clustersIcon: {
                    url: CLUSTER_ICON_URL,
                },
            };
            if (markerOriginData.length > 0) {
                let markerParams = this.createMarkers(markerOriginData);
                // 更新文本框信息
                this.updataCollapsed(markerParams);
                this.zMapApp.EXClustersAPI.addClusterMarkers(
                    markerParams,
                    DEFAULT_MARKER_ICON_OPTIONS,
                    centerAndZoom, // 是否开启自适应缩放及定位
                    null, // 聚合参数
                    result => {
                        if (result.status === 'success') {
                            this.handleBlinkCircle();
                        }
                    }
                );
                this.offlineMapEngineAdjust(centerAndZoom);
            }
            // 位置重影处理（有弊端，刷新时会导致当前选中样式清除）
            if (this.lastMarker) {
                this.zMapApp.EXClustersAPI.clearHighlightFeatures();
            }
            if (centerAndZoom) {
                let lonlat = this.zMapApp.zmap.getCenter();
                this.queryMapClickedPosition(lonlat.join(' '));
            }
        },
        createMarkers(datas) {
            let markers = [];
            for (const key in datas) {
                const { longitude, latitude } = datas[key];
                // 将属性直接添加到数据对象上，避免后续重复创建
                datas[key].originalLongitude = longitude;
                datas[key].originalLatitude = latitude;
                datas[key].batteryNames = [datas[key].batteryName];
                datas[key].lat = latitude;
                datas[key].lng = longitude;
                datas[key].motionState = +datas[key].motionState; // 运动状态, 0-静止, 1-运动
                // 使用已有的属性创建 marker 对象，避免重复创建
                let marker = {
                    lonLat: [longitude, latitude],
                    attributes: {
                        moId: datas[key].moId,
                        batteryNames: datas[key].batteryNames,
                        lat: datas[key].lat,
                        lng: datas[key].lng,
                        motionState: datas[key].motionState,
                        data: [datas[key]],
                    },
                };
                markers.push(marker);
            }
            return markers;
        },
        // 离线地图瓦片目前没有下载16以上级别，初始都在一个点的话，会不展示地图瓦片，此处特殊设置一下
        offlineMapEngineAdjust(centerAndZoom) {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let isOfflineMap = mapEngine && mapEngine.toLowerCase().includes('offline');
            let curZoom = this.zMapApp.zmap.getZoom();
            if (isOfflineMap && centerAndZoom && curZoom >= 15) {
                this.zMapApp.customOffsetPosition({
                    lon: this.zMapApp.zmap.getCenter()[0],
                    lat: this.zMapApp.zmap.getCenter()[1],
                    zoom: 14,
                });
            }
        },
        removePopup() {
            if (this.popup) {
                this.zMapApp.PopupAPI.removePopup(this.popup);
                this.popup = null;
            }
        },
        updateLastMarker(marker) {
            if (this.lastMarker) {
                this.zMapApp.EXClustersAPI.clearHighlightFeatures();
            }
            if (marker) {
                this.zMapApp.EXClustersAPI.highlightFeatures(
                    marker,
                    {
                        iconParams: {
                            url: BLUE_ICON_URL,
                            scale: 1.0,
                        },
                    },
                    false // 是否缩放到高亮
                );
            }
            this.lastMarker = marker;
        },
        showMarkerPopup(marker) {
            this.removePopup();
            let contentHTML = this.createPopupContent(marker);
            let popupParams = {
                // lonLat: marker.attributes.lonLat,
                xyCoordinate: marker.getGeometry().getCoordinates(),
                contentTitle: '',
                titleStyle: { display: 'none' },
                placement: 'top',
                contentStyle: '',
                contentHTML,
                offset: [0, -10],
            };
            this.popup = this.zMapApp.PopupAPI.addPopup(popupParams);
        },
        createPopupContent(marker) {
            let batteryNames = [];
            const markerData = marker.get('features');
            if (markerData) {
                markerData.forEach(item => {
                    batteryNames.push(...item.attributes.batteryNames);
                });
            } else {
                batteryNames = marker.attributes.batteryNames;
            }
            let contentArr = [
                `<div class="battery-content">
                    <span class="plx-ico-close-16 close-icon" onclick="_zMap_close_popup()"></span>
                    <div class="device-list">`,
            ];
            const contentSite = batteryNames.map(e => {
                return `
                        <div class="item">
                            ${e}
                        </div>
                    `;
            });
            contentArr.push(...contentSite);
            contentArr.push('</div></div>');
            let popupContent = contentArr.join('');
            return popupContent;
        },
        ...mapActions({
            setMarkerInfoData: 'battMonitor/setMarkerInfoData',
            changeLocInfoData: 'battMonitor/changeLocInfoData',
        }),
        handleMarkerData(data) {
            let result = [];
            if (Array.isArray(data) && data.length > 0) {
                let markerMap = {};
                data.forEach(item => {
                    const { longitude, latitude } = item;
                    let key = [longitude, latitude].toString();
                    item.originalLongitude = longitude;
                    item.originalLatitude = latitude;
                    if (!markerMap[key]) {
                        markerMap[key] = {
                            batteryNames: [item.batteryName],
                            lat: latitude,
                            lng: longitude,
                            motionState: +item.motionState === 1 ? 1 : 0, // 运动状态, 0-静止, 1-运动
                            data: [item],
                        };
                    } else {
                        markerMap[key].batteryNames.push(item.batteryName);
                        markerMap[key].data.push(item);
                        // 为每个marker指定其运动状态,只要节点中电池有一个是"运动",则此节点为"运动"
                        if (+item.motionState === 1) {
                            markerMap[key].motionState = 1;
                        }
                    }
                });
                Object.keys(markerMap).forEach(key => {
                    result.push(markerMap[key]);
                });
            }
            return result;
        },

        /**
         * 构造经纬度涵盖四个精度的数据,以适配不同zoom值下的效果
         * @param {Array} sourceData
         */
        preprocessingData(sourceData) {
            /**
             * 经纬度小数精度与地图zoom值的对应关系
             * 属性key代表精度
             * 属性值的数组的元素代表此精度对应的地图zoom值
             */
            let accuracyObj = {
                0: [0, 1, 2, 3, 4, 5, 6],
                1: [7, 8, 9, 10],
                2: [11, 12, 13],
                3: [14, 15],
                4: [16, 17, 18, 19, 20, 21, 22],
            };
            let result = {};
            Object.keys(accuracyObj).forEach(key => {
                result[key] = this.preprocessingAccuracyData(sourceData, accuracyObj[key], key);
            });
            return result;
        },

        /**
         * 根据经纬度精度进行数据预处理
         * @param {Array} sourceData
         * @param {Number} locationDataAccuracy: 地理位置数据精度,代表经纬度后面保留多少位数
         * @param {Array} zoomArr: 此经纬度精度对应的googlemap地图zoom值
         */
        preprocessingAccuracyData(sourceData, zoomArr, accuracy) {
            /**
             * 存储处理后的数据
             * {
             *      key: {
             *          motionState: number, // 0 or 1
             *          batteryNames: Array, // 此节点下所有电池的名字
             *          data: Array, // 此节点下所有电池的源数据
             *      }, ...
             * }
             */
            let result = {
                zoomArr,
            };
            // 以精度为locationDataAccuracy的经纬度作为属性键值,存入result
            sourceData.forEach(d => {
                let processedLat = +(+d.latitude).toFixed(accuracy); // latitude属性源数据中是string,需要先转换成number
                let processedLng = +(+d.longitude).toFixed(accuracy); // longitude属性源数据中是string,需要先转换成number
                let key = `${processedLat}-${processedLng}`; // 将处理后的经纬度作为属性键值
                d.originalLatitude = d.latitude; // 左侧折叠显示信息用
                d.originalLongitude = d.longitude; // 左侧折叠显示信息用
                let motionStateNumber; // 运动状态, 0-静止, 1-运动
                if (+d.motionState === 1) {
                    motionStateNumber = 1;
                } else {
                    motionStateNumber = 0;
                }
                // 为每个marker指定其运动状态,只要节点中电池有一个是"运动",则此节点为"运动"
                if (result[key]) {
                    if (motionStateNumber && !result[key].motionState) {
                        result[key].motionState = 1;
                    }
                    result[key].batteryNames.push(d.batteryName);
                    result[key].data.push(d);
                } else {
                    let motionState = 0;
                    if (motionStateNumber) {
                        motionState = 1;
                    }
                    result[key] = {
                        lat: processedLat,
                        lng: processedLng,
                        motionState,
                        batteryNames: [d.batteryName],
                        data: [d],
                    };
                }
            });
            // 再次处理,将所有数据放到数组中,方便绘制
            let mapRenderingDataResult = {};
            mapRenderingDataResult.zoomArr = result.zoomArr;
            mapRenderingDataResult.data = [];
            Object.keys(result).forEach(key => {
                if (key !== 'zoomArr') {
                    mapRenderingDataResult.data.push(result[key]);
                }
            });
            return mapRenderingDataResult;
        },
        zgisOfflineMapWarning() {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let isOfflineMap = mapEngine && mapEngine.toLowerCase().includes('offline');
            if (isOfflineMap) {
                this.$message({
                    message: this.$t('tipMessage.geocodeWarningInOfflineMap'),
                    duration: 10000,
                    showClose: true,
                    type: 'warning',
                });
            }
        },
    },
    beforeUnmount() {
        if (this.zMapApp) {
            this.removeMarker();
            this.removePopup();
            this.zMapApp.zmap.unbindClickEvent();
            this.zMapApp.zmap.unbindMoveEvent();
            this.zMapApp.dispose();
        }
    },
};
</script>

<style lang="scss" scoped>
.gis-map {
    position: fixed;
    width: 100%;
    height: 100%;
}
.battery-content {
    cursor: default;
}
:deep(.zpopover.top .arrow) {
    border-top-color: #fff;
}
:deep(.zpopover.top .arrow::after) {
    border-top-color: #fff;
}
:deep(.zpopover-content) {
    padding: 20px 16px 16px;
    border-radius: 4px;
    color: #303133;
    background-color: #fff;
    .battery-content {
        position: relative;
        min-width: 20px;
        white-space: nowrap;
        font-size: 16px;
        text-align: left;
        .close-icon {
            position: absolute;
            top: -20px;
            right: -16px;
            font-size: 18px;
            color: #606266;
            cursor: pointer;
            &:hover {
                color: #1993ff;
            }
        }
        .device-list {
            max-height: 304px;
            overflow: auto;
            margin-right: -16px;
            .item {
                text-align: left;
                padding: 8px 16px 8px 0;
                line-height: 16px;
                font-size: 14px;
            }
            .item:first-child {
                padding-top: 0;
            }
            .item:last-child {
                padding-bottom: 0;
            }
        }
    }
}
:deep(.ol-viewport .coordinate-display-control) {
    display: none;
}

:deep(.ol-viewport .ol-scale-line.zmap-scale-line-inner) {
    padding-bottom: 8px;
    color: #303133;
    border: #606266 1px solid;
    text-shadow: 1px 1px 1px #cfd3dc;
    box-shadow:
        1px 1px 1px #cfd3dc,
        -1px 0 0 #cfd3dc;
    border-top: none;
}

:deep(.ol-attribution ul) {
    color: #606266;
    text-shadow: none;
}
</style>
<style lang="scss">
body.theme-dark {
    .zpopover.top .arrow {
        border-top-color: #141414;
    }
    .zpopover.top .arrow::after {
        border-top-color: #141414;
    }
    .zpopover-content {
        color: #e5eaf3;
        background-color: #141414;
        .battery-content {
            .close-icon {
                color: #86888c;
                &:hover {
                    color: #1993ff;
                }
            }
        }
    }

    .ol-attribution ul {
        color: #86888c;
    }
}
.ol-overlaycontainer-stopevent {
    z-index: 9999 !important;
}
</style>
