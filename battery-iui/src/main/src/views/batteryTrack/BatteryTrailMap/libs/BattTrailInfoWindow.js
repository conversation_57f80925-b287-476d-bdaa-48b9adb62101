/*
 * @Date: 2021-01-12 08:26:17
 * @LastEditors: yan yan
 * @LastEditTime: 2021-01-29 17:17:18
 * @Description: 电池轨迹页面中，infowindow内所使用的组件，需要用Vue.extend实现
 */

// import Vue from 'vue';
// import BattInfoWindowComponent from '../BattInfoWindow.vue';

// export function BattInfoWindow({ title = 'no title', timeStr = 'no timeStr', btnStr = 'no btnStr', moId = '' }) {
//     let battInfo = Vue.extend(BattInfoWindowComponent);
//     this.elem = new battInfo({
//         propsData: {
//             title,
//             timeStr,
//             btnStr,
//             moId,
//         },
//     }).$mount();
// }

// BattInfoWindow.prototype.getContentStr = function () {
//     return this.elem.$el;
// };


// vue3

import { createApp, defineComponent, h } from 'vue';
import BattInfoWindowComponent from '../BattInfoWindow.vue';

export function BattInfoWindow({ title = 'no title', timeStr = 'no timeStr', btnStr = 'no btnStr', moId = '' }) {
    // 使用 defineComponent 创建组件
    const BattInfoWindowApp = defineComponent({
        render() {
            return h(BattInfoWindowComponent, {
                title,
                timeStr,
                btnStr,
                moId,
            });
        }
    });

    // 使用 createApp 创建应用实例并挂载
    const app = createApp(BattInfoWindowApp);
    const rootElem = document.createElement('div');
    this.elem = app.mount(rootElem);
}

BattInfoWindow.prototype.getContentStr = function () {
    return this.elem.$el;
};

