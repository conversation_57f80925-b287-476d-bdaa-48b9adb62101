<template>
    <div v-loading="loading" style="height: 100%; width: 100%">
        <div :class="['searchPanel', { 'lang-en': this.isLangEn}]">
            <div style="margin-top: 10px; font-size: 12px">
                <el-form ref="battTrailForm" :model="formData" :inline="true" :rules="formRules" class="form-container">
                    <el-form-item label="">
                        <el-button
                            :title="$t('common.lastPeriod')"
                            :disabled="!backable"
                            type="default"
                            @click="clickBack"
                        >
                            <el-icon><CaretLeft /></el-icon>
                        </el-button>
                    </el-form-item>
                    <el-form-item label="" prop="startTime">
                        <el-date-picker
                            v-model="formData.startTime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            type="datetime"
                            :placeholder="$t('datetimePicker.startTime')"
                            :disabled-date="pickerOptions.disabledDate"
                            @change="clearShortcutTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <span style="color: #404040">{{ $t('datetimePicker.to') }}</span>
                    </el-form-item>
                    <el-form-item label="" prop="endTime">
                        <el-date-picker
                            v-model="formData.endTime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            type="datetime"
                            :placeholder="$t('datetimePicker.endTime')"
                            :disabled-date="pickerOptions.disabledDate"
                            @change="clearShortcutTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <el-button
                            :title="$t('common.nextPeriod')"
                            :disabled="!forwardable"
                            type="default"
                            @click="clickForward"
                        >
                            <el-icon><CaretRight /></el-icon>
                        </el-button>
                    </el-form-item>
                    <el-form-item label="">
                        <el-button style="margin-left: 10px" size="mini" type="primary" @click="queryHistoryPath">
                            {{ $t('button.historyTrail') }}
                        </el-button>
                        <el-tooltip :content="$t('button.realTrail')" placement="top">
                            <span class="icon-button" @click="queryRealTimePath">
                                <el-icon><Location /></el-icon>
                            </span>
                        </el-tooltip>
                        <el-tooltip :content="$t('button.latestPosition')" placement="top">
                            <span class="icon-button el-icon-right" @click="latestPosition">
                                <el-icon><Right /></el-icon>
                            </span>
                        </el-tooltip>
                    </el-form-item>
                </el-form>
            </div>
            <div style="margin-top: 6px" class="radio-container">
                <el-radio-group v-model="shortcutTime" size="small" @change="clickShortcutTime">
                    <el-radio-button label="1">
                        {{ $t('common.lastHours', { num: 8 }) }}
                    </el-radio-button>
                    <el-radio-button label="2">
                        {{ $t('common.lastHours', { num: 24 }) }}
                    </el-radio-button>
                    <el-radio-button label="3">
                        {{ $t('common.lastDays', { num: 3 }) }}
                    </el-radio-button>
                    <el-radio-button label="4">
                        {{ $t('common.lastDays', { num: 10 }) }}
                    </el-radio-button>
                    <el-radio-button label="5">
                        {{ $t('common.lastDays', { num: 30 }) }}
                    </el-radio-button>
                </el-radio-group>
                <!-- 统计粒度提示 -->
                <!-- <div v-if="shortcutTime" class="radio-tip">
                    {{ dayGranularity }}
                </div> -->
            </div>
        </div>

        <div
            id="zgisMapContainer"
            ref="googleMapContainer"
            class="googleMapContainer"
            :class="{ 'bat-monitor-trail-dark-style': isDark }"
        ></div>

        <!-- 中下位置，当前地理位置信息 -->
        <batt-trail-location-info :location-data="locationData"></batt-trail-location-info>

        <!-- 左上位置，当前电池信息 -->
        <div class="battInfo">
            <div class="batt-info-title" :title="battName">
                {{ battName }}
            </div>
            <div
                v-for="(value, name) in currBattInfo"
                :key="name"
                class="batt-info-content"
                :title="`${$t(`batteryMonitor.attrNames.${name}`)}: ${value}`"
            >
                {{ $t(`batteryMonitor.attrNames.${name}`) }}: {{ translateInfoValue(value) }}
            </div>
        </div>
    </div>
</template>

<script>
import BattTrailLocationInfo from '../BattTrailLocationInfo.vue';
import HTTP from '@/util/httpService.js';
import { translateKeyWords } from '../../ApiMiddleWare';
import { fitlerBatteryInfomation } from '../../utils.js';
import queryFuncs from '../queryFuncs.js';
import { getLastTimeByDiff, stepTimeRange, timeFormat, getGrainByRange } from '@/util/timeUtil.js';
import { Loader } from '../../zgis/js/zgisMapLoader';
import {
    MARKER_ICON_SIZE,
    STEP_ICON_SIZE,
    BLUE_ICON_URL,
    CIRCLE_START_URL,
    CIRCLE_STEP_URL,
    LINE_STYLE_CONFIG,
    INTERVAL_MILLION_SECONDS,
    EXTENT_LIMIT,
    OSMOFFLINE_EXTENT_LIMIT,
    handleZgisPosition,
} from '../../zgis/js/zgisUtils';
import ToolTip from '../libs/ToolTip.js';

const TIME_GRAIN = {
    1: [8, 'hour'],
    2: [24, 'hour'],
    3: [3, 'day'],
    4: [10, 'day'],
    5: [30, 'day'],
};

export default {
    components: {
        'batt-trail-location-info': BattTrailLocationInfo,
    },

    data() {
        return {
            gmap: null,
            interval: null,
            formData: {
                startTime: '',
                endTime: '',
            },
            defaultParam: {
                moId: null,
                startTime: '',
                endTime: '',
            },

            moId: null,
            pageType: 'default',

            // 页面中下部位，地址信息框中的数据
            locationData: null,

            // 电池信息
            battInfo: null,
            // 当前电池的名字
            battName: void 0,
            currBattInfo: null,

            loading: false,
            shortcutTime: '',
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > new Date().getTime();
                },
            },
            isTimeError30Days: false,
        };
    },

    computed: {
        isLangEn() {
            return this.$i18n.locale === 'en-US';
        },
        // 统计粒度提示
        dayGranularity() {
            let granularityTip;
            if (this.shortcutTime == '1') {
                granularityTip = 5;
            }
            if (this.shortcutTime == '2') {
                granularityTip = 15;
            }
            if (this.shortcutTime == '3') {
                granularityTip = 45;
            }
            if (this.shortcutTime == '4') {
                granularityTip = 150;
            }
            if (this.shortcutTime == '5') {
                granularityTip = 450;
            }
            let granularityTipWord = this.$t('common.trailFormTip', { num: granularityTip });

            return granularityTipWord;
        },
        formGranularity() {
            let granularityTipWord = '';
            if (this.formData.startTime && this.formData.endTime) {
                let date1 = new Date(this.formData.startTime);
                let date2 = new Date(this.formData.endTime);
                let time1 = date1.getTime();
                let time2 = date2.getTime();
                if (time1 >= time2) {
                    return;
                }
                let granularity = (time2 - time1) / 1000 / 60;
                let granularityTip;
                if (granularity <= 1440 * 30) {
                    granularityTip = '450'; // 30天内 450分钟粒度
                }
                if (granularity <= 14400) {
                    granularityTip = '150'; // 10天内 150分钟粒度
                }
                if (granularity <= 1440 * 3) {
                    granularityTip = '45'; // 3天内 45分钟粒度
                }
                if (granularity <= 1440) {
                    granularityTip = '15'; // 24小时内 15分钟粒度
                }
                if (granularity <= 480) {
                    granularityTip = '5'; // 8小时内 5分钟粒度
                }
                granularityTipWord = this.$t('common.trailFormTip', { num: granularityTip });
            }
            return granularityTipWord;
        },
        backable() {
            let result = false;
            let start = this.formData.startTime;
            let end = this.formData.endTime;
            if (start && end && new Date(start) < new Date(end)) {
                result = true;
            }
            return result;
        },
        forwardable() {
            let result = false;
            let start = this.formData.startTime;
            let end = this.formData.endTime;
            let startDate = new Date(start);
            let endDate = new Date(end);
            let forwardEndDate = new Date(endDate - 0 + (endDate - startDate));
            let nowTime = `${timeFormat(new Date(), 'yyyy-MM-dd')} 23:59:59`;
            let nowDate = new Date(nowTime);
            if (start && end && startDate < endDate && forwardEndDate <= nowDate) {
                result = true;
            }
            return result;
        },
        formRules() {
            let _this = this;
            const startTimeValidator = (rule, value, callback) => {
                this.isTimeError30Days = false;
                if (value == null || value == '') {
                    return callback(new Error(_this.$t('batteryTest.message.selectStartTime')));
                }
                let start = Date.parse(value);
                if (_this.formData.endTime && _this.formData.endTime != '') {
                    let end = Date.parse(_this.formData.endTime);
                    let diff = end - start;
                    if (diff < 0) {
                        this.$refs.battTrailForm.clearValidate('endTime');
                        this.isTimeError30Days = true;
                        return callback(new Error(_this.$t('batteryMonitor.message.timeError')));
                    } else if (diff > 30 * 24 * 60 * 60 * 1000) {
                        this.isTimeError30Days = true;
                        this.$refs.battTrailForm.clearValidate('endTime');
                        return callback(new Error(_this.$t('batteryMonitor.message.timeError30Days')));
                    }
                }
                let myDate = new Date();
                let timeNow = myDate.getTime();
                if (start - timeNow > 0) {
                    return callback(new Error(_this.$t('batteryMonitor.message.notOverCurrent')));
                }
                return callback();
            };
            const endTimeValidator = (rule, value, callback) => {
                this.isTimeError30Days = false;
                if (value == null || value == '') {
                    return callback(new Error(_this.$t('batteryTest.message.selectEndTime')));
                }
                let end = Date.parse(value);
                if (_this.formData.startTime && _this.formData.startTime != '') {
                    let start = Date.parse(_this.formData.startTime);
                    let diff = end - start;

                    if (diff < 0) {
                        this.$refs.battTrailForm.clearValidate('startTime');
                        this.isTimeError30Days = true;
                        return callback(new Error(_this.$t('batteryMonitor.message.timeError')));
                    } else if (diff > 30 * 24 * 60 * 60 * 1000) {
                        this.isTimeError30Days = true;
                        this.$refs.battTrailForm.clearValidate('startTime');
                        return callback(new Error(_this.$t('batteryMonitor.message.timeError30Days')));
                    }
                }
                let myDate = new Date();
                let timeNow = myDate.getTime();
                if (end - timeNow > 0) {
                    return callback(new Error(_this.$t('batteryMonitor.message.notOverCurrent')));
                }

                this.formData.startTime && this.$refs.battTrailForm.validateField('startTime');
                return callback();
            };
            let rules = {
                startTime: [{ validator: startTimeValidator, trigger: 'change' }],
                endTime: [{ validator: endTimeValidator, trigger: 'change' }],
            };

            return rules;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        showStartPoint() {
            return this.pageType === 'steal';
        },
    },

    watch: {
        battInfo: {
            deep: true,
            handler(val) {
                let currBattInfo = this.fitlerBattInfo(val);
                this.currBattInfo = currBattInfo;
            },
        },
    },

    created() {
        document.title = this.$t('batteryMonitor.message.battTrailTracking');
    },

    mounted() {
        this.zMapAppMarkers = [];
        this.startMarker = null;
        this.startPointInfo = null;
        this.setPositionInfo = null;
        this.lineArrs = [];
        this.batteryTrailData = [];
        this._blinckCircles = [];
        this.zMapApp = null;
        this.popup = null;
        this.mapContainer = document.getElementById('zgisMapContainer');
        this.infoToolTip = new ToolTip(this.mapContainer.parentNode);
        // 根据路由参数初始化moId和页面类型
        let _this = this;

        // this.loading = true;

        this.moId = this.$route.query.id;
        if (this.$route.query.pageType) {
            this.pageType = this.$route.query.pageType;
        }
        this.defaultParam.moId = this.moId;
        this.loadZmap();
        window._zMap_close_popup = () => {
            this.removePopup();
        };
        window._zMap_set_position = this.setPosition;
    },
    beforeUnmount() {
        if (this.zMapApp) {
            this.removeMarker();
            this.removeStartMarker();
            this.removeTrailLine();
            this.removePopup();
            this.zMapApp.zmap.unbindClickEvent();
            this.zMapApp.dispose();
        }
    },
    methods: {
        // 修改时间时，取消最近时间快捷按钮选中
        clearShortcutTime() {
            this.shortcutTime = 0;
        },
        clickBack() {
            let start = this.formData.startTime;
            let end = this.formData.endTime;
            let newTime = stepTimeRange(start, end);
            this.formData.startTime = newTime.start;
            this.formData.endTime = newTime.end;
            this.clearShortcutTime();
            this.queryHistoryPath();
        },
        clickForward() {
            let start = this.formData.startTime;
            let end = this.formData.endTime;
            let newTime = stepTimeRange(start, end, 'forward');
            this.formData.startTime = newTime.start;
            this.formData.endTime = newTime.end;
            this.clearShortcutTime();
            this.queryHistoryPath();
        },
        clickShortcutTime(key) {
            const [diff, type] = TIME_GRAIN[key];
            let timeObj = getLastTimeByDiff(diff, type);
            this.formData.startTime = timeObj.start;
            this.formData.endTime = timeObj.end;
            this.queryHistoryPath();
        },
        latestPosition() {
            if (this.zMapApp) {
                let length = this.batteryTrailData.length;
                if (length) {
                    let lastPos = this.batteryTrailData[length - 1];
                    // this.zMapApp.zmap.setCenter(lastPos.lonLat);
                    /* Started by AICoder, pid:q50dbhbecevf4f8146e10b9f70e7e807b2a81f52 */
                    // 百度地图当精度较高时，上面setCenter方法会存在偏移（临时处理，后续版本升级）
                    if (this.zMapApp.zmap.mapEngine === 'BaiduMap' || this.zMapApp.zmap.mapEngine === 'BaiduOffLineMap') {
                        // eslint-disable-next-line max-len
                        this.zMapApp.zmap.map.getView().setCenter(this.zMapApp.zmap.getXYCoordinateByLonLat(lastPos.lonLat, true));
                    } else {
                        // eslint-disable-next-line max-len
                        this.zMapApp.zmap.map.getView().setCenter(this.zMapApp.zmap.getXYCoordinateByLonLat(lastPos.lonLat, false));
                    }
                    /* Ended by AICoder, pid:q50dbhbecevf4f8146e10b9f70e7e807b2a81f52 */
                }
            }
        },

        translateInfoValue(value) {
            let result = value;
            let translateArr = [
                'Static',
                'Motion',
                'Fortification',
                'Withdraw',
                'Normal',
                'Abnormal',
                'OnLine',
                'Online',
                'OffLine',
                'Offline',
                'Steal',
                'Normal',
                'undefined',
                'Undefined',
                'Unknown',
            ];
            if (translateArr.indexOf(value) + 1) {
                result = this.$t(`batteryMonitor.attrValues.${value}`);
            }

            return result;
        },

        // 筛选需要展示的数据
        fitlerBattInfo(sourceData) {
            let _this = this;
            return fitlerBatteryInfomation(
                sourceData,
                function (sourceData) {
                    _this.battName = sourceData.batteryName;
                },
                'theftStatus'
            );
        },
        initBatteryInfo() {
            let _this = this;
            this.loading = false;
            // 配置电池初始位置初始化数据
            queryFuncs
                .queryStartLocation(this.moId)
                .then(data => {
                    let param = this.handleTrailData([data]);
                    this.startPointInfo = param[0] ? param[0] : null;
                })
                .catch(e => {
                    throw new Error(e);
                });

            // 获取当前电池的地理信息位置
            queryFuncs.queryBattInfo(_this, function (battList) {
                // 筛选出当前电池的数据
                for (let i = 0; i < battList.length; i++) {
                    if (battList[i].moId === _this.moId) {
                        _this.battInfo = battList[i];
                        break;
                    }
                }
                const { longitude, latitude } = _this.battInfo;
                let lonlatString = [longitude, latitude].join(' ');
                _this.updateBatteryInfo(lonlatString);
            });
        },
        updateBatteryInfo(lonlat) {
            if (window.zmaps && window.zmaps.SearchPoint && lonlat) {
                // eslint-disable-next-line new-cap
                window.zmaps.SearchPoint(lonlat, res => {
                    const { currRegion } = handleZgisPosition(res.data);
                    this.currBattInfo.batteryLocation = currRegion || '--';
                });
            }
        },
        /* Started by AICoder, pid:zc7611d013v2dfa1439f0ab9a007340a0388e20d */
        updatePartBatteryInfo(data) {
            this.battName = data.batteryName;
            const { longitude, latitude } = data;
            let lonlatString = [longitude, latitude].join(' ');
            this.currBattInfo.batteryCoordinate = `${latitude}, ${longitude}`;
            this.currBattInfo.motionState = translateKeyWords('motionState', data.motionState);
            this.updateBatteryInfo(lonlatString);
        },
        /* Ended by AICoder, pid:zc7611d013v2dfa1439f0ab9a007340a0388e20d */
        queryMapClickedPosition(lonlat) {
            if (window.zmaps && window.zmaps.SearchPoint && lonlat) {
                // eslint-disable-next-line new-cap
                window.zmaps.SearchPoint(lonlat, res => {
                    this.locationData = res.data;
                });
            }
        },
        // 查询距离当前时间3小时之内的轨迹，并实时更新
        queryRealTimeBatteryTrail() {
            this.loading = true;
            this.removeMarker();
            this.removeStartMarker();
            this.removeTrailLine();
            this.batteryTrailData = [];
            HTTP.request('initialBatteryTrail', {
                method: 'post',
                urlParam: this.defaultParam,
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        /* Started by AICoder, pid:e2cfco7fc8y4c5d14d790bd06037be0ab5852c94 */
                        const trailData = data.data.map(item => ({
                            ...item,
                            moId: item.batteryId,
                            time: item.firstRecordTime,
                        }));
                        /* Ended by AICoder, pid:e2cfco7fc8y4c5d14d790bd06037be0ab5852c94 */
                        this.getInitTrailPath(trailData);
                        this.updateBatteryTrail();
                    }
                },
            });
        },
        // 查询用户选择时间范围的历史轨迹
        queryHistoryPath() {
            this.interval && clearInterval(this.interval);
            this.interval = null;

            this.$refs.battTrailForm.validate(valid => {
                if (valid) {
                    let param = {
                        moId: this.moId,
                        startTime: this.formData.startTime,
                        endTime: this.formData.endTime,
                    };
                    let isInit = false;
                    // param.graininNumber = getGrainByRange(param.startTime, param.endTime);

                    this.loading = true;

                    this.queryHistoryBatteryTrail(isInit, param);
                }
            });
        },
        queryHistoryBatteryTrail(isInit, param) {
            this.removeMarker();
            this.removeStartMarker();
            this.removeTrailLine();
            this.batteryTrailData = [];
            HTTP.request('initialBatteryTrail', {
                method: 'post',
                urlParam: param,
                complete: data => {
                    this.loading = false;
                    if (data.code === 0 && data.data) {
                        const newList = data.data.map(item => ({
                            ...item,
                            moId: item.batteryId,
                            time: item.firstRecordTime,
                        }));
                        let trailData = Array.isArray(newList) ? newList : [];
                        if (isInit === true) {
                            setTimeout(() => {
                                // 处理初始化-osm-多次调用无返回问题（临时方案）
                                this.getInitTrailPath(trailData);
                            }, 2000);
                            this.updateBatteryTrail(); // 开启定时获取最新轨迹点
                        } else if (trailData.length > 0) {
                            this.batteryTrailData = this.handleTrailData(trailData);
                            let center = true;
                            this.updateMarker(this.batteryTrailData, center);
                            this.updateTrailLine(this.batteryTrailData);
                        } else {
                            this.$message({
                                type: 'warning',
                                message: this.$t('batteryMonitor.message.emptyTrailData'),
                            });
                        }
                    }
                },
                error: e => {
                    this.loading = false;
                    this.$message({
                        type: 'warning',
                        message: this.$t('batteryMonitor.message.emptyTrailData'),
                    });
                    console.error('net work error:', e);
                },
            });
        },
        // 实时轨迹按钮点击, 查询实时轨迹
        queryRealTimePath() {
            this.shortcutTime = '';
            this.interval && clearInterval(this.interval);
            this.interval = null;
            this.$refs.battTrailForm.clearValidate();
            this.$refs.battTrailForm.resetFields();
            this.queryRealTimeBatteryTrail();
        },
        getInitTrailPath(hisTrail) {
            let trailData = Array.isArray(hisTrail) ? hisTrail : [];
            queryFuncs.getUpdateButteryTrailPosition(this.moId, data => {
                if (data) {
                    trailData.push(data);
                    this.updatePartBatteryInfo(data);
                }
                this.batteryTrailData = this.handleTrailData(trailData);
                let center = true;
                this.updateMarker(this.batteryTrailData, center);
                this.updateTrailLine(this.batteryTrailData);
            });
        },
        getTrail() {
            // 返回数据格式data
            // {
            //     "moId": "mo-batt-d0f1vw",
            //     "time": "2023-10-14 05:56:34",
            //     "longitude": "70.000000",
            //     "latitude": "40.000000"
            // }
            queryFuncs.getUpdateButteryTrailPosition(this.moId, data => {
                const { isExist, needLine } = this.isLastNodeTheSame(data);
                this.updatePartBatteryInfo(data);
                if (!isExist) {
                    let param = this.handleTrailData([data]);
                    this.batteryTrailData.push(...param);
                    let length = this.batteryTrailData.length;
                    if (length > 1 && needLine) {
                        let startPoint = { lonLat: this.batteryTrailData[length - 2].lonLat };
                        let endPoint = { lonLat: this.batteryTrailData[length - 1].lonLat };
                        let newLine = this.createSingleLine({ startPoint, endPoint });
                        this.lineArrs.push(newLine);
                    }
                    this.updateMarker(param);
                }
            });
        },
        updateBatteryTrail() {
            if (!this.interval) {
                this.interval = setInterval(() => {
                    this.getTrail();
                }, INTERVAL_MILLION_SECONDS);
            }
        },
        async loadZmap() {
            try {
                if (!window.zmaps) {
                    let loader = new Loader();
                    await loader.load();
                }
            } catch (error) {
                console.log('loadZmap error', error);
            }
            if (window.zmaps) {
                this.$nextTick(() => {
                    this.initMap();
                });
            }
        },

        initMap() {
            let userName = 'uedm';
            let pageName = 'uedmMap';
            let hostIP = '';
            let mapOptions = {
                element: document.getElementById('zgisMapContainer'),
                zoomLevel: 3, // 默认为3
                mapTheme: this.isDark ? 'blue' : '', // 支持:darkgreen,blue,reversal,black,gray. ''表示地图原始样式
                center: [0, 0], // 默认为[0, 0]
                minZoomLevel: 1, // 最小级别
                numZoomLevels: 20, // 最大级别
                hideLegend: true, // 是否隐藏图例面板，默认值false
                hideTools: true, // 是否隐藏工具栏，默认值false
                hideZoomBar: true, // 是否否隐藏缩放条，默认值false
                hidesatelliteMap: true, // 是否隐藏卫星视图按钮，默认值false
                callback: application => {
                    this.zMapApp = application;
                    this.limitExtent();
                    window.zMapApp = application;
                    this.zgisOfflineMapWarning();
                    this.initBatteryInfo();
                    this.initTrail();
                    this.bindMarkerEvents();
                    this.bindMapEvents();
                },
            };
            if (window.zmaps) {
                // eslint-disable-next-line new-cap
                window.zmaps.InitMap(userName, pageName, hostIP, mapOptions);
            }
        },
        limitExtent() {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let extent = mapEngine === 'OSMOffLineMap' ? OSMOFFLINE_EXTENT_LIMIT : EXTENT_LIMIT;
            this.zMapApp.zmap.setExtentLimit(extent);
        },
        initTrail() {
            let param = {
                moId: this.moId,
                startTime: '',
                endTime: '',
            };
            let isInit = true;
            this.queryHistoryBatteryTrail(isInit, param);
        },
        bindMarkerEvents() {
            if (this.zMapApp) {
                this.zMapApp.MarkerAPI.bindEvents({
                    click: feature => {
                        // console.log('marker click', feature);
                        this.canSetStartPosition(feature);
                    },
                    clickout: e => {
                        // console.log('marker click out', e);
                    },
                    mouseover: feature => {
                        // console.log('marker mouseover', feature);
                        this.showMarkerToolTip(feature);
                    },
                    mouseout: e => {
                        // console.log('marker mouseout', e);
                        this.infoToolTip.hide();
                    },
                });
            }
        },
        bindMapEvents() {
            if (this.zMapApp) {
                this.zMapApp.zmap.bindClickEvent(lonlat => {
                    let lonlatString = Array.isArray(lonlat) ? lonlat.join(' ') : '';
                    this.queryMapClickedPosition(lonlatString);
                });
            }
        },
        removeMarker() {
            if (this.zMapApp) {
                this.zMapApp.MarkerAPI.removeMarkers(this.zMapAppMarkers);
                // this.zMapApp.MarkerAPI.clearMarkers(this.zMapAppMarkers);
                this.zMapAppMarkers = [];
            }
        },
        removeStartMarker() {
            if (this.zMapApp && this.startMarker) {
                this.zMapApp.MarkerAPI.removeMarkers([this.startMarker]);
                this.startMarker = null;
            }
        },
        async updateMarker(originalData, centerAndZoom = false) {
            if (!this.zMapApp) {
                return;
            }
            let markerParams = this.createMarkerParams(originalData);
            let addedMarkers = this.zMapApp.MarkerAPI.addMarkers(markerParams, centerAndZoom);
            this.zMapAppMarkers = this.zMapAppMarkers.concat(addedMarkers);
            if (centerAndZoom) {
                let lonlat = this.zMapApp.zmap.getCenter();
                this.queryMapClickedPosition(lonlat.join(' '));
            }
            // 更新后，上次的最后一个标记icon设置为step的icon
            let length = this.zMapAppMarkers.length;
            if (length - 2 > -1) {
                this.zMapAppMarkers[length - 2].setIcon({
                    url: CIRCLE_STEP_URL,
                    size: STEP_ICON_SIZE,
                });
            }
            // 更新后，需重新生成一次startMarker，保证不被覆盖
            this.removeStartMarker();
            if (this.showStartPoint && !this.startMarker && this.startPointInfo) {
                this.startMarker = this.createStartMarker(false);
            }
            this.offlineMapEngineAdjust(centerAndZoom);
        },
        // 离线地图瓦片目前没有下载16以上级别，初始都在一个点的话，会不展示地图瓦片，此处特殊设置一下
        offlineMapEngineAdjust(centerAndZoom) {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let isOfflineMap = mapEngine && mapEngine.toLowerCase().includes('offline');
            let curZoom = this.zMapApp.zmap.getZoom();
            if (isOfflineMap && centerAndZoom && curZoom >= 15) {
                this.zMapApp.customOffsetPosition({
                    lon: this.zMapApp.zmap.getCenter()[0],
                    lat: this.zMapApp.zmap.getCenter()[1],
                    zoom: 14,
                });
            }
        },
        createStartMarker(center = false) {
            let startMarker = {
                lonLat: this.startPointInfo.lonLat,
                attributes: this.startPointInfo,
                icon: {
                    url: CIRCLE_START_URL,
                    size: STEP_ICON_SIZE,
                },
            };
            let markers = this.zMapApp.MarkerAPI.addMarkers([startMarker], center);
            return markers[0];
        },
        createMarkerParams(datas) {
            let markers = [];
            let lastIndex = datas.length - 1;
            for (let i = 0; i <= lastIndex; i++) {
                let marker = {
                    lonLat: datas[i].lonLat,
                    attributes: JSON.parse(JSON.stringify(datas[i])),
                    icon: {
                        url: i !== lastIndex ? CIRCLE_STEP_URL : BLUE_ICON_URL,
                        size: i !== lastIndex ? STEP_ICON_SIZE : MARKER_ICON_SIZE,
                    },
                };
                marker.attributes.id = `${marker.lonLat.toString()}_${datas[i].info.time}`;
                markers.push(marker);
            }
            return markers;
        },
        removePopup() {
            if (this.popup) {
                this.zMapApp.PopupAPI.removePopup(this.popup);
                this.popup = null;
            }
        },
        removeTrailLine() {
            if (this.lineArrs.length) {
                // this.zMapApp.LayerAPI.VectorLayer.removeFeatures(this.lineArrs);
                this.zMapApp.LayerAPI.VectorLayer.clear();
                this.lineArrs = [];
            }
        },
        showMarkerToolTip(feature) {
            let isStartPoint =
                this.startPointInfo && feature.attributes.lonLat.join(',') === this.startPointInfo.lonLat.join(',');
            let coorxy = feature.geometry.flatCoordinates;
            let pos = this.zMapApp.zmap.map.getPixelFromCoordinate(coorxy);
            let info = {
                ...feature.attributes.info,
            };
            if (this.showStartPoint && isStartPoint) {
                info.isStartPoint = true;
            }
            this.infoToolTip.change({ info, x: pos[0], y: pos[1] });
        },
        canSetStartPosition(feature) {
            // 不是起始位置才弹出设置popup
            let isStartPoint =
                this.startPointInfo && feature.attributes.lonLat.join(',') === this.startPointInfo.lonLat.join(',');
            if (this.showStartPoint && !isStartPoint) {
                this.showSetPositionPopup(feature);
            }
        },

        showSetPositionPopup(marker) {
            this.removePopup();
            if (marker.attributes) {
                let _this = this;
                this.setPositionInfo = {};
                this.setPositionInfo.moId = marker.attributes.info.moId;
                this.setPositionInfo.timeStr = marker.attributes.info.time;
                this.setPositionInfo.latitude = marker.attributes.lonLat[1];
                this.setPositionInfo.longitude = marker.attributes.lonLat[0];
                this.setPositionInfo.clickCb = function () {
                    // 修改当前电池起始点marker的位置
                    const { LatLng, lonLat, info } = marker.attributes;
                    _this.startPointInfo = { LatLng, lonLat, info };
                    _this.zMapApp.MarkerAPI.removeMarkers([_this.startMarker]);
                    _this.startMarker = _this.createStartMarker();
                };
                let contentArr = [
                    `<div class="battery-content">
                        <span class="plx-ico-close-16 close-icon" onclick="_zMap_close_popup()"></span>
                        <div class="position-update-time-label">
                            ${`${this.$t('batteryMonitor.infowindow.title')}:`}
                        </div>
                        <div class="position-update-time-value">${marker.attributes.info.time}</div>
                        <div class="position-set-button" onclick="_zMap_set_position()">
                            ${this.$t('batteryMonitor.infowindow.btn')}
                        </div>
                    </div>`,
                ];
                let contentHTML = contentArr.join('');
                let popupParams = {
                    // lonLat: marker.attributes.lonLat,
                    xyCoordinate: marker.getGeometry().getCoordinates(),
                    contentTitle: '',
                    titleStyle: { display: 'none' },
                    placement: 'top',
                    contentStyle: '',
                    contentHTML,
                };
                this.popup = this.zMapApp.PopupAPI.addPopup(popupParams);
            }
        },
        createPopupContent(marker) {
            const { batteryNames } = marker.attributes;
            let contentArr = [
                `<div class="title">
                    <span class="plx-ico-close-16 close-icon" onclick="_zMap_close_popup()"></span>
                </div>`,
            ];
            const contentSite = batteryNames.map(e => {
                return `
                        <div class="item">
                            ${e}
                        </div>
                    `;
            });
            contentArr.push(...contentSite);
            let popupContent = contentArr.join('');
            return popupContent;
        },
        updateTrailLine(originalData) {
            if (!this.zMapApp || !Array.isArray(originalData)) {
                return;
            }
            let length = originalData.length - 1;
            originalData.forEach((item, index, originArray) => {
                if (index < length) {
                    let startPoint = { lonLat: item.lonLat };
                    let endPoint = { lonLat: originArray[index + 1].lonLat };
                    let newLine = this.createSingleLine({ startPoint, endPoint });
                    this.lineArrs.push(newLine);
                }
            });
        },
        createSingleLine({ startPoint, endPoint }) {
            let lineFacilites = [
                {
                    // id: 1, // 可用于搜索
                    lonLat: startPoint.lonLat,
                    // attributes: {}, // 属性设置
                },
                {
                    lonLat: endPoint.lonLat,
                },
            ];
            let directionalLine = this.zMapApp.LayerAPI.VectorLayer.createMultiArrowLine(lineFacilites, {
                lineStyle: { strokeColor: '#1993ff', strokeWidth: 5 },
                hasArrow: true,
                arrowDirectionReverse: false, // 箭头方向
                // arrowPositionRatio: 0.1,
                // startPointStyle: {}, // 起点样式设置
                // endPointStyle: {}, // 终点样式设置
            });
            this.zMapApp.LayerAPI.VectorLayer.addFeature(directionalLine);
            return directionalLine;
        },
        setPosition() {
            const data = {
                id: this.moId,
                longitude: this.setPositionInfo.longitude,
                latitude: this.setPositionInfo.latitude,
            };
            HTTP.request('setBattLoc', {
                method: 'post',
                data,
                complete: data => {
                    if (data.code !== 0) {
                        this.$message.error(this.$t('batteryMonitor.infowindow.setBattPosFail'));
                        return;
                    } else {
                        this.$message.success(this.$t('batteryMonitor.infowindow.setBattPosSuccess'));
                        this.setPositionInfo.clickCb && this.setPositionInfo.clickCb();
                    }
                },
                error: err => {
                    this.$message.error(`${this.$t('batteryMonitor.infowindow.setBattPosFail')}${err}`);
                },
            });
        },
        handleTrailData(data) {
            let result = [];
            if (Array.isArray(data) && data.length > 0) {
                data.forEach(item => {
                    const { latitude, longitude, time } = item;
                    let lon = parseFloat(longitude);
                    let lat = parseFloat(latitude);
                    if (!isNaN(lon) && !isNaN(lat)) {
                        let param = {
                            LatLng: [lat, lon],
                            lonLat: [lon, lat],
                            info: {
                                moId: this.moId,
                                time: time || '--',
                                lat: `${lat} °`,
                                lnt: `${lon} °`,
                            },
                        };
                        result.push(param);
                    }
                });
            }
            return result;
        },
        isLastNodeTheSame(newData) {
            let isExist = false;
            let needLine = true;
            let length = this.batteryTrailData.length;
            if (length > 0) {
                const { longitude, latitude, time } = newData;
                let lastData = this.batteryTrailData[length - 1];
                const [lon, lat] = lastData.lonLat;
                let lastNodeTime = lastData.info.time;
                let newLon = parseFloat(longitude);
                let newLat = parseFloat(latitude);
                // 查询的点经纬度与轨迹最后一个点相同，且点的更新时间一致，则不生成新标记；如果时间不一致，则重新生成该标记；否则添加新标记。
                if (newLon === lon && newLat === lat) {
                    needLine = false;
                    if (time === lastNodeTime) {
                        isExist = true;
                    } else {
                        this.batteryTrailData.pop();
                        let markerLength = this.zMapAppMarkers.length;
                        let lastMarker = this.zMapAppMarkers[markerLength - 1];
                        this.zMapApp.MarkerAPI.removeMarkers([lastMarker]);
                        this.zMapAppMarkers.pop();
                    }
                }
            }
            return { isExist, needLine };
        },
        zgisOfflineMapWarning() {
            let mapEngine = this.zMapApp.zmap.mapEngine;
            let isOfflineMap = mapEngine && mapEngine.toLowerCase().includes('offline');
            if (isOfflineMap) {
                this.$message({
                    message: this.$t('tipMessage.geocodeWarningInOfflineMap'),
                    duration: 10000,
                    showClose: true,
                    type: 'warning',
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.form-container {
    position: relative;
    .el-form-item {
        margin-right: 12px;
    }
}
.form-tip {
    color: #595959;
    position: absolute;
    top: 47px;
    left: 36px;
    font-size: 12px;
}
.radio-container {
    position: relative;
}
.radio-tip {
    color: #595959;
    position: absolute;
    top: 34px;
    left: 9px;
    font-size: 12px;
}
.batt-info-content {
    font-size: 13px;
    padding: 8px 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.batt-info-title {
    font-size: 24px;
    padding: 10px 0;
    font-weight: 700;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: #000;
}

.battInfo {
    position: absolute;
    top: 140px;
    left: 25px;
    width: 300px;
    max-height: 350px;
    padding: 20px;
    overflow: auto;
    border-radius: 5px;
    box-shadow: 2px 2px 2px rgb(173, 173, 173), inset 1px 1px 1px gainsboro;
    background-color: white;
}

/* 关闭按钮的样式 */
.close-icon {
    transform: translateY(35%);
}

.close-icon-container:hover {
    background-color: rgb(245, 245, 245);
    z-index: 999;
}
.close-icon-container {
    cursor: pointer;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    font-size: 3.5em;
    color: rgb(160, 159, 159);
    box-shadow: 2px 2px 2px rgb(173, 173, 173), inset 1px 1px 1px gainsboro;
    text-align: center;
    margin: 10px;
    position: fixed;
    right: 10px;
    top: 10px;
}

.googleMapContainer {
    width: 100%;
    height: 100%;
}

.searchPanel {
    position: absolute;
    left: 25px;
    top: 25px;
    z-index: 5;
}
::v-deep .searchPanel .el-form-item__content .el-input__inner,
.el-form-item__content .el-select .el-input__inner {
    width: 147px;
    color: #a4a7b3 !important;
}
::v-deep .searchPanel .el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 190px;
}
::v-deep .lang-en.searchPanel .el-date-editor.el-input {
    width: 205px;
}
::v-deep .searchPanel .el-input--mini .el-input__inner {
    height: 26px;
    line-height: 26px;
}
::v-deep .searchPanel .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 12px;
    margin-right: 12px;
}

::v-deep .searchPanel .el-form--inline .el-button--default {
    padding: 0 4px;
    border-radius: 5px;
}
.searchPanel .el-button--default {
    color: #595959;
    &:hover {
        color: #1993ff;
    }
}
.searchPanel .el-button--default.is-disabled {
    color: #bfbfbf;
    background-color: #e5e5e5;
}

.icon-button {
    margin-left: 10px;
    display: inline-block;
    font-size: 12px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    color: #595959;
    cursor: pointer;
    border-radius: 4px;
    padding: 0 8px;
    line-height: 30px;
    &:hover {
        color: #1993ff;
        border-color: #1993ff;
    }
    .el-icon {
        position: relative;
        top: 2px;
    }
}

:deep().el-radio-group .is-active:not(.is-disabled) .el-radio-button__inner {
    border-color: #1993ff;
    background-color: #e6f7ff;
    color: #404040;
}
html.dark {
    .icon-button {
        background-color: #141414;
        color: #fff;
        &:hover {
            color: #1993ff;
            border: 1px solid transparent;
        }
    }
    .battInfo {
        box-shadow: unset;
        background-color: #141414;
    }
    .batt-info-title {
        color: #d9d9d9;
    }
    .radio-tip {
        color: #a4a7b3;
    }
    .searchPanel .el-button--default {
        color: #e6e6e6;
        background-color: #22242e !important;
        &:hover {
            color: #1993ff;
        }
    }
    .searchPanel .el-button--default.is-disabled {
        color: #474a59;
        background-color: #2a2c38 !important;
    }
    .el-radio-button:not(.is-active) :deep().el-radio-button__inner:hover,
    .el-radio-group .is-active:not(.is-disabled) :deep().el-radio-button__inner {
        background-color: #1e3659 !important;
    }
}
:deep(.zpopover.top .arrow) {
    border-top-color: #fff;
}
:deep(.zpopover.top .arrow:after) {
    border-top-color: #fff;
}
:deep(.zpopover-content) {
    min-width: 150px;
    color: #303133;
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    .battery-content {
        position: relative;
        white-space: nowrap;
        font-size: 12px;
        line-height: 16px;
        text-align: left;
        .position-update-time-label {
            color: #737373;
            margin-bottom: 8px;
        }
        .position-update-time-value {
            color: #404040;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .position-set-button {
            width: fit-content;
            max-width: 150px;
            margin: 0 auto;
            color: #fff;
            background-color: #1993ff;
            padding: 8px 24px;
            border-radius: 4px;
            &:hover {
                cursor: pointer;
                background-color: #0064d6;
            }
        }
        .close-icon {
            position: absolute;
            top: -11px;
            right: -11px;
            font-size: 16px;
            color: #606266;
            cursor: pointer;
            &:hover {
                color: #1993ff;
            }
        }
    }
}
:deep(.ol-viewport .coordinate-display-control) {
    display: none;
}

:deep(.ol-viewport .ol-scale-line.zmap-scale-line-inner) {
    padding-bottom: 8px;
    color: #303133;
    border: #606266 1px solid;
    text-shadow: 1px 1px 1px #cfd3dc;
    box-shadow: 1px 1px 1px #cfd3dc, -1px 0 0 #cfd3dc;
    border-top: none;
}

:deep(.ol-attribution ul) {
    color: #606266;
    text-shadow: none;
}
:deep() .el-radio-button__inner {
    min-width: 100px;
    line-height: 20px;
}
</style>
<style lang="scss">
html.dark {
    .zpopover.top .arrow {
        border-top-color: #161922;
    }
    .zpopover.top .arrow:after {
        border-top-color: #161922;
    }
    .zpopover-content {
        color: #dbdbdd;
        background-color: #161922;
        .battery-content {
            .position-update-time-label {
                color: #a4a7b3;
                margin-bottom: 8px;
            }
            .position-update-time-value {
                color: #d9d9d9;
                font-weight: bold;
                margin-bottom: 16px;
            }
            .close-icon {
                color: #a4a7b3;
                &:hover {
                    color: #1993ff;
                }
            }
        }
    }

    .ol-attribution ul {
        color: #86888c;
    }
}
</style>
