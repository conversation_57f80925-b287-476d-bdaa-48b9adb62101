export function createBlinkCircle(type) {
    const isClusterMarker = type === 'clusterMarker';
    const circleElemWidth = isClusterMarker ? 22 : 20;
    const blinkCircleElem = document.createElement('div');
    blinkCircleElem.style.cssText = 'position: absolute;';

    const createCircleElemDiv = animation => {
        const div = document.createElement('div');
        div.style.cssText = `
            cursor: pointer;
            height: ${circleElemWidth}px;
            width: ${circleElemWidth}px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0);
            transform: scale(0);
            animation: ${animation} 3s;
            animation-iteration-count: infinite;
            border: 1px solid #DC0513;
            pointer-events: none;
            position: absolute;
            z-index: 1001;
        `;
        return div;
    };
    if (isClusterMarker) {
        blinkCircleElem.appendChild(createCircleElemDiv('zgisFirstClusterCircle'));
    } else {
        blinkCircleElem.appendChild(createCircleElemDiv('zgisFirstCircle'));
    }
    blinkCircleElem.appendChild(createCircleElemDiv('zgisSecondCircle'));
    blinkCircleElem.appendChild(createCircleElemDiv('zgisThirdCircle'));
    return blinkCircleElem;
}
const zgisAddAnimateKeyframe = () => {
    const style = document.createElement('style');
    style.innerHTML = `
        @keyframes zgisFirstCircle {
            0% {
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 10px;
                opacity: 0;
            }
        }
        @keyframes zgisFirstClusterCircle {
            0% {
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 20px;
                opacity: 0;
            }
        }
        @keyframes zgisSecondCircle {
            0% {
                transform: translate(-50%, -50%) scale(1);
            }
            40% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 4px;
                opacity: 0.4;
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 10px;
                opacity: 0;
            }
        }
        @keyframes zgisThirdCircle {
            0% {
                transform: translate(-50%, -50%) scale(1);
            }
            80% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 8px;
                opacity: 0.2;
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                border-width: 10px;
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
};
zgisAddAnimateKeyframe();
