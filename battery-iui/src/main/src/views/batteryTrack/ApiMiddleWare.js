/*
 * @Date: 2020-11-21 09:07:01
 * @LastEditors: yan yan
 * @LastEditTime: 2021-02-01 09:24:51
 * @Description: api相关的统一操作
 */

/**
 * api: URL: POST api/monitor-manager/v1/battery-track/search的数据中间件
 * 用于紧急处理接口字段修改的情况,由于进行深拷贝,会消耗性能
 */
export function apiMidBattTrackSearch(sourceData) {
    let result = {};

    result['totalNumbers'] = sourceData['totalNumbers']; // 电池总数
    result['onlineNumbers'] = sourceData['onlineNumbers']; // 在线数
    result['offlineNumbers'] = sourceData['offlineNumbers']; // 离线数
    result['staticNumbers'] = sourceData['staticNumbers']; // 静止数
    result['motionNumbers'] = sourceData['motionNumbers']; // 运动数
    result['fortificationNumbers'] = sourceData['fortificationNumbers']; // 设防数
    result['withdrawNumbers'] = sourceData['withdrawNumbers']; // 撤防数
    result['communicationUnknownNumbers'] = sourceData['communicationUnknownNumbers']; // 未知
    result['motionUnknownNumbers'] = sourceData['motionUnknownNumbers']; // 未知
    result['fortificationUnknownNumbers'] = sourceData['fortificationUnknownNumbers']; // 未知
    result['batteryList'] = [];

    sourceData.batteryList.forEach(d => {
        let temp = {};
        temp['moId'] = d['moId']; // 电池id -> 电池id唯一标识
        temp['batteryName'] = d['batteryName']; // 电池名称
        temp['realGroupName'] = d['realGroupName']; // 区域名称
        temp['motionState'] = d['motionState']; // 运动状态
        temp['fortificationState'] = d['fortificationState']; // 设防状态
        temp['communicationStatus'] = d['communicationStatus']; // 通讯状态
        temp['theftStatus'] = d['theftStatus']; // 偷盗状态
        temp['siteName'] = d['siteName']; // 站点名称
        temp['siteLevel'] = d['siteLevel']; // 站点等级
        temp['ariseTime'] = d['ariseTime'] || '--'; // 更新时间

        // 经纬度由字符串转换为数字
        temp['longitude'] = d['longitude']; // 经度
        temp['latitude'] = d['latitude']; // 纬度

        result['batteryList'].push(temp);
    });

    return result;
}

// 将0/1字段修改为对应的状态名称
export function translateKeyWords(name, value) {
    // 对应关系
    let relationMap = {
        // 运动状态
        motionState: {
            0: 'Static', // 静止
            1: 'Motion', // 运动
            2: 'Unknown', // 未知
        },
        // 设防状态
        fortificationState: {
            1: 'Fortification', // 设防
            0: 'Withdraw', // 撤防
            2: 'Unknown', // 未知
        },
        // 通讯状态
        communicationStatus: {
            0: 'Online', // 通讯正常
            1: 'Offline', // 通讯异常
            2: 'Unknown', // 未知
        },
        // 偷盗状态-位置状态
        theftStatus: {
            1: 'Steal', // 被偷
            0: 'Normal', // 正常
            2: 'Unknown', // 未知
        },
    };

    // 经纬度格式转换
    let location = ['longitude', 'latitude'];

    if (Object.keys(relationMap).indexOf(name) + 1) {
        // 0/1格式转换，传入的是0.0/1.0字符串
        return relationMap[name][parseInt(value)];
    } else if (location.indexOf(name) + 1) {
        // 经纬度格式转换
        if (value === '') {
            return '--';
        }
        return (+value).toFixed(6);
    } else {
        // 默认不做转换,直接传回原字符串
        return value;
    }
}

/**
 * 将度转换成为度分秒
 * @param {Number} value 地理坐标位置
 */
export function formatDegree(value) {
    value = Math.abs(value);
    let v1 = Math.floor(value); // 度
    let v2 = Math.floor((value - v1) * 60); // 分
    let v3 = Math.round(((value - v1) * 3600) % 60); // 秒
    return `${v1}°${v2}'${v3}"`;
}

/**
 * 度分秒转换成为度
 * @param {Number} value 地理坐标位置
 */
export function DegreeConvertBack(value) {
    // let du = value.split("°")[0];
    // let fen = value.split("°")[1].split("'")[0];
    // let miao = value
    //     .split("°")[1]
    //     .split("'")[1]
    //     .split('"')[0];

    // return Math.abs(du) + "." + (Math.abs(fen) / 60 + Math.abs(miao) / 3600);

    return +value.toFixed(6);
}
