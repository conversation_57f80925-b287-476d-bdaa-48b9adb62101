<template>
    <div v-loading="loading" class="standby-div" @click="closePop($event)">
        <div class="set-div uedm-navigation uedm-breadcrumbs title">
            <li>
                <el-link type="primary" :underline="false" @click="backOverview">
                    <el-icon :size="20">
                        <Back />
                    </el-icon>
                </el-link>
                {{ $t('button.set') }}
            </li>
        </div>
        <split-line ref="SplitLine" :view-h="heightView" :left-padding-right="0" @styleControl="splitChange">
            <template v-slot:left>
                <div class="left-content">
                    <set-tree :height="heightView - 16" @nodeClick="handleClick"></set-tree>
                </div>
            </template>
            <template v-slot:right>
                <div class="overview" :style="{ height: height - 25 + 'px', overflow: 'auto' }">
                    <!-- 面包屑（不可跳转） -->
                    <div class="uedm-navigation">
                        <!-- 面包屑（不可跳转） -->
                        <path-breadcrumb :lists="pathNames"></path-breadcrumb>
                    </div>
                    <!-- 节点无权限 -->
                    <div v-if="!isPermission && !loading" :style="{ height: height + 'px' }">
                        <div class="pageBlankTips">
                            <div class="pageBlankTipsContent">
                                <el-icon><el-icon-warning /></el-icon>
                                <div>{{ $t('common.noRight') }}</div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="!loading">
                        <com-set
                            ref="setRef"
                            :default-expanded="defaultExpanded"
                            :node-id="nodeId"
                            :node="node"
                            :switch-power-tab="switchPowerTab"
                            :battery-pack-tab="batteryPackTab"
                            :loading="tabLoading"
                        ></com-set>
                    </div>
                </div>
            </template>
        </split-line>
    </div>
</template>

<script>
import { Warning as ElIconWarning } from '@element-plus/icons-vue';
import { $emit } from '../../utils/gogocodeTransfer';
import SplitLine from '@uedm/uedm-ui/src/components/SplitLineSide.vue';
import HTTP from '@/util/httpService.js';
import Set from './set/Index.vue';
import SitTree from './setTree.vue';
import PathBreadcrumb from '@uedm/uedm-ui/src/components/pathBreadcrumb.vue';

export default {
    components: {
        'split-line': SplitLine,
        'com-set': Set,
        'set-tree': SitTree,
        'path-breadcrumb': PathBreadcrumb,
        ElIconWarning,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            nodeId: '',
            type: 'overview',
            node: {},
            defaultExpanded: [], // 抽屉自动展开
            pathNames: [],
            widthChange: false,
            divChartWidthChange: 0, // 用户拖拽splitLine,图表宽度自适应
            isPermission: true, // 分域
            loading: false,
            switchPowerTab: false,
            batteryPackTab: false,
            tabLoading: false,
        };
    },
    computed: {
        height() {
            return this.$store.getters.getHeight - 35;
        },
        heightView() {
            let h = this.$store.getters.getHeight - 58;
            return h;
        },
    },
    mounted() {},
    methods: {
        closePop(ev) {
            let overview = this.$refs.overview;
            if (overview && overview.$refs.popQuery && !overview.$refs.popQuery.contains(ev.target)) {
                overview.showQuery = false;
            }
        },
        handleClick(node, pathNames, pathIds = []) {
            if (node.id === 'Root') {
                this.getRootPermission();
            }
            this.defaultExpanded = [];
            if (pathIds && pathIds.length > 0) {
                pathIds.forEach(id => {
                    id && this.defaultExpanded.push(id);
                });
            }
            this.pathNames = [];
            if (pathNames && pathNames.length > 0) {
                pathNames.forEach((name, index) => {
                    name && this.pathNames.push({
                        id: pathIds[index],
                        name
                    });
                });
            }
            this.nodeId = node.id;
            this.node = node;
            // this.isPermission = node.authorized;
            this.getTabShow();
        },
        getRootPermission() {
            this.loading = true;
            HTTP.request('getRootPermission', {
                method: 'get',
                urlParam: {
                    id: 'Root',
                },
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0) {
                        this.isPermission = resp.data;
                        if (!resp.data) {
                            return;
                        }
                    }
                },
            });
        },
        getTabShow() {
            this.loading = true;
            this.tabLoading = true;
            HTTP.request('checkStandbySpecialSetTabBackupPower', {
                method: 'get',
                urlParam: {
                    logicGroupId: this.nodeId,
                },
                complete: resp => {
                    this.loading = false;
                    this.tabLoading = false;
                    if (resp.code === 0) {
                        if (resp.data.spResult && resp.data.spResult > 0) {
                            this.switchPowerTab = true;
                        } else {
                            this.switchPowerTab = false;
                            this.$nextTick(() => {
                                if (this.$refs.setRef) {
                                    this.$refs.setRef.activeName = 'batteryPack';
                                }
                            });
                        }
                        if (resp.data.independentNetResult && resp.data.independentNetResult > 0) {
                            this.batteryPackTab = true;
                        } else {
                            this.batteryPackTab = false;
                        }
                    }
                },
                error: () => {
                    this.loading = false;
                },
            });
        },
        splitChange() {
            this.divChartWidthChange += 1;
            this.widthChange = !this.widthChange;
        },
        backOverview() {
            $emit(this, 'changeType', 'overview');
        },
    },
    emits: ['changeType'],
};
</script>

<style lang="scss" scoped>
.right-content {
    padding-top: 24px;
}
.crumb {
    padding-right: 150px;
    display: inline-block;
    .separation {
        display: inline-block;
        padding: 0 8px;
    }
    li {
        font-size: 14px;
        padding-bottom: 8px;
        line-height: 20px;
        &:last-child {
            font-size: 16px;
        }
    }
}
html.dark {
    .crumb {
        li {
            color: #d9d9d9;
        }
    }
}
</style>
