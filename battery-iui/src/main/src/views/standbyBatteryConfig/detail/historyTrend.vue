<template>
    <div>
        <el-form ref="trendForm" class="trend-form" :inline="true">
            <el-form-item :label="$t('standby.fields.grain')">
                <el-select v-model="grain" :disabled="disableChangeGrain" class="grain-select" @change="grainChange">
                    <el-option value="m" :label="$t('datetimePicker.month')"></el-option>
                    <el-option value="d" :label="$t('datetimePicker.day')"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="rangeTime"
                    :shortcuts="pickerOptions && pickerOptions.shortcuts"
                    :disabled-date="pickerOptions && pickerOptions.disabledDate"
                    :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                    :type="dateRangeType"
                    range-separator="-"
                    :value-format="valueFormatType"
                    :format="valueFormatType"
                    :clearable="false"
                    :start-placeholder="
                        $t('solarPower.dateTime.dateStart', {
                            name: $t('solarPower.dateTime.date'),
                        })
                    "
                    :end-placeholder="
                        $t('solarPower.dateTime.dateEnd', {
                            name: $t('solarPower.dateTime.date'),
                        })
                    "
                    @change="dataChange"
                    @blur="blurHandler"
                    @focus="focusHandler"
                ></el-date-picker>
            </el-form-item>
        </el-form>
        <div class="history-trend">
            <div ref="chart" v-loading="loading" class="chart"></div>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import HTTP from '@/util/httpService.js';
import { shallowRef } from 'vue';
import { CHART_COLORS, CHART_HANDLEICON, getLineNodata } from '@uedm/uedm-ui/src/util/constants.js';
const handleIcon =
    'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
export default {
    props: ['id'],
    data() {
        return {
            grain: 'm',
            rangeTime: [],
            dateRangeType: 'monthrange',
            valueFormatType: 'YYYY-MM',
            loading: false,
            trendEchartsData: [],
            xAxis: [],
            seriesData: [],
            myChart: null,

            disableChangeGrain: false,
            selectedDate: '',
            pickerOptions: {
                disabledDate: time => {
                    let flag = false;
                    if (this.selectedDate && this.grain === 'd') {
                        // 最多选择30天
                        let diff = dayjs(this.selectedDate).diff(dayjs(time), 'day');
                        flag = diff >= 30 || diff <= -30;
                    } else if (this.selectedDate && this.grain === 'm') {
                        // 最多选择12个月
                        let diff = dayjs(this.selectedDate).diff(dayjs(time), 'month');
                        flag = diff >= 11 || diff <= -12;
                    }
                    return flag || dayjs(time) > dayjs(); // 不超过日期限制，且不能是未来时间
                },
                onPick: ({ minDate, maxDate }) => {
                    this.selectedDate = minDate;
                    if (maxDate) {
                        this.selectedDate = '';
                    }
                },
            },
        };
    },
    computed: {
        skins() {
            let val = null;
            if (this.$store.getters.getIsDark) {
                val = 'dark';
            }
            return val;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        barZoomShow() {
            return this.seriesData.length > 15;
        },
    },
    mounted() {
        this.getTimeRange('m');
        this.getTrendData();
        if (this.$refs.chart) {
            this.initCharts();
        }
    },
    methods: {
        blurHandler() {
            this.disableChangeGrain = false;
        },
        focusHandler() {
            this.disableChangeGrain = true;
        },
        getOptions() {
            let _this = this;
            const colorType = this.isDark ? 'dark' : 'default';
            const legendColor = {
                normal: '#7cd180',
                deficiency: '#ffc850',
                unEvaluate: '#bfbfbf',
            };
            let dataZoom = {
                type: 'slider',
                xAxisIndex: 0,
                filterMode: 'empty',
                bottom: 10,
                height: 24,
                showDetail: false,
                zoomLock: false,
            };
            let options = {
                backgroundColor: 'transparent',
                legend: {
                    show: true,
                    top: '0',
                    icon: 'circle',
                    itemGap: 21,
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        fontSize: 12,
                        color: this.isDark ? '#d9d9d9' : '#595959',
                    },
                    selectedMode: false, // 取消图例上点击事件
                },
                grid: {
                    containLabel: true,
                    top: '20%',
                    left: '4%',
                    right: '4%',
                    bottom: (this.barZoomShow && '25') || (this.seriesData.length && '2') || '20',
                },
                xAxis: {
                    type: 'category',
                    data: this.xAxis,
                    axisTick: {
                        alignWithLabel: true,
                    },
                    axisLine: {
                        lineStyle: {
                            color: CHART_COLORS[colorType].line,
                        },
                    },
                    axisLabel: {
                        color: CHART_COLORS[colorType].label,
                    },
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: CHART_COLORS[colorType].background,
                    borderColor: CHART_COLORS[colorType].border,
                    textStyle: {
                        color: CHART_COLORS[colorType].color,
                        align: 'left',
                    },
                    formatter: val => {
                        let param = {};
                        if (val && val.length) {
                            param = val[0];
                            let color = legendColor[param.data.id];
                            let width = _this.$i18n.locale === 'en-US' ? 350 : 270;
                            let message = `<div style="width: ${width}px">`;
                            // 独立组网情况下不展示负载功率这项数据 battpackTag为true表示为独立组网
                            let powerShowStyle = param.data.battpackTag ? 'none' : 'flex';
                            message += `<div style="display: flex">
                                    <span style="display:inline-block">${param.name}</span>
                                    <div style="margin-left: 24px">
                                        <span style="display:inline-block;margin-right:4px;border-radius:8px;width:12px;height:12px;background-color:${color};"></span>
                                        <span style="display:inline-block">${param.data.value}</span>
                                    </div>
                                </div>`;
                            // 备电设计时长(h)
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.designDuration')}</span>
                                    <span>
                                        ${param.data.thresholdDuration || '--'}
                                        <span style="display: ${param.data.thresholdDuration || 'none'}; margin-left: 2px">h</span>
                                    </span>
                                </div>`;
                            // 备电时长(h)
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.evaluatedDuration')}</span>
                                    <span>
                                        ${param.data.backupPowerDuration || '--'}
                                        <span style="display: ${param.data.backupPowerDuration || 'none'}; margin-left: 2px">h</span>
                                    </span>
                                </div>`;
                            // 额定容量
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.ratedCapacity')}</span>
                                    <span>
                                        ${param.data.ratedCapacity || '--'}
                                        <span style="display: ${param.data.ratedCapacity || 'none'}; margin-left: 2px">Ah</span>
                                    </span>
                                </div>`;
                            // 满充容量
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.fullChargeCapacity')}</span>
                                    <span>
                                        ${param.data.fullChargeCapacity || '--'}
                                        <span style="display: ${param.data.fullChargeCapacity || 'none'}; margin-left: 2px">Ah</span>
                                    </span>
                                </div>`;
                            // 平均放电电流
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.averageDischargeCurrent')}</span>
                                    <span>
                                        ${param.data.averageDischargeCurrent || '--'}
                                        <span style="display: ${param.data.averageDischargeCurrent || 'none'}; margin-left: 2px">A</span>
                                    </span>
                                </div>`;
                            // 负载总功率
                            message += `<div style="display: ${powerShowStyle}; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.totalLoadPower')}</span>
                                    <span>
                                        ${param.data.totalLoadPower || '--'}
                                        <span style="display: ${param.data.totalLoadPower || 'none'}; margin-left: 2px">kW</span>
                                    </span>
                                </div>`;
                            // 交流停电告警时间
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.acPowerOutageAlarmTime')}</span>
                                    <span>${param.data.acStopAlarmTime || '--'}</span>
                                </div>`;
                            // 下电告警时间
                            message += `<div style="display: flex; justify-content: space-between">
                                    <span style="display:inline-block">${_this.$t('standby.fields.powerDownAlarmTime')}</span>
                                    <span>${param.data.powerOffAlarmTime || '--'}</span>
                                </div>`;
                            message += `</div>`;
                            return message;
                        }
                    },
                },
                yAxis: {
                    type: 'category',
                    axisLine: { show: true },
                    data: [
                        '',
                        this.$t('standby.fields.cannotEvaluated'),
                        this.$t('standby.fields.shortage'),
                        this.$t('standby.fields.normal'),
                    ],
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        align: 'right',
                        color: CHART_COLORS[colorType].label,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: CHART_COLORS[colorType].line,
                        },
                    },
                    splitLine: { show: true },
                    boundaryGap: false,
                    name: this.$t('standby.fields.status'),
                    nameTextStyle: {
                        align: 'right',
                        padding: [0, 8, 8, 0],
                        color: CHART_COLORS[colorType].label,
                    },
                },
                dataZoom: this.barZoomShow ? dataZoom : null,
                series: [
                    {
                        name: this.$t('standby.fields.normal'),
                        data: [],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 12,
                        itemStyle: {
                            color: legendColor['normal'],
                        },
                    },
                    {
                        name: this.$t('standby.fields.shortage'),
                        data: [],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 12,
                        itemStyle: {
                            color: legendColor['deficiency'],
                        },
                    },
                    {
                        name: this.$t('standby.fields.cannotEvaluated'),
                        data: [],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 12,
                        itemStyle: {
                            color: legendColor['unEvaluate'],
                        },
                    },
                    {
                        data: this.seriesData,
                        type: 'line',
                        lineStyle: {
                            color: CHART_COLORS[colorType].line,
                            width: 1,
                        },
                        symbol: 'circle',
                        symbolSize: 16,
                        itemStyle: {
                            color: function (params) {
                                return legendColor[params.data.id] || '';
                            },
                        },
                    },
                ],
            };
            if (!this.seriesData.length) {
                options.graphic = {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    silent: true,
                    invisible: false,
                    style: {
                        fill: CHART_COLORS[colorType].noData,
                        fontWeight: 'bold',
                        text: this.$t('common.noData'),
                        fontFamily: 'Microsoft Yahei',
                        fontSize: '25px',
                    },
                };
                options.legend.show = false;
                options.xAxis.name = this.$t('standby.fields.date');
                options.xAxis.nameLocation = 'start';
                options.yAxis.splitLine.show = false;
            }
            return options;
        },
        initCharts() {
            this.myChart = shallowRef(
                echarts.init(this.$refs.chart, this.isDark ? 'dark' : 'light', {
                    height: '235px',
                    width: '958px'
                })
            );
            this.myChart.setOption(this.getOptions());
        },
        handleData() {
            this.xAxis = [];
            this.seriesData = [];
            this.trendEchartsData.length &&
                this.trendEchartsData.forEach(item => {
                    this.xAxis.push(item.evalTime);
                    this.seriesData.push({
                        value: item.status.name,
                        id: item.status.id,
                        data: item.status,
                        backupPowerDuration: item.backupPowerDuration,
                        thresholdDuration: item.thresholdDuration,
                        ratedCapacity: item.ratedCapacity,
                        fullChargeCapacity: item.fullChargeCapacity,
                        averageDischargeCurrent: item.averageDischargeCurrent,
                        totalLoadPower: item.totalLoadPower,
                        acStopAlarmTime: item.acStopAlarmTime,
                        powerOffAlarmTime: item.powerOffAlarmTime,
                        battpackTag: item.battpackTag
                    });
                });

            this.updateChart();
        },
        updateChart() {
            if (this.myChart) {
                this.myChart.clear();
                this.myChart.setOption(this.getOptions());
            }
        },
        grainChange(value) {
            this.selectedDate = '';
            this.getTimeRange(value);
            this.getTrendData();
        },
        dataChange() {
            this.getTrendData();
            this.disableChangeGrain = false;
        },
        getTimeRange(value) {
            let startTime = '';
            let endTime = '';
            if (value !== '') {
                if (value === 'd') {
                    // 最近7天，含今天
                    let now = dayjs();
                    startTime = now.subtract(6, 'day').format('YYYY-MM-DD');
                    endTime = now.subtract(0, 'day').format('YYYY-MM-DD');
                    this.dateRangeType = 'daterange';
                    this.valueFormatType = 'YYYY-MM-DD';
                } else if (value === 'm') {
                    // 最近12个月，含本月
                    let now = dayjs();
                    startTime = now.subtract(11, 'month').format('YYYY-MM');
                    endTime = now.format('YYYY-MM');
                    this.dateRangeType = 'monthrange';
                    this.valueFormatType = 'YYYY-MM';
                }
                this.rangeTime = [String(startTime), String(endTime)];
            } else {
                this.rangeTime = [];
            }
        },
        getTrendData() {
            let evalTimeBegin = '';
            let evalTimeEnd = '';
            if (this.rangeTime.length > 0) {
                evalTimeBegin = this.rangeTime[0];
                evalTimeEnd = this.rangeTime[1] || '';
            }
            this.loading = true;
            HTTP.request('getEvalTrend', {
                method: 'post',
                data: {
                    grain: this.grain,
                    id: this.id,
                    evalTimeBegin,
                    evalTimeEnd,
                },
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0 && resp.data) {
                        this.trendEchartsData = resp.data;
                        this.handleData();
                    }
                },
                error: () => {
                    this.loading = false;
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.trend-form {
    text-align: left;

    .grain-select :deep(.el-input) .el-input__inner {
        width: 100px;
    }
    :deep(.el-form-item) {
        margin-right: 16px;
        margin-bottom: 8px;

        &:last-child {
            margin-right: 0;
        }
    }
}
.history-trend {
    padding: 10px 0;
}
</style>
