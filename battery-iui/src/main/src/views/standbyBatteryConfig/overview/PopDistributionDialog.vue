<template>
    <el-dialog
        :title="title"
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="1000px"
        @close="handleClose"
    >
        <div v-loading="loadingList && !tableHeader.length" style="padding: 0 0 4px">
            <div class="toolBar">
                <div class="rightBar floatRight">
                    <!-- 导出 -->
                    <el-tooltip
                        v-if="rights['battery.b.standby.manager.overview.export'] && tableHeader.length > 0"
                        effect="dark"
                        :content="$t('button.export')"
                        placement="bottom"
                    >
                        <span class="icon-button plx-ico-export-16 borderColor" @click="handleExport"></span>
                    </el-tooltip>
                </div>
                <div class="breadcrumb">{{ $t('standby.fields.position') }}: {{ crumbNames }}</div>
            </div>
            <div v-if="tableHeader.length > 0">
                <el-table
                    ref="table"
                    v-loading="loadingList"
                    :data="tableData"
                    style="width: 100%"
                    row-key="id"
                    stripe
                    border
                    max-height="500"
                    @sort-change="sortChange"
                >
                    <el-table-column
                        v-for="header in tableHeader"
                        :key="header.id"
                        :prop="header.id"
                        :label="header.name"
                        :min-width="$i18n.locale == 'zh-CN' ? '130px' : '130px'"
                        show-overflow-tooltip
                        :sortable="header.sortable ? 'custom' : false"
                    >
                        <template v-slot:header>
                            <span v-if="header.unit">{{ header.name + '(' + header.unit + ')' }}</span>
                            <span v-else>{{ header.name }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="pageInfo.pageNo"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[5, 10, 20, 50]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                ></el-pagination>
            </div>
            <div v-else class="no-data border" :style="{ height: 236 + 'px', 'line-height': 236 + 'px' }">
                <span>{{ $t('placeholder.noData') }}</span>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService';
import axios from 'axios';
export default {
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        popDistribution: {
            type: Object,
            default: function () {},
        },
        crumbNames: {
            type: String,
            default: '',
        },
        dims: {
            type: Array,
            default: () => {
                return [];
            },
        },
    },
    components: {},
    data() {
        return {
            loadingList: false,
            tableHeader: [
                {
                    id: 'position',
                    name: this.$t('standby.fields.name'),
                    sortable: false,
                },
                {
                    id: 'normal',
                    name: this.$t('standby.fields.normal'),
                    sortable: true,
                },
                {
                    id: 'deficiency',
                    name: this.$t('standby.fields.shortage'),
                    sortable: true,
                },
                {
                    id: 'unEvaluate',
                    name: this.$t('standby.fields.cannotEvaluated'),
                    sortable: true,
                },
            ],
            tableData: [],
            pageInfo: {
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            order: '',
            sort: '',
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                $emit(this, 'update:visible', value);
            },
        },
        title() {
            let t = this.$t('standby.title.statusDistribution');
            return t;
        },
    },
    created() {
        this.getTableData();
    },
    mounted() {},
    methods: {
        getTableData() {
            this.loadingList = true;
            let queryParameter = Object.assign({}, this.popDistribution, {
                dims: this.dims,
            });
            queryParameter.order = this.order;
            queryParameter.sort = this.sort;
            queryParameter.pageNo = this.pageInfo.pageNo;
            queryParameter.pageSize = this.pageInfo.pageSize;
            let page = this.pageInfo.pageSize + '_' + this.pageInfo.pageNo + '_' + this.order + '_' + this.sort;
            HTTP.request('getDistributionCharts', {
                method: 'post',
                data: queryParameter,
                complete: resp => {
                    if (
                        page !==
                        this.pageInfo.pageSize + '_' + this.pageInfo.pageNo + '_' + this.order + '_' + this.sort
                    ) {
                        return;
                    }
                    this.loadingList = false;
                    this.tableData = [];
                    this.pageInfo.total = 0;
                    let id = this.dims[0];
                    if (resp.code === 0 && resp.data[id] && resp.data[id].num && resp.data[id].statistics.length) {
                        this.pageInfo.total = resp.data[id].num;
                        this.tableData = resp.data[id].statistics;
                    }
                },
                error: () => {
                    this.loadingList = false;
                    this.tableData = [];
                    this.pageInfo.total = 0;
                },
            });
        },
        handleSizeChange() {
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        handleCurrentChange() {
            this.getTableData();
        },
        sortChange(p) {
            this.order = p.prop;
            let order = p.order;
            if (order === 'ascending') {
                this.sort = 'asc';
            } else if (order === 'descending') {
                this.sort = 'desc';
            } else {
                this.sort = '';
                this.order = '';
            }
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        handleExport() {
            let param = Object.assign({}, this.popDistribution, { dims: this.dims });
            param.position = this.crumbNames;
            param.order = this.order;
            param.sort = this.sort;
            const DOWNLOAD_URL = ' /api/battery-manager/v1/backup-power-overview/in-decrease-distribution/export';
            const forgerydefense = window.forgerydefense || '';
            const languageOption =
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) || '';
            /* Started by AICoder, pid:f18bdeec6ccac70147e00a8cc052cd0e5018781c */
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            /* Ended by AICoder, pid:f18bdeec6ccac70147e00a8cc052cd0e5018781c */
            axios
                .post(url, param, config)
                .then(res => {
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                this.$message.error(result.message);
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        let blob = new Blob([res.data]);
                        let url = window.URL.createObjectURL(blob); // 创建下载的链接
                        let link = document.createElement('a');
                        let fileName = '';
                        if (res.headers['content-disposition']) {
                            let contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                })
                .catch(e => {
                    console.log(e);
                    this.$message.warning(this.$t('batteryView.exportFailed'));
                });
        },
        handleClose() {
            $emit(this, 'close');
        },
    },
    emits: ['update:visible', 'close'],
};
</script>

<style lang="scss" scoped>
.toolBar {
    margin-bottom: 8px;

    .floatRight {
        float: right;
    }

    .icon-button {
        display: inline-block;
        font-size: 14px;
        width: 32px;
        height: 32px;
        box-sizing: border-box;
        padding: 8px;
        border: 1px solid #d9d9d9;
        color: #595959;
        cursor: pointer;
        border-radius: 4px;

        &:hover {
            border: 1px solid #1993ff;
            color: #1993ff;
        }
    }
    .item + .item {
        margin-left: 8px;
    }
}
.breadcrumb {
    display: inline-block;
    color: #595959;
    font-size: 14px;
    line-height: 22px;
    padding-top: 10px;
    width: calc(100% - 40px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
}

html.dark {
    .toolBar .icon-button {
        border-color: #474a59;
        color: #a4a7b3;

        &:hover {
            border: 1px solid #1993ff;
            color: #1993ff;
        }
    }
    .breadcrumb {
        color: #a4a7b3;
    }
}
</style>
