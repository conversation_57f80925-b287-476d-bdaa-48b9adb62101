<template>
    <div :id="itemId" ref="chart" v-loading="chartLoading" class="chart comparison-charts"></div>
</template>

<script>
import * as echarts from 'echarts';
import { shallowRef } from 'vue';
import { CHART_COLORS, CHART_HANDLEICON, getLineNodata } from '@uedm/uedm-ui/src/util/constants.js';
export default {
    props: {
        chartData: {
            type: Array,
            default: function () {
                return [];
            },
        },
        height: {
            type: Number,
            default: 420,
        },
        winResize: {
            type: Boolean,
            default: false,
        },
        itemId: {
            type: String,
            default: '',
        },
        dimType: {
            type: String,
            default: '',
        },
        barZoom: {
            type: Boolean,
            default: false,
        },
        defaultTitle: {
            type: String,
            default: '',
        },
        defaultXname: {
            type: String,
            default: '',
        },
        defaultYname: {
            type: String,
            default: '',
        },
        deviceName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            chartLoading: false,
            myChart: null,
            title: '',
            xAxisData: [],
            series: [],
            legend: [],
            xName: '',
            yName: '',
        };
    },
    created() {},
    mounted() {
        if (this.$refs.chart) {
            this.initCharts();
            this.$nextTick(() => {
                this.myChart.resize();
            });
        }
    },
    watch: {
        winResize() {
            if (this.myChart) {
                this.$nextTick(() => {
                    this.myChart.resize();
                });
            }
        },
        winTopResize() {
            if (this.myChart) {
                this.$nextTick(() => {
                    this.myChart.resize();
                });
            }
        },
        chartData: {
            deep: true,

            handler(val) {
                this.handleData(val);
            },

            immediate: true,
        },
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        winTopResize() {
            return this.$store.getters.getResize;
        },
    },
    methods: {
        initCharts() {
            this.myChart = shallowRef(
                echarts.init(this.$refs.chart, this.isDark ? 'dark' : 'light', {
                    height: this.height + 'px',
                })
            );
            this.myChart.setOption(this.getOptions());
        },
        getOptions() {
            const handleIcon =
                'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
            const colorType = this.isDark ? 'dark' : 'default';
            let options = {};
            if (!this.xAxisData || !this.xAxisData.length) {
                options = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '50px' : '32px',
                        right: '4%',
                        bottom: '22',
                        containLabel: true,
                    },
                    title: {
                        text: this.title,
                        x: 'left',
                        textStyle: {
                            color: CHART_COLORS[colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    xAxis: {
                        name: this.xName,
                        nameLocation: 'start',
                        nameTextStyle: {
                            align: 'center',
                            verticalAlign: 'top',
                            padding: [10, 0, 0, 0],
                            color: CHART_COLORS[colorType].label
                        },
                        axisLine: {
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                        axisLabel: {
                            color: CHART_COLORS[colorType].label,
                        },
                        type: 'category',
                        data: [],
                    },
                    yAxis: {
                        type: 'value',
                        name: this.yName,
                        nameTextStyle: {
                            align: 'right',
                            color: CHART_COLORS[colorType].label
                        },
                        show: true,
                        axisLabel: {
                            color: CHART_COLORS[colorType].label,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                    },
                    series: [
                        {
                            type: 'bar',
                            data: [],
                        },
                    ],
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'middle',
                        silent: true,
                        invisible: false, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: CHART_COLORS[colorType].noData,
                            fontWeight: 'bold',
                            text: this.$t('common.noData'),
                            fontFamily: 'Microsoft Yahei',
                            fontSize: '25px',
                        },
                    },
                };
            } else {
                options = {
                    title: {
                        text: this.title,
                        x: 'left',
                        textStyle: {
                            color: CHART_COLORS[colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '50px' : '32px',
                        right: '16px',
                        bottom: this.barZoom ? '35' : '2',
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: CHART_COLORS[colorType].background,
                        borderColor: CHART_COLORS[colorType].border,
                        textStyle: { color: CHART_COLORS[colorType].color },
                        formatter: val => {
                            let preData = {};
                            let currentData = {};
                            let preNumVal = '';
                            let currentNumVal = '';
                            let changeNumVal = '';
                            if (val && val.length) {
                                preData = Object.assign(preData, val[0]);
                                currentData = Object.assign(currentData, val[1]);
                            }
                            let preNum = parseInt(preData.value);
                            if (preNum || preNum === 0) {
                                preNumVal = preNum;
                            } else {
                                preNumVal = '--';
                            }
                            let currentNum = null;
                            if (currentData.data) {
                                currentNum = parseInt(currentData.data.realValue);
                            }
                            if (currentNum || currentNum === 0) {
                                currentNumVal = currentNum;
                            } else {
                                currentNumVal = '--';
                            }
                            let changeNum = null;
                            if (currentData.data) {
                                changeNum = parseInt(currentData.data.changeNumber);
                            }
                            changeNumVal =
                                changeNum || changeNum === 0 ? (changeNum > 0 ? '+' + changeNum : changeNum) : '--';
                            let status = currentData.axisValueLabel;
                            let width = this.$i18n.locale === 'en-US' ? 160 : 100;
                            let message = `<div>${status}</div>
                            <div>
                                <span style="display:inline-block;margin-right:4px;border-radius:8px;width:8px;height:8px;background-color:rgba(0, 0, 0, 0);border: 1px dashed #10a3c5"></span>
                                <span style="width: ${width}px;display:inline-block">${preData.seriesName}</span>
                                ${preNumVal}
                            </div>
                            <div>
                                ${currentData.marker}
                                <span style="width: ${width}px;display:inline-block">${currentData.seriesName}</span>
                                ${currentNumVal}
                            </div>
                            <div>
                                <span style="width: ${width}px;display:inline-block;padding-left:19px">${this.$t(
    'standby.fields.increaseOrDecrease'
)}</span>
                                ${changeNumVal}
                            </div>`;
                            return message;
                        },
                    },
                    backgroundColor: 'transparent',
                    xAxis: {
                        type: 'category',
                        data: this.xAxisData,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                        axisTick: {
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            interval: this.xAxisData.length > 10 ? 'auto' : 0,
                            hideOverlap: true,
                            width: 100,
                            overflow: 'truncate',
                            color: CHART_COLORS[colorType].label,
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: this.yName,
                        nameTextStyle: {
                            align: 'right',
                            color: CHART_COLORS[colorType].label
                        },
                        minInterval: 1,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                        axisLabel: {
                            color: CHART_COLORS[colorType].label,
                        },
                    },
                    legend: {
                        type: 'scroll',
                        orient: 'horizontal',
                        top: '6%',
                        itemWidth: 12,
                        itemHeight: 12,
                        itemGap: 20,
                        selectedMode: false,
                        textStyle: {
                            width: '150',
                            overflow: 'breakAll',
                        },
                        data: this.legend,
                    },
                    dataZoom: [],
                    series: this.series,
                };
                if (this.barZoom) {
                    options.dataZoom = [
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            filterMode: 'empty',
                            bottom: 10,
                            height: 24,
                            showDetail: false,
                            zoomLock: false,
                        },
                    ];
                }
            }
            return options;
        },
        updateChart() {
            if (this.myChart) {
                this.myChart.clear();
                this.myChart.setOption(this.getOptions());
            }
        },
        handleData(val) {
            this.xAxisData = [];
            this.xName = this.defaultXname;
            this.yName = this.defaultYname;
            let seriesLastData = [];
            let seriesCurrentData = [];
            this.title =
                this.deviceName +
                (this.$i18n.locale === 'en-US' ? ' ' : '') +
                this.$t('standby.fields.statusIncreaseOrDecrease');
            if (val && val.length > 0) {
                val.forEach(item => {
                    let result = parseInt(item.changeNumber) || 0;
                    this.xAxisData.push(item.name);
                    seriesLastData.push({
                        name: item.name,
                        value: item.preNumber,
                        changeNumber: item.changeNumber,
                        diff: result,
                        itemStyle: {
                            color: 'rgba(0, 0, 0, 0)',
                            borderColor: '#109ebf',
                            borderType: 'dashed',
                            borderWidth: item.preNumber > 0 ? 1 : 0,
                        },
                    });
                    seriesCurrentData.push({
                        name: item.name,
                        value: item.currentNumber || 0,
                        realValue: item.currentNumber,
                        changeNumber: item.changeNumber,
                        diff: result,
                        label: {
                            show: item.changeNumber || item.changeNumber == 0,
                            position: 'top',
                            rich: {
                                a: {
                                    color: '#76D63E',
                                    lineHeight: 10,
                                },
                                b: {
                                    color: '#F56C6C',
                                    lineHeight: 10,
                                },
                            },
                            formatter: val => {
                                let span = '';
                                if (val.data.diff > 0) {
                                    span = '{a| ↑}';
                                } else if (val.data.diff < 0) {
                                    span = '{b| ↓}';
                                }
                                return Math.abs(val.data.diff) + span;
                            },
                        },
                        itemStyle: {
                            color: '#109ebf',
                        },
                    });
                });
            }
            this.legend = [
                {
                    name: this.$t('standby.fields.lastMonth'),
                    itemStyle: {
                        color: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        borderColor: '#109ebf',
                        borderType: 'dashed',
                    },
                },
                {
                    name: this.$t('standby.fields.currentMonth'),
                    itemStyle: {
                        color: '#109ebf',
                    },
                },
            ];
            this.series = [
                {
                    name: this.$t('standby.fields.lastMonth'),
                    type: 'bar',
                    barMaxWidth: '40',
                    data: seriesLastData,
                },
                {
                    name: this.$t('standby.fields.currentMonth'),
                    type: 'bar',
                    barMaxWidth: '40',
                    data: seriesCurrentData,
                },
            ];
            this.updateChart();
        },
    },
};
</script>
