export default {
    data() {
        return {
            originlist: [],
            list: [],
            rowKey: 'oid',
            fields: [],
            currentPageSize: this.pageSize || 10,
            pageNo: 1,
            loading: false,
            showTree: false,

            checkedNodesName: '',
            checkedIds: [],
        };
    },
    created() {},
    mounted() {},
    computed: {
        columnList() {
            return this.fields.filter(item => {
                return item.hasOwnProperty('listShowOrder');
            });
        },
    },
    watch: {},
    methods: {
        keepTwoDecimal_filter(value, unit) {
            let u = unit || '';
            let result = parseFloat(value);
            if (isNaN(result)) {
                return value;
            }
            result = Math.round(value * 100) / 100;
            return result + u;
        },
        handleSizeChange(currentPageSize) {
            this.pageNo = 1;
            this.currentPageSize = currentPageSize;
        },
        handleCurrentChange(pageNo) {
            this.pageNo = pageNo;
        },
        expandTreeArea() {
            this.showTree = !this.showTree;
        },
        handleClickOutSide() {
            if (this.showTree && event.target.id !== 'areaInput') {
                this.showTree = false;
            }
        },
        getTreeCheckNode(ids, name) {
            this.checkedIds = ids;
            this.checkedNodesName = name;
        },
        keepTwo(value) {
            let result = parseFloat(value);
            if (isNaN(result)) {
                return value;
            }
            result = Math.round(value * 100) / 100;
            return result;
        },
    },
};
