/* Started by AICoder, pid:m9b6cua3b7dd553143b10a65d0821d90e628d2e4 */
<template>
    <div>
        <split-line ref="SplitLine" :view-h="height" :padding-right="0">
            <template #left>
                <div class="left-content" style="padding-top: 16px">
                    <left-tree :height="height - 16" @node-click="handleClick"></left-tree>
                </div>
            </template>
            <template #right>
                <div class="right-content">
                    <div class="uedm-navigation">
                        <path-breadcrumb :lists="pathNames"></path-breadcrumb>
                    </div>
                    <com-list
                        v-if="nodeId"
                        v-show="nodeId && isAuthorized"
                        :node-id="nodeId"
                        pv-types="pv"
                        :is-authorized="isAuthorized"
                        :height="height - 53"
                    ></com-list>
                </div>
                <div v-if="!nodeId" class="pageBlankTips" :style="{ height: `${height - 53}px` }">
                    <div class="pageBlankTipsContent">
                        <i class="el-icon-warning"></i>
                        <div>{{ $t('common.noData') }}</div>
                    </div>
                </div>
                <div
                    v-if="nodeId && !isAuthorized"
                    :style="{ height: `${height}px` }"
                >
                    <no-permission :height="height - 53"></no-permission>
                </div>
            </template>
        </split-line>
    </div>
</template>

<script>
import PathBreadcrumb from '@uedm/uedm-ui/src/components/pathBreadcrumb.vue';
import SplitLine from '@uedm/uedm-ui/src/components/SplitLineSide.vue';
import SolarMonitorTree from '../solarOverallMonitor/solarMonitorTree.vue';
import List from './list.vue';
import NoPermission from '@/components/noPermission.vue';
export default {
    components: {
        'com-list': List,
        'split-line': SplitLine,
        'left-tree': SolarMonitorTree,
        'path-breadcrumb': PathBreadcrumb,
        'no-permission': NoPermission,
    },
    data() {
        return {
            tabs: [
                { name: this.$t('solarPower.tab.PVString'), value: 'pv' },
                { name: this.$t('solarPower.tab.spcu'), value: 'pvarray' },
                { name: this.$t('solarPower.tab.spu'), value: 'pvmodule' },
            ],
            type: 'pv',
            nodeId: '',
            pathNames: [],
            expandedNodeIds: [],
            isAuthorized: true,
        };
    },
    created() {},
    mounted() {},
    computed: {
        height() {
            return this.$store.getters.getHeight;
        },
    },
    methods: {
        showView: function (childVal) {
            this.currentView = 'com-' + childVal.type; // 动态地改变currentView的值来改变组件挂载。
        },
        handleClick(node, pathNames, pathIds = []) {
            this.pathNames = [];
            if (pathNames && pathNames.length > 0) {
                pathNames.forEach((name, index) => {
                    this.pathNames.push({
                        id: pathIds[index],
                        name,
                    });
                });
            }
            this.expandedNodeIds = [...pathIds, 'Root'];
            this.$nextTick(() => {
                this.nodeId = node.id;
            });
            this.isAuthorized = node.authorizationStatus === 2;
        },
    },
};
</script>
/* Ended by AICoder, pid:m9b6cua3b7dd553143b10a65d0821d90e628d2e4 */
