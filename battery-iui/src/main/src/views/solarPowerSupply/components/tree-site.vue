<template>
    <div class="treeWrap" :style="{ maxHeight: maxHeight + 'px' }">
        <el-tree
            ref="tree"
            :props="treeProp"
            node-key="id"
            :expand-on-click-node="false"
            :load="loadNode"
            lazy
            show-checkbox
            @check="onTreeSelectCheck"
        ></el-tree>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
export default {
    props: {
        maxHeight: {
            type: Number,
            default: 300,
        },
    },
    data() {
        return {
            treeProp: {
                label: 'name',
                children: 'children',
                isLeaf: 'isLeaf',
            },
            checkedNodes: {}, // 树节点选中值
            data: [],
        };
    },
    created() {},
    mounted() {},
    computed: {},
    methods: {
        loadNode(node, resolve) {
            let id = this.id || '';
            if (node.data) {
                id = node.data.id;
            }
            this.getChild(id, d => {
                let data = d.data;
                // 过滤掉监控对象类型节点。
                data = data.filter(item => {
                    return item.resourceType !== 'MonitorObject';
                });
                resolve(data);
            });
        },
        getChild(id, callback) {
            let flag = true;
            HTTP.request('/api/configuration/v1/tree/get-child', {
                method: 'get',
                urlParam: {
                    id,
                    flag,
                },
                complete: res => {
                    if (res.code === 0) {
                        let data = res.data;
                        callback && callback(res);
                    }
                },
            });
        },
        onTreeSelectCheck(data, status) {
            this.getCheckedNodes();
        },
        getCheckedNodes() {
            // 获取树节点选中值
            this.checkedNodes = [];
            let tmpValueArr = [];
            let loopNodeChecked = node => {
                if (node.checked) {
                    if (node.data) {
                        tmpValueArr.push(node.data.id);
                        this.checkedNodes.push({
                            label: node.data.name,
                            value: node.data.id,
                        });
                    }
                } else {
                    let childNodes = node.childNodes;
                    if (childNodes && childNodes.length > 0) {
                        childNodes.forEach(chiild => {
                            loopNodeChecked(chiild);
                        });
                    }
                }
            };
            loopNodeChecked(this.$refs.tree.root);
            $emit(this, 'selected', this.checkedNodes, tmpValueArr);
        },

        clearHandler() {
            // 清空已选
            this.$refs.tree.setCheckedKeys([]);
            this.checkedNodes = [];
            $emit(this, 'selected', [], [], []);
        },
        removeTag(id) {
            // 删除指定一个节点
            let setChild = node => {
                let id = node.data.id;
                this.$refs.tree.setChecked(id, false);
                if (node.childNodes) {
                    node.childNodes.forEach(child => {
                        setChild(child);
                    });
                }
            };
            setChild(this.$refs.tree.getNode(id));
            this.getCheckedNodes();
        },
        setChecked(d) {
            this.$refs.tree.setCheckedNodes(d);
        },
    },
    emits: ['selected'],
};
</script>

<style lang="scss" scoped>
.treeWrap {
    overflow: auto;
    min-height: 100px;
}
.treeWrap :deep(.el-tree) > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.treeWrap :deep(.el-tree-node__content) > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
</style>
