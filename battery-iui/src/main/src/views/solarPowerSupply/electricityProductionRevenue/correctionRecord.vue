<template>
    <div v-loading="loading" class="correction-record" :style="{ background: bgColor }" @click="recordEventHandler">
        <div class="uedm-navigation">
            <span class="uedm-pagetitle">
                <el-icon class="page-back-arrow" @click="handleBackEvent"><Back /></el-icon>
                {{ $t('solarPower.correctionRecord') }}
            </span>
        </div>

        <div class="record_body">
            <div class="table-above-header">
                <div class="uedm-title">
                    {{ $t('solarPower.taskList') }}
                </div>
                <div class="right filterButtons">
                    <el-tooltip class="record_filter" :content="$t('button.filter')" placement="top">
                        <div>
                            <el-popover :visible="visible" :width="450" :popper-style="{ padding: '0px' }">
                                <el-form
                                    :model="queryForm"
                                    label-width="100px"
                                    class="record-query-form"
                                    @click="formClickHandler($event)"
                                >
                                    <el-form-item :label="$t('table.gmtCreated')">
                                        <el-date-picker
                                            v-model="timeReciver"
                                            type="datetimerange"
                                            :start-placeholder="$t('datetimePicker.beginTime')"
                                            :end-placeholder="$t('datetimePicker.endTime')"
                                            format="YYYY-MM-DD HH:mm:ss"
                                            date-format="YYYY/MM/DD ddd"
                                            time-format="A hh:mm:ss"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                            @focus="onPickerFocus(true)"
                                            @blur="onPickerFocus(false)"
                                        />
                                    </el-form-item>
                                    <el-form-item :label="$t('battery.fields.operator')">
                                        <el-input
                                            v-model="queryForm.operator"
                                            maxlength="50"
                                            style="width: 100%"
                                            :placeholder="$t('placeholder.input')"
                                            show-word-limit
                                            :clearable="true"
                                        />
                                    </el-form-item>
                                    <div class="record-form-button">
                                        <el-button type="primary" @click="saveFilterData">
                                            {{ $t('button.query') }}
                                        </el-button>
                                        <el-button @click="resetForm">
                                            {{ $t('button.reset') }}
                                        </el-button>
                                    </div>
                                </el-form>
                                <template #reference>
                                    <!-- 触发源 -->
                                    <span
                                        class="uedm-icon-button filterIconBtn"
                                        :class="{ on: hasFilter }"
                                        @click="popoverClickHandler"
                                    ></span>
                                </template>
                            </el-popover>
                        </div>
                    </el-tooltip>
                    <el-tooltip class="record_export" :content="$t('button.export')" placement="top">
                        <span class="icon-button plx-ico-export-16" @click="handleExport"></span>
                    </el-tooltip>
                </div>
            </div>
            <div class="record-table">
                <page-table
                    :columns="columns"
                    :data="tableData"
                    :pagination="pagination"
                    style="width: 100%"
                    @sort-change="sortChange"
                    @page-change="pageChange"
                    @size-change="sizeChange"
                >
                    <template #bodyCell="{ row, column }">
                        <span v-if="column.prop === 'timeRange'">
                            {{ row.correctionStartDate }} - {{ row.correctionEndDate }}
                        </span>
                        <!-- 状态映射和重试操作 -->
                        <span v-if="column.prop === 'taskStatus'" class="task_status">
                            <span :class="['circle', 'state_' + row.taskStatus]"></span>
                            {{ TASK_STATUS_MAP[row.taskStatus] || '--' }}
                            <el-link
                                v-if="row.taskStatus == '3'"
                                class="task_retry"
                                type="primary"
                                :underline="false"
                                @click="retryHandler(row.id)"
                            >
                                {{ $t('policyManagement.button.retry') }}
                            </el-link>
                        </span>
                        <!-- 矫正详情抽屉显示-->
                        <span v-if="column.prop === 'operate'">
                            <el-link type="primary" :underline="false" @click="queryDetail(row.id)">
                                {{ $t('button.detail') }}
                            </el-link>
                        </span>
                    </template>
                </page-table>
            </div>
        </div>

        <el-drawer v-model="detailVisible" size="900px" :show-close="false" append-to-body direction="rtl">
            <template #header="{ titleId, titleClass, close }">
                <div class="above-header filterButtons">
                    <div :id="titleId" :class="titleClass">{{ $t('button.detail') }}</div>
                    <div>
                        <el-tooltip class="record_export" :content="$t('button.export')" placement="bottom">
                            <span class="icon-button" @click="detailhandleExport">
                                <span class="plx-ico-export-16"></span>
                            </span>
                        </el-tooltip>
                        <span class="drawer-close" @click="close">
                            <span class="icon-button-close plx-ico-close-16"></span>
                        </span>
                    </div>
                </div>
            </template>
            <correction-detail v-if="detailVisible" ref="correctionDetail" :row="taskId"></correction-detail>
        </el-drawer>
    </div>
</template>

<script>
import PageinationTable from '@/components/pagination-table/index.vue';
import usePaginationTable from '@/components/pagination-table/utils';
import HTTP from '@/util/httpService';
import correctionRecordDetail from './correctionRecordDetail.vue';
import axios from 'axios';
import snakeCase from 'lodash/snakeCase';

export default {
    components: {
        'page-table': PageinationTable,
        'correction-detail': correctionRecordDetail,
    },
    computed: {
        bgColor() {
            return this.$store.getters.getIsDark ? '#1d1d1d' : '#f0f2f5';
        },
        hasFilter() {
            let tag = false;
            for (let key in this.queryForm) {
                if (this.queryForm[key].length && !this.visible) {
                    tag = true;
                }
            }

            return tag;
        },
    },
    data() {
        const { tableData, pagination, sortBy, sortChange, pageChange, sizeChange, fetchDataList } = usePaginationTable(
            this.fetchData,
            20
        );
        return {
            tableData,
            pagination,
            sortBy,
            fetchDataList,
            sortChange,
            pageChange,
            sizeChange,
            loading: false,
            pageInfo: {},
            timeReciver: ['', ''],
            queryForm: {
                operator: '',
                startDate: '',
                endDate: '',
            },
            visible: false,
            pickerFocus: false,
            detailVisible: false,
            taskId: '',
            TASK_STATUS_MAP: {
                0: this.$t('solarPower.toExecute'),
                1: this.$t('solarPower.inProgress'),
                2: this.$t('solarPower.finished'),
                3: this.$t('solarPower.abnormalTermination'),
            },
            columns: [
                {
                    prop: 'gmtCreate',
                    label: this.$t('solarPower.recordList.creationTime'),
                    sortable: true,
                    'show-overflow-tooltip': true,
                    minWidth: 200,
                },
                {
                    prop: 'timeRange',
                    label: this.$t('solarPower.recordList.startToEndTime'),
                    'show-overflow-tooltip': true,
                    minWidth: 200,
                },
                {
                    prop: 'taskStatus',
                    label: this.$t('solarPower.recordList.taskStatus'),
                    width: 250,
                    sortable: true,
                },
                {
                    prop: 'siteCount',
                    label: this.$t('solarPower.recordList.numOfSites'),
                    width: 150,
                },
                {
                    prop: 'gridScopeStrategyName',
                    label: this.$t('solarPower.recordList.gridElePrice'),
                    'show-overflow-tooltip': true,
                    minWidth: 200,
                },
                {
                    prop: 'solarScopeStrategyName',
                    label: this.$t('solarPower.recordList.solarElePrice'),
                    'show-overflow-tooltip': true,
                    minWidth: 200,
                },
                {
                    prop: 'operator',
                    label: this.$t('solarPower.recordList.operator'),
                    width: 100,
                },
                {
                    prop: 'operate',
                    label: this.$t('solarPower.recordList.operation'),
                    width: 100,
                    fixed: 'right',
                    align: 'center',
                },
            ],
        };
    },

    mounted() {
        this.fetchDataList();
    },
    methods: {
        handleBackEvent() {
            this.visible = false;
            this.$emit('back');
        },
        async fetchData() {
            return new Promise((resolve, reject) => {
                this.loading = true;
                HTTP.request('getCollectionRecord', {
                    method: 'post',
                    data: {
                        ...this.queryForm,
                        pageSize: this.pagination ? this.pagination.pageSize || 20 : 20,
                        pageNo: this.pagination ? this.pagination.currentPage || 1 : 1,
                        orderBy: this.sortBy ? this.formatOrderBy(this.sortBy.prop) : '',
                        sort: this.sortBy ? this.sortBy.order || '' : '',
                    },
                    complete: resp => {
                        this.loading = false;
                        if (resp.code === 0 && resp.data) {
                            let data = resp.data;
                            if (data && data.length > 0) {
                                data.forEach(d => {
                                    for (let key in d) {
                                        if (!d[key] || d[key] === '') {
                                            d[key] = '--';
                                        }
                                    }
                                });
                                resolve({
                                    data: data,
                                    total: resp.total,
                                });
                            } else {
                                console.log('矫正记录展示列表查询失败! => ' + resp.message);
                                resolve({ data: [], total: 0 });
                            }
                        }
                    },
                    error: data => {
                        this.loading = false;
                        console.log('矫正记录展示列表查询失败! => ' + data);
                        reject({ data: [], total: 0 });
                    },
                });
            });
        },
        formatOrderBy(item) {
            let res = '';
            if (item) {
                res = snakeCase(item);
            }
            return res;
        },
        detailhandleExport() {
            if (this.$refs.correctionDetail) {
                this.$refs.correctionDetail.detailhandleExport();
            }
        },
        recordEventHandler() {
            if (!this.pickerFocus && this.visible) {
                this.visible = false;
            }
        },
        formClickHandler(e) {
            // 表单点击阻止冒泡防止层隐藏
            if (e) {
                e.stopPropagation();
            }
        },
        popoverClickHandler(e) {
            // 自定义弹出层阻止冒泡防止层隐藏
            if (e) {
                e.stopPropagation();
            }
            this.visible = !this.visible;
        },
        saveFilterData() {
            this.visible = false;
            if (this.timeReciver) {
                this.queryForm.startDate = this.timeReciver[0] ? this.timeReciver[0] : '';
                this.queryForm.endDate = this.timeReciver[1] ? this.timeReciver[1] : '';
            } else {
                this.queryForm.startDate = '';
                this.queryForm.endDate = '';
            }
            this.fetchDataList();
        },
        resetForm() {
            this.queryForm.operator = '';
            this.queryForm.startDate = '';
            this.queryForm.endDate = '';
            this.timeReciver = [];
            this.pagination.currentPage = 1;
            this.visible = false;
            this.fetchDataList();
        },
        async handleExport() {
            this.loading = true;
            const tips = this.$message({
                message: this.$t('tipMessage.exportTimeLongTip'),
                showClose: true,
                duration: 2000,
                customClass: 'blue-info',
            });
            // 导出接口
            let param = Object.assign(
                this.queryForm,
                { orderBy: this.sortBy ? this.formatOrderBy(this.sortBy.prop) : '' },
                { sort: this.sortBy ? this.sortBy.order || '' : '' }
            );
            const DOWNLOAD_URL = '/api/battery-manager/v1/solar-revenue-correct/correct-records';
            const forgerydefense = (localStorage['csrftoken'] && localStorage['csrftoken'].replace(/\"/g, '')) || '';
            const languageOption = (
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) ||
                ''
            ).replace('-', '_');
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            axios
                .post(url, param, config)
                .then(res => {
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                this.$message.error(result.message);
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        let blob = new Blob([res.data]);
                        let url = window.URL.createObjectURL(blob); // 创建下载的链接
                        let link = document.createElement('a');
                        let fileName = '';
                        if (res.headers['content-disposition']) {
                            let contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename*=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                })
                .finally(() => (this.loading = false));
        },
        onPickerFocus(focus) {
            if (focus) {
                setTimeout(() => {
                    this.pickerFocus = focus;
                }, 100);
            } else {
                this.pickerFocus = focus;
            }
        },
        retryHandler(id) {
            // 重试接口
            if (!id) {
                console.log('任务ID为空!');
                return;
            }
            HTTP.request('collectionRetry', {
                method: 'get',
                urlParam: {
                    taskId: id,
                },
                complete: resp => {
                    if (resp.code === 0) {
                        this.showMsg(this.$t('tipMessage.operationSuccsee'), false);
                        // 操作成功，刷新列表
                        this.fetchDataList();
                    } else {
                        this.showMsg(this.$t('tipMessage.operationFailure'));
                    }
                },
                error: resp => {
                    this.showMsg(this.$t('tipMessage.operationFailure'));
                },
            });
        },
        showMsg(msg, isError = true) {
            this.$message({
                message: msg,
                showClose: false,
                type: isError ? 'error' : 'success',
            });
        },
        queryDetail(id) {
            this.taskId = id;
            this.detailVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
html.dark {
    .correction-record {
        .record_body {
            background-color: #141414;
        }
    }
}
.correction-record {
    height: 100%;
    .record_body {
        margin: 16px;
        background-color: white;
        padding-bottom: 16px;

        .table-above-header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            padding: 16px 16px 5px;

            .right {
                display: flex;
            }

            .uedm-icon-button {
                margin-left: 10px;
            }

            .icon-button {
                margin-left: 10px;
            }
        }

        .record-table {
            margin-left: 16px;
            margin-right: 16px;
            margin-bottom: 0px;

            .task_status {
                display: flex;
                align-items: center;
            }
        }
    }
}

.el-form {
    padding: 16px;
}

.record-form-button {
    text-align: right;
}

.circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.state_0 {
    background-color: #86e3dd;
}

.state_1 {
    background-color: #409eff;
}

.state_2 {
    background-color: #67c23a;
}

.state_3 {
    background-color: #f56c6c;
}

.task_retry {
    margin-left: 10px;
}

.el-table .cell .el-link {
    font-size: 15px;
}
.drawer-close {
    cursor: pointer;
    margin-left: 10px;
    font-size: 20px;
}

.above-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.uedm-pagetitle {
    display: flex;
    align-items: center;

    .page-back-arrow {
        cursor: pointer;
        margin-right: 5px;
    }
}
</style>
