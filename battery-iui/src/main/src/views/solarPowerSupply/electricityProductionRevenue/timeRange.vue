<template>
    <span class="time-range">
        <el-button-group>
            <el-button
                v-for="item in timeTypes"
                :key="item.key"
                :class="{ active: selTimeType === item.key }"
                @click.stop="handleBarClick(item.key)"
            >
                {{ item.name }}
            </el-button>
        </el-button-group>
        <div v-show="showTimeSelect" class="timeSelect" @click="handleTimeSelect($event)">
            <el-form ref="form" :model="form" :rules="rules" label-width="56px">
                <el-form-item :label="$t('solarPower.granularity')">
                    <el-select v-model="selGranularity" placeholder="Select">
                        <el-option v-for="item in granularity" :key="item.key" :label="item.name" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('solarPower.dateTime.date')" prop="custom">
                    <el-date-picker
                        v-if="!useYearPicker"
                        v-model="form.custom"
                        :disabled-date="pickerOptions.disabledDate"
                        :type="pickerType"
                        range-separator="-"
                        :clearable="false"
                        :start-placeholder="$t('datetimePicker.startDate')"
                        :end-placeholder="$t('datetimePicker.endDate')"
                        value-format="YYYY-MM-DD"
                        @focus="onPickerFocus(true)"
                        @blur="onPickerFocus(false)"
                    />
                    <div class="yearPicker" v-else>
                        <el-date-picker
                            v-model="form.custom[0]"
                            :disabled-date="pickerOptions.disabledDate"
                            value-format="YYYY"
                            type="year"
                            class="fstpick"
                            :clearable="false"
                            @focus="onPickerFocus(true)"
                            @blur="onPickerFocus(false)"
                        />
                        {{ ' - ' }}
                        <el-date-picker
                            v-model="form.custom[1]"
                            :disabled-date="pickerOptions.disabledDate"
                            value-format="YYYY"
                            type="year"
                            :clearable="false"
                            @focus="onPickerFocus(true)"
                            @blur="onPickerFocus(false)"
                        />
                    </div>
                </el-form-item>
            </el-form>

            <div class="btn-container">
                <el-button type="primary" @click="queryCustom">{{ $t('button.query') }}</el-button>
                <el-button @click="reset">{{ $t('button.reset') }}</el-button>
            </div>
        </div>
    </span>
</template>

<script>
import { $emit } from '../../../utils/gogocodeTransfer';
import toolDay from '@/util/tool_day.js';
import moment from 'moment';

export default {
    props: {
        range: {
            type: String,
            default: 'last7days',
        },
    },
    data() {
        this.timeTypes = [
            {
                key: 'day',
                name: this.$t('timeRange.curDay'),
            },
            {
                key: 'month',
                name: this.$t('staggerBigScreen.thisMonth'),
            },
            {
                key: 'year',
                name: this.$t('staggerBigScreen.thisYear'),
            },
            {
                key: 'custom',
                name: this.$t('timeRange.custom'),
            },
        ];
        this.granularity = [
            {
                key: 'day',
                name: this.$t('solarPower.daily'),
            },
            {
                key: 'month',
                name: this.$t('solarPower.monthly'),
            },
            {
                key: 'year',
                name: this.$t('solarPower.yearly'),
            },
            {
                key: 'all',
                name: this.$t('timeRange.all'),
            },
        ];

        this.handleFuncMap = {
            day: 'getToday',
            month: 'getCurMonth',
            year: 'getCurYear',
        };

        return {
            selTimeType: this.timeTypes[1].key,
            selGranularity: this.granularity[3].key,
            startTime: '',
            endTime: '',
            customStartTime: '',
            customEndTime: '',
            showTimeSelect: false,
            form: {
                custom: ['', ''],
            },
            rules: {
                custom: [
                    {
                        required: true,
                        message: this.$t('rules.selectDate'),
                        trigger: 'change',
                    },
                ],
            },
            selYearsRange: [],
            pickerFocus: false,
        };
    },
    computed: {
        pickerOptions() {
            return {
                disabledDate: time => {
                    if (time.getTime() > Date.now()) {
                        return true;
                    }
                    let maxDiffSeconds = 0;
                    switch (this.selGranularity) {
                        case 'day':
                            maxDiffSeconds = 31 * 24 * 60 * 60 * 1000;
                            break;
                        case 'month':
                            maxDiffSeconds = 365 * 24 * 60 * 60 * 1000;
                            break;
                        case 'year':
                        case 'all':
                            maxDiffSeconds = 10 * 365 * 24 * 60 * 60 * 1000;
                            break;
                        default:
                            break;
                    }
                    return moment().diff(moment(time)) > maxDiffSeconds;
                },
            };
        },
        pickerType() {
            return this.selGranularity === 'month' ? 'monthrange' : 'daterange';
        },
        useYearPicker() {
            return this.selGranularity === 'year';
        },
    },
    watch: {
        selGranularity() {
            this.initTimeRange();
        },
    },
    created() {
        this.getTime();
        this.initTimeRange();
    },
    mounted() {
        document.addEventListener('click', () => {
            if (!this.pickerFocus) {
                this.showTimeSelect = false;
            }
        });
    },
    methods: {
        handleBarClick(type) {
            this.selTimeType = type;
            if (type === 'custom') {
                this.showTimeSelect = true;
                this.$refs.form.clearValidate();
            } else {
                this.showTimeSelect = false;
                this.getTime();
                $emit(this, 'change', this.getTimeParam());
            }
        },
        getTime() {
            const funcName = this.handleFuncMap[this.selTimeType];
            this.startTime = funcName && toolDay[funcName](false).startTime;
            this.endTime = funcName && toolDay[funcName](false).endTime;
            return { startTime: this.startTime, endTime: this.endTime };
        },
        getTimeParam() {
            const dateFormatConfig = {
                day: 'YYYY-MM-DD',
                month: 'YYYY-MM',
                year: 'YYYY',
                all: 'YYYY-MM-DD',
            };
            /* Started by AICoder, pid:8a53cbae6943418d89474752d5e84586 */
            const format = this.selTimeType === 'custom' ? dateFormatConfig[this.selGranularity] : dateFormatConfig[this.selTimeType];
            /* Ended by AICoder, pid:8a53cbae6943418d89474752d5e84586 */
            const startTime = moment(this.startTime).format(format);
            const endTime = moment(this.endTime).format(format);
            return {
                timeType: this.selTimeType,
                granularity: this.selGranularity,
                startTime: startTime,
                endTime: endTime,
            };
        },
        handleTimeSelect(e) {
            // 自定义弹出层阻止冒泡防止层隐藏
            if (e) {
                e.stopPropagation();
            }
        },
        queryCustom(hide = true) {
            // 自定义弹出层查询按钮事件
            this.$refs.form.validate(valid => {
                if (valid) {
                    const custom = this.form.custom;
                    if (this.selGranularity === 'year' && custom[0] > custom[1]) {
                        this.$message({
                            message: this.$t('solarPower.invalidTime'),
                            duration: 2000,
                            showClose: false,
                            type: 'error',
                        });
                        return;
                    }
                    const dateFormat = this.getDateFormat();
                    this.startTime = moment(custom[0]).format(dateFormat);
                    this.endTime = moment(custom[1]).format(dateFormat);
                    $emit(this, 'change', this.getTimeParam());
                    if (hide) {
                        this.showTimeSelect = false;
                    }
                }
            });
        },
        getDateFormat() {
            switch (this.selGranularity) {
                case 'month':
                    return 'YYYY-MM';
                case 'year':
                    return 'YYYY';
                case 'day':
                case 'all':
                default:
                    return 'YYYY-MM-DD';
            }
        },
        reset() {
            this.selGranularity = 'all';
            this.initTimeRange();
            this.queryCustom(false);
        },
        onPickerFocus(focus) {
            if (focus) {
                setTimeout(() => {
                    this.pickerFocus = focus;
                }, 100);
            } else {
                this.pickerFocus = focus;
            }
        },
        initTimeRange() {
            let start = '';
            let end = '';
            switch (this.selGranularity) {
                case 'day':
                    start = moment().subtract(1, 'day').format('YYYY-MM-DD');
                    end = moment().subtract(1, 'day').format('YYYY-MM-DD');
                    break;
                case 'month':
                    //TODO:此处设为YYYY-MM时，时间设置到datePicker不生效，但是其他页面生效，待研究
                    start = moment().subtract(1, 'month').format('YYYY-MM-DD');
                    end = moment().subtract(1, 'month').format('YYYY-MM-DD');
                    break;
                case 'year':
                    start = moment().subtract(1, 'year').format('YYYY');
                    end = moment().subtract(1, 'year').format('YYYY');
                    break;
                case 'all':
                    start = moment().subtract(31, 'day').format('YYYY-MM-DD');
                    end = moment().subtract(1, 'day').format('YYYY-MM-DD');
                    break;
                default:
                    break;
            }

            this.form.custom = [start, end];
        },
    },
    emits: ['change'],
};
</script>

<style lang="scss" scoped>
.time-range {
    position: relative;
}
.timeSelect {
    position: absolute;
    left: 0;
    top: 34px;
    z-index: 1000;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 356px;
}

::v-deep .yearPicker {
    position: relative;
    .el-input__inner,
    .el-input__inner {
        width: 100px;
        flex-grow: 0;
    }

    .el-date-editor {
        width: 143px;
    }

    .el-input__wrapper {
        flex-grow: 0;
    }

    .el-input__wrapper:hover {
        flex-grow: 0;
    }
}

.btn-container {
    text-align: right;
    .el-button:first-child {
        margin-right: 8px;
    }
}

:deep(.el-button--default) {
    color: #303133;
}
:deep(.el-button-group) .el-button--default.active {
    color: #1993ff;
}

:deep(.el-button:not(.is-link, .is-text, .is-plain, .is-round) +) .el-button {
    margin-left: 0px;
}

html.dark {
    .timeSelect {
        border-color: #474a59;
        background-color: #22242e;
    }
    .el-button-group button.active {
        background-color: #1993ff;
    }
    .el-button--default {
        color: #a4a7b3;
    }
}
</style>
