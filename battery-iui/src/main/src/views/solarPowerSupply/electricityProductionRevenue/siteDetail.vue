<template>
    <div class="uedm-content-area">
        <div class="filter-tool">
            <div class="uedm-title">
                {{ $t('button.detail') }}
                <span class="site-count-title">{{ $t('solarPower.siteCount') }}</span>
                <span class="site-count">{{ siteCount }}</span>
            </div>
            <div class="filter-btn-wrapper">
                <el-button v-if="hasCorrectionRight" type="default" @click="onCorrectionClick">
                    {{ $t('solarPower.correction') }}
                </el-button>
                <span class="filterItem">
                    <column-filter
                        ref="columnFilter"
                        :value="headerConfig"
                        :height="560"
                        :is-dark="isDark"
                        :tip="$t('solarPower.columnCustom')"
                        @save="onFilterSave"
                    />
                </span>
            </div>
        </div>
        <div class="content">
            <pagination-table
                v-if="tableHeader.length"
                v-loading="tableLoading"
                class="detail-table"
                style="width: 100%"
                :tooltip-effect="isDark ? 'light' : 'dark'"
                :max-height="540"
                :columns="columns"
                :data="tableData"
                :pagination="pagination"
                :row-key="ROW_KEY"
                @sort-change="sortChange"
                @page-change="pageChange"
                @size-change="sizeChange"
            >
                <template #headerCell="{ column }">
                    <span v-if="column.prop === 'expand'">
                        <el-icon class="expand" @click="onToggle('all')">
                            <arrow-right v-show="isCollapseAll || !isExpandAll" />
                            <arrow-down v-show="isExpandAll" />
                        </el-icon>
                    </span>
                </template>
                <template #bodyCell="{ row, column }">
                    <span v-if="column.prop === 'expand'">
                        <el-icon v-show="row.rateType?.length > 1" class="expand" @click="onToggle(row[ROW_KEY])">
                            <arrow-right v-show="!expandRowKeys.includes(row[ROW_KEY])" />
                            <arrow-down v-show="expandRowKeys.includes(row[ROW_KEY])" />
                        </el-icon>
                    </span>
                    <span v-if="DETAIL_PROPS.includes(column.prop)">
                        <span v-if="!row[column.prop]">--</span>
                        <div
                            v-for="(item, index) in sliceDetailByCol(
                                row[column.prop],
                                expandRowKeys.includes(row[ROW_KEY])
                            )"
                            v-else
                            :key="`${column.prop}-${row[ROW_KEY]}-${index}`"
                        >
                            <template v-if="column.prop === 'rateType'">
                                {{ getRateTypeByKey(item) }}
                            </template>
                            <template v-else>
                                <ellipsis-text :text="column.formatter(row, column, item)">
                                    {{ column.formatter(row, column, item) }}
                                </ellipsis-text>
                            </template>
                        </div>
                    </span>
                    <span v-if="column.prop === 'operation'">
                        <el-link
                            v-if="!isArrayEmpty(row.rateType)"
                            type="primary"
                            :underline="false"
                            style="font-size: 14px"
                            @click="onPriceJump(row)"
                        >
                            {{ t('solarPower.elecPrice') }}
                        </el-link>
                        <el-link v-else :underline="false" style="font-size: 14px" disabled>
                            {{ t('solarPower.elecPrice') }}
                        </el-link>
                    </span>
                </template>
            </pagination-table>
            <div v-else class="no-data border" :style="{ height: 120 + 'px', 'line-height': 120 + 'px' }">
                <span>{{ $t('common.noData') }}</span>
            </div>
        </div>
        <!-- 矫正 -->
        <el-drawer
            v-model="showCorrectionDrawer"
            :with-header="false"
            :show-close="false"
            destroy-on-close
            append-to-body
            size="620px"
        >
            <correction
                ref="correctionRef"
                v-model:loading="correctionLoading"
                :node="node"
                @close="showCorrectionDrawer = false"
            />
            <template #footer>
                <div class="footer dialog-bottom-buttons">
                    <el-button :loading="correctionLoading" type="primary" @click="onCorrectionSubmit">
                        {{ $t('button.confirm') }}
                    </el-button>
                    <el-button type="default" @click="showCorrectionDrawer = false">
                        {{ $t('button.cancel') }}
                    </el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, inject, computed, watch, onMounted } from 'vue';
import { ElMessage as message } from 'element-plus';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import map from 'lodash/map';
import get from 'lodash/get';
import every from 'lodash/every';
import filter from 'lodash/filter';
import PaginationTable from '@/components/pagination-table';
import usePaginationTable from '@/components/pagination-table/utils';
import EllipsisText from '@/components/ellipsis-text.vue';
import Correction from './correction.vue';
import HTTP from '@/util/httpService.js';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
import { isArrayEmpty, isEmpty } from '../../../util/common';

const ROW_KEY = 'rowKey';
const RIGHT_DICT = {
    CORRECTION: 'pv.ppa.solar.revenue.correction',
    PRICE: 'dir.m.price.table.view',
};
const PRICE_JUMP_CONFIG = {
    FROM: 'battery_solar_revenue',
    KEY: 'configuration_priceTable_jump_param',
};
const COL_WIDTH_MAP = {
    name: 120,
    time: 260,
    totalEnergyGeneration: 140,
    totalSolarRevenue: 140,
    totalSavings: 140,
    totalGridFee: 140,
    carbonReduction: 120,
    location: 140,
};
const STATUS_CODE = {
    SUCCESS: 0,
    INVALID_PARAMS: -101,
    BLANK_DIM: -301,
    INVALID_SEQUENCE: -302,
    INVALID_RANGE: -305,
    CANNOT_MODIFY: -308,
};
const DETAIL_PROPS = ['rateType', 'energyGeneration', 'solarRevenue', 'gridFee', 'savings'];

const store = useStore();
const { t } = useI18n();
const rights = inject('rights');

const props = defineProps({
    node: {
        type: Object,
        default: null,
    },
    queryParameter: {
        type: Object,
        default: null,
    },
    peakPlatFilter: {
        type: Array,
        default: function () {
            return [];
        },
    },
});

const headerConfig = ref([]);
const showCorrectionDrawer = ref(false);
const expandRowKeys = ref([]);
const correctionRef = ref(null);
const correctionLoading = ref(false);

const hasCorrectionRight = computed(() => rights && rights[RIGHT_DICT.CORRECTION]);
const hasPriceRight = computed(() => rights && rights[RIGHT_DICT.PRICE]);
const tableHeader = computed(() => filter(headerConfig.value, col => col.enable));
const siteCount = computed(() => (isArrayEmpty(tableData.value) ? 0 : tableData.value[0].siteTotal ?? '--'));
const isDark = computed(() => store.getters.getIsDark);
const currencyUnit = computed(() => store.getters.getCurrencyUnit);
const rowKeys = computed(() => map(tableData.value, item => item[ROW_KEY]));
const isExpandAll = computed(() => {
    return (
        rowKeys.value.length &&
        rowKeys.value.length === expandRowKeys.value.length &&
        every(rowKeys.value, item => expandRowKeys.value.includes(item))
    );
});
const isCollapseAll = computed(() => isArrayEmpty(expandRowKeys.value));
const columns = computed(() => {
    const expandColumn = {
        prop: 'expand',
        width: 40,
    };
    const detailColumn = {
        prop: 'rateTypeRevenueDetail',
        label: t('solarPower.revenueDetail'),
        children: [
            {
                prop: 'rateType',
                label: t('solarPower.rateType'),
                minWidth: 180,
                className: 'detail-col',
                formatter: formatValue,
            },
            {
                prop: 'energyGeneration',
                label: `${t('solarPower.billedUsage')}(kWh)`,
                minWidth: 150,
                className: 'detail-col',
                formatter: formatValue,
            },
            {
                prop: 'solarRevenue',
                label: `${t('solarPower.recordDetail.solarRevenue', { unit: currencyUnit.value })}`,
                minWidth: 150,
                className: 'detail-col',
                formatter: formatValue,
            },
            {
                prop: 'gridFee',
                label: `${t('solarPower.recordDetail.gridFee', { unit: currencyUnit.value })}`,
                minWidth: 150,
                className: 'detail-col',
                formatter: formatValue,
            },
            {
                prop: 'savings',
                label: `${t('solarPower.recordDetail.savings', { unit: currencyUnit.value })}`,
                minWidth: 150,
                className: 'detail-col',
                formatter: formatValue,
            },
        ],
    };
    const operateColumn = {
        prop: 'operation',
        label: t('table.operation'),
        fixed: 'right',
        align: 'center',
        width: 120,
    };
    return [expandColumn]
        .concat(
            map(tableHeader.value, config => ({
                ...config,
                prop: config.id,
                label: config.name,
                minWidth: COL_WIDTH_MAP[config.id] || void 0,
                formatter: formatValue,
                showOverflowTooltip: true,
            }))
        )
        .concat([detailColumn, operateColumn]);
});

const fetchData = () => {
    const { prop: order = 'default', order: sort = 'asc' } = sortBy.value;
    const param = {
        ...props.queryParameter,
        order,
        sort,
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize,
    };
    expandRowKeys.value = [];
    return new Promise((resolve, reject) => {
        HTTP.request('getPvRevStatisticsDetail', {
            method: 'post',
            data: param,
            complete: resp => {
                tableData.value = [];
                if (resp.code === STATUS_CODE.SUCCESS) {
                    resolve({
                        data: map(get(resp, 'data.list', []), item => ({
                            ...item,
                            rowKey: `${item.siteId}-${item.time}`,
                            rateType: getDetailByCol(item.rateTypeRevenueDetail, 'rateType'),
                            energyGeneration: getDetailByCol(item.rateTypeRevenueDetail, 'energyGeneration'),
                            solarRevenue: getDetailByCol(item.rateTypeRevenueDetail, 'solarRevenue'),
                            gridFee: getDetailByCol(item.rateTypeRevenueDetail, 'gridFee'),
                            savings: getDetailByCol(item.rateTypeRevenueDetail, 'savings'),
                        })),
                        total: get(resp, 'data.total', 0),
                    });
                } else if (resp.code === STATUS_CODE.INVALID_PARAMS) {
                    resolve({ data: [], total: 0 });
                    message({
                        message: t('tipMessage.paramIsNull'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                } else {
                    message({
                        message: t('tipMessage.requestError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                    resolve({ data: [], total: 0 });
                }
            },
            error: err => {
                console.log(err);
                message({
                    message: t('tipMessage.requestError'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
                reject({ data: [], total: 0 });
            },
        });
    });
};

const { tableLoading, tableData, pagination, sortBy, sortChange, pageChange, sizeChange, fetchDataList } =
    usePaginationTable(fetchData);

const formatValue = (_row, _column, cellValue) => cellValue ?? '--';

const fetchFilterData = () => {
    HTTP.request('getPvRevDimensions', {
        method: 'get',
        complete: resp => {
            if (resp.code === STATUS_CODE.SUCCESS) {
                const config = map(get(resp, 'data', []), item => ({
                    ...item,
                    name: item.unit ? `${item.name ?? '--'}(${item.unit})` : item.name ?? '--',
                }));
                setFilter(config);
            }
        },
        error: err => {
            console.log(err);
        },
    });
};

const setFilter = config => {
    const filterConfigMap = new Map([
        ['peakestEnergyGeneration', 'Tip'],
        ['peakestSolarRevenue', 'Tip'],
        ['peakestSavings', 'Tip'],
        ['peakestGridFee', 'Tip'],
        ['peakEnergyGeneration', 'Peak'],
        ['peakSolarRevenue', 'Peak'],
        ['peakSavings', 'Peak'],
        ['peakGridFee', 'Peak'],
        ['normalEnergyGeneration', 'Plat'],
        ['normalSolarRevenue', 'Plat'],
        ['normalSavings', 'Plat'],
        ['normalGridFee', 'Plat'],
        ['lowEnergyGeneration', 'Valley'],
        ['lowSolarRevenue', 'Valley'],
        ['lowSavings', 'Valley'],
        ['lowGridFee', 'Valley'],
    ]);

    config.forEach(cl => {
        const configKey = filterConfigMap.get(cl.id);
        if (isEmpty(configKey)) {
            return;
        }
        const config = props.peakPlatFilter?.find(item => item.type === configKey);
        const vis = config?.visible ?? true;
        if (!vis) {
            cl.defaultEnable = false;
            cl.enable = false;
            cl.defaultFixed = true;
        }
    });
    headerConfig.value = config;
};

const onFilterSave = d => {
    tableLoading.value = true;
    const updateParameter = map(d || [], item => ({
        id: item.id,
        sequence: item.sequence,
        enable: item.enable,
    }));
    HTTP.request('updatePvRevDimensions', {
        method: 'post',
        data: updateParameter,
        complete: data => {
            tableLoading.value = false;
            if (data.code === STATUS_CODE.SUCCESS) {
                message({
                    message: t('batteryOverview.tips.filterSaveSuccess'),
                    type: 'success',
                });
                fetchFilterData();
            } else if (data.code === STATUS_CODE.BLANK_DIM) {
                message({
                    message: t('batteryOverview.tips.dimIdBlank'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            } else if (data.code === STATUS_CODE.INVALID_SEQUENCE) {
                message({
                    message: t('batteryOverview.tips.sequenceNotUnique'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            } else if (data.code === STATUS_CODE.INVALID_RANGE) {
                message({
                    message: t('batteryOverview.tips.valueNotRange'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            } else if (data.code === STATUS_CODE.CANNOT_MODIFY) {
                message({
                    message: t('batteryOverview.tips.valueNotModify'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            } else {
                message({
                    message: t('batteryOverview.tips.filterSaveError'),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            }
        },
        error: () => {
            tableLoading.value = false;
            message({
                message: t('tipMessage.networkError'),
                duration: 5000,
                showClose: true,
                type: 'error',
            });
        },
    });
};

const onCorrectionClick = () => {
    showCorrectionDrawer.value = true;
};

const onCorrectionSubmit = () => {
    correctionRef.value?.submit();
};

const onToggle = (rowKey = 'all') => {
    if (rowKey === 'all') {
        if (isExpandAll.value) {
            // 已经全部展开，点击则全部收起
            expandRowKeys.value = [];
        } else {
            // 没有全部展开，点击则全部展开
            expandRowKeys.value = rowKeys.value;
        }
    } else if (expandRowKeys.value.includes(rowKey)) {
        expandRowKeys.value = filter(expandRowKeys.value, key => key !== rowKey);
    } else {
        expandRowKeys.value.push(rowKey);
    }
};

// 空数组直接返回null，让列走formatter
const getDetailByCol = (datalist = [], colKey) => {
    const data = map(datalist, item => item[colKey]);
    return isArrayEmpty(data) ? null : data;
};

// 获取计费类型国际化
const getRateTypeByKey = key => {
    const mappings = {
        Flat: t('solarPower.flat'),
        'Critical Peak': t('solarPower.criticalPeak'),
        'On-Peak': t('solarPower.onPeak'),
        'Off-Peak': t('solarPower.offPeak'),
        'Super Off-Peak': t('solarPower.superOffPeak'),
    };
    const tieredKey = 'Tiered ';
    if (key.startsWith(tieredKey)) {
        const pattern = new RegExp(`^${tieredKey}(\\d+)-?(.+)?$`);
        const match = key.match(pattern);
        if (match) {
            const n = match[1];
            const suffix = match[2] ? mappings[match[2]] : '';
            return `${t('solarPower.tieredN', { n })}${suffix ? `-${suffix}` : ''}`;
        }
    }
    return mappings[key] || '--';
};

const sliceDetailByCol = (datalist = [], expand = false) => {
    /* Started by AICoder, pid:f71ad1524e0d43298e2cc0206f77361a */
    if (isArrayEmpty(datalist)) {
        return [];
    }
    return expand ? datalist : [datalist[0]];
    /* Ended by AICoder, pid:f71ad1524e0d43298e2cc0206f77361a */
};

// 跳转电价表
const onPriceJump = (row = {}) => {
    if (!hasPriceRight.value) {
        return message.error(t('solarPower.noAuthTip'));
    }
    HTTP.request('getSiteHistoryScopeIds', {
        method: 'post',
        data: {
            siteId: row.siteId,
            beginTime: row.beginTime,
            endTime: row.endTime,
        },
        complete: resp => {
            if (resp.code === STATUS_CODE.SUCCESS) {
                const { name = '' } = row;
                const params = { from: PRICE_JUMP_CONFIG.FROM, ids: get(resp, 'data', []), name };
                const location = top.location;
                const URL = `${location.origin}${location.pathname}#/_uedm-config-dir-m-price-table`;
                sessionStorage.setItem(PRICE_JUMP_CONFIG.KEY, JSON.stringify(params));
                console.log('jump, URL =', URL, ', PARAM =', params);
                window.open(URL, '_blank');
                setTimeout(() => {
                    sessionStorage.removeItem(PRICE_JUMP_CONFIG.KEY);
                }, 2000);
            } else {
                message({
                    message: get(resp, 'message', t('tipMessage.requestError')),
                    duration: 5000,
                    showClose: true,
                    type: 'error',
                });
            }
        },
        error: err => {
            message({
                message: t('tipMessage.requestError'),
                duration: 5000,
                showClose: true,
                type: 'error',
            });
        },
    });
};

watch(
    () => props.queryParameter,
    next => {
        if (isEmpty(next)) {
            return null;
        }
        pagination.currentPage = 1;
        fetchDataList();
    },
    { deep: true }
);

onMounted(() => {
    fetchFilterData();
});
</script>

<style lang="scss" scoped>
.uedm-content-area {
    margin-left: 16px;
    margin-bottom: 16px;
    background: white;
    margin-right: 16px;

    .filter-tool {
        position: relative;
        .uedm-title {
            .site-count {
                font-size: 14px;
            }

            .site-count-title {
                font-size: 14px;
                font-weight: 400;
                margin-left: 8px;
            }
        }
        .filter-btn-wrapper {
            position: absolute;
            top: -4px;
            right: 1px;
            .filterPop {
                width: 414px;
            }
        }
    }

    .content {
        .expand {
            cursor: pointer;
        }

        :deep(.detail-table) {
            .el-table__body {
                .el-table__cell {
                    vertical-align: baseline;
                }
                .el-table__cell.detail-col {
                    .cell {
                        padding: 0;

                        & > span > span {
                            padding: 0 12px;
                        }

                        & > span > div {
                            padding: 0 12px;
                            &:not(:last-child) {
                                padding-top: 8px;
                                padding-bottom: 8px;
                                border-bottom: var(--el-table-border);
                            }
                            &:not(:last-child):first-child {
                                padding-top: 0;
                            }
                            &:not(:first-child):last-child {
                                padding-top: 8px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
