<template>
    <div>
        <el-dialog
            v-model="dialogVisible"
            :title="$t('solarPower.elecPrice')"
            :close-on-click-modal="true"
            :close-on-press-escape="false"
            width="70%"
        >
            <div class="dialog-content">
                <el-table v-loading="loading" :data="tableData" row-key="id" :span-method="handleRowSpan">
                    <el-table-column prop="title" min-width="120" class-name="titleColumn" />
                    <el-table-column prop="name" :formatter="formatColumn" :label="$t('common.name')" min-width="180" />
                    <el-table-column
                        prop="effectiveTime"
                        :formatter="formatColumn"
                        :label="$t('common.time')"
                        min-width="160"
                    />

                    <el-table-column
                        v-for="item in varColConfig"
                        :key="item.key"
                        :prop="item.key"
                        :formatter="formatColumn"
                        :label="item.label"
                        min-width="200"
                    />
                </el-table>
            </div>
            <div v-if="showTip" class="tip-container">
                <span class="icon-info-tip"></span>
                <span>{{ $t('solarPower.gotoElecPriceLeft') }}</span>
                <a
                    class="tipHref"
                    :href="elecPriceHref"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    {{ $t('solarPower.gotoElecPriceCenter') }}
                </a>
                <span>{{ $t('solarPower.gotoElecPriceRight') }}</span>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import HTTP from '@/util/httpService';
import { $emit } from '@/utils/gogocodeTransfer';

export default {
    components: {},
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        peakPlatFilter: {
            type: Array,
            default: function () {
                return [];
            },
        },
    },
    data() {
        return {
            loading: false,
            tableData: [],
            solarRevenueCount: 0,
            gridFeeCount: 0,
            priceUnit: '',
            showTip: false,
        };
    },
    watch: {
        visible(vis) {
            if (vis) {
                this.getElecPrice();
            }
        },
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                $emit(this, 'update:visible', value);
            },
        },
        elecPriceHref() {
            let url = `${window.uedmPathname}#/_uedm-config-dir-m-price-table`;
            return url;
        },
        varColConfig() {
            const allConfig = [
                {
                    key: 'tipPrice',
                    filterKey: 'Tip',
                    label: this.$t('solarPower.tipWithUnit', { unit: this.priceUnit || '--' }),
                },
                {
                    key: 'peakPrice',
                    filterKey: 'Peak',
                    label: this.$t('solarPower.peakWithUnit', { unit: this.priceUnit || '--' }),
                },
                {
                    key: 'platPrice',
                    filterKey: 'Plat',
                    label: this.$t('solarPower.flatWithUnit', { unit: this.priceUnit || '--' }),
                },
                {
                    key: 'valleyPrice',
                    filterKey: 'Valley',
                    label: this.$t('solarPower.valleryWithUnit', { unit: this.priceUnit || '--' }),
                },
            ];

            return allConfig.filter(item => {
                const config = this.peakPlatFilter.find(it => it.type === item.filterKey);
                return config?.visible ?? true;
            });
        },
    },
    created() {},
    mounted() {},
    emits: ['hide'],
    methods: {
        getElecPrice() {
            this.loading = true;
            HTTP.request('getElectricPrice', {
                method: 'post',
                data: {},
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0) {
                        this.convertData(resp.data ?? []);
                    } else if (resp.code === -100) {
                        this.clearData();
                        this.$message({
                            message: this.$t('tipMessage.paramIsNull'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.clearData();
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: resp => {
                    this.loading = false;
                    this.clearData();
                    this.$message({
                        message: this.$t('tipMessage.requestError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        clearData() {
            this.tableData = [];
            this.solarRevenueCount = 0;
            this.gridFeeCount = 0;
            this.priceUnit = '';
            this.showTip = false;
        },
        convertData(data) {
            const solarRvType = '1';
            const gridFeeType = '0';
            const maxShowCount = 5;
            const solarRevenueData = data.find(item => item.priceType === solarRvType);
            const gridFeeData = data.find(item => item.priceType === gridFeeType);
            let solarRevenueList = solarRevenueData?.priceStatisticsDetail ?? [];
            let gridFeeList = gridFeeData?.priceStatisticsDetail ?? [];
            if (solarRevenueList.length > maxShowCount) {
                solarRevenueList = solarRevenueList.slice(0, maxShowCount);
                this.showTip = true;
            }
            if (gridFeeList.length > maxShowCount) {
                gridFeeList = gridFeeList.slice(0, maxShowCount);
                this.showTip = true;
            }
            solarRevenueList.forEach(item => (item.title = this.$t('solarPower.pvElecPrice')));
            gridFeeList.forEach(item => (item.title = this.$t('solarPower.gridElecPrice')));

            this.priceUnit = gridFeeData?.priceUnit ?? '';
            this.solarRevenueCount = solarRevenueList.length;
            this.gridFeeCount = gridFeeList.length;
            this.tableData = [...solarRevenueList, ...gridFeeList];
        },
        formatColumn(row, column, cellValue, index) {
            return cellValue ?? '--';
        },
        handleRowSpan({ row, column, rowIndex, columnIndex }) {
            if (columnIndex !== 0) {
                return;
            }

            if (this.solarRevenueCount > 0) {
                if (rowIndex === 0) {
                    return {
                        rowspan: this.solarRevenueCount,
                        colspan: 1,
                    };
                } else if (rowIndex === this.solarRevenueCount) {
                    return {
                        rowspan: this.gridFeeCount,
                        colspan: 1,
                    };
                }
            } else {
                if (rowIndex === 0) {
                    return {
                        rowspan: this.gridFeeCount,
                        colspan: 1,
                    };
                }
            }

            return {
                rowspan: 0,
                colspan: 1,
            };
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .titleColumn {
    background: var(--el-fill-color-light);
    font-weight: 700;
    color: var(--el-table-text-color);
    font-size: 14px;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background: var(--el-fill-color-light);
}

::v-deep .el-table th.el-table__cell {
    background-color: var(--el-fill-color-light) !important;
}

.tip-container {
    display: flex;
    height: 38px;
    background-color: #ecf5ff;
    border-radius: 2px;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    margin-bottom: 8px;
}

.dark {
    .tip-container {
        background-color: #18222c;
    }
}

.tipHref {
    margin-left: 6px;
    margin-right: 6px;
    color: rgba(64, 158, 255, 1);
}

.icon-info-tip {
    display: block;
    width: 14px;
    height: 14px;
    margin-right: 8px;
    background: url('../../../assets/img/icon-info.png') no-repeat center;
}
</style>
