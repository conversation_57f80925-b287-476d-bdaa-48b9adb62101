<!-- Started by AICoder, pid:c1ea7647780248aaa8a30dea0ccedaf9 -->
<template>
    <el-drawer
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :wrapper-closable="false"
        :size="dialogWidth"
        @close="handleCancel"
    >
        <template #header>
            <div class="uedm-flex-wrap title-wrap">
                <div class="uedm-title">{{ dialogTitle + t('solarPower.historyDetails') }}</div>
                <!-- Started by AICoder, pid:a45dd8ea1d33437c8d60c8eab57be256 -->
                <el-tooltip class="item" effect="dark" :content="$t('button.export')" placement="bottom">
                    <el-button v-if="rights[detailExportRight]" plain class="uedm-button-icon" @click="handleExport">
                        <span class="plx-ico-export-16"></span>
                    </el-button>
                </el-tooltip>
                <!-- Ended by AICoder, pid:a45dd8ea1d33437c8d60c8eab57be256 -->
            </div>
        </template>
        <pagination-table
            v-loading="tableLoading"
            style="width: 100%"
            :tooltip-effect="isDark ? 'light' : 'dark'"
            :data="tableData"
            :columns="tableColumns"
            :pagination="pagination"
            show-overflow-tooltip
            @sort-change="sortChange"
            @page-change="pageChange"
            @size-change="sizeChange"
        ></pagination-table>
    </el-drawer>
</template>
<script setup>
import { ref, reactive, watch, computed, nextTick, onMounted, onBeforeUnmount, markRaw, inject } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import axios from 'axios';
import { ElMessage as message } from 'element-plus';
import PaginationTable from '@/components/pagination-table';
import usePaginationTable from '@/components/pagination-table/utils';
import HTTP from '@/util/httpService.js';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    dialogTitle: {
        type: String,
        default: '',
    },
    id: {
        type: String,
        default: '',
    },
    dayNum: {
        type: String,
        default: '',
    },
});
// # Started by AICoder, pid:c2fff6a58eb542f685c177fc95e1b2e4
const detailExportRight = 'pv.remind.maintaining.details.export'; // 详情导出
const rights = inject('rights');
// # Ended by AICoder, pid:c2fff6a58eb542f685c177fc95e1b2e4

const emit = defineEmits(['visible']);

const { t } = useI18n();
const store = useStore();
const isDark = computed(() => {
    return store.isDark;
});

// 弹出窗口宽度
const dialogWidth = computed(() => {
    // 抽屉最大宽度设为屏幕宽度减20
    let maxWidth = window.innerWidth - 20;
    let width = 1240;
    return Math.min(width, maxWidth) + 'px';
});
// 弹出窗口显示隐藏
const dialogVisible = computed({
    get: () => {
        return props.visible;
    },
    set: value => {
        emit('visible', value);
    },
});
const handleCancel = function () {
    dialogVisible.value = false;
};

const tableColumns = [
    { prop: 'name', label: t('solarPower.solarName'), width: 200, sortable: false },
    { prop: 'statisticsDay', label: t('solarPower.date'), width: 150, sortable: false },
    { prop: 'efficiency', label: t('solarPower.efficiency'), width: 120, sortable: true },
    { prop: 'level', label: t('solarPower.level'), width: 120, sortable: false },
    { prop: 'generation', label: t('solarPower.generatedEnergy'), width: 120, sortable: false },
    { prop: 'load', label: t('solarPower.load'), width: 120, sortable: false },
    { prop: 'ratioSupply', label: t('solarPower.supplyUseRatio'), width: 120, sortable: true },
    { prop: 'position', label: t('solarPower.position'), width: 300, sortable: false },
];

const fetchData = () => {
    const param = {
        siteId: props.id,
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize,
        dayNum: props.dayNum,
    };
    if (sortBy.value) {
        const { prop: order = 'default', order: sort = 'asc' } = sortBy.value;
        param.order = order;
        param.sort = sort;
    }
    return new Promise((resolve, reject) => {
        HTTP.request('getPvMaintenanceHistory', {
            method: 'post',
            data: param,
            complete: resp => {
                if (resp.code === 0) {
                    resolve({
                        data: resp.data || [],
                        total: resp.total || 0,
                    });
                } else {
                    resolve({ data: [], total: 0 });
                }
            },
            error: err => {
                reject({ data: [], total: 0 });
            },
        });
    });
};

const handleExport = () => {
    const param = {
        siteId: props.id,
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize,
        dayNum: props.dayNum,
    };
    if (sortBy.value) {
        const { prop: order = 'default', order: sort = 'asc' } = sortBy.value;
        param.order = order;
        param.sort = sort;
    }
    const DOWNLOAD_URL = '/api/battery-manager/v1/solar-maintenance/maintenance-history/export';
    const forgerydefense = window.forgerydefense || '';
    const languageOption = window.languageOption || '';
    let url = `${DOWNLOAD_URL}`;
    let config = {
        responseType: 'blob',
        headers: {
            'language-option': languageOption,
            'forgerydefense': forgerydefense
        },
    };
    axios.post(url, param, config).then(res => {
        console.log('111 handleExport res', res);
        // 导出错误，返回json对象，需判断
        if (res.data.type === 'application/json') {
            let reader = new FileReader();
            reader.onload = e => {
                let result = JSON.parse(e.target.result);
                if (result && result.code !== 0) {
                    this.$message.error(result.message);
                }
            };
            reader.readAsText(res.data, ['utf-8']);
        } else {
            // 导出成功，返回数据流
            let blob = new Blob([res.data]);
            let url = window.URL.createObjectURL(blob); // 创建下载的链接
            let link = document.createElement('a');
            let fileName = '';
            if (res.headers['content-disposition']) {
                let contentDisposition = res.headers['content-disposition'];
                fileName = contentDisposition.split('filename=')[1];
                fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
            }

            link.style.display = 'none';
            link.href = url;
            link.download = `${fileName}`; // 下载后文件名
            document.body.appendChild(link);
            link.click(); // 点击下载
            document.body.removeChild(link); // 下载完成移除元素
            window.URL.revokeObjectURL(url); // 释放掉blob对象
        }
    });
};

const { tableLoading, tableData, pagination, sortBy, sortChange, pageChange, sizeChange, reload } =
    usePaginationTable(fetchData);

onMounted(() => {
    reload();
});
</script>
<style lang="scss" scoped>
.title-wrap {
    justify-content: space-between;
    align-items: center;
}
</style>
<!-- Ended by AICoder, pid:c1ea7647780248aaa8a30dea0ccedaf9 -->
