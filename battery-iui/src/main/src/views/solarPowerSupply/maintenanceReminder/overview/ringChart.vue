<template>
    <div :id="chartId" ref="chartRef"></div>
</template>
<script setup>
import * as echarts from 'echarts';
import { ref, watch, computed, nextTick, onMounted, onBeforeUnmount, markRaw } from 'vue';
import { useI18n } from 'vue-i18n';
import { CHART_COLORS, getPineNodata } from '@uedm/uedm-ui/src/util/constants.js';
import { canvsa2Image } from '@/util/imageTool';
// import { CHART_COLORS, getPineNodata } from '@/util/constants.js';
// eslint-disable-next-line no-undef
const props = defineProps({
    isDark: {
        type: Boolean,
        default: false,
    },
    chartId: {
        type: String,
        default: 'chartRing',
    },
    height: {
        type: Number,
        default: 420,
    },
    color: {
        type: Array,
        default: () => {
            return ['#7cd180', '#109ebf', '#4bd3f3', '#ffc850', '#0171ab', '#a8e2cf', '#CCA4E3', '#4ee5c8'];
        },
    },
    text: {
        type: String,
        default: '',
    },
    total: {
        type: Number,
        default: 0,
    },
    series: {
        type: Array,
        default: () => {
            return [];
        },
    },
    rendererType: {
        type: String,
        default: 'svg',
    },
});

const { t } = useI18n();
const emits = defineEmits(['handle-click']);

const chartRef = ref();
const myChart = ref(null);
const chartColor = CHART_COLORS;
const resizeObserver = new ResizeObserver(() => {
    resize();
});
onMounted(() => {
    if (chartRef.value) {
        initCharts();
        resize();
        resizeObserver.observe(chartRef.value);
    }
});
onBeforeUnmount(() => {
    resizeObserver.unobserve(chartRef.value);
    myChart.value && myChart.value.dispose();
});
watch(
    () => [props.series],
    (newVal, oldVal) => {
        updateChart();
    }
);

const nowSelectedName = ref({ id: '', name: '' });

const initCharts = function () {
    myChart.value = markRaw(
        echarts.init(chartRef.value, props.isDark ? 'dark' : 'light', {
            height: props.height + 'px',
            renderer: props.rendererType,
        })
    );
    myChart.value.setOption(getOptions());
    myChart.value.on('click', function (params) {
        if (nowSelectedName.value.id === params.data.id) {
            myChart.value.dispatchAction({
                type: 'downplay',
                name: nowSelectedName.value.name,
            });
            nowSelectedName.value = '';
        } else {
            myChart.value.dispatchAction({
                type: 'downplay',
                name: nowSelectedName.value.name,
            });
            myChart.value.dispatchAction({
                type: 'highlight',
                name: params.data.name,
            });
            nowSelectedName.value = params.data;
        }
        emits('handle-click', nowSelectedName.value.id);
    });
};

// <!-- Started by AICoder, pid:75cbc233775c403e9a6c9eacc218c11a -->
const colorType = props.isDark ? 'dark' : 'default';

const getLegendItem = (data, index) => {
    return {
        type: 'scroll',
        orient: 'horizontal',
        left: '21%',
        y: `${20 + index * 5}%`,
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 30,
        align: 'left',
        textStyle: {
            width: 143,
            overflow: 'break',
            fontSize: 14,
            color: CHART_COLORS[colorType].defaultColor,
            backgroundColor: 'transparent'
        },
        data: data.map(i => {
            return {
                name: i.name,
                itemStyle: {
                    color: i.color
                }
            }
        }),
        formatter: name => {
            let d = data || [];
            if (!d.length) {
                return;
            }
            let value = d.filter(x => x.name === name)[0].value;
            let total = props.total;
            let arr = [name + ': ' + '--'];
            if (total === 0 && (value || value === 0)) {
                arr = [name + ': ' + value + ' / ' + '0%'];
            } else if (total && (value || value === 0)) {
                arr = [name + ': ' + value + ' / ' + ((value / total) * 100).toFixed(2).replace(/(\.00|0)$/, '') + '%'];
            }
            return arr;
        },
    };
};

const getLegendItemList = (series = []) => {
    const seriesLen = series.length;
    const result = [];
    for (let i = 0; i < seriesLen; i++) {
        const index = i % 3;
        if (index === 0) {
            const endIndex = Math.min(i + 3, seriesLen);
            const item = getLegendItem(series.slice(i, endIndex), i + 1);
            result.push(item);
        }
    }
    return result;
};

const getOptions = function () {
    let options = {};
    options = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'item',
            backgroundColor: chartColor[colorType].background,
            borderColor: chartColor[colorType].border,
            textStyle: { color: chartColor[colorType].color },
        },
        legend: getLegendItemList(props.series),
        graphic: [
            {
                type: 'group',
                top: '40%',
                left: '10%',
                bounding: 'raw',
                children: [
                    {
                        type: 'text',
                        top: 10,
                        bounding: 'raw',
                        invisible: !props.series.length, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: chartColor[colorType].total,
                            text: `\n${props.text}`,
                            textAlign: 'center',
                            fontSize: 14,
                            lineHeight: 20,
                        },
                    },
                    {
                        type: 'text',
                        top: 0,
                        bounding: 'raw',
                        invisible: !props.series.length, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: chartColor[colorType].title,
                            text: `${props.total}`,
                            textAlign: 'center',
                            lineHeight: 28,
                            fontSize: 24,
                        },
                    },
                ],
            },
        ],
        series: [
            {
                type: 'pie',
                center: ['10%', '50%'],
                radius: ['55%', '70%'],
                avoidLabelOverlap: true,
                label: {
                    show: false,
                },
                data: props.series,
            },
        ],
    };
    options.graphic.push(getPineNodata(colorType, props.series.length, '62%', t('common.noData')));
    return options;
};
// <!-- Ended by AICoder, pid:75cbc233775c403e9a6c9eacc218c11a -->

const updateChart = function () {
    if (myChart.value) {
        myChart.value.clear();
        myChart.value.setOption(getOptions());
    }
};
const resize = function () {
    nextTick(() => {
        if (myChart.value) {
            myChart.value.resize();
        }
    });
};

// <!-- Started by AICoder, pid:ef253fe635f346cbadcfadd1351308b8 -->
const getChartData = () => {
    const canvas = document.querySelector(`#${props.chartId} canvas`);
    if (!canvas) {
        return null;
    }
    const base64Str = canvsa2Image(canvas);
    return {
        base64Str,
        imageName: t('solarPower.efficiencyDistribution'),
        xline: 0,
        yline: 0,
        dim: 'link_relative_ratio',
    };
};

// <!-- Ended by AICoder, pid:ef253fe635f346cbadcfadd1351308b8 -->

defineExpose({
    updateChart,
    resize,
    getChartData,
});
</script>

<style scoped></style>
