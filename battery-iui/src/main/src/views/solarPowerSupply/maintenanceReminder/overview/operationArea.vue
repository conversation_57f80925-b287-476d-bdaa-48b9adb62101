<template>
    <el-button-group class="uedm-timerangeshortcuts time-range-wrap">
        <el-button :class="{ active: timeActive == '1' }" @click.stop="handleBarClick('1')">
            {{ $t('solarPower.daySet', { num: 1 }) }}
        </el-button>
        <el-button :class="{ active: timeActive == '3' }" @click.stop="handleBarClick('3')">
            {{ $t('solarPower.daySet', { num: 3 }) }}
        </el-button>
        <el-button :class="{ active: timeActive == '7' }" @click.stop="handleBarClick('7')">
            {{ $t('solarPower.daySet', { num: 7 }) }}
        </el-button>
        <el-button :class="{ active: timeActive == '10' }" @click.stop="handleBarClick('10')">
            {{ $t('solarPower.daySet', { num: 10 }) }}
        </el-button>
        <el-button :class="{ active: timeActive == '15' }" @click.stop="handleBarClick('15')">
            {{ $t('solarPower.daySet', { num: 15 }) }}
        </el-button>
        <!-- 自定义 -->
        <el-popover ref="popoverCustomRef" :visible="showPopoverCustomTime" placement="bottom" :width="320">
            <el-form :model="form" :rules="rules" ref="formRef">
                <el-form-item
                    :label="$t('solarPower.timeRange')"
                    prop="custom"
                    :inline-message="$t('solarPower.tooltip.timeRangeTips')"
                    :show-message="false"
                >
                    <el-input v-model="form.custom" :min="1" :max="30">
                        <template #append>{{ $t('solarPower.daySet', { num: '' }) }}</template>
                    </el-input>
                    <span class="custom-tips uedm-info-color">{{ $t('solarPower.tooltip.timeRangeTips') }}</span>
                </el-form-item>
            </el-form>

            <div class="uedm-align__right">
                <el-button type="primary" @click="queryCustom">{{ $t('button.search') }}</el-button>
                <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
            </div>
            <template #reference>
                <el-button
                    class="custom"
                    :class="{ active: timeActive == '' }"
                    v-click-outside:[popperPanRef]="onClickOutside"
                    @click.stop="handleBarClick('')"
                >
                    {{ $t('solarPower.unfoldMore') }}
                </el-button>
            </template>
        </el-popover>
    </el-button-group>
</template>

<script setup>
import { ClickOutside as vClickOutside } from 'element-plus';
import { ref, reactive, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import toolDay from '@/util/tool_day.js';

const store = useStore();
const { t } = useI18n();
const emit = defineEmits(['change']);
// eslint-disable-next-line no-undef
const props = defineProps({
    range: {
        type: String,
        default: '3',
    },
});
// popover
const showPopoverCustomTime = ref(false);
const popoverCustomRef = ref();
const popperPanRef = computed(() => {
    return popoverCustomRef.value?.popperRef?.contentRef;
});
const onClickOutside = function () {
    showPopoverCustomTime.value = false;
    form.custom = oldCustom;
    if (form.custom == '' || form.custom === null) {
        //未选时间
        if (oldActive.value != '') {
            timeActive.value = oldActive.value;
        }
    }
};

const timeActive = ref(props.range);
const oldActive = ref(props.range);
let startTime = '';
let endTime = '';
let oldCustom = '';

// <!-- Started by AICoder, pid:e77022455a164e648a917ecee8e14158 -->
const formRef = ref();
const form = reactive({
    custom: [],
});
let rules = {
    custom: [
        {
            required: true,
            trigger: 'blur',
        },
        {
            validator: (rule, value, callback) => {
                if (value && (value < 1 || value > 30)) {
                    callback(new Error(''));
                } else {
                    callback();
                }
            },
            trigger: 'blur,change',
        },
    ],
};
// <!-- Ended by AICoder, pid:e77022455a164e648a917ecee8e14158 -->
const handleBarClick = function (val) {
    timeActive.value = val;
    if (val == '') {
        showPopoverCustomTime.value = true;
        formRef.value.clearValidate();
    } else {
        oldActive.value = val;
        showPopoverCustomTime.value = false;
        formRef.value.resetFields();
        emit('change', {
            range: timeActive.value,
        });
    }
};
const queryCustom = function () {
    //自定义弹出层查询按钮事件
    formRef.value.validate(valid => {
        if (valid) {
            oldActive.value = '';
            let custom = form.custom;
            oldCustom = custom;
            showPopoverCustomTime.value = false;
            emit('change', {
                range: custom,
            });
        }
    });
};

const handleCancel = function () {
    showPopoverCustomTime.value = false;
};
onMounted(() => {});
</script>

<style lang="scss" scoped>
.time-range-wrap {
    margin-right: 20px;
}
.custom-tips {
    flex: 1;
}
</style>
