<template>
    <div class="oper-area">
        <div class="main_rail" @dblclick.stop="addDot($event, dotArr, lineArr)" @click.stop @mousedown.stop>
            <!-- 时间 -->
            <div v-show="showDotOper" class="dot-oper border" @click.stop @mousedown.stop>
                <div class="oper-time">
                    <el-input
                        ref="inputRef"
                        v-model.trim="inputVal"
                        :class="{ err: inputValErr }"
                        @change="timeChange"
                        @input="validateInput"
                    ></el-input>
                    <span style="padding-left: 5px">%</span>
                    <el-icon class="el-icon-close" @click="handleCloseOperDot()"><el-icon-close /></el-icon>
                    <el-button type="text" style="color: rgb(245, 108, 108)" @click="handleDeleteDot">
                        {{ $t('button.delete') }}
                    </el-button>
                </div>
                <div class="down-arrow"></div>
                <div class="down-arrow-child"></div>
            </div>
            <!-- marks   点 -->
            <template v-for="itm in markers" :key="`${itm.index}${itm.indexWidth}`">
                <div class="markers" :style="{ left: itm.indexWidth + 'px' }"></div>
                <div class="marker-text" :style="{ left: itm.indexWidth + 'px' }">{{ itm.index }}{{ itm.unit }}</div>
            </template>
            <!-- 滑  点 -->
            <template v-for="(dot, index) in dotArr" :key="'dot' + dot.id">
                <div
                    class="dot"
                    :class="[curSelectedDot && curSelectedDot.id == dot.id ? 'selected-dot' : '']"
                    :style="{ left: dot.distance + 'px' }"
                    @mousedown="dotMousedown($event, dot, index, dotArr)"
                    @mouseenter="dotFocus(dot)"
                    @mouseleave="dotBlur"
                >
                    <div v-show="curFocusDot && curFocusDot.id == dot.id" class="tips" @mousedown.stop>
                        <span>{{ dot.time }}</span>
                        <div class="down-arrow"></div>
                    </div>
                </div>
            </template>
            <!-- 范围 线段 -->
            <template v-for="(line, index) in lineArr" :key="`${index}${line.state}${line.widthX}`">
                <div
                    class="line"
                    :class="'state_' + line.state"
                    :style="{
                        left: line.leftX + 'px',
                        width: line.widthX + 'px',
                        background: Color[lineArr.length - index],
                    }"
                    @mouseenter="lineFocus(line)"
                    @mouseleave="lineBlur"
                >
                    <div
                        class="select-peak"
                        :style="{
                            left: line.widthX / 2 + 'px',
                            minWidth: $i18n.locale == 'en-US' ? '67px' : '56px',
                            textAlign: 'center'
                        }"
                    >
                        <span :style="{ color: Color[lineArr.length - index] }">
                            {{ $t('solarPower.levelNo', { num: lineArr.length - index }) }}
                        </span>
                    </div>
                    <div v-show="currentFocusline && currentFocusline.id == line.id" class="tips" @mousedown.stop>
                        <span>
                            {{ $t('solarPower.levelNo', { num: lineArr.length - index }) }}
                        </span>:[{{ dotArr[index-1]?.time === -0 ? 0 : dotArr[index-1]?.time || showMin }}%
                        ~{{ dotArr[index]?.time ==- -0 ? 0 : dotArr[index]?.time || showMax }}%)
                        <div class="down-arrow"></div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import { onMounted, ref, watch, computed } from 'vue';
import { Close as ElIconClose, ArrowDown as ElIconArrowDown } from '@element-plus/icons-vue';
import { Color, isPositiveInteger } from '../../util.js';

const props = defineProps({
    data: {
        type: Array,
        default: () => {
            return [];
        },
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['change-level-num']);

const dotArr = ref([]); //点集合
const lineArr = ref([]); //线段集合
const markers = ref([]);

const showMin = ref(-100);
const showMax = ref(100);
const ALLLENGTH = 1600;
const ITEMLENGTH = ALLLENGTH / 20;

watch(
    () => props.data,
    newVal => {
        resetData();
        const newList = formatRangeData(newVal);
        newList.forEach((d, i) => {
            let cell = ALLLENGTH / (showMax.value - showMin.value);
            let leftX = Math.round((d.min - showMin.value) * cell);
            let widthX = Math.round((d.max - d.min) * cell);
            lineArr.value.push({
                leftX,
                widthX,
                state: d.id,
                id: 'line' + leftX,
            });
            if (newList.length - 1 != i) {
                let distance = Math.round((d.max - showMin.value) * cell);
                dotArr.value.push({
                    id: 'dot' + distance,
                    distance,
                    time: d.max,
                });
            }
        });

        // 格式化markers,范围不能平分10分的只显示首尾两端,全长1600
        let spacing = (showMax.value - showMin.value) * 10;
        let spacing100 = (showMax.value - showMin.value) * 100;
        let spacing10 = showMax.value - showMin.value;
        if (ALLLENGTH % spacing10 === 0 || ALLLENGTH % spacing === 0 || ALLLENGTH % spacing100 === 0) {
            for (let i = 0; i <= spacing10 / 10; i++) {
                let _num = showMin.value + 10 * i + '';
                if (_num.includes('.')) {
                    // 有小数点的数字处理
                    _num = Number(_num).toFixed(0);
                    let str = _num + '';
                    if (str.charAt(str.length - 1) == '0') {
                        _num = str.slice(0, str.length - 1);
                    }
                }
                markers.value.push({
                    index: _num,
                    indexWidth: i * ITEMLENGTH,
                    unit: '%',
                });
            }
        } else {
            markers.value = [
                {
                    index: showMin.value,
                    indexWidth: 0,
                    unit: '%',
                },
                {
                    index: showMax.value,
                    indexWidth: ALLLENGTH,
                    unit: '%',
                },
            ];
        }

        console.log(
            '111  markers.value: ',
            markers.value,
            'lineArr.value:',
            lineArr.value,
            'dotArr.value:',
            dotArr.value
        );
    }
);

const showDotOper = ref(false);
const isModified = ref(false);

const operDotDom = ref(null);
const curSelectedDot = ref({
    distance: null,
    time: null,
});
const curIndex = ref(-1);

const clickClose = ref(false);
const inputVal = ref('');
const inputValErr = ref(false);

const isDrop = ref(false);
const curFocusDot = ref(null);
const curDotXposition = ref(0);
const curDotDom = ref(null);

const minMoveX = ref(0);
const maxMoveX = ref(0);

const resetData = () => {
    lineArr.value = [];
    dotArr.value = [];
    markers.value = [];
};

const formatRangeData = data => {
    let newArr = data.sort(compare('min')); // 格式化之后的值
    return newArr;
};
const validateInput = value => {
    /* Started by AICoder, pid:44880981d8fb45b8baf095a8f5e9c794 */
    if (value <= showMin.value) {
        inputVal.value = value.substr(0, 3);
    } else if (value >= showMax.value) {
        inputVal.value = value.substr(0, 2);
    } else if (value.indexOf('.') !== -1) {
        inputVal.value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    }
    /* Ended by AICoder, pid:44880981d8fb45b8baf095a8f5e9c794 */
};

const compare = property => {
    return function (a, b) {
        let value1 = a[property];
        let value2 = b[property];
        return value1 - value2;
    };
};

const timeChange = data => {
    let _data = data;
    // 失去焦点事件晚于点击事件
    setTimeout(() => {
        if (clickClose.value || inputValErr.value) {
            return;
        }
        /* Started by AICoder, pid:3eb5733863144149a24d0432776d9422 */
        const prevDotVal = curIndex.value >= 1 ? Number(dotArr.value[curIndex.value - 1]?.time) : showMin.value;
        const nextDotVal = curIndex.value <= dotArr.value.length - 2
            ? Number(dotArr.value[curIndex.value + 1]?.time)
            : showMax.value;
        if (data <= prevDotVal) {
            inputVal.value = prevDotVal + 1;
            _data = prevDotVal + 1;
        } else if (data >= nextDotVal) {
            inputVal.value = nextDotVal - 1;
            _data = nextDotVal - 1;
        }
        /* Ended by AICoder, pid:3eb5733863144149a24d0432776d9422 */
        isModified.value = true; // 返回二次确认
        let min = showMin.value;
        let max = showMax.value;
        let leftX = (_data - min) * (ALLLENGTH / (max - min));
        operDotDom.value.style.left = leftX + 'px';
        curSelectedDot.value.distance = leftX;
        curSelectedDot.value.time = _data;
        updateLine();
        clickClose.value = false;
    }, 200);
};

const updateLine = () => {
    let i = curIndex.value;
    let preLine = lineArr.value[i];
    let lastLine = lineArr.value[i + 1];
    preLine.widthX = dotArr.value[i].distance - (dotArr.value[i - 1] ? dotArr.value[i - 1].distance : 0);
    lastLine.widthX = (dotArr.value[i + 1] ? dotArr.value[i + 1].distance : ALLLENGTH) - dotArr.value[i].distance;
    lastLine.leftX = dotArr.value[i].distance;
};

const transTimeFromLeftX = data => {
    isModified.value = true; // 返回二次确认
    data = data === ALLLENGTH - 1 ? ALLLENGTH : data;
    data = data === 1 ? 0 : data;
    let result = Number(data) / (ALLLENGTH / (showMax.value - showMin.value)) + showMin.value;
    result = Number(result.toFixed(0));
    return result;
};

const addDot = (e, itemDotArr, itemLineArr) => {
    if (!props.isEdit) {
        return;
    }
    if (itemLineArr.length >= 9) {
        return;
    }
    let targetClass = e.target.className;

    if (targetClass != 'markers') {
        if (!(targetClass.includes('main_rail') || targetClass.includes('line state_'))) {
            return;
        }
    }

    let leftX = e.offsetX;
    if (targetClass != 'main_rail') {
        leftX += e.target.offsetLeft;
    }
    let newDot = null;
    if (!itemDotArr.includes(leftX)) {
        newDot = {
            id: 'dot' + leftX,
            distance: leftX,
            time: transTimeFromLeftX(leftX),
        };
    }
    let newPreIndex = -1;
    if (itemDotArr.length > 0) {
        itemDotArr.forEach((item, index) => {
            if (item.distance < leftX) {
                newPreIndex = index;
            }
        });
    }
    if (newDot) {
        if (newPreIndex == -1 && itemDotArr.length == 0) {
            itemDotArr.push(newDot);
        } else {
            itemDotArr.splice(newPreIndex + 1, 0, newDot);
        }
        addLine(newPreIndex, itemDotArr, itemLineArr);
        emit('change-level-num', lineArr.value.length);
    }
};

const addLine = (index, itemDotArr, itemLineArr) => {
    if (index == -1 && itemLineArr.length == 0) {
        let dot = itemDotArr[0];
        let ele1 = {
            leftX: 0,
            widthX: dot.distance,
            state: 0,
        };
        let ele2 = {
            leftX: dot.distance,
            widthX: ALLLENGTH - dot.distance,
            state: 0,
        };
        lineArr.value = [ele1, ele2];
    } else {
        let dot = itemDotArr[index + 1];
        let oldLine = itemLineArr[index + 1];
        let leftX = itemDotArr[index] ? itemDotArr[index].distance : 0;
        /* Started by AICoder, pid:23573a221d7048a88ea079742e0b1466 */
        let newLine = {
            id: 'line' + leftX, // 使用toString()方法将leftX转换为字符串
            leftX: leftX,
            widthX: dot.distance - (itemDotArr[index] ? itemDotArr[index].distance : 0),
            state: 0,
        };
        if (oldLine) {
            oldLine.leftX = dot.distance;
            oldLine.id = 'line' + oldLine.leftX; 
            oldLine.widthX = (itemDotArr[index + 2] ? itemDotArr[index + 2].distance : ALLLENGTH) - dot.distance;
        }
        /* Ended by AICoder, pid:23573a221d7048a88ea079742e0b1466 */
        itemLineArr.splice(index + 1, 0, newLine);
    }
};

const handleCloseOperDot = () => {
    clickClose.value = true;
    if (curSelectedDot.value) {
        curSelectedDot.value = null;
        showDotOper.value = false; // 设置为false不可移动
    }
};

const showDotOperDialog = item => {
    curFocusDot.value = null;
    showDotOper.value = true;
    inputVal.value = item.time;
    operDotDom.value.style.left = item.distance + 'px';
};

const dotMousedown = (event, item, indexs, itemArr) => {
    if (!props.isEdit) {
        return;
    }
    isDrop.value = true;
    clickClose.value = false;
    showDotOperDialog(item);
    let e = window.event; // 要用event这个对象来获取鼠标的位置
    curDotXposition.value = e.clientX - event.target.offsetLeft;
    curSelectedDot.value = item;
    curDotDom.value = event.target;
    let dotLength = itemArr.length;
    let min = showMin.value;
    let max = showMax.value;
    if (indexs === 0) {
        minMoveX.value = 0;
        maxMoveX.value = itemArr[indexs + 1] ? itemArr[indexs + 1].distance - ALLLENGTH / (max - min) : ALLLENGTH;
    } else if (indexs === dotLength - 1) {
        minMoveX.value = itemArr[indexs - 1].distance + ALLLENGTH / (max - min);
        maxMoveX.value = ALLLENGTH - ALLLENGTH / (max - min);
    } else {
        minMoveX.value = itemArr[indexs - 1].distance + ALLLENGTH / (max - min);
        maxMoveX.value = itemArr[indexs + 1].distance - ALLLENGTH / (max - min);
    }
    curIndex.value = indexs;
};
const currentFocusline = ref(null);
const lineFocus = item => {
    currentFocusline.value = item;
};
const lineBlur = () => {
    currentFocusline.value = null;
};
const dotFocus = item => {
    let selectedId = curSelectedDot.value && curSelectedDot.value.id;
    if (selectedId != item.id) {
        curFocusDot.value = item;
    }
};
const dotBlur = () => {
    curFocusDot.value = null;
};

const handleDeleteDot = () => {
    if (lineArr.value.length <= 3) {
        return;
    }
    if (curSelectedDot.value) {
        let dotArrTmp = dotArr.value;
        let lineArrTmp = lineArr.value;
        dotArrTmp.splice(curIndex.value, 1);
        if (dotArrTmp.length == 0) {
            lineArr.value = [];
        } else {
            let lastLine = lineArrTmp[curIndex.value + 1];
            if (lastLine) {
                let preDot = dotArrTmp[curIndex.value - 1];
                let lastDot = dotArrTmp[curIndex.value];
                lastLine.leftX = preDot ? preDot.distance : 0;
                lastLine.widthX = (lastDot ? lastDot.distance : ALLLENGTH) - lastLine.leftX;
            }
            lineArrTmp.splice(curIndex.value, 1);
            emit('change-level-num', lineArr.value.length);
        }
        curSelectedDot.value = null;
    }
    showDotOper.value = false;
};

const changeShowDotOper = value => {
    showDotOper.value = value;
};

const getRangeList = () => {
    let result = [];
    const defaultItem = {
        containMin: true,
        containMax: false,
        unit: '%',
    };
    let valueList = dotArr.value.map(item => item.time);
    lineArr.value.forEach((alarm, i) => {
        const id = lineArr.value.length - i;
        if (i == 0) {
            result.push({
                id,
                min: showMin.value,
                max: valueList[i],
                ...defaultItem,
            });
        } else if (i == lineArr.value.length - 1) {
            result.push({
                id,
                efficiencyLevels: id,
                min: valueList[i - 1],
                max: showMax.value,
                ...defaultItem,
            });
        } else {
            result.push({
                id,
                efficiencyLevels: id,
                min: valueList[i - 1],
                max: valueList[i],
                ...defaultItem,
            });
        }
    });
    return result;
};
onMounted(() => {
    setTimeout(() => {
        operDotDom.value = document.querySelector('.main_rail .dot-oper');
        document.onmousemove = e => {
            if (isDrop.value) {
                document.body.style.cursor = 'grabbing';
                let moveX = e.clientX - curDotXposition.value; // 得到距离左边移动距离
                if (moveX < minMoveX.value) {
                    moveX = minMoveX.value + 1;
                } else if (moveX > maxMoveX.value) {
                    moveX = maxMoveX.value - 1;
                }

                curSelectedDot.value.distance = moveX;
                let time = transTimeFromLeftX(moveX);
                curSelectedDot.value.time = time;
                operDotDom.value.style.left = moveX + 'px';
                inputVal.value = time;
                updateLine();
            }
        };
        document.onmouseup = e => {
            if (curDotDom.value) {
                curDotDom.value = null;
                isDrop.value = false; // 设置为false不可移动
                document.body.style.cursor = 'default';
            }
        };
    });
});

defineExpose({
    getRangeList,
    changeShowDotOper,
});
</script>

<style lang="scss" scoped>
.oper-area {
    padding: 29px 0 11px;
    .main_rail {
        position: relative;
        width: 1600px;
        height: 6px;
        border-radius: 3px;
        background-color: #bfbfbf;
        margin: 30px auto 40px;
        user-select: none;
        cursor: pointer;
        .dot-oper {
            position: absolute;
            width: 150px;
            height: 56px;
            bottom: 18px;
            border-radius: 4px;
            transform: translateX(-50%);
            cursor: default;
            z-index: 9999;
            .el-date-editor.el-input {
                width: 116px;
            }

            .oper-time {
                position: relative;
                width: 80px;
                padding: 12px 16px;
                display: flex;
                align-items: center;
                i {
                    position: absolute;
                    right: 2px;
                    top: 2px;
                    cursor: pointer;
                    font-size: 14px;
                }
                .el-button {
                    font-size: 14px;
                    position: absolute;
                    right: -40px;
                    top: 12px;
                    cursor: pointer;
                }
            }

            .down-arrow {
                position: absolute;
                left: 50%;
                bottom: -5px;
                height: 0;
                width: 0;
                transform: translateX(-50%);
                border-top: 5px solid #dcdfe6;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
            }
            .down-arrow-child {
                position: absolute;
                left: 50%;
                bottom: -4px;
                height: 0;
                width: 0;
                transform: translateX(-50%);
                border-top: 4px solid #fff;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
            }
            :deep(.err) .el-input__inner {
                border: 1px solid #f56c6c;
            }
        }
        .markers {
            position: absolute;
            user-select: none;
            width: 6px;
            height: 6px;
            border-radius: 3px;
            background-color: #fff;
            transform: translateX(-50%);
            z-index: 13;
        }
        .marker-text {
            position: absolute;
            user-select: none;
            top: 18px;
            font-size: 14px;
            color: #737373;
            transform: translateX(-50%);
            z-index: 13;
        }
        .dot {
            position: absolute;
            width: 16px;
            height: 16px;
            font-size: 14px;
            background-color: #fff;
            border-radius: 100%;
            border: 2px solid #1993ff;
            transform: translateX(-50%) translateY(-7px);
            z-index: 13;
            .tips {
                position: absolute;
                left: 50%;
                bottom: 24px;
                color: #fff;
                user-select: none;
                padding: 6px;
                background-color: #000;
                transform: translateX(-50%);
                border-radius: 4px;
                .down-arrow {
                    position: absolute;
                    left: 50%;
                    bottom: -4px;
                    height: 0;
                    width: 0;
                    transform: translateX(-50%);
                    border-top: 5px solid #000;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                }
            }
        }
        .dot.selected-dot {
            background-color: #1993ff;
        }
        .line {
            position: absolute;
            height: 6px;
            background-color: red;
            z-index: 11;
            .select-peak {
                position: absolute;
                bottom: 16px;
                transform: translateX(-50%);
                min-width: 56px;
            }
            .tips {
                position: absolute;
                left: 50%;
                bottom: 24px;
                color: #fff;
                user-select: none;
                padding: 6px;
                background-color: #000;
                transform: translateX(-50%);
                border-radius: 4px;
                white-space: nowrap;
                .down-arrow {
                    position: absolute;
                    left: 50%;
                    bottom: -4px;
                    height: 0;
                    width: 0;
                    transform: translateX(-50%);
                    border-top: 5px solid #000;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                }
            }
        }
        .state_4 {
            background-color: rgb(134, 188, 219);
        }
        .state_3 {
            background-color: rgb(233, 212, 38);
        }
        .state_2 {
            background-color: rgb(222, 143, 64);
        }
        .state_1 {
            background-color: rgb(222, 64, 64);
        }
        .state_0 {
            background-color: rgb(191, 191, 191);
        }
    }
}
html.dark {
    .oper-area .main_rail .dot .tips{
        color: #000;
        background-color: #fff;
        .down-arrow {
            border-top: 5px solid #fff;
        }
    }
    .oper-area .main_rail .line .tips{
        color: #000;
        background-color: #fff;
        white-space: nowrap;
        .down-arrow {
            border-top: 5px solid #fff;
        }
    }

}
</style>
