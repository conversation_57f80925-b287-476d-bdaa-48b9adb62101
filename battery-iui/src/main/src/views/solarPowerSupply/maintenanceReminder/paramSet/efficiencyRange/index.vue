<template>
    <div>
        <div v-if="!isEdit" class="operate-button">
            <el-button @click="editHandler">
                {{ $t('maintenanceReminder.button.edit') }}
            </el-button>
        </div>
        <div>
            <el-alert
                v-if="isEdit"
                :title="$t('maintenanceReminder.fields.setLevelTip')"
                type="info"
                show-icon
                class="alert-wrap"
                :closable="false"
            ></el-alert>
            <slider-range
                ref="sliderRangeRef"
                :is-edit="isEdit"
                :data="rangeData"
                @change-level-num="changeLevelNum"
            ></slider-range>
            <span
                style="color: #a9acb0"
            >
                {{ $t('solarPower.levelNo', { num: maxLevel + 1 }) }}
            </span>
            :{{ $t('maintenanceReminder.fields.level9Tip') }}
            <div style="margin-top: 24px">
                <el-form v-if="isEdit" ref="formRef" :model="ruleform" :rules="rules">
                    <el-form-item
                        :label="$t('maintenanceReminder.fields.levelToBeMaintained')"
                        prop="levelToBeMaintained"
                        required
                    >
                        <el-select v-model="ruleform.levelToBeMaintained">
                            <el-option
                                v-for="item in LevelOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div v-else>
                    <span>{{ $t('maintenanceReminder.fields.levelToBeMaintained') }}：</span>
                    <span>{{ levelToBeMaintainedText || '--' }}</span>
                </div>
            </div>

            <div v-if="isEdit" class="bottom-btns">
                <el-button type="primary" @click="saveConfigData">{{ $t('button.confirm') }}</el-button>
                <el-button @click="cancelHandle">{{ $t('button.cancel') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, reactive, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import HTTP from '@/util/httpService';
import SliderRange from './sliderRange.vue';
import { Color, isPositiveInteger } from '../../util.js';

const { t } = useI18n();

const sliderRangeRef = ref();

const isEdit = ref(false);
const rangeData = ref([]);

const formRef = ref();
const ruleform = reactive({
    levelToBeMaintained: '',
});

const rules = reactive({
    levelToBeMaintained: [{ required: true, message: t('placeholder.select'), trigger: 'change' }],
});

const levelToBeMaintainedText = ref('');

const maxLevel = ref(0);
const LevelOptions = computed(() => {
    let newList = [];
    for (let i = -1; i < maxLevel.value; i++) {
        newList.push({
            id: `${maxLevel.value - i}`,
            name: t('maintenanceReminder.fields.levelOptions', { num: maxLevel.value - i }),
        });
    }
    return newList;
});

const editHandler = () => {
    isEdit.value = true;
};
const cancelHandle = () => {
    sliderRangeRef.value.changeShowDotOper(false);
    isEdit.value = false;
    queryRangData();
};

//  Started by AICoder, pid:0ee63d319e954708a8253c96298e4184
const changeLevelNum = num => {
    maxLevel.value = num;
    ruleform.levelToBeMaintained = '';
};
//  Ended by AICoder, pid:0ee63d319e954708a8253c96298e4184

const saveConfigData = () => {
    formRef.value
        .validate()
        .then(() => {
            handleSave();
        })
        .catch(() => {});
};

const handleSave = () => {
    const sliderRangeList = sliderRangeRef.value.getRangeList();
    console.log('111 sliderRangeList:', sliderRangeList);
    HTTP.request('saveRangData', {
        method: 'post',
        urlParam: {
            maintenceLevel: ruleform.levelToBeMaintained,
        },
        data: sliderRangeList.map(item => {
            const {id, ...others} = item;
            return {
                id,
                efficiencyLevels: {...others}
            }
        }),
        complete: resp => {
            if (resp.code === 0) {
                ElMessage.success(t('tipMessage.saveSuccess'));
                cancelHandle();
            }
        },
        error: () => {},
    });
};

const queryRangData = () => {
    HTTP.request('queryRangData', {
        method: 'get',
        complete: resp => {
            if (resp.code === 0 && resp.data) {
                const { maintenceLevel, sliderRangeList } = resp.data;
                ruleform.levelToBeMaintained = `${maintenceLevel}`;
                const efficiencyLevelLen = sliderRangeList.length;
                maxLevel.value = efficiencyLevelLen;
                if (efficiencyLevelLen > 0) {
                    levelToBeMaintainedText.value = t('maintenanceReminder.fields.levelOptions', {
                        num: maintenceLevel,
                    });
                    rangeData.value = sliderRangeList.map(item => {
                        const { id, efficiencyLevels } = item;
                        return {
                            id: id,
                            min: efficiencyLevels.min,
                            max: efficiencyLevels.max,
                        };
                    });
                } else {
                    levelToBeMaintainedText.value = '';
                    rangeData.value = [];
                }
            }
        },
        error: err => {},
    });
};

onMounted(() => {
    queryRangData();
});
</script>

<style scoped>
.operate-button {
    text-align: right;
}
.alert-wrap {
    margin-bottom: 20px;
    width: 100%;
}
.bottom-btns {
    text-align: center;
    padding-top: 30px;
    button {
        padding: 8px 24px;
    }
}
</style>
../../util