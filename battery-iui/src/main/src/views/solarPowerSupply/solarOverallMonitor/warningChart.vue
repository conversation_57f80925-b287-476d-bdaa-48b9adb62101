<template>
    <div ref="box" class="containerBg">
        <div v-loading="loading" class="uedm-title-tab title-container">
            <span class="title-left" @click="gotoWarningDetail()">
                {{ $t('solarPower.alarm') }}
                <span v-if="showNavArrow" class="plx-ico-right-16 title-arrow"></span>
            </span>
        </div>
        <div class="z-block">
            <div class="z-block-content horizontal-layout" style="padding-top: 0">
                <ul style="display: flex; flex-direction: row; align-items: center; flex: 1">
                    <li class="icon-contaner">
                        <i class="icon-warning-sites"></i>
                    </li>
                    <li>
                        <div class="warning-sites-title">{{ $t('solarPower.warningSitesCount') }}</div>
                        <div class="warning-sites-val">{{ warningSiteCount }}</div>
                    </li>
                    <li>
                        <div class="sep-line"></div>
                    </li>
                    <li class="chart-container">
                        <div :id="chartId" class="echartsItem" :style="{ height: 212 + 'px' }" />
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script>
import { isEmpty } from '../../../util/common.js';
import domToImage from 'dom-to-image-more';

const chartColor = {
    default: {
        title: '#595959',
        total: '#000',
        totalTitle: '#000',
        noData: '#A8ABB2',
        background: 'rgb(48, 49, 51, 0.75)',
        border: 'transparent',
        color: '#fff',
    },
    dark: {
        title: '#d9d9d9',
        total: '#e5eaf3',
        totalTitle: '#cfd3dc',
        noData: '#666666',
        background: 'rgb(48, 49, 51, 0.75)',
        border: 'transparent',
        color: '#fff',
    },
};

const MIN_TRUNCATE_WIDTH = 660;

export default {
    components: {},
    emits: ['gotoWarningDetail'],
    props: {
        widthChange: {
            type: Boolean,
            default: false,
        },
        config: {
            type: Object,
            default: null,
        },
        chartId: {
            type: String,
            default: '',
        },
        peakPlatFilter: {
            type: Array,
            default: function () {
                return [];
            },
        },
        colorConfig: {
            type: Object,
            default: function () {
                return {};
            },
        },
    },
    data() {
        return {
            loading: false,
            showCard: true, // 为了解决调整显示顺序后轮播图的指示器位置更新，不然轮播图顺序看上去没有更新
            chartObj: null, // 图表实例
            showNavArrow: true,
            lastChartDivWidth: 0,
        };
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        carouselHeight() {
            return '270px';
        },
        winResize() {
            return this.$store.getters.getResize;
        },
        warningSiteCount() {
            return this.config?.alarmSiteCount ?? '--';
        },
    },
    watch: {
        config() {
            setTimeout(() => {
                this.initCarousel();
            }, 100);
        },
        winResize: function () {
            if (this.chartDivWidth() < MIN_TRUNCATE_WIDTH || this.lastChartDivWidth < MIN_TRUNCATE_WIDTH) {
                this.initCarousel();
            }
            this.lastChartDivWidth = this.chartDivWidth();

            this.chartResize();
        },
        widthChange() {
            this.chartResize();
        },
    },
    created() {},
    mounted() {
        this.lastChartDivWidth = this.chartDivWidth();
        this.initCarousel();
    },
    beforeUnmount() {
        this.chartObj && this.chartObj.dispose();
    },
    methods: {
        initCarousel() {
            const container = document.getElementById(this.chartId);
            if (container) {
                const myEchart = this.$echarts.init(container, this.isDark ? 'dark' : 'light', 270);
                myEchart.clear();
                const options = this.getChartOption(this.config);
                myEchart.setOption(options);
                this.chartObj = myEchart;
                // 监听父级dom变化
                const resizeOb = new ResizeObserver(entries => {
                    for (let entry of entries) {
                        if (this.$echarts.getInstanceByDom(entry.target)) {
                            this.$echarts.getInstanceByDom(entry.target).resize();
                        }
                    }
                });
                resizeOb.observe(document.getElementById(this.chartId));
            }
        },
        chartDivWidth() {
            return this.$refs.box?.offsetWidth ?? 0;
        },
        getChartOption() {
            if (isEmpty(this.config)) {
                return this.getEmptyConfigOption();
            }

            return this.getValidOption();
        },
        getEmptyConfigOption() {
            const colorType = this.isDark ? 'dark' : 'default';
            return {
                series: [
                    {
                        type: 'pie',
                        center: ['28%', '50%'],
                        radius: ['60vh', '72vh'],
                        avoidLabelOverlap: true,
                        silent: true,
                        label: { show: false },
                        data: [
                            {
                                value: 1,
                                name: '',
                                itemStyle: {
                                    color: this.isDark ? '#363637' : '#F5F7FA',
                                },
                            },
                        ],
                    },
                ],
                backgroundColor: 'transparent',
                graphic: {
                    type: 'text',
                    left: '60%',
                    top: 'middle',
                    silent: true,
                    invisible: false,
                    style: {
                        fill: chartColor[colorType].noData,
                        text: this.$t('common.noData'),
                        fontFamily: 'Microsoft Yahei',
                        fontSize: '28px',
                    },
                },
            };
        },

        getValidOption() {
            const { centerTitle, alarmCount, serieses } = this.config ?? {};

            const colorType = this.isDark ? 'dark' : 'default';
            const defaultColor = this.isDark ? '#363637' : '#F5F7FA';
            const isValAllEmpty = serieses?.every(item => isEmpty(item.data?.value)) ?? true;
            const legendData =
                serieses?.map((item, index) => {
                    const color = this.colorConfig[item.colorKey];
                    return {
                        name: item.seriesName,
                        itemStyle: {
                            color: color,
                        },
                    };
                }) ?? [];

            return {
                tooltip: {
                    trigger: 'item',
                    backgroundColor: chartColor[colorType].background,
                    borderColor: chartColor[colorType].border,
                    textStyle: { color: chartColor[colorType].color },
                },
                legend: {
                    orient: 'vertical',
                    left: '54%',
                    y: 'center',
                    itemWidth: 12,
                    itemHeight: 12,
                    selectedMode: false,
                    textStyle: {
                        width: this.getLegendWidth(),
                        overflow: 'truncate',
                        fontSize: '14',
                        color: chartColor[colorType].totalTitle,
                    },
                    data: legendData,
                    formatter: name => {
                        const aimSeries = serieses?.find(series => series.seriesName === name) ?? {};
                        const seriesName = aimSeries.seriesName ?? '--';
                        const value = aimSeries.data?.value ?? '--';
                        const percent = aimSeries.data?.percent ?? '--';

                        return [`${seriesName}: ${value} / ${percent}`];
                    },
                },
                backgroundColor: 'transparent',
                graphic: [
                    {
                        type: 'group',
                        top: '43%',
                        left: '28%%',
                        bounding: 'raw',
                        children: [
                            {
                                type: 'text',
                                top: '30%',
                                bounding: 'raw',
                                style: {
                                    fill: chartColor[colorType].total,
                                    text: `${alarmCount ?? '--'}`,
                                    textAlign: 'center',
                                    lineHeight: 20,
                                    fontSize: 18,
                                    fontWeight: '700',
                                    color: '#303133',
                                },
                            },
                            {
                                type: 'text',
                                top: '20%',
                                bounding: 'raw',
                                style: {
                                    fill: chartColor[colorType].totalTitle,
                                    text: `\n\n${centerTitle ?? '--'}`,
                                    textAlign: 'center',
                                    color: '#606266',
                                    fontSize: 14,
                                },
                            },
                        ],
                    },
                ],
                series: [
                    {
                        type: 'pie',
                        center: ['28%', '50%'],
                        radius: ['60vh', '72vh'],
                        avoidLabelOverlap: true,
                        label: {
                            show: false,
                        },
                        silent: isValAllEmpty,
                        data:
                            serieses?.map((item, index) => {
                                const color = isValAllEmpty
                                    ? defaultColor
                                    : this.colorConfig[item.colorKey] ?? defaultColor;
                                return {
                                    value: isValAllEmpty ? '1' : item.data.value,
                                    name: item.seriesName,
                                    itemStyle: {
                                        color: color,
                                    },
                                };
                            }) ?? [],
                    },
                ],
            };
        },
        async getWarningChartData() {
            this.showNavArrow = false;
            try {
                const viewRef = this.$refs.box;
                if (!viewRef) {
                    return;
                }
                const base64Str = await domToImage.toPng(viewRef);
                const imageData = {
                    base64Str: base64Str,
                    imageName: this.$t('solarPower.alarm'),
                    xline: 0,
                    yline: 270,
                    dim: 'summary',
                };
                this.showNavArrow = true;
                return imageData;
            } catch (error) {
                console.log(error);
                this.showNavArrow = true;
                return null;
            }
        },
        getLegendWidth() {
            if (this.chartDivWidth() >= MIN_TRUNCATE_WIDTH) {
                return void 0;
            }
            return this.chartDivWidth() * 0.33 - 32;
        },
        chartResize() {
            this.chartObj?.resize();
        },
        gotoWarningDetail() {
            this.$emit('gotoWarningDetail');
        },
    },
};
</script>

<style lang="scss" scoped>
.echartsItem {
    display: flex;
    flex: 1;
}
.containerBg {
    background-color: white;
    height: 100%;
}

.horizontal-layout {
    display: flex;
    flex-direction: row;
}

i.icon-warning-sites {
    display: block;
    width: 40px;
    height: 40px;
    margin-right: 8px;
    margin-left: 8px;
    background: url('../../../assets/img/icon-warning-sites.png') no-repeat center;
}

.sep-line {
    width: 1px;
    height: 160px;
    background-color: #eff2f7;
    margin-left: 12px;
}

.warning-sites-title {
    color: #606266;
    font-size: 14px;
    line-height: 16px;
    margin-bottom: 12px;
}
.warning-sites-val {
    color: #303133;
    font-size: 24px;
    line-height: 16px;
}

.dark {
    .warning-sites-title {
        color: #cfd3dc;
    }
    .warning-sites-val {
        color: #e5eaf3;
    }

    .sep-line {
        background-color: #363637;
    }

    .uedm-title-tab {
        border-color: #414243;
    }

    .title-left {
        color: #e5eaf3;
    }
    .containerBg {
        background-color: #141414;
    }
}

.title-container {
    display: flex;
    align-items: center;
    height: 56px;
}
.title-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    cursor: pointer;
    &:hover {
        color: #409eff;
    }
}

.title-arrow {
    font-weight: 800;
    font-size: 20px;
    cursor: pointer;
}

.chart-container {
    display: flex;
    flex: 1;
}
</style>
