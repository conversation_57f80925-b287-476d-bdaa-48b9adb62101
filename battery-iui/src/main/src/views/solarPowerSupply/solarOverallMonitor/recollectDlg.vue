<template>
    <div>
        <el-dialog
            v-model="dialogVisible"
            :title="$t('solarPower.siteRecollection', { site: currSite?.name ?? '' })"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @close="clearAndClose"
            width="688px"
        >
            <div v-loading="loading">
                <div class="tip-container top-tip-container">
                    <span class="icon-info-tip"></span>
                    <span>
                        {{
                            $t('solarPower.recollectionCount', {
                                currCount: formatStr(currRecollectCount),
                                maxCount: formatStr(maxRecollectCount),
                            })
                        }}
                    </span>
                </div>
                <div class="time-form-container">
                    <el-form
                        ref="timeFormRef"
                        :model="timeForm"
                        :rules="timeRangeRules"
                        class="time-form"
                        :size="formSize"
                    >
                        <el-form-item prop="timeRange" :label="$t('solarPower.recollectionTime')" required>
                            <el-date-picker
                                v-if="showDatePicker"
                                v-model="timeForm.timeRange"
                                type="datetimerange"
                                range-separator="-"
                                :default-time="defaultTime"
                                :start-placeholder="$t('datetimePicker.startTime')"
                                :end-placeholder="$t('datetimePicker.endTime')"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                popper-class="only-sel-hour-time-range-picker"
                                :disabled-date="disabledDate"
                                @change="timeChange"
                            />
                        </el-form-item>
                    </el-form>
                    <span
                        class="log-btn-container"
                        :style="`color:${logBtnColor};cursor:${logBtnCursor}`"
                        @click="expandRecLog()"
                    >
                        {{ $t('solarPower.recollectionRecord') }}
                        <span
                            v-if="recLogExpanded"
                            class="plx-ico-up-16 expand-arrow"
                            :style="`color:${logBtnColor}`"
                        ></span>
                        <span v-else class="plx-ico-down-16 expand-arrow" :style="`color:${logBtnColor}`"></span>
                    </span>
                </div>
                <div class="dialog-content" v-if="recLogExpanded">
                    <el-table :data="logList" row-key="id">
                        <el-table-column prop="index" :formatter="formatColumn" min-width="30" />
                        <el-table-column
                            prop="gmtCreate"
                            :formatter="formatOperateTime"
                            :label="$t('battery.fields.operTime')"
                            min-width="160"
                        />
                        <el-table-column
                            prop="startTime"
                            :formatter="formatColumn"
                            :label="$t('datetimePicker.startTime')"
                            min-width="160"
                        />
                        <el-table-column
                            prop="endTime"
                            :formatter="formatColumn"
                            :label="$t('datetimePicker.endTime')"
                            min-width="160"
                        />
                    </el-table>
                </div>
                <div v-if="recLogExpanded && showTip" class="tip-container bottom-tip-container">
                    <span class="icon-info-tip"></span>
                    <span>{{ this.$t('solarPower.gotoLogManageLeft') }}</span>
                    <a
                        class="tipHref"
                        :href="logManageHref"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {{ $t('solarPower.gotoLogManageCenter') }}
                    </a>
                    <span>{{ $t('solarPower.gotoLogManageRight') }}</span>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" :disabled="!timeValid" @click="recollectionWithSite">
                        {{ $t('button.confirm') }}
                    </el-button>
                    <el-button @click="clearAndClose">{{ $t('button.cancel') }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import HTTP from '@/util/httpService';
import { $emit } from '@/utils/gogocodeTransfer';
import moment from 'moment';
import { fmtStrEmpty, isArrayEmpty, isEmpty, isStrEmpty } from '../../../util/common';

export default {
    components: {},
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        currSite: {
            type: Object,
            default: function () {
                return null;
            },
        },
    },
    data() {
        return {
            loading: true,
            showTip: false,
            timeForm: {
                timeRange: ['', ''],
            },
            timeRangeRules: {
                timeRange: [
                    {
                        required: true,
                        message: this.$t('tipMessage.pleaseSelectRange'),
                        trigger: ['blur', 'change'],
                    },
                    {
                        validator: this.timeRangeCheck,
                        trigger: ['blur', 'change'],
                    },
                ],
            },
            currRecollectCount: null,
            maxRecollectCount: null,
            timeValid: true,
            recLogExpanded: false,
            logList: [],
            showDatePicker: true,
        };
    },
    watch: {
        visible(vis) {
            if (!vis) {
                return;
            }
            this.getRecollectionLog();
            this.getRecollectSiteNum();
            const site = this.currSite;
            if (isEmpty(site)) {
                return;
            }

            const endTime = moment().format('YYYY-MM-DD');
            const sudDays = 7 - 1;
            const startTime = moment(endTime).subtract(sudDays, 'days').format('YYYY-MM-DD');
            const fmtStartTime = moment(startTime).format('YYYY-MM-DD 00:00:00');
            const fmtEndTime = moment(endTime).format('YYYY-MM-DD 23:59:59');
            this.timeForm.timeRange = [fmtStartTime, fmtEndTime];
            this.$refs.timeFormRef?.validateField('timeRange');
        },
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                $emit(this, 'update:visible', value);
            },
        },
        logManageHref() {
            let _location = top.location;
            let url = `${_location.origin}${_location.pathname}#/_otcp-log-query`;
            return url;
        },
        dlgTitle() {
            return this.currSite?.name;
        },
        isLogBtnDisabled() {
            return isArrayEmpty(this.logList);
        },
        logBtnColor() {
            return this.isLogBtnDisabled ? '#bfbfbf' : '#409eff';
        },
        logBtnCursor() {
            return this.isLogBtnDisabled ? 'not-allowed' : 'pointer';
        },
        defaultTime() {
            // 以下2个设置只有时间部分起作用，前面日期可以写死
            const defStartTime = this.timeForm.timeRange?.[0] ?? '2023-01-01 00:00:00';
            const defEndTime = this.timeForm.timeRange?.[1] ?? '2023-01-01 23:59:59';
            const defTime = [new Date(defStartTime), new Date(defEndTime)];
            return defTime;
        },
    },
    created() {},
    mounted() {},
    emits: ['hide', 'refreshSiteList'],
    methods: {
        timeRangeCheck(rule, value, callback) {
            if (isEmpty(value) || isStrEmpty(value[0]) || isStrEmpty(value[1])) {
                this.timeValid = false;
                return callback();
            }

            const startTime = `${value[0]}`;
            const endTime = `${value[1]}`;
            // 最多选则7天范围内的时间
            const maxDiffMs = 168 * 60 * 60 * 1000;
            if (moment(endTime).diff(moment(startTime)) > maxDiffMs) {
                this.timeValid = false;
                return callback(new Error(this.$t('solarPower.recollectionRangeTip')));
            } else {
                this.timeValid = true;
                return callback();
            }
        },
        /* Started by AICoder, pid:y7906w5b1772c071400009b510d6d1216b4338ec */
        disabledDate(time) {
            if (time.getTime() > Date.now()) {
                return true;
            }
            // 最多选择7天范围时间
            const maxDiffMs = 7 * 24 * 60 * 60 * 1000;
            return moment().diff(moment(time)) > maxDiffMs;
        },
        /* Ended by AICoder, pid:y7906w5b1772c071400009b510d6d1216b4338ec */
        formatStr(str) {
            return fmtStrEmpty(str);
        },
        expandRecLog() {
            if (this.isLogBtnDisabled) {
                return;
            }
            this.recLogExpanded = !this.recLogExpanded;
        },
        timeChange() {
            if (isEmpty(this.timeForm.timeRange)) {
                this.timeValid = false;
            }
            this.showDatePicker = false;
            this.$nextTick(() => {
                this.showDatePicker = true;
            });
        },
        recollectionWithSite() {
            if (
                !isStrEmpty(this.currRecollectCount) &&
                !isStrEmpty(this.maxRecollectCount) &&
                this.currRecollectCount >= this.maxRecollectCount
            ) {
                this.showMsg(this.$t('tipMessage.recollectSiteMaxTip'));
                return;
            }

            this.loading = true;

            const siteId = this.currSite?.siteId ?? '';
            const startTime = this.timeForm.timeRange[0];
            const endTime = this.timeForm.timeRange[1];
            const param = {
                siteIdList: [siteId],
                startTime: startTime,
                endTime: endTime,
                pointList: [],
            };
            HTTP.request('recollectionWithSite', {
                method: 'post',
                data: param,
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0) {
                        this.showMsg(this.$t('tipMessage.operationSuccsee'), false);
                        this.clearAndClose();
                    } else if (resp.code === -1) {
                        this.showMsg(this.$t('tipMessage.operationFailure'));
                    } else if (resp.message) {
                        this.showMsg(resp.message);
                    } else {
                        this.showMsg(this.$t('tipMessage.operationFailure'));
                    }
                    $emit(this, 'refreshSiteList');
                },
                error: resp => {
                    this.loading = false;
                    this.showMsg(this.$t('tipMessage.operationFailure'));
                    $emit(this, 'refreshSiteList');
                },
            });
        },
        getRecollectionLog() {
            const siteId = this.currSite?.siteId ?? '';
            const param = {
                siteIdList: siteId,
            };
            HTTP.request('getRecollectionLog', {
                method: 'get',
                data: param,
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0) {
                        this.logList = (resp.data ?? []).map((item, index) => {
                            return {
                                ...item,
                                index: index + 1,
                            };
                        });

                        const maxShowLogCount = 5;
                        if (this.logList.length > maxShowLogCount) {
                            this.showTip = true;
                            this.logList = this.logList.slice(0, maxShowLogCount);
                        }
                    } else if (resp.code === -100) {
                        this.showMsg(this.$t('tipMessage.paramIsNull'));
                    } else {
                        this.showMsg(this.$t('tipMessage.requestError'));
                    }
                },
                error: resp => {
                    this.loading = false;
                    this.showMsg(this.$t('tipMessage.requestError'));
                },
            });
        },
        getRecollectSiteNum() {
            this.loading = true;
            HTTP.request('getRecollectSiteNum', {
                method: 'get',
                data: {},
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0) {
                        this.currRecollectCount = fmtStrEmpty(resp.data?.currentRecollectionSiteNum);
                        this.maxRecollectCount = fmtStrEmpty(resp.data?.concurrentSitesAcrossTheEntireNetwork);
                    } else if (resp.code === -100) {
                        this.showMsg(this.$t('tipMessage.paramIsNull'));
                    } else {
                        this.showMsg(this.$t('tipMessage.requestError'));
                    }
                },
                error: resp => {
                    this.loading = false;
                    this.showMsg(this.$t('tipMessage.requestError'));
                },
            });
        },
        showMsg(msg, isError = true) {
            this.$message({
                message: msg,
                duration: 5000,
                showClose: true,
                type: isError ? 'error' : 'success',
            });
        },
        formatColumn(row, column, cellValue, index) {
            return fmtStrEmpty(cellValue);
        },
        formatOperateTime(row, column, cellValue, index) {
            if (isStrEmpty(cellValue)) {
                return '--';
            }
            return moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
        },
        clearAndClose() {
            this.loading = false;
            this.showTip = false;
            this.timeForm = {
                timeRange: ['', ''],
            };
            this.currRecollectCount = null;
            this.maxRecollectCount = null;
            this.timeValid = true;
            this.recLogExpanded = false;
            this.logList = [];
            this.dialogVisible = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.tip-container {
    display: flex;
    height: 38px;
    background-color: #ecf5ff;
    border-radius: 2px;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    font-size: 12px;
}

.top-tip-container {
    margin-bottom: 8px;
}

.bottom-tip-container {
    margin-bottom: 12px;
    margin-top: 8px;
}

.dark {
    .tip-container {
        background-color: #18222c;
    }
}

.tipHref {
    color: rgba(64, 158, 255, 1);
}

.icon-info-tip {
    display: block;
    width: 14px;
    height: 14px;
    margin-right: 8px;
    background: url('../../../assets/img/icon-info.png') no-repeat center;
}

.time-form-container {
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    justify-content: space-between;
}

.time-form {
    width: 430px;
}

.log-btn-container {
    display: flex;
    padding-top: 6px;
    padding-right: 16px;
    word-break: break-all;
}

.expand-arrow {
    margin-top: 3px;
}

:deep(.el-dialog) .el-dialog__body {
    padding-bottom: 0;
}
</style>