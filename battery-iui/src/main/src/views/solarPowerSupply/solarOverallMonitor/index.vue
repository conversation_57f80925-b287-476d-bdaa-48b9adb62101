<template>
    <div>
        <!-- Started by AICoder, pid:s7660x6389gd86f14e2e0b6d7043ee3cd279c97b -->
        <split-line ref="SplitLine" :view-h="height" :padding-right="0" @styleControl="splitChange">
            <template #left>
                <div class="left-content" style="padding-top: 16px">
                    <left-tree :height="height - 16" @nodeClick="handleClick"></left-tree>
                </div>
            </template>
            <template #right>
                <div class="right-content">
                    <div v-if="!nodeId" class="pageBlankTips" :style="{ height: `${height}px` }">
                        <div class="pageBlankTipsContent">
                            <i class="el-icon-warning"></i>
                            <div>{{ $t('common.noData') }}</div>
                        </div>
                    </div>
                    <div
                        v-if="!rights['pv.ppa.solar.monitor.view']"
                        class="pageBlankTips"
                        :style="{ height: `${height}px` }"
                    >
                        <div class="pageBlankTipsContent">
                            <i class="el-icon-warning"></i>
                            <div>{{ $t('common.noRight') }}</div>
                        </div>
                    </div>
                    <monitor-statistics
                        v-show="rights['pv.ppa.solar.monitor.view'] && nodeId"
                        v-if="nodeId"
                        ref="overview"
                        :height="height"
                        :node-id="nodeId"
                        :path-names="pathNames"
                        :is-authorized="isAuthorized"
                        :width-change="widthChange"
                        :show-correlation-info="showCorrelationInfo"
                        @fullScreen="hideColumnFilter"
                    ></monitor-statistics>
                </div>
            </template>
        </split-line>
        <!-- Ended by AICoder, pid:s7660x6389gd86f14e2e0b6d7043ee3cd279c97b -->
    </div>
</template>
<script>
import SplitLine from '@uedm/uedm-ui/src/components/SplitLineSide.vue';
import SolarMonitorTree from './solarMonitorTree.vue';
import MonitorStatistics from './monitorStatistics.vue';
/* Started by AICoder, pid:de9f0z8b3e407ac146170b99301b7e8547a85b40 */
export default {
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    components: {
        'split-line': SplitLine,
        'left-tree': SolarMonitorTree,
        'monitor-statistics': MonitorStatistics,
    },
    data() {
        return {
            nodeId: '',
            historyType: '',
            pathNames: [],
            expandedNodeIds: [],
            batteryId: '',
            widthChange: false, // 用户拖拽splitLine,图表宽度自适应
            showCorrelationInfo: false,
            detailRow: {}, // 点击详情传过来的数据
            isAuthorized: true, // 树分域是否允许显示
        };
    },
    computed: {
        height() {
            return this.$store.getters.getHeight;
        },
    },
    watch: {},
    created() {},
    mounted() {
        window.batteryOverview = this;
    },
    beforeUnmount() {
        window.batteryOverview = null;
        this.$bus.off('isHistory');
    },
    methods: {
        handleClick(node, pathNames, pathIds = []) {
            this.pathNames = [];
            if (pathNames && pathNames.length > 0) {
                pathNames.forEach((name, index) => {
                    this.pathNames.push({
                        id: pathIds[index],
                        name,
                    });
                });
            }
            this.expandedNodeIds = [...pathIds, 'Root'];
            this.$nextTick(() => {
                this.nodeId = node.id;
            });
            this.isAuthorized = node.authorizationStatus === 2;
        },
        splitChange() {
            this.widthChange = !this.widthChange;
        },
        // 隐藏列展示弹出
        hideColumnFilter() {
            if (this.$refs.overview) {
                if (
                    this.$refs.overview.$refs.asset &&
                    this.$refs.overview.$refs.asset.$refs.columnFilter &&
                    this.$refs.overview.$refs.asset.$refs.columnFilter.hide
                ) {
                    this.$refs.overview.$refs.asset.$refs.columnFilter.hide();
                }
                if (
                    this.$refs.overview.$refs.working &&
                    this.$refs.overview.$refs.working.$refs.columnFilter &&
                    this.$refs.overview.$refs.working.$refs.columnFilter.hide
                ) {
                    this.$refs.overview.$refs.working.$refs.columnFilter.hide();
                }
                if (
                    this.$refs.overview.$refs.list &&
                    this.$refs.overview.$refs.list.$refs.columnFilter &&
                    this.$refs.overview.$refs.list.$refs.columnFilter.hide
                ) {
                    this.$refs.overview.$refs.list.$refs.columnFilter.hide();
                }
            }
        },
    },
};
/* Ended by AICoder, pid:de9f0z8b3e407ac146170b99301b7e8547a85b40 */
</script>
