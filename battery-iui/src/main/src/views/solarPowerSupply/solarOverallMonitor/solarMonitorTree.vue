<template>
    <div class="mo-tree">
        <div class="el-row">
            <el-col :span="24">
                <el-input
                    v-model="searchText"
                    class="search-input"
                    :placeholder="$t('placeholder.enter')"
                    autocomplete="on"
                    size="mini"
                    clearable
                    maxlength="40"
                    @clear="clearSearch"
                    @blur="inputFocusHandler(false)"
                    @focus="inputFocusHandler(true)"
                >
                    <template v-slot:prefix>
                        <el-icon class="el-input__icon"><el-icon-search /></el-icon>
                    </template>
                </el-input>
                <div class="searchHistory" v-if="showSearchHistory">
                    <div
                        class="searchHistoryItem"
                        v-for="(item, idx) in filteredHistory"
                        :key="'item' + idx"
                        @click="handleHistoryClick(item)"
                    >
                        <div class="searchHistoryText">{{ item }}</div>
                    </div>
                </div>
            </el-col>
        </div>
        <div v-loading="searchLoading" class="elTreeWrap newTreeStyle" :style="treeStle">
            <el-tree
                v-show="searchText.trim() === ''"
                ref="tree"
                :props="defaultProps"
                node-key="id"
                :load="loadNode"
                :default-expanded-keys="defaultExpanded"
                :lazy="true"
                highlight-current
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender"
                @node-click="handleClick"
            ></el-tree>
            <!-- 搜索树 -->
            <el-tree
                v-show="searchText"
                ref="searchTree"
                :props="defaultProps"
                node-key="id"
                :data="searchTreeData"
                highlight-current
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender"
                :default-expand-all="true"
                @node-expand="handleExpand"
                @node-click="handleClick"
            ></el-tree>
            <div
                v-if="searchText.trim() && searchTreeData.length > 0"
                style="text-align: center; padding-top: 20px; padding-bottom: 20px"
            >
                <el-button
                    plain
                    :class="[showMore ? 'hasMore' : 'nomore']"
                    size="mini"
                    :disabled="!showMore"
                    @click="searchMore"
                >
                    {{ showMore ? $t('tipMessage.moreResult') : $t('tipMessage.noMore') }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="jsx">
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import { isArrayEmpty, isStrEmpty, isStrTrimEmpty } from '@/util/common';
import { GROUP_FIELD_MODELIDS } from '@/util/constants';

export default {
    data() {
        return {
            defaultProps: {
                label: 'name',
                children: 'children',
                isLeaf: 'leaf',
            },
            iconLevelClassName: {
                Root: 'icon-root',
                RealGroup: 'icon-group',
                Site: 'icon-site',
            },
            currentId: 'r32.uedm.group-global',
            pageSize: 5000,
            alarmTimer: null,
            // 搜索树
            searchText: '',
            searchTreeData: [],
            searchPageSize: 1000,
            searchPage: 1,
            showMore: true,
            searchLoading: false,
            checkedNodes: [],
            searchHistory: [],
            inputFocus: false,
        };
    },
    components: {
        ElIconSearch,
    },
    name: 'ResourceTree',
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        height: {
            type: Number,
            default: null,
        },
        defaultExpanded: {
            type: Array,
            default: () => {
                return ['r32.uedm.group-global'];
            },
        },
        defaultNodeId: {
            type: String,
            default: null,
        },
    },
    computed: {
        treeStle() {
            let h = 'auto';
            if (this.height) {
                h = this.height - 56 - 16 + 'px'; // 56为搜索区域高度(包含分割线),16为底部多留距离
            }
            let item = {
                height: h,
            };
            return item;
        },
        filteredHistory() {
            let hisArr = this.searchHistory.filter(item => {
                const lowItem = item?.toLowerCase() ?? '';
                const lowKey = this.searchText?.trim()?.toLowerCase() ?? '';
                return lowItem.includes(lowKey);
            });
            const maxHisCount = 5;
            if (hisArr.length > maxHisCount) {
                hisArr = hisArr.slice(0, maxHisCount);
            }

            return hisArr;
        },
        showSearchHistory() {
            return this.inputFocus && !isArrayEmpty(this.filteredHistory);
        },
    },
    watch: {
        searchText(val) {
            this.searchTreeData = [];
            let keyWord = val.trim();
            if (!keyWord) {
                this.searchLoading = false;
                return;
            }
            this.searchTree(keyWord);
        },
    },
    created() {
        if (this.defaultNodeId) {
            this.currentId = this.defaultNodeId;
        }
        this.getLocalHistory();
    },
    mounted() {},
    beforeUnmount() {},
    methods: {
        clearSearch() {
            this.searchPage = 1;
        },
        loopNode(node, func) {
            if (node.data) {
                func(node);
            }
            if (!node.childNodes || node.childNodes.length === 0) {
                return;
            }
            for (let i = 0, max = node.childNodes.length; i < max; i++) {
                this.loopNode(node.childNodes[i], func);
            }
        },
        handleClick(d, node) {
            if (node.data.id === '#more') {
                let nodePage = node.data.page;
                let parentId = node.data.parentId;
                let total = node.data.total;
                let parentNode = node.parent;

                // 注意parentNode的处理
                // 注意监控对象树没有根节点, 如果是根（假的）的情况，直接传一个null才正确, 同时树上的data需要设置成[]，然后“根节点”才能正常的append，否则会报错
                if (parentNode.level === 0) {
                    parentNode = null; //
                }

                console.warn('parentNode ==> ', parentNode);

                node.loading = 'true';

                this.getChild(parentId, nodePage, d1 => {
                    node.loading = 'false';
                    d1.data.forEach(nd => {
                        // append该节点上前，最好判断下，该节点是否已经存在这个节点下
                        let notFound = true;
                        node.parent.childNodes.forEach(n => {
                            if (n.data.id === nd.id) {
                                notFound = false;
                            }
                        });
                        if (notFound) {
                            this.$refs.tree.append(nd, parentNode);
                        }
                    });

                    this.$refs.tree.remove(node);
                    // 没加载完，需要添加“加载更多”
                    if (nodePage + 1 <= Math.ceil(total / this.pageSize)) {
                        this.$refs.tree.append(this.getMoreNode(parentId, nodePage + 1, total), parentNode);
                    }
                    this.$nextTick(() => {
                        this.$refs.tree.setCurrentKey(this.currentId);
                    });
                });
            } else {
                this.currentId = node.data.id;
                let defaultExpand = [];
                let defaultExpandIds = [];
                let nodeUrl = this.getNodeUrl(node);
                defaultExpand = nodeUrl.names;
                defaultExpandIds = nodeUrl.ids;
                $emit(this, 'nodeClick', node.data, defaultExpand, defaultExpandIds);
                this.addLocalHistory(this.searchText);
                this.$nextTick(() => {
                    let currentNode = this.$refs.tree.getCurrentNode();
                    if (currentNode && currentNode.id !== this.currentId) {
                        this.$refs.tree.setCurrentKey(null);
                        this.$refs.tree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.tree.setCurrentKey(this.currentId);
                    }
                    let currentNodeSearch = this.$refs.searchTree.getCurrentNode();
                    if (currentNodeSearch && currentNodeSearch.id !== this.currentId) {
                        this.$refs.searchTree.setCurrentKey(null);
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    }
                });
            }
        },
        nodeRender(h, { node, data, store }) {
            const type = data.resourceType;
            const className = this.iconLevelClassName[type] || 'icon-site';
            const name = data.id === 'Root' ? this.$t('tree-config-root') : data.name;
            return (
                <span class="el-tree-node__label">
                    <i class={className}></i>
                    {name}
                </span>
            );
        },
        loadNode(node, resolve) {
            let id = '';
            // 子节点加载
            if (node.data && !Array.isArray(node.data)) {
                id = node.data.id;
            }
            this.getChild(id, 1, d => {
                let data = d.data;
                for (let i = 0; i < data.length; i++) {
                    if (!data[i].isOperation) {
                        data[i].disabled = true;
                    }

                    data[i].leaf = data[i].resourceType === 'Site';
                }
                // 根据返回的数据，判断是否需要加“加载更多”
                if (2 <= Math.ceil(d.total / this.pageSize)) {
                    data.push(this.getMoreNode(id, 2, d.total));
                }
                this.$nextTick(() => {
                    if (this.currentId) {
                        this.$refs.tree && this.$refs.tree.setCurrentKey(this.currentId);
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].id === this.currentId) {
                                let node = this.$refs.tree.getNode(this.currentId);
                                this.handleClick(node.data, node);
                            }
                        }
                    }
                });
                return resolve(data);
            });
        },
        getMoreNode(parentId, page, total) {
            return {
                name: '加载更多',
                id: '#more',
                leaf: true,
                showPopper: false,
                childId: [],
                resourceType: 'More',
                page: page,
                parentId: parentId,
                total: total,
            };
        },
        getChild(id, pageNo, callback) {
            /* Started by AICoder, pid:9fc007ac90d94aaaa294982db8e2030f */
            const data = {
                parentId: id === 'Root' ? '' : id,
                pageNo,
                pageSize: this.pageSize,
                modelIds: GROUP_FIELD_MODELIDS,
            };
            /* Ended by AICoder, pid:9fc007ac90d94aaaa294982db8e2030f */
            HTTP.request('getPvAuthResTree', {
                method: 'post',
                data,
                complete: data => {
                    if (data.code === 0) {
                        callback && callback(data);
                    }
                },
            });
        },
        searchMore() {
            this.searchLoading = true;
            HTTP.request('searchPvAuthResTree', {
                method: 'post',
                data: {
                    name: this.searchText,
                    pageNo: this.searchPage,
                    pageSize: this.searchPageSize,
                    modelIds: GROUP_FIELD_MODELIDS,
                },
                complete: data => {
                    this.searchLoading = false;
                    if (data.code === 0) {
                        this.searchPage++;
                        data.data = data.data || [];
                        if (data.data.length) {
                            this.updateTreeData(data.data, this.searchTreeData);
                            // 如果返回的数据条数小于pageSize，则认为是最后一页
                            if (data.data.length < this.searchPageSize) {
                                this.showMore = false;
                            }
                            this.$nextTick(() => {
                                if (this.currentId) {
                                    this.$refs.searchTree.setCurrentKey(this.currentId);
                                }
                            });
                        } else {
                            this.showMore = false;
                        }
                    }
                },
                error: data => {
                    console.log(data);
                    this.searchLoading = false;
                },
            });
        },
        searchTree(val) {
            this.searchLoading = true;
            HTTP.request('searchPvAuthResTree', {
                method: 'post',
                data: {
                    name: val,
                    pageNo: 1,
                    pageSize: this.searchPageSize,
                    modelIds: GROUP_FIELD_MODELIDS,
                },
                complete: data => {
                    let treeData = [];
                    if (val === this.searchText?.trim()) {
                        this.searchLoading = false;
                        if (data.code === 0) {
                            this.searchPage = 2;
                            data.data = data.data || [];
                            if (data.data.length) {
                                this.updateTreeData(data.data, treeData);
                                this.searchTreeData = treeData;
                                this.showMore = true;
                                if (data.data.length < this.searchPageSize) {
                                    this.showMore = false;
                                }
                                this.$nextTick(() => {
                                    if (this.currentId) {
                                        this.$refs.searchTree.setCurrentKey(this.currentId);
                                    }
                                });
                            } else {
                                this.searchTreeData = treeData;
                                this.showMore = false;
                            }
                        }
                    }
                },
                error: data => {
                    this.searchLoading = false;
                    console.log('net error ' + data);
                },
            });
        },
        updateTreeData(responseData, treeData) {
            if (responseData.length) {
                responseData.forEach(path => {
                    let mountedNode = null;
                    path.forEach((node, i) => {
                        if (!node.isOperation) {
                            node.disabled = true;
                        }
                        if (i === 0) {
                            // 预先设置当前节点为挂载点
                            let hasOne = false;
                            for (let j = 0, max = treeData.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === treeData[j].id) {
                                    mountedNode = treeData[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = node.resourceType === 'Site' ? void 0 : [];
                                treeData.push(node);
                                mountedNode = node;
                            }
                        } else {
                            let hasOne = false;
                            for (let j = 0, max = mountedNode.children.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === mountedNode.children[j].id) {
                                    mountedNode = mountedNode.children[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = node.resourceType === 'Site' ? void 0 : [];
                                mountedNode.children.push(node);
                                mountedNode = node;
                            }
                        }
                    });
                });
            }
        },
        handleExpand(d, node) {
            if (node.childNodes.length === 0) {
                let id = (node.data && node.data.id) || '';
                node.expanded = false;
                node.loading = true;
                this.getChild(id, 1, d => {
                    let nodeData = d.data;
                    for (let i = 0; i < nodeData.length; i++) {
                        if (!nodeData[i].isOperation) {
                            nodeData[i].disabled = true;
                        }
                        this.$refs.searchTree.append(nodeData[i], node);
                    }
                    if (nodeData.length === 0) {
                        node.isLeaf = true;
                        node.isRealLeaf = true;
                    }
                    node.expanded = true;
                    node.loading = false;
                    // 根据返回的数据，判断是否需要加“加载更多”
                    if (2 <= Math.ceil(d.total / this.pageSize)) {
                        this.$refs.searchTree.append(this.getMoreNode(id, 2, d.total), node);
                    }
                    this.$nextTick(() => {
                        if (this.currentId) {
                            this.$refs.searchTree.setCurrentKey(this.currentId);
                        }
                    });
                });
            }
        },
        getAllNodeIds() {
            let ids = [];
            this.$refs.tree &&
                this.loopNode(this.$refs.tree.root, node => {
                    ids.push(node.data.id);
                });
            this.$refs.searchTree &&
                this.loopNode(this.$refs.searchTree.root, node => {
                    if (node.data.id && ids.indexOf(node.data.id) === -1) {
                        ids.push(node.data.id);
                    }
                });
            return ids;
        },
        getNodeUrl(node) {
            // 获取当前节点的全路径
            let nodeUrl = [];
            let nodeUrlIds = [];
            let getNodeName = function (node) {
                if (node.data) {
                    if (node.data.id && node.data.id !== 'Root') {
                        nodeUrl.unshift(node.data.name);
                        nodeUrlIds.unshift(node.data.id);
                        if (node.parent) {
                            getNodeName(node.parent);
                        }
                    }
                }
            };
            getNodeName(node);
            return {
                names: nodeUrl,
                ids: nodeUrlIds,
            };
        },
        inputFocusHandler(focus) {
            if (focus) {
                this.inputFocus = true;
            } else {
                setTimeout(() => {
                    this.inputFocus = false;
                }, 360);
            }
        },
        handleHistoryClick(item) {
            this.searchText = item;
        },
        getLocalHistory() {
            try {
                const searchHistoryStr = localStorage.getItem('solar_tree_search_history');
                if (isStrEmpty(searchHistoryStr)) {
                    return;
                }
                const searchHistory = JSON.parse(searchHistoryStr);
                this.searchHistory = searchHistory;
            } catch (error) {
                console.log(error);
            }
        },
        addLocalHistory(text) {
            if (isStrTrimEmpty(text)) {
                return;
            }
            const item = text.trim();
            const index = this.searchHistory.indexOf(item);
            if (index !== -1) {
                this.searchHistory.splice(index, 1);
            }

            this.searchHistory.unshift(item);
            const maxLoacalHisLength = 50;
            if (this.searchHistory > maxLoacalHisLength) {
                this.searchHistory.shift();
            }

            try {
                const newHisStr = JSON.stringify(this.searchHistory);
                localStorage.setItem('solar_tree_search_history', newHisStr);
            } catch (error) {
                console.log(error);
            }
        },
    },
    emits: ['nodeClick'],
};
</script>

<style lang="scss" scoped>
::v-deep .icon-treenode {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-size: 14px 14px;
    white-space: nowrap;
    letter-spacing: -1em;
    text-indent: -99em;
    color: transparent;
}
::v-deep .icon-treenode:before {
    content: '\3000';
}
.elTreeWrap {
    overflow: auto;
    margin-top: 8px;
}

/* 横向滚动条 */
.elTreeWrap ::v-deep .el-tree > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.tree-node {
    padding-right: 140px;
    position: relative;
}
.elTreeWrap ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
button.nomore {
    border: 0 none;
}
.nodeEllipsis {
    display: inline-block;
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}
.el-tree-node__content .hover-to-show {
    position: absolute;
}
.mo-tree {
    padding-left: 8px;
}
::v-deep .el-tree-node__label .iconfont {
    font-size: 14px;
    margin-right: 4px;
}
::v-deep .alarm-level-1 {
    background-color: transparent;
    color: #fe2739 !important;
    margin-left: 0;
}

::v-deep .alarm-level-2 {
    background-color: transparent;
    color: #fc710a !important;
    margin-left: 0;
}

::v-deep .alarm-level-3 {
    background-color: transparent;
    color: #fab913 !important;
    margin-left: 0;
}

::v-deep .alarm-level-4 {
    background-color: transparent;
    color: #31bffd !important;
    margin-left: 0;
}

.searchHistory {
    position: absolute;
    z-index: 1;
    top: 40px;
    left: 0;
    right: 0;
    background: white;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.searchHistoryItem {
    display: flex;
    align-items: center;
    height: 30px;
    padding-left: 10px;
    padding-right: 10px;
}

.searchHistoryText {
    overflow: hidden; /* 隐藏超出父视图的文本内容 */
    text-overflow: ellipsis; /* 显示省略号 */
}

.dark {
    .searchHistory {
        background: #262727;
    }
}

::v-deep .icon-root {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin-right: 4px;
    background: url(../../../assets/img/svg/icon_quanwang_tree.svg) no-repeat center center;
}
::v-deep .icon-group {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin-right: 4px;
    background: url(../../../assets/img/svg/icon_fenzhu_tree.svg) no-repeat center center;
}

::v-deep .icon-site {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin-right: 4px;
    background: url(../../../assets/img/svg/icon_zhandian_tree.svg) no-repeat center center;
}
</style>
