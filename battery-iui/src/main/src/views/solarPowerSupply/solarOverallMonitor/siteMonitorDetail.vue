<template>
    <div class="uedm-content-area">
        <div class="filter-tool">
            <div class="uedm-title">
                {{ $t('button.detail') }}
            </div>
            <div class="filterButtons">
                <span class="filterItem">
                    <column-filter
                        ref="columnFilter"
                        :value="headerConfig"
                        :height="columnFilterHeight"
                        :isDark="isDark"
                        :tip="$t('solarPower.columnCustom')"
                        @save="handleFilterOk"
                    ></column-filter>
                </span>
            </div>
        </div>
        <div v-if="tableHeader.length">
            <el-table
                v-if="showTable"
                ref="table"
                v-loading="loading"
                tooltip-effect="dark"
                :data="tableData"
                style="width: 100%"
                :height="tableHeight"
                @sort-change="sortChange"
            >
                <el-table-column
                    v-for="item in tableHeader"
                    :key="item.id"
                    :prop="item.id"
                    :sortable="item.sortable ? 'custom' : false"
                    :label="formatHeader(item)"
                    show-overflow-tooltip
                    :min-width="minWidth(item)"
                    :formatter="formatValue"
                >
                    <template v-if="item.id === 'onLine'" #default="scope">
                        <div :class="getOnlineStatusClass(scope.row)">{{ getOnlineStatusStr(scope.row) }}</div>
                    </template>
                    <template v-else-if="item.id === 'alarmState'" #default="scope">
                        <div class="warn-status-container">
                            <div class="warn-status-icon" :style="{ background: getWarnStatusColor(scope.row) }"></div>
                            {{ getWarnStatusStr(scope.row) }}
                        </div>
                    </template>
                    <template v-else-if="efficiencyProps.includes(item.id)" #default="scope">
                        <div :style="{ color: getEfficiencyColor(scope.row, item.id) }">
                            {{ getEfficiencyStr(scope.row, item.id) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    :min-width="operationColWidth"
                    fixed="right"
                    align="center"
                    :label="$t('table.operation')"
                    class="operationCol"
                >
                    <template #default="scope">
                        <el-button type="text" @click="gotoDetail(scope.row, scope.$index)">
                            {{ $t('button.detail') }}
                        </el-button>
                        <el-button
                            v-if="recollectRights"
                            type="text"
                            :disabled="scope.row.siteRecollectionStatus"
                            @click="showRecollectDlg(true, scope.row)"
                        >
                            {{
                                scope.row.siteRecollectionStatus
                                    ? $t('solarPower.recollecting')
                                    : $t('solarPower.recollection')
                            }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                :page-sizes="[5, 10, 20, 30, 50]"
                v-model:current-page="pageInfo.pageNo"
                v-model:page-size="pageInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageInfo.total"
                title=""
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <div v-else class="no-data border" :style="{ height: 120 + 'px', 'line-height': 120 + 'px' }">
            <span>{{ $t('common.noData') }}</span>
        </div>
        <recollect-dlg
            v-model:visible="recollectDlgVis"
            :currSite="currRecollectSite"
            @hide="showRecollectDlg(false)"
            @refreshSiteList="queryTableData()"
        />
        <div id="temporary"></div>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import RecollectDlg from './recollectDlg.vue';
import { convertThousandFormat, fmtStrEmpty, getPrecision, isEmpty, isStrEmpty } from '../../../util/common';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
const ONLINE_CODE = '0';
const OFFLINE_CODE = '1';
const OFFLINE_CODE_TWO = '2';
const warningCode = {
    normal: '0',
    critical: '1',
    major: '2',
    minor: '3',
    warning: '4',
};
export default {
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    components: {
        'column-filter': ColumnFilter,
        'recollect-dlg': RecollectDlg,
    },
    props: {
        logicId: {
            type: String,
            default: '',
        },
        queryParameter: {
            type: Object,
            default: null,
        },
        alarmRights: {
            type: Boolean,
            default: false,
        },
        colorConfig: {
            type: Object,
            default: function () {
                return {};
            },
        },
        scrollY: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            loading: false,
            tableData: [], // 列表数据值
            headerConfig: [], // 表头配置
            showTable: true,
            // 分页相关
            pageInfo: {
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            order: 'default',
            sort: 'asc',
            requestId: 0,
            recollectDlgVis: false,
            currRecollectSite: null,
            efficiencyProps: ['efficiency', 'todayMaxEfficiency', 'monthMaxEfficiency', 'yearMaxEfficiency'],
        };
    },
    computed: {
        tableHeader() {
            return this.headerConfig.filter(col => col.enable);
        },
        tableHeight() {
            if (this.pageInfo.pageSize > 10 && this.pageInfo.total > 10) {
                return '450';
            }

            return void 0;
        },
        columnFilterHeight() {
            const topViewHeight = 484;
            const minTopHeight = 300;
            // 上方剩余空间为固定的高度减去页面滚动高度
            const topHeight = topViewHeight - this.scrollY;
            const padding = 16;

            if (topHeight > minTopHeight) {
                return topHeight - padding;
            }
            // 上方有足够高度时，使其显示在上方，使用上方的剩余高度。否则显示在下方，使用表格实际占用高度
            let tableHeight;
            const rowHeight = 42;
            if (this.pageInfo.pageSize > 10 && this.pageInfo.total > 10) {
                tableHeight = 450;
            } else {
                tableHeight = 112 + Math.max(Math.min(this.pageInfo.total, this.pageInfo.pageSize), 1) * rowHeight;
            }

            return tableHeight - padding;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        recollectRights() {
            return this.rights['pv.ppa.solarMonitor.recollection'];
        },
        operationColWidth() {
            if (!this.recollectRights) {
                return this.minWidth({ name: this.$t('table.operation'), id: 'operation' });
            }
            const rcStr = this.tableData.some(item => item.siteRecollectionStatus)
                ? this.$t('solarPower.recollecting')
                : this.$t('solarPower.recollection');
            return this.minWidth({ name: `${this.$t('button.detail')}*${rcStr}`, id: 'operation' });
        },
    },
    watch: {
        queryParameter: {
            deep: true,
            handler() {
                this.reloadData();
            },
        },
    },
    created() {
        this.getFilter();
    },
    mounted() {
        this.reloadData();
    },

    methods: {
        formatValue(row, column, cellValue, index) {
            return fmtStrEmpty(cellValue);
        },
        handleFilterOk(d) {
            this.saveFilter(d);
        },
        reloadData() {
            if (isEmpty(this.queryParameter)) {
                return;
            }
            this.pageInfo.pageNo = 1;
            this.queryTableData();
        },
        getFilter() {
            HTTP.request('getSMDimensions', {
                method: 'get',
                complete: resp => {
                    if (resp.code === 0) {
                        const config = resp.data || [];
                        config.forEach(item => {
                            item.name = item.unit ? `${fmtStrEmpty(item.name)}(${item.unit})` : fmtStrEmpty(item.name);
                        });
                        this.headerConfig = config;
                    }
                },
                error: resp => {
                    this.headerConfig = [];
                },
            });
        },

        saveFilter(d) {
            let updateParameter = [];
            for (let i = 0; i < d.length; i++) {
                let item = d[i];
                updateParameter.push({
                    id: item.id,
                    sequence: item.sequence,
                    enable: item.enable,
                });
            }

            this.loading = true;
            HTTP.request('updateSMRevDimensions', {
                method: 'post',
                data: updateParameter,
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.getFilter();
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotRange'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotModify'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.loading = false;
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        queryTableData() {
            const param = {
                ...this.queryParameter,
                order: this.order,
                sort: this.sort,
                pageNo: this.pageInfo.pageNo,
                pageSize: this.pageInfo.pageSize,
            };

            this.loading = true;
            this.requestId++;
            const thisRequestId = this.requestId;
            HTTP.request('getSMSiteDetail', {
                method: 'post',
                data: param,
                complete: resp => {
                    if (thisRequestId !== this.requestId) {
                        return;
                    }
                    this.tableData = [];
                    if (resp.code === 0) {
                        const data = resp.data || {};
                        this.tableData = data.list || [];
                        this.pageInfo.total = data.total ?? 0;
                        this.loading = false;
                    } else if (resp.code === -101) {
                        this.clearTableData();
                        this.$message({
                            message: this.$t('tipMessage.paramIsNull'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.clearTableData();
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: resp => {
                    if (thisRequestId !== this.requestId) {
                        return;
                    }
                    this.clearTableData();
                    this.loading = false;
                    this.$message({
                        message: this.$t('tipMessage.requestError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        showRecollectDlg(vis, row) {
            this.recollectDlgVis = vis;
            this.currRecollectSite = vis ? row : null;
        },
        clearTableData() {
            this.tableData = [];
            this.pageInfo.total = 0;
        },
        handleSizeChange() {
            // 页码大小改变
            this.pageInfo.pageNo = 1;
            this.queryTableData();
        },
        handleCurrentChange() {
            // 翻页
            this.queryTableData();
        },
        minWidth(item) {
            let text = item.name;
            if (item.unit) {
                text = text + '(' + item.unit + ')';
            }
            const content = document.getElementById('temporary');
            const span = document.createElement('span');
            let w = span.offsetWidth;
            span.style.visibility = 'hidden';
            span.style.fontSize = item.fontSize ? `${item.fontSize}px` : '14px';
            span.style.fontWeight = 'bolder';
            span.style.display = 'inline-block';
            content.appendChild(span);
            if (typeof span.textContent !== 'undefined') {
                span.textContent = text;
            } else {
                span.innerText = text;
            }
            w = parseFloat(window.getComputedStyle(span).width) - w;
            content.removeChild(span);

            const customWidthConfig = new Map([
                ['time', 300],
                ['name', 250],
                ['location', 500],
            ]);

            const customMarginConfig = new Map([['operation', 30]]);

            const minWidth = customWidthConfig.get(item.id) ?? 0;
            const margin = customMarginConfig.get(item.id) ?? 100;
            return Math.max(w + margin, minWidth);
        },
        sortChange(p) {
            this.order = p.prop === 'alarmState' ? 'alarmStateCode' : p.prop;
            let order = p.order;
            if (order === 'ascending') {
                this.sort = 'asc';
            } else if (order === 'descending') {
                this.sort = 'desc';
            } else {
                this.sort = 'asc';
                this.order = 'default';
            }
            this.pageInfo.pageNo = 1;
            this.queryTableData();
        },
        formatHeader(item) {
            return fmtStrEmpty(item.name);
        },
        getSortParam() {
            return {
                order: this.order,
                sort: this.sort,
            };
        },
        gotoDetail(row) {
            this.$emit('clickSiteMonitorDetail', row);
        },
        getOnlineStatusStr(row) {
            switch (row.onLine) {
                case ONLINE_CODE:
                    return this.$t('policyManagement.online');
                case OFFLINE_CODE:
                case OFFLINE_CODE_TWO:
                    return this.$t('policyManagement.offline');
                default:
                    return '--';
            }
        },
        getOnlineStatusClass(row) {
            switch (row.onLine) {
                case ONLINE_CODE:
                    return 'online-style';
                case OFFLINE_CODE:
                case OFFLINE_CODE_TWO:
                    return 'offline-style';
                default:
                    return 'unknown-style';
            }
        },
        getWarnStatusStr(row) {
            if (isStrEmpty(row.alarmStateCode)) {
                return '--';
            }
            if (row.alarmStateCode === warningCode.normal) {
                return this.$t('common.normal');
            }
            return this.$t('solarPower.alarm');
        },
        getWarnStatusColor(row) {
            switch (row.alarmStateCode) {
                case warningCode.critical:
                    return this.colorConfig.criticalcolor;
                case warningCode.major:
                    return this.colorConfig.majorcolor;
                case warningCode.minor:
                    return this.colorConfig.minorcolor;
                case warningCode.warning:
                    return this.colorConfig.warningcolor;
                default:
                    return '';
            }
        },
        getEfficiencyStr(row, prop) {
            if (isStrEmpty(row[prop])) {
                return '--';
            }

            const val = parseFloat(row[prop]);
            // 保留小数点后2位
            const precision = 2;
            return convertThousandFormat(val * 100, precision);
        },
        getEfficiencyColor(row, prop) {
            const normalColor = this.isDark ? '#e5eaf3;' : '#303133;';
            const warnColor = '#de4040';
            if (isStrEmpty(row[prop])) {
                return normalColor;
            }

            const val = parseFloat(row[prop]);
            return val >= 1 ? warnColor : normalColor;
        },
    },
    emits: ['clickSiteMonitorDetail', 'filter'],
};
</script>

<style lang="scss" scoped>
h4.subTitle {
    display: inline-block;
    margin: 0;
    padding: 8px 0 0;
}
.filter-tool {
    position: relative;
    .filterButtons {
        position: absolute;
        top: -4px;
        right: 1px;
        .filterPop {
            width: 414px;
        }
    }
}
i.plx-ico-fm-alarm-f-16 {
    margin-right: 5px;
}

::v-deep .no-ellipsis .cell {
    text-overflow: clip;
}

.uedm-content-area {
    margin-left: 16px;
    margin-bottom: 16px;
    background: white;
    margin-right: 16px;
}
.online-style {
    display: inline-block;
    height: 24px;
    padding-left: 8px;
    padding-right: 8px;
    background-color: #f0f9eb;
    border-radius: 4px;
    color: #67c23a;
    font-size: 12px;
    margin-left: 3px;
}

.offline-style {
    display: inline-block;
    height: 24px;
    padding-left: 8px;
    padding-right: 8px;
    background-color: #fef0f0;
    border-radius: 4px;
    color: #f56c6c;
    font-size: 12px;
    margin-left: 3px;
}

.unknown-style {
    display: inline-block;
    height: 24px;
    padding-left: 8px;
    padding-right: 8px;
    color: #303133;
    font-size: 12px;
    margin-left: 3px;
}

.dark {
    .online-style {
        background-color: #1c2518;
    }

    .offline-style {
        background-color: #2b1d1d;
    }

    .unknown-style {
        color: #e5eaf3;
    }
}

.warn-status-icon {
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 4px;
}

.warn-status-container {
    display: flex;
    align-items: center;
}

::v-deep .el-table .el-table__cell.is-center {
    text-align: left;
}
</style>
