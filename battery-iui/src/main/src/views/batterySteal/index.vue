<template>
    <div class="batteryForm" style="padding: 24px">
        <span class="firstTitle-16px">{{ $t('batteryMonitor.btnLabels.batterySecurity') }}</span>
        <!-- name 及 安全区域 -->
        <div class="safety-area">
            <el-button @click="setSafetyArea">
                {{ $t('batteryMonitor.btnLabels.safetyArea') }}
            </el-button>
        </div>
        <div v-if="isNetwork" class="safety-area">
            <el-button @click="handleElectronicKey">
                {{ $t('batteryMonitor.btnLabels.electronicKey') }}
            </el-button>
        </div>

        <!-- 筛选条件 -->
        <div class="battery-filter-condition">
            <battery-statistic-filter
                :echarts-filter-item="echartsFilterItem"
                :table-filter-item="tableFilterItem"
                @filterData="filterData"
            ></battery-statistic-filter>
        </div>
        <!-- 电池统计筛选 -->
        <div v-show="!loading" class="filterButtons">
            <span class="filterItem">
                <el-tooltip class="item" effect="dark" :content="$t('tooltip.displayedItems')" placement="bottom">
                    <span
                        class="icon-button plx-ico-col-16"
                        @click="
                            showFilter = !showFilter;
                            showTableFilter = false;
                        "
                    ></span>
                </el-tooltip>
                <!-- 筛选内容 -->
                <div v-if="showFilter" class="filterPop">
                    <div class="inner">
                        <filter-list
                            :loading="filterLoading"
                            :list="filterList"
                            :search-list="filterSearchList"
                            @search="getFilterSearch"
                            @filterOk="handleFilterOk"
                            @filterCancel="handleFilterCancel"
                        ></filter-list>
                    </div>
                </div>
            </span>
        </div>
        <div v-loading="loading">
            <!-- 电池统计 -->
            <battery-statistic-data
                :eachrt-key="eachrtKey"
                style="margin-top: 57px"
                :data="totalData"
                :echarts-filter-item="echartsFilterItem"
            ></battery-statistic-data>

            <!-- 列表 -->
            <div class="btn-container">
                <el-button
                    v-if="rights['c.battery.export']"
                    type="primary"
                    :disabled="!tableData.length || btnDisabled"
                    @click="exportBegin"
                >
                    {{ $t('batteryMonitor.btnLabels.export') }}
                </el-button>
                <el-button
                    v-if="rights['c.battery.fortification']"
                    :disabled="disabledFortification"
                    @click="batchFortify"
                >
                    {{ $t('batteryMonitor.btnLabels.batchFortify') }}
                </el-button>
                <el-button v-if="rights['c.battery.fortification']" :disabled="disabledWithdraw" @click="batchWithdraw">
                    {{ $t('batteryMonitor.btnLabels.batchWithdraw') }}
                </el-button>
                <el-button v-if="rights['c.battery.unlock']" :disabled="disabledUnlock" @click="batchUnlock">
                    {{ $t('batteryMonitor.btnLabels.batchUnlock') }}
                </el-button>

                <!-- 电池表格筛选 -->
                <div class="filterButtons">
                    <span class="filterItem">
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="$t('tooltip.displayedItems')"
                            placement="bottom"
                        >
                            <span
                                class="icon-button plx-ico-col-16"
                                @click="
                                    showTableFilter = !showTableFilter;
                                    showFilter = false;
                                "
                            ></span>
                        </el-tooltip>
                        <!-- 筛选内容 -->
                        <div v-if="showTableFilter" class="filterPop">
                            <div class="inner">
                                <filter-list
                                    :loading="tableFilterLoading"
                                    :list="tableFilterList"
                                    :search-list="tableFilterSearchList"
                                    @search="getTableFilterSearch"
                                    @filterOk="handleTableFilterOk"
                                    @filterCancel="handleTableFilterCancel"
                                ></filter-list>
                            </div>
                        </div>
                    </span>
                </div>
            </div>

            <battery-statistic-form-content
                ref="table"
                :table-key="tableKey"
                :table-data="tableData"
                :total="total"
                :index-page-size="pageSize"
                :index-page-no="pageNo"
                :table-filter-item="tableFilterItem"
                :is-network="isNetwork"
                @refeshTable="filterData"
                @selectionChange="tableDataSelected"
                @loading="loading = true"
                @loaded="dataLoaded"
                @tableSortChange="tableSortChange"
                @tablePageSizeChange="tablePageSizeChange"
                @tablePageChange="tablePageChange"
            ></battery-statistic-form-content>
        </div>

        <el-dialog
            v-model="safetyAreaDialogVisible"
            :title="$t('batteryMonitor.title.setSafetyArea')"
            :destroy-on-close="true"
            :before-close="handleSetSafetyAreaFn"
            width="600px"
            @opened="sliderValChange"
        >
            <div class="safety-area-content">
                <div class="title">
                    {{ $t('batteryMonitor.labels.safetyArea') }}
                </div>
                <div class="slider">
                    <el-slider
                        v-model="sliderValue"
                        :min="10"
                        :max="600"
                        :marks="safetyAreaMarks"
                        :format-tooltip="formatTooltip"
                        @change="sliderValChange"
                    ></el-slider>
                </div>
            </div>
            <div class="bottomBar">
                <el-button type="primary" size="mini" @click="handleSetSafetyArea">
                    {{ $t('button.confirm') }}
                </el-button>
                <el-button size="mini" @click="handleSetSafetyAreaFn">
                    {{ $t('button.cancel') }}
                </el-button>
            </div>
        </el-dialog>
        <!-- Started by AICoder, pid:b320cu3a9f233da145950aa91047900992c8f9dc -->
        <confirm-dialog
            v-model:visible="confirmInfo.show"
            type="warning"
            :content="confirmInfo.content"
            :description="confirmInfo.description"
            width="460px"
            confirm-button-type="primary"
            @confirm="handleRegenerateKey"
        ></confirm-dialog>
        <!-- Ended by AICoder, pid:b320cu3a9f233da145950aa91047900992c8f9dc -->
        <!-- Started by AICoder, pid:9cb531bd44v853914f81089510fe164bca30a360 -->
        <el-dialog
            v-model="electronicKeyDialogVisible"
            :title="$t('batteryMonitor.btnLabels.electronicKey')"
            :destroy-on-close="true"
            :before-close="closeElectronicKeyDialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            width="460px"
            class="electronickey-dialog"
            @opened="openElectronicKeyDialog"
        >
            <div class="electronickey-content">
                <div class="electronickey-content-item">
                    {{ `${$t('standby.fields.updateTime')}: ${updateTime}` }}
                </div>
                <div class="bottomBar">
                    <el-button @click="handleOpenEncrypt">
                        {{ $t('button.export') }}
                    </el-button>
                    <el-upload
                        ref="uploadUpgradeFile"
                        class="upload-upgrade-file"
                        accept=".zip"
                        action
                        :on-change="handleChange"
                        :multiple="false"
                        :file-list="uploadFiles"
                        :show-file-list="false"
                        :auto-upload="false"
                        :http-request="upload"
                        :with-credentials="true"
                        style="display: inline-block; margin-left: 8px"
                    >
                        <template #trigger>
                            <el-button>
                                {{ $t('button.import') }}
                            </el-button>
                        </template>
                    </el-upload>
                    <el-button @click="regenerateKeyHandler">
                        {{ $t('button.regenerate') }}
                    </el-button>
                </div>
            </div>
        </el-dialog>
        <!-- Ended by AICoder, pid:9cb531bd44v853914f81089510fe164bca30a360 -->
        <!-- 导出加密弹框 -->
        <div class="encrypt-dialog">
            <el-dialog
                :title="$t('batteryMonitor.title.encryption')"
                v-model="encryptDialogVisible"
                width="500px"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                @close="cancelEncrypt"
            >
                <el-form ref="encryptForm" :model="encryptForm" :rules="encryptFormRules" @submit.prevent>
                    <el-form-item
                        prop="passwd"
                        :label="$t('batteryMonitor.attrNames.password')"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="encryptForm.passwd"
                            show-password
                            :placeholder="$t('batteryMonitor.message.passwordTip')"
                            @copy.prevent
                            @cut.prevent
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        prop="confirmPasswd"
                        :label="$t('batteryMonitor.attrNames.passwordAgain')"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="encryptForm.confirmPasswd"
                            show-password
                            :placeholder="$t('batteryMonitor.message.passwordAgainTip')"
                            @copy.prevent
                            @cut.prevent
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="" :label-width="formLabelWidth">
                        <div class="tip-info">
                            <div>{{ $t('batteryMonitor.message.passwordTip0') }}</div>
                            <div>{{ $t('batteryMonitor.message.passwordTip1') }}</div>
                            <div>{{ $t('batteryMonitor.message.passwordTip2') }}</div>
                        </div>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button type="primary" @click="encryptDownloadSubmit">
                            {{ $t('button.confirm') }}
                        </el-button>
                        <el-button @click="cancelEncrypt">
                            {{ $t('button.cancel') }}
                        </el-button>
                    </span>
                </template>
            </el-dialog>
        </div>

        <!-- 导入解密弹框 -->
        <div class="decrypt-dialog">
            <el-dialog
                :title="$t('batteryMonitor.title.decryption')"
                v-model="decryptDialogVisible"
                width="500px"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                @close="cancelDecrypt"
            >
                <el-form ref="decryptForm" :model="decryptForm" :rules="decryptFormRules" @submit.prevent>
                    <el-form-item
                        prop="passwd"
                        :label="$t('batteryMonitor.attrNames.password')"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            ref="decryptInput"
                            v-model="decryptForm.passwd"
                            show-password
                            :placeholder="$t('batteryMonitor.message.passwordTip')"
                            @input="decryptFormChange"
                            @keyup.enter="decryptSubmit"
                            @copy.prevent
                            @cut.prevent
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="" :label-width="formLabelWidth">
                        <div class="tip-info">
                            <div>{{ $t('batteryMonitor.message.passwordTip0') }}</div>
                            <div>{{ $t('batteryMonitor.message.passwordTip1') }}</div>
                            <div>{{ $t('batteryMonitor.message.passwordTip2') }}</div>
                        </div>
                    </el-form-item>
                </el-form>
                <template v-slot:footer>
                    <div class="dialog-footer">
                        <el-button type="primary" @click="decryptSubmit">
                            {{ $t('button.confirm') }}
                        </el-button>
                        <el-button @click="cancelDecrypt">
                            {{ $t('button.cancel') }}
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import BatteryStatisticFilter from './BatteryStatisticFilter';
import BatteryStatisticData from './BatteryStatisticData';
import BatteryStatisticFormContent from './BatteryStatisticFormContent';
import HTTP from '@/util/httpService.js';
import XLSX from 'xlsx';
import axios from 'axios';
import JSZip from 'jszip';
import * as zip from '@zip.js/zip.js/lib/zip-full.js';
import { encryptRSA } from '@/util/jsencrypt.js';
import { locationName } from '@/views/batteryTrack/utils.js';
import FilterList from './filterList.vue';
import ConfirmDialog from '@uedm/uedm-ui/src/components/confirmDialog.vue';
import { handelErrMessage } from '@/util/common.js';
const MAX_FILE_SIZE = 1 * 1024 * 1024;
// 导出列名，含顺序
const fields = [
    'moId',
    'batteryName',
    'realGroupName',
    'siteName',
    'fortificationState',
    'communicationStatus',
    'lockStatus',
    'motionState',
    'theftStatus',
    'latitude',
    'longitude',
    'ariseTime',
    'siteLevel',
];
export default {
    name: 'BatteryForm',

    components: {
        'battery-statistic-filter': BatteryStatisticFilter,
        'battery-statistic-data': BatteryStatisticData,
        'battery-statistic-form-content': BatteryStatisticFormContent,
        'filter-list': FilterList,
        'confirm-dialog': ConfirmDialog,
    },

    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },

    data() {
        let validatePasswd = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t('batteryMonitor.message.passwordTip')));
            } else {
                if (value.length < 8 || value.length > 128) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                if (!/^[0-9a-zA-Z~!@#$%^&*.?_]*$/.test(value)) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                if (!(/\d/.test(value) && /[a-z]/.test(value) && /[A-Z]/.test(value) && /[~!@#$%^&*.?_]/.test(value))) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                callback();
            }
        };
        let validateConfirmPasswd = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t('batteryMonitor.message.passwordAgainTip')));
            } else {
                if (this.encryptForm.passwd && this.encryptForm.confirmPasswd !== this.encryptForm.passwd) {
                    callback(new Error(this.$t('batteryMonitor.message.inconsistentPassword')));
                }
                callback();
            }
        };
        let validateDecryptPasswd = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t('batteryMonitor.message.passwordTip')));
            } else {
                if (this.showDecryptError) {
                    callback(new Error(this.$t('batteryMonitor.message.passwordError')));
                }
                if (value.length < 8 || value.length > 128) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                if (!/^[0-9a-zA-Z~!@#$%^&*.?_]*$/.test(value)) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                if (!(/\d/.test(value) && /[a-z]/.test(value) && /[A-Z]/.test(value) && /[~!@#$%^&*.?_]/.test(value))) {
                    callback(new Error(this.$t('batteryMonitor.message.weakPassword')));
                }
                callback();
            }
        };
        return {
            btnDisabled: false,
            // 配置默认打开哪几个collapse
            activeNames: ['batteryStatistics', 'batteryList'],

            // 列表内容数据
            tableData: [],

            // 导出excel表格所用的数据
            exportTableData: [],
            isExport: false,

            // 统计页面所用数据
            totalData: null,

            loading: false,

            // 安全区域
            safetyAreaDialogVisible: false,
            electronicKeyDialogVisible: false,
            updateTime: '',
            sliderValue: 100,
            safetyAreaMarks: {
                10: '10m',
                600: '600m',
            },
            // 筛选
            oldFilter: null, // 表单数据
            showFilter: false,
            showTableFilter: false,
            filterLoading: false,
            tableFilterLoading: false,
            filterList: [], // echarts初始列表
            filterSearchList: [], // echarts过滤条件列表数据
            tableFilterList: [], // table初始列表
            tableFilterSearchList: [], // table过滤条件列表数据
            echartsFilterItem: {}, // 用于过滤图表的数据
            tableFilterItem: {}, // 用于过滤表格的数据
            eachrtKey: 0, // 更新echarts的数据
            tableKey: 0, // 更新表格的数据

            selectedData: [],
            sort: '',
            order: '',
            pageSize: 5,
            pageNo: 1,
            total: 0,
            disabledFortification: true,
            disabledWithdraw: true,
            disabledUnlock: true,
            lang: '',
            isNetwork: false,
            confirmInfo: {
                show: false,
                content: this.$t('batteryMonitor.infowindow.regenerateTitle'),
                description: this.$t('batteryMonitor.infowindow.regenerateTip'),
            },
            encryptDialogVisible: false, // 安防钥匙加密导出弹窗
            // 导出加密对话框
            encryptForm: {
                passwd: '',
                confirmPasswd: '',
            },
            encryptFormRules: {
                passwd: [
                    {
                        required: true,
                        message: this.$t('batteryMonitor.message.passwordTip'),
                        trigger: 'blur',
                    },
                    { validator: validatePasswd, trigger: 'blur' },
                ],
                confirmPasswd: [
                    {
                        required: true,
                        message: this.$t('batteryMonitor.message.passwordAgainTip'),
                        trigger: 'blur',
                    },
                    { validator: validateConfirmPasswd, trigger: 'change' },
                ],
            },
            decryptDialogVisible: false,
            // 导入解密对话框
            decryptForm: {
                passwd: '',
            },
            decryptFormRules: {
                passwd: [
                    {
                        required: true,
                        message: this.$t('batteryMonitor.message.passwordTip'),
                        trigger: 'change',
                    },
                    { validator: validateDecryptPasswd, trigger: 'change' },
                ],
            },
            showDecryptError: false, // 解压密码是否错误
            uploadFiles: [],
        };
    },
    computed: {
        formLabelWidth() {
            return this.$i18n.locale === 'en-US' ? '140px' : '120px';
        },
    },
    created() {
        this.getTheftConfig();
    },
    mounted() {
        this.getSafeArea();
        this.getFilterList();
        this.getTableFilterList();
        this.filterData();
        this.lang = window.languageOption;
    },

    methods: {
        /* Started by AICoder, pid:nd19a24f097127a141a80bc830f4502db5b5ccd8 */
        getTheftConfig() {
            HTTP.request(`/api/battery-manager/v1/CfgCenterData/get-by-key`, {
                method: 'get',
                urlParam: { key: 'battery.theft.config' },
                complete: data => {
                    if (data.code === 0) {
                        this.isNetwork = data.data === 'network';
                    }
                },
                error: () => {
                    this.handleError(this.$t('tipMessage.networkError'));
                },
            });
        },

        handleError(message) {
            this.$message({
                message: message,
                duration: 5000,
                showClose: true,
                type: 'error',
            });
        },
        /* Ended by AICoder, pid:nd19a24f097127a141a80bc830f4502db5b5ccd8 */

        /**
         * 查询echarts列表
         */
        getFilterList() {
            this.filterLoading = true;
            HTTP.request('batteryTrackStatusSelect', {
                method: 'post',
                complete: data => {
                    this.filterLoading = false;
                    if (data.code === 0) {
                        this.filterList = data.data;
                        data.data.forEach(item => {
                            this.echartsFilterItem[item.id] = item.enable;
                        });
                        this.eachrtKey += 1;
                    }
                },
                error: () => {
                    this.filterLoading = false;
                },
            });
        },
        /**
         * echarts列表搜索
         */
        getFilterSearch(name) {
            if (!name) {
                return;
            }
            this.filterLoading = true;
            HTTP.request('batteryTrackStatusSelect', {
                method: 'post',
                complete: data => {
                    if (data.code === 0) {
                        let arr = data.data.filter(item => {
                            return (
                                item.name.includes(name) ||
                                item.name.toLowerCase().includes(name.toLowerCase()) ||
                                item.name.toLowerCase().includes(name.toUpperCase())
                            );
                        });
                        this.filterSearchList = arr;
                    }
                    this.filterLoading = false;
                },
                error: () => {
                    this.filterLoading = false;
                },
            });
        },
        /**
         * 更新echarts列表
         */
        handleFilterOk(tableList) {
            HTTP.request('batteryTrackStatusUpdate', {
                method: 'post',
                data: tableList.map(i => {
                    return {
                        id: i.id,
                        sequence: i.sequence,
                        enable: i.enable,
                    };
                }),
                complete: data => {
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.filterSaveSuccess'),
                            type: 'success',
                        });
                        // this.filterList = tableList;
                        this.showFilter = false;
                        this.getFilterList();
                        // this.filterData();
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: `${this.$t('batteryMonitor.btnLabels.valueNotRange')}: ${data.error}`,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: `${this.$t('batteryMonitor.btnLabels.valueNotModify')}: ${data.error}`,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {},
            });
        },
        /**
         * 查询table列表
         */
        getTableFilterList() {
            this.tableFilterLoading = true;
            HTTP.request('batteryTrackOverviewSelect', {
                method: 'post',
                complete: data => {
                    this.tableFilterLoading = false;
                    if (data.code === 0) {
                        this.tableFilterList = data.data;
                        data.data.forEach(item => {
                            this.tableFilterItem[item.id] = item.enable;
                        });
                        this.tableKey += 1;
                        // this.filterData();
                    }
                },
                error: () => {
                    this.tableFilterLoading = false;
                },
            });
        },
        /**
         * table列表搜索
         */
        getTableFilterSearch(name) {
            if (!name) {
                return;
            }
            this.tableFilterLoading = true;
            HTTP.request('batteryTrackOverviewSelect', {
                method: 'post',
                complete: data => {
                    if (data.code === 0) {
                        let arr = data.data.filter(item => {
                            return (
                                item.name.includes(name) ||
                                item.name.toLowerCase().includes(name.toLowerCase()) ||
                                item.name.toLowerCase().includes(name.toUpperCase())
                            );
                        });
                        this.tableFilterSearchList = arr;
                    }
                    this.tableFilterLoading = false;
                },
                error: () => {
                    this.tableFilterLoading = false;
                },
            });
        },
        /**
         * 更新table列表
         */
        handleTableFilterOk(tableList) {
            HTTP.request('batteryTrackOverviewUpdate', {
                method: 'post',
                data: tableList.map(i => {
                    return {
                        id: i.id,
                        sequence: i.sequence,
                        enable: i.enable,
                    };
                }),
                complete: data => {
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.showTableFilter = false;
                        this.getTableFilterList();
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: `${this.$t('batteryMonitor.btnLabels.valueNotRange')}: ${data.error}`,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: `${this.$t('batteryMonitor.btnLabels.valueNotModify')}: ${data.error}`,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryMonitor.btnLabels.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {},
            });
        },
        handleFilterCancel() {
            this.showFilter = false;
        },
        handleTableFilterCancel() {
            this.showTableFilter = false;
        },

        dataLoaded(flag) {
            this.loading = false;
            if (!flag) {
                this.$message({
                    message: this.$t('batteryMonitor.infowindow.getLoacationFailed'),
                    type: 'info',
                });
            }
        },
        // 度分秒转换成为度
        degreeConvertBack(value) {
            // let du = value.split("°")[0];
            // let fen = value.split("°")[1].split("'")[0];
            // let miao = value.split("°")[1].split("'")[1].split('"')[0];
            // let result = Math.abs(du) + "." + ("" + (Math.abs(fen)/60 + Math.abs(miao)/3600)).split(".")[1];
            // return (+result).toFixed(6);

            return value;
        },

        getLocationPromise(elem) {
            let lat = this.degreeConvertBack(elem.latitude);
            let lng = this.degreeConvertBack(elem.longitude);
            return locationName({ lat, lng }, elem.moId);
        },

        handleExportTableData(val) {
            let exportData = val;
            this.i18nList(exportData);
            return exportData;
        },

        i18nList(data) {
            data.forEach(item => {
                switch (parseInt(item.motionState)) {
                    case 0:
                        item['motionState'] = this.$t('batteryMonitor.attrValues.static');
                        break;
                    case 1:
                        item['motionState'] = this.$t('batteryMonitor.attrValues.motion');
                        break;
                    case 2:
                        item['motionState'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['motionState'] = '--';
                }

                switch (parseInt(item.fortificationState)) {
                    case 1:
                        item['fortificationState'] = this.$t('batteryMonitor.attrValues.fortification');
                        break;
                    case 0:
                        item['fortificationState'] = this.$t('batteryMonitor.attrValues.withdraw');
                        break;
                    case 2:
                        item['fortificationState'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['fortificationState'] = '--';
                }

                switch (parseInt(item.communicationStatus)) {
                    case 0:
                        item['communicationStatus'] = this.$t('batteryMonitor.attrValues.onLine');
                        break;
                    case 1:
                        item['communicationStatus'] = this.$t('batteryMonitor.attrValues.offLine');
                        break;
                    case 2:
                        item['communicationStatus'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['communicationStatus'] = '--';
                }

                switch (parseInt(item.theftStatus)) {
                    case 0:
                        item['theftStatus'] = this.$t('batteryMonitor.attrValues.normal');
                        break;
                    case 1:
                        item['theftStatus'] = this.$t('batteryMonitor.attrValues.steal');
                        break;
                    case 2:
                        item['theftStatus'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['theftStatus'] = '--';
                }

                switch (parseInt(item.lockStatus)) {
                    case 0:
                        item['lockStatus'] = this.$t('batteryMonitor.attrValues.normal');
                        break;
                    case 1:
                        item['lockStatus'] = this.$t('batteryMonitor.attrValues.locked');
                        break;
                    case 2:
                        item['lockStatus'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['lockStatus'] = '--';
                }
                switch (item.latitude) {
                    case '':
                        item.latitude = '--';
                        break;
                }

                switch (item.longitude) {
                    case '':
                        item.longitude = '--';
                        break;
                }

                switch (item.ariseTime) {
                    case '':
                        item.ariseTime = '--';
                        break;
                }
            });
        },

        // 导出按钮
        exportBegin() {
            this.isExport = true;
            this.filterData();
            this.btnControl();
        },
        // 按钮禁止连续点击
        btnControl() {
            this.btnDisabled = true;
            setTimeout(() => {
                this.btnDisabled = false;
            }, 1000);
        },
        exportEnd() {
            // 获取当前日期和时间
            const dateObj = new Date();
            const year = dateObj.getFullYear();
            const month = `0${dateObj.getMonth() + 1}`.slice(-2);
            const day = `0${dateObj.getDate()}`.slice(-2);
            const hour = `0${dateObj.getHours()}`.slice(-2);
            const min = `0${dateObj.getMinutes()}`.slice(-2);

            // 创建文件名和工作表名
            const date = `${year}${month}${day}`;
            const time = `${hour}${min}`;
            const sheetName = `batteryList_${date}${time}`;
            const filename = `batteryList_${date}${time}.xlsx`;

            // 过滤不需要的属性，并转换成自定义列表数据格式
            let temp = this.transferData(this.exportTableData, fields);

            // 创建工作表和工作簿
            const worksheet = XLSX.utils.aoa_to_sheet(temp);
            const workbook = XLSX.utils.book_new();
            workbook['SheetNames'] = [sheetName];
            workbook['Sheets'] = { [sheetName]: worksheet };

            // 写入文件
            XLSX.writeFile(workbook, filename);
            this.isExport = false;
        },
        transferData(data, fields) {
            let content = [],
                header = [];
            for (let i in fields) {
                header.push(this.$t(`batteryMonitor.attrNames.${fields[i]}`));
            }
            content.push(header);
            data.forEach(item => {
                content.push(fields.map(field => item[field]));
            });
            return content;
        },
        // 表格排序
        tableSortChange(column) {
            this.sort = column.prop || '';
            if (column.order === 'descending') {
                this.order = 'desc';
            } else if (column.order === 'ascending') {
                this.order = 'asc';
            } else {
                this.order = '';
                this.sort = '';
            }
            this.filterData();
        },

        // 表格size大小
        tablePageSizeChange(size) {
            this.pageSize = size;
            this.pageNo = 1;
            this.filterData();
        },
        // 表格cur page
        tablePageChange(curPage) {
            this.pageNo = curPage;
            this.filterData();
        },

        csvDataI18N(csvData) {
            let contentELem = csvData.split('\n');
            let titlesStr = contentELem.splice(0, 1)[0].replace(/\"/g, '');
            let titles = titlesStr.split(',');

            let translatedTitles = titles
                .map(d => {
                    return this.$t(`batteryMonitor.attrNames.${d}`);
                })
                .join(',');

            let result = `${translatedTitles}\n${contentELem.join('\n')}`;

            return result;
        },

        // 页面跳转：跳转到全局地图页面
        changeToGlobalMap() {
            this.$emit('changePage', 'BatteryGlobalMap');
        },

        // 接收筛选框筛选后的数据
        filterData(filterOptions) {
            if (filterOptions) {
                this.oldFilter = JSON.parse(JSON.stringify(filterOptions));
            }
            /* Started by AICoder, pid:1c4433e5ceyc3a61473f0a8f50affb0a7889664a */
            let filterOptioinsInTable = filterOptions ||
                this.oldFilter || {
                    realGroupIds: [], // 区域
                    siteName: '', // 站点名称
                    motionState: '', // 运动状态
                    fortificationState: '', // 设防状态
                    communicationStatus: '', // 通讯状态
                    siteLevel: [], // 站点等级
                };
            /* Ended by AICoder, pid:1c4433e5ceyc3a61473f0a8f50affb0a7889664a */
            filterOptioinsInTable.order = this.order;
            filterOptioinsInTable.sort = this.sort;
            if (!this.isExport) {
                filterOptioinsInTable.pageSize = this.pageSize;
                filterOptioinsInTable.pageNo = this.pageNo;
            } else {
                filterOptioinsInTable.pageSize = null;
                filterOptioinsInTable.pageNo = null;
            }
            if (filterOptions) {
                this.pageNo = 1;
                filterOptioinsInTable.pageNo = 1;
            }

            this.queryData(filterOptioinsInTable);
        },

        querySiteLevels() {
            return new Promise(resolve => {
                HTTP.request('/api/configuration/v1/site-level', {
                    complete: data => {
                        if (data.code == 0) {
                            let siteLevelMap = {};
                            Object.keys(data.data).forEach(key => {
                                let currData = data.data[key];
                                siteLevelMap[currData.id] = currData.name;
                            });
                            resolve(siteLevelMap);
                        }
                    },
                    error: data => {
                        this.loading = false;
                    },
                });
            });
        },

        // 得到异步数据
        queryData(filterOptions) {
            if (!filterOptions.lockStatus) {
                filterOptions.lockStatus = '';
            }
            if (!filterOptions.theftStatus) {
                filterOptions.theftStatus = '';
            }
            let _this = this;
            this.loading = true;

            this.querySiteLevels().then(siteLevelMap => {
                HTTP.request('batterySearch', {
                    method: 'post',
                    data: filterOptions,
                    complete: data => {
                        if (data.code == 0) {
                            processData(data.data, siteLevelMap);
                            this.total = data.total;
                        } else {
                            this.$message({
                                message: this.$t('tipMessage.selectError'),
                                type: 'error',
                            });
                        }
                        this.loading = false;
                    },
                    error: data => {
                        this.loading = false;
                        this.$message({
                            message: data.response.data,
                            type: 'error',
                        });
                    },
                });
            });

            function processData(data, siteLevelMap) {
                _this.totalData = data;
                _this.eachrtKey += 1;

                let tableData = data.batteryList;

                if (tableData.length) {
                    _this.exportTableData = _this.handleExportTableData(copyTableData(tableData));
                    if (!_this.isExport) {
                        _this.tableData = tableData;
                    }
                } else {
                    _this.tableData = [];
                    _this.exportTableData = [];
                }
                if (_this.isExport) {
                    _this.exportEnd();
                }
            }
        },

        setSafetyArea() {
            this.safetyAreaDialogVisible = true;
        },
        handleSetSafetyAreaFn() {
            this.getSafeArea();
            this.safetyAreaDialogVisible = false;
        },
        handleSetSafetyArea() {
            HTTP.request('setSafeArea', {
                method: 'get',
                urlParam: {
                    safeArea: this.sliderValue,
                },
                complete: data => {
                    if (data.code == 0) {
                        this.$message({
                            message: this.$t('tipMessage.operationSuccsee'),
                            type: 'success',
                        });
                        this.safetyAreaDialogVisible = false;
                    }
                },
                error: error => {
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        /* Started by AICoder, pid:cbb22aeca9251c0146ce0a811058611466a84deb */
        handleErrorMsg(data) {
            let translatedMessage = '';
            let parsedData = {};

            try {
                parsedData = JSON.parse(data) || {};
            } catch (e) {
                // ignore
            }

            if (this.lang && this.lang.includes('en')) {
                translatedMessage = parsedData['en_US'] || '';
            } else if (this.lang && this.lang.includes('zh')) {
                translatedMessage = parsedData['zh_CN'] || '';
            }

            return translatedMessage;
        },
        /* Ended by AICoder, pid:cbb22aeca9251c0146ce0a811058611466a84deb */
        handleElectronicKey() {
            this.electronicKeyDialogVisible = true;
        },
        closeElectronicKeyDialog() {
            this.electronicKeyDialogVisible = false;
        },
        openElectronicKeyDialog() {
            this.queryKey();
        },
        actionUrl() {
            return `/api/battery-manager/v1/battery-safe/import-ekey`;
        },
        headers() {
            return {
                forgerydefense: window.forgerydefense || '',
            };
        },
        queryKey() {
            HTTP.request('queryKey', {
                method: 'post',
                complete: data => {
                    if (data.code === 0) {
                        this.updateTime = data.data || '--';
                    } else {
                        this.$message({
                            message: this.handleErrorMsg(data.message),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        cancelEncrypt() {
            this.encryptDialogVisible = false;
            // 延时重置表格，避免界面上看到表单置空
            setTimeout(() => {
                this.$refs.encryptForm.resetFields();
            }, 20);
        },
        handleOpenEncrypt() {
            this.encryptDialogVisible = true;
        },
        encryptDownloadSubmit() {
            // 校验密码强度，通过则调用download接口
            this.$refs.encryptForm.validate(valid => {
                if (valid) {
                    this.handleExportKey();
                    this.encryptDialogVisible = false;
                    // 延时重置表格，避免界面上看到表单置空
                    setTimeout(() => {
                        this.$refs.encryptForm.resetFields();
                    }, 500);
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        handleChange(file, fileList) {
            if (file.name.includes('.zip')) {
                let zipFile = file.raw;
                this.parseZip(zipFile);
            }
        },
        decryptFormChange() {
            if (this.showDecryptError) {
                this.showDecryptError = false;
                this.$refs.decryptForm.validateField('passwd');
            }
        },
        cancelDecrypt() {
            this.decryptDialogVisible = false;
            // 延时重置表格，避免界面上看到表单置空
            setTimeout(() => {
                this.$refs.decryptForm.resetFields();
                this.zipFile = null;
            }, 20);
        },
        // 处理 zip 文件
        async parseZip(zipFile) {
            let zipFiles = await new zip.ZipReader(new zip.BlobReader(zipFile)).getEntries({});
            const encrypted = zipFiles.every(entry => entry.encrypted);
            if (encrypted) {
                // 判断压缩方法是否支持
                try {
                    await zipFiles[0].getData(new zip.BlobWriter(), {});
                } catch (err) {
                    // 如果解压失败，判断 err 信息并提示
                    if (err.toString().includes('Compression method not supported')) {
                        this.$message({
                            message: this.$t('batteryMonitor.message.unsupportedCompressionMethod'),
                            duration: 5000,
                            showClose: true,
                            type: 'warning',
                        });
                        return;
                    }
                }
                // 加密zip文件，弹出解密弹框，使用 zip.js 解密
                this.decryptDialogVisible = true;
                this.zipFile = zipFile;
                this.$nextTick(() => {
                    this.$refs.decryptInput?.focus();
                });
            } else {
                this.$message({
                    message: this.$t('batteryMonitor.message.noEncrypt'),
                    duration: 5000,
                    showClose: true,
                    type: 'warning',
                });
            }
        },
        // 解密 zip 文件
        async decryptSubmit() {
            // 读取blob文件内容
            async function readFileContent(file) {
                const reader = new FileReader();
                return new Promise((resolve, reject) => {
                    reader.onload = function () {
                        const content = reader.result;
                        resolve(content);
                    };
                    reader.onerror = function () {
                        reject(reader.error);
                    };
                    reader.readAsText(file);
                });
            }
            this.$refs.decryptForm.validate(async valid => {
                if (valid) {
                    const passwd = this.decryptForm.passwd;
                    let zipFiles = await new zip.ZipReader(new zip.BlobReader(this.zipFile)).getEntries({
                        password: passwd,
                    });
                    try {
                        await zipFiles[0].getData(new zip.BlobWriter(), {
                            password: passwd,
                        });
                    } catch (err) {
                        // 如果解压失败，提示解压密码错误，并发送日志到后端
                        this.showDecryptError = true;
                        this.$refs.decryptForm.validateField('passwd');
                        return;
                    }
                    // 开始校验文件，关闭密码输入框，弹出校验中提示
                    this.decryptDialogVisible = false;

                    // 延时操作，刷新 DOM
                    setTimeout(async () => {
                        let postFiles = [];
                        for (let item of zipFiles) {
                            // 单文件限制大小，避免 zip 炸弹攻击
                            if (item.uncompressedSize > MAX_FILE_SIZE) {
                                console.warn('The file size exceeds the limit: ', item.uncompressedSize);
                                break;
                            }
                            let file = null;
                            try {
                                // 解压文件
                                file = await item.getData(new zip.BlobWriter(), {
                                    password: passwd,
                                });
                            } catch (err) {
                                break; // 部分文件解压密码不一致，说明压缩包被篡改
                            }
                            let unzipFile = new File([file], item.filename);
                            postFiles.push(unzipFile);
                        }
                        // 判断文件列表数量是否全部匹配，如果不匹配则退出
                        if (postFiles.length !== zipFiles.length) {
                            this.$message({
                                message: this.$t('batteryMonitor.message.fileCheckFail'),
                                duration: 5000,
                                showClose: true,
                                type: 'error',
                            });
                        } else {
                            // 加入上传文件列表
                            postFiles.forEach(rawFile => {
                                this.upload(rawFile);
                            });
                        }
                        // 延时重置表格，避免界面上看到表单置空
                        setTimeout(() => {
                            this.$refs.decryptForm.resetFields();
                            this.zipFile = null;
                        }, 500);
                    }, 10);
                } else {
                    return false;
                }
            });
        },
        upload(item) {
            if (!item || item.name === '') {
                this.$message({
                    message: this.$t('tipMessage.selectFileWarning'),
                    type: 'error',
                });
            } else {
                let fileName = item.name;
                if (/\.txt?$/i.test(fileName)) {
                    let url = this.actionUrl();
                    let formData = new FormData();
                    formData.append('file', item);
                    let config = {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            forgerydefense: window.forgerydefense || '',
                        },
                    };

                    return axios
                        .post(url, formData, config)
                        .then(response => {
                            let d = response.data;
                            if (d.code === 0 && d.data) {
                                this.currentFile = item;
                                this.currentFileId = d.data;
                                this.queryKey();
                                this.$message({
                                    message: this.$t('tipMessage.importSuccess'),
                                    type: 'success',
                                });
                            } else {
                                let msg = handelErrMessage(response.data); // 统一错误码处理
                                this.$message({
                                    message: msg || this.$t('tipMessage.importError'),
                                    type: 'error',
                                });
                            }
                        })
                        .catch(
                            // eslint-disable-next-line no-unused-vars
                            error => {
                                // console.log('导入 catch error', error);
                                let msg = handelErrCode(error.response.data); // 统一错误码处理
                                this.$message({
                                    message: msg || this.$t('tipMessage.importError'),
                                    type: 'error',
                                });

                                this.uploadFiles.forEach(file => {
                                    if (file.uid === item.uid) {
                                        file.status = 'error';
                                        file.curErrorCode = -1000;
                                        file.curErrorMessage = '';
                                    }
                                });

                                $emit(this, 'upload-error');
                            }
                        );
                } else {
                    console.warn('Unsupported file format: ', item.filename);
                    this.$message({
                        message: this.$t('tipMessage.fileFormatError'),
                        type: 'error',
                    });
                }
            }
        },
        /* Started by AICoder, pid:fb19edf8e2ka94414d9e096fc04b956406b7561b */
        async handleExportKey() {
            const url = '/api/battery-manager/v1/battery-safe/export-ekey';
            const config = {
                responseType: 'blob',
                headers: {
                    forgerydefense: window.forgerydefense || '',
                },
            };
            let params = {
                passwd: await encryptRSA(this.encryptForm.passwd),
            };
            axios.post(url, params, config).then(res => {
                if (res.data.type === 'application/json') {
                    // 导出错误，文件数据类型不对
                    let reader = new FileReader();
                    reader.onload = e => {
                        let result;
                        try {
                            result = JSON.parse(e.target.result);
                        } catch (error) {
                            return;
                        }
                        if (result && result.code !== 0) {
                            this.$message({
                                message: this.handleErrorMsg(result.message),
                                duration: 5000,
                                showClose: true,
                                type: 'error',
                            });
                        }
                    };
                    reader.readAsText(res.data, 'utf-8');
                } else {
                    // 导出成功，返回数据流
                    this.exportLoading = false;
                    const blob = new Blob([res.data]);
                    const url = window.URL.createObjectURL(blob); // 创建下载的链接
                    const link = document.createElement('a');
                    let fileName = '';

                    if (res.headers['content-disposition']) {
                        const contentDisposition = res.headers['content-disposition'];
                        const match = contentDisposition.match(/filename\*?=(.*)/);
                        if (match) {
                            fileName = decodeURIComponent(match[1].replace(/\+/g, '%20'));
                        }
                    }

                    link.href = url;
                    link.download = fileName; // 下载后文件名
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click(); // 点击下载
                    setTimeout(() => {
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }, 100);
                }
            });
        },
        regenerateKeyHandler() {
            this.confirmInfo.show = true;
        },
        /* Ended by AICoder, pid:fb19edf8e2ka94414d9e096fc04b956406b7561b */
        handleRegenerateKey() {
            HTTP.request('changeKey', {
                method: 'post',
                complete: data => {
                    if (data.code === 0) {
                        this.queryKey();
                        this.$message({
                            message: this.$t('tipMessage.operationSuccsee'),
                            type: 'success',
                        });
                    } else {
                        this.$message({
                            message: this.handleErrorMsg(data.message),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        batchFortify() {
            this.$refs.table.batchFortify();
        },

        batchWithdraw() {
            this.$refs.table.batchWithdraw();
        },

        batchUnlock() {
            this.$refs.table.batchUnlock();
        },

        formatTooltip(val) {
            return `${val}m`;
        },
        sliderValChange() {
            let text = document.createElement('div');
            text.innerHTML = `${this.sliderValue}m`;
            text.className = 'el-slider__marks-text';
            text.className += ' new_mark';
            text.style.left = `${this.sliderValue / 6}%`;
            text.style.transform = 'translate(-40px, -50px)';
            let oldText = document.getElementsByClassName('new_mark');
            if (oldText.length > 0) {
                oldText[0].remove();
            }

            document.getElementsByClassName('el-slider__marks')[0].appendChild(text);
        },

        tableDataSelected(data) {
            if (data.length) {
                this.disabledFortification = false;
                this.disabledWithdraw = false;
                this.disabledUnlock = false;
            } else {
                this.disabledFortification = true;
                this.disabledWithdraw = true;
                this.disabledUnlock = true;
            }
            data.forEach(item => {
                if (parseInt(item.fortificationState) !== 0 || !item.hasFortification) {
                    this.disabledFortification = true;
                }
                if (parseInt(item.fortificationState) !== 1 || !item.hasWithdraw) {
                    this.disabledWithdraw = true;
                }
                if (parseInt(item.lockStatus) !== 1 || !item.canUnlock) {
                    this.disabledUnlock = true;
                }
            });
            this.selectedData = data;
        },

        getSafeArea() {
            HTTP.request('getSafeArea', {
                method: 'get',
                complete: data => {
                    if (data.code == 0) {
                        this.sliderValue = data.data;
                    }
                },
            });
        },
    },
};

function copyTableData(tableData) {
    let result = [];
    tableData.forEach(d => {
        result.push(Object.assign({}, d));
    });
    return result;
}
</script>

<style lang="scss" scoped>
.batteryForm {
    height: 100%;
    box-sizing: border-box;
    background-color: #fff;
}
.filterButtons .filterPop {
    width: 325px;
}
.firstTitle-16px {
    float: left;
    font-size: 16px;
    color: #333;
}
.btn-container {
    padding: 4px 0 8px;
}

.elem-container {
    margin: 10px;
    padding: 10px 10px 0;
    box-shadow:
        2px 2px 2px rgb(173, 173, 173),
        inset 1px 1px 1px gainsboro;
    border-radius: 5px;
    background-color: white;
}

/* 整体筛选框的样式 */
.battery-filter-condition {
    position: relative;
    z-index: 999;
    margin-bottom: 25px;
}

.safety-area {
    float: right;
    margin-left: 8px;
}
.safety-area-content {
    margin-right: 10px;
    .title {
        float: left;
        height: 45px;
        line-height: 45px;
        vertical-align: middle;
        width: 120px;
    }
    .slider {
        margin-left: 120px;
    }
}

.bottomBar {
    text-align: center;
    margin-top: 30px;
}
.electronickey-content-item {
    text-align: center;
}
.upload-upgrade-file {
    margin-right: 8px;
    vertical-align: middle;
}

.el-collapse :deep() {
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;

    .el-collapse-item__header {
        position: relative;

        /* 此处修改折叠按钮的字体大小 */
        font-size: 14px;
        padding-left: 12px;
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;
        color: #737373 !important;
        font-weight: bold;
        height: inherit;
        line-height: inherit;
        padding-top: 10px;
        padding-bottom: 16px;
        pointer-events: none;

        &::before {
            content: '';
            position: absolute;
            width: 4px;
            height: 16px;
            background: #0f8dfb;
            left: 0;
        }

        /* 注释掉分割线部分，不需要分割线 */

        /* &:after {
            content: "";
            position: absolute;
            width: 100%;
            height: 1px;
            background: #e3e3e3;
            left: 110px;
        } */
    }

    .el-collapse-item__arrow {
        margin: 0;
        font-weight: 600;
        pointer-events: auto;
        padding: 8px;
        background-color: #fff;
        z-index: 0;
    }

    .collapse-item-title {
        height: 16px;
        line-height: 16px;
        text-align: justify;

        /* z-index: 2002; */

        z-index: 0;

        // 中文标题两端对齐
        &::after {
            content: '';
            display: inline-block;
            width: 100%;
        }
    }

    .el-collapse-item__wrap {
        border-bottom: 1px solid transparent;
    }
}
:deep() .el-slider__marks {
    .el-slider__marks-text {
        text-align: center;
        width: 80px;
    }
}
:deep() .el-form-item__content .el-date-editor.el-input__inner {
    width: 300px;
}
body.theme-dark .bottomBar ::v-deep .el-button--default {
    color: #1993ff !important;
}
.encrypt-dialog {
    :deep(.el-input__inner) {
        width: 100%;
    }

    .tip-info {
        margin-top: 4px;
        font-size: 12px;
        color: #bfbfbf;
        line-height: 1.8;
        word-break: break-word;
    }
}
.decrypt-dialog {
    :deep(.el-input__inner) {
        width: 100%;
    }
    .dialog-footer {
        text-align: center;
    }
    .el-button + .el-button {
        margin-left: 8px;
    }
    .tip-info {
        margin-top: 4px;
        font-size: 12px;
        color: #bfbfbf;
        line-height: 1.8;
        word-break: break-word;
    }
}
html.dark {
    .batteryForm {
        background-color: #1d1d1d;
    }
    .firstTitle-16px {
        color: #d9d9d9;
    }
    .decrypt-dialog,
    .encrypt-dialog {
        .tip-info {
            color: #666;
        }
    }
}
</style>
