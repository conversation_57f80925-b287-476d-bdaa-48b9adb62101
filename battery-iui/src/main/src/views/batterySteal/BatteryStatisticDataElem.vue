<template>
    <!-- 电池统计页面中的统计框，包含一项数据、两项数据两种情况 -->
    <div class="container">
        <!-- 标题 -->
        <!-- <div v-if="battData.data.length !== 1" class="title">{{ battData.title }}</div> -->
        <div class="title">
            {{ i18nLabel(battData.title) }}
        </div>

        <!-- {{ $t('batteryMonitor.btnLables.reset') }} -->
        <!-- 只包含一项数据 -->
        <div v-if="battData.data.length === 1" class="batt-data-container single-data">
            <div class="single-label" :title="i18nLabel(battData.data[0].label)">
                {{ i18nLabel(battData.data[0].label) }}
            </div>
            <div class="single-value">
                {{ battData.data[0].value }}
            </div>
        </div>

        <!-- 包含两项数据 -->
        <div v-else class="batt-data-container double-data">
            <!-- 左侧数据 -->
            <div class="left" style="display: inline-block;">
                <div class="double-label" :title="i18nLabel(battData.data[0].label)">
                    {{ i18nLabel(battData.data[0].label) }}
                </div>
                <div class="double-value">
                    {{ battData.data[0].value }}
                </div>
            </div>
            <!-- 垂直分割线 -->
            <el-divider direction="vertical" class="split"></el-divider>
            <!-- 中侧数据 -->
            <div class="right" style="display: inline-block;">
                <div class="double-label" :title="i18nLabel(battData.data[1].label)">
                    {{ i18nLabel(battData.data[1].label) }}
                </div>
                <div class="double-value">
                    {{ battData.data[1].value }}
                </div>
            </div>
            <el-divider direction="vertical" class="split"></el-divider>
            <!-- 右侧数据 -->
            <div class="right" style="display: inline-block;">
                <div class="double-label" :title="i18nLabel(battData.data[2].label)">
                    {{ i18nLabel(battData.data[2].label) }}
                </div>
                <div class="double-value">
                    {{ battData.data[2].value }}
                </div>
                <el-tooltip :effect="lightOrDark" :content="i18nLabel(battData.data[3].label)" placement="top">
                    <i class="el-icon-question"></i>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        battData: {
            type: Object,
            default: () => {
                return {
                    title: 'no data',
                    data: [{ label: 'label', value: 0 }],
                };
            },
        },
    },
    computed: {
        lightOrDark() {
            if (this.$store.getters.getIsDark) {
                return 'dark';
            } else {
                return 'light';
            }
        },
    },

    methods: {
        i18nLabel(val) {
            return this.$t(`batteryMonitor.battStatisticLabel.${val}`);
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    left: 16%;
}

.batt-data-container {
    border-radius: 5px;
    width: 200px;
    height: 100px;
    border: 1px solid #d9d9d9;

    /* box-shadow: 2px 2px 2px rgb(173, 173, 173), inset 1px 1px 1px gainsboro; */
}

.title {
    padding: 5px 5px 0;
}

/* 只包含一项数据的情况 */
.single-data {
    text-align: center;
}
.single-label {
    font-size: 1.5em;
    margin-top: 10px;
}
.single-value,
.double-value {
    font-size: 2.5em;
    color: #5b9fec;
}

/* 包含两项数据的情况 */
.double-data {
    line-height: 30px;
    width: 300px;
}
.double-label {
    font-size: 1.5em;
    margin: 10px 0;
    width: 82px;

    /* 不换行，限制在一行 */
    white-space: nowrap;

    /* 超过部分用...标识 */
    text-overflow: ellipsis;

    /* 隐藏超过部分 */
    overflow: hidden;
}
.split {
    position: relative;
    float: left;
    left: 4%;
    height: 6em;
    margin-top: 10px;
}
.left {
    position: relative;
    float: left;
    left: 9px;
    text-align: center;
}
.right {
    position: relative;
    float: left;
    left: 9px;
    text-align: center;
    i {
        font-size: 14px;
        color: #5b9fec;
        position: absolute;
        right: -10px;
        top: 4px;
    }
}
</style>
