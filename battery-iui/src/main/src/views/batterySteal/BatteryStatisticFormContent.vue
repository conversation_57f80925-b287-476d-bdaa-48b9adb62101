<template>
    <!-- 电池列表主体，包含分页 -->
    <div>
        <el-table
            ref="multipleTable"
            :key="tableKey"
            :data="list"
            style="width: 100%;"
            :stripe="true"
            :row-key="getRowKey"
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
        >
            <!-- 首列多选 -->
            <el-table-column
                type="selection"
                :reserve-selection="true"
                :selectable="rowSelectable"
                width="55"
            ></el-table-column>
            <el-table-column
                min-width="120"
                prop="batteryName"
                sortable="custom"
                show-overflow-tooltip
                :label="$t('batteryMonitor.attrNames.batteryName')"
            >
                <template #default="scope">
                    {{ scope.row.batteryName }}
                </template>
            </el-table-column>
            <el-table-column
                min-width="120"
                prop="realGroupName"
                sortable="custom"
                show-overflow-tooltip
                :label="$t('batteryMonitor.attrNames.realGroupName')"
            ></el-table-column>
            <el-table-column
                min-width="120"
                prop="siteName"
                sortable="custom"
                show-overflow-tooltip
                :label="$t('batteryMonitor.attrNames.siteName')"
            ></el-table-column>
            <el-table-column
                v-if="tableFilterItem['fortificationState']"
                min-width="150"
                prop="fortificationState_i18n"
                :label="$t('batteryMonitor.attrNames.fortificationState')"
            ></el-table-column>
            <el-table-column
                v-if="tableFilterItem['communicationStatus']"
                min-width="120"
                prop="communicationStatus_i18n"
                :label="$t('batteryMonitor.attrNames.communicationStatus')"
            ></el-table-column>

            <el-table-column
                v-if="tableFilterItem['lockStatus']"
                min-width="120"
                prop="lockStatus_i18n"
                :label="$t('batteryMonitor.attrNames.lockStatus')"
            ></el-table-column>

            <el-table-column
                v-if="tableFilterItem['motionState']"
                min-width="120"
                prop="motionState_i18n"
                :label="$t('batteryMonitor.attrNames.motionState')"
            ></el-table-column>
            <el-table-column
                v-if="tableFilterItem['theftStatus']"
                min-width="125"
                prop="theftStatus_i18n"
                :label="$t('batteryMonitor.attrNames.theftStatus')"
            >
                <template #default="scope">
                    <i class="circle-class" :style="{ background: getBgColor(parseInt(scope.row.theftStatus)) }"></i>
                    {{ scope.row.theftStatus_i18n }}
                </template>
            </el-table-column>

            <el-table-column
                v-if="tableFilterItem['latitude']"
                min-width="90"
                prop="latitude"
                :label="$t('batteryMonitor.attrNames.latitude')"
            ></el-table-column>
            <el-table-column
                v-if="tableFilterItem['longitude']"
                min-width="90"
                prop="longitude"
                :label="$t('batteryMonitor.attrNames.longitude')"
            ></el-table-column>
            <el-table-column
                v-if="tableFilterItem['ariseTime']"
                min-width="155"
                sortable="custom"
                prop="ariseTime"
                :label="$t('batteryMonitor.attrNames.updateTime')"
            ></el-table-column>

            <el-table-column
                v-if="tableFilterItem['siteLevel']"
                min-width="90"
                prop="siteLevel"
                :label="$t('batteryMonitor.attrNames.siteLevel')"
            ></el-table-column>
            <!-- “操作”列 -->
            <el-table-column
                :label="$t('batteryMonitor.labels.operations')"
                fixed="right"
                align="center"
                :width="languageOptionWidth"
            >
                <template #default="scope">
                    <!-- 撤防 -->
                    <template v-if="rights['c.battery.fortification'] && parseInt(scope.row.fortificationState) === 1">
                        <el-button
                            v-if="isNetwork && scope.row.supportNetworkStatus"
                            type="text"
                            @click="fortifiyBtnClick(scope.row)"
                        >
                            {{ $t('batteryMonitor.attrValues.withdraw') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="isNetwork && !scope.row.supportNetworkStatus"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.networkDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.withdraw') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="!isBmsBcuaHcgps(scope.row.deviceModule)"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.protocolTypeDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.withdraw') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="parseInt(scope.row.communicationStatus) !== 0"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.communicationIsNt0')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.withdraw') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="!scope.row.hasWithdraw"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.commandExecuteDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.withdraw') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-button v-else type="text" @click="fortifiyBtnClick(scope.row)">
                            {{ $t('batteryMonitor.attrValues.withdraw') }}
                        </el-button>
                    </template>
                    <!-- 设防 -->
                    <template v-if="rights['c.battery.fortification'] && parseInt(scope.row.fortificationState) !== 1">
                        <el-button
                            v-if="isNetwork && scope.row.supportNetworkStatus"
                            type="text"
                            @click="fortifiyBtnClick(scope.row)"
                        >
                            {{ $t('batteryMonitor.attrValues.fortification') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="isNetwork && !scope.row.supportNetworkStatus"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.networkDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.fortification') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="!isBmsBcuaHcgps(scope.row.deviceModule)"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.protocolTypeDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.fortification') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="parseInt(scope.row.communicationStatus) !== 0"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.communicationIsNt0')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.fortification') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="parseInt(scope.row.fortificationState) !== 0"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.unWithdrawDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.fortification') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="!scope.row.hasFortification"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.commandExecuteDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.attrValues.fortification') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-button v-else type="text" @click="fortifiyBtnClick(scope.row)">
                            {{ $t('batteryMonitor.attrValues.fortification') }}
                        </el-button>
                    </template>
                    <!-- 解锁 -->
                    <template v-if="rights['c.battery.unlock']">
                        <!-- Started by AICoder, pid:t9845730ees4b11144100a254047c01807435eb4 -->
                        <el-tooltip
                            v-if="isNetwork"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.networkUnlockDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.unlock') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <!-- Ended by AICoder, pid:t9845730ees4b11144100a254047c01807435eb4 -->
                        <el-tooltip
                            v-else-if="!isBmsBcuaHcgps(scope.row.deviceModule)"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.deviceModuleDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.unlock') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="parseInt(scope.row.communicationStatus) !== 0"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.communicationIsNt0')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.unlock') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="parseInt(scope.row.lockStatus) !== 1"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.unlockDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.unlock') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="!scope.row.canUnlock"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.infowindow.commandExecuteDisabled')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.unlock') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-button v-else type="text" @click="lockBtnClick(scope.row)">
                            {{ $t('batteryMonitor.btnLabels.unlock') }}
                        </el-button>
                    </template>

                    <!-- 位置设置 -->
                    <template v-if="rights['c.battery.setPosition']">
                        <el-tooltip
                            v-if="disableTrailLink"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.message.mapWarnning')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.setPosition') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="scope.row.latitude === '' || scope.row.latitude === '--'"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.message.emptyLatLng')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.setPosition') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <router-link
                            v-else
                            target="_blank"
                            :to="getTrailMapRoute(scope.row.moId, 'steal')"
                        >
                            <el-button type="text">
                                {{ $t('batteryMonitor.btnLabels.setPosition') }}
                            </el-button>
                        </router-link>
                    </template>
                    <!-- 轨迹 -->
                    <template v-if="rights['c.battery.battTrail']">
                        <el-tooltip
                            v-if="disableTrailLink"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.message.mapWarnning')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.battTrail') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <el-tooltip
                            v-else-if="scope.row.latitude === '' || scope.row.latitude === '--'"
                            :enterable="false"
                            effect="dark"
                            :content="$t('batteryMonitor.message.emptyLatLng')"
                            placement="top"
                        >
                            <span>
                                <el-button type="text" disabled>
                                    {{ $t('batteryMonitor.btnLabels.battTrail') }}
                                </el-button>
                            </span>
                        </el-tooltip>
                        <router-link
                            v-else
                            target="_blank"
                            :to="getTrailMapRoute(scope.row.moId, 'default')"
                        >
                            <el-button v-if="rights['c.battery.battTrail']" type="text">
                                {{ $t('batteryMonitor.btnLabels.battTrail') }}
                            </el-button>
                        </router-link>
                    </template>

                    <!-- 记录 -->
                    <el-button
                        v-if="
                            rights['c.battery.fortification.record'] ||
                            rights['c.battery.setPosition.record'] ||
                            rights['c.battery.unlock.record']
                        "
                        type="text"
                        @click="allRecord(scope.row)"
                    >
                        {{ $t('batteryMonitor.btnLabels.record') }}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页栏 -->
        <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 30]"
            layout="total, sizes, prev, pager, next,jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>

        <!-- 记录 -->
        <el-dialog
            v-if="recordVisible"
            v-model="recordVisible"
            :class="['threeRecord', { 'dark-theme': isDark }]"
            :title="$t('batteryMonitor.btnLabels.record')"
            :destroy-on-close="false"
            width="940px"
        >
            <el-tabs v-model="activeName" class="dialog-tab" @tab-click="handleClick">
                <!-- 布放记录 -->
                <!-- 网络级不显示布放记录 -->
                <el-tab-pane
                    v-if="rights['c.battery.fortification.record'] && !isNetwork"
                    :label="$t('batteryMonitor.btnLabels.fortifyRecord')"
                    name="first"
                >
                    <el-form :inline="true" :model="searchFormData" label-width="120px" label-position="right">
                        <el-form-item :label="$t('batteryMonitor.labels.optTime')">
                            <el-date-picker
                                v-model="searchFormData.optTime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="datetimerange"
                                range-separator="-"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchFortifyRecordClick">
                                {{ $t('button.search') }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                    <el-table :data="fortifyRecords" style="width: 100%;" height="350" stripe>
                        <el-table-column
                            prop="updateTime"
                            width="150px"
                            :label="$t('batteryMonitor.attrNames.optTime')"
                        ></el-table-column>
                        <el-table-column
                            prop="operator"
                            :label="$t('batteryMonitor.attrNames.optUser')"
                        ></el-table-column>
                        <el-table-column
                            prop="detail_i18n"
                            :label="$t('batteryMonitor.attrNames.excuteType')"
                        ></el-table-column>
                        <el-table-column prop="status_i18n" :label="$t('batteryMonitor.attrNames.excuteStatus')">
                            <template #default="scope">
                                <i
                                    class="circle-class"
                                    :style="{ background: getBgColorByStatus(scope.row.status) }"
                                ></i>
                                {{ scope.row.status_i18n }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="description"
                            :label="$t('batteryMonitor.attrNames.descriptionInfo')"
                            show-overflow-tooltip
                        ></el-table-column>
                    </el-table>
                    <el-pagination
                        v-model:page-size="fortifyRecordsPageSize"
                        :current-page="fortifyRecordsPageNo"
                        :page-sizes="[5, 10, 20, 30]"
                        layout="total, sizes, prev, pager, next"
                        :total="fortifyRecordsTotal"
                        @size-change="fortifyRecordsSizeChange"
                        @current-change="fortifyRecordsPageChange"
                    ></el-pagination>
                </el-tab-pane>
                <!-- 位置状态记录 -->
                <el-tab-pane
                    v-if="rights['c.battery.setPosition.record']"
                    :label="$t('batteryMonitor.btnLabels.stealRecord')"
                    name="second"
                >
                    <el-form :inline="true" :model="searchFormData" label-width="120px" label-position="right">
                        <el-form-item :label="$t('batteryMonitor.labels.optTime')">
                            <el-date-picker
                                v-model="searchFormData.optTime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="datetimerange"
                                range-separator="-"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchStealRecordClick">
                                {{ $t('button.search') }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                    <el-table
                        v-loading="stealRecordsLoading"
                        :data="stealRecordsData"
                        style="width: 100%;"
                        height="350"
                        stripe
                    >
                        <el-table-column
                            prop="updateTime"
                            width="150px"
                            :label="$t('batteryMonitor.attrNames.optTime')"
                        ></el-table-column>
                        <el-table-column
                            prop="originalLocation"
                            :label="$t('batteryMonitor.attrNames.originPosition')"
                            show-overflow-tooltip
                        ></el-table-column>
                        <el-table-column
                            prop="currentLocation"
                            :label="$t('batteryMonitor.attrNames.currentPosition')"
                            show-overflow-tooltip
                        ></el-table-column>
                        <el-table-column
                            prop="distance"
                            :label="`${$t('batteryMonitor.attrNames.distance')}(m)`"
                        ></el-table-column>
                        <el-table-column prop="theftStatus" :label="$t('batteryMonitor.attrNames.theftStatus')">
                            <template #default="scope">
                                <span :class="{ stealed: parseInt(scope.row.theftStatus) === 1 }">
                                    {{
                                        scope.row.theftStatus == '0'
                                            ? $t('batteryMonitor.attrValues.normal')
                                            : $t('batteryMonitor.attrValues.steal')
                                    }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            width="160"
                            prop="safeArea"
                            :label="`${$t('batteryMonitor.attrNames.safetyPosition')}(m)`"
                        ></el-table-column>
                    </el-table>
                    <el-pagination
                        v-model:page-size="stealRecordsPageSize"
                        :current-page="stealRecordsPageNo"
                        :page-sizes="[5, 10, 20, 30]"
                        layout="total, sizes, prev, pager, next"
                        :total="stealRecordsTotal"
                        @size-change="stealRecordsSizeChange"
                        @current-change="stealRecordsPageChange"
                    ></el-pagination>
                </el-tab-pane>
                <!-- 解锁记录 -->
                <el-tab-pane
                    v-if="rights['c.battery.unlock.record'] && !isNetwork"
                    :label="$t('batteryMonitor.btnLabels.unlockRecord')"
                    name="third"
                >
                    <el-form :inline="true" :model="searchFormData" label-width="120px" label-position="right">
                        <el-form-item :label="$t('batteryMonitor.labels.optTime')">
                            <el-date-picker
                                v-model="searchFormData.optTime"
                                type="datetimerange"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                range-separator="-"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchUnlockRecordClick">
                                {{ $t('button.search') }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                    <el-table :data="unlockRecordList" style="width: 100%;" height="350" stripe>
                        <el-table-column
                            prop="operationDate"
                            width="150px"
                            :label="$t('batteryMonitor.attrNames.optTime')"
                        ></el-table-column>
                        <el-table-column
                            prop="operator"
                            :label="$t('batteryMonitor.attrNames.optUser')"
                        ></el-table-column>
                        <el-table-column
                            prop="operType"
                            :label="$t('batteryMonitor.attrNames.excuteType')"
                        ></el-table-column>
                        <el-table-column prop="status_i18n" :label="$t('batteryMonitor.attrNames.excuteStatus')">
                            <template #default="scope">
                                <i
                                    class="circle-class"
                                    :style="{ background: getBgColorByStatus(scope.row.status) }"
                                ></i>
                                {{ scope.row.status_i18n }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        v-model:page-size="unlockRecordsPageSize"
                        :current-page="unlockRecordsPageNo"
                        :page-sizes="[5, 10, 20, 30]"
                        layout="total, sizes, prev, pager, next"
                        :total="unlockRecordsTotal"
                        @size-change="unlockRecordsSizeChange"
                        @current-change="unlockRecordsPageChange"
                    ></el-pagination>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <!-- 撤防 -->
        <el-dialog
            v-model="withdrawDialogVisible"
            :title="$t('batteryMonitor.btnLabels.withdraw')"
            :destroy-on-close="true"
            width="500px"
        >
            <el-form ref="form" :model="form" :rules="formRules" label-width="120px">
                <el-form-item :label="$t('batteryMonitor.labels.withdrawReason')" prop="reason">
                    <el-input v-model="form.reason" type="textarea" :rows="3" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>
            <div class="bottomBar">
                <el-button type="primary" @click="withdraw">
                    {{ $t('button.confirm') }}
                </el-button>
                <el-button @click="withdrawDialogVisible = false">
                    {{ $t('button.cancel') }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 解锁 -->
        <el-dialog
            v-model="unlockDialogVisible"
            :title="$t('batteryMonitor.btnLabels.isUnlock')"
            :destroy-on-close="true"
            width="500px"
        >
            <span class="darkFontColor-d9" style="padding-left: 16px; font-size: 12px;">
                {{ $t('batteryMonitor.btnLabels.askUnlock') }}
            </span>
            <div class="bottomBar">
                <el-button type="primary" @click="unlockWithdraw">
                    {{ $t('button.confirm') }}
                </el-button>
                <el-button @click="unlockDialogVisible = false">
                    {{ $t('button.cancel') }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 解析弹窗 -->
        <el-dialog
            v-model="tipDialogVisible"
            class="dialog"
            :title="dialogTitle"
            width="900"
            :before-close="handleClose"
        >
            <div class="dialogItem">
                {{ $t('tooltip.Statistics') }}
            </div>
            <div class="dialogItemInfo1">
                <span>
                    {{ $t('tooltip.total') }}
                    <span class="num">{{ msgData.totalNum }}</span>
                </span>
                <span style="margin: 0 200px;">
                    {{ $t('tooltip.success') }}
                    <el-tooltip effect="dark" placement="top">
                        <template #content>
                            <div class="tooltip">{{ $t('batteryMonitor.message.commandTip') }}</div>
                        </template>
                        <i class="plx-ico-help-tip-16 question-tip"></i>
                    </el-tooltip>
                    <span class="num" style="color: green;">{{ msgData.successNum }}</span>
                </span>
                <span>
                    {{ $t('tooltip.err') }}
                    <span class="num" style="color: red;">{{ msgData.failedNum }}</span>
                </span>
            </div>
            <div v-if="!!msgData.failedNum" class="dialogItem">
                {{ $t('tooltip.errorMessage') }}
            </div>
            <div v-if="!!msgData.failedNum" class="dialogItemInfo2">
                <el-table :data="msgData.failedItemList" style="width: 100%;" max-height="245" stripe>
                    <el-table-column type="index" width="50"></el-table-column>
                    <el-table-column
                        width="200"
                        prop="deviceName"
                        sortable="custom"
                        show-overflow-tooltip
                        :label="$t('batteryMonitor.attrNames.batteryName')"
                    >
                        <template #default="scope">
                            {{ scope.row.batteryName }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        width="200"
                        prop="realGroupName"
                        sortable="custom"
                        show-overflow-tooltip
                        :label="$t('batteryMonitor.attrNames.realGroupName')"
                    ></el-table-column>
                    <el-table-column
                        prop="failedReason"
                        show-overflow-tooltip
                        :label="$t('batteryMonitor.attrNames.failedReason')"
                    >
                        <template #default="scope">
                            <span v-if="!scope.row.failedReason"></span>
                            <span v-if="scope.row.failedReason.length">
                                <span v-for="(item, i) in scope.row.failedReason" :key="i">
                                    {{ i + 1 }}：{{ item }}&nbsp;&nbsp;
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { locationName, googleLocationName, getPageType, getTrailMapRoute } from '@/views/batteryTrack/utils.js';
import HTTP from '@/util/httpService.js';
import { queryMapOptions } from '@/components/Map/utils/mapOptions.js';
import {
    MAPTYPE_ZGIS_STRING,
    BATTERY_SECURITY_MANAGEMENT_TYPE,
    handleZgisPosition,
    zgisOfflineMapTips,
} from '@/views/batteryTrack/zgis/js/zgisUtils';
import { Loader } from '@/views/batteryTrack/zgis/js/zgisMapLoader';

const DEVICE_MODULE_ARR = ['GPS_HC', 'GPS_ZTE_BMS', 'BCUA']; // 协议字段变更

export default {
    props: {
        tableData: {
            type: Array,
            default: function () {
                return [];
            },
        },
        tableFilterItem: {
            type: Object,
            default: () => {},
        },
        tableKey: {
            type: Number,
            default: 0,
        },
        total: {
            type: Number,
            default: 0,
        },
        indexPageNo: {
            type: Number,
            default: 1,
        },
        indexPageSize: {
            type: Number,
            default: 5,
        },
        isNetwork: {
            type: Boolean,
            default: false,
        }
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },

    data() {
        return {
            multipleSelection: [],

            // firstWatch: true,
            // 设防撤防
            withdrawDialogVisible: false,
            // 解锁
            unlockDialogVisible: false,
            form: {
                reason: '',
            },
            currRow: null,
            selectedData: [],
            isBatchWidthdraw: false,
            isBatchUnlock: false,

            // 布防记录
            searchFormData: {
                optTime: ['', ''],
            },
            fortifyRecords: [],
            fortifyRecordsPageNo: 1,
            fortifyRecordsPageSize: 10,
            fortifyRecordsTotal: 0,
            // 偷盗记录
            stealRecords: [],
            stealRecordsData: [],
            stealRecordsPageNo: 1,
            stealRecordsPageSize: 10,
            stealRecordsTotal: 0,
            stealRecordsLoading: false,

            // 记录
            recordVisible: false,
            unlockRecordList: [],
            unlockRecordsPageNo: 1,
            unlockRecordsPageSize: 10,
            unlockRecordsTotal: 0,

            // 保持当前页
            keepPageNo: false,

            disableTrailLink: false,
            // 请求结果提示
            tipDialogVisible: false,
            dialogTitle: '',
            msgData: {},

            // 位置适配谷歌地图
            mapType: 'baidu',
            googleApiKey: null,
            // 查询语言
            languageOptionWidth: this.$i18n.locale === 'zh-CN' ? 310 : 390,
        };
    },

    mounted() {
        this.loadZmap();
        zgisOfflineMapTips.call(this);
    },

    computed: {
        useZgis() {
            return this.$store.state.battMonitor.mapType === MAPTYPE_ZGIS_STRING;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        formRules() {
            return {
                reason: [
                    {
                        required: true,
                        message: new Error(
                            this.$t('tipMessage.pleaseInput', {
                                filedName: this.$t('batteryMonitor.labels.withdrawReason'),
                            })
                        ),
                        trigger: 'blur',
                    },
                ],
            };
        },
        list() {
            return this.i18nList(this.tableData);
        },
        pageNo: {
            get() {
                return JSON.parse(JSON.stringify(this.indexPageNo));
            },
            set() {},
        },
        pageSize: {
            get() {
                return JSON.parse(JSON.stringify(this.indexPageSize));
            },
            set() {},
        },
        activeName() {
            if (this.rights['c.battery.fortification.record'] && !this.isNetwork) {
                return 'first';
            } else if (this.rights['c.battery.setPosition.record']) {
                return 'second';
            } else if (this.rights['c.battery.unlock.record']) {
                return 'third';
            } else {
                return 'first';
            }
        },
    },

    methods: {
        getTrailMapRoute(moId, pageType, fromPage = BATTERY_SECURITY_MANAGEMENT_TYPE) {
            return getTrailMapRoute(moId, pageType, fromPage);
        },
        async loadZmap() {
            try {
                if (!window.zmaps) {
                    let loader = new Loader();
                    await loader.load();
                }
            } catch (error) {
                console.log('loadZmap error', error);
            }
        },
        isBmsBcuaHcgps(deviceModule) {
            if (DEVICE_MODULE_ARR.includes(deviceModule)) {
                return true;
            }
            return false;
        },
        handleClose() {
            this.tipDialogVisible = false;
        },

        rowSelectable(row) {
            return parseInt(row.communicationStatus) === 0;
        },
        i18nList(data) {
            data.forEach(item => {
                switch (parseInt(item.motionState)) {
                    case 0:
                        item['motionState_i18n'] = this.$t('batteryMonitor.attrValues.static');
                        break;
                    case 1:
                        item['motionState_i18n'] = this.$t('batteryMonitor.attrValues.motion');
                        break;
                    case 2:
                        item['motionState_i18n'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['motionState_i18n'] = '--';
                }

                switch (parseInt(item.fortificationState)) {
                    case 1:
                        item['fortificationState_i18n'] = this.$t('batteryMonitor.attrValues.fortification');
                        break;
                    case 0:
                        item['fortificationState_i18n'] = this.$t('batteryMonitor.attrValues.withdraw');
                        break;
                    case 2:
                        item['fortificationState_i18n'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['fortificationState_i18n'] = '--';
                }

                switch (parseInt(item.communicationStatus)) {
                    case 0:
                        item['communicationStatus_i18n'] = this.$t('batteryMonitor.attrValues.onLine');
                        break;
                    case 1:
                        item['communicationStatus_i18n'] = this.$t('batteryMonitor.attrValues.offLine');
                        break;
                    case 2:
                        item['communicationStatus_i18n'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['communicationStatus_i18n'] = '--';
                }

                switch (parseInt(item.theftStatus)) {
                    case 0:
                        item['theftStatus_i18n'] = this.$t('batteryMonitor.attrValues.normal');
                        break;
                    case 1:
                        item['theftStatus_i18n'] = this.$t('batteryMonitor.attrValues.steal');
                        break;
                    case 2:
                        item['theftStatus_i18n'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['theftStatus_i18n'] = '--';
                }

                switch (parseInt(item.lockStatus)) {
                    case 0:
                        item['lockStatus_i18n'] = this.$t('batteryMonitor.attrValues.normal');
                        break;
                    case 1:
                        item['lockStatus_i18n'] = this.$t('batteryMonitor.attrValues.locked');
                        break;
                    case 2:
                        item['lockStatus_i18n'] = this.$t('batteryMonitor.attrValues.unknown');
                        break;
                    default:
                        item['lockStatus_i18n'] = '--';
                }

                switch (item.latitude) {
                    case '':
                        item.latitude = '--';
                        break;
                }

                switch (item.longitude) {
                    case '':
                        item.longitude = '--';
                        break;
                }

                switch (item.ariseTime) {
                    case '':
                        item.ariseTime = '--';
                        break;
                }
            });
            // 通过表格的模拟选择，来实现表格刷新时批量操作置灰逻辑的同步执行
            data && data.length && this.$refs.multipleTable?.toggleRowSelection(data[0]);
            data && data.length && this.$refs.multipleTable?.toggleRowSelection(data[0]);
            return data;
        },
        // 多选操作
        handleSelectionChange(val) {
            this.multipleSelection = val.slice();
            this.$emit('selectionChange', val);
            this.selectedData = val;
        },

        // 分页相关方法
        // 分页，当前页面变化
        handleCurrentChange(pageNo) {
            this.$emit('tablePageChange', pageNo);
        },
        // 分页，页面尺寸变化
        handleSizeChange(pageSize) {
            this.$emit('tablePageSizeChange', pageSize);
        },
        // 排序
        sortChange(column) {
            this.$emit('tableSortChange', column);
        },

        getLocationName(list) {
            let _this = this;

            if (!list.length) {
                this.list = [];
                _this.$emit('loaded', true);
                return;
            }

            let promises = [];
            list.forEach(d => {
                if (!d.batteryLocation) {
                    promises.push(_this.getLocationPromise(d));
                }
            });

            if (!promises.length) {
                // 如果数据中所有项都有batteryLocation属性值
                this.list = list;
                _this.$emit('loaded', true);
            } else {
                Promise.all(promises)
                    .then(data => {
                        console.log('-----Promise data-----', data);
                        list.forEach(d => {
                            data.forEach(k => {
                                if (d.moId === k.moId) {
                                    d.batteryLocation = k.locationName;
                                }
                            });
                        });
                        _this.list = list;
                        _this.$emit('loaded', true);
                    })
                    .catch(res => {
                        console.warn('获取位置信息失败');
                        _this.list = list;
                        _this.$emit('loaded', false);
                    });
            }
        },

        getLocationPromise(elem) {
            let lat = this.degreeConvertBack(elem.latitude);
            let lng = this.degreeConvertBack(elem.longitude);
            return locationName({ lat, lng }, elem.moId);
        },

        getOriAndCurLocationNameByGoogle(data) {
            let promises = [],
                lat,
                lng;
            let _this = this;
            data.forEach((item, index) => {
                item.originalLocation = '--';
                item.currentLocation = '--';
                lat = this.degreeConvertBack(+item.originalLatitude);
                lng = this.degreeConvertBack(+item.originalLongitude);
                promises.push(googleLocationName({ lat, lng }, `original${index}`, this.googleApiKey));
                lat = this.degreeConvertBack(+item.currentLatitude);
                lng = this.degreeConvertBack(+item.currentLongitude);
                promises.push(googleLocationName({ lat, lng }, `current${index}`, this.googleApiKey));
            });

            Promise.all(promises)
                .then(res => {
                    data.forEach((d, index) => {
                        res.forEach(k => {
                            if (`original${index}` === k.moId) {
                                d.originalLocation = k.locationName;
                            }
                            if (`current${index}` === k.moId) {
                                d.currentLocation = k.locationName;
                            }
                        });
                    });
                    _this.stealRecordsData = data;
                    _this.stealRecordsLoading = false;
                })
                .catch(e => {
                    _this.stealRecordsData = data;
                    _this.stealRecordsLoading = false;
                });
        },

        getOriAndCurLocationName(data) {
            let promises = [],
                lat,
                lng;
            let _this = this;
            data.forEach((item, index) => {
                item.originalLocation = '--';
                item.currentLocation = '--';
                lat = this.degreeConvertBack(+item.originalLatitude);
                lng = this.degreeConvertBack(+item.originalLongitude);
                promises.push(locationName({ lat, lng }, `original${index}`));
                lat = this.degreeConvertBack(+item.currentLatitude);
                lng = this.degreeConvertBack(+item.currentLongitude);
                promises.push(locationName({ lat, lng }, `current${index}`));
            });

            Promise.all(promises)
                .then(res => {
                    data.forEach((d, index) => {
                        res.forEach(k => {
                            if (`original${index}` === k.moId) {
                                d.originalLocation = k.locationName;
                            }
                            if (`current${index}` === k.moId) {
                                d.currentLocation = k.locationName;
                            }
                        });
                    });
                    _this.stealRecordsData = data;
                    _this.stealRecordsLoading = false;
                })
                .catch(e => {
                    _this.stealRecordsData = data;
                    _this.stealRecordsLoading = false;
                });
        },

        // 度分秒转换成为度
        degreeConvertBack(value) {
            return value;
        },

        // 设防撤防
        fortifiyBtnClick(row) {
            // 0.0:设防  1.0：撤防
            this.isBatchWidthdraw = false;
            this.currRow = row;
            if (parseInt(row.fortificationState) === 1) {
                this.withdrawDialogVisible = true;
                this.form.reason = '';
            } else {
                let params = {
                    ids: [row.moId],
                    protectState: '1.0',
                };
                this.setProtectState(params);
            }
        },
        // 解锁
        lockBtnClick(row) {
            this.isBatchUnlock = false;
            this.currRow = row;
            this.unlockDialogVisible = true;
        },
        /* Started by AICoder, pid:eb55e6f1959f4e6cb0b02c33308d1f8c */
        getBgColor(type) {
            const colorObj = {
                2: '#bfbfbf',
                1: '#ffc850',
                0: '#76d63e',
            };
            return colorObj[type] || '';
        },
        /* Ended by AICoder, pid:eb55e6f1959f4e6cb0b02c33308d1f8c */
        /* Started by AICoder, pid:087e9021ba2d486ba4aac33e091f509e */
        getBgColorByStatus(type) {
            const colorMap = {
                1: '#1993ff',
                2: '#76d63e',
                3: 'red',
                4: '#bfbfbf',
            };

            return colorMap[type] || '';
        },
        /* Ended by AICoder, pid:087e9021ba2d486ba4aac33e091f509e */
        setProtectState(params) {
            HTTP.request('setProtectState', {
                method: 'post',
                data: params,
                complete: data => {
                    this.withdrawDialogVisible = false;
                    if (data.code === 0 || data.code === -2) {
                        this.msgData = data.data || {};
                        if (parseInt(params.protectState) === 0) {
                            let _tip = this.$t('dialog.prompt');
                            let _withdraw = this.$t('batteryMonitor.btnLabels.withdraw');
                            this.dialogTitle = `${_withdraw}${_tip}`;
                        } else {
                            let _tip = this.$t('dialog.prompt');
                            let _fortification = this.$t('batteryMonitor.btnLabels.fortification');
                            this.dialogTitle = `${_fortification}${_tip}`;
                        }
                        this.tipDialogVisible = true;
                        this.$refs.multipleTable.clearSelection();
                        this.$emit('refeshTable');
                        this.keepPageNo = true;
                    } else {
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                },
                error: error => {
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        withdraw() {
            let the = this;
            let params;
            this.$refs.form.validate(valid => {
                if (valid) {
                    if (!this.isBatchWidthdraw) {
                        params = {
                            ids: [the.currRow.moId],
                            protectState: '0.0',
                            msg: the.form.reason,
                        };
                    } else {
                        let ids = [];
                        the.selectedData.forEach(item => {
                            ids.push(item.moId);
                        });
                        params = {
                            ids: ids,
                            protectState: '0.0',
                            msg: the.form.reason,
                        };
                    }
                    the.setProtectState(params);
                }
            });
        },
        // 解锁确认后
        unlockWithdraw() {
            let params;
            if (!this.isBatchUnlock) {
                params = {
                    moIdList: [this.currRow.moId],
                };
            } else {
                let moIdList = [];
                this.selectedData.forEach(item => {
                    moIdList.push(item.moId);
                });
                params = {
                    moIdList: moIdList,
                };
            }
            this.setUnlockProtectState(params);
        },
        setUnlockProtectState(params) {
            HTTP.request('batteryTrackUnlock', {
                method: 'post',
                data: params,
                complete: data => {
                    this.unlockDialogVisible = false;
                    if (data.code === 0) {
                        this.msgData = data.data || {};
                        let _tip = this.$t('dialog.prompt');
                        let _unlock = this.$t('batteryMonitor.btnLabels.unlock');
                        this.dialogTitle = `${_unlock}${_tip}`;
                        this.tipDialogVisible = true;
                        this.$refs.multipleTable.clearSelection();
                        this.$emit('refeshTable');
                        this.keepPageNo = true;
                    } else if (data.code === -2) {
                        let info = `${this.$t('batteryMonitor.attrNames.battery')} ${data.message} ${this.$t(
                            'batteryMonitor.infowindow.commandExcuting'
                        )}`;
                        this.$alert(info, this.$t('dialog.prompt'), { confirmButtonText: this.$t('button.confirm') });
                    } else {
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                },
                error: error => {
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        batchFortify() {
            let flag = true;
            let ids = [];
            this.selectedData.forEach(item => {
                if (parseInt(item.fortificationState) === 1) {
                    flag = false;
                }
                ids.push(item.moId);
            });
            if (!flag) {
                this.$alert(this.$t('batteryMonitor.message.batchFortifySelectTip'), this.$t('dialog.prompt'), {
                    confirmButtonText: this.$t('button.confirm'),
                });
                return;
            }
            let params = {
                ids: ids,
                protectState: '1.0',
            };
            this.setProtectState(params);
        },

        batchUnlock() {
            let ids = [];
            this.selectedData.forEach(item => {
                ids.push(item.moId);
            });
            let params = {
                moIdList: ids,
            };
            this.setUnlockProtectState(params);
        },

        batchWithdraw() {
            let flag = true;
            this.selectedData.forEach(item => {
                if (parseInt(item.fortificationState) === 0) {
                    this.$alert(this.$t('batteryMonitor.message.batchWidthdrawSelectTip'), this.$t('dialog.prompt'), {
                        confirmButtonText: this.$t('button.confirm'),
                    });
                    flag = false;
                    return false;
                }
            });

            if (flag) {
                this.withdrawDialogVisible = true;
                this.isBatchWidthdraw = true;
                this.form.reason = '';
            }
        },

        // 布防记录
        queryFortifyRecords(params) {
            this.fortifyRecords = [];
            HTTP.request('queryArmedRecord', {
                method: 'post',
                data: params,
                complete: data => {
                    if (data.code === 0) {
                        this.fortifyRecords = data.data;
                        this.fortifyRecordsTotal = data.total;
                        this.fortifyRecords.forEach(item => {
                            item.detail_i18n =
                                parseInt(item.detail) === 1
                                    ? this.$t('batteryMonitor.attrValues.fortification')
                                    : this.$t('batteryMonitor.attrValues.withdraw');

                            switch (item.status) {
                                case 1:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excuting');
                                    break;
                                case 2:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutSuccess');
                                    break;
                                case 3:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutFailed');
                                    break;
                                case 4:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutTimeout');
                                    break;
                                default:
                                    break;
                            }
                        });
                    } else {
                        this.fortifyRecordsTotal = 0;
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                },
                error: err => {
                    this.fortifyRecordsTotal = 0;
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        // 偷盗记录
        queryStealRecord(params) {
            this.stealRecords = [];
            this.stealRecordsData = [];
            this.stealRecordsLoading = true;
            HTTP.request('queryStealRecord', {
                method: 'post',
                data: params,
                complete: data => {
                    if (data.code === 0) {
                        this.stealRecords = data.data;
                        this.stealRecords.forEach(item => {
                            item.theftStatus_i18n =
                                item.theftStatus === 0
                                    ? this.$t('batteryMonitor.attrValues.normal')
                                    : this.$t('batteryMonitor.attrValues.steal');
                        });
                        this.stealRecordsTotal = data.total;
                        this.getLocationByZgis(this.stealRecords);

                    } else {
                        this.stealRecordsLoading = false;
                        this.stealRecordsTotal = 0;
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                },
                error: err => {
                    this.stealRecordsLoading = false;
                    this.stealRecordsTotal = 0;
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        // 解锁记录
        queryUnlockRecords(params) {
            this.unlockRecordList = [];
            HTTP.request('batteryTrackUnlockRecord', {
                method: 'post',
                data: params,
                complete: data => {
                    if (data.code === 0) {
                        this.unlockRecordList = data.data;
                        this.unlockRecordsTotal = data.total;
                        this.unlockRecordList.forEach(item => {
                            switch (item.status) {
                                case 1:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excuting');
                                    break;
                                case 2:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutSuccess');
                                    break;
                                case 3:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutFailed');
                                    break;
                                case 4:
                                    item.status_i18n = this.$t('batteryMonitor.attrValues.excutTimeout');
                                    break;
                                default:
                                    break;
                            }
                        });
                    } else {
                        this.unlockRecordsTotal = 0;
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                },
                error: err => {
                    this.unlockRecordsTotal = 0;
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        // 设防查询-重置参数
        searchFortifyRecord() {
            if (!Array.isArray(this.searchFormData.optTime)) {
                this.searchFormData.optTime = ['', ''];
            }
            let params = {
                id: this.currRow.moId,
                beginTime: this.searchFormData.optTime[0] || '',
                endTime: this.searchFormData.optTime[1] || '',
                pageNo: this.fortifyRecordsPageNo,
                pageSize: this.fortifyRecordsPageSize,
            };
            this.queryFortifyRecords(params);
        },
        // 解锁查询-重置参数
        searchUnlockRecord() {
            if (!Array.isArray(this.searchFormData.optTime)) {
                this.searchFormData.optTime = ['', ''];
            }
            let params = {
                id: this.currRow.moId,
                beginTime: this.searchFormData.optTime[0] || '',
                endTime: this.searchFormData.optTime[1] || '',
                pageNo: this.unlockRecordsPageNo,
                pageSize: this.unlockRecordsPageSize,
            };
            this.queryUnlockRecords(params);
        },
        searchStealRecord() {
            if (!Array.isArray(this.searchFormData.optTime)) {
                this.searchFormData.optTime = ['', ''];
            }
            let params = {
                id: this.currRow.moId,
                beginTime: this.searchFormData.optTime[0] || '',
                endTime: this.searchFormData.optTime[1] || '',
                pageNo: this.stealRecordsPageNo,
                pageSize: this.stealRecordsPageSize,
            };
            this.queryStealRecord(params);
        },
        searchFortifyRecordClick() {
            this.fortifyRecordsPageNo = 1;
            this.searchFortifyRecord();
        },
        // 解锁
        searchUnlockRecordClick() {
            this.unlockRecordsPageNo = 1;
            this.searchUnlockRecord();
        },
        searchStealRecordClick() {
            this.stealRecordsPageNo = 1;
            this.searchStealRecord();
        },
        // 设防查询接口
        fortifyRecord() {
            this.fortifyRecordsPageNo = 1;
            this.searchFortifyRecord();
        },
        unlockRecordSearch() {
            this.unlockRecordsPageNo = 1;
            this.searchUnlockRecord();
        },
        stealRecord() {
            this.stealRecordsPageNo = 1;
            this.searchStealRecord();
        },
        handleClick(tab) {
            if (this.mapType === 'google' && tab.name === 'second' && !this.googleApiKey) {
                this.$message.error(this.$t('monitor.googleMapWarnning'));
            }
        },
        allRecord(row) {
            this.searchFormData.optTime = ['', ''];
            this.currRow = row;
            this.fortifyRecord(); // 点开记录开始查询-设防
            this.stealRecord(); // 点开记录开始查询-偷盗
            this.unlockRecordSearch(); // 点开记录开始查询-解锁
            this.recordVisible = true;
        },
        fortifyRecordsSizeChange(size) {
            this.fortifyRecordsPageSize = size;
            this.fortifyRecordsPageNo = 1;
            this.searchFortifyRecord();
        },
        fortifyRecordsPageChange(page) {
            this.fortifyRecordsPageNo = page;
            this.searchFortifyRecord();
        },
        unlockRecordsSizeChange(size) {
            this.unlockRecordsPageSize = size;
            this.unlockRecordsPageNo = 1;
            this.searchUnlockRecord();
        },
        unlockRecordsPageChange(page) {
            this.unlockRecordsPageNo = page;
            this.searchUnlockRecord();
        },
        stealRecordsSizeChange(size) {
            this.stealRecordsPageSize = size;
            this.stealRecordsPageNo = 1;
            this.searchStealRecord();
        },
        stealRecordsPageChange(page) {
            this.stealRecordsPageNo = page;
            this.searchStealRecord();
        },
        getRowKey(row) {
            return row.moId;
        },
        getLocationByZgis(listData) {
            let queryParam = this.getZgisPositionQueryParam(listData);
            this.queryPosition(queryParam, listData);
        },
        getZgisPositionQueryParam(listData) {
            let positionMap = this.$store.state.battMonitor.queriedPosition;
            let queryLonLat = [];
            let queryIds = [];
            listData.forEach(item => {
                const { currentLongitude, currentLatitude, originalLongitude, originalLatitude } = item;
                let curLon = parseFloat(currentLongitude);
                let curLat = parseFloat(currentLatitude);
                let oriLon = parseFloat(originalLongitude);
                let oriLat = parseFloat(originalLatitude);
                let curLonlatString = [curLon, curLat].join(' ');
                let oriLonlatString = [oriLon, oriLat].join(' ');
                item.curLonlatString = curLonlatString;
                item.oriLonlatString = oriLonlatString;
                item.currentLocation = '--';
                item.originalLocation = '--';
                if (positionMap[curLonlatString]) {
                    item.currentLocation = positionMap[curLonlatString];
                } else {
                    if (!queryIds.includes(curLonlatString)) {
                        queryIds.push(curLonlatString);
                        queryLonLat.push(JSON.stringify({ id: curLonlatString, keyword: curLonlatString }));
                    }
                }
                if (positionMap[oriLonlatString]) {
                    item.originalLocation = positionMap[oriLonlatString];
                } else {
                    if (!queryIds.includes(oriLonlatString)) {
                        queryIds.push(oriLonlatString);
                        queryLonLat.push(JSON.stringify({ id: oriLonlatString, keyword: oriLonlatString }));
                    }
                }
            });
            return queryLonLat;
        },
        queryPosition(queryLonLat, listData) {
            if (queryLonLat.length > 0 && window.zmaps && window.zmaps.SearchPoints) {
                let positionMap = this.$store.state.battMonitor.queriedPosition;
                let queryParam = queryLonLat.join(',');
                // eslint-disable-next-line new-cap
                window.zmaps.SearchPoints(queryParam, (error, results) => {
                    if (error || !Array.isArray(results)) {
                        // throw error
                    } else {
                        let resultMap = {};
                        results.forEach(ele => {
                            const { currRegion } = handleZgisPosition(ele.data);
                            if (currRegion) {
                                resultMap[ele.id] = currRegion;
                            }
                        });
                        positionMap = Object.assign(positionMap, resultMap);
                        listData.forEach(element => {
                            if (positionMap[element.curLonlatString]) {
                                element.currentLocation = positionMap[element.curLonlatString];
                            }
                            if (positionMap[element.oriLonlatString]) {
                                element.originalLocation = positionMap[element.oriLonlatString];
                            }
                        });
                    }
                    this.stealRecordsData = listData;
                    this.stealRecordsLoading = false;
                });
            } else {
                this.stealRecordsData = listData;
                this.stealRecordsLoading = false;
            }
        },
    },
};

function copyArr(arr) {
    let res = [];
    for (let i = 0; i < arr.length; i++) {
        res.push(arr[i]);
    }
    return res;
}
</script>

<style lang="scss" scoped>
.operation {
    color: #409eff;
    cursor: pointer;
}
.circle-class {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
}
.stealed {
    color: #e02222;
}
.failed {
    color: #e02222;
}
.bottomBar {
    text-align: center;
    margin-top: 30px;
}
:deep() .el-table .cell {
    padding-right: 0;
}
:deep() .el-table .el-table__header .cell {
    line-height: 40px;
}
::v-deep .el-table thead .el-table__cell {
    padding: 0;
}
.dialog-tab {
    ::v-deep .el-tabs__header {
        margin-bottom: 24px;

        .el-tabs__item.is-active {
            border-top: 2px solid #118eea;
            border-left: 1px solid #d9d9d9;
            border-right: 1px solid #d9d9d9;
        }

        .el-tabs__item {
            border-color: #d9d9d9;
            border-bottom: 2px solid transparent;
            border-left: 1px solid transparent;
            border-right: 1px solid transparent;
            border-top: 2px solid transparent;
            line-height: 36px !important;
        }

        .el-tabs__item:first-child {
            border-left: 1px solid transparent;
        }

        .el-tabs__item:first-child.is-active {
            border-left: 1px solid #d9d9d9;
        }

        .el-tabs__nav {
            border: 0 none;
            border-radius: 0;
        }
    }
    ::v-deep .el-tabs__item.is-active {
        border-bottom-color: #fff !important;
    }
    ::v-deep .el-form-item__content .el-date-editor.el-input__inner {
        width: 332px;
    }
    ::v-deep .el-form-item__label {
        text-align: right;
    }
}
.threeRecord {
    ::v-deep .is-scrolling-none {
        height: 307px !important;
    }

    .query-failed {
        color: #e02222;
        margin-right: 4px;
    }
    .disabled-refresh {
        color: #c0c4cc;
        cursor: not-allowed !important;
    }
    ::v-deep .el-icon-refresh-right {
        cursor: pointer;
    }
}
.threeRecord.dark-theme .disabled-refresh {
    color: #474a59;
}

::v-deep .el-tabs--top .el-tabs__item.is-top {
    padding: 0 20px !important;
}
::v-deep .el-tabs__active-bar {
    display: none;
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom-color: red;

    // border-bottom-color: #FFF;
}
::v-deep .el-table .el-table__row .el-button.el-button--text {
    padding: 5px 8px;
}

// 请求结果弹窗样式
:deep().dialog {
    .el-dialog__body {
        padding: 0 50px 40px 30px;
    }
    .dialogItem {
        font-size: 16px;
        margin: 15px 0;
        padding-left: 15px;
        border-left: 4px solid #409eff;
    }
    .dialogItemInfo2 {
        height: 230px;

        // overflow: auto;
    }
    .dialogItemInfo1 {
        font-size: 15px;
        padding-left: 60px;
    }
    .num {
        margin-left: 20px;
        font-size: 16px;
        font-weight: 700;
    }
}

html.dark .dialog-tab ::v-deep .el-tabs__item.is-active {
    border-bottom-color: #191919 !important;
    border-left: 1px solid #414243 !important;
    border-right: 1px solid #414243 !important;
}
html.dark .el-dialog .el-input__inner {
    background-color: transparent;
}
html.dark .bottomBar ::v-deep .el-button--default {
    color: #1993ff !important;
}
.question-tip {
    vertical-align: text-bottom;
}

</style>
