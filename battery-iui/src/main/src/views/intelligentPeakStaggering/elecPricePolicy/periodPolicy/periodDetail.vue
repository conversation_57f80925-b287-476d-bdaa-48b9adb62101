<template>
    <div class="setting-list">
        <div v-if="activeName === 'toPeriod'">
            <el-form ref="periodModeFrom" :model="periodModeFrom" :inline="true">
                <el-form-item :label="$t('peakSetting.fields.periodMode')" style="margin-bottom: 0">
                    <span>{{ mode[periodModeFrom.mode].name || '--' }}</span>
                </el-form-item>
            </el-form>
        </div>
        <div class="tableBar">
            <div class="tips">
                <div v-for="(item, index) in strategyTypeData" :key="index" class="tipsBox">
                    <div class="tipsColorBox" :style="{ 'background-color': item.color }"></div>
                    <span>{{ item.name }}</span>
                </div>
            </div>
        </div>
        <el-table
            v-loading="loading"
            class="periodTable"
            :data="tableData"
            border
            style="width: 100%"
            :header-cell-style="{ 'text-align': 'center' }"
        >
            <el-table-column
                prop="name"
                :label="$t('peakSetting.fields.SeasonRange')"
                width="250"
                :class-name="'operationDel date-range'"
            >
                <template v-slot="scope">
                    <!-- 日期控件 -->
                    <div v-if="activeName === 'toPeriod'">
                        <div class="seasonRange">
                            <span>{{ scope.row.startDate }}</span>
                            <span class="to">-</span>
                            <span>{{ scope.row.endDate }}</span>
                        </div>
                        <div class="seasonRange">
                            <span class="description">{{ $t('peakSetting.fields.description') }}</span>
                            <div
                                :style="{
                                    marginLeft: $i18n.locale === 'zh-CN' ? '32px' : '68px',
                                }"
                            >
                                <span>{{ scope.row.remark || '--' }}</span>
                            </div>
                        </div>
                    </div>
                    <template v-if="activeName === 'toHoliday'">
                        <el-table
                            :data="tableData[scope.$index].interval"
                            :show-header="false"
                            :class="['period-setting-date', 'in-detail']"
                            style="width: 250px"
                        >
                            <el-table-column>
                                <template v-slot="scopeDate">
                                    <div class="detail-date">
                                        <div class="date">
                                            {{ scopeDate.row.startDate + ' - ' + scopeDate.row.endDate }}
                                        </div>
                                        <div class="remark">
                                            {{ $t('peakSetting.fields.description') }}
                                            <div>
                                                {{ scopeDate.row.remark || '--' }}
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </template>
            </el-table-column>
            <el-table-column
                prop="scope"
                :label="$t('peakSetting.fields.period')"
                class-name="operationDel"
                show-overflow-tooltip
            >
                <template v-slot="scope">
                    <!-- 天控件 -->
                    <div
                        v-for="(periodItem, order) in scope.row.detailStrategy"
                        :key="'periodItem' + order"
                        class="periodContent"
                    >
                        <div v-if="scope.row.mode === 2" class="dateOption item">
                            <div class="seasonRange">
                                <span>{{ periodItem.periodStart }}</span>
                                <span class="to">-</span>
                                <span>{{ periodItem.periodEnd }}</span>
                            </div>
                        </div>
                        <div v-if="scope.row.mode === 1" class="dateOption item">
                            <div class="weekLabel">
                                <span v-for="item in weekOption" :key="item.label">{{ item.label }}</span>
                            </div>
                            <el-checkbox-group v-model="periodItem.weekArr" :min="0" :max="7">
                                <el-checkbox
                                    v-for="item in weekOption"
                                    :key="item.value"
                                    :label="item.value"
                                    disabled
                                ></el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div class="periodSetItem">
                            <Add
                                :type="type"
                                :period-item="periodItem"
                                :index-key="scope.$index + '' + order + activeName"
                                @updateDetail="handleupdateDetail($event, periodItem)"
                            ></Add>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import ConfirmDialog from '@/components/ConfirmDialog.vue';
import Add from './Add';

export default {
    props: ['activeName', 'type', 'periodData'],
    components: {
        Add,
    },
    data() {
        return {
            periodModeFrom: {
                mode: 0,
            },
            mode: [
                {
                    name: this.$t('peakSetting.fields.day'),
                },
                {
                    name: this.$t('peakSetting.fields.week'),
                },
                {
                    name: this.$t('peakSetting.fields.month'),
                },
            ],
            strategyTypeData: [
                {
                    name: this.$t('peakSetting.fields.highest'),
                    color: '#FF9852',
                    value: 3,
                },
                {
                    name: this.$t('peakSetting.fields.peak'),
                    color: '#FFC850',
                    value: 2,
                },
                {
                    name: this.$t('peakSetting.fields.normal'),
                    color: '#1FB6D9',
                    value: 1,
                },
                {
                    name: this.$t('peakSetting.fields.trough'),
                    color: '#92DD92',
                    value: 0,
                },
            ],
            //
            loading: false,
            tableData: [],
            option: {}, // test
            periodObj: {}, // 新增用
            detailStrategyObj: {},

            pageInfo: {
                orderColumn: '',
                orderDirection: 'asc', // desc/asc  默认asc
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            daysObj: {
                days31: [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27,
                    28, 29, 30, 31,
                ],
                days30: [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27,
                    28, 29, 30,
                ],
                days29: [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27,
                    28, 29,
                ],
            },
            days31M: [1, 3, 5, 7, 8, 10, 12],
            days30M: [4, 6, 9, 11],
            monthOption: [],
            weekOption: [
                {
                    label: this.$t('peakSetting.fields.mon'),
                    value: 1,
                },
                {
                    label: this.$t('peakSetting.fields.tue'),
                    value: 2,
                },
                {
                    label: this.$t('peakSetting.fields.wed'),
                    value: 3,
                },
                {
                    label: this.$t('peakSetting.fields.thur'),
                    value: 4,
                },
                {
                    label: this.$t('peakSetting.fields.fri'),
                    value: 5,
                },
                {
                    label: this.$t('peakSetting.fields.sat'),
                    value: 6,
                },
                {
                    label: this.$t('peakSetting.fields.sun'),
                    value: 7,
                },
            ],
        };
    },
    mounted() {
        this.initDayMonthOption();
        // type   day
        // add
        if (this.type === 'addPeriodPolicy') {
            if (this.activeName === 'toHoliday') {
                this.periodObj.mode = 3;

                // this.tableData.push(this.periodObj);
            } else {
                this.periodObj.mode = 0;
                // this.tableData.push(this.periodObj);
            }
        } else {
            // edit
            // 待处理
            // this.initEditData();
        }

        //  this.tableData = this.option.season;
    },
    watch: {
        periodData: {
            deep: true,
            handler(val) {
                if (this.periodData.season) {
                    this.initEditData();
                }
            },
        },
    },
    computed: {},
    methods: {
        // 初始化编辑数据

        initEditData() {
            this.tableData = [];
            // season
            if (this.activeName === 'toPeriod') {
                this.periodModeFrom.mode = this.periodData.season[0].mode;
                this.tableData = this.initTableData(this.periodData.season);
            } else {
                // holiday
                // this.periodModeFrom.mode = this.periodData.holiday ? this.periodData.holiday[0].mode : 3;
                this.tableData = this.initTableData(this.periodData.holiday) || [];
            }
        },
        initTableData(row) {
            let tableData = JSON.parse(JSON.stringify(row));
            if (tableData.length) {
                tableData.forEach(data => {
                    if (this.activeName === 'toPeriod') {
                        data.detailStrategy.forEach(item => {
                            // 唯一标识，循环时用，解决删除问题
                            item.oid = Math.randomValue() + '';
                            if (item.weekStr) {
                                item.weekArr = JSON.parse(item.weekStr);
                                item.weekArr.forEach((ele, i, arr) => {
                                    arr[i] = Number(ele);
                                });
                            } else {
                                item.weekArr = [];
                            }
                        });
                    } else {
                        Array.isArray(data.detailList) &&
                            data.detailList.forEach(item => {
                                // 唯一标识，循环时用，解决删除问题
                                item.oid = Math.randomValue() + '';
                            });
                        data.interval = data.interval || [];
                        data.detailStrategy = [
                            {
                                detailList: data.detailList || [],
                            },
                        ];
                    }
                });
            }

            return tableData;
        },
        // 切换模型

        // 新增行

        handleupdateDetail(detailList, periodItem) {
            periodItem.detailList = detailList;
        },
        initDayMonthOption() {
            this.monthOption = [
                {
                    label: this.$t('datetimePicker.january'),
                    value: '01',
                },
                {
                    label: this.$t('datetimePicker.february'),
                    value: '02',
                },
                {
                    label: this.$t('datetimePicker.march'),
                    value: '03',
                },
                {
                    label: this.$t('datetimePicker.april'),
                    value: '04',
                },
                {
                    label: this.$t('datetimePicker.may'),
                    value: '05',
                },
                {
                    label: this.$t('datetimePicker.june'),
                    value: '06',
                },
                {
                    label: this.$t('datetimePicker.july'),
                    value: '07',
                },
                {
                    label: this.$t('datetimePicker.august'),
                    value: '08',
                },
                {
                    label: this.$t('datetimePicker.september'),
                    value: '09',
                },
                {
                    label: this.$t('datetimePicker.october'),
                    value: '10',
                },
                {
                    label: this.$t('datetimePicker.november'),
                    value: '11',
                },
                {
                    label: this.$t('datetimePicker.december'),
                    value: '12',
                },
            ];
        },
        // 新增操作
        addMonth(type, row) {
            if (type === 'start') {
                if (row.startM === 11) {
                    return;
                }
                row.startM++;

                if (this.days31M.includes(row.startM + 1)) {
                    row.startDayType = 'days31';
                } else if (this.days30M.includes(row.startM + 1)) {
                    row.startDayType = 'days30';
                } else {
                    row.startDayType = 'days29';
                }
            } else {
                if (row.endM === 11) {
                    return;
                }
                row.endM++;

                if (this.days31M.includes(row.endM + 1)) {
                    row.endDayType = 'days31';
                } else if (this.days30M.includes(row.endM + 1)) {
                    row.endDayType = 'days30';
                } else {
                    row.endDayType = 'days29';
                }
            }
        },
        deleteMonth(type, row) {
            if (type === 'start') {
                if (row.startM === 0) {
                    return;
                }
                row.startM--;
                if (this.days31M.includes(row.startM + 1)) {
                    row.startDayType = 'days31';
                } else if (this.days30M.includes(row.startM + 1)) {
                    row.startDayType = 'days30';
                } else {
                    row.startDayType = 'days29';
                }
            } else {
                if (row.endM === 0) {
                    return;
                }
                row.endM--;
                if (this.days31M.includes(row.endM + 1)) {
                    row.endDayType = 'days31';
                } else if (this.days30M.includes(row.endM + 1)) {
                    row.endDayType = 'days30';
                } else {
                    row.endDayType = 'days29';
                }
            }
        },
        chooseDay(type, row, day) {
            let [dayStr, monthStr] = ['', ''];
            if (day < 10) {
                dayStr = '0' + day;
            } else {
                dayStr = day + '';
            }
            if (type === 'start') {
                if (row.startM < 9) {
                    monthStr = '0' + (row.startM + 1);
                } else {
                    monthStr = row.startM + 1 + '';
                }
                row.startDate = `${monthStr}-${dayStr}`;
            } else {
                if (row.endM < 9) {
                    monthStr = '0' + (row.endM + 1);
                } else {
                    monthStr = row.endM + 1 + '';
                }
                row.endDate = `${monthStr}-${dayStr}`;
            }
        },
        setDay(type, periodItem, day) {
            // let dayStr = '';
            // if (day < 10) {
            //     dayStr = '0' + day;
            // } else {
            //     dayStr = day + '';
            // }
            if (type === 'start') {
                periodItem.periodStart = day;
            } else {
                periodItem.periodEnd = day;
            }
        },
        //
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

:deep(.en-lang) .el-form-item__label {
    width: 191px;
}

// :deep(.en-lang) .el-form-item__content .el-input__inner {
//     width: 231px;
// }

:deep(.el-table__body-wrapper) > table {
    border-top: unset;
}

:deep(.el-table) th.is-leaf {
    border-bottom: 1px solid #d9d9d9;
}
html.dark .setting-list :deep(.el-table) th.is-leaf {
    border-bottom: 1px solid #474a59;
}
//
:deep(.el-form) .el-form-item__content .el-input__inner,
.el-form .el-form-item__content .el-date-editor.el-input,
.el-form .el-form-item__content .el-select .el-input__inner {
    width: 200px;
}
.formEdit :deep(.el-dialog) .el-dialog__body {
    padding: 0 30px 0px;
}
:deep(.el-dialog__footer) {
    padding: 10px 24px 24px;
}
.page-title {
    height: 16px;
    font-size: 16px;
    color: #333;
    padding-top: 24px;
    padding-bottom: 24px;
    line-height: 16px;
    position: relative;
}
.tableBar {
    height: 16px;
    margin-top: 16px;
    position: relative;
}
.tips {
    position: absolute;
    right: 0;
    bottom: 0;
    color: #888585;
    font-size: 14px;
    .tipsBox {
        display: inline-block;
        margin-left: 16px;
        .tipsColorBox {
            display: inline-block;
            width: 16px;
            height: 6px;
            border-radius: 3px;
            margin: 0 6px 2px 6px;
        }
    }
}
html.dark .setting-list {
    .tips {
        color: #d9d9d9;
    }
}
// :deep(.el-dropdown-menu--small) .el-dropdown-menu__item {
//     background-color: #fff !important;
// }
:deep(.el-dropdown-menu__item) {
    background-color: #fff !important;
    color: #444 !important;
}
.dateBox {
    width: 300px;
    height: 300px;

    .dateM {
        width: 100%;
        height: 80px;
        border-bottom: 1px solid #eee;
        .elBtn:hover {
            color: #1993ff;
        }
    }
    .dateD {
        width: 100%;
        height: 220px;
    }
    .dateMitem {
        display: inline-block;
        width: 45px;
        height: 35px;
        margin: 5px 0 0 5px;
    }
}
.periodContent {
    display: flex;
    justify-content: center;
    align-items: center;
    .periodSetItem {
        margin-left: 30px;
        margin-right: 30px;
    }
    .dateOption {
        width: 200px;
        :deep(.el-checkbox) {
            margin-right: 8px;
            margin-left: 8px;
        }
        :deep(.el-checkbox__label) {
            display: none;
        }
        .weekLabel {
            width: 100%;
            span {
                display: inline-block;
                width: 30px;
                text-align: center;
            }
        }
    }
}
.seasonRange {
    margin: 16px;
    span.to {
        padding: 0 12px;
    }
    span.description {
        float: left;
    }
}
.item {
    // display: inline-block;
}

.periodTable {
    :deep(.el-table__row) {
        td {
            background-color: #fff !important;
        }
    }
    :deep(.el-table__body) tbody tr td.date-range {
        padding: 0;
        .cell {
            padding: 0;
        }
    }
    .period-setting-date.el-table {
        margin: 0 auto;
        border: none;
        :deep(.el-table__body) tbody tr td {
            padding: 0;
        }
        &.in-detail {
            &::before {
                background-color: transparent;
                height: 0;
            }
            :deep(.el-table__body) tbody tr {
                &:last-child td {
                    border-bottom: none;
                }
                td .cell .detail-date {
                    .date {
                        height: 32px;
                        line-height: 32px;
                        margin: 8px 24px;
                    }
                    .remark {
                        margin: 8px 24px;
                    }
                }
            }
        }
    }
    .period-setting-date ::v-deep tbody tr td {
        border-right: none;
    }
}
html.dark .periodTable {
    :deep(.el-table__row) {
        td {
            background-color: #161a25 !important;
        }
    }
}
</style>

<style>
.el-table .operationDel {
    vertical-align: middle;
}
</style>
