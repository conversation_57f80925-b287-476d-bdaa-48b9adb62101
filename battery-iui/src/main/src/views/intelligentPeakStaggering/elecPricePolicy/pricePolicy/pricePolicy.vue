<template>
    <div class="setting-list">
        <div class="note">
            <i class="plx-ico-dialog-risk-48 note-icon"></i>
            <span>{{ $t('peakSetting.tipMessage.pricePolicyTip') }}</span>
        </div>
        <h4 class="page-title">
            {{ $t('peakSetting.button.pricePolicy') }}
            <div class="goBack">
                <el-button @click="handleBack(false)">
                    {{ $t('button.back') }}
                </el-button>
            </div>
        </h4>
        <div :class="['search-form', { 'en-lang': isEnLang }]">
            <el-form ref="form" :model="queryForm" :inline="true">
                <el-form-item :label="$t('peakSetting.fields.effectiveStartDate')">
                    <el-date-picker
                        :shortcuts="pickerStartOptions && pickerStartOptions.shortcuts"
                        :disabled-date="pickerStartOptions && pickerStartOptions.disabledDate"
                        :cell-class-name="pickerStartOptions && pickerStartOptions.cellClassName"
                        v-model="queryForm.startTime"
                        :placeholder="$t('peakSetting.placeholder.selectDate')"
                        type="date"
                        value-format="YYYY-MM-DD"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item :label="$t('peakSetting.fields.effectiveEndDate')">
                    <el-date-picker
                        :shortcuts="pickerEndOptions && pickerEndOptions.shortcuts"
                        :disabled-date="pickerEndOptions && pickerEndOptions.disabledDate"
                        :cell-class-name="pickerEndOptions && pickerEndOptions.cellClassName"
                        v-model="queryForm.endTime"
                        :placeholder="$t('peakSetting.placeholder.selectDate')"
                        type="date"
                        value-format="YYYY-MM-DD"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item :label="$t('refuelRemind.fields.status')">
                    <el-select v-model="queryForm.status" multiple collapse-tags clearable>
                        <el-option :label="$t('peakSetting.fields.finished')" :value="STATUS.FINISHED"></el-option>
                        <el-option
                            :label="$t('peakSetting.fields.takingEffect')"
                            :value="STATUS.TAKING_EFFECT"
                        ></el-option>
                        <el-option
                            :label="$t('peakSetting.fields.toBeEffective')"
                            :value="STATUS.TO_BE_EFFECTIVE"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="query">
                        {{ $t('button.query') }}
                    </el-button>
                    <el-button @click="reset">
                        {{ $t('button.reset') }}
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="tableBar">
            <el-button
                v-if="rights['intelligent.peak.period.price.add']"
                type="primary"
                :icon="ElIconPlus"
                @click="toAdd"
            >
                {{ $t('button.add') }}
            </el-button>
        </div>
        <el-table ref="table" v-loading="loading" :data="tableData" style="width: 100%" @sort-change="sortChange">
            <el-table-column
                prop="effectiveTime"
                :label="$t('peakSetting.fields.effectiveStartDate')"
                sortable="custom"
                show-overflow-tooltip
            >
                <template v-slot="scope">
                    {{ scope.row.effectiveTime || '--' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="expirationTime"
                :label="$t('peakSetting.fields.effectiveEndDate')"
                sortable="custom"
                show-overflow-tooltip
            >
                <template v-slot="scope">
                    {{ scope.row.expirationTime || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="pinnacle" :label="$t('peakSetting.fields.pinnacle') + `(${currencyUnit})`">
                <template v-slot="scope">
                    {{ scope.row.prices[3] || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="peak" :label="$t('peakSetting.fields.peak2') + `(${currencyUnit})`">
                <template v-slot="scope">
                    {{ scope.row.prices[2] || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="flat" :label="$t('peakSetting.fields.flat') + `(${currencyUnit})`">
                <template v-slot="scope">
                    {{ scope.row.prices[1] || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="valley" :label="$t('peakSetting.fields.valley') + `(${currencyUnit})`">
                <template v-slot="scope">
                    {{ scope.row.prices[0] || '--' }}
                </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="status" :label="$t('peakSetting.fields.status')">
                <template v-slot="scope">
                    {{ statusMap[scope.row.status] || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('table.operation')" fixed="right">
                <template v-slot="scope">
                    <el-button
                        v-if="rights['intelligent.peak.period.price.copy']"
                        size="mini"
                        type="text"
                        @click="toEditOrCopy(scope.row, 'copy')"
                    >
                        {{ $t('button.copy') }}
                    </el-button>
                    <el-button
                        v-if="rights['intelligent.peak.period.price.edit']"
                        size="mini"
                        type="text"
                        :disabled="STATUS.TO_BE_EFFECTIVE !== scope.row.status"
                        @click="toEditOrCopy(scope.row, 'edit')"
                    >
                        {{ $t('button.edit') }}
                    </el-button>
                    <el-button
                        v-if="rights['intelligent.peak.period.price.delete']"
                        size="mini"
                        type="text danger"
                        :disabled="STATUS.TO_BE_EFFECTIVE !== scope.row.status"
                        @click="toDelete(scope.row)"
                    >
                        {{ $t('button.delete') }}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageInfo.pageNo"
            :page-sizes="[5, 10, 20, 30]"
            v-model:page-size="pageInfo.pageSize"
            layout="total, sizes, prev, pager, next"
            :total="pageInfo.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>

        <!-- 删除确认对话框 -->
        <com-confirm
            v-model:visible="confirmShow"
            :title="confirmTitle"
            :ok-text="confirmOkText"
            @confirm="handleComConfirm"
        >
            <template v-slot:message>
                {{ confirmContent }}
            </template>
        </com-confirm>

        <!-- 新增/编辑弹窗 -->
        <el-dialog
            :title="dialogTitle"
            :close-on-click-modal="false"
            v-model="dialogVisible"
            width="500px"
            class="add-form"
            @close="handleCancel"
        >
            <div v-if="dialogVisible">
                <el-form
                    ref="addForm"
                    :inline="true"
                    :model="formData"
                    :rules="addFormRules"
                    label-width="142px"
                    label-position="right"
                >
                    <el-form-item :label="$t('peakSetting.fields.pinnacle') + `(${currencyUnit})`" prop="pinnacle">
                        <el-input
                            v-model="formData.pinnacle"
                            clearable
                            maxlength="100"
                            :placeholder="$t('peakSetting.placeholder.input')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('peakSetting.fields.peak2') + `(${currencyUnit})`" prop="peak">
                        <el-input
                            v-model="formData.peak"
                            clearable
                            maxlength="100"
                            :placeholder="$t('peakSetting.placeholder.input')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('peakSetting.fields.flat') + `(${currencyUnit})`" prop="flat">
                        <el-input
                            v-model="formData.flat"
                            clearable
                            maxlength="100"
                            :placeholder="$t('peakSetting.placeholder.input')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('peakSetting.fields.valley') + `(${currencyUnit})`" prop="valley">
                        <el-input
                            v-model="formData.valley"
                            clearable
                            maxlength="100"
                            :placeholder="$t('peakSetting.placeholder.input')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('peakSetting.fields.effectiveStartDate')" prop="startExecuteDate">
                        <el-date-picker
                            :shortcuts="pickerOptions && pickerOptions.shortcuts"
                            :disabled-date="pickerOptions && pickerOptions.disabledDate"
                            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                            v-model="formData.startExecuteDate"
                            :placeholder="$t('peakSetting.placeholder.selectDate')"
                            type="date"
                            value-format="YYYY-MM-DD"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
            <!-- <template #footer> -->
            <span class="dialog-footer">
                <el-button type="primary" size="small" :loading="loading" @click="handleConfirm">
                    {{ $t('button.confirm') }}
                </el-button>
                <el-button size="small" @click="handleCancel">{{ $t('button.cancel') }}</el-button>
            </span>
            <!-- </template> -->
        </el-dialog>
    </div>
</template>

<script>
import { Plus as ElIconPlus } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import ConfirmDialog from '@/components/ConfirmDialog.vue';

export default {
    data() {
        // 状态
        this.STATUS = {
            FINISHED: 'invalid', // 已结束
            TAKING_EFFECT: 'effective', // 生效中
            TO_BE_EFFECTIVE: 'pending', // 待生效
        };
        const { FINISHED, TAKING_EFFECT, TO_BE_EFFECTIVE } = this.STATUS;
        this.statusMap = {
            [FINISHED]: this.$t('peakSetting.fields.finished'),
            [TAKING_EFFECT]: this.$t('peakSetting.fields.takingEffect'),
            [TO_BE_EFFECTIVE]: this.$t('peakSetting.fields.toBeEffective'),
        };
        this.initQueryFrom = {
            scopeStrategyId: '',
            status: [TAKING_EFFECT, TO_BE_EFFECTIVE], // 待生效、生效中
            startTime: '',
            endTime: '',
        };
        this.initFormData = {
            pinnacle: '',
            peak: '',
            flat: '',
            valley: '',
            startExecuteDate: '',
        };
        // 验证两位小数
        const validateFloatNumber = (rule, value, callback) => {
            if (/^(0|[1-9][0-9]*)(.\d{1,5})?$/.test(value)) {
                callback();
            } else {
                callback(this.$t('peakSetting.rule.validPeak'));
            }
        };
        // 必填校验提示
        const getRuleMessage = field => {
            let fieldName = this.$t(`peakSetting.fields.${field}`);
            if (this.$i18n.locale === 'en-US') {
                fieldName = fieldName
                    .split(' ')
                    .map(e => e[0].toLowerCase() + e.slice(1))
                    .join(' ');
            }
            return this.$t('tipMessage.pleaseInput', { fieldName });
        };
        return {
            dialogVisible: false,
            loading: false,
            tableData: [],
            queryForm: { ...this.initQueryFrom },
            pickerStartOptions: {
                disabledDate: time => {
                    if (this.queryForm.endTime) {
                        const endTime = new Date(this.queryForm.endTime);
                        const timeEnd = endTime.getTime();
                        return time.getTime() > timeEnd;
                    }
                    return false;
                },
            },
            pickerEndOptions: {
                disabledDate: time => {
                    if (this.queryForm.startTime) {
                        const startTime = new Date(this.queryForm.startTime);
                        const timeStart = startTime.getTime();
                        return time.getTime() < timeStart;
                    }
                    return false;
                },
            },
            formData: { ...this.initFormData },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < new Date().getTime();
                },
            },
            pageInfo: {
                orderRule: '',
                orderField: '',
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            // 操作
            operationType: 'add',
            currentRow: {},
            confirmShow: false,
            confirmOkText: '',
            confirmContent: '',
            confirmTitle: '',
            confirmType: '',
            addFormRules: {
                pinnacle: [
                    {
                        required: true,
                        message: getRuleMessage('pinnacle'),
                    },
                    {
                        validator: validateFloatNumber,
                    },
                ],
                peak: [
                    {
                        required: true,
                        message: getRuleMessage('peak2'),
                    },
                    {
                        validator: validateFloatNumber,
                    },
                ],
                flat: [
                    {
                        required: true,
                        message: getRuleMessage('flat'),
                    },
                    {
                        validator: validateFloatNumber,
                    },
                ],
                valley: [
                    {
                        required: true,
                        message: getRuleMessage('valley'),
                    },
                    {
                        validator: validateFloatNumber,
                    },
                ],
                startExecuteDate: [
                    {
                        required: true,
                        message: getRuleMessage('startExecuteDate'),
                    },
                ],
            },
            ElIconPlus,
        };
    },
    components: {
        'com-confirm': ConfirmDialog,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        rowData: {
            type: Object,
            default: () => {},
            require: true,
        },
    },
    computed: {
        isEnLang() {
            return this.$i18n.locale === 'en-US';
        },
        dialogTitle() {
            let title = this.$t('button.add');
            if (this.operationType === 'edit') {
                title = this.$t('button.edit');
            } else if (this.operationType === 'copy') {
                title = this.$t('button.copy');
            }
            return title;
        },
        currencyUnit() {
            return this.$store.getters.getCurrencyUnit;
        },
    },
    mounted() {
        this.selectBy();
    },
    methods: {
        handleBack(refresh) {
            $emit(this, 'show', { view: 'list', refresh });
        },
        reset() {
            this.queryForm = { ...this.initQueryFrom };
            this.$refs.table.clearSort();
            this.pageInfo.orderRule = '';
            this.pageInfo.orderField = '';
            this.selectBy();
        },
        handleCancel() {
            this.dialogVisible = false;
            this.formData = { ...this.initFormData };
        },
        handleConfirm() {
            this.$refs.addForm.validate(valid => {
                if (!valid) {
                    return;
                }
                const p = this.formData;
                const params = {
                    effectiveTime: p.startExecuteDate,
                    prices: {
                        0: Number(p.valley).toFixed(5),
                        1: Number(p.flat).toFixed(5),
                        2: Number(p.peak).toFixed(5),
                        3: Number(p.pinnacle).toFixed(5),
                    },
                    scopeStrategyId: this.rowData.id,
                    status: this.currentRow.status,
                };
                let url = 'addPricePolicy';
                if (this.operationType === 'edit') {
                    params.id = this.currentRow.id;
                    url = 'editPricePolicy';
                }
                this.save(url, params);
            });
        },
        save(url, params) {
            this.loading = true;
            HTTP.request(url, {
                method: 'post',
                data: params,
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        this.dialogVisible = false;
                        this.query();
                        this.$message({
                            message: this.$t('tipMessage.operationSuccsee'),
                            type: 'success',
                        });
                    } else {
                        this.$message({
                            message: data.message,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.loading = false;
                    this.dialogLoading = false;
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        sortChange(sortInfo) {
            const { order, prop } = sortInfo;
            this.pageInfo.orderRule = '';
            if (order === 'descending') {
                this.pageInfo.orderRule = 'desc';
            } else if (order === 'ascending') {
                this.pageInfo.orderRule = 'asc';
            }

            this.pageInfo.orderField = prop;
            this.pageInfo.pageNo = 1;
            this.selectBy();
        },
        query() {
            this.pageInfo.pageNo = 1;
            this.selectBy();
        },
        selectBy() {
            const params = {
                scopeStrategyId: this.rowData.id,
                startTime: this.queryForm.startTime,
                endTime: this.queryForm.endTime,
                status: this.queryForm.status,
                sortBy: this.pageInfo.orderField,
                order: this.pageInfo.orderRule,
                pageNo: this.pageInfo.pageNo,
                pageSize: this.pageInfo.pageSize,
            };

            HTTP.request('getPricePolicy', {
                method: 'post',
                data: params,
                complete: res => {
                    this.loading = false;
                    if (res.code === 0) {
                        this.pageInfo.total = res.data ? res.total : 0;
                        this.tableData = res.data ? res.data : [];
                    } else {
                        this.$message.error(this.$t('common-query-error'));
                    }
                },
                error: () => {
                    this.loading = false;
                    this.$message.error(this.$t('common-query-error'));
                },
            });
        },
        handleSizeChange(pageSize) {
            this.pageInfo.pageSize = pageSize;
            this.pageInfo.pageNo = 1;
            this.selectBy();
        },
        handleCurrentChange(pageNo) {
            this.pageInfo.pageNo = pageNo;
            this.selectBy();
        },
        toAdd() {
            this.operationType = 'add';
            this.currentRow = {};
            this.dialogVisible = true;
        },
        toEditOrCopy(row, type) {
            this.operationType = type;
            this.currentRow = row;
            this.dialogVisible = true;
            // 表单回填数据
            const { prices, effectiveTime } = this.currentRow;
            this.formData = {
                pinnacle: prices[3],
                peak: prices[2],
                flat: prices[1],
                valley: prices[0],
                startExecuteDate: type === 'edit' ? effectiveTime : '',
            };
        },
        // 删除
        toDelete(row) {
            this.currentRow = row;
            this.confirmShow = true;
            this.confirmType = 'delete';
            this.confirmOkText = this.$t('button.confirm');
            this.confirmContent = this.$t('tipMessage.singleDelTips');
            this.confirmTitle = this.$t('dialog.deleteConfirm');
        },
        handleComConfirm() {
            if (this.confirmType === 'delete') {
                this.delete();
            }
        },
        delete() {
            HTTP.request('deletePricePolicy', {
                method: 'delete',
                urlParam: { id: this.currentRow.id },
                complete: res => {
                    if (res.code === 0) {
                        if (this.tableData.length === 1 && this.pageInfo.pageNo > 1) {
                            this.pageInfo.pageNo--;
                        }
                        this.selectBy();
                        this.$message.success(this.$t('tipMessage.deleteSuccess'));
                        this.confirmShow = false;
                    } else {
                        this.$message({
                            showClose: true,
                            duration: 5000,
                            message: this.$t(res.message),
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.loading = false;
                    this.$message({
                        showClose: true,
                        duration: 5000,
                        message: this.$t('tipMessage.networkError'),
                        type: 'error',
                    });
                },
            });
        },
    },
    emits: ['show'],
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

:deep(.en-lang) .el-form-item__label {
    width: 191px;
}

:deep(.en-lang) .el-form-item__content .el-input__inner {
    width: 231px;
}

:deep(.el-table__body-wrapper) > table {
    border-top: unset;
}

:deep(.el-table) th.is-leaf {
    border-bottom: 1px solid #d9d9d9;
}
html.dark .setting-list :deep(.el-table) th.is-leaf {
    border-bottom: 1px solid #474a59;
}
//
.add-form :deep(.el-form-item__content) .el-date-editor .el-input__inner,
.add-form :deep(.el-form-item__content) .el-input__inner,
.add-form :deep(.el-date-editor).el-input {
    width: 255px;
}
.search-form :deep(.el-form-item__content) .el-date-editor .el-input__inner {
    width: 150px;
}
.search-form :deep(.el-form-item__content) .el-date-editor.el-input {
    width: 150px;
}
:deep(.el-form-item__content) .el-range-editor.el-input__inner {
    width: 300px;
}
.formEdit :deep(.el-dialog) .el-dialog__body {
    padding: 0 30px 0px;
}
:deep(.el-dialog__footer) {
    padding: 10px 24px 24px;
}
.note {
    // height: 38px;
    font-size: 12px;
    color: #333;
    background: #e6f7ff;
    // margin-bottom: 24px;
    line-height: 38px;
    .note-icon {
        margin-left: 16px;
        margin-right: 8px;
        color: #1993ff;
        font-size: 16px;
        position: relative;
        top: 2px;
    }
}
.page-title {
    height: 16px;
    font-size: 16px;
    color: #333;
    padding-top: 24px;
    padding-bottom: 24px;
    line-height: 16px;
    position: relative;
    .goBack {
        position: absolute;
        right: 0;
        top: 16px;
    }
}
html.dark {
    .note {
        color: #d9d9d9;
        background-color: #1e3659;
    }
}
</style>
