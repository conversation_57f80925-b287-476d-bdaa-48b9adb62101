<template>
    <div>
        <el-popover
            v-model="visible"
            placement="bottom"
            :width="popoverWidth"
            trigger="click"
            @show="handleShow"
            @hide="handleHide"
        >
            <div class="treeWrap" :style="{ maxHeight: maxHeight + 'px' }">
                <position-tree
                    ref="positionTreeRef"
                    :default-expand="defaultExpand"
                    :default-checked="defaultChecked"
                    :is-strictly="isStrictly"
                    :scope-strategy-id="scopeStrategyId"
                    :flag="flag"
                    @selected="handleSelected"
                    @setDefaultExpand="setDefaultExpand"
                ></position-tree>
            </div>
            <template v-slot:reference>
                <div class="el-select el-select--small" :title="tips">
                    <div class="el-select__tags">
                        <span>
                            <template v-for="(item, index) in selectedNodes">
                                <span
                                    v-if="index <= showLabelNumber - 1"
                                    class="el-tag el-tag--info el-tag--small el-tag--light"
                                >
                                    <span class="el-select__tags-text custom-tags_text">{{ item.name }}</span>
                                    <el-icon class="el-tag__close"><el-icon-close /></el-icon>
                                </span>
                            </template>
                            <span
                                v-if="selectedNodes.length > showLabelNumber"
                                class="el-tag el-tag--info el-tag--small el-tag--light"
                            >
                                <span class="el-select__tags-text">+{{ selectedNodes.length - showLabelNumber }}</span>
                            </span>
                        </span>
                    </div>
                    <div class="el-input el-input--small el-input--suffix">
                        <input
                            type="text"
                            readonly="readonly"
                            autocomplete="off"
                            :placeholder="blankTips"
                            class="el-input__inner"
                            :class="{ 'error-border': showErrorMessage }"
                        />
                        <span class="el-input__suffix" @mouseover="inputMouseOver" @mouseleave="inputMouseLeave">
                            <span class="el-input__suffix-inner">
                                <el-icon class="el-select__caret el-input__icon"><el-icon-arrow-up /></el-icon>
                                <el-icon class="el-select__caret el-input__icon"><el-icon-circle-close /></el-icon>
                            </span>
                        </span>
                    </div>
                    <!-- <div class="el-form-item__error" v-show="showErrorMessage">
                          {{errorMessage}}
                      </div> -->
                </div>
            </template>
        </el-popover>
    </div>
</template>

<script>
import {
    Close as ElIconClose,
    ArrowUp as ElIconArrowUp,
    CircleClose as ElIconCircleClose,
} from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import PositionTree from './SiteSearchMulSelectTree';
import HTTP from '@/util/httpService.js';
export default {
    components: {
        'position-tree': PositionTree,
        ElIconClose,
        ElIconArrowUp,
        ElIconCircleClose,
    },
    props: {
        popoverWidth: {
            type: Number,
            default: 196,
        },
        nodeTypes: {
            type: Array,
            default: () => {
                return ['RealGroup', 'Site', 'CoreSite'];
            },
        },
        maxHeight: {
            type: Number,
            default: 300,
        },
        showLabelNumber: {
            // 显示的数据项数量：默认显示1个，其他的以计数项显示。
            type: Number,
            default: 1,
        },
        halfCheckedKeys: {
            type: Array,
            default: () => {
                return [];
            },
        },
        isValidate: {
            type: Boolean,
            default: false,
        },
        flag: {
            type: Boolean,
            default: false,
        },
        defaultChecked: {
            type: Array,
            default: () => {
                return [];
            },
        },
        scopeStrategyId: {
            type: String,
            default: '',
        },
        isStrictly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: false,
            inputClearable: false,
            opened: false,
            treeProp: {
                label: 'name',
                children: 'children',
                isLeaf: 'isLeaf',
            },
            selectedNodes: [], // 树节点选中值,
            treeInstance: null,
            searchTreeInstance: null,
            showErrorMessage: false,
            defaultExpand: [],
        };
    },
    created() {},
    mounted() {
        // this.$nextTick(()=>{
        // 	this.treeInstance = this.$refs.positionTreeRef.$refs.tree;
        // 	this.searchTreeInstance = this.$refs.positionTreeRef.$refs.searchTree;
        // })
    },
    watch: {
        isValidate(val) {
            if (!val) {
                this.showErrorMessage = false;
            }
        },
        selectedNodes(val) {
            $emit(this, 'getSelectedNodes', val);
        },
        defaultChecked: {
            deep: true,
            immediate: true,
            handler(val) {
                if (val.length) {
                    this.getPathByIds(val);
                }
            },
        },
    },
    computed: {
        blankTips() {
            let tips = this.$t('placeholder.select');
            if (this.selectedNodes.length > 0) {
                tips = '';
            }
            return tips;
        },
        tips() {
            let tips = '';
            for (let i = 0; i < this.selectedNodes.length; i++) {
                if (i == 0) {
                    tips = this.selectedNodes[i].name;
                } else {
                    tips = tips + ',' + this.selectedNodes[i].name;
                }
            }
            return tips;
        },
        // errorMessage(){
        // 	let tips = this.$t("tipMessage.pleaseSelect", {filedName: this.$t("monitor.batchExportMonitoring.position")});
        // 	if(this.selectedNodes.length>50){
        // 		tips=this.$t("monitor.batchExportMonitoring.numberLTNum", {num: 50});
        // 	}
        // 	return tips;
        // }
    },
    methods: {
        getPathByIds(ids) {
            HTTP.request('getPathByIds', {
                method: 'post',
                data: ids,
                complete: res => {
                    if (res.code === 0 && res.data) {
                        let idPaths = Object.values(res.data);
                        let nodeUrlId = [];
                        if (idPaths.length) {
                            idPaths.forEach(item => {
                                let arr = item.idPath.split('/');
                                nodeUrlId = [...nodeUrlId, ...arr];
                            });
                            this.setDefaultExpand(nodeUrlId);
                        }
                    }
                },
                error: () => {
                    this.loading = false;
                    this.$message.error(this.$t('common-query-error'));
                },
            });
        },
        handleShow() {
            // 下拉框点击弹出选项树
            this.opened = true;
        },
        handleHide() {
            // 下拉框隐藏不显示选项树
            this.opened = false;
        },
        handleSelected(checkedNodes, keys) {
            this.selectedNodes = checkedNodes;
        },
        setDefaultExpand(nodeUrlId) {
            let arr = [...this.defaultExpand, ...nodeUrlId];
            this.defaultExpand = Array.from(new Set(arr));
        },
        handleRemoveTag(val) {
            // 单个删除tag
            this.removeTag(val);
        },
        removeTag(id) {
            // 删除指定一个节点
            this.selectedNodes = this.selectedNodes.filter(node => {
                return node.id != id;
            });
            this.$refs.positionTreeRef.uncheckNode(id);
        },
        handleClearable() {
            // 整个下拉清除
            this.$refs.positionTreeRef.handleClear();
            this.selectedNodes = [];
            // this.$emit('cleared');
        },
        inputMouseOver() {
            this.inputClearable = true;
        },
        inputMouseLeave() {
            this.inputClearable = false;
        },
    },
    emits: ['getSelectedNodes'],
};
</script>

<style lang="scss" scoped>
.treeWrap {
    overflow: auto;
    min-height: 100px;
}
.treeWrap::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
.treeWrap :deep(.el-tree) > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.treeWrap :deep(.el-tree-node__content) > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
.el-select .el-input .error-border,
.el-select .el-input .error-border:focus {
    border-color: #f56c6c;
}
.el-select .el-select__tags .el-tag .custom-tags_text {
    display: inline-block;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}
// :deep(.el-select) .el-tag__close.el-icon-close {
//     top: -5px;
// }
</style>
