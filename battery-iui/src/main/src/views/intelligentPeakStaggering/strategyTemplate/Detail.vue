<template>
    <div v-loading="pageLoading" class="strategy-detail bcua">
        <div class="uedm-breadcrumbs title uedm-content-area">
            <div style="float: right">
                <el-button v-if="showExport" type="primary" @click="handleExportXls('template')">
                    {{ $t('battery.fields.exportXls') }}
                </el-button>
                <el-button @click="handleBack">
                    {{ $t('button.back') }}
                </el-button>
            </div>
            <li v-if="queryType !== 'history'">
                <span>{{ $t('button.detail') }}</span>
            </li>
        </div>
        <div class="uedm-space" :style="{ height: height - 96 + 'px', overflow: 'auto' }">
            <el-form ref="templateForm" class="uedm-content-area" :model="templateForm" :inline="true">
                <el-row>
                    <el-col :span="6">
                        <el-form-item :label="$t('common.templateName')" class="ellipsis-content">
                            <span :title="templateForm.name">{{ templateForm.name || '--' }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('battery.fields.peakShiftingScenario')" class="item-content">
                            {{ deviceTypeMap[templateForm.deviceType] || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('battery.fields.source')" class="item-content">
                            {{ sourceMap[templateForm.source] || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('battery.fields.periodPolicy')" class="ellipsis-content">
                            <span :title="templateForm.seasonStrategyName">
                                {{ templateForm.seasonStrategyName || '--' }}
                            </span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6" v-if="templateForm.source === SOURCE_EXCEL">
                        <el-form-item
                            :label="$t('common.attachment')"
                            class="ellipsis-content"
                        >
                            <el-link
                                v-if="templateForm.fileId && !queryType"
                                :icon="ElIconDocument"
                                :underline="false"
                                :title="templateForm.fileName"
                                @click="handleExportXls('file')"
                            >
                                {{ templateForm.fileName || '--' }}
                            </el-link>
                            <span v-else :title="templateForm.fileName">
                                {{ templateForm.fileName || '--' }}
                            </span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('table.creator')" class="item-content">
                            {{ templateForm.userCreate || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('table.gmtCreated')" class="item-content">
                            {{ templateForm.gmtCreate || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('table.modifiedBy')" class="item-content">
                            {{ templateForm.userModified || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col v-if="templateForm.source === SOURCE_WEB" :span="6">
                        <el-form-item :label="$t('table.gmtModified')" class="item-content">
                            {{ templateForm.gmtModified || '--' }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col v-if="templateForm.source === SOURCE_EXCEL" :span="6">
                        <el-form-item :label="$t('table.gmtModified')" class="item-content">
                            {{ templateForm.gmtModified || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="$t('common.version')">
                            {{ templateForm.version || '--' }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item :label="$t('battery.fields.remarks')" class="ellipsis-content">
                            <span :title="templateForm.remark">{{ templateForm.remark || '--' }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="uedm-content-area">
                <el-tabs v-model="activeName" class="uedm-navigation-tabs">
                    <el-tab-pane :label="$t('peakSetting.title.period')" name="toPeriod">
                        <PeriodContent
                            v-show="showSettingDetail"
                            ref="Period"
                            :active-name="'toPeriod'"
                            :config-source="templateForm.source"
                            :page-type="'detail'"
                            :period-data="periodData"
                            :period-mode="periodMode"
                        ></PeriodContent>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('peakSetting.title.holiday')" name="toHoliday">
                        <PeriodContent
                            v-show="showSettingDetail"
                            ref="Holiday"
                            :active-name="'toHoliday'"
                            :config-source="templateForm.source"
                            :page-type="'detail'"
                            :period-data="holidayData"
                        ></PeriodContent>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script>
import { Document as ElIconDocument } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import axios from 'axios';
import HTTP from '@/util/httpService.js';
import PeriodContent from './periodContent.vue';

export default {
    data() {
        this.DEVICE_BCUA = 'BCUA';
        this.DEVICE_CSU5 = 'CSU5';
        this.SOURCE_WEB = 'web';
        this.SOURCE_EXCEL = 'excel';
        return {
            pageLoading: false,
            activeName: 'toPeriod',
            templateForm: {
                id: '',
                name: '',
                deviceType: '',
                source: '',
                seasonStrategyName: '',
                fileName: '',
                userCreate: '',
                userModified: '',
                remark: '',
                gmtCreate: '',
                gmtModified: '',
                version: '',
                fileId: '',
                seasonStrategyId: '',
            },
            loading: false,
            periodData: [],
            holidayData: [],
            queryReturn: false,
            periodMode: '',
            ElIconDocument,
        };
    },
    components: {
        PeriodContent,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        rowData: {
            type: Object,
            default: () => ({}),
        },
        operType: {
            type: String,
            default: 'add',
        },
        queryType: {
            type: String,
            default: '',
        },
    },
    computed: {
        showExport() {
            return this.rights['intelligent.peak.setting.strategy.template.export'] && !this.queryType;
        },
        showSettingDetail() {
            return this.templateForm.fileId || this.templateForm.seasonStrategyId;
        },
        deviceTypeMap() {
            let map = {};
            this.$store.getters.getPeakDeviceTypes.forEach(item => {
                map[item.id] = item.name;
            });
            return map;
        },
        sourceMap() {
            return {
                [this.SOURCE_WEB]: this.$t('battery.fields.webConfig'),
                [this.SOURCE_EXCEL]: this.$t('battery.fields.excelImport'),
            };
        },
        height() {
            return this.$store.getters.getHeight;
        },
    },
    mounted() {
        this.queryTemplateDetail();
    },
    methods: {
        handleExportXls(type) {
            if (!this.showExport) {
                return;
            }
            const EXPORT_TEMPLATE_URL = '/api/battery-manager/v1/peak-shift-file/device/export-template-to-xls';
            const EXPORT_FILE_URL = '/api/battery-manager/v1/peak-shift-file/device/universal-download';
            let exportUrl = type === 'template' ? EXPORT_TEMPLATE_URL : EXPORT_FILE_URL;
            let exportId = type === 'template' ? `id=${this.templateForm.id}` : `fileId=${this.templateForm.fileId}`;
            const forgerydefense = (localStorage['csrftoken'] && localStorage['csrftoken'].replace(/\"/g, '')) || '';
            const languageOption =
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) || '';
            let url = `${exportUrl}?${exportId}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            axios.get(url, config).then(res => {
                // 导出错误，返回json对象，需判断
                if (res.data.type === 'application/json') {
                    let reader = new FileReader();
                    reader.onload = e => {
                        let result = JSON.parse(e.target.result);
                        if (result && result.code !== 0) {
                            this.$message.error(result.message);
                        }
                    };
                    reader.readAsText(res.data, ['utf-8']);
                } else {
                    // 导出成功，返回数据流
                    let blob = new Blob([res.data]);
                    let url = window.URL.createObjectURL(blob); // 创建下载的链接
                    let link = document.createElement('a');
                    let fileName = '';
                    if (res.headers['content-disposition']) {
                        let contentDisposition = res.headers['content-disposition'];
                        let key = type === 'template' ? 'filename=' : 'fileName=';
                        fileName = contentDisposition.split(key)[1];
                        fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                    }
                    link.style.display = 'none';
                    link.href = url;
                    link.download = `${fileName}`; // 下载后文件名
                    document.body.appendChild(link);
                    link.click(); // 点击下载
                    document.body.removeChild(link); // 下载完成移除元素
                    window.URL.revokeObjectURL(url); // 释放掉blob对象
                }
            });
        },
        handleBack() {
            $emit(this, 'show', { view: 'list' });
        },
        queryTemplateDetail() {
            this.pageLoading = true;
            let queryUrl = 'getStrategyTemplateDetail';
            let paramKey = 'id';
            if (this.queryType === 'history') {
                queryUrl = 'getStrategyTemplateHistoryDetail';
                paramKey = 'taskId';
            }
            HTTP.request(queryUrl, {
                method: 'get',
                urlParam: {
                    [paramKey]: this.rowData.id,
                },
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data || {};
                        if (this.queryType === 'history') {
                            result = result.templateDetail || {};
                        }
                        this.templateForm = {
                            id: result.id,
                            name: result.name,
                            deviceType: result.deviceType,
                            source: result.source,
                            seasonStrategyName: result.seasonStrategyName,
                            fileName: result.fileName,
                            userCreate: result.userCreate,
                            userModified: result.userModified,
                            remark: result.remark,
                            gmtCreate: result.gmtCreate,
                            gmtModified: result.gmtModified,
                            version: result.version,
                            fileId: result.fileId,
                            seasonStrategyId: result.seasonStrategyId,
                        };
                        this.periodData = result.detail;
                        this.holidayData = result.holiday;
                        this.periodMode = result.mode;
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.pageLoading = false;
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                    this.pageLoading = false;
                },
            });
        },
    },
    emits: ['show'],
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

:deep(.el-table__body-wrapper) > table {
    border-top: unset;
}
:deep(.el-form) .el-form-item__content .el-input__inner {
    width: 240px;
}
.el-form .select-policy-config {
    .el-form-item__content .el-input {
        width: 240px;
    }
    .el-form-item__content .el-button {
        padding: 9px 8px;
    }
}
.formEdit :deep(.el-dialog) .el-dialog__body {
    padding: 0 30px 0px;
}
:deep(.el-dialog__footer) {
    padding: 10px 24px 24px;
}
.uedm-breadcrumbs li::before {
    content: '';
}
:deep(.el-form-item).ellipsis-content {
    width: 100%;
    .el-form-item__content {
        width: calc(100% - 132px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        .el-input__count {
            line-height: 24px;
        }
    }
}
.strategy-detail {
    .el-link {
        color: #1993ff;
    }
}
</style>
