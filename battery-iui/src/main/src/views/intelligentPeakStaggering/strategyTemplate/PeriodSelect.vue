<template>
    <div :class="['period-select', mode === '2' ? 'period-select-day' : '']" @mouseleave="selectBlur">
        <div
            v-for="val in timeOptions"
            :key="val"
            class="week-item"
            :class="[
                inrange(val) ? 'week-in-range' : '',
                inAlreadySelected(val) ? 'week-already-selected' : '',
                inrepeat(val) ? 'week-repeated' : '',
                inselect && inrange(val) ? 'week-selected' : '',
                val === selectStart ? 'start-date' : '',
                val === selectEnd ? 'end-date' : '',
            ]"
            @click.stop="weekClick(arguments, val)"
            @mouseenter="weekFocus(arguments, val)"
        >
            <div :class="['date-value']">
                {{ mode === '1' ? weekLabel[val] : val }}
            </div>
        </div>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
export default {
    props: {
        mode: {
            type: String,
            default: '0',
        },
        rowIndex: {
            type: Number,
            default: -1,
        },
        allData: {
            type: Array,
            default: () => [],
        },
        pageType: {
            type: String,
            default: 'add',
        },
    },
    data() {
        return {
            dayArr: [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31,
            ],
            weekArr: [1, 2, 3, 4, 5, 6, 7],
            selectStart: 0,
            selectEnd: 0,
            curHover: 0,
            inselect: false,
            selectedDates: [],
            curSelectDates: [],
        };
    },
    computed: {
        inrange(valKey) {
            let _this = this;
            return function (valKey) {
                let selected = JSON.parse(JSON.stringify(_this.selectedDates));
                if (_this.inselect) {
                    selected = _this.selectedDates.filter(item => {
                        return _this.curSelectDates.indexOf(item) < 0;
                    });
                    let curSelect = _this.curSelectDates.filter(item => {
                        return _this.selectedDates.indexOf(item) < 0;
                    });
                    selected = selected.concat(curSelect);
                }
                return selected.indexOf(valKey) > -1;
            };
        },
        inrepeat(valKey) {
            let _this = this;
            return function (valKey) {
                return _this.repeatArr.indexOf(valKey) > -1;
            };
        },
        inAlreadySelected(valKey) {
            let _this = this;
            return function (valKey) {
                return _this.alreadySelectedArr.indexOf(valKey) > -1;
            };
        },
        alreadySelectedArr() {
            let result = [];
            let data = [];
            this.allData.forEach((item, index) => {
                if (this.rowIndex !== index) {
                    data.push(item);
                }
            });
            data.forEach((item, index) => {
                item.selectedDates.forEach(item => {
                    if (result.indexOf(item) < 0) {
                        result.push(item);
                    }
                });
            });
            return result;
        },
        repeatArr() {
            let result = [];
            let curRow = this.allData[this.rowIndex] || null;
            if (curRow && this.alreadySelectedArr.length > 0) {
                curRow.selectedDates.forEach(item => {
                    if (this.alreadySelectedArr.indexOf(item) > -1) {
                        result.push(item);
                    }
                });
            }
            return result;
        },
        weekLabel() {
            return {
                1: this.$t('peakSetting.fields.mon'),
                2: this.$t('peakSetting.fields.tue'),
                3: this.$t('peakSetting.fields.wed'),
                4: this.$t('peakSetting.fields.thur'),
                5: this.$t('peakSetting.fields.fri'),
                6: this.$t('peakSetting.fields.sat'),
                7: this.$t('peakSetting.fields.sun'),
            };
        },
        timeOptions() {
            let result = this.dayArr;
            if (this.mode === '1') {
                result = this.weekArr;
            }
            return result;
        },
    },
    mounted() {
        let curRow = this.allData[this.rowIndex] || null;
        if (curRow) {
            this.selectedDates = JSON.parse(JSON.stringify(curRow.timeGran || []));
        }
        window.addEventListener('click', e => {
            if (this.pageType === 'detail') {
                return;
            }
            if (this.inselect && !e.target.className.includes('week-item')) {
                this.inselect = false;
                this.selectStart = 0;
                this.selectEnd = 0;
            }
        });
    },
    methods: {
        selectBlur(e) {
            if (this.pageType === 'detail') {
                return;
            }
            this.inselect = false;
            this.selectStart = 0;
            this.selectEnd = 0;
        },
        weekClick(argu, val) {
            if (this.pageType === 'detail') {
                return;
            }
            if (!this.inselect) {
                this.inselect = true;
                this.selectStart = val;
                this.curSelectDates = [val];
            } else {
                this.selectStart = 0;
                this.selectEnd = 0;
                let selected = this.selectedDates.filter(item => {
                    return this.curSelectDates.indexOf(item) < 0;
                });
                let curSelect = this.curSelectDates.filter(item => {
                    return this.selectedDates.indexOf(item) < 0;
                });
                this.selectedDates = selected.concat(curSelect);
                this.inselect = false;
                $emit(
                    this,
                    'change',
                    this.selectedDates.sort(function (a, b) {
                        return a - b;
                    })
                );
            }
        },
        weekFocus(argu, val) {
            if (this.pageType === 'detail') {
                return;
            }
            if (this.selectStart > 0 && this.inselect) {
                this.selectEnd = val;
                this.curSelectDates = [];
                let min = Math.min(this.selectStart, val);
                let max = Math.max(this.selectStart, val);
                for (let i = min; i <= max; i++) {
                    let index = this.curSelectDates.indexOf(i);
                    if (index < 0) {
                        this.curSelectDates.push(i);
                    }
                }
            }
            this.curHover = val;
        },
    },
    emits: ['change'],
};
</script>

<style lang="scss" scoped>
.period-select {
    width: 234px;
    padding: 2px 4px;
    margin: 0 auto;
}
.period-select-day {
    border: 1px solid #d9d9d9;
}
.week-item {
    display: inline-block;
    text-align: center;
    width: 32px;
    height: 32px;
    margin: 2px 0;
    padding: 2px;
    line-height: 32px;
    font-size: 12px;
    color: #404040;
    user-select: none;
    &:hover {
        color: #1993ff;
    }
    cursor: pointer;
    box-sizing: border-box;
    &.week-in-range {
        background-color: #e6f7ff;
    }
    &.week-already-selected {
        background-color: #d9d9d9;
    }
    &.week-selected {
        background-color: #e6f7ff;
    }
    &.week-in-range.week-repeated {
        background-color: #ffe5d3;
    }
    .date-value {
        width: 28px;
        height: 28px;
        line-height: 28px;
        border-radius: 50%;
    }
    &.start-date .date-value {
        background-color: #1993ff;
        color: #fff;
    }
    &.end-date .date-value {
        background-color: #1993ff;
        color: #fff;
    }
}
html.dark {
    .period-select {
        &.period-select-day {
            border-color: #474a59;
            background-color: #2a2c38;
        }
        .week-item {
            color: #d9d9d9;
            &.week-in-range {
                background-color: #1fb6d9;
            }
            &.week-already-selected {
                background-color: #474a59;
            }
            &.week-selected {
                background-color: #1fb6d9;
            }
            &.week-in-range.week-repeated {
                background-color: #ff9852;
            }
        }
    }
}
</style>
