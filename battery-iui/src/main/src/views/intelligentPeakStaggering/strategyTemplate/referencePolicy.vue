<template>
    <div class="reference-page">
        <div class="reference-title">
            <span>{{ $t('battery.fields.periodPolicyReference') }}</span>
            <span v-if="activeName === 'toPeriod'">
                {{ `( ${getRange()} )` }}
            </span>
        </div>
        <div class="reference-content">
            <div class="reference-row">
                <div
                    class="reference-col"
                    v-for="(item, index) in data.reference"
                    :key="'reference_' + index"
                    :span="6"
                    :style="{ width: item.widthX + '%' }"
                >
                    <div class="content-item" :style="{ 'background-color': electricType[item.strategyType] }">
                        <span class="type-name">{{ peakOptions[item.strategyType] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
const DAY_MODE = '0';
const WEEK_MODE = '1';
const MONTH_MODE = '2';
export default {
    props: {
        data: {
            type: Object,
            default: () => ({
                reference: [],
            }),
        },
        activeName: {
            type: String,
            default: '',
        },
        periodMode: {
            type: String,
            default: '',
        },
    },
    data() {
        return {};
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        electricType() {
            let result = {
                0: 'rgba(146, 221, 146, 0.2)',
                1: 'rgba(31, 182, 217, 0.2)',
                2: 'rgba(255, 200, 80, 0.2)',
                3: 'rgba(255, 152, 82, 0.2)',
            };
            if (this.isDark) {
                result = {
                    0: 'rgba(146, 221, 146, 0.5)',
                    1: 'rgba(31, 182, 217, 0.5)',
                    2: 'rgba(255, 200, 80, 0.5)',
                    3: 'rgba(255, 152, 82, 0.5)',
                };
            }
            return result;
        },
        weekLabel() {
            return {
                1: this.$t('battery.fields.mon'),
                2: this.$t('battery.fields.tue'),
                3: this.$t('battery.fields.wed'),
                4: this.$t('battery.fields.thur'),
                5: this.$t('battery.fields.fri'),
                6: this.$t('battery.fields.sat'),
                7: this.$t('battery.fields.sun'),
            };
        },
        peakOptions() {
            return {
                0: this.$t('battery.fields.trough'),
                1: this.$t('battery.fields.plain'),
                2: this.$t('battery.fields.peak'),
                3: this.$t('battery.fields.highest'),
            };
        },
    },
    methods: {
        getRange() {
            let timeGran = JSON.parse(JSON.stringify(this.data.timeGran || []));
            let dateRange = JSON.parse(JSON.stringify(this.data.dateRange || []));
            let rangeString = (dateRange[0] || '--') + ' - ' + (dateRange[1] || '--');
            if (this.periodMode === DAY_MODE) {
                return rangeString;
            } else if (this.periodMode === WEEK_MODE) {
                timeGran.sort(function (a, b) {
                    return a - b;
                });
                let result = [];
                let tempArr = [];
                for (let index = 0; index < timeGran.length; index++) {
                    tempArr.push(this.weekLabel[timeGran[index]]);
                    if (timeGran[index + 1] !== timeGran[index] + 1) {
                        if (tempArr.length > 2) {
                            result.push(tempArr[0] + ' - ' + tempArr[tempArr.length - 1]);
                        } else {
                            result.push(tempArr.join(', '));
                        }
                        tempArr = [];
                    }
                }
                return rangeString + ': ' + result.join(', ');
            } else if (this.periodMode === MONTH_MODE) {
                timeGran.sort(function (a, b) {
                    return a - b;
                });
                let start = timeGran[0];
                let end = timeGran[timeGran.length - 1];
                return rangeString + ': ' + this.formatMonthLabel(start) + ' - ' + this.formatMonthLabel(end);
            }
        },
        formatMonthLabel(day) {
            if (!day) {
                return '--';
            }
            let result = '';
            if (this.$i18n.locale === 'en-US') {
                if (day === 1) {
                    result = '1st';
                } else if (day === 2) {
                    result = '2nd';
                } else if (day === 3) {
                    result = '3rd';
                } else {
                    result = day + 'th';
                }
            } else {
                result = day + '日';
            }
            return result;
        },
    },
};
</script>

<style lang="scss" scoped>
.reference-page {
    margin-bottom: 12px;
    .reference-title {
        height: 24px;
        margin-bottom: 4px;
        font-family: Microsoft YaHei;
        color: #404040;
        font-size: 12px;
        line-height: 24px;
    }
    .reference-content {
        .reference-row {
            display: flex;
            position: relative;
            box-sizing: border-box;
        }
        .content-item {
            display: inline-block;
            width: 100%;
            height: 32px;
            background-color: #1fb6d9;
            text-align: center;
            .type-name {
                display: inline-block;
                height: 32px;
                font-family: Microsoft YaHei;
                font-size: 12px;
                line-height: 32px;
                color: #404040;
            }
        }
    }
}
html.dark {
    .reference-title {
        color: #a4a7b3;
    }
    .reference-content .content-item {
        .type-name {
            color: #d9d9d9;
        }
    }
}
</style>
