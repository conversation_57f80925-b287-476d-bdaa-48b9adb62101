<template>
    <div class="device-list">
        <div class="tableBar">
            <!-- v-if="rights['intelligent.peak.setting.set']" -->
            <el-button
                v-if="setStartupAuth"
                type="default"
                :disabled="queryParam.enablePeak === false"
                @click="handleAllCommand('start-stop')"
            >
                {{ $t('battery.fields.allEnableDisablesettings') }}
            </el-button>
            <el-button
                v-if="setStartupAuth"
                type="default"
                :disabled="selectedRows.length < 1"
                @click="handleBatchCommand('start-stop')"
            >
                {{ $t('battery.fields.batchEnableDisablesettings') }}
            </el-button>
            <!-- <el-dropdown v-if="setStartupAuth || setPolicyAuth" @command="handleAllCommand">
                      <el-button type="default" :disabled="queryParam.enablePeak === false">
                          {{ $t('peakSetting.button.allSetting') }}
                          <i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item v-if="setPolicyAuth" command="policy">
                              {{ $t('peakSetting.policySetting') }}
                          </el-dropdown-item>
                          <el-dropdown-item v-if="setStartupAuth" command="start-stop">
                              {{ $t('battery.fields.enableDisablesettings') }}
                          </el-dropdown-item>
                      </el-dropdown-menu>
                  </el-dropdown>
                  <el-dropdown v-if="setStartupAuth || setPolicyAuth" @command="handleBatchCommand">
                      <el-button type="default" :disabled="selectedRows.length < 1">
                          {{ $t('peakSetting.button.batchSetting') }}
                          <i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item v-if="setPolicyAuth" :disabled="batchDisabled" command="policy">
                              {{ $t('peakSetting.policySetting') }}
                          </el-dropdown-item>
                          <el-dropdown-item v-if="setStartupAuth" command="start-stop">
                              {{ $t('battery.fields.enableDisablesettings') }}
                          </el-dropdown-item>
                      </el-dropdown-menu>
                  </el-dropdown> -->
            <div class="select-content">
                <div v-show="selectedRows.length !== 0" class="select-tip">
                    {{ $t('common.selectedPre') }}
                    <span class="import-val">{{ selectedRows.length }}</span>
                    {{ $t('common.selectedPost') }}
                    <el-button type="text" @click="cancelSelected">
                        {{ $t('button.cancelAll') }}
                    </el-button>
                    <!-- <span v-if="batchDisabled" class="batch-tip">
                              <i class="el-icon-warning"></i>
                              {{ $t('peakSetting.tipMessage.differentDeviceType') }}
                          </span> -->
                </div>
            </div>
            <div class="filterButtons">
                <span class="filterItem">
                    <column-filter
                        ref="columnFilter"
                        :is-dark="isDark"
                        :value="filterList"
                        :tip="$t('tooltip.displayedItems')"
                        @save="handleFilterOk"
                    ></column-filter>
                </span>
                <span class="filterItem">
                    <el-tooltip class="item" effect="dark" :content="$t('button.refresh')" placement="bottom">
                        <span class="icon-button plx-ico-refresh-16" @click="refreshTable"></span>
                    </el-tooltip>
                </span>
            </div>
        </div>
        <el-table
            v-if="showTable"
            ref="deviceTable"
            v-loading="loading"
            style="width: 100%"
            border
            :data="list"
            row-key="deviceId"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
        >
            <el-table-column
                v-if="tableHeader.length > 0"
                type="selection"
                width="55"
                :selectable="tableRowSelectable"
                align="center"
                :reserve-selection="true"
            ></el-table-column>
            <el-table-column
                v-for="item in tableHeader"
                :key="item.id"
                :prop="item.id"
                :sortable="item.sortable ? 'custom' : false"
                :label="formatHeader(item)"
                show-overflow-tooltip
                :min-width="textSize(item)"
            >
                <template v-slot="scope">
                    <span v-if="item.id === 'enablePeak'">
                        {{ scope.row.enablePeak === true ? $t('common.yes') : $t('common.no') }}
                    </span>
                    <span v-else-if="item.id === 'runningStatus'">{{ runningStatusMap[scope.row.runningStatus] }}</span>
                    <div v-else-if="item.id === 'execStatus'">
                        <div
                            class="circle"
                            :style="{
                                backgroundColor: status_color_map[scope.row.execStatus],
                            }"
                        ></div>
                        {{ scope.row.execStatus === 'normal' ? $t('common.normal') : $t('common.abnormal') }}
                    </div>
                    <span v-else-if="item.id === 'currStrategy'">{{ peakMap[scope.row.currStrategy] }}</span>
                    <span v-else-if="item.id === 'deviceOnlineName'">
                        <span
                            class="status-circle"
                            :style="{
                                'background-color': handleOnlineStatusColor(scope.row.deviceOnlineName.id),
                            }"
                        ></span>
                        {{ scope.row.deviceOnlineName.name || '--' }}
                    </span>
                    <span v-else-if="item.id === 'deviceName'">
                        {{ (scope.row.deviceName && scope.row.deviceName.name) || '--' }}
                    </span>
                    <span v-else-if="item.id === 'deviceTypeName'">
                        {{ (scope.row.deviceTypeName && scope.row.deviceTypeName.name) || '--' }}
                    </span>
                    <span v-else-if="item.id === 'siteName'">
                        {{ (scope.row.siteName && scope.row.siteName.name) || '--' }}
                    </span>
                    <span v-else>{{ scope.row[item.id] || '--' }}</span>
                </template>
            </el-table-column>
            <!-- 操作列 -->
            <el-table-column
                v-if="tableHeader.length > 0"
                :label="$t('table.operation')"
                :width="$i18n.locale == 'en-US' ? '350px' : '200px'"
                fixed="right"
            >
                <template v-slot="scope">
                    <el-link type="primary" :underline="false" @click="toDetail(scope.row)">
                        {{ $t('button.detail') }}
                    </el-link>
                    <!-- <el-button
                              v-if="setPolicyAuth"
                              type="text"
                              :underline="false"
                              :disabled="
                                  queryParam.enablePeak === false ||
                                      scope.row.enablePeak === false ||
                                      scope.row.deviceOnlineId === 'offline'
                              "
                              @click="toSetPolicy(scope.row)"
                          >
                              {{ $t('peakSetting.policySetting') }}
                          </el-button> -->
                    <el-link
                        v-if="setStartupAuth"
                        type="primary"
                        :underline="false"
                        :disabled="
                            queryParam.enablePeak === false ||
                                scope.row.enablePeak === false ||
                                (scope.row.deviceOnlineName && scope.row.deviceOnlineName.id === 'offline')
                        "
                        @click="toSetStart(scope.row)"
                    >
                        {{ $t('battery.fields.enableDisablesettings') }}
                    </el-link>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 30]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>
        <div id="temporary"></div>

        <!-- 启停设置 -->
        <el-dialog
            v-model="settingVisible"
            v-loading="settingLoading"
            :title="setTitle"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div v-if="setTitle === $t('battery.fields.allEnableDisablesettings')" class="top-note">
                <i class="plx-ico-dialog-risk-48 note-icon"></i>
                <span>
                    {{ $t('peakSetting.tipMessage.peakSettingNote') }}
                </span>
            </div>
            <div class="set-content">
                <span>{{ $t('battery.title.peakShifting') }}</span>
                <el-radio v-model="enablePeak" :label="true">
                    {{ $t('common.enable') }}
                </el-radio>
                <el-radio v-model="enablePeak" :label="false">
                    {{ $t('common.disable') }}
                </el-radio>
            </div>
            <!-- <template #footer> -->
            <div class="bottomBar">
                <el-button type="primary" :disabled="enablePeak === ''" @click="handleConfirm">
                    {{ $t('button.confirm') }}
                </el-button>
                <el-button @click="hideSetting">{{ $t('button.cancel') }}</el-button>
            </div>
            <!-- </template> -->
        </el-dialog>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
export default {
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    components: {
        'column-filter': ColumnFilter,
    },
    props: {
        queryParam: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        this.peakMap = {
            99: '--',
            3: this.$t('common.tip'),
            2: this.$t('common.peak'),
            1: this.$t('common.flat'),
            0: this.$t('common.valley'),
        };
        this.status_color_map = {
            normal: '#76d63e',
            abnormal: '#e02222',
            unknown: '#e02222',
        };
        this.url_map = {
            all: 'deviceAllEnable',
            batch: 'deviceBatchEnable',
            singleRow: 'deviceEnable',
        };
        this.runningStatusMap = {
            enabled: this.$t('common.enabled'),
            disabled: this.$t('common.disabled'),
            part: this.$t('common.partiallyEnabledorDisabled'),
            unknown: this.$t('common.unknown'),
        };
        return {
            loading: false,
            list: [],
            pageNo: 1,
            pageSize: 10,
            total: 0,
            selectedRows: [],
            selectedSingleRow: null,
            sortOrder: '',
            sortProp: '',
            settingVisible: false,
            settingLoading: false,
            setTitle: '',
            setType: '',
            enablePeak: '',

            filterList: [], // 过滤条件列表数据原始
            filterParameter: [], // 过滤条件参数对象
            filterIds: [], // 过滤条件参数id
            tableHeader: [], // 列表表头
            showTable: true,
        };
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        minWidth() {
            return this.$i18n.locale === 'en-US' ? '205px' : '170px';
        },
        currencyUnit() {
            return this.$store.getters.getCurrencyUnit;
        },
        setPolicyAuth() {
            return this.rights['intelligent.peak.monitor.policy'];
        },
        setStartupAuth() {
            return this.rights['intelligent.peak.monitor.startup'];
        },
        batchDisabled() {
            // 判断是否为同一设备类型
            let types = Array.from(new Set(this.selectedRows.map(item => item.deviceType)));
            return this.selectedRows.length < 1 || types.length > 1;
        },
    },
    watch: {
        settingVisible(val) {
            if (val) {
                $emit(this, 'clearPeakInterval');
            }
        },
    },
    created() {
        this.getFilter();
    },
    methods: {
        tableRowSelectable(row) {
            let result = true;
            if (this.queryParam.enablePeak === false) {
                result = false;
            } else if (row.enablePeak === false) {
                result = false;
            } else if (row.deviceOnlineName && row.deviceOnlineName.id === 'offline') {
                result = false;
            }
            return result;
        },
        initQuery() {
            this.pageNo = 1;
            this.pageSize = 10;
            if (this.$refs.deviceTable) {
                this.$refs.deviceTable.clearSelection();
                this.$refs.deviceTable.clearSort();
            }
            this.sortOrder = '';
            this.sortProp = '';
            this.$nextTick(() => {
                this.queryList();
            });
        },
        textSize(item) {
            let text = item.name;
            if (item.unit) {
                text = text + '(' + item.unit + ')';
            }
            let content = document.getElementById('temporary');
            let span = document.createElement('span');
            let w = span.offsetWidth;
            span.style.visibility = 'hidden';
            span.style.fontSize = '14px';
            span.style.fontWeight = 'bolder';
            span.style.display = 'inline-block';
            content.appendChild(span);
            if (typeof span.textContent !== 'undefined') {
                span.textContent = text;
            } else {
                span.innerText = text;
            }
            w = parseFloat(window.getComputedStyle(span).width) - w;
            content.removeChild(span);
            if (['name'].includes(item.id)) {
                return Math.max(w + 50, 120);
            } else if (item.id === 'position') {
                return Math.max(w + 50, 280);
            } else if (['expirationDate', 'effectiveDate'].includes(item.id)) {
                return Math.max(w + 50, 140);
            }else {
                return w + 50; // 50为排序按钮和内边距
            }
        },
        handleAllCommand(command) {
            if (command === 'policy') {
                $emit(this, 'changeView', {
                    view: 'policy',
                    data: { ...this.queryParam, deviceIds: [] },
                });
            } else {
                this.setType = 'all';
                this.setTitle = this.$t('battery.fields.allEnableDisablesettings');
                this.settingVisible = true;
            }
        },
        handleBatchCommand(command) {
            if (command === 'policy') {
                let deviceIds = [];
                this.selectedRows.forEach(item => {
                    deviceIds.push(item.id);
                });
                $emit(this, 'changeView', {
                    view: 'policy',
                    data: {
                        ...this.queryParam,
                        deviceIds,
                        deviceType: (this.selectedRows[0] && this.selectedRows[0].deviceType) || '',
                    },
                });
            } else {
                this.setType = 'batch';
                this.setTitle = this.$t('battery.fields.batchEnableDisablesettings');
                this.settingVisible = true;
            }
        },
        toDetail(row) {
            $emit(this, 'changeView', { view: 'detail', data: row });
        },
        toSetPolicy(row) {
            let deviceIds = [row.deviceName.id];
            $emit(this, 'changeView', {
                view: 'policy',
                data: {
                    ...this.queryParam,
                    deviceIds,
                    deviceType: row.deviceTypeName.id,
                },
            });
        },
        toSetStart(row) {
            this.enablePeak = row.enablePeak;
            this.setType = 'singleRow';
            this.setTitle = this.$t('battery.fields.enableDisablesettings');
            this.selectedSingleRow = row;
            this.settingVisible = true;
        },
        hideSetting() {
            this.settingVisible = false;
            setTimeout(() => {
                $emit(this, 'initPeakInterval');
            }, 60000);
            this.enablePeak = '';
        },
        refreshTable() {
            // this.pageNo = 1;
            this.queryList();
        },
        handleCurrentChange() {
            this.queryList();
        },
        handleSizeChange() {
            this.pageNo = 1;
            this.queryList();
        },
        handleSelectionChange(rows) {
            this.selectedRows = [];
            rows.forEach(item => {
                this.selectedRows.push({
                    id: item.deviceName && item.deviceName.id,
                    name: item.deviceName && item.deviceName.name,
                    position: item.position,
                    deviceType: (item.deviceTypeName && item.deviceTypeName.id) || '',
                });
            });
        },
        sortChange(column) {
            // 排序
            this.sortProp = column.prop || '';
            if (column.order === 'descending') {
                this.sortOrder = 'desc';
            } else if (column.order === 'ascending') {
                this.sortOrder = 'asc';
            } else {
                this.sortOrder = '';
                this.sortProp = '';
            }
            this.queryList();
        },
        getListParams() {
            return {
                ...this.queryParam,
                peakShiftMonitorDims: this.filterParameter,
                order: this.sortOrder,
                sortBy: this.sortProp,
            };
        },
        queryList() {
            if (!this.filterParameter.length) {
                return;
            }
            if (Object.keys(this.queryParam).length===0) {
                return;
            }
            this.loading = true;
            this.list = [];
            const params = this.getListParams();
            const key = `${JSON.stringify(params)}_${this.pageNo}_${this.pageSize}`;
            HTTP.request('peakMonitorList', {
                method: 'post',
                urlParam: {
                    pageNo: this.pageNo,
                    pageSize: this.pageSize,
                },
                data: params,
                complete: resp => {
                    const currParams = this.getListParams();
                    const currKey = `${JSON.stringify(currParams)}_${this.pageNo}_${this.pageSize}`;
                    if (currKey === key) {
                        if (resp.code === 0) {
                            this.list = resp.data || [];
                            this.total = resp.total;
                        } else {
                            this.total = 0;
                        }
                        this.loading = false;
                    }
                },
                error: () => {
                    this.total = 0;
                    this.loading = false;
                },
            });
        },
        handleConfirm() {
            let url = this.url_map[this.setType];
            if (!url || this.enablePeak === '') {
                this.hideSetting();
                return;
            }
            this.settingLoading = true;
            let data = {};
            if (this.setType === 'singleRow') {
                data = {
                    id: (this.selectedSingleRow.deviceName && this.selectedSingleRow.deviceName.id) || '',
                    deviceType:
                        (this.selectedSingleRow.deviceTypeName && this.selectedSingleRow.deviceTypeName.id) || '',
                    enable: this.enablePeak,
                };
            } else if (this.setType === 'batch') {
                let option = [];
                this.selectedRows.forEach(item => {
                    option.push({
                        id: item.id || '',
                        name: item.name,
                        deviceType: item.deviceType || '',
                        enable: this.enablePeak,
                    });
                });
                data = option;
            } else if (this.setType === 'all') {
                data = {
                    ...this.queryParam,
                    enable: this.enablePeak,
                };
            }

            HTTP.request(url, {
                method: 'post',
                data,
                complete: resp => {
                    if (resp.code === 0) {
                        this.$message({
                            showClose: true,
                            duration: 1000,
                            message: this.$t('peakSetting.tipMessage.enableSuccess'),
                            type: 'success',
                        });
                        this.$refs.deviceTable.clearSelection();
                        this.hideSetting();
                        this.queryList();
                    } else if (resp.code === -604) {
                        let message = '';
                        if (this.setType === 'singleRow') {
                            message = this.$t('peakSetting.tipMessage.enableFail');
                        } else {
                            let error = '';
                            if (resp.error) {
                                error = JSON.parse(resp.error).toString();
                            }
                            message =
                                this.$t('peakSetting.tipMessage.enablePartFail') +
                                '<br/>' +
                                this.$t('peakSetting.failDevice') +
                                ': ' +
                                error;
                        }
                        this.$message({
                            showClose: true,
                            dangerouslyUseHTMLString: true,
                            duration: 5000,
                            message,
                            type: 'error',
                        });
                    } else if (resp.code === -304) {
                        if (this.setType === 'singleRow' || this.setType === 'batch') {
                            this.$message({
                                showClose: true,
                                duration: 5000,
                                message: this.$t('peakSetting.tipMessage.deviceTypeError'),
                                type: 'error',
                            });
                        }
                    }
                    this.settingLoading = false;
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.requestError'),
                        type: 'error',
                    });
                    this.settingLoading = false;
                },
            });
        },
        cancelSelected() {
            this.$refs.deviceTable.clearSelection();
        },
        handleFilterOk(d) {
            this.saveFilter(d);
        },
        saveFilter(d) {
            let updateParameter = [];
            for (let i = 0; i < d.length; i++) {
                let item = d[i];
                updateParameter.push({
                    id: item.id,
                    sequence: item.sequence,
                    enable: item.enable,
                });
            }
            HTTP.request('peakMonitorDimSave', {
                method: 'put',
                data: updateParameter,
                complete: data => {
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.getFilter();
                        this.showFilter = false;
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotRange') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -208) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotModify') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: data => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        getFilter() {
            this.loading = true;
            HTTP.request('peakMonitorDimQuery', {
                method: 'get',
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        this.filterList = data.data;
                        this.getFilterParameter(data.data, false);
                    }
                },
                error: () => {
                    this.filterList = [];
                    this.loading = false;
                },
            });
        },
        getFilterParameter(d) {
            this.filterParameter = [];
            this.filterIds = [];
            this.tableHeader = [];
            for (let i = 0; i < d.length; i++) {
                let item = d[i];
                if (item.enable) {
                    this.filterParameter.push(item);
                    this.filterIds.push(item.id);
                    this.tableHeader.push({
                        id: item.id,
                        sequence: item.sequence,
                        name: item.name,
                        unit: item.unit,
                        sortable: item.sortable,
                    });
                }
            }
            this.showTable = false;
            this.$nextTick(() => {
                this.showTable = true;
            });
            this.pageNo = 1;
            this.queryList();
        },
        formatHeader(item) {
            let name = item.name;
            if (item.unit) {
                name = name + '(' + item.unit + ')';
            }
            return name;
        },
        handleOnlineStatusColor(deviceOnlineId) {
            let color = this.$store.getters.getIsDark ? '#1b1d26' : '#fff';
            if (deviceOnlineId === 'online') {
                color = '#76d63e';
            } else if (deviceOnlineId === 'offline') {
                color = '#e02222';
            }
            return color;
        },
    },
    emits: ['changeView', 'clearPeakInterval', 'initPeakInterval'],
};
</script>

<style lang="scss" scoped>
.tableBar {
    display: flex;
    height: 32px;
    .el-dropdown {
        .el-button {
            padding: 9px 16px;
        }
        & + .el-dropdown {
            margin-left: 8px;
        }
    }
}
.icon-button {
    float: right;
    font-size: 14px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: none !important;
    color: #595959;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
        border-color: #0c7beb;
        color: #1993ff;
    }

    &.is-disabled {
        border-color: #bfbfbf;
        color: #bfbfbf;
    }
}
:deep(.set-content) {
    text-align: center;
    font-size: 14px;
    .el-radio:nth-child(2) {
        margin: 0 16px;
    }
}
.circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #0c7beb;
    border-radius: 50%;
}
.top-note {
    width: 100%;
    font-size: 12px;
    text-align: left;
    color: #333333;
    line-height: 38px;
    background-color: rgba(25, 147, 255, 0.15);
    height: 38px;
    margin-bottom: 16px;
    .note-icon {
        margin-left: 16px;
        margin-right: 8px;
        color: #1993ff;
        font-size: 14px;
        position: relative;
        top: 2px;
    }
}
.select-content {
    flex-grow: 1;
}
.select-tip {
    // display: inline-block;
    background-color: #fafafa;
    box-sizing: border-box;
    // width: calc(100% - 345px);
    // flex-grow: 1;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #404040;
    margin: 0 16px;
    padding: 0 16px;
    .import-val {
        font-weight: 700;
        padding: 0 5px;
    }
    .el-button {
        padding-left: 24px;
    }
}
.batch-tip {
    margin-left: 24px;

    .el-icon-warning {
        color: #ff9852;
    }
}
.status-circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}
:deep(.el-pagination) {
    padding-bottom: 24px;
}
html.dark {
    .set-content,
    .el-radio {
        color: #a4a7b3;
    }
    .top-note {
        color: #a4a7b3;
    }
    .select-tip {
        background-color: #22242e;
        color: #d9d9d9;
    }
}
</style>
