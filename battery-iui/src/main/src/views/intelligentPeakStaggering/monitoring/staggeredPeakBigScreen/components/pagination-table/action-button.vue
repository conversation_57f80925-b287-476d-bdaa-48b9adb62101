<template>
    <el-button
        v-show="buttonVisible(btn, row)"
        type="text"
        size="medium"
        :disabled="disabledHandler(btn, row)"
        @click="clickHandler"
    >
        <span :class="btn.class">{{ btn.text }}</span>
    </el-button>
</template>

<script>
export default {
    props: {
        btn: Object,
        row: Object,
        index: Number,
    },
    methods: {
        buttonVisible(btn, row) {
            if (typeof btn.show === 'undefined') {
                return true;
            }
            if (typeof btn.show === 'function') {
                return btn.show(row);
            }
            return btn.show;
        },
        disabledHandler(btn, row) {
            if (typeof btn.disabled === 'function') {
                return btn.disabled(row);
            }
            return btn.disabled;
        },
        clickHandler() {
            this.btn.handle(this.row, this.index);
        },
    },
};
</script>

<style scoped>
.highlight-color-red {
    color: #e02222;
}
.highlight-color-red:hover {
    color: #ba1621;
}
.el-button.is-disabled .highlight-color-red {
    color: #bfbfbf;
}
html.dark .el-button.is-disabled .highlight-color-red {
    color: #474a59;
}
</style>
