<template>
    <div class="device-status-item">
        <span class="device-status-item__icon-box">
            <!-- eslint-disable-next-line vue/html-self-closing -->
            <img :src="deviceIcon" class="device-status-item__icon" />
        </span>
        <span v-if="!suffixTags.length" class="device-status-item__detail">
            <span class="device-status-item__detail-number" :style="`color: ${numberColor}`">
                {{ number || '--' }}
            </span>
            <span class="device-status-item__detail-tag">
                {{ tag }}
            </span>
        </span>
        <span v-else class="device-status-item__detail">
            <span class="device-status-item__detail-number">
                <span v-for="(color, index) in numberColor" :key="`${color}${index}`" :style="`color: ${color}`">
                    {{ number[index] || '--' }}
                    <span v-show="index !== lastSuffixTagIndex" style="color: #595959">/</span>
                </span>
            </span>
            <span class="device-status-item__detail-tag">
                {{ tag }}
                (
                <span
                    v-for="(suffixTag, index) in suffixTags"
                    :key="`${tag}${suffixTag}`"
                    :style="`color: ${suffixTagColors[index]}`"
                >
                    {{ suffixTag }}
                    <span v-show="index !== lastSuffixTagIndex" style="color: #595959">/</span>
                </span>
                )
            </span>
        </span>
    </div>
</template>

<script>
import deviceIcon from '@/assets/img/icon_peak_device.png';

export default {
    props: {
        number: {
            type: [String, Array],
            default: '0',
        },
        numberColor: {
            type: [String, Array],
            default: '#d0e9ff',
        },
        tag: {
            type: String,
            default: '',
        },
        suffixTags: {
            type: Array,
            default: () => [],
        },
        suffixTagColors: {
            type: Array,
            default: () => ['#76d63e', '#f56c6c'],
        },
    },
    data() {
        return {};
    },
    computed: {
        deviceIcon() {
            return deviceIcon;
        },
        lastSuffixTagIndex() {
            return this.suffixTags.length - 1;
        },
    },
};
</script>

<style scoped>
.device-status-item {
    display: flex;
    align-items: flex-start;
    width: 100%;
    height: 52px;
    max-height: 92px;
}
.device-status-item__icon {
    width: 27px;
    height: 27px;
}
.device-status-item__icon-box {
    box-sizing: border-box;
    width: 33px;
    height: 33px;
    margin-right: 12px;
    padding: 3px;
    background: rgba(25, 147, 255, 0.15);
    border-radius: 50%;
}
.device-status-item__detail {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
.device-status-item__detail-number {
    font-size: 24px;
}
.device-status-item__detail-tag {
    font-size: 12px;
    color: #5f80b4;
}
</style>
