<template>
    <div class="mo-tree">
        <div class="el-row">
            <el-col :span="24">
                <el-input
                    v-model="searchText"
                    class="search-input"
                    maxlength="100"
                    :placeholder="$t('peakSetting.inputName')"
                    autocomplete="on"
                    size="mini"
                    clearable
                    @clear="clearSearch"
                >
                    <template v-slot:prefix>
                        <el-icon class="el-input__icon"><el-icon-search /></el-icon>
                    </template>
                </el-input>
            </el-col>
        </div>
        <hr/>
        <div v-loading="searchLoading" class="elTreeWrap newTreeStyle" :style="treeStle">
            <el-tree
                v-show="searchText.trim() === ''"
                ref="tree"
                :props="defaultProps"
                node-key="id"
                :load="loadNode"
                :lazy="true"
                highlight-current
                :default-expanded-keys="[idsMap.root]"
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender"
                @node-click="handleClick"
            ></el-tree>
            <!-- 搜索树 -->
            <el-tree
                v-show="searchText"
                ref="searchTree"
                :props="defaultProps"
                node-key="id"
                :data="searchTreeData"
                highlight-current
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender_search"
                :default-expand-all="true"
                @node-expand="handleExpand"
                @node-click="handleClick"
            ></el-tree>
            <div
                v-if="searchText.trim() && searchTreeData.length > 0"
                style="text-align: center; padding-top: 20px; padding-bottom: 20px"
            >
                <el-button
                    plain
                    :class="[showMore ? 'hasMore' : 'nomore']"
                    size="mini"
                    :disabled="!showMore"
                    @click="searchMore"
                >
                    {{ showMore ? $t('tipMessage.moreResult') : $t('tipMessage.noMore') }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="jsx">
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import { modelIdsMap, idsMap } from '@/util/constants';
/* Started by AICoder, pid:6814b7c4cdt0dce14f210a08204dfa159236705a */
import { useTreeIcon } from '@/hooks/useTreeIcon.js';
/* Ended by AICoder, pid:6814b7c4cdt0dce14f210a08204dfa159236705a */

export default {
    components: {
        ElIconSearch,
    },
    name: 'DeviceTree',
    props: ['height'],
    /* Started by AICoder, pid:6814b7c4cdt0dce14f210a08204dfa159236705a */
    setup() {
        const { loadNodeIcons, getTreeIconByMoc, getIconColor, getDefaultIconByModel } = useTreeIcon();
        return {
            loadNodeIcons,
            getTreeIconByMoc,
            getIconColor,
            getDefaultIconByModel,
        };
    },
    /* Ended by AICoder, pid:6814b7c4cdt0dce14f210a08204dfa159236705a */
    data() {
        return {
            defaultProps: {
                label: 'name',
                children: 'children',
                isLeaf: 'leaf',
            },
            iconLevelClassName: {
                Root: 'icon-treenode icon-treenode-mo-root',
                RealGroup: 'icon-treenode icon-treenode-mo-realgroup',
                Site: 'icon-treenode icon-treenode-mo-site',
                Building: 'icon-treenode icon-treenode-mo-building',
                Floor: 'icon-treenode icon-treenode-mo-floor',
                DataCenter: 'icon-treenode icon-treenode-mo-datacenter',
                Room: 'icon-treenode icon-treenode-mo-room',
                Mdc: 'icon-treenode icon-treenode-mo-mdc',
                MonitorObject: 'icon-treenode icon-treenode-mo-monitorobject',
                MonitorDevice: 'icon-treenode icon-treenode-mo-monitordevice',
                Warehouse: 'icon-treenode icon-treenode-mo-warehouse',
                More: 'el-icon-more',
            },
            currentId: '',
            pageSize: 100,

            // 搜索树
            searchText: '',
            searchTreeData: [],
            searchPageSize: 100,
            searchPage: 1,
            showMore: true,
            searchLoading: false,
            checkedNodes: [],
            idsMap,
        };
    },
    computed: {
        treeStle() {
            let h = 'auto';
            if (this.height) {
                h = this.height - 79 + 'px';
            }
            let item = {
                height: h,
            };
            return item;
        },
    },
    watch: {
        searchText(val) {
            this.searchTreeData = [];
            let keyWord = val.trim();
            if (!keyWord) {
                this.searchLoading = false;
                return;
            }
            this.searchTree(val);
        },
    },
    created() { 
        this.toNodeShow();
     },
    mounted() {},
    methods: {
        clearSearch() {
            this.searchPage = 1;
        },
        handleClick(d, node) {
            if (node.data.id === '#more') {
                let nodePage = node.data.page;
                let parentId = node.data.parentId;
                let total = node.data.total;
                let parentNode = node.parent;

                // 注意parentNode的处理
                // 注意监控对象树没有根节点, 如果是根（假的）的情况，直接传一个null才正确, 同时树上的data需要设置成[]，然后“根节点”才能正常的append，否则会报错
                if (parentNode.level === 0) {
                    parentNode = null; //
                }

                console.warn('parentNode ==> ', parentNode);

                node.loading = 'true';

                this.getChild(parentId, nodePage, d1 => {
                    d1 = d1.data;
                    node.loading = 'false';
                    d1.forEach(nd => {
                        // append该节点上前，最好判断下，该节点是否已经存在这个节点下
                        let notFound = true;
                        node.parent.childNodes.forEach(n => {
                            if (n.data.id === nd.id) {
                                notFound = false;
                            }
                        });
                        if (notFound) {
                            this.$refs.tree.append(nd, parentNode);
                        }
                    });

                    this.$refs.tree.remove(node);
                    // 没加载完，需要添加“加载更多”
                    if (nodePage + 1 <= Math.ceil(total / this.pageSize)) {
                        this.$refs.tree.append(this.getMoreNode(parentId, nodePage + 1, total), parentNode);
                    }
                });
            } else {
                this.currentId = node.data.id;
                $emit(this, 'nodeClick', node);
                this.$nextTick(() => {
                    let currentNode = this.$refs.tree.getCurrentNode();
                    if (currentNode && currentNode.id != this.currentId) {
                        this.$refs.tree.setCurrentKey(null);
                        this.$refs.tree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.tree.setCurrentKey(this.currentId);
                    }
                    let currentNodeSearch = this.$refs.searchTree.getCurrentNode();
                    if (currentNodeSearch && currentNodeSearch.id != this.currentId) {
                        this.$refs.searchTree.setCurrentKey(null);
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    }
                });
            }
        },
        nodeRender(h, { node, data, store }) {
            return (
                <span class="el-tree-node__label">
                    {/* <!-- Started by AICoder, pid:45c5d04cb8h90af14b3c08b84063f424a8b0054f --> */}
                    <div class="node-name-container">
                        <div style={{ position: 'relative', height: '14px' }}>
                            {this.getTreeIconByMoc(data.moc) ? (
                                <svg-icon
                                    size="14"
                                    base64={this.getTreeIconByMoc(data.moc)}
                                    color={this.getIconColor()}
                                    class="tree-node-icon"
                                />
                            ) : (
                                <svg-icon
                                    name={this.getDefaultIconByModel(data.model)}
                                    color={this.getIconColor()}
                                    size="14"
                                    class="tree-node-icon"
                                />
                            )}
                        </div>
                        {data.name}
                    </div>
                    {/* <!-- Ended by AICoder, pid:45c5d04cb8h90af14b3c08b84063f424a8b0054f --> */}
                </span>
            );
        },
        nodeRender_search(h, { node, data, store }) {
            if (!node.isRealLeaf && data.id !== '#more') {
                node.isLeaf = false;
            }
            if (node.childNodes.length === 0) {
                node.expanded = false;
            }
            return this.nodeRender(h, { node, data, store });
        },
        toNodeShow(){
            this.getAllTree(null,1,res=>{
            }).then(res=>{
                if (res.data) {
                    let allFirstChild = res.data.children
                    let firstIndex = allFirstChild.findIndex(item=>item.authorizationStatus == 2)
                    if(res.data.authorizationStatus == 2 || firstIndex == -1){
                        return
                    }          
                    this.currentId = allFirstChild[firstIndex]?.id
                }
            })
        },
        loadNode(node, resolve) {
            let id = '';
            // 子节点加载
            if (node.data && !Array.isArray(node.data)) {
                id = node.data.id;
            }
            this.getChild(id, 1, d => {
                let data = d.data;
                // 根据返回的数据，判断是否需要加“加载更多”
                if (!this.ischeck && 2 <= Math.ceil(d.total / this.pageSize)) {
                    data.push(this.getMoreNode(id, 2, d.total));
                }
                this.$nextTick(() => {
                    if (this.currentId) {
                        this.$refs.tree.setCurrentKey(this.currentId);
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].id === this.currentId) {
                                let node = this.$refs.tree.getNode(this.currentId);
                                this.handleClick(node.data, node);
                            }
                        }
                    }
                });
                return resolve(data);
            });
        },
        getMoreNode(parentId, page, total) {
            return {
                name: this.$t('button.showMore'),
                id: '#more',
                leaf: true,
                showPopper: false,
                childId: [],
                resourceType: 'More',
                page: page,
                parentId: parentId,
                total: total,
            };
        },
        /* Started by AICoder, pid:x9eac77072ie634145660aeb006c3c250e206cae */
        getAllTree(id, pageNo, callback) {
            return new Promise((resolve, reject) => {
                HTTP.request('newGetAllTree', {
                    method: 'post',
                    data: {
                        focusNodeId: id,
                        pageNo,
                        pageSize: this.pageSize, 
                        modelIds: [modelIdsMap.group, modelIdsMap.field],
                    },
                    complete: async data => {
                        if (data.code === 0) {
                            await this.loadNodeIcons([data.data] || []);
                            callback && callback(data);
                            resolve(data)
                        }
                    },
                });
            });
        },
        /* Ended by AICoder, pid:x9eac77072ie634145660aeb006c3c250e206cae */
        getChild(id, pageNo, callback) {
            HTTP.request('newGetLogicGroupTree', {
                method: 'post',
                data: {
                    modelIds: [modelIdsMap.field, modelIdsMap.group],
                    parentId: id,
                    pageNo: pageNo,
                    pageSize: this.pageSize,
                },
                complete: async data => {
                    if (data.code === 0) {
                        await this.loadNodeIcons(data.data || []);
                        callback && callback(data);
                    }
                },
            });
        },
        searchMore() {
            this.searchLoading = true;
            HTTP.request('newSearchLogicGroupTree', {
                method: 'post',
                data: {
                    modelIds: [modelIdsMap.field, modelIdsMap.group],
                    name: this.searchText,
                    pageNo: this.searchPage,
                    pageSize: this.searchPageSize,
                },
                complete: data => {
                    this.searchLoading = false;
                    if (data.code === 0) {
                        this.searchPage++;
                        data.data = data.data || [];
                        if (data.data.length) {
                            this.updateTreeData(data.data, this.searchTreeData);
                            // 如果返回的数据条数小于100条，则认为是最后一页
                            if (data.data.length < this.searchPageSize) {
                                this.showMore = false;
                            }
                            this.$nextTick(() => {
                                if (this.currentId) {
                                    this.$refs.searchTree.setCurrentKey(this.currentId);
                                }
                            });
                        } else {
                            this.showMore = false;
                        }
                    }
                },
                error: () => {
                    this.searchLoading = false;
                },
            });
        },
        searchTree(val) {
            this.searchLoading = true;
            HTTP.request('newSearchLogicGroupTree', {
                method: 'post',
                data: {
                    modelIds: [modelIdsMap.field, modelIdsMap.group],
                    name: val,
                    pageNo: 1,
                    pageSize: this.searchPageSize,
                },
                complete: async data => {
                    let treeData = [];
                    if (val === this.searchText) {
                        this.searchLoading = false;
                        if (data.code === 0) {
                            this.searchPage = 2;
                            data.data = data.data || [];
                            if (data.data.length) {
                                await this.updateTreeData(data.data, treeData);
                                this.searchTreeData = treeData;
                                this.showMore = true;
                                if (data.data.length < this.searchPageSize) {
                                    this.showMore = false;
                                }
                                this.$nextTick(() => {
                                    if (this.currentId) {
                                        this.$refs.searchTree.setCurrentKey(this.currentId);
                                    }
                                });
                            } else {
                                this.searchTreeData = treeData;
                                this.showMore = false;
                            }
                        }
                    }
                },
                error: () => {
                    this.searchLoading = false;
                },
            });
        },
        updateTreeData(responseData, treeData) {
            if (responseData.length) {
                responseData.forEach(async path => {
                    let mountedNode = null;
                    path.forEach((node, i) => {
                        if (this.ischeck && node.model !== modelIdsMap.collector) {
                            // node.disabled = true;
                        }
                        if (i === 0) {
                            // 预先设置当前节点为挂载点
                            let hasOne = false;
                            for (let j = 0, max = treeData.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === treeData[j].id) {
                                    mountedNode = treeData[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = [];
                                treeData.push(node);
                                mountedNode = node;
                            }
                        } else {
                            let hasOne = false;
                            for (let j = 0, max = mountedNode.children.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === mountedNode.children[j].id) {
                                    mountedNode = mountedNode.children[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = [];
                                mountedNode.children.push(node);
                                mountedNode = node;
                            }
                        }
                    });
                    await this.loadNodeIcons(path || []);
                });
            }
        },
        handleExpand(d, node) {
            if (node.hasExpanded) {
                return;
            }
            node.hasExpanded = true;
            if (node.childNodes.length === 0) {
                let id = (node.data && node.data.id) || '';
                node.expanded = false;
                node.loading = true;
                this.getChild(id, 1, d => {
                    let nodeData = d.data;
                    for (let i = 0; i < nodeData.length; i++) {
                        this.$refs.searchTree.append(nodeData[i], node);
                    }
                    if (nodeData.length === 0) {
                        node.isLeaf = true;
                        node.isRealLeaf = true;
                    }
                    node.expanded = true;
                    node.loading = false;
                    // 根据返回的数据，判断是否需要加“加载更多”
                    if (!this.ischeck && 2 <= Math.ceil(d.total / this.pageSize)) {
                        this.$refs.searchTree.append(this.getMoreNode(id, 2, d.total), node);
                    }
                    this.$nextTick(() => {
                        if (this.currentId) {
                            this.$refs.searchTree.setCurrentKey(this.currentId);
                        }
                    });
                });
            }
        },
    },
    emits: ['nodeClick'],
};
</script>

<style lang="scss" scoped>
:deep(.icon-treenode) {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-size: 14px 14px;
    white-space: nowrap;
    letter-spacing: -1em;
    text-indent: -99em;
    color: transparent;
}
:deep(.icon-treenode):before {
    content: '\3000';
}
.elTreeWrap {
    overflow: auto;
}

/* 横向滚动条 */
.elTreeWrap :deep(.el-tree) > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.tree-node {
    padding-right: 140px;
    position: relative;
}
.elTreeWrap :deep(.el-tree-node__content) > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
button.nomore {
    border: 0 none;
}
.nodeEllipsis {
    display: inline-block;
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}
.el-tree-node__content .hover-to-show {
    position: absolute;
}
.mo-tree hr {
    margin: 16px 0 10px;
    border: none;
    height: 1px;
}

/* Started by AICoder, pid:8561ef6adb62e0d1487f0acda0b6e80bebf61635 */
:deep(.node-name-container) {
    display: flex;
    align-items: center;
    width: 100%;
}

/* Ended by AICoder, pid:8561ef6adb62e0d1487f0acda0b6e80bebf61635 */

/* Started by AICoder, pid:a143dhb492w3dd8142c00a65d073d606f1443459 */
:deep(.tree-node-icon) {
    display: inline-block;
    margin-right: 4px;
}

/* Ended by AICoder, pid:a143dhb492w3dd8142c00a65d073d606f1443459 */
</style>
