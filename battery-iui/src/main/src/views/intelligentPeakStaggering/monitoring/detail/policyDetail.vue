<!-- /* Started by AICoder, pid:p3accgdda3522bf143030b862272c51b25913f4b */ -->
<template>
    <div v-loading="pageLoading" class="strategy-detail">
        <div class="strategy-detail-title">
            <div class="uedm-title">{{ $t('peakSetting.policy') }}</div>
            <div
                v-if="
                    (rowData.deviceTypeName && rowData.deviceTypeName.id === 'SNMP') ||
                    rowData.deviceType === 'SNMP' ||
                    (rowData.deviceTypeName && rowData.deviceTypeName.id === 'BCUA') ||
                    rowData.deviceType === 'BCUA'
                "
            >
                <div v-if="deviceTactics[0] && deviceTactics[0].id" class="tableBar">
                    <div class="filterButtons">
                        <!-- 导出按钮 -->
                        <span class="filterItem">
                            <el-tooltip class="item" effect="dark" :content="$t('button.export')" placement="bottom">
                                <span class="icon-button plx-ico-export-16" @click.stop="handleDownload()"></span>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <PolicyCSU5
            ref="Period"
            page-type="detail"
            :device-type="rowData.deviceTypeName && rowData.deviceTypeName.id"
            :period-data="periodData"
            :weekend-flag-value="weekendFlag"
        ></PolicyCSU5>
        <div class="uedm-title">{{ $t('peakSetting.title.holiday') }}</div>
        <HolidayCSU5 ref="Holiday" :holiday-data="holidayData"></HolidayCSU5>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import axios from 'axios';
import PolicyCSU5 from '@/views/intelligentPeakStaggering/strategyTemplate/PolicyCSU5.vue';
import HolidayCSU5 from '@/views/intelligentPeakStaggering/strategyTemplate/HolidayCSU5Detail.vue';
export default {
    components: {
        PolicyCSU5,
        HolidayCSU5,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        rowData: {
            type: Object,
            default: () => ({}),
        },
        deviceTactics: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            pageLoading: false,
            weekendFlag: false,
            loading: false,
            periodData: [],
            holidayData: [],
            periodMode: '',
        };
    },
    computed: {},
    mounted() {
        this.queryStrategyCSU5();
    },
    methods: {
        queryStrategyCSU5() {
            this.pageLoading = true;
            HTTP.request('queryStrategyCSU5', {
                method: 'post',
                urlParam: {
                    deviceId: (this.rowData.deviceName && this.rowData.deviceName.id) || '',
                },
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data || {};
                        this.weekendFlag = result.weekendFlag;
                        this.periodData = result.detail || [];
                        this.holidayData = result.holiday || [];
                        this.periodMode = result.mode;
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.pageLoading = false;
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                    this.pageLoading = false;
                },
            });
        },
        isImag(name) {
            let tag = false;
            let nameSuffix = '';
            let imgType = ['pdf', 'jpg', 'png', 'gif', 'bmp'];
            if (name.indexOf('.') >= 0) {
                let names = name.split('.');
                nameSuffix = names[names.length - 1];
            }
            if (nameSuffix != '' && imgType.indexOf(nameSuffix.toLowerCase()) >= 0) {
                tag = true;
            }
            return tag;
        },
        handleDownload() {
            const DOWNLOAD_URL = '/api/battery-manager/v1/peak-shift-file/device/universal-download';
            const forgerydefense = window.forgerydefense || '';
            /* Started by AICoder, pid:o6289k707742e151445a0860b05b481d6f81c389 */
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                params: {
                    fileId: this.deviceTactics[0].id,
                },
                headers: {
                    'Content-Type': 'application/octet-stream;charset=utf-8',
                    forgerydefense: forgerydefense,
                },
            };
            /* Ended by AICoder, pid:o6289k707742e151445a0860b05b481d6f81c389 */
            axios.get(url, config).then(res => {
                // 导出错误，文件数据类型不对
                if (res.data.type === 'application/json') {
                    let reader = new FileReader();
                    reader.onload = e => {
                        let result = JSON.parse(e.target.result);
                        if (result && result.code !== 0) {
                            this.$message.error(result.message);
                        }
                    };
                    reader.readAsText(res.data, ['utf-8']);
                } else {
                    // 导出成功，返回数据流
                    let blob = new Blob([res.data]);
                    let url = window.URL.createObjectURL(blob); // 创建下载的链接
                    let fileName = this.deviceTactics[0].name;
                    if (this.isImag(this.deviceTactics[0].name)) {
                        // 如果是图片类型的显示预览
                        this.dialogVisible = true;
                        this.dialogImageUrl = url;
                        this.imgName = fileName;
                    } else {
                        // 非图片直接下载
                        let link = document.createElement('a');
                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.strategy-detail-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.link-span {
    display: inline-flex;
}
:deep(.detail) .switch-box_label {
    position: relative;
    top: 15px;
}
.uedm-title {
    margin-top: 16px;
}
</style>
<!-- /* Ended by AICoder, pid:p3accgdda3522bf143030b862272c51b25913f4b */ -->