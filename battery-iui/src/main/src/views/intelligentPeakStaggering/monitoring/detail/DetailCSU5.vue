<template>
    <div v-loading="pageLoading" class="strategy-detail">
        <div>
            <el-form
                ref="templateForm"
                :model="templateForm"
                :inline="true"
                :class="{ en: $i18n.locale !== 'zh-CN' }"
                :label-width="$i18n.locale == 'zh-CN' ? '120px' : '160px'"
            >
                <el-row>
                    <el-col :span="8">
                        <el-form-item :label="$t('common.name')" class="ellipsis-content">
                            <span :title="templateForm.name">{{ templateForm.name || '--' }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('battery.fields.deviceType')" class="item-content">
                            {{ deviceTypeMap[templateForm.deviceType] || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('battery.fields.source')" class="item-content">
                            {{ sourceMap[templateForm.source] || '--' }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item :label="$t('battery.fields.electricityPricePeriod')" class="ellipsis-content">
                            <span :title="templateForm.seasonStrategyName">
                                {{ templateForm.seasonStrategyName || '--' }}
                            </span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('table.creator')" class="item-content">
                            {{ templateForm.userCreate || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('table.gmtCreated')" class="item-content">
                            {{ templateForm.gmtCreate || '--' }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item :label="$t('table.modifiedBy')" class="item-content">
                            {{ templateForm.userModified || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('table.gmtModified')" class="item-content">
                            {{ templateForm.gmtModified || '--' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('common.version')">
                            {{ templateForm.version || '--' }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item :label="$t('battery.fields.remarks')" class="ellipsis-content">
                            <span :title="templateForm.remark">{{ templateForm.remark || '--' }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane :label="$t('peakSetting.policy')" name="toStrategy">
                <PolicyCSU5
                    ref="Period"
                    page-type="detail"
                    :period-data="periodData"
                    :weekend-flag-value="templateForm.weekendFlag"
                ></PolicyCSU5>
            </el-tab-pane>
            <el-tab-pane :label="$t('peakSetting.title.holiday')" name="toHoliday">
                <HolidayCSU5 ref="Holiday" :holiday-data="holidayData"></HolidayCSU5>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import PolicyCSU5 from '@/views/intelligentPeakStaggering/strategyTemplate/PolicyCSU5.vue';
import HolidayCSU5 from '@/views/intelligentPeakStaggering/strategyTemplate/HolidayCSU5Detail.vue';
export default {
    components: {
        PolicyCSU5,
        HolidayCSU5,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        rowData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        this.DEVICE_BCUA = 'BCUA';
        this.DEVICE_CSU5 = 'CSU5';
        this.SOURCE_WEB = 'web';
        this.SOURCE_EXCEL = 'excel';
        return {
            pageLoading: false,
            activeName: 'toStrategy',
            templateForm: {
                id: '',
                name: '',
                deviceType: '',
                source: '',
                seasonStrategyName: '',
                fileName: '',
                userCreate: '',
                userModified: '',
                remark: '',
                gmtCreate: '',
                gmtModified: '',
                version: '',
                fileId: '',
                seasonStrategyId: '',
                weekendFlag: false,
            },
            loading: false,
            periodData: [],
            holidayData: [],
            queryReturn: false,
            periodMode: '',
        };
    },
    computed: {
        showSettingDetail() {
            return this.templateForm.fileId || this.templateForm.seasonStrategyId;
        },
        deviceTypeMap() {
            let map = {};
            this.$store.getters.getPeakDeviceTypes.forEach(item => {
                map[item.id] = item.name;
            });
            return map;
        },
        sourceMap() {
            return {
                [this.SOURCE_WEB]: this.$t('battery.fields.webConfig'),
                [this.SOURCE_EXCEL]: this.$t('battery.fields.excelImport'),
            };
        },
    },
    mounted() {
        this.queryTemplateDetail();
    },
    methods: {
        queryTemplateDetail() {
            this.pageLoading = true;
            HTTP.request('getStrategyTemplateHistoryDetail', {
                method: 'get',
                urlParam: {
                    taskId: this.rowData.peakStrategyCsu5Vo.taskId,
                },
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data.templateDetail || {};
                        this.templateForm = {
                            id: result.id,
                            name: result.name,
                            deviceType: result.deviceType,
                            source: result.source,
                            seasonStrategyName: result.seasonStrategyName,
                            fileName: result.fileName,
                            userCreate: result.userCreate,
                            userModified: result.userModified,
                            remark: result.remark,
                            gmtCreate: result.gmtCreate,
                            gmtModified: result.gmtModified,
                            version: result.version,
                            fileId: result.fileId,
                            seasonStrategyId: result.seasonStrategyId,
                            weekendFlag: result.weekendFlag,
                        };
                        this.periodData = result.detail;
                        this.holidayData = result.holiday;
                        this.periodMode = result.mode;
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.pageLoading = false;
                },
                error: () => {
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                    this.pageLoading = false;
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-form) {
    margin-bottom: 16px;
}
:deep(.el-form-item__label) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
:deep(.el-table__body-wrapper) > table {
    border-top: unset;
}

.el-form .select-policy-config {
    .el-form-item__content .el-input {
        width: 240px;
    }
    .el-form-item__content .el-button {
        padding: 9px 8px;
    }
}

:deep(.el-form-item).ellipsis-content {
    width: 100%;
    .el-form-item__content {
        width: calc(100% - 132px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        .el-input__count {
            line-height: 24px;
        }
    }
}
:deep(.el-form).en .el-form-item.ellipsis-content .el-form-item__content {
    width: calc(100% - 172px);
}
.strategy-detail {
    .el-link {
        color: #1993ff;
    }
}
:deep(.detail) .switch-box_label {
    position: relative;
    top: 15px;
}
:deep(.el-tabs__header) {
    width: 100% !important;
}
</style>
