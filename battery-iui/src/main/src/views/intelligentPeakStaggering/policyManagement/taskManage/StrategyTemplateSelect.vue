<template>
    <el-dialog
        v-model="dialogVisible"
        :title="
            $t('battery.fields.selectPeakShiftingStrategyTemplate', {
                type: deviceName,
            })
        "
        width="1340px"
        top="8vh"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @opened="opened"
    >
        <div ref="formWrapRef" class="uedm-query-form col3">
            <el-form
                ref="form"
                class="policy-template-form uedm-form-datetime"
                :inline="true"
                :model="queryForm"
                :label-width="$i18n.locale == 'zh-CN' ? '100px' : '120px'"
                label-position="right"
            >
                <el-form-item :label="$t('common.templateName')">
                    <el-input
                        v-model="queryForm.name"
                        :placeholder="$t('placeholder.input')"
                        clearable
                        maxlength="100"
                    ></el-input>
                </el-form-item>
                <el-form-item :label="$t('table.creator')">
                    <el-input
                        v-model="queryForm.userCreate"
                        :placeholder="$t('placeholder.input')"
                        clearable
                        maxlength="100"
                    ></el-input>
                </el-form-item>
                <el-form-item :label="$t('table.gmtCreated')">
                    <el-date-picker
                        v-model="queryForm.gmtCreated"
                        type="datetimerange"
                        :start-placeholder="$t('datetimePicker.startTime')"
                        :end-placeholder="$t('datetimePicker.endTime')"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                        @change="effTimeChange"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item :label="$t('table.modifiedBy')">
                    <el-input
                        v-model="queryForm.userModified"
                        :placeholder="$t('placeholder.input')"
                        clearable
                        maxlength="100"
                    ></el-input>
                </el-form-item>
                <el-form-item :label="$t('table.gmtModified')">
                    <el-date-picker
                        v-model="queryForm.gmtModified"
                        type="datetimerange"
                        :start-placeholder="$t('datetimePicker.startTime')"
                        :end-placeholder="$t('datetimePicker.endTime')"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                        @change="expTimeChange"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item class="uedm-query-form__btn">
                    <el-button type="primary" @click="query">
                        {{ $t('button.query') }}
                    </el-button>
                    <el-button @click="reset">
                        {{ $t('button.reset') }}
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table
            v-loading="loading"
            :row-key="getRowKeys"
            :max-height="520"
            :data="tableData"
            stripe
            style="width: 100%"
        >
            <el-table-column width="55">
                <template v-slot="scope">
                    <el-radio v-model="selectedRow" :label="scope.row" :disabled="scope.row.status === 2">
                        {{ '' }}
                    </el-radio>
                </template>
            </el-table-column>
            <el-table-column :label="$t('common.templateName')" min-width="150px">
                <template v-slot="scope">
                    {{ scope.row.name || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('common.version')" min-width="100px">
                <template v-slot="scope">
                    {{ scope.row.version || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('table.creator')" min-width="100px" show-overflow-tooltip>
                <template v-slot="scope">
                    {{ scope.row.userCreate || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('table.gmtCreated')" min-width="140px" show-overflow-tooltip>
                <template v-slot="scope">
                    {{ scope.row.gmtCreate || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('table.modifiedBy')" min-width="100px" show-overflow-tooltip>
                <template v-slot="scope">
                    {{ scope.row.userModified || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('table.gmtModified')" min-width="140px" show-overflow-tooltip>
                <template v-slot="scope">
                    {{ scope.row.gmtModified || '--' }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('battery.fields.remarks')" min-width="150px" show-overflow-tooltip>
                <template v-slot="scope">
                    {{ scope.row.remark || '--' }}
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 30]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>
        <!-- <template #footer> -->
        <div class="bottomBar">
            <el-button type="primary" @click="handleConfirm">
                {{ $t('button.confirm') }}
            </el-button>
            <el-button @click="handleCancel">
                {{ $t('button.cancel') }}
            </el-button>
        </div>
        <!-- </template> -->
    </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import { getFormCol } from '@uedm/uedm-ui/src/util/calculation.js';
import HTTP from '@/util/httpService';
export default {
    data() {
        return {
            queryForm: {
                name: '',
                userCreate: '',
                gmtCreated: [],
                userModified: '',
                gmtModified: [],
            },
            // 查询时实际的参数
            queryParameter: {},
            tableData: [],
            pageNo: 1,
            pageSize: 5,
            total: 0,
            selectedRow: '',
            loading: false,
        };
    },
    name: 'ChooseAssetMulti',
    components: {},
    props: {
        visible: Boolean,
        selectedId: {
            type: String,
            default: '',
        },
        deviceType: {
            type: String,
            default: '',
        },
        deviceName: {
            type: String,
            default: '',
        },
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                return $emit(this, 'update:visible', val);
            },
        },
        periodModeMap() {
            return {
                0: this.$t('peakSetting.fields.day'),
                1: this.$t('peakSetting.fields.week'),
                2: this.$t('peakSetting.fields.month'),
            };
        },
        statusMap() {
            return {
                0: this.$t('peakSetting.fields.toBeEffective'),
                1: this.$t('peakSetting.fields.takingEffect'),
                2: this.$t('peakSetting.fields.finished'),
            };
        },
    },
    mounted() {
        this.$nextTick(() => {
            getFormCol(this.$refs.formWrapRef);
        });
    },
    methods: {
        handleSizeChange() {
            this.pageNo = 1;
            this.queryList();
        },
        handleCurrentChange() {
            this.queryList();
        },
        handleConfirm() {
            $emit(this, 'change', this.selectedRow);
            this.tableData = [];
            this.selectedRow = '';
            this.dialogVisible = false;
        },
        handleCancel() {
            this.dialogVisible = false;
        },
        opened() {
            this.query();
        },
        reset() {
            this.queryForm = {
                name: '',
                userCreate: '',
                gmtCreated: [],
                userModified: '',
                gmtModified: [],
            };
            this.pageNo = 1;
            this.pageSize = 5;
            this.tableData = [];
            this.total = 0;
            this.selectedRow = '';
            this.query();
        },
        updateQueryParameter() {
            this.queryParameter = this.assemblingdata();
        },
        query() {
            this.pageNo = 1;
            this.updateQueryParameter();
            this.queryList();
        },
        queryList() {
            this.loading = true;
            this.tableData = [];
            let data = this.queryParameter;
            data.pageSize = this.pageSize;
            data.pageNo = this.pageNo;
            HTTP.request('getStrategyTemplateList', {
                method: 'post',
                data,
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data || [];
                        this.tableData = result;
                        this.tableData.some(item => {
                            if (item.id === this.selectedId) {
                                this.selectedRow = item;
                            }
                            return item.id === this.selectedId;
                        });
                        this.total = resp.total;
                    } else {
                        this.total = 0;
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.loading = false;
                },
                error: () => {
                    this.total = 0;
                    this.loading = false;
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        assemblingdata() {
            let [gmtCreateBegin, gmtCreateEnd] = this.splitTimeRange(this.queryForm.gmtCreated);
            let [gmtModifiedBegin, gmtModifiedEnd] = this.splitTimeRange(this.queryForm.gmtModified);
            let param = {
                ...this.queryForm,
                gmtCreateBegin,
                gmtCreateEnd,
                gmtModifiedBegin,
                gmtModifiedEnd,
                deviceType: this.deviceType,
                pageSize: this.pageSize,
                pageNo: this.pageNo,
            };
            delete param.gmtCreated;
            delete param.gmtModified;
            return param;
        },
        splitTimeRange(data) {
            let start = '';
            let end = '';
            if (Array.isArray(data) && data.length === 2) {
                start = data[0];
                end = data[1];
            }
            return [start, end];
        },
        // 复选框相关
        getRowKeys(row) {
            return row.id;
        },
        effTimeChange(value) {
            if (value === null) {
                this.$nextTick(() => {
                    this.queryForm.effectiveTime = [];
                });
            }
        },
        expTimeChange(value) {
            if (value === null) {
                this.$nextTick(() => {
                    this.queryForm.expirationTime = [];
                });
            }
        },
    },
    emits: ['update:visible', 'change'],
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
    width: 350px;
    .el-input {
        width: 350px;
    }
}
</style>
