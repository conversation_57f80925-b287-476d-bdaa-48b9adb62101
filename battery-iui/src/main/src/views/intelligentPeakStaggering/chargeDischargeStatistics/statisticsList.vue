<template>
    <div class="statistics-list">
        <div class="uedm-navigation" style="position: relative">
            <ul class="uedm-breadcrumbs title">
                <li>
                    {{ $t('battery.title.report') }}
                </li>
            </ul>
        </div>
        <div class="uedm-space">
            <div ref="formWrapRef" class="search-form uedm-content-area uedm-query-form col4">
                <el-form
                    ref="form"
                    :model="queryForm"
                    :inline="true"
                    :rules="formRules"
                    :label-width="$i18n.locale === 'en-US' ? '160px' : '78px'"
                >
                    <el-form-item :label="$t('battery.fields.position')">
                        <position-tree ref="tree" :popover-width="280" @nodeChange="nodeChange"></position-tree>
                    </el-form-item>
                    <el-form-item :label="$t('battery.fields.positionGranularity')">
                        <el-select
                            v-model="queryForm.positionGran"
                            :placeholder="$t('placeholder.select')"
                            clearable
                            @focus="hiddenDatePick"
                        >
                            <el-option
                                v-for="item in positionGranOptions"
                                :key="`Level-${item.id}_Subnet`"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('battery.fields.timeGranularity')">
                        <el-select v-model="queryForm.timeGran" @focus="hiddenDatePick">
                            <el-option :label="$t('timeRange.day')" value="day"></el-option>
                            <el-option :label="$t('timeRange.month')" value="month"></el-option>
                            <el-option :label="$t('timeRange.year')" value="year"></el-option>
                            <el-option :label="$t('timeRange.all')" value="all"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('solarPower.dateScope')" prop="rangeTime">
                        <div v-show="queryForm.timeGran !== 'year'" style="width:100%">
                            <el-date-picker
                                :key="dateKey + rangeTimeType"
                                ref="rangeTimeRef"
                                v-model="queryForm.rangeTime"
                                :shortcuts="pickerOptions && pickerOptions.shortcuts"
                                :disabled-date="pickerOptions && pickerOptions.disabledDate"
                                :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                                :type="rangeTimeType"
                                range-separator="-"
                                :start-placeholder="$t('datetimePicker.startDate')"
                                :end-placeholder="$t('datetimePicker.endDate')"
                                :format="rangeValueFormat"
                                :value-format="rangeValueFormat"
                            ></el-date-picker>
                        </div>
                        <year-range-select
                            v-show="queryForm.timeGran === 'year'"
                            ref="yearRangeSelect"
                            v-model:value="queryForm.rangeTime"
                        ></year-range-select>
                    </el-form-item>
                    <el-form-item :label="$t('battery.fields.peakShiftingScenario')">
                        <el-select
                            v-model="queryForm.deviceType"
                            :placeholder="$t('placeholder.select')"
                            clearable
                            @focus="hiddenDatePick"
                        >
                            <el-option
                                v-for="item in peakDeviceTypes"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('battery.fields.peakShiftingStrategy')">
                        <el-select v-model="queryForm.peakStrategy" @focus="hiddenDatePick">
                            <el-option :label="$t('common.enabled')" :value="'0'"></el-option>
                            <el-option :label="$t('common.disabledDiff')" :value="'1'"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="uedm-query-form__btn">
                        <el-button type="primary" @click="query()">
                            {{ $t('button.search') }}
                        </el-button>
                        <el-button @click="resetQuery()">
                            {{ $t('button.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="uedm-content-area" style="margin-top: 1px">
                <div class="tableBar">
                    <div class="filterButtons">
                        <!-- 导出按钮 -->
                        <span class="filterItem">
                            <el-tooltip class="item" effect="dark" :content="$t('button.export')" placement="bottom">
                                <span class="icon-button plx-ico-export-16" @click.stop="handleExport()"></span>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
                <el-table
                    ref="MonitoringHistoryTable"
                    v-loading="loading"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    border
                >
                    <template v-for="(header, key, idx) in tableHeaders">
                        <el-table-column
                            v-if="!(header instanceof Object)"
                            :key="key + idx"
                            :label="header"
                            :show-overflow-tooltip="true"
                            :prop="key"
                            min-width="155px"
                        >
                            <template v-slot="scope">
                                {{ cellValueFormatter(scope.row[key]) }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            :key="'object' + key + idx"
                            :label="header.name"
                            :show-overflow-tooltip="true"
                            :min-width="getSpecialTextWidth(header.name, true)"
                            header-align="center"
                        >
                            <template v-for="(childColValue, childKey) in header">
                                <el-table-column
                                    v-if="childKey != 'name'"
                                    :key="childKey"
                                    :show-overflow-tooltip="true"
                                    :label="childColValue"
                                    :prop="childKey"
                                    :min-width="getSpecialTextWidth(childColValue, false, header)"
                                >
                                    <template #default="scope">
                                        {{ cellValueFormatter(scope.row[childKey]) }}
                                    </template>
                                </el-table-column>
                            </template>
                            <el-table-column
                                v-if="Object.keys(header).length"
                                :label="$t('table.operation')"
                                min-width="110px"
                            >
                                <template #default="scope">
                                    <el-link
                                        v-if="key !== 'grain'"
                                        type="primary"
                                        :underline="false"
                                        @click="toDetail(scope.row, key)"
                                    >
                                        {{ $t('button.detail') }}
                                    </el-link>
                                    <el-link type="primary" :underline="false" @click="toTrend(scope.row, key)">
                                        {{ $t('common.trend') }}
                                    </el-link>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </el-table>
                <el-pagination
                    v-model:current-page="pageNo"
                    v-model:page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-sizes="[5, 10, 20, 30]"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import { $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import axios from 'axios';
import dayjs from 'dayjs';
import { getFormCol } from '@uedm/uedm-ui/src/util/calculation.js';
import { idsMap } from '@/util/constants';
import PositionTree from './positionMultiSelect.vue';
import YearRangeSelect from './YearRangeSelect.vue';

const DATE_RANGE_TYPE = {
    day: 'daterange',
    month: 'monthrange',
    year: 'daterange',
    all: 'daterange',
};
const RANGE_FORMAT = {
    day: 'YYYY-MM-DD',
    month: 'YYYY-MM',
    year: 'YYYY',
    all: 'YYYY-MM-DD',
};
const TIME_PERIOD_MAP = {
    lowTime: 0,
    commonTime: 1,
    peakTime: 2,
    maxPeakTime: 3,
};
export default {
    components: {
        PositionTree,
        'year-range-select': YearRangeSelect,
    },
    data() {
        return {
            queryForm: {
                location: '',
                deviceType: '',
                positionGran: '',
                timeGran: 'day',
                rangeTime: '',
                peakStrategy: '0',
            },
            cacheQueryCondition: {},
            pageNo: 1, // 当前页码
            pageSize: 10, // 每页显示记录数
            total: 0, // 当前页总记录数
            queryParameter: {}, // 列表查询条件参数
            loading: false,
            tableData: [],
            tableHeaders: {},
            positionGranOptions: [],
            startYear: '',
            endYear: '',
            dateKey: 1, // 时间控件的唯一值，每次改变粒度之后+1
        };
    },
    computed: {
        rangeTimeType() {
            return DATE_RANGE_TYPE[this.queryForm.timeGran];
        },
        rangeValueFormat() {
            return RANGE_FORMAT[this.queryForm.timeGran];
        },
        pickerOptions: function () {
            let _minTime = null;
            let _maxTime = null;
            let options = {
                disabledDate: time => {
                    let excludeNowStamp = Date.now();
                    // if (this.queryForm.timeGran === 'month') {
                    //     let { lastmonth } = this.getLimitedTime();
                    //     excludeNowStamp = new Date(lastmonth).getTime();
                    // }
                    if (_minTime && _maxTime) {
                        return (
                            time.getTime() < _minTime || time.getTime() > _maxTime || time.getTime() > excludeNowStamp
                        );
                    } else if (!_minTime && !_maxTime) {
                        return time.getTime() > excludeNowStamp;
                    }
                },
                onPick: time => {
                    // 只选择了一个时间
                    if (!time.maxDate) {
                        let gran = this.queryForm.timeGran;
                        if (gran === 'day' || gran === 'all') {
                            let range = 29;
                            let timeRange = range * 24 * 60 * 60 * 1000;
                            _minTime = time.minDate.getTime() - timeRange; // 最小时间
                            _maxTime = time.minDate.getTime() + timeRange; // 最大时间
                        } else if (this.queryForm.timeGran === 'month') {
                            let { startM, endM } = this.getLimitedTime(time.minDate);
                            _minTime = new Date(startM).getTime();
                            _maxTime = new Date(endM).getTime();
                        }
                    } else {
                        _minTime = null;
                        _maxTime = null;
                    }
                },
            };
            return options;
        },
        formRules: function () {
            let rules = {
                rangeTime: [
                    {
                        required: true,
                        message: this.$t('rules.selectWarning', {
                            fieldName: this.$t('solarPower.dateScope'),
                        }),
                        trigger: 'change',
                    },
                ],
            };
            return rules;
        },
        peakDeviceTypes() {
            return this.$store.getters.getPeakDeviceTypes;
        },
    },
    watch: {
        'queryForm.timeGran': {
            deep: true,
            handler: function () {
                this.dateKey++;
                this.initTimerange();
            },
        },
    },
    created() {
        this.querySubnetLevel([{ id: idsMap.root }]);
    },
    mounted() {
        this.initTimerange();
        this.query();
        setTimeout(() => {
            getFormCol(this.$refs.formWrapRef);
        }, 0);
    },
    methods: {
        hiddenDatePick() {
            // 时间控件选择框打开后点击其他下拉框不会关闭，单独处理
            if (this.$refs.rangeTimeRef) {
                this.$refs.rangeTimeRef.pickerVisible = false;
            }
        },
        getLimitedTime(minDate) {
            let startM = dayjs(minDate).subtract(11, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss');
            let endM = dayjs(minDate).add(11, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss');
            let lastmonth = dayjs(minDate).subtract(1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss');
            return { startM, endM, lastmonth };
        },
        cellValueFormatter(data) {
            let result = '--';
            if (data || data === 0) {
                result = data;
            }
            return result;
        },
        /* Started by AICoder, pid:a8f61g9f7fh79c8141710ab8701cd406c3a5f2c2 */
        nodeChange(data) {
            this.queryForm.positionGran = '';
            const ids = data && data.length > 0 ? data : [{ id: idsMap.root }];
            this.querySubnetLevel(ids);
        },
        /* Ended by AICoder, pid:a8f61g9f7fh79c8141710ab8701cd406c3a5f2c2 */
        initTimerange() {
            let start = '';
            let end = '';
            let gran = this.queryForm.timeGran;
            if (gran === 'day' || gran === 'all') {
                start = dayjs().subtract(6, 'day').format('YYYY-MM-DD');
                end = dayjs().subtract(0, 'day').format('YYYY-MM-DD');
            } else if (gran === 'month') {
                start = dayjs().subtract(5, 'month').format('YYYY-MM');
                end = dayjs().subtract(0, 'month').format('YYYY-MM');
            } else if (gran === 'year') {
                start = dayjs().subtract(2, 'year').format('YYYY');
                end = dayjs().subtract(0, 'year').format('YYYY');
                this.$refs.yearRangeSelect.startYear = start;
                this.$refs.yearRangeSelect.endYear = end;
            }
            this.queryForm.rangeTime = [start, end];
        },
        query() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.pageNo = 1;
                    let startTime = this.queryForm.rangeTime ? this.queryForm.rangeTime[0] : '';
                    let endTime = this.queryForm.rangeTime ? this.queryForm.rangeTime[1] : '';
                    let positionNodes = this.$refs.tree.selectedNodes || [];
                    let position = [];
                    positionNodes.forEach(node => {
                        position.push(node.idPath);
                    });
                    this.cacheQueryCondition = {
                        position,
                        positionGran: this.queryForm.positionGran,
                        startTime,
                        endTime,
                        deviceType: this.queryForm.deviceType || '',
                        timeGran: this.queryForm.timeGran,
                        peakStrategy: this.queryForm.peakStrategy,
                    };
                    this.selectBy();
                }
            });
        },
        resetQuery() {
            this.queryForm = {
                location: '',
                positionGran: '',
                timeGran: 'day',
                rangeTime: '',
                peakStrategy: '0',
                deviceType: '',
            };
            this.$refs.tree.handleClearable();
            this.cacheQueryCondition = {};
            this.initTimerange();
            this.pageNo = 1;
            this.pageSize = 10;
            this.$nextTick(() => {
                this.query();
            });
        },
        selectBy() {
            // 列表查询请求数据
            this.loading = true;
            this.tableData = [];
            HTTP.request('queryStatisticsList', {
                method: 'post',
                urlParam: {
                    pageNo: this.pageNo,
                    pageSize: this.pageSize,
                },
                data: this.cacheQueryCondition,
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data || {};
                        this.tableHeaders = result.titles || {};
                        // 不展示对象ID
                        delete this.tableHeaders.objectId;
                        this.tableData = result.values || [];
                        this.total = result.total;
                    } else {
                        this.tableHeaders = {};
                        this.total = 0;
                        this.$message.error(this.$t('tipMessage.requestError'));
                    }
                    this.loading = false;
                },
                error: () => {
                    this.tableHeaders = {};
                    this.total = 0;
                    this.$message.error(this.$t('tipMessage.networkError'));
                    this.loading = false;
                },
            });
        },
        handleSizeChange() {
            this.pageNo = 1;
            this.selectBy();
        },
        handleCurrentChange() {
            this.selectBy();
        },
        toDetail(row, colKey) {
            let pathname = row.position ? row.position : row.object;
            $emit(this, 'toDetail', {
                ...this.cacheQueryCondition,
                id: row.objectId,
                strategyType: TIME_PERIOD_MAP[colKey],
                rowDate: row.date || '',
                pathname,
            });
        },
        toTrend(row, colKey) {
            let pathname = row.position ? row.position : row.object;
            $emit(this, 'toTrend', {
                ...this.cacheQueryCondition,
                objectId: row.objectId,
                colKey,
                strategyType: TIME_PERIOD_MAP[colKey],
                pathname,
            });
        },
        handleExport() {
            const DOWNLOAD_URL = '/api/battery-manager/v1/price-strategy/exportStatistics';
            const forgerydefense = window.forgerydefense || '';
            const languageOption = window.languageOption || '';
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            axios.post(url, this.cacheQueryCondition, config).then(res => {
                // 导出错误，返回json对象，需判断
                if (res.data.type === 'application/json') {
                    let reader = new FileReader();
                    reader.onload = e => {
                        let result = JSON.parse(e.target.result);
                        if (result && result.code !== 0) {
                            this.$message.error(result.message);
                        }
                    };
                    reader.readAsText(res.data, ['utf-8']);
                } else {
                    // 导出成功，返回数据流
                    let blob = new Blob([res.data]);
                    let url = window.URL.createObjectURL(blob); // 创建下载的链接
                    let link = document.createElement('a');
                    let fileName = '';
                    if (res.headers['content-disposition']) {
                        let contentDisposition = res.headers['content-disposition'];
                        fileName = contentDisposition.split('filename=')[1];
                        fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                    }
                    link.style.display = 'none';
                    link.href = url;
                    link.download = `${fileName}.xlsx`; // 下载后文件名
                    document.body.appendChild(link);
                    link.click(); // 点击下载
                    document.body.removeChild(link); // 下载完成移除元素
                    window.URL.revokeObjectURL(url); // 释放掉blob对象
                }
            });
        },
        getSpecialTextWidth(text, isHeader, header = null) {
            let width = this.getTextWidth(text);
            if (!isHeader && header) {
                let headerWidth = this.getTextWidth(header.name);
                let length = Object.keys(header).length - 1;
                if (length * width < headerWidth) {
                    width = Math.ceil(headerWidth / length);
                }
            }
            return width;
        },
        // eslint-disable-next-line quotes
        getTextWidth(text, redeem = 72, font = "14px arial, 'microsoft yahei'") {
            // if (this.$i18n.locale === "en-US") {
            //     redeem = 36;
            // }
            let canvas = this.getTextWidth.canvas || (this.getTextWidth.canvas = document.createElement('canvas'));
            let context = canvas.getContext('2d');
            context.font = font;
            let metrics = context.measureText(text);
            let result = metrics.width + redeem > 150 ? metrics.width + redeem : 150;
            result = Math.ceil(result);
            return result;
        },
        nodeSelectClick(node, value) {
            this.queryForm.range = value;
            // this.$refs.Form.validateField('range')
            // this.checkedNodesId = value;
        },
        nodeClear() {
            this.queryForm.range = [];
            // this.checkedNodesId = [];
        },
        querySubnetLevel(nodes) {
            this.positionGranOptions = [];
            let location = [];
            nodes.forEach(item => {
                location.push(item.id);
            });
            HTTP.request('newQuerySubnetLevel', {
                method: 'post',
                data: location,
                complete: resp => {
                    if (resp.code === 0) {
                        let result = resp.data || [];
                        result.forEach(item => {
                            let name = this.$t('common.wholeNetwork');
                            if (item !== 0) {
                                name = this.$t('battery.fields.nLevelSubnet', { num: item });
                            }
                            let option = {
                                id: item,
                                name,
                            };
                            this.positionGranOptions.push(option);
                        });
                    }
                },
            });
        },
    },
    emits: ['toDetail', 'toTrend'],
};
</script>

<style lang="scss" scoped>
.search-form {
    padding-bottom: 0;
}

:deep(.el-form) {
    .el-date-editor.el-input__wrapper {
        width: auto;
    }
}
:deep(.el-table__fixed-right) {
    height: 100% !important;
}

::v-deep::-webkit-scrollbar {
    width: 10px;
    height: 8px;
    border-radius: 0;
    -webkit-border-radius: 0;
}
</style>
