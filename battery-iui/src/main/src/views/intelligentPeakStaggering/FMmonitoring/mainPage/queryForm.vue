<template>
    <div class="queryForm">
        <!-- Started by AICoder, pid:349e8q4295y373214fa10a58d0abf297c3500f0a -->
        <popover-wrap
            ref="filterRef"
            v-model:visible="filterVisible"
            placement="bottom"
            width="auto"
            :tip="$t('button.filter')"
            tip-placement="top"
            :visible-arrow="false"
            @visible="filterVisibleChange"
        >
            <template #content>
                <el-form
                    ref="formRef"
                    :model="queryForm"
                    :label-width="labelWidth"
                    label-position="right"
                    class="uedm-form-single"
                >
                    <!-- 调频响应 -->
                    <el-form-item :label="t('frequencyModulation.fields.FMresponse')">
                        <el-select
                            v-model="queryForm.fmResponse"
                            :placeholder="$t('placeholder.select')"
                            clearable
                            :teleported="false"
                        >
                            <el-option
                                v-for="item in FMresponseOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <!-- 设备类型 -->
                    <el-form-item :label="t('frequencyModulation.fields.collectorType')">
                        <el-select
                            v-model="queryForm.deviceType"
                            :placeholder="$t('placeholder.select')"
                            clearable
                            :teleported="false"
                        >
                            <el-option
                                v-for="item in deviceTypeOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <!-- 工作模式 -->
                    <el-form-item :label="t('frequencyModulation.fields.mode')">
                        <el-select
                            v-model="queryForm.mode"
                            :placeholder="$t('placeholder.select')"
                            clearable
                            :teleported="false"
                        >
                            <el-option
                                v-for="item in workModeOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <!-- 支持调频 -->
                    <el-form-item :label="t('frequencyModulation.fields.supportFM')">
                        <el-select
                            v-model="queryForm.supportFM"
                            :placeholder="$t('placeholder.select')"
                            :teleported="false"
                        >
                            <el-option
                                v-for="item in supportFMOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <!-- 站点名称 -->
                    <el-form-item :label="$t('frequencyModulation.fields.siteName')">
                        <el-input
                            v-model="queryForm.siteName"
                            maxlength="100"
                            clearable
                            :placeholder="$t('placeholder.input')"
                        ></el-input>
                    </el-form-item>
                    <!-- 底部按钮栏 -->
                    <el-form-item>
                        <el-button type="primary" @click="query()">
                            {{ $t('button.search') }}
                        </el-button>
                        <el-button @click="reset()">
                            {{ $t('button.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </template>
            <template #button>
                <el-button plain class="uedm-button-icon" @click.stop="handleShow">
                    <el-badge :is-dot="hasFilter" :key="hasFilter">
                        <span class="plx-ico-filter-16"></span>
                    </el-badge>
                </el-button>
            </template>
        </popover-wrap>
        <!-- Ended by AICoder, pid:349e8q4295y373214fa10a58d0abf297c3500f0a -->
    </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineExpose } from 'vue';
import PopoverWrap from '@uedm/uedm-ui/src/components/popoverWrap.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const props = defineProps({
    value: {
        type: Object,
        default: null,
    },
});
const emit = defineEmits(['query']);
// popover
const filterVisible = ref(false);
const filterRef = ref();

const labelWidth = computed(() => {
    return localStorage.getItem('language-option') === '"en-US"' ? 220 : 90;
});

const handleShow = function () {
    filterRef.value.show();
};
const hide = function () {
    filterRef.value.hide();
};
// 过滤弹出中的查询条件
const queryForm = reactive({
    fmResponse: '',
    deviceType: 'BCUA',
    mode: '',
    supportFM: 'supportFM',
    siteName: ''
});
const FMresponseOptions = [
    {
        id: 'inFM',
        name: t('frequencyModulation.fields.inFM'),
    },
    {
        id: 'unFM',
        name: t('frequencyModulation.fields.unFM'),
    }
];
const deviceTypeOptions = [
    {
        id: 'BCUA',
        name: 'BCUA',
    }
];
const workModeOptions = [
    {
        id: '0',
        name: t('frequencyModulation.fields.standby'),
    },
    {
        id: '1',
        name: t('frequencyModulation.fields.FM'),
    },
    {
        id: '2',
        name: t('frequencyModulation.fields.peakShifting'),
    },
];
const supportFMOptions = [
    {
        id: 'supportFM',
        name: t('frequencyModulation.fields.yes')
    },
    {
        id: 'notSupportFM',
        name: t('frequencyModulation.fields.no')
    },
];
let defaultData = {};

const hasFilter = ref(false);
const updataHasFilter = function () {
    let tag = false;
    for (let key in queryForm) {
        if (queryForm[key].length) {
            tag = true;
        }
    }
    hasFilter.value = tag;
};
// 过滤表单数据初始化
const init = function () {
    if (props.value) {
        for (let key in props.value) {
            queryForm[key] = props.value[key];
        }
    }
    updataHasFilter();
};
const filterVisibleChange = function (val) {
    if (val) {
        init();
    }
};
// 查询事件
const query = function () {
    emit('query', queryForm);
    updataHasFilter();
    hide();
};
// 重置事件
const reset = function () {
    for (let key in queryForm) {
        queryForm[key] = defaultData[key];
    }
    emit('query', queryForm);
    updataHasFilter();
    hide();
};

init();
// 第一次初始化时将传入参数作为重置的默认值(必须写在初始化init后)
defaultData = Object.assign({}, queryForm);
defineExpose({
    query,
});
</script>

<style lang="scss" scoped>
.queryForm {
    position: absolute;
    top: 10px;
    right: 16px;
}
</style>