<template>
    <div style="height: 100%">
        <div class="uedm-navigation" style="position: relative">
            <path-breadcrumb :lists="pathNames" :minus-width="150"></path-breadcrumb>
            <query-form ref="queryFormRef" @query="queryHandler" class="queryForm"></query-form>
        </div>
        <!-- Started by AICoder, pid:a49c8e01bci528614dc70ae281de3c10b4d1affb -->
        <div class="uedm-space" style="height: calc(100% - 85px)">
            <div class="statistics">
                <div class="uedm-content-area device">
                    <div class="icon-area">
                        <div class="icon"></div>
                        <div class="text">{{ $t('frequencyModulation.fields.device') }}</div>
                    </div>
                    <div class="line"></div>
                    <div class="numbers">
                        <div class="support-fm">
                            <div class="title">{{ $t('frequencyModulation.fields.supportFM') }}</div>
                            <div class="number">{{ supportFMNums }}</div>
                        </div>
                        <div class="line"></div>
                        <div class="fm-response">
                            <div class="title">
                                <span>{{ $t('frequencyModulation.fields.FMresponse') }}</span>
                                <span>(</span>
                                <span style="color: #409eff">{{ $t('frequencyModulation.fields.inFM')}}</span>
                                <span style="margin: 0 6px">|</span>
                                <span>{{ $t('frequencyModulation.fields.unFM') }}</span>
                                <span>)</span>
                            </div>
                            <div class="number">
                                <span style="color: #409eff">{{ inFMNums }}</span>
                                <span style="margin: 0 6px">|</span>
                                <span>{{ notInFMNums }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uedm-content-area capacity">
                    <div class="icon-area">
                        <div class="icon"></div>
                        <div class="text">{{ $t('frequencyModulation.fields.capacity') }}</div>
                    </div>
                    <div class="line"></div>
                    <div class="numbers">
                        <div class="title">{{ $t('frequencyModulation.fields.energyStorageCapacity') + '(Ah)' }}</div>
                        <div class="number">{{ capacity || '--' }}</div>
                    </div>
                </div>
            </div>
            <div class="uedm-content-area table-area">
                <div class="uedm-table__bar">
                    <div class="floatRight">
                        <column-filter
                            :is-dark="isDark"
                            :value="columnData"
                            :height="500"
                            :tip="$t('tooltip.displayedItems')"
                            @save="handleFilterOk"
                        ></column-filter>
                        <el-tooltip class="item" effect="dark" :content="$t('button.refresh')" placement="bottom">
                            <el-button plain class="uedm-button-icon ml8" @click="refreshHandler">
                                <span class="plx-ico-refresh-16"></span>
                            </el-button>
                        </el-tooltip>
                    </div>
                </div>
                <el-table
                    v-if="showTable"
                    ref="deviceTable"
                    v-loading="loading"
                    style="width: 100%; height: calc(100% - 82px)"
                    :data="tableData"
                    @sort-change="sortChange"
                >
                    <el-table-column
                        v-for="item in tableHeader"
                        :key="item.id"
                        :prop="item.id"
                        :sortable="item.sortable ? 'custom' : false"
                        :label="formatHeader(item)"
                        show-overflow-tooltip
                        :min-width="textSize(item)"
                    >
                        <template v-slot="scope">
                            <span v-if="item.id === 'workModel'">
                                <el-tag
                                    v-if="scope.row.workModel"
                                    :type="modeToTagType[scope.row.workModel]"
                                >
                                    {{ scope.row.workModel }}
                                </el-tag>
                                <span v-else>--</span>
                            </span>
                            <span v-else>{{ scope.row[item.id] || '--' }}</span>
                        </template>
                    </el-table-column>
                    <!-- 操作列 -->
                    <el-table-column
                        v-if="tableHeader.length > 0"
                        :label="$t('table.operation')"
                        fixed="right"
                        :min-width="100"
                    >
                        <template v-slot="scope">
                            <el-link type="primary" :underline="false" @click="toDetail(scope.row)">
                                {{ $t('button.detail') }}
                            </el-link>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="pageInfo.pageNo"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[5, 10, 20, 30]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                ></el-pagination>
                <div id="temporary"></div>
            </div>
        </div>
        <!-- Ended by AICoder, pid:a49c8e01bci528614dc70ae281de3c10b4d1affb -->
    </div>
</template>

<script setup>
import { defineProps, computed, ref, reactive, onMounted, watch, onBeforeMount, nextTick } from 'vue';
import HTTP from '@/util/httpService';
import { ElMessage as Message } from 'element-plus';
import { useI18n } from 'vue-i18n';
import PathBreadcrumb from '@uedm/uedm-ui/src/components/pathBreadcrumb.vue';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
import QueryForm from './queryForm.vue';
import { useStore } from 'vuex';

const props = defineProps({
    pathNames: {
        type: Array,
        required: true,
        default: () => [],
    },
    nodeData: {
        type: Object,
        default: null,
    }
});

const store = useStore();
const { t, locale } = useI18n();
const supportFMNums = ref(0);
const inFMNums = ref(0);
const notInFMNums = ref(0);
const capacity = ref(0);
const showTable = ref(true);
const loading = ref(false);
const tableData = ref([]);
const tableHeader = ref([]);
const columnData = ref([]); // 过滤条件列表数据原始
const filterParams = ref([]); // 过滤条件参数对象
const filterIds = ref([]); // 过滤条件参数Id
const pageInfo = reactive(
    {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
);
const sortOrder = ref('');
const sortProp = ref('');
const filterParam = ref([]);
const queryParam = ref({});
const queryFormRef = ref();
const emit = defineEmits(['changeView']);
const isDark = computed(() => {
    return store.getters.getIsDark;
});

const modeToTagType = {
    [t('frequencyModulation.fields.peakShifting')]: 'warning',
    [t('frequencyModulation.fields.FM')]: 'success',
    [t('frequencyModulation.fields.standby')]: 'primary'
};
const toDetail = row => {
    emit('changeView', { data: row });
};
/* Started by AICoder, pid:e9923p6dceyc51614f7a0aae1038433c9f02e013 */
const textSize = item => {
    let text = item.name;
    if (item.unit) {
        text = text + '(' + item.unit + ')';
    }
    let content = document.getElementById('temporary');
    let span = document.createElement('span');
    let w = span.offsetWidth;
    span.style.visibility = 'hidden';
    span.style.fontSize = '14px';
    span.style.fontWeight = 'bolder';
    span.style.display = 'inline-block';
    content.appendChild(span);
    if (typeof span.textContent !== 'undefined') {
        span.textContent = text;
    } else {
        span.innerText = text;
    }
    w = parseFloat(window.getComputedStyle(span).width) - w;
    content.removeChild(span);
    if (['deviceName'].includes(item.id)) {
        return Math.max(w + 50, 120);
    } else if (item.id === 'position') {
        return Math.max(w + 50, 160);
    } else if (['startTime', 'endTime'].includes(item.id)) {
        return Math.max(w + 50, 160);
    } else if (['workModel'].includes(item.id) && locale.value === 'en-US') {
        return w + 90;
    } else if (['namePath'].includes(item.id)) {
        return w + 250;
    } else if (['siteName'].includes(item.id)) {
        return w + 150;
    } else if (['deviceType'].includes(item.id)) {
        return w + 30;
    } else {
        return w + 50; // 50为排序按钮和内边距
    }
};
/* Ended by AICoder, pid:e9923p6dceyc51614f7a0aae1038433c9f02e013 */
const getStatisticData = param => {
    HTTP.request('getStatisticData', {
        method: 'post',
        data: {
            logicGroupId: props.nodeData.id,
            ...queryParam.value
        },
        complete: data => {
            if (data.code === 0) {
                const { enableFreqNum, freqNum, unfreqNum, enableFreqCapacity } = data.data;
                supportFMNums.value = enableFreqNum;
                inFMNums.value = freqNum;
                notInFMNums.value = unfreqNum;
                capacity.value = enableFreqCapacity;
            } else {
                Message.error(t('tipMessage.requestError'));
            }
        },
        error: () => {
            Message.error(t('tipMessage.requestError'));
        }
    });
};
const getListParams = () => {
    return {
        logicGroupId: props.nodeData.id,
        ...queryParam.value,
        fmMonitorDims: filterParam.value,
        order: sortOrder.value,
        sortBy: sortProp.value,
    };
};
/* Started by AICoder, pid:cc303sad49t900d14c8008da90d3b9261289e88c */
const getTableData = () => {
    if (!filterParam.value.length) {
        return;
    }
    loading.value = true;
    tableData.value = [];
    const params = getListParams();
    HTTP.request('getTheDeviceList', {
        method: 'post',
        urlParam: {
            pageNo: pageInfo.pageNo,
            pageSize: pageInfo.pageSize,
        },
        data: params,
        complete: data => {
            loading.value = false;
            if (data.code === 0 && data.data) {
                tableData.value = data.data;
                pageInfo.total = data.total;
            } else {
                Message.error(t('tipMessage.requestError'));
            }
        },
        error: () => {
            loading.value = false;
            Message.error(t('tipMessage.requestError'));
        }
    });
};
const queryHandler = params => {
    queryParam.value = {
        deviceType: params.deviceType,
        workModel: params.mode,
        enableFreq: params.supportFM === 'supportFM',
        siteName: params.siteName,
        freqResponse: params.fmResponse ? (params.fmResponse === 'inFM') : ''
    };
    getStatisticData();
    getTableData();
};
/* Ended by AICoder, pid:cc303sad49t900d14c8008da90d3b9261289e88c */
const sortChange = column => {
    // 排序
    sortProp.value = column.prop || '';
    if (column.order === 'descending') {
        sortOrder.value = 'desc';
    } else if (column.order === 'ascending') {
        sortOrder.value = 'asc';
    } else {
        sortOrder.value = '';
        sortProp.value = '';
    }
    getTableData();
};
/* Started by AICoder, pid:l1d12b8f9dhfa541426c0a4c50d31225a2957cec */
const getFilterParameter = d => {
    filterParam.value = [];
    filterIds.value = [];
    tableHeader.value = [];
    for (let i = 0; i < d.length; i++) {
        let item = d[i];
        if (item.enable) {
            filterParam.value.push(item);
            filterIds.value.push(item.id);
            tableHeader.value.push({
                id: item.id,
                sequence: item.sequence,
                name: item.name,
                unit: item.unit,
                sortable: item.sortable,
            });
        }
    }
    showTable.value = false;
    nextTick(() => {
        showTable.value = true;
    });
    pageInfo.pageNo = 1;
    getTableData();
};
/* Ended by AICoder, pid:l1d12b8f9dhfa541426c0a4c50d31225a2957cec */
const getFilter = () => {
    loading.value = true;
    HTTP.request('getTableHeader', {
        method: 'get',
        complete: data => {
            loading.value = false;
            if (data.code === 0) {
                columnData.value = data.data;
                getFilterParameter(data.data, false);
            }
        },
        error: () => {
            columnData.value = [];
            loading.value = false;
        },
    });
};
const formatHeader = item => {
    let name = item.name;
    if (item.unit) {
        name = name + '(' + item.unit + ')';
    }
    return name;
};
/* Started by AICoder, pid:sc659pea86a488b144890a0d8029083e8eb0615d */
const saveFilter = d => {
    let updateParameter = d.map(item => ({
        id: item.id,
        sequence: item.sequence,
        enable: item.enable,
    }));
    HTTP.request('updateTableHeader', {
        method: 'post',
        data: updateParameter,
        complete: data => {
            if (data.code === 0) {
                Message.success(t('batteryOverview.tips.filterSaveSuccess'));
                getFilter();
            } else if (data.code === -301) {
                Message.error(t('batteryOverview.tips.dimIdBlank'));
            } else if (data.code === -302) {
                Message.error(t('batteryOverview.tips.sequenceNotUnique'));
            } else if (data.code === -305) {
                Message.error(t('batteryOverview.tips.valueNotRange'));
            } else if (data.code === -208) {
                Message.error(t('batteryOverview.tips.valueNotModify'));
            } else {
                Message.error(t('batteryOverview.tips.filterSaveError'));
            }
        },
        error: () => {
            Message.error(t('tipMessage.networkError'));
        },
    });
};
/* Ended by AICoder, pid:sc659pea86a488b144890a0d8029083e8eb0615d */
const handleFilterOk = d => {
    saveFilter(d);
};
const refreshHandler = () => {
    getTableData();
};
const handleSizeChange = () => {
    pageInfo.pageNo = 1;
    getTableData();
};
const handleCurrentChange = () => {
    getTableData();
};
watch(
    () => props.nodeData,
    () => {
        queryFormRef.value.query();
        getFilter();
    }
);
onMounted(() => {
    queryFormRef.value.query();
});
onBeforeMount(() => {
    getFilter();
});
</script>

<style lang="scss" scoped>
/* Started by AICoder, pid:4f5f1kd213t1898141380a7181a0f2145814358a */
.statistics {
    height: 100px;
    display: flex;
    margin-bottom: 16px;

    .device {
        flex: 1;
        display: flex;

        .icon-area {
            flex: 2;
            padding-right: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            .icon {
                width: 120px;
                height: 40px;
                background: center center no-repeat;
                background-image: url('../../../../assets/img/icon_device.png');
            }
        }

        .numbers {
            flex: 8;
            display: flex;

            .support-fm,
            .fm-response {
                flex: 1;
                padding-left: 16px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .title {
                    font-weight: bold;
                }

                .number {
                    font-size: 32px;
                    color: #303133;
                }
            }

            .fm-response .number {
                font-size: 24px;
            }
        }
    }

    .capacity {
        flex: 1;
        display: flex;
        margin-left: 16px;

        .icon-area {
            flex: 2;
            padding-right: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            .icon {
                width: 120px;
                height: 40px;
                background: center center no-repeat;
                background-image: url('../../../../assets/img/icon_peak_total.png');
            }
        }

        .numbers {
            padding-left: 16px;
            flex: 8;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .title {
                font-weight: bold;
            }

            .number {
                font-size: 32px;
                color: #303133;
            }
        }
    }

    .uedm-content-area + .uedm-content-area {
        margin-top: 0;
    }

    .line {
        width: 1px;
        height: 68px;
        background-color: #dcdfe6;
    }
}

.table-area {
    height: calc(100% - 148px);
}

:deep(.el-tag) {
    padding: 0 8px;
}

:deep(.el-scrollbar__view) {
    height: 100%;
}
html.dark {
    .statistics .numbers .number {
        color: #cfd3dc;
    }
    .statistics .device .numbers .support-fm .number {
        color: #cfd3dc;
    }
    .statistics .device .numbers .fm-response .number {
        color: #cfd3dc;
    }
}

/* Ended by AICoder, pid:4f5f1kd213t1898141380a7181a0f2145814358a */
</style>