<template>
    <div>
        <div class="uedm-navigation">
            <span class="uedm-pagetitle">
                <el-link type="primary" :underline="false" @click="backOverview()">
                    <el-icon :size="20">
                        <Back />
                    </el-icon>
                </el-link>
                {{ $t('batteryOverview.fields.automaticTest') }}
            </span>
        </div>
        <div class="space">
            <!-- 电池数量统计 -->
            <div v-loading="statisticsLoading" class="z-block uedm-content-area">
                <div class="z-block-content">
                    <div class="float batteryStatistics">
                        <div class="iconArea borderRight"><i class="icon-battery"></i></div>
                        <div class="statisic-content borderRight">
                            <span class="number-title">{{ $t('batteryOverview.fields.sitesInExecution') }}</span>
                            <span class="totalNumber ">
                                {{ batteryStatistics.siteNumber && batteryStatistics.siteNumber.number }}
                            </span>
                        </div>
                        <div class="statisic-content">
                            <span class="number-title">{{ $t('batteryOverview.fields.powerInExecution') }}</span>
                            <span class="totalNumber">
                                {{ batteryStatistics.switchPowerNumber && batteryStatistics.switchPowerNumber.number }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="uedm-content-area">
                <el-button
                    type="primary"
                    class="add-btn"
                    v-if="!!rights['battery.test.task.add']"
                    @click="goAddPage('add')"
                >
                    <el-icon><el-icon-plus /></el-icon>
                    {{ $t('batteryTest.fields.addTask') }}
                </el-button>
                <div class="filterButtons">
                    <!-- 顶部查询条件内容 -->
                    <span class="filterItem">
                        <el-tooltip class="item" effect="dark" :content="$t('button.filter')" placement="bottom">
                            <span
                                class="icon-button filterIconBtn"
                                :class="{ on: hasFilter }"
                                @click.stop="handleShowQuery"
                            ></span>
                        </el-tooltip>
                        <div class="filterContent">
                            <div v-show="showQuery" ref="popQuery" class="filterPop">
                                <div class="inner">
                                    <!-- 筛选表单 -->
                                    <form-query @query="handleQuery"></form-query>
                                </div>
                            </div>
                        </div>
                    </span>
                </div>

                <!-- 任务列表 -->
                <el-table v-loading="loading" :data="tableData" style="width: 100%" stripe @sort-change="sortChange">
                    <el-table-column
                        sortable="custom"
                        min-width="120"
                        prop="name"
                        :label="$t('batteryTest.fields.name')"
                    >
                        <template v-slot="scope">
                            <span class="table-name" @click="detailClick(scope.row)">{{ scope.row.name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        sortable="custom"
                        prop="siteNumber"
                        width="220"
                        :label="$t('batteryOverview.fields.sitesInExecution')"
                    ></el-table-column>
                    <el-table-column
                        sortable="custom"
                        prop="switchPowerNumber"
                        width="320"
                        :label="$t('batteryOverview.fields.powerInExecution')"
                    ></el-table-column>
                    <el-table-column
                        width="210"
                        sortable="custom"
                        prop="period"
                        :label="$t('batteryTest.fields.ExecutionFrequency')"
                    ></el-table-column>
                    <el-table-column
                        sortable="custom"
                        width="130"
                        prop="status"
                        :label="$t('batteryTest.fields.planStatus')"
                    >
                        <template v-slot="scope">
                            <span v-if="scope.row.status && scope.row.status.id">
                                <span v-if="scope.row.status.id == 'pending'">
                                    <i class="circle" style="background: rgb(191, 191, 191)" />
                                    {{ scope.row.status.name || '--' }}
                                </span>
                                <span v-else-if="scope.row.status.id == 'running'">
                                    <i class="circle" style="background: rgb(25, 147, 255)" />
                                    {{ scope.row.status.name || '--' }}
                                </span>
                                <span v-else-if="scope.row.status.id == 'pausing'">
                                    <i class="circle" style="background: rgb(224, 34, 34)" />
                                    {{ scope.row.status.name || '--' }}
                                </span>
                                <span v-else>--</span>
                            </span>
                            <span v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        sortable="custom"
                        width="160"
                        prop="startTime"
                        :label="$t('batteryTest.fields.executionTime')"
                    >
                        <template v-slot="scope">
                            {{ scope.row.startTime || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column sortable="custom" width="120" prop="creator" :label="$t('peakSetting.creator')">
                        <template v-slot="scope">
                            {{ scope.row.creator || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        sortable="custom"
                        width="180"
                        prop="gmtCreate"
                        :label="$t('peakSetting.createTime')"
                    >
                        <template v-slot="scope">
                            {{ scope.row.gmtCreate || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column sortable="custom" width="120" prop="updater" :label="$t('peakSetting.changeMan')">
                        <template v-slot="scope">
                            {{ scope.row.updater || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        sortable="custom"
                        width="180"
                        prop="gmtModified"
                        :label="$t('peakSetting.modifyTime')"
                    >
                        <template v-slot="scope">
                            {{ scope.row.gmtModified || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column v-if="operateRights" :label="$t('table.operation')" width="180" fixed="right">
                        <template v-slot="scope">
                            <el-button
                                v-if="!!rights['battery.test.task.edit']"
                                type="text"
                                size="small"
                                :disabled="scope.row.status.id == 'running'"
                                @click="editClick(scope.row)"
                            >
                                {{ $t('button.edit') }}
                            </el-button>
                            <el-button
                                v-if="!!rights['battery.test.task.update']"
                                type="text"
                                size="small"
                                @click="stateClick(scope.row)"
                            >
                                {{
                                    scope.row.status.id == 'running'
                                        ? $t('batteryTest.fields.pausing')
                                        : $t('button.execute')
                                }}
                            </el-button>
                            <el-button
                                v-if="!!rights['battery.test.task.delete']"
                                type="text"
                                size="small"
                                class="red"
                                :class="{
                                    unDel:
                                        !rights['battery.test.task.delete'] ||
                                        scope.row.status.id == 'running' ||
                                        scope.row.internal,
                                }"
                                :disabled="scope.row.status.id == 'running' || scope.row.internal"
                                @click="toDelete(scope.row)"
                            >
                                {{ $t('button.delete') }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="pageInfo.pageNo"
                    :page-sizes="[5, 10, 20, 30]"
                    v-model:page-size="pageInfo.pageSize"
                    layout="total, sizes, prev, pager, next,jumper"
                    :total="pageInfo.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                ></el-pagination>
            </div>
        </div>
        <confirm-dialog
            v-model:visible="deleteDialog"
            :title="$t('dialog.prompt')"
            type="warning"
            :append-to-body="true"
            @confirm="deleteConfirm"
        >
            <template v-slot:message>
                {{ $t('tipMessage.deleteConfirm', { message: '' }) }}
            </template>
        </confirm-dialog>
    </div>
</template>

<script>
import { Plus as ElIconPlus } from '@element-plus/icons-vue';
import { $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import ConfirmDialog from '@/components/ConfirmDialog';
import FormQuery from './formQuery.vue';

export default {
    components: {
        'confirm-dialog': ConfirmDialog,
        'form-query': FormQuery,
        ElIconPlus,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            formInline: {
                name: '',
                state: [],
                deviceIds: [],
            },
            showQuery: false, // 顶部查询条件
            loading: false,
            statisticsLoading: false,
            batteryStatistics: {}, // 顶部统计数据
            tableData: [],
            pageInfo: {
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            deleteDialog: false,
            deleteLoading: false,
            readyToDeleteItem: {},
            order: '',
            sort: '',
        };
    },
    computed: {
        hasFilter() {
            let tag = false;
            let formInline = this.formInline;
            for (let key in formInline) {
                if (['name'].includes(key)) {
                    if (formInline[key]) {
                        tag = true;
                    }
                } else if (formInline[key] && formInline[key].length) {
                    tag = true;
                }
            }
            return tag;
        },
        operateRights() {
            return (
                !!this.rights['battery.test.task.edit'] ||
                !!this.rights['battery.test.task.update'] ||
                !!this.rights['battery.test.task.delete']
            );
        },
    },
    created() {},
    mounted() {
        this.searchTask();
        this.getTestManualStatistics(); // 概览数据查询
    },
    methods: {
        backOverview() {
            $emit(this, 'switchShowPage', 'overview');
        },
        // 切换查询条件显示
        handleShowQuery() {
            this.showQuery = !this.showQuery;
        },
        handleQuery(val) {
            this.pageInfo.pageNo = 1;
            this.formInline = JSON.parse(JSON.stringify(val));
            this.handleShowQuery();
            this.searchTask(); // 列表查询
        },
        // 查询任务列表
        searchTask() {
            this.loading = true;
            HTTP.request('searchTaskList', {
                method: 'post',
                data: {
                    status: this.formInline.state,
                    deviceIds: this.formInline.deviceIds,
                    name: this.formInline.name,
                    pageNo: this.pageInfo.pageNo,
                    pageSize: this.pageInfo.pageSize,
                    order: this.order,
                    sort: this.sort,
                },
                complete: data => {
                    if (data.code === 0) {
                        this.pageInfo.total = data.total;
                        // 防止删除后最后一页无数据
                        let pageNum = Math.ceil(this.pageInfo.total / this.pageInfo.pageSize) || 1;
                        if (pageNum < this.pageInfo.pageNo) {
                            this.pageInfo.pageNo = pageNum;
                            this.searchTask();
                        }
                        this.tableData = data.data;
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.loading = false;
                },
                error: () => {
                    this.loading = false;
                    this.$message.error(this.$t('tipMessage.requestError'));
                },
            });
        },
        // 置空参数
        reset() {
            this.formInline = {
                name: '',
                state: [],
                deviceIds: [],
            };
            this.pageInfo.pageNo = 1;
            this.pageInfo.pageSize = 10;
            this.pageInfo.total = 0;
            this.searchTask();
        },
        // 详情展示
        detailClick(row) {
            $emit(this, 'changeCurPage', 'detail', row.id);
        },
        // 状态编辑
        stateClick(row) {
            if (!this.rights['battery.test.task.update']) {
                return;
            }
            let status = '';
            if (row.status.id === 'pending') {
                status = 'running';
            }
            if (row.status.id === 'running') {
                status = 'pausing';
            }
            if (row.status.id === 'pausing') {
                status = 'running';
            }
            HTTP.request('taskStateEdit', {
                method: 'put',
                data: {
                    id: row.id,
                    status: status,
                },
                complete: data => {
                    if (data.code === 0) {
                        this.$message({
                            showClose: false,
                            duration: 3000,
                            message: this.$t('tipMessage.operationSuccsee'),
                            type: 'success',
                        });
                        this.searchTask();
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    this.getTestManualStatistics(); // 概览查询
                },
                error: () => {
                    this.$message.error(this.$t('tipMessage.networkError'));
                    this.getTestManualStatistics(); // 概览查询
                },
            });
        },
        // 任务编辑
        editClick(row) {
            $emit(this, 'changeCurPage', 'add', row.id);
        },
        toDelete(row) {
            this.readyToDeleteItem = row;
            this.deleteDialog = true;
        },
        // 删除任务
        deleteConfirm() {
            if (!this.rights['battery.test.task.delete']) {
                return;
            }
            let row = this.readyToDeleteItem;
            let ids = [];
            ids.push(row.id);
            HTTP.request('delTestTask', {
                method: 'delete',
                data: ids,
                complete: data => {
                    this.deleteDialog = false;
                    if (data.code === 0) {
                        this.$message({
                            showClose: false,
                            duration: 3000,
                            message: this.$t('tipMessage.deleteSuccess'),
                            type: 'success',
                        });
                        this.searchTask();
                    } else {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.deleteDialog = false;
                    this.$message.error(this.$t('tipMessage.networkError'));
                },
            });
        },
        handleSizeChange(pageSize) {
            this.pageInfo.pageSize = pageSize;
            this.pageInfo.pageNo = 1;
            this.searchTask();
        },
        handleCurrentChange(pageNo) {
            // 页码
            this.pageInfo.pageNo = pageNo;
            this.searchTask();
        },
        sortChange(column) {
            // 排序
            this.order = column.prop || '';
            if (column.order === 'descending') {
                this.sort = 'desc';
            } else if (column.order === 'ascending') {
                this.sort = 'asc';
            } else {
                this.order = '';
                this.sort = '';
            }
            this.pageInfo.pageNo = 1;
            this.searchTask();
        },
        goAddPage(page) {
            $emit(this, 'changeCurPage', page, '');
        },
        // 顶部概览查询
        getTestManualStatistics() {
            this.statisticsLoading = true;
            this.batteryStatistics = {
                siteNumber: { number: '--' },
                switchPowerNumber: { number: '--' },
            };
            HTTP.request('getTestManualStatistics', {
                method: 'post',
                data: {
                    status: [],
                    deviceIds: [],
                    name: '',
                    autoStatisticsDims: ['siteNumber', 'switchPowerNumber'],
                },
                complete: data => {
                    if (data.code === 0) {
                        for (let k in data.data) {
                            if (k && data.data[k]) {
                                if (!data.data[k].number && data.data[k].number !== 0) {
                                    data.data[k].number = '--';
                                }
                            }
                        }
                        this.batteryStatistics = data.data;
                    }
                    this.statisticsLoading = false;
                },
                error: () => {
                    this.statisticsLoading = false;
                },
            });
        },
    },
    emits: ['switchShowPage', 'changeCurPage'],
};
</script>

<style lang="scss" scoped>
.filterButtons {
    margin-bottom: 8px;
}
.space {
    padding-top: 0;
}
.uedm-breadcrumbs li::before {
    content: '';
}
.title {
    line-height: 30px;
}
.table-name {
    font-style: 12px;
    color: #1993ff;
    cursor: pointer;
}
.circle {
    width: 8px;
    height: 8px;
    background: red;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}
.red {
    color: red !important;
}
.unDel {
    color: #bfbfbf !important;
}
.periodicPlan-device-select {
    .device-name {
        margin-right: 32px;
        display: inline-block;
        width: 145px;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: bottom;
    }
    .device-path {
        display: inline-block;
        width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: bottom;
    }
}
.z-block {
    margin-top: 16px;
}
.statisic-content {
    display: flex;
    flex-direction: column;
    .number-title {
        font-weight: bold;
    }
}
.borderRight {
    border-right: solid 1px #ebeef5
}
.add-btn.el-button {
    float: left;
    width: 97px;
    margin-bottom: 8px;
}
.filterContent .filterPop {
    width: 430px;
    padding-bottom: 0;
}
i.icon-battery {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: url('../../../assets/img/auto-test.png');
}
.batteryStatistics {
    display: flex;
    .iconArea {
        width: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .statisic-content {
        padding: 0 16px;
    }
}
.totalNumber {
    font-size: 32px;
    margin-top: 10px;
}
.alarmsNumber {
    font-size: 24px;
    color: #ff9852;
}
html.dark {
    .tableBar .evaluate-date {
        color: #a4a7b3;
    }
    .unDel {
        color: #474a59 !important;
    }
    .borderRight {
        border-right: 1px solid #474a59;
    }
}
</style>
