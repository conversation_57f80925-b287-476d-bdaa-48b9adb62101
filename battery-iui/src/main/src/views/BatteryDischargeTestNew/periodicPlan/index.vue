<template>
    <div class="task-view">
        <task-list
            v-if="curPage === 'list'"
            @switchShowPage="switchShowPage"
            @changeCurPage="changeCurPage"
        ></task-list>
        <task-add v-if="curPage === 'add'" :edit-task-id="taskId" @changeCurPage="changeCurPage"></task-add>
        <task-detail v-if="curPage === 'detail'" :task-id="taskId" @changeCurPage="changeCurPage"></task-detail>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import taskAdd from './taskAdd.vue';
import taskDetail from './taskDetail.vue';
import taskList from './taskList.vue';

export default {
    components: {
        taskAdd,
        taskDetail,
        taskList,
    },
    data() {
        return {
            curPage: 'list',
            taskId: '',
        };
    },
    computed: {},
    created() {},
    beforeUnmount() {},
    mounted() {},
    methods: {
        changeCurPage(val, id) {
            this.curPage = val;
            this.taskId = id;
        },
        switchShowPage(page) {
            $emit(this, 'switchShowPage', page);
        },
    },
    emits: ['switchShowPage'],
};
</script>
