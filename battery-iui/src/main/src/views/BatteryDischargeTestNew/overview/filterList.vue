<template>
    <div class="filterWrap" :class="{ dark: isDark }">
        <div>
            <el-checkbox
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
            ></el-checkbox>
            <span class="title">
                {{ $t('batteryOverview.fields.selected') }} / {{ $t('batteryOverview.fields.optional') }}:
            </span>
            <span class="title">{{ selectedNumber }} / {{ optionalNumber }}</span>
            <el-link :underline="false" type="primary" class="buttonReset" @click="handleReset()">
                {{ $t('button.reset') }}
            </el-link>
        </div>
        <div style="padding: 8px 0">
            <el-input
                v-model="searchText"
                class="search-input"
                :placeholder="$t('placeholder.search')"
                autocomplete="on"
                size="mini"
                clearable
            >
                <template v-slot:prefix>
                    <el-icon class="el-input__icon"><el-icon-search /></el-icon>
                </template>
            </el-input>
            <span v-show="searchText.trim() === ''">
                <!-- 置顶 -->
                <span
                    class="direction icon-button plx-ico-top-16"
                    :class="{ disabled: sequenceMin === moveItemSequence }"
                    @click="moveTop"
                ></span>
                <!-- 上移 -->
                <span
                    class="direction icon-button plx-ico-arrow-up-16"
                    :class="{ disabled: sequenceMin === moveItemSequence }"
                    @click="moveUp"
                ></span>
                <!-- 下移动 -->
                <span
                    class="direction icon-button plx-ico-arrow-down-16"
                    :class="{ disabled: sequenceMax === moveItemSequence }"
                    @click="moveDown"
                ></span>
                <!-- 置底 -->
                <span
                    class="direction icon-button plx-ico-bottom-16"
                    :class="{ disabled: sequenceMax === moveItemSequence }"
                    @click="moveBottom"
                ></span>
            </span>
        </div>
        <el-checkbox-group v-model="checkedItem" v-loading="loading">
            <div class="listContent" :style="listStyle">
                <ul v-show="searchText.trim() === ''">
                    <template v-for="item in tableList">
                        <li
                            v-if="(item.id !== 'alarm' || alarmRights) && item.assetAttributeShow"
                            :key="item.id"
                            :class="{ on: moveItemId === item.id }"
                            @click="liClick(item)"
                        >
                            <el-checkbox
                                :label="item.id"
                                :disabled="item.defaultFixed"
                                @change="handleCheckedItemChange($event, item)"
                            >
                                <span @click.prevent="labelClick">{{ item.name }}</span>
                            </el-checkbox>
                        </li>
                    </template>
                </ul>
                <!-- 搜索结果列表 -->
                <ul v-if="searchText">
                    <template v-for="item in tableSearchList">
                        <li v-if="(item.id !== 'alarm' || alarmRights) && item.assetAttributeShow" :key="item.id">
                            <el-checkbox
                                :label="item.id"
                                :disabled="item.defaultFixed"
                                @change="handleCheckedSearchChange($event, item)"
                            >
                                <span @click.prevent="labelClick">{{ item.name }}</span>
                            </el-checkbox>
                        </li>
                    </template>
                </ul>
            </div>
        </el-checkbox-group>
        <div class="bottomBar">
            <el-button type="primary" @click="handleConfirm">
                {{ $t('button.confirm') }}
            </el-button>
            <el-button style="margin-left: 10px" @click="handleCancel">
                {{ $t('button.cancel') }}
            </el-button>
        </div>
    </div>
</template>

<script>
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $emit } from '../../../utils/gogocodeTransfer';
export default {
    components: {
        ElIconSearch,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default: function () {
                return [];
            },
        },
        searchList: {
            type: Array,
            default: function () {
                return [];
            },
        },
        maxHeight: {
            type: Number,
            default: 500,
        },
    },
    data() {
        return {
            checkAll: false,
            isIndeterminate: false,
            selectedNumber: 0, // 已选数量
            optionalNumber: 0, // 可选数量
            selectedNumberSearch: 0, // 已选数量
            optionalNumberSearch: 0, // 可选数量
            searchText: '',
            tableList: [], // 总的列表数据
            tableSearchList: [], // 搜索列表数据
            checkedItem: [], // 已选数据
            sequenceMin: -1, // 列表最小序号
            sequenceMax: -1, // 列表最大序号
            moveItem: {}, // 需要移动的元素
        };
    },
    computed: {
        moveItemId() {
            return this.moveItem.id || '';
        },
        moveItemSequence() {
            return this.moveItem.sequence || '';
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        listStyle() {
            return {
                'max-height': this.maxHeight + 'px',
            };
        },
        alarmRights() {
            return this.rights['operation.fm.currentAlarm'];
        },
    },
    watch: {
        searchText(val) {
            $emit(this, 'search', val);
        },
        searchList() {
            this.updateSearchList();
        },
    },
    created() {
        this.updateList();
    },
    mounted() {},
    methods: {
        // 初始化列表数据
        updateList(list) {
            let table = [];
            this.checkedItem = [];
            this.moveItem = {};
            this.sequenceMin = -1;
            this.sequenceMax = -1;
            this.optionalNumber = 0;
            this.selectedNumber = 0;
            let listDate = this.list;
            if (list) {
                listDate = list;
            }
            for (let i = 0; i < listDate.length; i++) {
                let item = listDate[i];
                table.push(item);
                if (item.enable) {
                    this.checkedItem.push(item.id);
                }
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    if (this.sequenceMin === -1) {
                        this.sequenceMin = item.sequence;
                    }
                    this.sequenceMax = item.sequence;
                    this.optionalNumber++;
                    if (item.enable) {
                        this.selectedNumber++;
                    }
                }
            }
            this.tableList = table;
            this.isIndeterminate = this.selectedNumber > 0 && this.selectedNumber < this.optionalNumber;
            this.checkAll = this.selectedNumber === this.optionalNumber && this.optionalNumber !== 0;
        },
        // 更新搜索的列表数据
        updateSearchList() {
            this.tableSearchList = [];
            this.selectedNumberSearch = 0;
            this.optionalNumberSearch = 0;
            for (let i = 0; i < this.searchList.length; i++) {
                let item = this.searchList[i];
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    this.optionalNumberSearch++;
                    if (this.checkedItem.indexOf(item.id) >= 0) {
                        item.enable = true;
                        this.selectedNumberSearch++;
                    } else {
                        item.enable = false;
                    }
                }
                this.tableSearchList.push(item);
            }
            this.isIndeterminate =
                this.selectedNumberSearch > 0 && this.selectedNumberSearch < this.optionalNumberSearch;
            this.checkAll = this.selectedNumberSearch === this.optionalNumberSearch && this.optionalNumberSearch !== 0;
        },
        // 全选
        handleCheckAllChange(val) {
            if (this.searchText) {
                // 搜索列表显示时
                let ids = this.tableSearchList.map(item => {
                    if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                        return item.id;
                    }
                });
                this.updateListCheck(val, ids);
            } else {
                this.setcheckedAll(val);
            }
            this.isIndeterminate = false;
        },
        // 设置全选和全不选效果
        setcheckedAll(isAll) {
            this.selectedNumber = 0;
            this.checkedItem = [];
            for (let i = 0; i < this.tableList.length; i++) {
                let item = this.tableList[i];
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    if (isAll) {
                        item.enable = true;
                        this.selectedNumber++;
                    } else {
                        item.enable = false;
                    }
                }
                if (item.enable) {
                    this.checkedItem.push(item.id);
                }
            }
        },
        // 设置搜索列表全选和全不选效果
        updateListCheck(isCheck, ids) {
            this.checkedItem = [];
            this.selectedNumber = 0;
            for (let i = 0; i < this.tableList.length; i++) {
                let item = this.tableList[i];
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    if (ids.indexOf(item.id) >= 0) {
                        item.enable = isCheck;
                    }
                    if (item.enable) {
                        this.selectedNumber++;
                    }
                }
                if (item.enable) {
                    this.checkedItem.push(item.id);
                }
            }
        },
        // 单项选择
        handleCheckedItemChange(val, item) {
            if (val) {
                item.enable = true;
                this.selectedNumber++;
            } else {
                item.enable = false;
                this.selectedNumber--;
            }
            this.checkAll = this.selectedNumber === this.optionalNumber;
            this.isIndeterminate = this.selectedNumber > 0 && this.selectedNumber < this.optionalNumber;
        },
        // 搜索列表的单项选择
        handleCheckedSearchChange(val, item) {
            this.updateListCheck(val, [item.id]);
            /* Started by AICoder, pid:c337cdce223043a19af2b4c0f1e0af60 */
            val ? this.selectedNumberSearch++ : this.selectedNumberSearch--;
            /* Ended by AICoder, pid:c337cdce223043a19af2b4c0f1e0af60 */
            this.checkAll = this.selectedNumberSearch === this.optionalNumberSearch;
            this.isIndeterminate =
                this.selectedNumberSearch > 0 && this.selectedNumberSearch < this.optionalNumberSearch;
        },
        // 重置
        handleReset() {
            this.searchText = '';
            if (this.tableList.length > 0) {
                this.tableList.forEach(d => {
                    d.sequence = d.defaultIndex;
                    d.enable = d.defaultEnable;
                });
                this.tableList.sort((a, b) => a.sequence - b.sequence);
                this.updateList(this.tableList);
            }
        },
        handleConfirm() {
            $emit(this, 'filterOk', this.tableList);
        },
        handleCancel() {
            $emit(this, 'filterCancel');
        },
        labelClick() {},
        liClick(item) {
            if (item.defaultFixed) {
                return;
            }
            if (this.moveItemId === item.id) {
                this.moveItem = {};
            } else {
                this.moveItem = Object.assign({}, item);
            }
        },
        moveTop() {
            if (this.sequenceMin === this.moveItemSequence || this.tableList.length === 0) {
                return;
            }
            let indexCurrent = this.sequenceMin;
            let isMove = false;
            this.tableList.forEach(item => {
                if (item.defaultFixed || (item.id === 'alarm' && !this.alarmRights) || !item.assetAttributeShow) {
                    indexCurrent = item.sequence + 1;
                } else {
                    if (!isMove) {
                        indexCurrent++;
                        isMove = true;
                    }
                    if (item.id === this.moveItemId) {
                        item.sequence = this.sequenceMin;
                        this.moveItem.sequence = this.sequenceMin;
                    } else {
                        item.sequence = indexCurrent;
                        indexCurrent++;
                    }
                }
            });
            this.tableList.sort((a, b) => a.sequence - b.sequence);
        },
        moveBottom() {
            if (this.sequenceMax === this.moveItemSequence || this.tableList.length === 0) {
                return;
            }
            let indexCurrent = this.sequenceMin;
            this.tableList.forEach(item => {
                if (item.defaultFixed || (item.id === 'alarm' && !this.alarmRights) || !item.assetAttributeShow) {
                    indexCurrent = item.sequence + 1;
                } else {
                    if (item.id === this.moveItemId) {
                        item.sequence = this.sequenceMax;
                        this.moveItem.sequence = this.sequenceMax;
                    } else {
                        item.sequence = indexCurrent;
                        indexCurrent++;
                    }
                }
            });
            this.tableList.sort((a, b) => a.sequence - b.sequence);
        },
        moveUp() {
            if (this.moveItemSequence === this.sequenceMin || this.tableList.length === 0) {
                return;
            }
            let isMove = false;
            let prevItem = {};
            this.tableList.forEach(item => {
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    if (item.id === this.moveItemId) {
                        isMove = true;
                        item.sequence = prevItem.sequence;
                        prevItem.sequence = this.moveItemSequence;
                        this.moveItem.sequence = item.sequence;
                    } else {
                        if (!isMove) {
                            prevItem = item;
                        }
                    }
                }
            });
            this.tableList.sort((a, b) => a.sequence - b.sequence);
        },
        moveDown() {
            if (this.moveItemSequence === this.sequenceMax || this.tableList.length === 0) {
                return;
            }
            let isMove = false;
            let moveItem = {};
            this.tableList.forEach(item => {
                if (!item.defaultFixed && (item.id !== 'alarm' || this.alarmRights) && item.assetAttributeShow) {
                    if (item.id === this.moveItemId) {
                        isMove = true;
                        moveItem = item;
                    } else {
                        if (isMove) {
                            isMove = false;
                            moveItem.sequence = item.sequence;
                            item.sequence = this.moveItemSequence;
                            this.moveItem.sequence = moveItem.sequence;
                        }
                    }
                }
            });
            this.tableList.sort((a, b) => a.sequence - b.sequence);
        },
    },
    emits: ['search', 'filterOk', 'filterCancel'],
};
</script>

<style lang="scss" scoped>
.listContent {
    min-height: 200px;
    overflow: auto;
}
.buttonReset {
    float: right;
    font-size: 12px;
}
.title {
    font-size: 12px;
    color: #404040;
}
.search-input {
    width: 200px;
    :deep(.el-input__inner) {
        width: 200px;
        height: 34px;
        line-height: 34px;
    }
}
.bottomBar {
    margin: 16px 0 0;
}
.listContent li {
    padding: 8px 0;
    border-bottom: solid 1px #d9d9d9;
    &.on {
        background-color: #ecf5ff;
    }
}
.listContent li:first-child {
    border-top: solid 1px #d9d9d9;
}
.direction {
    margin-left: 10px;
    vertical-align: middle;
}
.dark {
    .title {
        color: #d9d9d9;
    }
    .listContent li {
        border-color: #474a59;
        :deep(.el-checkbox__label) {
            color: #a4a7b3;
        }
        :deep(.is-disabled) .el-checkbox__inner {
            background-color: #2a2c38;
            border-color: #474a59;
        }
        :deep(.el-checkbox__input).is-disabled.is-checked .el-checkbox__inner::after {
            border-color: #474a59;
        }
        &.on {
            background-color: #2a2c38;
        }
    }
}
</style>
