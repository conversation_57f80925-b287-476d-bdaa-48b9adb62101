<template>
    <div v-loading="exportLoading" :style="{ height: height + 'px' }" class="overview" @click="closePop($event)">
        <div style="min-width: 1000px">
            <!-- 顶部查询和导出按钮 -->
            <div ref="filterTool" class="filterTool uedm-navigation">
                <path-breadcrumb :lists="pathNames"></path-breadcrumb>
                <div class="filterButtons">
                    <el-button v-if="rights['battery.test.task.view']" @click="switchShowPage('automaticTest')">
                        {{ $t('batteryOverview.fields.automaticTest') }}
                    </el-button>
                    <span class="export-wrap" v-if="authorized">
                        <span class="filterItem">
                            <el-tooltip class="item" effect="dark" :content="$t('button.filter')" placement="bottom">
                                <span
                                    class="icon-button filterIconBtn"
                                    :class="{ on: hasFilter }"
                                    @click.stop="handleShowQuery"
                                ></span>
                            </el-tooltip>
                            <!-- 顶部查询条件内容 -->
                            <div class="filterContent">
                                <div v-show="showQuery" ref="popQuery" class="filterPop popQuery">
                                    <div class="inner">
                                        <pop-queryForm @query="handleQuery"></pop-queryForm>
                                    </div>
                                </div>
                            </div>
                        </span>
                        <span v-if="rights['battery.test.history.export']" class="filterItem">
                            <el-tooltip class="item" effect="dark" :content="$t('button.export')" placement="bottom">
                                <span class="icon-button plx-ico-export-16" @click="handleExport"></span>
                            </el-tooltip>
                        </span>
                    </span>
                </div>
            </div>
            <!-- 开关电源数量统计 -->
            <div class="space" v-if="authorized">
                <div v-loading="statisticsLoading" class="number-statics flexs">
                    <div style="margin-right: 8px" class="el-table info-padding uedm-content-area">
                        <div class="info-part borderRight">
                            <i class="typeIcon icon-battery"></i>
                            <!-- Started by AICoder, pid:610e4tb34c28123142a508c6a0efa001a287b0bd -->
                            <el-tooltip
                                effect="dark"
                                :content="$t('statistics.powerSupplyEquipment')"
                                placement="bottom"
                            >
                                <span class="name">{{ $t('statistics.powerSupplyEquipment') }}</span>
                            </el-tooltip>
                            <!-- Ended by AICoder, pid:610e4tb34c28123142a508c6a0efa001a287b0bd -->
                        </div>
                        <div class="info-part borderRight">
                            <span class="info-title">{{ $t('solarPower.total') }}</span>
                            <span class="info-data truncate">
                                {{ (batteryStatistics.total && batteryStatistics.total.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.tested') }}</span>
                            <span class="info-data">
                                {{ (batteryStatistics.tested && batteryStatistics.tested.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.manualTest') }}</span>
                            <span class="info-data">
                                {{ (batteryStatistics.manual && batteryStatistics.manual.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.automaticTest') }}</span>
                            <span class="info-data">
                                {{ (batteryStatistics.auto && batteryStatistics.auto.number) || '--' }}
                            </span>
                        </div>
                    </div>
                    <!-- Started by AICoder, pid:950a4900d4qe47d140060b4cd0745f3a6a30a3a2 -->
                    <div style="margin-left: 8px" class="el-table info-padding supervisory-control">
                        <div class="info-part borderRight">
                            <i class="typeIcon icon-battery"></i>
                            <!-- Started by AICoder, pid:r519du16f6q3d1e14cc408149065360984f3740c -->
                            <el-tooltip effect="dark" :content="$t('standby.fields.batteryPack')" placement="bottom">
                                <span class="name">{{ $t('standby.fields.batteryPack') }}</span>
                            </el-tooltip>
                            <!-- Ended by AICoder, pid:r519du16f6q3d1e14cc408149065360984f3740c -->
                        </div>
                        <div class="info-part borderRight">
                            <span class="info-title">{{ $t('solarPower.total') }}</span>
                            <span class="info-data truncate">
                                {{ (batteryPack.total && batteryPack.total.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.tested') }}</span>
                            <span class="info-data">
                                {{ (batteryPack.tested && batteryPack.tested.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.manualTest') }}</span>
                            <span class="info-data">
                                {{ (batteryPack.manual && batteryPack.manual.number) || '--' }}
                            </span>
                        </div>
                        <div class="info-part">
                            <span class="info-title">{{ $t('batteryOverview.fields.automaticTest') }}</span>
                            <span class="info-data">
                                {{ (batteryPack.auto && batteryPack.auto.number) || '--' }}
                            </span>
                        </div>
                    </div>
                    <!-- Ended by AICoder, pid:950a4900d4qe47d140060b4cd0745f3a6a30a3a2 -->
                </div>
                <!-- 图表 -->
                <el-row :gutter="16" style="overflow-x: hidden">
                    <el-col :span="12">
                        <proportion-part
                            ref="proportion"
                            :logic-id="nodeId"
                            :query-parameter="queryParameter"
                            :div-chart-width-change="divChartWidthChange"
                            @stopLoop="handleStopLoop"
                            @startLoop="handleStartLoop"
                        ></proportion-part>
                    </el-col>
                    <el-col :span="12">
                        <influence-trend
                            ref="trend"
                            :logic-id="nodeId"
                            :query-parameter="queryParameter"
                            @stopLoop="handleStopLoop"
                            @startLoop="handleStartLoop"
                        ></influence-trend>
                    </el-col>
                </el-row>
                <list-statistics
                    ref="list"
                    :logic-id="nodeId"
                    :query-parameter="queryParameter"
                    @manualTest="manualTest"
                    @switchShowPage="switchShowPage"
                ></list-statistics>
            </div>

            <div v-if="!authorized" class="no-access">
                <div class="pageBlankTips">
                    <div class="pageBlankTipsContent">
                        <img src="@/assets/img/no-permission.png" />
                        <div class="txt">{{ $t('battery.tipMessage.accessDescription') }}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 临时性放电测试 -->
        <el-dialog
            :title="testTitle"
            v-model="manualTestIsShow"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <temporary-test
                v-if="powerSourceIsShow"
                :test-table-data="selectedArr"
                @temporaryClose="temporaryClose"
                @changeTestTitle="changeTestTitle"
            ></temporary-test>
            <independent-monitoring
                v-else
                ref="independentMonitoring"
                :test-table-data="selectedArr"
                @temporaryClose="temporaryClose"
                @changeTestTitle="changeTestTitle"
            ></independent-monitoring>
        </el-dialog>
    </div>
</template>

<script>
import { $emit } from '../../../utils/gogocodeTransfer';
import { Warning as ElIconWarning } from '@element-plus/icons-vue';
import HTTP from '@/util/httpService.js';
import QueryForm from './testQueryForm.vue';
import proportionPart from './proportionPart.vue';
import influenceTrend from './influenceTrend.vue';
import ListStatistics from './listStatistics.vue';
import TemporaryTest from './temporaryTest.vue';
import IndependentMonitoring from './independentMonitoring.vue';
import axios from 'axios';
import PathBreadcrumb from '@uedm/uedm-ui/src/components/pathBreadcrumb.vue';
const OFFLINE_CODE = '1';
export default {
    components: {
        ElIconWarning,
        'pop-queryForm': QueryForm,
        'influence-trend': influenceTrend, // 影响趋势
        'temporary-test': TemporaryTest,
        'independent-monitoring': IndependentMonitoring,
        'list-statistics': ListStatistics,
        'proportion-part': proportionPart, // 占比
        'path-breadcrumb': PathBreadcrumb,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        height: {
            type: Number,
            default: null,
        },
        authorized: {
            type: Boolean,
            default: true,
        },
        nodeId: {
            type: String,
            default: null,
        },
        pathNames: {
            type: Array,
            default: function () {
                return [];
            },
        },
        widthChange: {
            type: Boolean,
            default: false,
        },
        divChartWidthChange: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            queryParameter: {},
            showQuery: false,
            loading: false,
            exportLoading: false,
            statisticsLoading: false, // 顶部概览
            total: '--',
            alarms: '--',
            timer: false,
            manualTestIsShow: false, // 批量测试弹窗
            selectedArr: [], // 表格复选数组
            batteryStatistics: {},
            batteryPack: {},
            type: OFFLINE_CODE,
            testTitle: this.$t('batteryTest.title.testConfirm'),
        };
    },
    computed: {
        hasFilter() {
            let tag = false;
            let queryParameter = this.queryParameter;
            for (let key in queryParameter) {
                if (['deliveryEndTime', 'deliveryStartTime'].includes(key)) {
                    if (queryParameter[key]) {
                        tag = true;
                    }
                } else if (queryParameter[key] && queryParameter[key].length) {
                    tag = true;
                }
            }
            return tag;
        },
        powerSourceIsShow() {
            return this.type === OFFLINE_CODE;
        },
    },
    watch: {
        widthChange() {
            if (this.$refs.proportion && this.$refs.proportion.chartResize) {
                this.$refs.proportion.chartResize();
            }
            if (this.$refs.trend && this.$refs.trend.chartResize) {
                this.$refs.trend.chartResize();
            }
        },
    },
    created() {},
    mounted() {
        this.loopPlay();
    },
    beforeUnmount() {
        this.stopPlay();
    },
    methods: {
        // 顶部概览查询
        getTestStatisticData() {
            this.statisticsLoading = true;
            let objVal = JSON.parse(JSON.stringify(this.queryParameter));
            objVal.statisticsDims = ['total', 'tested', 'manual', 'auto'];
            objVal.logicGroupId = this.nodeId;
            HTTP.request('getTestStatisticData', {
                method: 'post',
                data: objVal,
                complete: data => {
                    if (data.code === 0) {
                        /* Started by AICoder, pid:c5536tdb0defec214b910bafc089b814dae154f4 */
                        for (let k in data.data.batteryPack) {
                            if (data.data.batteryPack[k] && data.data.batteryPack[k].number === 0) {
                                data.data.batteryPack[k].number = '0';
                            }
                        }

                        for (let k in data.data.powerSupplyEquipment) {
                            if (data.data.powerSupplyEquipment[k] && data.data.powerSupplyEquipment[k].number === 0) {
                                data.data.powerSupplyEquipment[k].number = '0';
                            }
                        }
                        /* Ended by AICoder, pid:c5536tdb0defec214b910bafc089b814dae154f4 */
                        this.batteryStatistics = data.data.powerSupplyEquipment;
                        this.batteryPack = data.data.batteryPack;
                    }
                    this.statisticsLoading = false;
                },
                error: () => {
                    this.statisticsLoading = false;
                },
            });
        },
        loopPlay() {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setInterval(() => {
                if (this.$refs.proportion && this.$refs.proportion.$refs.proportionCard) {
                    this.$refs.proportion.$refs.proportionCard.next();
                }
                if (this.$refs.trend && this.$refs.trend.$refs.trendCard) {
                    this.$refs.trend.$refs.trendCard.next();
                }
            }, 5000);
        },
        stopPlay() {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
        },
        handleShowQuery() {
            this.showQuery = !this.showQuery;
        },
        // 导出
        async handleExport() {
            this.exportLoading = true;
            let tips = this.$message({
                message: this.$t('tipMessage.exportTimeLongTip'),
                showClose: true,
                duration: 0,
                customClass: 'blue-info',
            });
            let queryParameter = Object.assign({}, this.queryParameter);

            queryParameter.logicGroupId = this.nodeId;
            /* Started by AICoder, pid:vaf467259bt7f6514ae70ba2d0d7340409c8ddfa */
            const obj = await this.$refs.list.getSupplementaryParameter();
            if (obj && typeof obj === 'object' && Object.keys(obj).length > 0) {
                queryParameter = {
                    ...queryParameter,
                    overviewDims: obj.overviewDims?.filter(i => i.enable),
                    overviewBattSetDims: obj.overviewBattSetDims?.filter(i => i.enable),
                };
            }
            /* Ended by AICoder, pid:vaf467259bt7f6514ae70ba2d0d7340409c8ddfa */
            queryParameter.name = this.$refs.list.searchVal; // 列表过滤名称
            queryParameter.order = this.$refs.list.order; // 列表排序
            queryParameter.sort = this.$refs.list.sort; // 列表排序
            queryParameter.position = this.pathNames.join('/');
            queryParameter.statisticsDims = ['total', 'tested', 'manual', 'auto'];

            let assetDate = {
                assetdims: [],
                images: [],
            }; // 资产维度过滤条件
            if (this.$refs.proportion && this.$refs.proportion.getChartData) {
                let assetD = this.$refs.proportion.getChartData();
                assetDate.assetdims = assetD.assetdims || [];
                assetDate.images = assetD.images || [];
            }
            let workingDate = {
                workCoditionDims: [],
                images: [],
            }; // 工况维度过滤条件
            if (this.$refs.trend && this.$refs.trend.getChartData) {
                let workingD = this.$refs.trend.getChartData();
                workingDate.images = workingD.images || [];
            }
            queryParameter.images = [...assetDate.images, ...workingDate.images];
            const DOWNLOAD_URL = '/api/battery-manager/v1/battery-test/overview/export';
            const forgerydefense = window.forgerydefense || '';
            const languageOption = (
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) ||
                ''
            ).replace('-', '_');
            /* Started by AICoder, pid:28f93z7f95ibc37140630b4270c1370a01189cbc */
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            /* Ended by AICoder, pid:28f93z7f95ibc37140630b4270c1370a01189cbc */
            axios
                .post(url, queryParameter, config)
                .then(res => {
                    this.exportLoading = false;
                    tips.close();
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                if (result.code === -301) {
                                    this.$message({
                                        message: this.$t('tipMessage.cannotNull') + ': ' + result.error,
                                        duration: 5000,
                                        showClose: true,
                                        type: 'error',
                                    });
                                } else if (result.code === -304) {
                                    // 超出可选范围
                                    this.$message({
                                        message: this.$t('tipMessage.beyondOptionalRange') + ': ' + result.error,
                                        duration: 5000,
                                        showClose: true,
                                        type: 'error',
                                    });
                                } else if (result.code === -307) {
                                    // 超出可选范围
                                    this.$message({
                                        message: this.$t('tipMessage.cannotNull') + ': ' + result.error,
                                        duration: 5000,
                                        showClose: true,
                                        type: 'error',
                                    });
                                } else {
                                    let message = this.$message({
                                        message:
                                            this.$t('tipMessage.exportError') + ': ' + result.message || result.error,
                                        duration: 5000,
                                        showClose: true,
                                        type: 'error',
                                    });
                                    this.$message.error(message);
                                }
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        let blob = new Blob([res.data]);
                        let url = window.URL.createObjectURL(blob); // 创建下载的链接
                        let link = document.createElement('a');
                        let fileName = '';
                        if (res.headers['content-disposition']) {
                            let contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                })
                .catch(() => {
                    this.exportLoading = false;
                    tips.close();
                });
        },
        handleQuery(queryParameter) {
            // 顶部查询条件点查询事件
            this.queryParameter = queryParameter;
            this.getTestStatisticData(); // 概览数据查询
            this.showQuery = false;
        },
        switchShowPage(page, val, type) {
            // if (this.$refs.proportion) {
            //     this.$refs.proportion.hide();
            // }
            // if (this.$refs.trend) {
            //     this.$refs.trend.hide();
            // }
            // if (this.$refs.list) {
            //     this.$refs.list.hide();
            // }
            this.type = type;
            $emit(this, 'switchShowPage', page, val, type);
        },
        // 批量手动测试页面出现
        manualTest(arr, type) {
            this.selectedArr = arr;
            this.type = type;
            this.manualTestIsShow = true;
            this.$nextTick(() => {
                if (this.type !== OFFLINE_CODE) {
                    this.$refs.independentMonitoring.getCheck();
                }
            });
        },
        changeTestTitle() {
            this.testTitle = this.$t('batteryTest.title.powerTestResult');
        },
        temporaryClose(val) {
            this.manualTestIsShow = false;
            // 请求成功操作，不成功或者无操作则保持现状
            if (val === 'success' && this.$refs.list) {
                this.$refs.list.clearSelection(); // 清空表格选项
                this.$refs.list.getTableData(); // 刷新表格
            }
        },
        closePop(ev) {
            if (this.$refs.popQuery && !this.$refs.popQuery.contains(ev.target)) {
                this.showQuery = false;
            }
        },
        handleStartLoop() {
            this.loopPlay();
        },
        handleStopLoop() {
            this.stopPlay();
        },
    },
    emits: ['switchShowPage'],
};
</script>

<style lang="scss" scoped>
.overview {
    overflow: auto;
}
.filterTool {
    position: relative;
    .filterButtons {
        position: absolute;
        right: 16px;
        top: 13px;
    }
}
i.icon-battery {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: url('../../../assets/img/test.png');
}
.number-statics :deep(.el-table)::before {
    height: 0;
}
.flexs {
    display: flex;
    justify-content: space-between;
}
.info-padding {
    display: flex;
    height: 100px;
    width: 100%;
    padding: 19px 0 24px;
    .info-part {
        display: inline-block;
        height: 65px;
        padding: 0 20px;
        position: relative;
        line-height: 20px;
        .info-title {
            color: #404040;
            font-size: 14px;
            font-weight: 700;
            white-space: nowrap;
            display: inline-block;
        }
        .info-data {
            display: block;
            color: #404040;
            font-size: 24px;
            line-height: 58px;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .name {
            display: block;
            color: #404040;
            font-size: 12px;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .typeIcon {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-position: center bottom;
            background-repeat: no-repeat;
        }
        ~ .info-part {
            padding: 0 14px 0 24px;
        }
    }
    .borderRight {
        border-right: 1px solid #ebeef5;
    }
    .info-part:nth-child(1) {
        text-align: center;
        min-width: 60px;
    }
}
:deep(.el-icon-refresh-right) {
    color: #595959;
}
/* Started by AICoder, pid:3d08e7a44bf843dfaa8d8f875e4b80ab */
:deep(.uedm-breadcrumbs) {
    width: 90%;
}
/* Ended by AICoder, pid:3d08e7a44bf843dfaa8d8f875e4b80ab */
html.dark {
    .borderRight {
        border-right: 1px solid #474a59;
    }
    .info-part span.info-data {
        color: #a4a7b3;
    }
    .info-padding .info-part .name,
    .info-part span.info-title {
        color: #d9d9d9;
    }
    :deep(.el-icon-refresh-right) {
        color: #a4a7b3;
    }
    .supervisory-control {
        background-color: #141414;
    }
}
/* Started by AICoder, pid:s25a6c9136b482f14bf30a33a09a5107acd4eaa6 */
.no-access{
    height: calc(100vh - 53px);
    img{
        width:250px;
    }
    .txt{
        font-size: 14px;
        margin-top:10px;
    }
}
/* Ended by AICoder, pid:s25a6c9136b482f14bf30a33a09a5107acd4eaa6 */
.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 36px !important;
    max-width: 200px;
}
</style>
