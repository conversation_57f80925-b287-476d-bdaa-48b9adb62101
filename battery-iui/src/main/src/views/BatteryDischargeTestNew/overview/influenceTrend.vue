<template>
    <div ref="box" :class="{ containerBg: !isDark }" class="uedm-content-area" style="margin-top: 14px;">
        <div style="position: relative;">
            <div style="font-size: 16px" v-show="!isFullScreen" class="uedm-title-tab">
                <span>{{ $t('batteryTest.fields.trend') }}</span>
            </div>
            <div class="filterButtons">
                <el-radio-group v-if="!isFullScreen" v-model="shortcutTime" class="time-select">
                    <el-radio-button label="5" @click="clickShortcutTime(30, 'day')">
                        {{ $t('common.1month') }}
                    </el-radio-button>
                    <el-radio-button :class="{ isFocus: isFocus }" label="4">
                        {{ $t('common.Self-Defined') }}
                    </el-radio-button>
                </el-radio-group>
                <el-button
                    v-if="!isFullScreen"
                    class="hide-input"
                    :style="{ width: ($i18n.locale == 'zh-CN' ? 70 : 80) + 'px' }"
                    @click="
                        shortcutTime = '4';
                        isFocus = false;
                    "
                    @mouseenter="setFocus(true)"
                    @mouseleave="setFocus(false)"
                >
                    <el-date-picker
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                        v-model="definedTime"
                        type="daterange"
                        range-separator="-"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :unlink-panels="true"
                    ></el-date-picker>
                </el-button>
                <span class="filterItem">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('batteryOverview.fields.fullScreen')"
                        placement="bottom"
                    >
                        <span v-show="!isFullScreen" class="icon-button plx-ico-full-screen-16" @click="fullScreen"></span>
                    </el-tooltip>
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('batteryOverview.fields.restore')"
                        placement="bottom"
                    >
                        <span v-show="isFullScreen" style="z-index: 100" class="icon-button plx-ico-reduction-16" @click="restore"></span>
                    </el-tooltip>
                </span>
                <span v-if="!isFullScreen" class="filterItem">
                    <column-filter
                        ref="columnFilter"
                        :value="filterList"
                        :isDark="isDark"
                        :tip="$t('tooltip.displayedItems')"
                        :is-dark="isDark"
                        @save="handleFilterOk"
                    ></column-filter>
                </span>
            </div>
        </div>
        <div class="z-block">
            <div v-loading="loading" class="z-block-content">
                <el-carousel
                    v-if="showCard && carouselFilterList.length"
                    ref="trendCard"
                    :height="carouselHeight"
                    :autoplay="false"
                    :arrow="arrowShow"
                    trigger="click"
                    :class="{ len1: carouselFilterList.length === 1 }"
                    @mouseenter="handleMouseEnter"
                    @mouseleave="handleMouseLeave"
                >
                    <el-carousel-item v-for="item in carouselFilterList" :key="item.id">
                        <div :id="'echart_' + item.id" class="echartsItem" :style="{ height: carouselHeight }"></div>
                    </el-carousel-item>
                </el-carousel>
                <div
                    v-if="!carouselFilterList.length"
                    class="no-data"
                    :style="{ height: 270 + 'px', 'line-height': 270 + 'px' }"
                >
                    <span>{{ $t('common.noData') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
import { CHART_COLORS, CHART_HANDLEICON, getLineNodata } from '@uedm/uedm-ui/src/util/constants.js';

export default {
    data() {
        return {
            loading: false,
            // 为了解决调整显示顺序后轮播图的指示器位置更新，不然轮播图顺序看上去没有更新
            showCard: true,
            isFullScreen: false,
            filterLoading: false,
            // 过滤条件初始列表
            filterList: [],
            // 过滤条件列表数据
            filterSearchList: [],
            // 轮播图列表数据
            carouselFilterList: [],
            // 所有图表显示数据
            statisticsList: [],
            // 所有图表实例
            chartsList: [],
            chartLegend: [],
            xAxisData: [],
            series: [],
            seriesMap: {},
            colorMap: {
                normal: '#7cd180',
                deficiency: '#ffc850', // 不足
                unEvaluate: '#bfbfbf', // 无法评估
                healthy: '#7cd180',
                subhealth: '#4bd3f3', // 亚健康
                exception: '#ffc850', // 异常
            },
            shortcutTime: '5',
            // 右y轴最大值
            rightMaxNum: 10,
            // 左y轴最大值
            leftMaxNum: 10,
            // 左y轴最小值
            leftMinNum: -10,
            isFocus: false,
            // 自定义时间
            startTime: '',
            endTime: '',
            definedTime: '',
        };
    },
    components: {
        'column-filter': ColumnFilter,
    },
    props: {
        logicId: {
            type: String,
            default: '',
        },
        queryParameter: {
            type: Object,
            default() {
                return {};
            },
        },
        divChartWidthChange: {
            type: Number,
            default: 0,
        },
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        carouselHeight() {
            return this.isFullScreen ? 'calc(100vh - 56px - 34px)' : '270px';
        },
        winResize() {
            return this.$store.getters.getResize;
        },
        arrowShow() {
            let type = 'never';
            if (this.carouselFilterList.length > 1) {
                type = 'always';
            }
            return type;
        },
        // 柱状图柱子太多，大于4条数据，就要产生dataZoom
        barZoomShow() {
            return this.xAxisData.length > 4;
        },
    },
    watch: {
        queryParameter: {
            handler: function () {
                if (this.filterList.length > 0) {
                    this.getStatistics();
                }
            },
            deep: true,
        },
        '$i18n.locale'() {
            this.initCarousel();
        },
        winResize: function () {
            this.chartResize();
        },
        divChartWidthChange() {
            this.chartResize();
        },
        definedTime(val) {
            if (val) {
                this.startTime = val[0] || '';
                this.endTime = val[1] || '';
                this.getStatistics();
            }
        },
    },
    created() {},
    mounted() {
        this.clickShortcutTime(30, 'day');
        this.getFilterList();
        document.addEventListener('fullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('mozfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('webkitfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('msfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
    },
    beforeUnmount() {
        for (let i = 0; i < this.chartsList.length; i++) {
            this.chartsList[i].dispose();
        }
    },
    methods: {
        fullScreen() {
            let element = this.$refs.box;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullScreen) {
                element.webkitRequestFullScreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        },
        restore() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitCancelFullScreen) {
                document.webkitCancelFullScreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document?.msExitFullscreen) {
                document.msExitFullscreen();
            }
        },
        /**
         * 查询影响趋势维度列表
         */
        getFilterList() {
            this.filterLoading = true;
            HTTP.request('getTestTrendSelect', {
                method: 'get',
                complete: data => {
                    this.filterLoading = false;
                    if (data.code === 0) {
                        this.filterList = data.data;
                        this.carouselFilterList = this.filterList.filter(i => i.enable && i.assetAttributeShow);
                        this.getStatistics();
                    }
                },
                error: () => {
                    this.filterLoading = false;
                },
            });
        },
        /**
         * 保存影响趋势维度列表
         */
        handleFilterOk(tableList) {
            HTTP.request('getTestTrendUpdate', {
                method: 'put',
                data: tableList.map(i => {
                    return {
                        id: i.id,
                        sequence: i.sequence,
                        enable: i.enable,
                    };
                }),
                complete: data => {
                    if (data.code === 0) {
                        this.showCard = false;
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.filterList = tableList;
                        this.carouselFilterList = this.filterList.filter(i => i.enable && i.assetAttributeShow);
                        this.$nextTick(() => {
                            this.showCard = true;
                            this.getStatistics();
                        });
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotRange') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotModify') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {},
            });
        },
        // 影响趋势数据查询
        getStatistics() {
            this.loading = true;
            HTTP.request('getTestTrendData', {
                method: 'post',
                data: {
                    logicGroupId: this.logicId,
                    impactDims: this.carouselFilterList.map(i => i.id),
                    startTime: this.startTime,
                    endTime: this.endTime,
                    ...this.queryParameter,
                },
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        this.statisticsList = data.data;
                    } else {
                        this.statisticsList = [];
                    }
                    this.initCarousel();
                },
                error: () => {
                    this.loading = false;

                    this.initCarousel();
                },
            });
        },
        initCarousel() {
            for (let index = 0; index < this.carouselFilterList.length; index++) {
                const nowSlectedItem = this.carouselFilterList[index];
                if (document.getElementById('echart_' + nowSlectedItem.id)) {
                    let myEchart = this.$echarts.init(
                        document.getElementById('echart_' + nowSlectedItem.id),
                        this.isDark ? 'dark' : 'light',
                        270
                    );
                    myEchart.clear();
                    let options = {};
                    const nowItemData = this.statisticsList.find(i => i.testImpactDim === nowSlectedItem.id);
                    let a = JSON.parse(JSON.stringify(nowItemData));
                    options = this.getChartOption(nowItemData, nowSlectedItem.name || '');
                    myEchart.setOption(options);
                    this.chartsList.push(myEchart);
                    // 监听父级dom变化
                    let resizeOb = new ResizeObserver(entries => {
                        for (let entry of entries) {
                            if (this.$echarts.getInstanceByDom(entry.target)) {
                                this.$echarts.getInstanceByDom(entry.target).resize();
                            }
                        }
                    });
                    resizeOb.observe(document.getElementById(`echart_${nowSlectedItem.id}`));
                }
            }
        },
        getChartOption(data, title) {
            this.handleData(data);
            const handleIcon =
                'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
            const colorType = this.isDark ? 'dark' : 'default';
            let statistics = data ? data.statistic || [] : [];
            let options = {};
            if (!this.xAxisData || !this.xAxisData.length) {
                options = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '100px' : '50px',
                        right: this.$i18n.locale === 'en-US' ? '100px' : '70px',
                        bottom: '22',
                        containLabel: true,
                    },
                    title: {
                        text: (data && data.testImpactDimName) || title,
                        x: 'left',
                        textStyle: {
                            color: CHART_COLORS[colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    xAxis: {
                        name: this.$t('monitor.inspectTool.date'),
                        nameLocation: 'start',
                        nameTextStyle: {
                            align: 'center',
                            verticalAlign: 'top',
                            padding: [10, 0, 0, 0],
                            color: CHART_COLORS[colorType].label,
                        },
                        axisLine: {
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                        type: 'category',
                        data: [],
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: this.$t('batteryTest.fields.increaseDecrease'),
                            nameTextStyle: {
                                align: 'center',
                                color: CHART_COLORS[colorType].label,
                            },
                            show: true,
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: CHART_COLORS[colorType].line,
                                },
                            },
                            axisLabel: {
                                color: CHART_COLORS[colorType].label,
                            },
                        },
                        {
                            type: 'value',
                            position: 'right',
                            name: this.$t('batteryTest.fields.powerSupplyTotal'),
                            nameTextStyle: {
                                align: 'center',
                                color: CHART_COLORS[colorType].label,
                            },
                            show: true,
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: CHART_COLORS[colorType].line,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            type: 'bar',
                            data: [],
                        },
                    ],
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'middle',
                        silent: true,
                        invisible: false, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: CHART_COLORS[colorType].noData,
                            fontWeight: 'bold',
                            text: this.$t('common.noData'),
                            fontFamily: 'Microsoft Yahei',
                            fontSize: '25px',
                        },
                    },
                };
            } else {
                options = {
                    title: {
                        text: (data && data.testImpactDimName) || title,
                        x: 'left',
                        textStyle: {
                            color: CHART_COLORS[colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '50px' : '32px',
                        right: '50px',
                        top: '30%',
                        bottom: this.barZoomShow ? '40' : '12',
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                        },
                        backgroundColor: CHART_COLORS[colorType].background,
                        borderColor: CHART_COLORS[colorType].border,
                        textStyle: { color: CHART_COLORS[colorType].color },
                    },
                    backgroundColor: 'transparent',
                    xAxis: {
                        type: 'category',
                        data: this.xAxisData,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: CHART_COLORS[colorType].line,
                            },
                        },
                        axisTick: {
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            interval: this.xAxisData.length > 10 ? 'auto' : 0,
                            hideOverlap: true,
                            width: 100,
                            overflow: 'truncate',
                            color: CHART_COLORS[colorType].label,
                        },
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: this.$t('batteryTest.fields.increaseDecrease'),
                            nameTextStyle: {
                                align: 'center',
                            },
                            max: this.leftMaxNum,
                            min: this.leftMinNum,
                            splitNumber: 4, // 产生5个左右的间隔
                            splitLine: {
                                show: true, // 去掉折线图中的横线
                            },
                            axisLabel: {
                                color: CHART_COLORS[colorType].label,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: CHART_COLORS[colorType].line,
                                },
                            },
                        },
                        {
                            type: 'value',
                            name: this.$t('batteryTest.fields.powerSupplyTotal'),
                            position: 'right',
                            nameTextStyle: {
                                align: 'center',
                            },
                            max: this.rightMaxNum,
                            min: (this.leftMinNum * this.rightMaxNum) / this.leftMaxNum, // 等比例算出右侧最小值
                            splitNumber: 4,
                            axisLabel: {
                                formatter(value) {
                                    // 隐藏负数
                                    if (value < 0) {
                                        return '';
                                    } else {
                                        return value;
                                    }
                                },
                                color: CHART_COLORS[colorType].label,
                            },
                            splitLine: {
                                show: false, // 去掉折线图中的横线
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: CHART_COLORS[colorType].line,
                                },
                            },
                        },
                    ],
                    dataZoom: [],
                    series: this.series,
                };
                if (this.chartLegend && this.chartLegend.length > 0) {
                    // 图例设置
                    options.legend = {
                        orient: 'horizontal',
                        top: '8%',
                        itemGap: 18,
                        itemWidth: 18,
                        itemHeight: 12,
                        textStyle: {
                            width: '155',
                            overflow: 'breakAll',
                        },
                        data: this.chartLegend,
                    };
                }
                if (this.barZoomShow) {
                    options.dataZoom = [
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            filterMode: 'empty',
                            bottom: 10,
                            height: 24,
                            showDetail: false,
                            zoomLock: false,
                        },
                    ];
                }
            }
            return options;
        },
        handleData(data) {
            this.chartLegend = [];
            this.xAxisData = [];
            this.series = [];
            this.seriesMap = {};
            this.rightMaxNum = 10; // 右y轴最大值
            this.leftMaxNum = 10; // 左y轴最大值
            this.leftMinNum = -10; // 左y轴最小值
            if (data && data.statistic && data.statistic.length) {
                data.statistic.forEach((d, index) => {
                    this.xAxisData.push(d.time);
                    if (d.statisticDetail && d.statisticDetail.length) {
                        d.statisticDetail.forEach(val => {
                            if (index === 0) {
                                this.seriesMap[val.dimTypeId] = {
                                    name: val.dimTypeName,
                                    number: [
                                        {
                                            value: val.number,
                                            itemStyle: {
                                                color: this.colorMap[val.dimTypeId],
                                            },
                                        },
                                    ],
                                };
                            } else {
                                this.seriesMap[val.dimTypeId].number.push({
                                    value: val.number,
                                    itemStyle: {
                                        color: this.colorMap[val.dimTypeId],
                                    },
                                });
                            }
                        });
                    }
                });
                for (let key in this.seriesMap) {
                    if (key === 'testTotal') {
                        this.chartLegend.push({
                            name: this.seriesMap[key].name,
                            icon: '',
                        });
                        // 获取右侧y轴最大值
                        let arr = this.seriesMap[key].number.map(item => {
                            return item.value;
                        });
                        // arr.sort()后，arr不能再用，原来的顺序已经改变; (a, b) => b - a 降序排列
                        let max = arr.sort((a, b) => b - a)[0];
                        if (this.rightMaxNum < max) {
                            this.rightMaxNum = max;
                        }
                        // 成为10的公倍数
                        if (this.rightMaxNum % 10 !== 0) {
                            this.rightMaxNum = Math.ceil(this.rightMaxNum / 10) * 10;
                        }
                        this.series.push({
                            color: '#72bcff',
                            name: this.seriesMap[key].name,
                            type: 'line',
                            yAxisIndex: 1,
                            data: this.seriesMap[key].number.map(item => {
                                return item.value;
                            }),
                        });
                    } else {
                        this.chartLegend.push({
                            name: this.seriesMap[key].name,
                            icon: 'square',
                        });
                        let arr = this.seriesMap[key].number.map(item => {
                            return item.value;
                        });
                        // 获取左侧y轴最大值
                        let max = arr.sort().reverse()[0];
                        if (this.leftMaxNum < max) {
                            this.leftMaxNum = max;
                        }
                        // 最大值成为10的公倍数
                        if (this.leftMaxNum % 10 !== 0) {
                            this.leftMaxNum = Math.ceil(this.leftMaxNum / 10) * 10;
                        }
                        // 获取左侧y轴最小值
                        let min = arr.sort()[0];
                        if (this.leftMinNum > min) {
                            this.leftMaxNum = min;
                        }
                        // 最小值成为10的公倍数
                        if (this.rightMinNum % 10 !== 0) {
                            this.rightMinNum = Math.floor(this.rightMinNum / 10) * 10;
                        }
                        this.series.push({
                            color: this.colorMap[key],
                            name: this.seriesMap[key].name,
                            type: 'bar',
                            barWidth: '20',

                            tooltip: {
                                valueFormatter: function (value) {
                                    if (value > 0) {
                                        return '+' + value;
                                    } else {
                                        return value;
                                    }
                                },
                            },
                            data: this.seriesMap[key].number.map(item => {
                                return item.value;
                            }),
                        });
                    }
                }
            }
        },
        getChartData() {
            const images = [];
            for (let i = 0; i < this.carouselFilterList.length; i++) {
                let canvas = document.querySelector(`#echart_${this.carouselFilterList[i].id} canvas`);
                if (canvas) {
                    let base64Str = this.canvsa2Image(canvas);
                    images.push({
                        base64Str: base64Str,
                        imageName: this.carouselFilterList[i].name,
                        xline: 0,
                        yline: i * 270,
                        dim: 'impactTrend',
                    });
                }
            }
            return {
                assetdims: this.carouselFilterList.map(i => i.id),
                images,
            };
        },
        canvsa2Image(canvas, prefix = true) {
            let context = canvas.getContext('2d');
            let w = canvas.width;
            let h = canvas.height;
            // cache
            let data = context.getImageData(0, 0, w, h);

            // 添加背景
            let compositeOperation = context.globalCompositeOperation;
            context.globalCompositeOperation = 'destination-over';
            context.fillStyle = '#fff';
            context.fillRect(0, 0, w, h);

            let url = canvas.toDataURL('image/png');
            let imageData = prefix ? url : url.replace('data:image/png;base64,', '');

            // 恢复canvas
            context.clearRect(0, 0, w, h);
            context.putImageData(data, 0, 0);
            context.globalCompositeOperation = compositeOperation;

            return imageData;
        },
        chartResize() {
            this.$nextTick(() => {
                for (let i = 0; i < this.chartsList.length; i++) {
                    this.chartsList[i].resize();
                }
            });
        },
        handleMouseEnter() {
            $emit(this, 'stopLoop');
        },
        handleMouseLeave() {
            $emit(this, 'startLoop');
        },
        // 时间选择相关代码
        setFocus(val) {
            this.shortcutTime !== '4' && (this.isFocus = val);
        },
        clickShortcutTime(diff, type) {
            this.definedTime = ''; // 清理掉自定义时间
            let timeObj = this.getLastTimeByDiff(diff, type);
            this.startTime = timeObj.start;
            this.endTime = timeObj.end;
            this.getStatistics();
        },
        getLastTimeByDiff(diffNum, type) {
            let mSeconds = diffNum * 24 * 60 * 60 * 1000;
            let defaultTime = {};
            // 今天
            let nowDate = new Date();
            nowDate.setTime(nowDate.getTime());
            let preDate = new Date();
            preDate.setTime(nowDate.getTime() - mSeconds);
            defaultTime.start = this.timeFormat(preDate);
            defaultTime.end = this.timeFormat(nowDate);

            return defaultTime;
        },
        timeFormat(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
            let o = {
                'M+': date.getMonth() + 1, // 月份
                'D+': date.getDate(), // 日
                'h+': date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, // 小时
                'H+': date.getHours(), // 小时
                'm+': date.getMinutes(), // 分
                's+': date.getSeconds(), // 秒
                'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
                S: date.getMilliseconds(), // 毫秒
            };
            if (/(Y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
            }
            for (let k in o) {
                if (new RegExp('(' + k + ')').test(fmt)) {
                    fmt = fmt.replace(
                        RegExp.$1,
                        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
                    );
                }
            }
            return fmt;
        },
    },
    emits: ['stopLoop', 'startLoop'],
};
</script>

<style lang="scss" scoped>
.filterButtons {
    position: absolute;
    right: 0;
    top: 0;
    .filterPop {
        width: 414px;
    }
}

.echartsItem {
    width: 100%;
}

.containerBg {
    background-color: #fff;
}

:deep(.el-carousel__indicator--horizontal) {
    padding: 0px 4px;
}

:deep(.el-carousel__button) {
    height: 5px;
    background-color: #999;
}
.len1 :deep(.el-carousel__indicators) {
    display: none;
}
.isFocus :deep(.el-radio-button__inner) {
    background: rgb(217, 217, 217);
}
.time-select {
    margin: 0;
    vertical-align: bottom;
}
.hide-input {
    position: absolute !important;
    top: 0;
    right: 82px;
    z-index: 30;
    opacity: 0;
    cursor: pointer;
    .el-input__inner {
        padding: 0;
    }
    :deep(.el-range-input) {
        cursor: pointer;
    }
}
:deep(.el-button.hide-input) > span {
        width: 70px;
}

:deep(.el-range-editor--small) .el-range__icon {
    width: 70px;
    height: 30px;
}

html.dark {
    :deep(.el-radio-button__inner) {
        background: transparent;
        border-color: #474a59;
        color: #a4a7b3;
    }
    .isFocus :deep(.el-radio-button__inner) {
        color: #1993ff;
        background: transparent;
    }
}
</style>
