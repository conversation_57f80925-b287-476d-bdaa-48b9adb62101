/* Started by AICoder, pid:7312de3cd7s5903140120921c08ce54b24529c55 */
<template>
    <div>
        <div class="confirmNote">
            {{ $t('batteryTest.message.testConfirmMsg') }}
        </div>
        <el-table
            ref="table"
            :data="tableData"
            row-key="id"
            style="width: 100%"
            max-height="500"
            @selection-change="selectionChangeHandle"
        >
            <el-table-column
                width="40"
                type="selection"
                :selectable="isSelectable"
                :reserve-selection="true"
            ></el-table-column>
            <el-table-column
                prop="name"
                :label="$t('batteryTest.fields.device')"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column prop="reason" :label="$t('batteryOverview.fields.SOCResult')" show-overflow-tooltip></el-table-column>
            <el-table-column
                prop="pathName"
                :label="$t('battery.fields.position')"
                show-overflow-tooltip
            ></el-table-column>
        </el-table>

        <div class="dialog-footer">
            <el-button type="primary" :disabled="!deviceIds.length" @click="handleConfirm">
                {{ $t('button.confirm') }}
            </el-button>
            <el-button @click="cancel">
                {{ $t('button.cancel') }}
            </el-button>
        </div>
    </div>
</template>
/* Ended by AICoder, pid:7312de3cd7s5903140120921c08ce54b24529c55 */

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
export default {
    props: {
        testTableData: {
            type: Array,
            default: () => {
                return [];
            },
        },
    },
    data() {
        return {
            loading: false,
            deviceIds: [],
            tableData: [],
        };
    },
    watch: {},
    mounted() {},
    methods: {
        isSelectable(row) {
            return row.result;
        },
        // 设置默认选中
        setDefaultSelections() {
            this.$nextTick(() => {
                this.tableData.forEach(row => {
                    if (row.result === true) {
                        this.$refs.table.toggleRowSelection(row, true);
                    }
                });
                this.deviceIds = this.tableData.filter(i => i.result).map(i => i.id);
            });
        },
        selectionChangeHandle(val) {
            this.deviceIds = val.map(i => i.id);
        },
        /* Started by AICoder, pid:58611i9cd3zd494149f70acf70841a89d393a46d */
        getCheck() {
            const ids = this.testTableData.map(i => i.id);
            HTTP.request('checkBatterySoc', {
                method: 'post',
                data: ids,
                complete: data => {
                    if (data.code === 0) {
                        this.tableData = data.data || [];
                        this.setDefaultSelections();
                    } else {
                        this.tableData = [];
                        this.$message({
                            message: data.message,
                            type: 'error',
                        });
                    }
                },
                error: err => {
                    this.tableData = [];
                    this.$message({
                        message: err.message,
                        type: 'error',
                    });
                },
            });
        },
        handleConfirm() {
            this.loading = true;
            HTTP.request('temporaryBattPackStart', {
                method: 'post',
                data: this.deviceIds,
                complete: resp => {
                    this.loading = false;
                    $emit(this, 'temporaryClose', 'success');
                    if (resp.code === -301) {
                        this.$message({
                            message: this.$t('batteryTest.message.paramBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (resp.code === -206 || resp.code === -209) {
                        this.$message({
                            message: this.$t('batteryTest.message.dontTest'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (resp.code === -205) {
                        this.$message({
                            message: this.$t('batteryTest.message.haveNoBatt'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (resp.code === -207) {
                        this.$message({
                            message: this.$t('batteryTest.message.haveNoBatt'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (resp.code !== 0) {
                        this.$message({
                            message: this.$t('tipMessage.requestError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.loading = false;
                    $emit(this, 'temporaryClose');
                    this.$message({
                        message: this.$t('tipMessage.requestError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        /* Ended by AICoder, pid:58611i9cd3zd494149f70acf70841a89d393a46d */
        cancel() {
            $emit(this, 'temporaryClose');
        },
    },
    emits: ['temporaryClose'],
};
</script>

<style lang="scss" scoped>
.confirmNote {
    font-size: 12px;
    padding: 0 0 16px 0;
}
.dot {
    width: 10px;
    height: 10px;
    display: inline-block;
    border-radius: 50%;
    margin: 1px 2px 0 0;
}
.dot-normal {
    background-color: #76d63e;
}
.dot-deficiency {
    background-color: #e02222;
}
.dot-unEvaluate {
    background-color: #bfbfbf;
}
.dialog-footer {
    margin-top: 16px;
}
html.dark {
    .confirmNote {
        color: #a4a7b3;
    }
}
</style>
