<template>
    <el-form
        width="auto"
        :label-width="$i18n.locale == 'zh-CN' ? '109px' : '222px'"
        label-position="right"
    >
        <!-- 测试状态 -->
        <el-form-item :label="$t('batteryTest.fields.testStatus')">
            <el-select
                v-model="queryForm.testStatus"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.testStatus"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 测试类型 -->
        <el-form-item :label="$t('batteryTest.fields.testType')">
            <el-select
                v-model="queryForm.testTypes"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.testTypes"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 测试结果 -->
        <!-- <el-form-item :label="$t('batteryTest.fields.testResult')">
            <el-select
                v-model="queryForm.testResult"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.testResult"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item> -->
        <!-- 下发时间范围 *-->
        <el-form-item :label="$t('batteryTest.fields.deliveryTimeRange')" class="time-item">
            <el-date-picker
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                v-model="queryForm.timeRange"
                class="datePicker"
                type="datetimerange"
                range-separator="-"
                value-format="YYYY-MM-DD HH:mm:ss"
                :unlink-panels="true"
                :start-placeholder="$t('datetimePicker.beginTime')"
                :end-placeholder="$t('datetimePicker.endTime')"
            ></el-date-picker>
        </el-form-item>
        <!-- 供电场景 -->
        <el-form-item :label="$t('monitor.fields.powerSupplyScene')">
            <el-select
                v-model="queryForm.powerSupplyScene"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.powerSupplyScene"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 站点等级 -->
        <el-form-item :label="$t('common-site-level')">
            <el-select
                v-model="queryForm.siteLevels"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.siteLevels"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 测试后备电状态 -->
        <el-form-item :label="$t('batteryTest.fields.standbyPowerStatusAfterTest')">
            <el-select
                v-model="queryForm.backupPowerStatus"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.backupPowerStatus"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 测试后健康状态 -->
        <el-form-item :label="$t('batteryTest.fields.healthStatusAfterTest')">
            <el-select
                v-model="queryForm.healthStatus"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.healthStatus"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <!-- 交流输入状态 -->
        <!-- <el-form-item :label="$t('battery-test-acInputState')">
            <el-select
                v-model="queryForm.acInputStatus"
                :placeholder="$t('placeholder.select')"
                multiple
                filterable
                collapse-tags
                clearable
            >
                <el-option
                    v-for="item in options.acInputStatus"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
            </el-select>
        </el-form-item> -->
        <div
            style="text-align: right"
        >
            <el-button type="primary" @click="query()">
                {{ $t('button.search') }}
            </el-button>
            <el-button @click="resetQuery()">
                {{ $t('button.reset') }}
            </el-button>
        </div>
    </el-form>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import { mocIdsMap, powerSupplyWhenGetOptions, siteLevelWhenGetOptions } from '@/util/constants.js';
export default {
    data() {
        return {
            queryForm: {
                testStatus: [], // 测试状态
                testTypes: [], // 测试类型
                testResult: [], // 测试结果
                powerSupplyScene: [], // 供电场景
                siteLevels: [], // 站点等级
                backupPowerStatus: [], // 当前备电状态
                healthStatus: [], // 当前健康状态
                acInputStatus: [], // 交流输入状态
                timeRange: [], // 启用时间
            },
            queryParameter: {},
            showQuery: false,
            options: {
                testStatus: [], // 测试状态
                testTypes: [], // 测试类型
                testResult: [], // 测试结果
                powerSupplyScene: [], // 供电场景
                siteLevels: [], // 站点等级
                backupPowerStatus: [], // 当前备电状态
                healthStatus: [], // 当前健康状态
                acInputStatus: [], // 交流输入状态
            },
        };
    },
    components: {},
    props: {
        alarmRights: {
            type: Boolean,
            default: false,
        },
    },
    computed: {},
    watch: {},
    created() {
        this.getOptions();
        this.query();
    },
    mounted() {},
    methods: {
        updateQueryParameter() {
            this.queryParameter = {};
            for (let key in this.queryForm) {
                let value = this.queryForm[key];
                if (key === 'timeRange') {
                    if (Array.isArray(value) && value.length === 2) {
                        this.queryParameter.deliveryStartTime = value[0];
                        this.queryParameter.deliveryEndTime = value[1];
                    } else {
                        this.queryParameter.deliveryStartTime = '';
                        this.queryParameter.deliveryEndTime = '';
                    }
                } else {
                    this.queryParameter[key] = value;
                }
            }
        },
        query() {
            this.updateQueryParameter();
            $emit(this, 'query', this.queryParameter);
        },
        resetQuery() {
            for (let key in this.queryForm) {
                this.queryForm[key] = [];
            }
            this.query();
        },
        getOptions() {
            this.requestOptions('testStatus', 'battTestOptions', 'get'); // 测试状态
            this.requestOptions('testTypes', 'getTestType', 'get'); // 测试类型
            this.requestOptions('testResult', 'getTestResult', 'get'); // 测试结果
            this.getPowerSupplyOptions(); // 供电场景
            this.getSiteLevelOptions(); // 站点等级
            this.requestOptions('backupPowerStatus', 'battStatusOptions', 'get'); // 当前备电状态
            this.requestOptions('healthStatus', 'battSohOptions', 'get'); // 当前健康状态
            this.requestOptions('acInputStatus', 'getACStatus', 'get'); // 交流输入状态
        },
        requestOptions(type, url, method, parameter = {}) {
            HTTP.request(url, {
                method: method,
                data: parameter,
                complete: data => {
                    if (data.code === 0) {
                        this.options[type] = data.data || [];
                    } else {
                        this.options[type] = [];
                    }
                },
                error: () => {
                    this.options[type] = [];
                },
            });
        },
        getPowerSupplyOptions() {
            HTTP.request('getSiteLevelOrPowerSupply', {
                method: 'post',
                data: {
                    mocId: mocIdsMap.site,
                    ids: [powerSupplyWhenGetOptions]
                },
                complete: data => {
                    if (data.code === 0 && data.data && data.data.length && data.data[0].valueOptions) {
                        this.options.powerSupplyScene = data.data[0].valueOptions;
                    } else {
                        this.options.powerSupplyScene = [];
                    }
                },
                error: () => {
                    this.options.powerSupplyScene = [];
                },
            });
        },
        getSiteLevelOptions() {
            HTTP.request('getSiteLevelOrPowerSupply', {
                method: 'post',
                data: {
                    mocId: mocIdsMap.site,
                    ids: [siteLevelWhenGetOptions]
                },
                complete: data => {
                    if (data.code === 0 && data.data && data.data.length && data.data[0].valueOptions) {
                        this.options.siteLevels = data.data[0].valueOptions;
                    } else {
                        this.options.siteLevels = [];
                    }
                },
                error: () => {
                    this.options.siteLevels = [];
                },
            });
        },
    },
    emits: ['query'],
};
</script>

<style lang="scss" scoped>
.time-item {
    ::v-deep .el-date-editor.el-date-editor--datetimerange {
        width: 280px !important;
    }
}
:deep(.el-select__input) {
    font-size: 12px;
}
</style>
