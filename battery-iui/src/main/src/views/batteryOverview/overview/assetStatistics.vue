<template>
    <div
        id="assetStatistics"
        ref="box"
        class="uedm-content-area"
        :class="{ containerBg: !isDark }"
        style="margin-top: 16px; padding: 16px 0; height: 300px; box-sizing: border-box"
    >
        <div class="filter-tool">
            <!-- <div v-show="!isFullScreen" class="uedm-title-tab">
                {{ $t('batteryOverview.fields.assets') }}
            </div> -->
            <div class="filterButtons">
                <span class="filterItem">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('batteryOverview.fields.fullScreen')"
                        placement="bottom"
                    >
                        <span
                            v-show="!isFullScreen"
                            class="icon-button plx-ico-full-screen-16"
                            @click="fullScreen"
                        ></span>
                    </el-tooltip>
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('batteryOverview.fields.restore')"
                        placement="bottom"
                    >
                        <span v-show="isFullScreen" class="icon-button plx-ico-reduction-16" @click="restore"></span>
                    </el-tooltip>
                </span>
                <span v-show="!isFullScreen" class="filterItem">
                    <column-filter
                        ref="columnFilter"
                        :value="filterList"
                        :tip="$t('tooltip.displayedItems')"
                        :is-dark="isDark"
                        @save="handleFilterOk"
                    ></column-filter>
                </span>
            </div>
        </div>
        <div class="z-block">
            <div v-loading="loading" class="z-block-content">
                <el-tabs v-if="carouselFilterList.length" :key="'asset' + tabsKey" v-model="activeName">
                    <el-tab-pane
                        v-for="item in carouselFilterList"
                        :key="item.id"
                        :name="item.id"
                        :label="item.name"
                    >
                        <div
                            :id="'echart_' + item.id"
                            class="echartsItem"
                            :style="{ height: carouselHeight, width: changeWidth }"
                        ></div>
                    </el-tab-pane>
                </el-tabs>
                <div
                    v-if="!carouselFilterList.length"
                    class="no-data"
                    :style="{ height: 216 + 'px', 'line-height': 216 + 'px' }"
                >
                    <span>{{ $t('common.noData') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
import { CHART_COLORS } from '@uedm/uedm-ui/src/util/constants.js';
const chartColor = CHART_COLORS;

export default {
    components: {
        'column-filter': ColumnFilter,
    },
    props: {
        logicId: {
            type: String,
            default: '',
        },
        queryParameter: {
            type: Object,
            default() {
                return {};
            },
        },
        widthChange: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            loading: true,
            isFullScreen: false,
            filterLoading: false,
            filterList: [], // 过滤条件初始列表
            filterSearchList: [], // 过滤条件列表数据
            carouselFilterList: [], // 轮播图列表数据
            assetStatisticsList: [], // 所有图表显示数据
            chartsList: [], // 所有图表实例
            tabsKey: 1, // 排序后el-tabs不重新渲染，给它加一个key值强制渲染
            changeWidth: 'auto',
        };
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        carouselHeight() {
            return this.isFullScreen ? 'calc(100vh - 56px - 34px)' : '216px';
        },
        winResize() {
            return this.$store.getters.getResize;
        },
        arrowShow() {
            let type = 'never';
            if (this.carouselFilterList.length > 1) {
                type = 'always';
            }
            return type;
        },
        activeName() {
            let val = '';
            if (this.carouselFilterList.length) {
                val = this.carouselFilterList[0]?.id || '';
            }
            return val;
        },
    },
    watch: {
        queryParameter: {
            handler: function () {
                if (this.filterList.length > 0) {
                    this.getAssetStatistics();
                }
            },
            deep: true,
        },
        '$i18n.locale'() {
            this.initCarousel();
        },
        winResize: function () {
            this.chartResize();
        },
        widthChange() {
            this.chartResize();
        },
    },
    created() {},
    mounted() {
        this.getFilterList();
        document.addEventListener('fullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('mozfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('webkitfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        document.addEventListener('msfullscreenchange', () => {
            this.isFullScreen = !this.isFullScreen;
            this.chartResize();
        });
        this.updataChangeWidth();
        let resizeWorkingCondition = new ResizeObserver(() => {
            this.updataChangeWidth();
        });
        resizeWorkingCondition.observe(document.getElementById('assetStatistics'));
    },
    beforeUnmount() {
        for (let i = 0; i < this.chartsList.length; i++) {
            this.chartsList[i].dispose();
        }
    },
    methods: {
        updataChangeWidth() {
            this.changeWidth = this.isFullScreen ? '100vw' : this.$el.offsetWidth + 'px';
        },
        fullScreen() {
            $emit(this, 'fullScreen');
            let element = this.$refs.box;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullScreen) {
                element.webkitRequestFullScreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        },
        restore() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitCancelFullScreen) {
                document.webkitCancelFullScreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document?.msExitFullscreen) {
                document.msExitFullscreen();
            }
        },
        /**
         * 查询资产维度列表
         */
        getFilterList() {
            this.filterLoading = true;
            HTTP.request('getAssetFilterList', {
                method: 'get',
                complete: data => {
                    this.filterLoading = false;
                    if (data.code === 0) {
                        this.filterList = data.data;
                        this.carouselFilterList = this.filterList.filter(i => i.enable && i.assetAttributeShow);
                        this.getAssetStatistics();
                    }
                },
                error: () => {
                    this.filterLoading = false;
                },
            });
        },
        /**
         * 保存资产维度列表
         */
        handleFilterOk(tableList) {
            HTTP.request('assetFilterUpdate', {
                method: 'post',
                data: tableList.map(i => {
                    return {
                        id: i.id,
                        sequence: i.sequence,
                        enable: i.enable,
                    };
                }),
                complete: data => {
                    /* Started by AICoder, pid:j81d811cbai8a4c1458f0be0c0cc59448247d68c */
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.filterList = tableList;
                        this.carouselFilterList = this.filterList.filter(i => i.enable && i.assetAttributeShow);
                        this.tabsKey++;
                        this.$nextTick(() => {
                            this.getAssetStatistics();
                        });
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotRange') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotModify') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                    /* Ended by AICoder, pid:j81d811cbai8a4c1458f0be0c0cc59448247d68c */
                },
                error: () => {},
            });
        },
        /**
         * 查询各个资产维度的电池分类数量
         */
        getAssetStatistics() {
            this.loading = true;
            HTTP.request('getAssetStatistics', {
                method: 'post',
                data: {
                    logicId: this.logicId,
                    dims: this.carouselFilterList.map(i => i.id),
                    ...this.queryParameter,
                },
                complete: data => {
                    this.loading = false;
                    if (data.code === 0) {
                        this.assetStatisticsList = data.data;
                    } else {
                        this.assetStatisticsList = [];
                    }
                    this.initCarousel();
                },
                error: () => {
                    this.loading = false;
                    this.assetStatisticsList = [];
                    this.initCarousel();
                },
            });
        },
        initCarousel() {
            this.chartsList = [];
            for (let index = 0; index < this.carouselFilterList.length; index++) {
                const nowSlectedItem = this.carouselFilterList[index];
                if (document.getElementById('echart_' + nowSlectedItem.id)) {
                    let myEchart = this.$echarts.init(
                        document.getElementById('echart_' + nowSlectedItem.id),
                        this.isDark ? 'dark' : 'light',
                        216
                    );
                    myEchart.clear();
                    let options = {};
                    const nowItemData = this.assetStatisticsList.find(i => i.dim === nowSlectedItem.id);
                    switch (nowSlectedItem.id) {
                        case 'battType':
                            options = this.getPieChartOption(nowItemData, nowSlectedItem.name || '');
                            break;
                        case 'manufactuer':
                        case 'supplier':
                            options = this.getBarChartOption(nowItemData, nowSlectedItem.name || '');
                            break;
                        default:
                            break;
                    }
                    myEchart.setOption(options);
                    this.chartsList.push(myEchart);
                    // 监听父级dom变化
                    let resizeOb = new ResizeObserver(entries => {
                        for (let entry of entries) {
                            if (this.$echarts.getInstanceByDom(entry.target)) {
                                this.$echarts.getInstanceByDom(entry.target).resize();
                            }
                        }
                    });
                    resizeOb.observe(document.getElementById(`echart_${nowSlectedItem.id}`));
                }
            }
        },
        getPieChartOption(data) {
            const colorType = this.isDark ? 'dark' : 'default';
            const colors = ['#ffc850', '#109ebf', '#a8e2cf', '#7cd180', '#0171ab', '#4bd3f3', '#CCA4E3', '#4ee5c8'];
            let colorIndex = 0;
            let statistics = data ? data.statistics || [] : [];
            return {
                // title: {
                //     text: (data && data.assetDimName) || title,
                //     x: 'left',
                //     textStyle: {
                //         color: chartColor[colorType].title,
                //         fontSize: 14,
                //         fontWeight: 'bold',
                //     },
                // },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: chartColor[colorType].background,
                    borderColor: chartColor[colorType].border,
                    textStyle: { color: chartColor[colorType].color },
                },
                color: chartColor.color,
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    left: '60%',
                    y: 'center',
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        width: '200',
                        overflow: 'breakAll',
                        fontSize: 14
                    },
                    formatter: name => {
                        let d = data ? data.statistics || [] : [];
                        if (!d.length) {
                            return;
                        }
                        let value = d.filter(x => x.dimTypeName === name)[0].number;
                        let total = data ? data.totalNumber || 0 : 0;
                        let arr = [name + ': ' + '--'];
                        if (total == 0 && (value || value === 0)) {
                            arr = [name + ': ' + value + '/' + '0%'];
                        } else if (total && (value || value === 0)) {
                            arr = [
                                name +
                                    ': ' +
                                    value +
                                    '/' +
                                    ((value / total) * 100).toFixed(2).replace(/(\.00|0)$/, '') +
                                    '%',
                            ];
                        }
                        return arr;
                    },
                },
                backgroundColor: 'transparent',
                graphic: [
                    {
                        type: 'group',
                        top: '43%',
                        left: '35%',
                        bounding: 'raw',
                        children: [
                            {
                                type: 'text',
                                top: '30%',
                                bounding: 'raw',
                                invisible: !(data && data.statistics && data.statistics.length),
                                style: {
                                    fill: chartColor[colorType].total,
                                    text: this.$t('batteryOverview.fields.total'),
                                    textAlign: 'center',
                                    fontSize: 14
                                },
                            },
                            {
                                type: 'text',
                                top: '50%',
                                bounding: 'raw',
                                invisible: !(data && data.statistics && data.statistics.length),
                                style: {
                                    fill: chartColor[colorType].total,
                                    text: `\n${data ? data.totalNumber || 0 : 0}`,
                                    textAlign: 'center',
                                    lineHeight: 20,
                                    fontSize: 26,
                                },
                            },
                        ],
                    },
                    {
                        type: 'group',
                        top: '45%',
                        left: '62%',
                        bounding: 'raw',
                        children: [
                            {
                                type: 'text',
                                bounding: 'raw',
                                silent: true,
                                invisible: data && data.statistics && data.statistics.length,
                                z: 100,
                                style: {
                                    fill: chartColor[colorType].noData,
                                    textAlign: 'center',
                                    fontWeight: 'bold',
                                    text: this.$t('common.noData'),
                                    fontFamily: 'Microsoft Yahei',
                                    fontSize: '25px',
                                },
                            },
                        ],
                    },
                ],
                series: [
                    {
                        type: 'pie',
                        center: ['35%', '50%'],
                        radius: ['75%', '90%'],
                        avoidLabelOverlap: true,
                        label: {
                            show: false,
                        },
                        data: statistics.map(item => {
                            let color = null;
                            if (colorIndex < colors.length) {
                                color = colors[colorIndex];
                                colorIndex++;
                            }
                            return {
                                value: item.number,
                                name: item.dimTypeName,
                                itemStyle: {
                                    color: color,
                                },
                            };
                        }),
                    },
                ],
            };
        },
        getBarChartOption(data, title) {
            const colorType = this.isDark ? 'dark' : 'default';
            const handleIcon =
                'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
            let option = {};
            if (data && data.statistics && data.statistics.length) {
                option = {
                    grid: {
                        left: '4.5%',
                        right: '4%',
                        bottom: '30',
                        containLabel: true,
                    },
                    color: chartColor.color,
                    backgroundColor: 'transparent',
                    legend: {},
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: chartColor[colorType].background,
                        borderColor: chartColor[colorType].border,
                        textStyle: { color: chartColor[colorType].color },
                    },
                    xAxis: {
                        type: 'category',
                        data: data?.statistics.map(i => i.dimTypeName),
                        axisTick: {
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            interval: 0,
                            hideOverlap: true,
                            width: 100,
                            overflow: 'truncate',
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: this.$t('batteryOverview.fields.batteryNumUnit'),
                        nameLocation: 'end',
                        nameTextStyle: {
                            align: 'center',
                            width: 100,
                        },
                        axisLine: {
                            show: true,
                        },
                        minInterval: 1,
                    },
                    dataZoom: [
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            filterMode: 'empty',
                            bottom: 10,
                            height: 24,
                            showDetail: false,
                            zoomLock: false,
                        },
                    ],
                    series: [
                        {
                            data: data?.statistics.map(i => i.number),
                            type: 'bar',
                            barMaxWidth: 30,
                        },
                    ],
                };
            } else {
                // 显示暂无数据
                option = {
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '60px' : '40px',
                        right: '4%',
                        bottom: '30',
                        containLabel: true,
                    },
                    title: {
                        text: (data && data.assetDimName) || title,
                        x: 'left',
                        textStyle: {
                            color: chartColor[colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    color: ['#109ebf'],
                    backgroundColor: 'transparent',
                    legend: {},
                    tooltip: {
                        trigger: 'axis',
                    },
                    xAxis: {
                        type: 'category',
                        data: [],
                        name: data ? data.assetDimName : this.$t('standby.fields.manufacture'),
                        nameLocation: 'start',
                        nameTextStyle: {
                            align: 'center',
                            verticalAlign: 'top',
                            padding: [10, 0, 0, 0],
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: this.$t('batteryOverview.fields.batteryNumUnit'),
                        nameLocation: 'end',
                        nameTextStyle: {
                            align: 'center',
                            width: 100,
                        },
                        show: true,
                        axisLine: {
                            show: true,
                        },
                    },
                    series: [
                        {
                            data: [],
                            type: 'bar',
                        },
                    ],
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'middle',
                        silent: true,
                        invisible: false, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: chartColor[colorType].noData,
                            fontWeight: 'bold',
                            text: this.$t('common.noData'),
                            fontFamily: 'Microsoft Yahei',
                            fontSize: '25px',
                        },
                    },
                };
            }
            return option;
        },
        getChartData() {
            const images = [];
            for (let i = 0; i < this.carouselFilterList.length; i++) {
                let canvas = document.querySelector(`#echart_${this.carouselFilterList[i].id} canvas`);
                if (canvas) {
                    let imageStr = this.canvsa2Image(canvas);
                    images.push({
                        base64Str: imageStr,
                        imageName: this.carouselFilterList[i].name,
                        xline: 0,
                        yline: i * 216,
                        dim: 'asset',
                    });
                }
            }
            return {
                assetdims: this.carouselFilterList.map(i => i.id),
                images,
            };
        },
        canvsa2Image(canvas, prefix = true) {
            let context = canvas.getContext('2d');
            let w = canvas.width;
            let h = canvas.height;
            // cache
            let data = context.getImageData(0, 0, w, h);

            // 添加背景
            let compositeOperation = context.globalCompositeOperation;
            context.globalCompositeOperation = 'destination-over';
            context.fillStyle = '#fff';
            context.fillRect(0, 0, w, h);

            let url = canvas.toDataURL('image/png');
            let imageData = prefix ? url : url.replace('data:image/png;base64,', '');

            // 恢复canvas
            context.clearRect(0, 0, w, h);
            context.putImageData(data, 0, 0);
            context.globalCompositeOperation = compositeOperation;

            return imageData;
        },
        chartResize() {
            this.$nextTick(() => {
                for (let i = 0; i < this.chartsList.length; i++) {
                    this.chartsList[i].resize();
                }
            });
        },
        handleMouseEnter() {
            $emit(this, 'stopLoop');
        },
        handleMouseLeave() {
            $emit(this, 'startLoop');
        },
    },
    emits: ['fullScreen', 'stopLoop', 'startLoop'],
};
</script>

<style lang="scss" scoped>
h4.subTitle {
    display: inline-block;
    margin: 0;
    padding: 8px 0 0;
}
.filter-tool {
    position: relative;
    .filterButtons {
        position: absolute;
        top: 0;
        right: 16px;
        z-index: 888;
        .filterPop {
            width: 414px;
        }
    }
}

.echartsItem {
    width: 100%;
}

.containerBg {
    background-color: #fff;
}

:deep(.el-carousel__indicator--horizontal) {
    padding: 0 4px;
}

:deep(.el-carousel__button) {
    height: 5px;
    background-color: #999;
}
.len1 :deep(.el-carousel__indicators) {
    display: none;
}
.z-block-content {
    padding: 0;
}
:deep(.el-tabs) {
    .el-tabs__header {
        .el-tabs__nav-wrap {
            padding-left: 16px;
        }
    }
}
</style>
