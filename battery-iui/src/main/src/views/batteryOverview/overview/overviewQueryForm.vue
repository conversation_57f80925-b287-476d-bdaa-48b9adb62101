<template>
    <div ref="formWrapRef" class="uedm-query-form">
        <el-form
            ref="ruleForm"
            :rules="rules"
            :model="queryForm"
            :inline="true"
            :label-width="$i18n.locale == 'zh-CN' ? '130px' : '160px'"
            label-position="right"
        >
            <!-- 电池类型 -->
            <el-form-item :label="$t('batteryOverview.fields.batteryType')">
                <el-select
                    v-model="queryForm.types"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.types"
                        :key="item.id"
                        v-loading="loadingOptions.types"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 健康状态 -->
            <el-form-item :label="$t('batteryOverview.fields.healthStatus')">
                <el-select
                    v-model="queryForm.soh"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in healthOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 剩余容量 -->
            <el-form-item :label="$t('batteryOverview.fields.SOC')">
                <el-select
                    v-model="queryForm.prstSocLevels"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.prstSocLevels"
                        :key="item.id"
                        v-loading="loadingOptions.prstSocLevels"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 告警 -->
            <el-form-item v-if="alarmRights" :label="$t('battery-field-alarmStatus-alarm')">
                <el-select
                    v-model="queryForm.alarmLevels"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.alarmLevels"
                        :key="item.id"
                        v-loading="loadingOptions.alarmLevels"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 寿命 -->
            <el-form-item :label="$t('batteryOverview.fields.remainingLife')">
                <el-select
                    v-model="queryForm.lifeLevels"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in lifeOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 运行状态 -->
            <el-form-item :label="$t('batteryOverview.fields.operatingStatus')">
                <el-select
                    v-model="queryForm.chargeStatus"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.chargeStatus"
                        :key="item.id"
                        v-loading="loadingOptions.chargeStatus"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 额定容量 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['82fafb6d-RatedCapacity']"
                :label="$t('battery-life-basicInfo-ratedCapacity')"
            >
                <el-select
                    v-model="queryForm.ratedCapLevels"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.ratedCapLevels"
                        :key="item.id"
                        v-loading="loadingOptions.ratedCapLevels"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 风险等级 -->
            <el-form-item :label="$t('batteryOverview.fields.riskLevels')">
                <el-select
                    v-model="queryForm.riskLevels"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    collapse-tags
                    clearable
                >
                    <el-option
                        v-for="item in options.riskLevels"
                        :key="item.id"
                        v-loading="loadingOptions.riskLevels"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 厂商 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['0105-Manufacturer']"
                :label="$t('batteryOverview.fields.manufacturer')"
            >
                <el-select
                    v-model="queryForm.manufacturers"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    filterable
                    remote
                    collapse-tags
                    clearable
                    :remote-method="query => remoteMethod(query, 'manufacturers')"
                    @visible-change="selectVisibleChange(arguments, 'manufacturers')"
                >
                    <el-option
                        v-for="item in options.manufacturers"
                        :key="item.id"
                        v-loading="loadingOptions.manufacturers"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 品牌 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['0105-Brand']"
                :label="$t('batteryOverview.fields.brand')"
            >
                <el-select
                    v-model="queryForm.brands"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    filterable
                    remote
                    collapse-tags
                    clearable
                    :remote-method="query => remoteMethod(query, 'brands')"
                    @visible-change="selectVisibleChange(arguments, 'brands')"
                >
                    <el-option
                        v-for="item in options.brands"
                        :key="item.id"
                        v-loading="loadingOptions.brands"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 系列 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['0105-Series']"
                :label="$t('batteryOverview.fields.series')"
            >
                <el-select
                    v-model="queryForm.series"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    filterable
                    remote
                    collapse-tags
                    clearable
                    :remote-method="query => remoteMethod(query, 'series')"
                    @visible-change="selectVisibleChange(arguments, 'series')"
                >
                    <el-option
                        v-for="item in options.series"
                        :key="item.id"
                        v-loading="loadingOptions.series"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 型号 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['0105-Model']"
                :label="$t('batteryOverview.fields.model')"
            >
                <el-select
                    v-model="queryForm.models"
                    :placeholder="$t('placeholder.select')"
                    multiple
                    filterable
                    remote
                    collapse-tags
                    clearable
                    :remote-method="query => remoteMethod(query, 'models')"
                    @visible-change="selectVisibleChange(arguments, 'models')"
                >
                    <el-option
                        v-for="item in options.models"
                        :key="item.id"
                        v-loading="loadingOptions.models"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 启用时间 -->
            <el-form-item
                v-if="showCorrelationInfo && assetFormIsShow['0102-StartDate']"
                :label="$t('batteryOverview.fields.startTime')"
            >
                <el-date-picker
                    v-model="queryForm.startTime"
                    class="datePicker"
                    type="daterange"
                    range-separator="-"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :unlink-panels="true"
                    :start-placeholder="$t('datetimePicker.beginTime')"
                    :end-placeholder="$t('datetimePicker.endTime')"
                ></el-date-picker>
            </el-form-item>
            <!-- 放电剩余时长 -->
            <el-form-item
                :label="$t('batteryOverview.fields.dischargeResidualDuration')"
                :title="$t('batteryOverview.fields.dischargeResidualDuration')"
                prop="remainChargeEnd"
            >
                <div class="reaminTimeArea">
                    <el-input
                        v-model="queryForm.remainChargeBegin"
                        class="start"
                        maxlength="50"
                        :placeholder="$t('placeholder.enter')"
                        oninput="value=value.replace(/[^\-\d.]/g, '').replace(/\.{2}/g, '.').replace(/-/g, '')"
                    ></el-input>
                    <span class="middleText">
                        <span>-</span>
                        <span v-show="queryForm.remainChargeBegin" style="color: #f56c6c; margin-left: 8px">*</span>
                    </span>
                    <el-input
                        v-model="queryForm.remainChargeEnd"
                        class="end"
                        maxlength="50"
                        :placeholder="$t('placeholder.enter')"
                        oninput="value=value.replace(/[^\-\d.]/g, '').replace(/\.{2}/g, '.').replace(/-/g, '')"
                    ></el-input>
                </div>
                <div v-if="isValid" class="uedm-form-note">
                    {{ $t('batteryOverview.tips.dischargeRemainTimeInput') }}
                </div>
            </el-form-item>
            <el-form-item
                :style="{ 'margin-top': $i18n.locale === 'zh-CN' ? '4px' : '8px' }"
                class="uedm-query-form__btn"
            >
                <el-button type="primary" @click="searchQuery()">
                    {{ $t('button.search') }}
                </el-button>
                <el-button @click="resetQuery()">
                    {{ $t('button.reset') }}
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import { getFormCol } from '@uedm/uedm-ui/src/util/calculation.js';
export default {
    components: {},
    props: {
        value: {
            type: Object,
            default: null,
        },
        showQuery: {
            type: Boolean,
            default: false,
        },
        alarmRights: {
            type: Boolean,
            default: false,
        },
        showCorrelationInfo: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        let validateRemainTime = (rule, value, callback) => {
            if (this.queryForm.remainChargeBegin && !this.queryForm.remainChargeEnd) {
                this.isValid = false;
                return callback(new Error(this.$t('batteryOverview.rules.inputEndTime')));
            } else {
                if (String(this.queryForm.remainChargeBegin).indexOf('.') >= 1) {
                    let len = String(this.queryForm.remainChargeBegin).split('.')[1].length;
                    if (len > 1) {
                        this.isValid = false;
                        return callback(new Error(this.$t('batteryOverview.rules.precision')));
                    }
                }
                if (String(this.queryForm.remainChargeEnd).indexOf('.') >= 1) {
                    let len = String(this.queryForm.remainChargeEnd).split('.')[1].length;
                    if (len > 1) {
                        this.isValid = false;
                        return callback(new Error(this.$t('batteryOverview.rules.precision')));
                    }
                }
                if (this.queryForm.remainChargeEnd && this.queryForm.remainChargeBegin
                        && Number(this.queryForm.remainChargeEnd) <= Number(this.queryForm.remainChargeBegin)) {
                    this.isValid = false;
                    return callback(new Error(this.$t('batteryOverview.rules.timeRange')));
                }
            }
            this.isValid = true;
            callback();
        };
        return {
            queryForm: {
                // 查询条件
                types: [], // 电池类型
                soh: [], // 健康状态
                prstSocLevels: [], // 剩余容量
                alarmLevels: [], // 告警
                lifeLevels: [], // 寿命
                chargeStatus: [], // 电池运行状态
                ratedCapLevels: [], // 额定容量
                riskLevels: [],
                manufacturers: [], // 厂商
                brands: [], // 品牌
                series: [], // 系列
                models: [], // 型号
                startTime: [], // 启用时间
                remainChargeBegin: '', // 放电剩余时长开始时间
                remainChargeEnd: '' // 放电剩余时长结束时间
            },
            isValid: true,
            queryParameter: {},
            options: {
                types: [],
                prstSocLevels: [],
                alarmLevels: [],
                chargeStatus: [],
                ratedCapLevels: [],
                manufacturers: [],
                brands: [],
                series: [],
                models: [],
                riskLevels: [],
            },
            loadingOptions: {
                types: false,
                prstSocLevels: false,
                alarmLevels: false,
                chargeStatus: false,
                ratedCapLevels: false,
                manufacturers: false,
                brands: false,
                series: false,
                models: false,
                riskLevels: false,
            },
            assetFormIsShow: {
                '82fafb6d-BatteryType': false,
                '82fafb6d-RatedCapacity': false,
                '0105-Manufacturer': false,
                '0105-Brand': false,
                '0105-Series': false,
                '0105-Model': false,
                '0102-StartDate': false,
            },
            rules: {
                remainChargeEnd: [
                    { validator: validateRemainTime, trigger: ['blur', 'change'] },
                ]
            }
        };
    },
    computed: {
        lifeOptions() {
            return this.$store.getters.getLifeOptions || [];
        },
        healthOptions() {
            return this.$store.getters.getHealthOptions || [];
        },
    },
    watch: {
        showQuery(val) {
            if (val) {
                getFormCol(this.$refs.formWrapRef);
                this.initValue();
            }
        },
    },
    created() {
        this.queryAttributes();
        this.getOptions();
        this.query();
    },
    mounted() {},
    methods: {
        initValue() {
            if (this.value) {
                for (let key in this.queryForm) {
                    let value = this.value[key];
                    if (key === 'startTime') {
                        this.queryForm[key] = [];
                        if (this.value.startDateBegin && this.value.startDateEnd) {
                            this.queryForm[key] = [this.value.startDateBegin, this.value.startDateEnd];
                        }
                    } else if (key === 'alarmLevels') {
                        let alarmLevels = [];
                        // 告警需要特殊处理值为字符串
                        if (value.length > 0) {
                            value.forEach(d => {
                                alarmLevels.push(d + '');
                            });
                        }
                        this.queryForm[key] = alarmLevels;
                    } else if (key === 'remainChargeBegin' || key === 'remainChargeEnd') {
                        this.queryForm[key] = this.queryForm[key] || '';
                    } else {
                        this.queryForm[key] = this.value[key] || [];
                    }
                }
            }
        },
        updateQueryParamByClickChart(val) {
            this.queryForm.remainChargeBegin = val[0];
            this.queryForm.remainChargeEnd = val[1] === '∞' ? 10000 : val[1];
            this.updateQueryParameter();
            $emit(this, 'query', this.queryParameter);
        },
        updateQueryParameter() {
            this.queryParameter = {};
            for (let key in this.queryForm) {
                let value = this.queryForm[key];
                if (key === 'startTime') {
                    if (Array.isArray(value) && value.length === 2) {
                        this.queryParameter.startDateBegin = value[0];
                        this.queryParameter.startDateEnd = value[1];
                    } else {
                        this.queryParameter.startDateBegin = '';
                        this.queryParameter.startDateEnd = '';
                    }
                } else if (key === 'alarmLevels') {
                    let alarmLevels = [];
                    // 告警需要特殊处理值为数值
                    if (value.length > 0) {
                        value.forEach(d => {
                            alarmLevels.push(parseInt(d));
                        });
                    }
                    this.queryParameter[key] = alarmLevels;
                } else {
                    this.queryParameter[key] = value;
                }
            }
        },
        query() {
            this.updateQueryParameter();
            $emit(this, 'query', this.queryParameter);
        },
        resetQuery() {
            for (let key in this.queryForm) {
                if (key === 'remainChargeBegin' || key === 'remainChargeEnd') {
                    this.queryForm[key] = '';
                } else {
                    this.queryForm[key] = [];
                }
            }
            this.query();
        },
        searchQuery() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    this.query();
                }
            });
        },
        getOptions() {
            this.requestOptions('types', 'getBattTypeOption', 'get'); // 电池类型
            this.requestOptions('prstSocLevels', 'battPrstSocLevelsOptions', 'get'); // 电池剩余容量级别
            this.requestOptions('alarmLevels', 'alarmLevelsOptions', 'get'); // 电池告警级别
            this.requestOptions('chargeStatus', 'battChargeStatusOptions', 'get'); // 电池运行状态
            this.requestOptions('ratedCapLevels', 'battRatedCapLevelsOptions', 'get'); // 额定容量等级
            this.requestOptions('manufacturers', 'manufacturersOptions', 'post'); // 电池厂商
            this.requestOptions('brands', 'brandsOptions', 'post'); // 电池品牌
            this.requestOptions('series', 'seriesOptions', 'post'); // 电池系列
            this.requestOptions('models', 'modelsOptions', 'post'); // 电池型号
            this.requestOptions('riskLevels', 'getRiskLevelOption', 'get'); // 风险等级
        },
        requestOptions(type, url, method, parameter = {}) {
            this.loadingOptions[type] = true;
            HTTP.request(url, {
                method: method,
                data: parameter,
                complete: data => {
                    this.loadingOptions[type] = false;
                    if (data.code === 0) {
                        this.options[type] = data.data || [];
                    } else {
                        this.options[type] = [];
                    }
                },
                error: data => {
                    console.log(data);
                    this.loadingOptions[type] = false;
                    this.options[type] = [];
                },
            });
        },
        remoteMethod(val, type) {
            let parameter = {};
            if (val && val.trim() !== '') {
                parameter = {
                    name: val,
                };
            }
            this.requestOptions(type, type + 'Options', 'post', parameter);
        },
        selectVisibleChange(args, attr) {
            if (args[0]) {
                // 下拉展开时需要重新请求下拉列表选项
                this.requestOptions(attr, attr + 'Options', 'post');
            }
        },
        queryAttributes() {
            // 查询资产通用属性
            HTTP.request('getActivityAssetDetail', {
                method: 'get',
                data: {
                    assetModelId: '82fafb6d-0419-49e3-b876-e07ca0460f57',
                },
                complete: resp => {
                    if (resp.code === 0) {
                        let generalAttrs = [];
                        resp.data.forEach(item1 => {
                            item1.groupAttributes.forEach(item2 => {
                                item2.attributes.forEach(item3 => {
                                    generalAttrs.push(item3);
                                });
                            });
                        });
                        generalAttrs.forEach(ele => {
                            for (let key in this.assetFormIsShow) {
                                if (key === ele.id) {
                                    this.assetFormIsShow[key] = ele.isEnable;
                                }
                            }
                        });
                    }
                },
                error: data => {
                    console.log('查询资产详情失败!' + data);
                },
            });
        },
    },
    emits: ['query', 'update:value'],
};
</script>

<style lang="scss" scoped>
.battLifeLevelsInput,
.battLifeLevelsInput :deep(.el-input__inner) {
    width: 105px !important;
    text-align: left;
}
.reaminTimeArea {
    display: flex;
}
.uedm-query-form__btn {
    margin-bottom: 0;
}
.uedm-query-form .el-form--inline .el-form-item {
    .start {
        padding: 0;
        flex: 0 0 45%;
    }
    .middleText {
        width: 10%;
        text-align: center;
    }
    .end {
        padding: 0;
        flex: 0 0 45%;
    }
}
</style>
