<script>
import List from './StandardPointList.vue';

export default {
    mixins: [List],
    data() {
        return {
            deleteTipInfo: '',
        };
    },
    created() {
        this.fields = this.$store.state.fields.get('ai');
        this.conditionField = '';
        this.queryName = '';
        this.deleteTipInfo = '';
        this.rowKey = 'smpId';
        this.expands = [];
    },
};
</script>
