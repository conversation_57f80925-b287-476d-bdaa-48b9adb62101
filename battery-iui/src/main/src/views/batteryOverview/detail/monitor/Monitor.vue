<template>
    <div class="monitor">
        <div v-show="!showMore">
            <div class="risk-monitor">
                <slot name="riskDetail"></slot>
                <div class="uedm-content-area" style="width: 100%; overflow: auto">
                    <div class="uedm-title">
                        {{ $t('batteryOverview.monitor.monitor') }}
                        <span style="margin-left: 14px">
                            <el-link :underline="false" type="primary" @click="toMonitor">
                                {{ $t('button.detail') }}
                            </el-link>
                        </span>
                    </div>
                    <monitor-base :id="id" ref="monitorBase" @show-more="handleShowMore"></monitor-base>
                </div>
            </div>

            <div v-if="rights['operation.fm.currentAlarm']" class="uedm-content-area" style="margin-top: 16px">
                <div class="title-div">
                    <span class="uedm-title">
                        {{ $t('battery-field-alarmStatus-alarm') }}
                    </span>
                    <span style="color: red">
                        {{ '(' + warningNumber + ')' }}
                    </span>
                    <span style="margin-left: 14px">
                        <el-link :underline="false" type="primary" @click="toWarning">
                            {{ $t('button.detail') }}
                        </el-link>
                    </span>
                </div>
                <alarm ref="alarm" :battery-id="id" @getWarningTotal="getWarningTotal"></alarm>
            </div>
        </div>
        <div v-if="showMore" :style="{ height: height + 'px' }">
            <div class="history-view">
                <site-device-history
                    ref="siteDeviceHistory"
                    :global-node-data="globalObj"
                    :current-point-data="currPointData"
                ></site-device-history>
            </div>

            <div class="back-button">
                <el-button v-if="showMore" class="return-button" size="mini" @click="closeHistory">
                    {{ $t('button.back') }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import MonitorBase from './MonitorBase.vue';
import Alarm from '../alarm/Alarm.vue';
import SiteDeviceHistory from './SiteDeviceHistory.vue';
import HTTP from '@/util/httpService.js';

export default {
    props: {
        id: {
            type: String,
            default: '',
        },
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
        battPathId: {
            default: () => {
                return '';
            },
        }
    },
    components: {
        'site-device-history': SiteDeviceHistory,
        'monitor-base': MonitorBase,
        alarm: Alarm,
    },
    data() {
        return {
            activeNames: ['monitor', 'alarm'],
            monitorBaseRef: null,
            alarmRef: null,
            showMore: false,
            monitorBaseData: {},
            globalObj: {},
            currPointData: {},
            warningNumber: 0,
            warningLevelNumber: {},
            time: null, // 时间戳
        };
    },
    computed: {
        titleStyle() {
            let width = this.$i18n.locale === 'zh-CN' ? '56px' : '96px';

            return {
                width,
            };
        },
        height() {
            return this.$store.getters.getHeight - 40;
        },
    },
    mounted() {
        this.monitorBaseRef = this.$refs.monitorBase;
        this.alarmRef = this.$refs.alarm;
        this.time = Date.now();
        // this.timingRequest();
        // this.getLevelWarningNum();
    },
    methods: {
        toWarning() {
            let url = `${window.uedmPathname}#/_ngict-fm-currentAlarm`;
            window.open(url, '_blank');
        },
        joinSplitPathId(str) {
            let pathArr = [];
            if (!str.trim()) {
                pathArr = [];
            }
            if (str.includes('/')) {
                pathArr = str.split('/');
            } else {
                pathArr = [str];
            }
            return pathArr.join('_');
        },
        toMonitor() {
            const url = new URL(`${window.location.origin}/${window.uedmPathname}#/_uedm-monitor-a-manager-reconstruct-normal`);
            const pathIdStr = this.joinSplitPathId(this.battPathId);
            if (pathIdStr) {
                url.searchParams.set('monitorObjectIds', pathIdStr);
            }
            url.searchParams.set('activeTab', 'detail');
            url.searchParams.set('flag', 'true');
            window.open(url);
        },
        updateProp(data) {
            this.activeNames = ['monitor', 'alarm'];
            this.monitorBaseRef.updateProp(data);
            this.alarmRef.updateProp(data);
        },
        getWarningTotal(val) {
            this.warningNumber = val;
        },
        handleShowMore(dateRange, props, tabPosition, currentNodeData) {
            let currPointData = Object.assign({}, props);
            this.globalObj = Object.assign(this.globalObj, currentNodeData);
            currPointData.pointName = props.name;
            // currPointData.pointName = this.globalObj.name + " " + props.name;
            this.globalObj.id = this.id;
            currPointData.pointId = {
                currObj: this.globalObj.id,
                smpId: props.smpId,
                tabPosition: tabPosition.toUpperCase(),
            };
            this.currPointData = currPointData;
            this.$nextTick(() => {
                let monitorBase = this.$refs.monitorBase;
                this.showMore = true;
                this.monitorBaseData = monitorBase.$data;
                monitorBase.queryHistoryData(this, dateRange);
                $emit(this, 'historyIsShow', false);
            });
        },
        closeHistory() {
            this.showMore = false;
            $emit(this, 'historyIsShow', true);
        },
    },
    emits: ['historyIsShow'],
};
</script>

<style scoped>
.blue-block {
    width: 4px;
    height: 16px;
    background-color: #0f8dfb;
    display: inline-block;
    position: relative;
    top: 2px;
    right: 4px;
}
.title-div {
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: bold;
}
.back-button {
    position: absolute;
    right: 0;
    top: 11px;
}
:deep(.mo-tree) hr {
    margin: 0px 5px 10px 5px;
}
:deep(.history-view) .show-more {
    display: none;
}
:deep(.history-view) .history {
    width: 96%;
}
:deep(.history-view) .echarts {
    height: 480px;
    margin-bottom: 8px;
}
/* :deep(.history-view) .el-table {
    height: 460px !important;
} */
:deep(.history-view) .el-tabs__header .el-tabs__item.is-active {
    border: none;
    border-bottom: 1px solid #d9d9d9;
}
:deep(.history-view) .el-tabs__header .el-tabs__item:hover {
    cursor: default !important;
}
:deep(.history-view) .el-tabs__header .el-tabs__item i:hover {
    cursor: pointer;
}
.risk-monitor {
    display: flex;
}
</style>
