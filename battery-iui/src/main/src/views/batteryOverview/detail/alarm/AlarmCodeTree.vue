<template>
    <div>
        <el-tree
            ref="tree"
            highlight-current
            :load="loadNode"
            :lazy="isLazy"
            :props="defaultProps"
            :expand-on-click-node="true"
            @node-click="handleClick"
        ></el-tree>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';

export default {
    data() {
        return {
            defaultProps: {
                children: 'children',
                isLeaf: 'leaf',
                label: 'name',
            },
            currentNode: null,
            isLazy: true,
            list: [],
            treeData: [],
        };
    },
    props: ['searchTxt'],
    watch: {
        searchTxt(val) {
            this.$refs.tree.store.setData([]);
            // console.warn(this.$refs.tree.store.root);
            this.loadNode(this.$refs.tree.store.root, data => {
                this.$refs.tree.store.root.doCreateChildren(data);
                let childNodes = this.$refs.tree.store.root.childNodes;
                if (childNodes && childNodes.length) {
                    childNodes.forEach(node => {
                        node.expand();
                    });
                }
            });
        },
    },
    mounted() {
        this.list = [];
    },
    methods: {
        handleClick(d, node) {
            $emit(this, 'node-click', d);
        },
        loadNode(node, resolve) {
            // console.warn(node);

            if (node.level === 0) {
                // 默认第一级
                this.getChild(node, data => {
                    return resolve(data);
                });
            } else if (node.data.children && node.data.children.length) {
                return resolve(node.data.children);
            } else {
                this.getTree(node, data => {
                    return resolve(data);
                });
            }
        },
        getTree(node, callback) {
            let param = {
                restype: node.data.restype,
                moc: node.data.id,
            };

            if (this.searchTxt) {
                param.namefilter = this.searchTxt;
            }

            HTTP.request('alarmcodetree', {
                urlParam: param,
                complete: data => {
                    // console.warn(data);
                    data.forEach(item => {
                        item.name = `${item.alarmcodename}(${item.alarmcode})`;
                        item.restype = node.data.restype;
                        item.leaf = true;
                    });
                    callback && callback(data);
                },
            });
        },
        getChild(node, callback) {
            let param = {
                codetype: 0,
            };

            if (this.searchTxt) {
                param.namefilter = this.searchTxt;
            }

            HTTP.request('restypetree', {
                urlParam: param,
                complete: data => {
                    // console.warn(data);

                    if (node.level === 0) {
                        let arr = [];
                        data.forEach(item => {
                            // console.warn(item.restypepath);
                            if (item.restypepath && item.restypepath.length) {
                                let result = arr.filter(value => {
                                    return value.id === item.restypepath[0][0].id;
                                });

                                // console.warn("result", result);
                                if (result.length) {
                                    result[0].children.push({
                                        name: item.restypename,
                                        id: item.mocid,
                                        restype: item.restype,
                                    });
                                } else {
                                    arr.push({
                                        name: item.restypepath[0][0].name,
                                        id: item.restypepath[0][0].id,
                                        children: [
                                            {
                                                name: item.restypename,
                                                id: item.mocid,
                                                restype: item.restype,
                                            },
                                        ],
                                    });
                                }
                            }
                        });

                        // console.warn(arr);
                        callback && callback(arr);
                    }
                },
            });
        },
    },
    emits: ['node-click'],
};
</script>

<style scoped>
:deep(.icon-treenode) {
    width: 18px;
    height: 18px;
    background-size: 16px 16px;
    padding-right: 20px;
}
</style>
