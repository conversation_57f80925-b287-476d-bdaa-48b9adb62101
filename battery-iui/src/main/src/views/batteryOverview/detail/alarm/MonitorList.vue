<template>
    <div>
        <div v-if="showCondition" class="test-area">
            <el-form :inline="true" :model="conditionForm">
                <template v-for="item in propList">
                    <el-form-item v-if="conditionItemIsShow(item)" :label="item.label" :prop="item.prop" :key="item.prop">
                        <el-input v-model="conditionForm[item.prop]"></el-input>
                    </el-form-item>
                </template>
            </el-form>
        </div>

        <div class="btn-group list-btn-group" style="overflow: hidden">
            <!-- 20200918, yanyan added -->
            <slot name="trail"></slot>

            <slot name="button"></slot>
            <el-input
                v-show="showSearch !== false"
                v-model="searchTxt"
                :prefix-icon="ElIconSearch"
                :placeholder="$t('placeholder.search')"
                class="search"
                style="float: right"
            ></el-input>
        </div>
        <div
            class="table-wrap"
            :class="{
                statusBreakGray: !isHistory && (mocommunication === '2' || mocommunication === '1'),
            }"
        >
            <el-table
                ref="monitorListTable"
                :data="list"
                style="width: 100%"
                border
                max-height="199"
                :row-key="rowKey"
                :expand-row-keys="expands"
                @row-click="handleRowClick"
            >
                <!-- <el-table-column type="expand" width="1">
                    <slot name="expand"></slot>
                </el-table-column> -->
                <el-table-column
                    v-bind="item"
                    v-for="(item, index) in columnList"
                    :key="'column-' + (item.prop || Math.randomValue()) + (item.listShowOrder || index)"
                    :label="item.label ? $t(item.label) : ''"
                    min-width="150"
                    :sortable="item.sortable ? item.sortable : false"
                    :formatter="item.formatter ? colFormatter(item) : null"
                >
                    <template v-slot="scope">
                        <!-- 超上限无效 -->
                        <i
                            v-if="scope.row.state && scope.row.state === '99' && item.prop == 'value'"
                            class="outOfLimitArrowUp disabled"
                            :title="$t('batteryOverview.title.exceedInvalid')"
                        ></i>
                        <!-- 超上限 -->
                        <i
                            v-else-if="
                                scope.row.state &&
                                    (scope.row.state == '2' || scope.row.state == '1') &&
                                    item.prop == 'value'
                            "
                            class="outOfLimitArrowUp"
                            :title="$t('batteryOverview.title.exceedUpper')"
                        ></i>
                        <!-- 超下限 -->
                        <i
                            v-else-if="
                                scope.row.state &&
                                    (scope.row.state == '-2' || scope.row.state == '-1') &&
                                    item.prop == 'value'
                            "
                            class="outOfLimitArrowDown"
                            :title="$t('batteryOverview.title.exceedLower')"
                        ></i>
                        <!-- 超下限无效 -->
                        <i
                            v-else-if="scope.row.state && scope.row.state === '-99' && item.prop == 'value'"
                            class="outOfLimitArrowDown disabled"
                            :title="$t('batteryOverview.title.exceedInvalid')"
                        ></i>
                        <slot v-if="item.prop == 'perceivedseverity'" name="perceivedseverity" :data="scope.row"></slot>
                        <slot v-else-if="item.prop == 'ackstate'" name="ackstate" :data="scope.row"></slot>
                        <slot v-else-if="item.prop == 'alarmstate'" name="alarmstate" :data="scope.row"></slot>
                        <slot v-else>
                            {{ scope.row[item.prop] }}
                        </slot>
                        <!-- 过期 -->
                        <i
                            v-if="scope.row.invalidTime && scope.row.invalidTime === 'expired' && item.prop == 'value'"
                            class="icon-expired"
                            :title="$t('batteryOverview.title.expired')"
                        ></i>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="!notShowOperation"
                    fixed="right"
                    :label="$t('table.operation')"
                    width="150"
                >
                    <template v-slot="scope">
                        <slot name="operate" :data="scope.row" :communication="mocommunication"></slot>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <confirm-dialog
            v-model:visible="deleteDialog"
            :loading="deleteLoading"
            type="warning"
            :title="'dialog.deleteConfirm'"
            @confirm="deleteConfirm"
        >
            <template v-slot:message>
                {{ $t('tipMessage.singleDelTips') }}
            </template>
        </confirm-dialog>
    </div>
</template>

<script>
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import ConfirmDialog from '../alarm/ConfirmDialog.vue';
import ColumnPlus from '../alarm/ColumnPlus.vue';
import HTTP from '@/util/httpService.js';

export default {
    data() {
        return {
            editDialog: false,
            deleteDialog: false,
            list: [],
            allList: [],
            tempAllList: [],
            readyToDeleteItem: null,
            deleteLoading: false,
            itemInfo: null,
            // 分页相关
            currentPageSize: this.pageSize || 10,
            pageNo: 1,
            total: 0,
            selection: [],
            loading: false,
            fields: [],
            conditionField: '',
            conditionForm: {},
            radioChoosed: '',
            searchTxt: '',
            expands: [],
            rowKey: 'id',
            warningNumList: {
                1: null,
                2: null,
                3: null,
                4: null,
            },
            ElIconSearch,
        };
    },
    props: [
        'notShowSelect',
        'notShowOperation',
        'mode',
        'pageSize',
        'operation',
        'showSearch',
        'condition',
        'pagination',
        'expandable',
        'selectedPoints',
        'currObj',
        'currentTabPosition',
        'isHistory',
        'pageType',
        'batteryId',
        'searchWarningTxt',
        'iconList',
    ],
    inject: {
        showCondition: {
            default: () => {
                return true;
            },
        },
        rights: {
            default: () => {
                return {};
            },
        },
    },
    provide() {
        return {
            list: this,
        };
    },
    watch: {
        searchWarningTxt(val) {
            this.getAlarmData(val);
            this.getAlarmNumber(val);
        },
        condition(val) {
            this.queryByCondition(val);
        },
        list(val) {
            this.$nextTick(() => {
                this.$refs.monitorListTable && this.$refs.monitorListTable.doLayout();
            });
        },
        selectedPoints: {
            deep: true,
            handler(val) {
                this.$refs.monitorListTable.clearSelection();
                val.forEach(d => {
                    // if (d.pointId.split(";")[0] === this.currObj.id) {
                    if (d.pointId.currObj === this.currObj.id) {
                        this.allList.forEach(k => {
                            if (d.smpId === k.smpId) {
                                this.$refs.monitorListTable.toggleRowSelection(k, true);
                            }
                        });
                    }
                });
            },
        },
    },
    computed: {
        conditionFieldArray() {
            return this.conditionField.split(',');
        },
        propList() {
            return this.fields.filter(item => {
                // eslint-disable-next-line no-prototype-builtins
                return item.hasOwnProperty('prop');
            });
        },
        columnList() {
            return this.fields.filter(item => {
                // eslint-disable-next-line no-prototype-builtins
                return item.hasOwnProperty('listShowOrder');
            });
        },
        mocommunication() {
            return this.$store.getters.getMocommunication;
        },
    },
    mounted() {
        this.getAlarmData('');
        this.getAlarmNumber('');
        if (this.selectedPoints) {
            this.rowKey = 'smpId';
        }
    },
    methods: {
        colFormatter(item) {
            return item.formatter.bind(this);
        },
        isClient() {
            return this.pagination === 'client';
        },
        getRadioChoosed() {
            if (this.radioChoosed) {
                for (let i = 0, max = this.list.length; i < max; i++) {
                    if (this.list[i].id === this.radioChoosed) {
                        return JSON.parse(JSON.stringify(this.list[i]));
                    }
                }
            }
        },
        conditionItemIsShow(item) {
            return item.prop && this.conditionFieldArray.indexOf(item.prop) !== -1;
        },
        tableSelectionChange(selection) {
            if (!this.selectedPoints) {
                this.selection = selection;
            }
        },
        getAlarmData(schData) {
            // this.list = [];
            // this.tableLoading = true;
            HTTP.request('alarmInfo', {
                method: 'post',
                data: {
                    ackState: [],
                    alarmClearedTimeEnd: '',
                    alarmClearedTimeStart: '',
                    alarmCodes: [],
                    alarmRaisedTimeEnd: '',
                    alarmRaisedTimeStart: '',
                    alarmState: ['1'],
                    hasChildNode: false,
                    id: this.batteryId,
                    schData: schData,
                    severities: [],
                    fromOthers: 1,
                },
                complete: data => {
                    this.loading = false;
                    // this.tableLoading = false;
                    if (data.code === 0) {
                        this.list = data.data;
                        this.total = data.total;
                        this.$nextTick(() => {
                            this.$refs.monitorListTable.doLayout();
                        });
                    } else {
                        this.list = [];
                        this.total = 0;
                    }
                },
                error: data => {
                    this.loading = false;
                    // this.tableLoading = false;
                    this.list = [];
                    this.total = 0;
                },
            });
        },
        getAlarmNumber(schData) {
            HTTP.request('getLevelWarningNum', {
                method: 'post',
                data: {
                    ackState: [],
                    alarmClearedTimeEnd: '',
                    alarmClearedTimeStart: '',
                    alarmCodes: [],
                    alarmRaisedTimeEnd: '',
                    alarmRaisedTimeStart: '',
                    alarmState: ['1'],
                    hasChildNode: false,
                    id: this.batteryId,
                    schData: schData,
                    severities: [],
                    fromOthers: 1,
                },
                complete: data => {
                    if (data.code === 0) {
                        this.iconList.forEach(item => {
                            if (item.id == 1) {
                                item.num = data.data[1];
                            }
                            if (item.id == 2) {
                                item.num = data.data[2];
                            }
                            if (item.id == 3) {
                                item.num = data.data[3];
                            }
                            if (item.id == 4) {
                                item.num = data.data[4];
                            }
                        });
                        let total = data.data[1] + data.data[2] + data.data[3] + data.data[4];
                        $emit(this, 'getIconList', this.iconList, total);
                    }
                },
                error: data => {
                    this.loading = false;
                    this.list = [];
                    this.total = 0;
                },
            });
        },
        batchDelete() {
            let ids = this.selection.map(item => {
                return item.id;
            });

            if (!ids || ids.length === 0) {
                this.$message({
                    type: 'warning',
                    message: this.$t('tipMessage.notNullWarning'),
                });
                return;
            }

            HTTP.request(this.batchDeleteQueryName, {
                method: 'delete',
                data: ids,
                complete: data => {
                    console.warn(data);
                    if (data.code === 0) {
                        this.$message({
                            type: 'success',
                            message: this.$t('tipMessage.deleteSuccess'),
                        });
                        this.deleteDialog = false;

                        for (let i = 0, max = this.list.length; i < max; i++) {
                            if (ids.indexOf(this.list[i].id) !== -1) {
                                this.list.splice(i, 1);
                                i--; // NOSONAR
                            }
                        }
                    } else {
                        this.$message({
                            type: 'error',
                            message: this.$t('tipMessage.deleteError'),
                        });
                    }
                },
            });
        },
        editDone(item) {
            let itemId = item.id;
            for (let i = 0, max = this.list.length; i < max; i++) {
                if (this.list[i].id === itemId) {
                    // item拷贝出来是解决编辑成功后，再打开编辑框的一刻会影响列表中的值
                    this.list.splice(i, 1, JSON.parse(JSON.stringify(item)));
                    break;
                }
            }
        },
        refreshData() {
            this.initPage();
            this.getAlarmData('');
            this.getAlarmNumber('');
        },
        getRequestParam(param, condition) {
            param.data = {};

            for (let key in condition) {
                // eslint-disable-next-line no-prototype-builtins
                if (condition.hasOwnProperty(key)) {
                    if (key === 'urlParam') {
                        param.urlParam = Object.assign(param.urlParam, condition.urlParam);
                    } else if (key === 'requestData') {
                        param.data = condition.requestData;
                    } else {
                        param[key] = condition[key];
                    }
                }
            }

            // console.warn("---", this.conditionForm);

            for (let key in this.conditionForm) {
                // eslint-disable-next-line no-prototype-builtins
                if (this.conditionForm.hasOwnProperty(key)) {
                    param.data[key] = this.conditionForm[key];
                }
            }

            return param;
        },
        handleSearchCondition(val) {
            return {
                requestData: {
                    name: val,
                },
            };
        },
        initOpen() {
            this.searchTxt = '';
        },
        initPage() {
            this.radioChoosed = '';
        },
        setTotalData(list, page = this.pageNo) {
            // 用于客户端分页
            this.initPage();
            this.allList = list;
            let start = (page - 1) * this.currentPageSize;
            let end = start + this.currentPageSize;
            this.list = this.allList;
            this.total = list.length;
            this.expands = [];
        },
        timingSetTotalData(list, page = this.pageNo) {
            // 用于定时刷新分页，保留上次选择的页码

            this.radioChoosed = '';

            this.allList = list;
            let start = (page - 1) * this.currentPageSize;
            let end = start + this.currentPageSize;
            this.list = this.allList;
            this.total = list.length;
            this.expands = [];
        },

        selectRow(selection, row) {
            let removeRow = true;
            console.log('alex -> row', row);
            // if (row.pointId.split(";")[0] === this.currObj.id) {
            if (row.pointId.currObj === this.currObj.id) {
                selection.forEach(d => {
                    if (d.smpId === row.smpId) {
                        removeRow = false;
                    }
                });
            }
            $emit(this, 'listTableRowClick', row, removeRow);
        },
        selectAllRow(selection) {
            console.log('selection in all', selection);
            if (selection.length) {
                selection.forEach(d => {
                    $emit(this, 'listTableRowClick', d);
                });
            } else {
                this.allList.forEach(d => {
                    $emit(this, 'listTableRowClick', d, true);
                });
            }
        },
        selectableFunc(row, index) {
            let points = this.selectedPoints;
            if (!points) {
                return true;
            }
            if (!Array.isArray(points) || points.length >= 10) {
                for (let i = 0; i < points.length; i++) {
                    if (row.pointId === points[i].pointId) {
                        return true;
                    }
                }
                return false;
            }
            return true;
        },

        handleClose() {},
        itemDetail(info) {
            $emit(this, 'dialog:edit', info);
        },
        deleteItem(row) {
            this.deleteDialog = true;
            this.readyToDeleteItem = row;
        },
        deleteConfirm() {
            let id = this.readyToDeleteItem.id;
            this.deleteLoading = true;
            HTTP.request(this.deleteQueryName, {
                method: 'delete',
                data: [id],
                complete: data => {
                    this.deleteLoading = false;
                    console.warn(data);
                    if (data.code === 0) {
                        this.$message({
                            type: 'success',
                            message: this.$t('tipMessage.deleteSuccess'),
                        });
                        this.deleteDialog = false;

                        for (let i = 0, max = this.list.length; i < max; i++) {
                            if (this.list[i].id === id) {
                                this.list.splice(i, 1);
                                break;
                            }
                        }
                    } else {
                        this.$message({
                            type: 'error',
                            message: this.$t('tipMessage.deleteError'),
                        });
                    }
                },
                error: err => {
                    this.deleteLoading = false;
                },
            });
        },
        queryByCondition(val) {
            console.warn('----->', val);
            this.setCondition(val);
            this.refreshData();
        },
        setCondition(val) {
            this.conditionForm = {};
            for (let key in val) {
                if (this.conditionField.indexOf(key) !== -1) {
                    this.conditionForm[key] = val[key];
                }
            }
            console.warn('==============', this.conditionForm);
        },
        handleRowClick(row, event, column) {
            this.radioChoosed = row.id;
            // this.$emit("listTableRowClick", row);
        },
    },
    components: {
        'confirm-dialog': ConfirmDialog,
    },
    emits: ['getIconList', 'listTableRowClick', 'dialog:edit'],
};
</script>

<style lang="scss" scoped>
:deep(.el-table__expand-icon) {
    overflow: hidden;
}

:deep(.el-table__header),
.el-table__body,
.el-table__footer {
    width: 100%;
    table-layout: fixed !important;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar {
    height: 8px;
}

:deep(.table-wrap) {
    position: relative;
    bottom: 22px;
}

:deep(.el-table__empty-block) {
    height: 99%;
    width: 100% !important;
}

:deep(.el-table__fixed-right) {
    height: 100% !important;
}

:deep(.el-table__empty-text) {
    margin-top: 30px;
    width: 97px;
    height: 125px;
    padding-top: 70px;
    background: url(~@/assets/img/table__empty-text.png) no-repeat;
    color: #bfbfbf;
    box-sizing: border-box;
}
.alarm-level {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0;
}
.alarm-level-1 {
    background-color: #d14242;
}
.alarm-level-2 {
    background-color: #ff8e19;
}
.alarm-level-3 {
    background-color: #fcc652;
}
.alarm-level-4 {
    background-color: #5b9fec;
}
</style>
