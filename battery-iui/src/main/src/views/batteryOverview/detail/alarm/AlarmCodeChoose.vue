<template>
    <div style="height: 300px">
        <div>
            <el-row class="chooseBox">
                <el-col :span="12">
                    <div>
                        <h4>
                            <span class="bar"></span>
                            {{ $t('batteryOverview.monitor.alarmCode') }}
                        </h4>
                        <div class="left-content-wrap content-wrap">
                            <div class="search-input-wrap">
                                <el-input
                                    v-model="pointSearchTxt"
                                    :prefix-icon="ElIconSearch"
                                    :placeholder="$t('placeholder.search')"
                                ></el-input>
                            </div>

                            <div class="left-content">
                                <alarmcode-tree :search-txt="pointSearchTxt" @node-click="nodeClick"></alarmcode-tree>
                            </div>
                        </div>
                    </div>
                </el-col>

                <el-col :span="12">
                    <div class="pane">
                        <h4>
                            <span class="bar"></span>
                            {{ $t('batteryOverview.monitor.selected') }}
                        </h4>

                        <div class="right-content-wrap content-wrap">
                            <ul>
                                <li v-for="(item, index) in rightList" :key="'rightList-' + index">
                                    <el-tag size="medium" closable @close="handleClose(index)">
                                        {{ item.name }}
                                    </el-tag>
                                </li>
                            </ul>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <div class="left-side"></div>
        </div>
        <!-- <template #footer> -->
        <span class="dialog-footer">
            <el-button type="primary" @click="handleSave">{{ $t('button.confirm') }}</el-button>
            <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
        </span>
        <!-- </template> -->
    </div>
</template>

<script>
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import AlarmCodeTree from './AlarmCodeTree';
export default {
    data() {
        return {
            rightList: [],
            pointSearchTxt: '',
            ElIconSearch,
        };
    },
    props: ['alarmcodeList'],
    watch: {
        alarmcodeList(data) {
            this.rightList = JSON.parse(JSON.stringify(data));
        },
    },
    mounted() {
        this.rightList = JSON.parse(JSON.stringify(this.alarmcodeList));
    },
    methods: {
        handleClose(index) {
            this.rightList.splice(index, 1);
        },
        nodeClick(data) {
            if (data.alarmcode) {
                let result = this.rightList.filter(item => {
                    return item.alarmcode === data.alarmcode && item.restype === data.restype;
                });

                if (result.length === 0) {
                    this.rightList.push({
                        name: data.name,
                        alarmcode: data.alarmcode,
                        restype: data.restype,
                    });
                }
            }
        },
        cancel() {
            this.rightList = JSON.parse(JSON.stringify(this.alarmcodeList));
            $emit(this, 'cancel');
        },
        handleSave() {
            $emit(this, 'save', this.rightList);
        },
        reset() {
            this.rightList.splice(0, this.rightList.length);
        },
    },
    components: {
        'alarmcode-tree': AlarmCodeTree,
    },
    emits: ['save', 'cancel'],
};
</script>

<style scoped>
.chooseBox :deep(.content-wrap) {
    height: 209px;
}

.chooseBox .el-col:first-child {
    padding-right: 10px;
}

.chooseBox .el-col:last-child {
    padding-left: 10px;
}

.left-content {
    height: 160px;
    overflow: auto;
}

.right-content-wrap {
    height: 160px;
    overflow: auto;
}
.right-content-wrap li {
    margin-bottom: 5px;
}

.dialog-footer {
    display: block;
    text-align: center;
    padding-top: 10px;
}
</style>
