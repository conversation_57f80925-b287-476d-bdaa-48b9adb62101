<template>
    <div v-loading="pageLoading" class="battery-view__page space" style="padding-top: 0;" spinner="el-icon-loading">
        <div class="battery-view__title">
            <span class="uedm-title">
                {{ $t('batteryView.batteryUnit') }}
            </span>
            <el-button class="battery-view__title-btn" :loading="exportLoading" @click="exportData">
                {{ $t('batteryView.export') }}
            </el-button>
        </div>
        <chart-component
            ref="chart"
            class="bar-chart"
            :all-data="chartData"
            :show-battery-view="showBatteryView"
            @base64UrlsMap="getBase64Urls"
            @data-zoom-changed="debounceDeliver"
        ></chart-component>
        <battery-table ref="table" :table-props="tableProps" :table-data="tableData" :offset="offset"></battery-table>
    </div>
</template>

<script>
import forEach from 'lodash/forEach';
import debounce from 'lodash/debounce';
import find from 'lodash/find';
import keys from 'lodash/keys';
import HTTP from '@/util/httpService.js';
import ChartComponent from './chartComponent.vue';
import BatteryTable from './batteryTable.vue';
import { download, getLang, transformedValue } from './common';
import axios from 'axios';

const VOLTAGE = 'voltage';
const RESISTENCE = 'resistence';
const TEMPERATURE = 'temperature';

const VOLTAGE_SMPIDS = 'battery.cellvoltage';
const RESISTENCE_SMPIDS = 'battery.cellresistance';
const TEMPERATURE_SMPIDS = 'battery.celltemp';

const LANGUAGE = getLang();

export default {
    props: {
        row: {
            type: Object,
            default: () => {},
        },
        showBatteryView: Boolean,
    },
    components: {
        ChartComponent,
        BatteryTable,
    },
    created() {
        this.debounceDeliver = debounce(offset => this.deliverHandler(offset), 500);
        this.getData();
    },
    data() {
        this.base64UrlsMap = {};
        return {
            pageLoading: false,
            exportLoading: false,
            chartData: {
                unitNumberArr: [],
                voltageValueArr: [],
                temperatureValueArr: [],
                resistanceValueArr: [],
            },
            tableProps: [],
            tableData: [
                { headerProp: `${this.$t('batteryView.voltage')}(V)` },
                { headerProp: `${this.$t('batteryView.resistance')}(Ω)` },
                { headerProp: `${this.$t('batteryView.temperature')}(℃)` },
            ],
            offset: 0,
        };
    },
    methods: {
        getData() {
            this.pageLoading = true;
            HTTP.request('getBatteryView', {
                method: 'post',
                data: {
                    battId: this.row?.id || '',
                    smpIds: [VOLTAGE_SMPIDS, RESISTENCE_SMPIDS, TEMPERATURE_SMPIDS],
                    pageNo: null,
                    pageSize: null,
                    // request: '',
                    'language-option': LANGUAGE,
                },
                complete: data => {
                    this.pageLoading = false;
                    if (data.code === 0) {
                        this.transformData(data.data);
                    }
                },
                error: () => {
                    this.pageLoading = false;
                },
            });
        },
        transformData(data = []) {
            forEach(data, (item, index) => {
                this.chartData.unitNumberArr.push(item.index);
                const voltageValue = this.getValue(item.dataMap, VOLTAGE_SMPIDS);
                const resistanceValue = this.getValue(item.dataMap, RESISTENCE_SMPIDS);
                const temperatureValue = this.getValue(item.dataMap, TEMPERATURE_SMPIDS);
                voltageValue && (this.chartData.voltageValueArr[index] = voltageValue);
                resistanceValue && (this.chartData.resistanceValueArr[index] = resistanceValue);
                temperatureValue && (this.chartData.temperatureValueArr[index] = temperatureValue);

                const voltageLevel = this.getValue(item.alarmLevelMap, VOLTAGE_SMPIDS);
                const resistanceLevel = this.getValue(item.alarmLevelMap, RESISTENCE_SMPIDS);
                const temperatureLevel = this.getValue(item.alarmLevelMap, TEMPERATURE_SMPIDS);
                this.tableProps.push({ prop: `${item.index}`, label: `${item.index}` });

                // 一组电压值/电阻值/温度值分别作一行
                Object.assign(this.tableData[0], {
                    [item.index]: transformedValue(voltageValue),
                    [`${VOLTAGE_SMPIDS}.cell_${item.index}`]: voltageLevel,
                });
                Object.assign(this.tableData[1], {
                    [item.index]: transformedValue(resistanceValue),
                    [`${RESISTENCE_SMPIDS}.cell_${item.index}`]: resistanceLevel,
                });
                Object.assign(this.tableData[2], {
                    [item.index]: transformedValue(temperatureValue),
                    [`${TEMPERATURE_SMPIDS}.cell_${item.index}`]: temperatureLevel,
                });
            });
        },
        getValue(dataMap, prop) {
            const key = this.getKey(dataMap, prop);
            return dataMap[key] || '';
        },
        getKey(dataMap, prop) {
            return find(keys(dataMap), k => k.includes(prop));
        },
        getBase64Urls(urlsMap) {
            this.base64UrlsMap = urlsMap;
        },
        exportData() {
            const images = [
                {
                    base64Str: this.base64UrlsMap[VOLTAGE],
                    imageName: `${this.$t('batteryView.voltage')}(V)`,
                    xline: 0,
                    yline: 1,
                },
                {
                    base64Str: this.base64UrlsMap[RESISTENCE],
                    imageName: `${this.$t('batteryView.internalResistance')}(Ω)`,
                    xline: 2,
                    yline: 4,
                },
                {
                    base64Str: this.base64UrlsMap[TEMPERATURE],
                    imageName: `${this.$t('batteryView.temperature')}(℃)`,
                    xline: 5,
                    yline: 8,
                },
            ];
            this.exportLoading = true;
            const queryParameter = {
                battId: this.row?.id || '',
                smpIds: [VOLTAGE_SMPIDS, RESISTENCE_SMPIDS, TEMPERATURE_SMPIDS],
                pageNo: null,
                pageSize: null,
                images,
            };
            /* Started by AICoder, pid:h68bf6a0b0jafca149ea0a35d031f91b35c0433c */
            const forgerydefense = window.forgerydefense || '';
            const DOWNLOAD_URL = '/api/battery-manager/v1/battery-cell/statistics-real-data/export-by-smpId';
            const url = `${DOWNLOAD_URL}`;
            const config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            /* Ended by AICoder, pid:h68bf6a0b0jafca149ea0a35d031f91b35c0433c */
            /* Started by AICoder, pid:m62ec5d6eaq26c514f9e0bd7608d7f6a9ea1b8c5 */
            axios
                .post(url, queryParameter, config)
                .then(res => {
                    this.exportLoading = false;
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                let errorMessage = '';
                                switch (result.code) {
                                    case -301:
                                        errorMessage = this.$t('tipMessage.cannotNull') + ': ' + result.error;
                                        break;
                                    case -304:
                                        errorMessage = this.$t('tipMessage.beyondOptionalRange') + ': ' + result.error;
                                        break;
                                    case -307:
                                        errorMessage = this.$t('tipMessage.cannotNull') + ': ' + result.error;
                                        break;
                                    case -208:
                                        errorMessage = this.$t('tipMessage.attributeNotEnabled') + ': ' + result.error;
                                        break;
                                    default:
                                        errorMessage = this.$t('tipMessage.exportError') + ': ' + result.message || result.error;
                                        break;
                                }
                                this.$message({
                                    message: errorMessage,
                                    duration: 5000,
                                    showClose: true,
                                    type: 'error',
                                });
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        const blob = new Blob([res.data]);
                        const url = window.URL.createObjectURL(blob); // 创建下载的链接
                        const link = document.createElement('a');
                        let fileName = '';
                        if (res.headers['content-disposition']) {
                            const contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                    })
                .catch(() => {
                    this.exportLoading = false;
                });
            /* Ended by AICoder, pid:m62ec5d6eaq26c514f9e0bd7608d7f6a9ea1b8c5 */
        },
        deliverHandler(offset) {
            this.offset = offset;
        },
    },
};
</script>

<style scoped>
.battery-view__title {
    position: relative;
    padding: 0;
}
.battery-view__title-label {
    position: relative;
    font-size: 14px;
    font-weight: 600;
}
.battery-view__title-btn.el-button {
    position: absolute;
    right: 0px;
    color: #2c89e6;
}
</style>
