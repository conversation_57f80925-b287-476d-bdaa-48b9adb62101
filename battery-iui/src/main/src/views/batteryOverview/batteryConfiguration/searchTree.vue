<template>
    <div class="mo-tree">
        <el-input
            v-model="searchText"
            class="search-input"
            :placeholder="$t('placeholder.search')"
            autocomplete="on"
            size="mini"
            clearable
            @clear="clearSearch"
        >
            <template #prefix>
                <el-icon class="el-input__icon"><el-icon-search /></el-icon>
            </template>
        </el-input>
        <div v-loading="searchLoading" class="elTreeWrap newTreeStyle" :style="treeStle">
            <el-tree
                v-show="searchText.trim() === ''"
                ref="tree"
                :props="defaultProps"
                node-key="id"
                :load="loadNode"
                :default-expanded-keys="defaultExpanded"
                :lazy="true"
                highlight-current
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender"
                @node-click="handleClick"
            ></el-tree>
            <!-- 搜索树 -->
            <el-tree
                v-show="searchText"
                ref="searchTree"
                :props="defaultProps"
                node-key="id"
                :data="searchTreeData"
                highlight-current
                :current-node-key="currentId"
                :expand-on-click-node="false"
                :render-content="nodeRender_search"
                :default-expand-all="true"
                @node-expand="handleExpand"
                @node-click="handleClick"
            ></el-tree>
            <div
                v-if="searchText.trim() && searchTreeData.length > 0"
                style="text-align: center; padding-top: 20px; padding-bottom: 20px"
            >
                <el-button
                    plain
                    :class="[showMore ? 'hasMore' : 'nomore']"
                    size="mini"
                    :disabled="!showMore"
                    @click="searchMore"
                >
                    {{ showMore ? $t('tipMessage.moreResult') : $t('tipMessage.noMore') }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="jsx">
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { $emit } from '../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import { modelIdsMap } from '@/util/constants.js';
import { useTreeIcon } from '@/hooks/useTreeIcon.js';

export default {
    setup() {
        const { loadNodeIcons,
            getTreeIconByMoc,
            getIconColor,
            getDefaultIconByModel,
            getIconColorWithAlarmLevel
        } = useTreeIcon();
        return {
            loadNodeIcons,
            getTreeIconByMoc,
            getIconColor,
            getDefaultIconByModel,
            getIconColorWithAlarmLevel
        };
    },
    data() {
        return {
            defaultProps: {
                label: 'name',
                children: 'children',
                isLeaf: 'isLeaf',
            },
            iconLevelClassName: {
                Root: 'iconfont iconmd-tree-root',
                RealGroup: 'iconfont iconmo-tree-realgroup',
                Site: 'iconfont iconmo-tree-site',
                CoreSite: 'iconfont iconmo-tree-site',
                Building: 'iconfont iconmo-tree-building',
                Floor: 'iconfont iconmo-tree-floor',
                DataCenter: 'iconfont iconmo-tree-datacenter',
                Room: 'iconfont iconmo-tree-room',
                Mdc: 'iconfont iconmo-tree-mdc',
                MonitorObject: 'iconfont iconmo-tree-monitorobject',
                MonitorDevice: 'iconfont iconmo-tree-device',
                More: 'el-icon-more',
            },
            currentId: '',
            pageSize: 100,
            alarmTimer: null,
            // 搜索树
            searchText: '',
            searchTreeData: [],
            searchPageSize: 100,
            searchPage: 1,
            showMore: true,
            searchLoading: false,
            checkedNodes: [],
        };
    },
    components: {
        ElIconSearch,
    },
    name: 'DeviceTree',
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        height: {
            type: Number,
            default: null,
        },
        defaultExpanded: {
            type: Array,
            default: () => {
                return [];
            },
        },
        defaultNodeId: {
            type: String,
            default: null,
        },
    },
    computed: {
        treeStle() {
            let h = 'auto';
            if (this.height) {
                h = this.height - 56 - 16 + 'px'; // 56为搜索区域高度(包含分割线),16为底部多留距离
            }
            let item = {
                height: h,
            };
            return item;
        },
    },
    watch: {
        searchText(val) {
            this.searchTreeData = [];
            let keyWord = val.trim();
            if (!keyWord) {
                this.searchLoading = false;
                return;
            }
            this.searchTree(val);
        },
    },
    created() {
        if (this.defaultNodeId) {
            this.currentId = this.defaultNodeId;
        }
    },
    mounted() {
        // 如果有告警查询权限则定时更新告警状态
        if (this.rights['operation.fm.currentAlarm']) {
            this.alarmTimer = setInterval(() => {
                let ids = this.getAllNodeIds(); // 获取已展开树节点id
                this.statisticsseverities(ids); // 获取已展开树节点的告警统计并更新图标
            }, 15000);
        }
    },
    beforeUnmount() {
        if (this.alarmTimer) {
            clearInterval(this.alarmTimer);
            this.alarmTimer = null;
        }
    },
    methods: {
        clearSearch() {
            this.searchPage = 1;
        },
        loopNode(node, func) {
            if (node.data) {
                func(node);
            }
            if (!node.childNodes || node.childNodes.length === 0) {
                return;
            }
            for (let i = 0, max = node.childNodes.length; i < max; i++) {
                this.loopNode(node.childNodes[i], func);
            }
        },
        handleClick(d, node) {
            if (node.data.id === '#more') {
                let nodePage = node.data.page;
                let parentId = node.data.parentId;
                let total = node.data.total;
                let parentNode = node.parent;

                // 注意parentNode的处理
                // 注意监控对象树没有根节点, 如果是根（假的）的情况，直接传一个null才正确, 同时树上的data需要设置成[]，然后“根节点”才能正常的append，否则会报错
                if (parentNode.level === 0) {
                    parentNode = null; //
                }

                console.warn('parentNode ==> ', parentNode);

                node.loading = 'true';

                this.getChild(parentId, nodePage, d1 => {
                    node.loading = 'false';
                    d1.data.forEach(nd => {
                        // append该节点上前，最好判断下，该节点是否已经存在这个节点下
                        let notFound = true;
                        node.parent.childNodes.forEach(n => {
                            if (n.data.id === nd.id) {
                                notFound = false;
                            }
                        });
                        if (notFound) {
                            this.$refs.tree.append(nd, parentNode);
                        }
                    });

                    this.$refs.tree.remove(node);
                    // 没加载完，需要添加“加载更多”
                    if (nodePage + 1 <= Math.ceil(total / this.pageSize)) {
                        this.$refs.tree.append(this.getMoreNode(parentId, nodePage + 1, total), parentNode);
                    }
                    this.$nextTick(() => {
                        this.$refs.tree.setCurrentKey(this.currentId);
                    });
                });
            } else {
                this.currentId = node.data.id;
                let defaultExpand = [];
                let defaultExpandIds = [];
                let nodeUrl = this.getNodeUrl(node);
                defaultExpand = nodeUrl.names;
                defaultExpandIds = nodeUrl.ids;
                $emit(this, 'nodeClick', node.data, defaultExpand, defaultExpandIds);
                this.$nextTick(() => {
                    let currentNode = this.$refs.tree.getCurrentNode();
                    if (currentNode && currentNode.id !== this.currentId) {
                        this.$refs.tree.setCurrentKey(null);
                        this.$refs.tree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.tree.setCurrentKey(this.currentId);
                    }
                    let currentNodeSearch = this.$refs.searchTree.getCurrentNode();
                    if (currentNodeSearch && currentNodeSearch.id !== this.currentId) {
                        this.$refs.searchTree.setCurrentKey(null);
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    } else {
                        this.$refs.searchTree.setCurrentKey(this.currentId);
                    }
                });
            }
        },
        nodeRenderBase(data) {
            if (data.id === '#more') {
                return (
                    <span class="el-tree-node__label">
                        <div class="node-name-container">
                            <div style={{ position: 'relative', height: '14px', width: '18px' }}></div>
                            {data.name}
                        </div>
                    </span>
                );
            }
            return (
                <span class="el-tree-node__label">
                    <div class="node-name-container">
                        <div style={{ position: 'relative', height: '14px' }}>
                            {this.getTreeIconByMoc(data.moc) ? (
                                <svg-icon
                                    size="14"
                                    base64={this.getTreeIconByMoc(data.moc)}
                                    color={this.getIconColorWithAlarmLevel(data.alarmLevel)}
                                    class="tree-node-icon"
                                />
                            ) : (
                                <svg-icon
                                    name={this.getDefaultIconByModel(data.model)}
                                    color={this.getIconColorWithAlarmLevel(data.alarmLevel)}
                                    size="14"
                                    class="tree-node-icon"
                                />
                            )}
                        </div>
                        {data.name}
                    </div>
                </span>
            );
        },
        nodeRender(h, { data }) {
            return this.nodeRenderBase(data);
        },
        nodeRender_search(h, { node, data }) {
            if (!node.isRealLeaf && data.id !== '#more') {
                node.isLeaf = false;
            }
            if (node.childNodes.length === 0) {
                node.expanded = false;
            }
            return this.nodeRenderBase(data);
        },
        loadNode(node, resolve) {
            let id = '';
            // 子节点加载
            if (node.data && !Array.isArray(node.data)) {
                id = node.data.id;
            }
            this.getChild(id, 1, d => {
                let data = d.data;
                // 根据返回的数据，判断是否需要加“加载更多”
                if (2 <= Math.ceil(d.total / this.pageSize)) {
                    data.push(this.getMoreNode(id, 2, d.total));
                }
                this.$nextTick(() => {
                    if (this.currentId) {
                        this.$refs.tree && this.$refs.tree.setCurrentKey(this.currentId);
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].id === this.currentId) {
                                let node = this.$refs.tree.getNode(this.currentId);
                                this.handleClick(node.data, node);
                            }
                        }
                    } else if (data && data.length > 0) {
                        this.currentId = data[0].id;
                        this.$refs.tree && this.$refs.tree.setCurrentKey(this.currentId);
                        let node = this.$refs.tree.getNode(this.currentId);
                        this.handleClick(node.data, node);
                        this.handleExpand(void 0, node);
                    }
                });
                return resolve(data);
            });
        },
        getMoreNode(parentId, page, total) {
            return {
                name: this.$t('button.showMore'),
                id: '#more',
                isLeaf: true,
                showPopper: false,
                childId: [],
                resourceType: 'More',
                page: page,
                parentId: parentId,
                total: total,
            };
        },
        /* Started by AICoder, pid:w1330j4cb3j1b8f14d1f08ff60c9ed11d4298041 */
        getAllTree(id, pageNo, callback) {
            HTTP.request('newGetAllTree', {
                method: 'post',
                data: {
                    focusNodeId: id,
                    pageNo,
                    pageSize: this.pageSize, 
                    modelIds: [modelIdsMap.group, modelIdsMap.field],
                },
                complete: async data => {
                    if (data.code === 0) {
                        await this.loadNodeIcons([data.data] || []);
                        callback && callback(data);
                    }
                },
            });
        },
        /* Ended by AICoder, pid:w1330j4cb3j1b8f14d1f08ff60c9ed11d4298041 */      
        getChild(id, pageNo, callback) {
            HTTP.request('newGetChildWithDg', {
                method: 'post',
                data: {
                    parentId: id,
                    pageNo: pageNo,
                    pageSize: this.pageSize,
                    modelIds: [modelIdsMap.group, modelIdsMap.field],
                },
                complete: async data => {
                    if (data.code === 0) {
                        await this.loadNodeIcons(data.data || []);
                        callback && callback(data);
                    }
                },
            });
        },
        searchMore() {
            this.searchLoading = true;
            HTTP.request('newSearchLogicGroupTree', {
                method: 'post',
                data: {
                    name: this.searchText,
                    pageNo: this.searchPage,
                    pageSize: this.searchPageSize,
                    modelIds: [modelIdsMap.group, modelIdsMap.field]
                },
                complete: data => {
                    this.searchLoading = false;
                    if (data.code === 0) {
                        this.searchPage++;
                        data.data = data.data || [];
                        if (data.data.length) {
                            this.updateTreeData(data.data, this.searchTreeData);
                            // 如果返回的数据条数小于100条，则认为是最后一页
                            if (data.data.length < this.searchPageSize) {
                                this.showMore = false;
                            }
                            this.$nextTick(() => {
                                if (this.currentId) {
                                    this.$refs.searchTree.setCurrentKey(this.currentId);
                                }
                            });
                        } else {
                            this.showMore = false;
                        }
                    }
                },
                error: data => {
                    console.log(data);
                    this.searchLoading = false;
                },
            });
        },
        searchTree(val) {
            this.searchLoading = true;
            HTTP.request('newSearchLogicGroupTree', {
                method: 'post',
                data: {
                    name: val,
                    pageNo: 1,
                    pageSize: this.searchPageSize,
                    modelIds: [modelIdsMap.group, modelIdsMap.field],
                },
                complete: data => {
                    let treeData = [];
                    if (val === this.searchText) {
                        this.searchLoading = false;
                        if (data.code === 0) {
                            this.searchPage = 2;
                            data.data = data.data || [];
                            if (data.data.length) {
                                this.updateTreeData(data.data, treeData);
                                this.searchTreeData = treeData;
                                this.showMore = true;
                                if (data.data.length < this.searchPageSize) {
                                    this.showMore = false;
                                }
                                this.$nextTick(() => {
                                    if (this.currentId) {
                                        this.$refs.searchTree.setCurrentKey(this.currentId);
                                    }
                                });
                            } else {
                                this.searchTreeData = treeData;
                                this.showMore = false;
                            }
                        }
                    }
                },
                error: data => {
                    this.searchLoading = false;
                    console.log('搜索网络问题!' + data);
                },
            });
        },
        updateTreeData(responseData, treeData) {
            if (responseData.length) {
                responseData.forEach(async path => {
                    let mountedNode = null;
                    path.forEach((node, i) => {
                        if (i === 0) {
                            // 预先设置当前节点为挂载点
                            let hasOne = false;
                            for (let j = 0, max = treeData.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === treeData[j].id) {
                                    mountedNode = treeData[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = [];
                                treeData.push(node);
                                mountedNode = node;
                            }
                        } else {
                            let hasOne = false;
                            for (let j = 0, max = mountedNode.children.length; j < max; j++) {
                                // 找到已经存在的挂载点，则设置成当前挂载点
                                if (node.id === mountedNode.children[j].id) {
                                    mountedNode = mountedNode.children[j];
                                    hasOne = true;
                                    break;
                                }
                            }
                            if (!hasOne) {
                                // 新的节点
                                node.children = [];
                                mountedNode.children.push(node);
                                mountedNode = node;
                            }
                        }
                    });
                    await this.loadNodeIcons(path || []);
                });
            }
        },
        handleExpand(d, node) {
            if (node.hasExpanded) {
                return;
            }
            node.hasExpanded = true;
            if (node.childNodes.length === 0) {
                let id = (node.data && node.data.id) || '';
                node.expanded = false;
                node.loading = true;
                this.getChild(id, 1, d => {
                    let nodeData = d.data;
                    for (let i = 0; i < nodeData.length; i++) {
                        this.$refs.searchTree.append(nodeData[i], node);
                    }
                    if (nodeData.length === 0) {
                        node.isLeaf = true;
                        node.isRealLeaf = true;
                    }
                    node.expanded = true;
                    node.loading = false;
                    // 根据返回的数据，判断是否需要加“加载更多”
                    if (2 <= Math.ceil(d.total / this.pageSize)) {
                        this.$refs.searchTree.append(this.getMoreNode(id, 2, d.total), node);
                    }
                    this.$nextTick(() => {
                        if (this.currentId) {
                            this.$refs.searchTree.setCurrentKey(this.currentId);
                        }
                    });
                });
            }
        },
        getAllNodeIds() {
            let ids = [];
            this.$refs.tree &&
                this.loopNode(this.$refs.tree.root, node => {
                    ids.push(node.data.id);
                });
            this.$refs.searchTree &&
                this.loopNode(this.$refs.searchTree.root, node => {
                    if (node.data.id && ids.indexOf(node.data.id) === -1) {
                        ids.push(node.data.id);
                    }
                });
            return ids;
        },
        // 位置树上更新告警统计
        statisticsseverities(ids) {
            HTTP.request('statisticsseverities', {
                method: 'post',
                data: ids,
                complete: data => {
                    if (data.code === 0) {
                        this.updateAlarmLevel(data.data);
                    }
                },
            });
        },
        updateAlarmLevel(list) {
            let obj = {};
            list.forEach(item => {
                obj[item.id] = item.severity;
            });
            this.$refs.tree &&
                this.loopNode(this.$refs.tree.root, node => {
                    if (obj[node.data.id]) {
                        node.data['alarmLevel'] = obj[node.data.id];
                    }
                });
            this.$refs.searchTree &&
                this.loopNode(this.$refs.searchTree.root, node => {
                    if (obj[node.data.id]) {
                        node.data['alarmLevel'] = obj[node.data.id];
                    }
                });
        },
        getNodeUrl(node) {
            // 获取当前节点的全路径
            let nodeUrl = [];
            let nodeUrlIds = [];
            let getNodeName = function (node) {
                if (node.data) {
                    if (node.data.id && node.data.id !== 'Root') {
                        nodeUrl.unshift(node.data.name);
                        nodeUrlIds.unshift(node.data.id);
                        if (node.parent) {
                            getNodeName(node.parent);
                        }
                    }
                }
            };
            getNodeName(node);
            return {
                names: nodeUrl,
                ids: nodeUrlIds,
            };
        },
    },
    emits: ['nodeClick'],
};
</script>

<style lang="scss" scoped>
:deep(.icon-treenode) {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-size: 14px 14px;
    white-space: nowrap;
    letter-spacing: -1em;
    text-indent: -99em;
    color: transparent;
}
:deep(.icon-treenode):before {
    content: '\3000';
}
.elTreeWrap {
    overflow: auto;
}
.search-input {
    padding: 16px 16px 8px;
}
/* 横向滚动条 */
.elTreeWrap :deep(.el-tree) > .el-tree-node {
    display: inline-block;
    min-width: 100%;
}
.tree-node {
    padding-right: 140px;
    position: relative;
}
.elTreeWrap :deep(.el-tree-node__content) > .el-tree-node__expand-icon {
    padding: 4px 6px;
}
button.nomore {
    border: 0 none;
}
.nodeEllipsis {
    display: inline-block;
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}
.el-tree-node__content .hover-to-show {
    position: absolute;
}
:deep(.el-tree-node__label) .iconfont {
    font-size: 14px;
    margin-right: 4px;
}
:deep(.node-name-container) {
    display: flex;
    align-items: center;
    width: 100%;
}

:deep(.tree-node-icon) {
    display: inline-block;
    margin-right: 4px;
}
</style>
