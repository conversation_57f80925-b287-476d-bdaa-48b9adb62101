<template>
    <div>
        <div class="trend-view">
            <div
                v-if="cloneMergePoint.length > 1"
                ref="chartMerge"
                class="el-table"
                style="margin-bottom: 20px; padding: 10px 5px"
            ></div>
            <div
                v-for="item in singleData"
                :ref="getRefSetter('chart')"
                :key="item.id"
                class="el-table"
                style="margin-bottom: 20px; padding: 10px 5px"
            ></div>
        </div>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer';
import * as echarts from 'echarts';
import { CHART_COLORS, CHART_HANDLEICON, getLineNodata } from '@uedm/uedm-ui/src/util/constants.js';
const VOLTAGE = 'voltage';
const handleIcon =
    'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
export default {
    props: {
        allEchartData: {
            require: true,
        },
        height: {
            type: Number,
            default: 350,
        },
        cloneMergePoint: {
            type: Array,
        },
        monthDataArr: {
            type: Array,
        },
        singleData: {
            type: Array,
        },
    },
    data() {
        return {
            echartsArr: [],
            myChart: null,
            xAxis: [],
            base64UrlsMap: {},
            // 聚合图预设颜色防重色
            mergeColor: ['#67C23A', '#E6A23C', '#F56C6C', '#409EFF', '#C50EBC'],
        };
    },
    mounted() {
        this.singleData.length &&
            this.singleData.forEach((item, index) => {
                this[item.id] = null;
                this.handleData(item, index);
            });
        this.cloneMergePoint.length && this.handleDataMore();
        setTimeout(() => {
            echarts.connect(this.echartsArr);
        }, 3000);
    },
    watch: {
        winResize: function () {
            this.$nextTick(() => {
                this.echartsArr.forEach(item => {
                    item.resize();
                });
            });
        },
    },
    computed: {
        // eslint-disable-next-line vue/return-in-computed-property
        gridRight() {
            if (this.cloneMergePoint.length == 2) {
                return '40';
            }
            if (this.cloneMergePoint.length == 3) {
                return '120';
            }
            if (this.cloneMergePoint.length == 4) {
                return '190';
            }
            if (this.cloneMergePoint.length == 5) {
                return '265';
            }
        },
        winResize() {
            return this.$store.getters.getResize;
        },
        skins() {
            let val = null;
            if (this.$store.getters.getIsDark) {
                val = 'dark';
            }
            return val;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        colorType() {
            return this.$store.getters.getIsDark ? 'dark' : 'default';
        },
    },
    methods: {
        initCharts(seriesData, index, obj) {
            this.$nextTick(() => {
                this[obj.id] = echarts.init(this.$arrRefs.chart[index], this.skins, {
                    height: this.height + 'px',
                });
                this[obj.id].clear();
                this[obj.id].setOption(this.getOptions(seriesData, obj));
                this.echartsArr.push(this[obj.id]);
            });
        },
        initChartsMore(legendData, seriesData, yAxisData) {
            this.$nextTick(() => {
                // 堆叠折线图，不能把echart实例包装成响应式对象，会导致tootip显示不出来等问题
                const _myChart = echarts.init(this.$refs['chartMerge'], this.skins, {
                    height: this.height + 'px',
                });
                this.myChart = _myChart;
                _myChart.clear();
                _myChart.setOption(this.getOptionsMore(legendData, seriesData, yAxisData));
                this.echartsArr.push(_myChart);
            });
        },
        getOptionsMore(legendData, seriesData, yAxisData) {
            let option = {
                backgroundColor: 'transparent',
                grid: {
                    containLabel: true,
                    top: '23%',
                    left: '50px',
                    right: this.gridRight,
                    bottom: '10%',
                },
                legend: {
                    top: 0,
                    data: legendData,
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: CHART_COLORS[this.colorType].background,
                    borderColor: CHART_COLORS[this.colorType].border,
                    textStyle: { color: CHART_COLORS[this.colorType].color },
                },
                xAxis: {
                    type: 'category',
                    data: this.xAxis,
                    name: this.xAxis.length ? '' : this.$t('battery.fields.date'),
                    nameLocation: 'start',
                    nameTextStyle: {
                        align: 'center',
                        verticalAlign: 'top',
                        padding: [10, 0, 0, 0],
                    },
                    axisTick: {
                        alignWithLabel: true,
                    },
                    axisLabel: {
                        interval: this.xAxis.length > 8 ? 'auto' : 0,
                        hideOverlap: true,
                        formatter: function (value) {
                            let ret = value;
                            if (value.length > 11) {
                                let part1 = value.slice(0, 10);
                                let part2 = value.slice(10);
                                ret = part1 + '\n' + part2;
                                return ret;
                            } else {
                                return value;
                            }
                        },
                        overflow: 'truncate',
                    },
                },
                yAxis: yAxisData,
                dataZoom: [],
                series: seriesData,
                graphic: {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    silent: true,
                    invisible: this.xAxis.length,
                    style: {
                        fill: CHART_COLORS[this.colorType].noData,
                        fontWeight: 'bold',
                        text: this.$t('common.noData'),
                        fontFamily: 'Microsoft Yahei',
                        fontSize: '25px',
                    },
                },
            };
            if (this.xAxis.length) {
                option.dataZoom = [
                    {
                        type: 'slider',
                        xAxisIndex: 0,
                        filterMode: 'empty',
                        bottom: 10,
                        height: 24,
                        showDetail: false,
                        zoomLock: false,
                    },
                ];
            }
            return option;
        },
        getOptions(seriesData, obj) {
            let option = {};
            if (this.xAxis.length) {
                option = {
                    backgroundColor: 'transparent',
                    title: {
                        text: obj.name,
                        left: '0',
                        textStyle: {
                            fontWeight: 700,
                            fontSize: 14,
                            color: CHART_COLORS[this.colorType].title,
                        },
                    },
                    grid: {
                        containLabel: true,
                        top: '23%',
                        left: '3.5%',
                        right: '3.5%',
                        bottom: '10%',
                    },
                    legend: {
                        top: 20,
                        data: [obj.name],
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: CHART_COLORS[this.colorType].background,
                        borderColor: CHART_COLORS[this.colorType].border,
                        textStyle: { color: CHART_COLORS[this.colorType].color },
                    },
                    xAxis: {
                        type: 'category',
                        data: this.xAxis,
                        axisTick: {
                            alignWithLabel: true,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: { color: CHART_COLORS[this.colorType].line },
                        },
                        axisLabel: {
                            show: true,
                            interval: this.xAxis.length > 8 ? 'auto' : 0,
                            hideOverlap: true,
                            color: CHART_COLORS[this.colorType].label,
                            formatter: function (value) {
                                let ret = value;
                                if (value.length > 11) {
                                    let part1 = value.slice(0, 10);
                                    let part2 = value.slice(10);
                                    ret = part1 + '\n' + part2;
                                    return ret;
                                } else {
                                    return value;
                                }
                            },
                            overflow: 'truncate',
                        },
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: obj.unit,
                            nameTextStyle: {
                                color: CHART_COLORS[this.colorType].label,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: { color: CHART_COLORS[this.colorType].line },
                            },
                            axisLabel: {
                                show: true,
                                color: CHART_COLORS[this.colorType].label,
                            },
                            splitLine: { show: false },
                        },
                    ],
                    dataZoom: [
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            filterMode: 'empty',
                            bottom: 10,
                            height: 24,
                            showDetail: false,
                            zoomLock: false,
                        }
                    ],
                    series: [
                        {
                            name: obj.name,
                            type: 'line',
                            data: seriesData,
                            itemStyle: {
                                color: obj.color,
                            },
                            smooth: true,
                        },
                    ],
                };
            } else {
                option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '50px' : '32px',
                        right: '4%',
                        bottom: '22',
                        containLabel: true,
                    },
                    title: {
                        text: this.title,
                        x: 'left',
                        textStyle: {
                            color: CHART_COLORS[this.colorType].title,
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                    },
                    xAxis: {
                        name: this.$t('battery.fields.date'),
                        nameLocation: 'start',
                        nameTextStyle: {
                            align: 'center',
                            verticalAlign: 'top',
                            padding: [10, 0, 0, 0],
                        },
                        axisLine: {
                            show: true,
                            lineStyle: { color: CHART_COLORS[this.colorType].line },
                        },
                        type: 'category',
                        data: [],
                    },
                    yAxis: {
                        type: 'value',
                        name: obj.unit,
                        nameTextStyle: {
                            align: 'right',
                            color: CHART_COLORS[this.colorType].label
                        },
                        axisLine: {
                            show: true,
                            lineStyle: { color: CHART_COLORS[this.colorType].line },
                        },
                        axisLabel: {
                            show: true,
                            color: CHART_COLORS[this.colorType].label,
                        },
                    },
                    series: [
                        {
                            type: 'bar',
                            data: [],
                        },
                    ],
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'middle',
                        silent: true,
                        invisible: false, // 通过数据长度判断是否显示暂无数据
                        style: {
                            fill: CHART_COLORS[this.colorType].noData,
                            fontWeight: 'bold',
                            text: this.$t('common.noData'),
                            fontFamily: 'Microsoft Yahei',
                            fontSize: '25px',
                        },
                    },
                };
            }
            if (obj.pointType == 'DI') {
                option.yAxis = [
                    {
                        type: 'value',
                        margin: 10,
                        interval: 100000,
                        showMinLabel: true,
                        showMaxLabel: true,
                        name: obj.unit,
                        nameTextStyle: {
                            color: CHART_COLORS[this.colorType].label,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: { color: CHART_COLORS[this.colorType].line },
                        },
                        axisLabel: {
                            show: true,
                            color: CHART_COLORS[this.colorType].label,
                        },
                        splitLine: { show: false },
                    },
                ];
            }
            return option;
        },
        handleData(obj, index) {
            this.xAxis = this.monthDataArr;
            let seriesData = [];
            this.allEchartData.forEach(d => {
                seriesData.push(d[obj.id]);
            });

            this.initCharts(seriesData, index, obj);

            this.$nextTick(() => {
                this.initChartSinglePicture(seriesData, index, obj);
            });
        },
        handleDataMore() {
            this.xAxis = this.monthDataArr;
            let legendData = [];
            let seriesData = [];
            let yAxisData = [];
            this.cloneMergePoint.forEach((item, index) => {
                let _name = item.name; // merge图长name换行
                if (this.$i18n.locale === 'zh-CN') {
                    if (_name.length > 6) {
                        let part1 = _name.slice(0, 6);
                        let part2 = _name.slice(6);
                        _name = `${part1}\n${part2}`;
                    }
                } else {
                    // 英文状态单词长，默认只显示第一个单词
                    let arr = _name.split(' ');
                    if (arr.length === 1) {
                        _name = arr[0];
                    } else {
                        _name = `${arr[0]}...`;
                    }
                }
                legendData.push(item.name);
                // series
                seriesData[index] = {
                    name: item.name,
                    type: 'line',
                    yAxisIndex: index,
                    itemStyle: {
                        color: this.mergeColor[index], // 只有5种颜色
                    },
                    data: [],
                    smooth: true,
                };
                // yAxis
                yAxisData[index] = {
                    type: 'value',
                    name: _name,
                    position: 'right',
                    offset: 75 * (index - 1),
                    splitLine: { show: false },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: this.mergeColor[index],
                        },
                    },
                };
                if (index == 0) {
                    yAxisData[index] = {
                        name: _name,
                        type: 'value',
                        position: 'left',
                        splitLine: { show: false },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: this.mergeColor[0],
                            },
                        },
                    };
                }
                this.allEchartData.forEach(d => {
                    seriesData[index].data.push(d[item.id]);
                });
                if (item.pointType == 'DI') {
                    yAxisData[index].type = 'value';
                    yAxisData[index].margin = 10;
                    yAxisData[index].interval = 100000;
                    yAxisData[index].showMinLabel = true;
                    yAxisData[index].showMaxLabel = true;
                }
            });
            this.initChartsMore(legendData, seriesData, yAxisData);

            this.$nextTick(() => {
                this.initChartMorePicture(legendData, seriesData, yAxisData);
            });
        },
        // single图片
        async initChartSinglePicture(seriesData, index, obj) {
            this.initCharts(seriesData, index, obj);
            setTimeout(() => {
                const imgConfig = {
                    backgroundColor: '#fff',
                    pixelRatio: 2,
                    excludeComponents: ['dataZoom'],
                };
                (this.base64UrlsMap[obj.id] = this[obj.id].getDataURL(imgConfig)),
                $emit(this, 'base64UrlsMap', this.base64UrlsMap);
            }, 2000);
        },
        // merge图片
        async initChartMorePicture(legendData, seriesData, yAxisData) {
            this.initChartsMore(legendData, seriesData, yAxisData);

            setTimeout(() => {
                const imgConfig = {
                    backgroundColor: '#fff',
                    pixelRatio: 2,
                    excludeComponents: ['dataZoom'],
                };
                const base64UrlsMap = {
                    merge: this.myChart.getDataURL(imgConfig),
                };
                $emit(this, 'base64UrlsMapMore', base64UrlsMap);
            }, 5000);
        },
        getRefSetter(refKey) {
            return ref => {
                !this.$arrRefs && (this.$arrRefs = {});
                !this.$arrRefs[refKey] && (this.$arrRefs[refKey] = []);
                ref && this.$arrRefs[refKey].push(ref);
            };
        },
    },
    beforeUpdate() {
        this.$arrRefs && (this.$arrRefs = {});
    },
    emits: ['base64UrlsMap', 'base64UrlsMapMore'],
};
</script>
