<template>
    <div v-loading="loading || exportLoading" class="life-history">
        <div class="space">
            <div class="uedm-content-area">
                <div class="uedm-title-tab">
                    {{ $t('standby.title.historyTrend') }}
                </div>
                <life-echarts
                    v-if="initFinish"
                    :node-id="nodeId"
                    :width-change="widthChange"
                    @getTime="getTime"
                    @base64UrlsMap="getBase64Urls"
                ></life-echarts>
            </div>
            <div class="uedm-content-area">
                <div class="tableBar">
                    <div class="title">
                        <span>{{ $t('common.listDetail') }}</span>
                    </div>
                    <span class="evaluate-date">
                        {{ $t('standby.fields.evalData') }}：{{ evalTime || '--' }}
                        <span>{{ $t('battery.tipMessage.echartsClickTip') }}</span>
                    </span>
                    <span style="float: right">
                        <el-popover
                            v-model:visible="filterIsShow"
                            placement="top-end"
                            :content="$t('button.filter')"
                            width="500"
                            trigger="click"
                            :teleported="false"
                        >
                            <el-form
                                ref="filterForm"
                                :model="queryForm"
                                class="filter-form"
                                :rules="rules"
                                :label-width="$i18n.locale == 'en-US' ? '180px' : '120px'"
                            >
                                <el-form-item :label="$t('batteryOverview.fields.remainingLife')">
                                    <el-select
                                        v-model="queryForm.lifeLevels"
                                        :teleported="false"
                                        clearable
                                        multiple
                                        collapse-tags
                                    >
                                        <el-option
                                            v-for="item in lifeOptions"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    prop="numberOfRatedCycles"
                                    :label="$t('battery.fields.numberOfRatedCycles')"
                                    :title="$t('battery.fields.numberOfRatedCycles')"
                                >
                                    <el-input
                                        v-model.number="queryForm.ratedCycleTimesBegin"
                                        clearable
                                        @input="handleInput($event, 'ratedCycleTimesBegin')"
                                    ></el-input>
                                    <span class="connect-line">
                                        {{ ' - ' }}
                                    </span>
                                    <el-input
                                        v-model.number="queryForm.ratedCycleTimesEnd"
                                        clearable
                                        @input="handleInput($event, 'ratedCycleTimesEnd')"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    prop="cumulativeNumberOfCycles"
                                    :label="$t('battery.fields.cumulativeNumberOfCycles')"
                                    :title="$t('battery.fields.cumulativeNumberOfCycles')"
                                >
                                    <el-input
                                        v-model.number="queryForm.accumCycleTimesBegin"
                                        clearable
                                        @input="handleInput($event, 'accumCycleTimesBegin')"
                                    ></el-input>
                                    <span class="connect-line">
                                        {{ ' - ' }}
                                    </span>
                                    <el-input
                                        v-model.number="queryForm.accumCycleTimesEnd"
                                        clearable
                                        @input="handleInput($event, 'accumCycleTimesEnd')"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item :label="$t('batteryOverview.fields.batteryType')">
                                    <el-select
                                        v-model="queryForm.battType"
                                        :teleported="false"
                                        clearable
                                        multiple
                                        collapse-tags
                                    >
                                        <el-option
                                            v-for="item in battOption"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="query()">
                                        {{ $t('button.search') }}
                                    </el-button>
                                    <el-button @click="reset()">
                                        {{ $t('button.reset') }}
                                    </el-button>
                                </el-form-item>
                            </el-form>
                            <template v-slot:reference>
                                <span :class="{ on: hasFilter }" class="icon-button filterIconBtn"></span>
                            </template>
                        </el-popover>
                        <column-filter
                            ref="columnFilter"
                            :value="filterList"
                            :tip="$t('tooltip.displayedItems')"
                            :is-dark="isDark"
                            style="margin: 0 0 0 8px;"
                            @save="saveColumns"
                        ></column-filter>
                    </span>
                </div>
                <div
                    v-if="rights['battery.b.standby.manager.assessment.view'] && tableHeader.length"
                    style="margin-top: 16px;"
                >
                    <el-table
                        v-if="showTable"
                        ref="historyTable"
                        v-loading="tableLoading"
                        style="width: 100%"
                        :data="tableData"
                        border
                        @sort-change="sortChange"
                    >
                        <el-table-column
                            v-for="item in tableHeader"
                            :key="item.id"
                            :prop="item.id"
                            :sortable="item.sortable ? 'custom' : false"
                            :label="formatHeader(item)"
                            show-overflow-tooltip
                            :min-width="textSize(item)"
                        >
                            <template v-slot="scope">
                                {{
                                    // eslint-disable-next-line max-len
                                    item.id === 'leftLife' ? formatValue(scope.row.remainLife.name) : formatValue(scope.row[item.id])
                                }}
                                <!-- evalSource 数据来源，1-AI，0-设备 -->
                                <span v-if="item.id === 'leftLife' && scope.row.remainLife.evalSource === '1'">
                                    <el-tag>
                                        AI
                                    </el-tag>
                                </span>
                                <el-tooltip
                                    v-if="item.id === 'leftLife'"
                                    placement="top"
                                    class="item"
                                    effect="dark"
                                >
                                    <template #content>
                                        <div class="tool-tip-overflow">
                                            <div class="tooltip-rule-title">
                                                {{ scope.row?.remainLife?.evalDetail?.evalRule || '--' }}
                                            </div>
                                            <div
                                                v-for="itemInfo in scope.row.remainLife.evalDetail?.evalDetail || []"
                                                :key="itemInfo"
                                            >
                                                {{ itemInfo || '--' }}
                                            </div>
                                        </div>
                                    </template>
                                    <i class="plx-ico-more-f-16 table-tooltip-icon"></i>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-if="tableHeader.length > 0"
                            :min-width="80"
                            fixed="right"
                            :label="$t('table.operation')"
                            :width="$i18n.locale === 'zh-CN' ? 85 : 120"
                        >
                            <template v-slot="scope">
                                <el-button size="mini" type="text" @click="toDetail(scope.row)">
                                    {{ $t('battery.fields.historyTrend') }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        v-model:current-page="pageInfo.pageNo"
                        :page-sizes="[5, 10, 20, 30]"
                        v-model:page-size="pageInfo.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageInfo.total"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    ></el-pagination>
                </div>
                <div v-else class="no-data border" :style="{ height: 120 + 'px', 'line-height': 120 + 'px' }">
                    <span v-if="!rights['battery.b.standby.manager.assessment.view']">{{ $t('common.noRight') }}</span>
                    <span v-else>{{ $t('common.noData') }}</span>
                </div>
            </div>
        </div>
        <trend-line v-if="trendLine.show" v-model:visible="trendLine.show" :infos="trendLine.infos"></trend-line>
        <div id="temporary3"></div>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import axios from 'axios';
import lifeEcharts from './echarts.vue';
import ColumnFilter from '@uedm/uedm-ui/src/components/columnFilter.vue';
import TrendLine from './trendLine.vue';
export default {
    components: {
        lifeEcharts,
        'column-filter': ColumnFilter,
        'trend-line': TrendLine,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        nodeId: {
            type: String,
            default: null,
        },
        widthChange: {
            type: Boolean,
            default: false,
        },
        height: {
            type: Number,
            default: 900,
        },
        pathNames: {
            type: Array,
            default: function () {
                return [];
            },
        },
    },
    data() {
        let numSizeFormatValid = (rule, value, callback) => {
            if (this.queryForm.ratedCycleTimesBegin && this.queryForm.ratedCycleTimesEnd) {
                if (Number(this.queryForm.ratedCycleTimesBegin) > Number(this.queryForm.ratedCycleTimesEnd)) {
                    callback(this.$t('battery.tipMessage.inputError'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        let numSizeFormatValid2 = (rule, value, callback) => {
            if (this.queryForm.accumCycleTimesBegin && this.queryForm.accumCycleTimesEnd) {
                if (Number(this.queryForm.accumCycleTimesBegin) > Number(this.queryForm.accumCycleTimesEnd)) {
                    callback(this.$t('battery.tipMessage.inputError'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            queryForm: {
                lifeLevels: [],
                ratedCycleTimesBegin: null,
                ratedCycleTimesEnd: null,
                accumCycleTimesBegin: null,
                accumCycleTimesEnd: null,
                battType: [],
            },
            evalTime: '',
            pageInfo: {
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            queryParameter: {
                lifeLevels: [],
                ratedCycleTimesBegin: null,
                ratedCycleTimesEnd: null,
                accumCycleTimesBegin: null,
                accumCycleTimesEnd: null,
                battType: [],
            }, // 列表查询条件参数
            loading: false,
            exportLoading: false,
            tableLoading: false,
            initFinish: false, // 为了初始化表头查询完成才调getTime
            showTable: true,
            tableData: [],
            filterIsShow: false,
            base64Url: {},
            battOption: [],
            sort: '',
            order: '',
            rules: {
                numberOfRatedCycles: [
                    {
                        validator: numSizeFormatValid,
                        trigger: 'change',
                    },
                ],
                cumulativeNumberOfCycles: [
                    {
                        validator: numSizeFormatValid2,
                        trigger: 'change',
                    },
                ],
            },
            filterList: [],
            trendLine: {
                show: false,
                infos: {},
            },
        };
    },
    watch: {},
    created() {
        this.loading = true;
        this.getColumns(() => {
            this.initFinish = true;
        });
    },
    mounted() {
        this.setQueryParameter();
        this.getBattTypes();
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        tableHeader() {
            let header = [];
            for (let i = 0; i < this.filterList.length; i++) {
                let item = this.filterList[i];
                if (item.enable && item.assetAttributeShow) {
                    header.push({
                        id: item.id,
                        sequence: item.sequence,
                        name: item.name,
                        unit: item.unit,
                        sortable: item.sortable,
                    });
                }
            }
            return header;
        },
        hasFilter() {
            let tag = false;
            let queryParameter = this.queryParameter;
            for (let key in queryParameter) {
                if (key === 'lifeLevels' || key === 'battType') {
                    if (queryParameter[key].length) {
                        tag = true;
                    }
                } else if (queryParameter[key] || queryParameter[key] === 0) {
                    tag = true;
                }
            }
            return tag;
        },
        lifeOptions() {
            return this.$store.getters.getLifeOptions || [];
        },
    },
    methods: {
        // 点击图表获取到的时间数据
        getTime(val) {
            this.evalTime = val;
            this.reset();
        },
        handleInput(e, name) {
            let value = e.replace(/[^\d]/g, ''); // 只能输入数字
            value = value.replace(/^0+(\d)/, '$1'); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
            value = value.replace(/(\d{15})\d*/, '$1'); // 最多保留15位整数
            this.queryForm[name] = value;
        },
        getBase64Urls(url) {
            this.base64Url = url;
        },
        back() {
            this.$bus.emit('isHistory', '', false);
        },
        getBattTypes() {
            this.battOption = [];
            HTTP.request('getBattTypeOption', {
                method: 'get',
                complete: resp => {
                    if (resp.code === 0 && resp.data) {
                        this.battOption = resp.data;
                    }
                },
                error: () => {
                    this.battOption = [];
                },
            });
        },
        handleExport() {
            let a = new Date().toLocaleDateString().split('/');
            let b = new Date().getHours() + '';
            let c = new Date().getMinutes() + '';
            b = b.length == 1 ? '0' + b : b;
            c = c.length == 1 ? '0' + c : c;
            a[1] = a[1].length == 1 ? '0' + a[1] : a[1];
            a[2] = a[2].length == 1 ? '0' + a[2] : a[2];
            a = a.join('');
            let _name = `lifeHistory_${a}${b}${c}.xlsx`;
            let images = [
                {
                    base64Str: this.base64Url.base64Url,
                    imageName: this.base64Url.name,
                    xline: 0,
                    yline: 1,
                    dim: 'History_Trend',
                },
            ];
            let queryParameter = {
                ...this.queryParameter,
                evalTime: this.evalTime,
                sort: this.order,
                order: this.sort,
                logicGroupId: this.nodeId,
                battLifeDims: this.filterList,
                images,
            };
            const languageOption = (
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) ||
                ''
            ).replace('-', '_');
            const forgerydefense = window.forgerydefense || '';
            let DOWNLOAD_URL = '/api/battery-manager/v1/batt-life/overview/export';
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            this.exportLoading = true;
            /* Started by AICoder, pid:w519fb0e32n61a31463a097c9002d60e4b16f8fd */
            let tips = this.$message({
                message: this.$t('tipMessage.exportTimeLongTip'),
                showClose: true,
                duration: 0,
                customClass: 'blue-info',
            });
            /* Ended by AICoder, pid:w519fb0e32n61a31463a097c9002d60e4b16f8fd */
            axios
                .post(url, queryParameter, config)
                .then(res => {
                    this.exportLoading = false;
                    tips.close();
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                let message = this.$message({
                                    message:
                                        this.$t('batteryView.exportFailed') + ': ' + result.message || result.error,
                                    duration: 5000,
                                    showClose: true,
                                    type: 'error',
                                });
                                this.$message.error(message);
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        let blob = new Blob([res.data]);
                        let url = window.URL.createObjectURL(blob); // 创建下载的链接
                        let link = document.createElement('a');
                        let fileName = _name;
                        if (res.headers['content-disposition']) {
                            let contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                })
                .catch(() => {
                    this.exportLoading = false;
                    tips.close();
                });
        },
        setQueryParameter() {
            for (let key in this.queryForm) {
                if (
                    key === 'ratedCycleTimesBegin' ||
                    key === 'ratedCycleTimesEnd' ||
                    key === 'accumCycleTimesBegin' ||
                    key === 'accumCycleTimesEnd'
                ) {
                    if (this.queryForm[key]) {
                        this.queryParameter[key] = Number(this.queryForm[key]);
                    } else {
                        this.queryParameter[key] = null;
                    }
                } else {
                    this.queryParameter[key] = this.queryForm[key];
                }
            }
        },
        query() {
            this.$refs.filterForm.validate(valid => {
                if (valid) {
                    this.pageInfo.pageNo = 1;
                    this.setQueryParameter();
                    this.filterIsShow = false;
                    this.getTableData();
                }
            });
        },
        reset() {
            this.queryForm.lifeLevels = [];
            this.queryForm.battType = [];
            this.queryForm.ratedCycleTimesBegin = null;
            this.queryForm.ratedCycleTimesEnd = null;
            this.queryForm.accumCycleTimesBegin = null;
            this.queryForm.accumCycleTimesEnd = null;
            this.setQueryParameter();
            this.pageInfo.pageNo = 1;
            this.pageInfo.total = 0;
            this.order = '';
            this.sort = '';
            this.getTableData();
        },
        handleSizeChange(pageSize) {
            this.pageInfo.pageSize = pageSize;
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        handleCurrentChange(pageNo) {
            this.pageInfo.pageNo = pageNo;
            this.getTableData();
        },
        sortChange(column) {
            // 排序
            this.sort = column.prop || '';
            if (column.order === 'descending') {
                this.order = 'desc';
            } else if (column.order === 'ascending') {
                this.order = 'asc';
            } else {
                this.order = '';
                this.sort = '';
            }
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        getRequestData() {
            return {
                ...this.queryParameter,
                evalTime: this.evalTime,
                sort: this.order,
                order: this.sort,
                pageNo: this.pageInfo.pageNo,
                pageSize: this.pageInfo.pageSize,
                logicGroupId: this.nodeId,
                battLifeDims: this.filterList,
            };
        },
        getTableData() {
            this.tableLoading = true;
            let queryCompareData = this.getRequestData();
            HTTP.request('getLifeTable', {
                method: 'post',
                data: queryCompareData,
                complete: resp => {
                    if (JSON.stringify(queryCompareData) !== JSON.stringify(this.getRequestData())) {
                        return;
                    }
                    this.tableData = [];
                    this.tableLoading = false;
                    this.specialFilterIsShow = false;
                    if (resp.code === 0) {
                        this.tableData = resp.data || [];
                        this.pageInfo.total = resp.total;
                    } else {
                        this.pageInfo.total = 0;
                    }
                },
                error: () => {
                    if (JSON.stringify(queryCompareData) !== JSON.stringify(this.getRequestData())) {
                        return;
                    }
                    this.tableData = [];
                    this.tableLoading = false;
                    this.pageInfo.total = 0;
                },
            });
        },
        textSize(item) {
            let text = item.name;
            if (item.unit) {
                text = text + '(' + item.unit + ')';
            }
            let content = document.getElementById('temporary3');
            let span = document.createElement('span');
            let w = span.offsetWidth;
            span.style.visibility = 'hidden';
            span.style.fontSize = '14px';
            span.style.fontWeight = 'bolder';
            span.style.display = 'inline-block';
            content.appendChild(span);
            if (typeof span.textContent !== 'undefined') {
                span.textContent = text;
            } else {
                span.innerText = text;
            }
            w = parseFloat(window.getComputedStyle(span).width) - w;
            content.removeChild(span);
            if (['status', 'preStatus'].includes(item.id)) {
                return Math.max(w + 50, 120);
            } else if (item.id === 'position') {
                return Math.max(w + 50, 250);
            } else if (item.id === 'soh') {
                return w + 63;
            } else if (
                [
                    'evaluateTime',
                    'startDate',
                    'productionDate',
                    'gmtModified',
                    'collectionTime',
                    'sohThresholdRange',
                ].includes(item.id)
            ) {
                return Math.max(w + 50, 160);
            } else if (item.id === 'name') {
                return Math.max(w + 50, 120);
            } else if (item.id === 'leftLife') {
                return Math.max(w + 80, 160);
            } else {
                return w + 50;
            }
        },
        formatHeader(item) {
            let name = item.name;
            if (item.unit) {
                name = name + '(' + item.unit + ')';
            }
            return name;
        },
        formatValue(val) {
            if (!val && val !== 0) {
                return '--';
            } else {
                return val;
            }
        },
        getColumns(callFn) {
            HTTP.request('lifeListFilter', {
                method: 'get',
                urlParam: {
                    pageNo: null,
                    pageSize: null,
                },
                complete: data => {
                    if (this.loading) {
                        this.loading = false;
                    }
                    if (this.tableLoading) {
                        this.tableLoading = false;
                    }
                    if (data.code === 0) {
                        this.filterList = data.data || [];
                        this.showTable = false;
                        this.$nextTick(() => {
                            this.showTable = true;
                        });
                    } else {
                        this.filterList = [];
                    }
                    if (callFn) {
                        callFn();
                    }
                },
                error: () => {
                    this.filterList = [];
                    if (this.loading) {
                        this.loading = false;
                    }
                    if (this.tableLoading) {
                        this.tableLoading = false;
                    }
                    if (callFn) {
                        callFn();
                    }
                },
            });
        },
        saveColumns(d) {
            let updateParameter = [];
            for (let i = 0; i < d.length; i++) {
                let item = d[i];
                updateParameter.push({
                    id: item.id,
                    sequence: item.sequence,
                    enable: item.enable,
                });
            }
            this.tableLoading = true;
            HTTP.request('lifeListFilterUpdate', {
                method: 'put',
                data: updateParameter,
                complete: data => {
                    this.tableLoading = false;
                    if (data.code === 0) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveSuccess'),
                            type: 'success',
                        });
                        this.tableLoading = true;
                        this.getColumns(() => {
                            this.getTableData();
                        });
                    } else if (data.code === -301) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.dimIdBlank'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -302) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.sequenceNotUnique'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -305) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotRange') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else if (data.code === -308) {
                        this.$message({
                            message: this.$t('batteryOverview.tips.valueNotModify') + ': ' + data.error,
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    } else {
                        this.$message({
                            message: this.$t('batteryOverview.tips.filterSaveError'),
                            duration: 5000,
                            showClose: true,
                            type: 'error',
                        });
                    }
                },
                error: () => {
                    this.tableLoading = false;
                    this.$message({
                        message: this.$t('tipMessage.networkError'),
                        duration: 5000,
                        showClose: true,
                        type: 'error',
                    });
                },
            });
        },
        toDetail(row) {
            this.trendLine = {
                show: true,
                infos: row,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.el-form {
    padding-top: 14px;
    :deep(.el-form-item__label) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
    }
}
.title {
    color: #333;
    font-size: 16px;
    position: relative;
    display: inline-block;
    .back-button {
        position: absolute;
        right: 0;
        top: 0;
    }
    .export-button {
        position: absolute;
        right: 70px;
        top: 0;
    }
}
.tableBar {
    padding-top: 8px;
    height: 32px;
    line-height: 32px;
    h4 {
        display: inline-block;
        padding: 0;
    }
    .evaluate-date {
        margin-left: 16px;
        font-size: 12px;
        color: #606266;
        font-weight: 700;
        span {
            color: #bfbfbf;
            font-weight: 400;
            margin-left: 16px;
        }
    }
}
.icon-button {
    font-size: 14px;
    width: 32px;
    height: 32px;
    box-sizing: border-box;
    padding: 8px;
    margin: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: none !important;
    color: #595959;
    cursor: pointer;
    vertical-align: middle;
    a {
        text-decoration: none;
    }

    &:hover {
        border: 1px solid #0f8dfb;
    }
}
.active {
    &:before {
        color: rgb(25, 147, 255) !important;
    }
}
.connect-line {
    padding: 0 8px;
    color: #4c4d4f;
}
.filter-form {
    :deep(.el-form-item):nth-child(2),
    :deep(.el-form-item):nth-child(3) {
        .el-form-item__content {
            .el-input {
                width: 140px;
                .el-input__wrapper {
                    width: 140px;
                }
                .el-input__inner {
                    width: 100%;
                }
            }
        }
    }
}
.tooltip-rule-title {
    font-size: 14px;
    font-weight: 700;
}
html.dark {
    .title {
        color: #d9d9d9;
    }
    .tableBar .evaluate-date {
        color: #d9d9d9;
        span {
            color: #a4a7b3;
        }
    }
    .connect-line {
        color: #4c4d4f;
    }
}
</style>
