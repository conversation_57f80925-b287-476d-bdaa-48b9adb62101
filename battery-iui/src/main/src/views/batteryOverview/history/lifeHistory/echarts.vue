<template>
    <div v-loading="loading" class="echarts-view">
        <el-radio-group v-model="shortcutTime" class="time-select">
            <el-radio-button label="1" @click="clickShortcutTime(90, 'day')">
                {{ $t('common.3months') }}
            </el-radio-button>
            <el-radio-button label="2" @click="clickShortcutTime(180, 'day')">
                {{ $t('common.6months') }}
            </el-radio-button>
            <el-radio-button label="3" @click="clickShortcutTime(360, 'day')">
                {{ $t('common.12months') }}
            </el-radio-button>
            <el-radio-button :class="{ isFocus: isFocus }" label="4">
                {{ $t('common.Self-Defined') }}
            </el-radio-button>
        </el-radio-group>
        <el-button
            :class="[$i18n.locale == 'zh-CN' ? 'zh-hide' : 'en-hide', 'hide-input']"
            :style="{ width: ($i18n.locale == 'zh-CN' ? 75 : 100) + 'px' }"
            size="small"
            @click="
                shortcutTime = '4';
                isFocus = false;
            "
            @mouseenter="setFocus(true)"
            @mouseleave="setFocus(false)"
        >
            <el-date-picker
                v-model="definedTime"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                type="monthrange"
                unlink-panels
                format="YYYY-MM"
                value-format="YYYY-MM"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :teleported="false"
            ></el-date-picker>
        </el-button>
        <div ref="chart" class="chart"></div>
    </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer';
import HTTP from '@/util/httpService.js';
import * as echarts from 'echarts';
import { shallowRef } from 'vue';
const handleIcon =
    'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';

export default {
    data() {
        return {
            loading: false,
            myChart: null,
            shortcutTime: '1',
            base64Url: '',
            formData: {
                startTime: '',
                endTime: '',
            },
            definedTime: '',
            xIndex: -100,
            xData: [],
            // mock
            echartData: [],
            isFocus: false,
            dataZoomShow: false,
        };
    },
    props: {
        height: {
            type: Number,
            default: 268,
        },
        nodeId: {
            type: String,
            default: '',
        },
        widthChange: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        winResize() {
            return this.$store.getters.getResize;
        },
        skins() {
            let val = null;
            if (this.$store.getters.getIsDark) {
                val = 'dark';
            }
            return val;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        lifeOptions() {
            let options = this.$store.getters.getLifeOptions || [];
            if (!options.length) {
                options = [
                    {
                        id: '0',
                        name: this.$t('batteryOverview.title.mouth3'),
                    },
                    {
                        id: '1',
                        name: this.$t('batteryOverview.title.mouth6'),
                    },
                    {
                        id: '2',
                        name: this.$t('batteryOverview.title.mouth9'),
                    },
                    {
                        id: '3',
                        name: this.$t('batteryOverview.title.mouth12'),
                    },
                    {
                        id: '4',
                        name: this.$t('batteryOverview.title.moreYear'),
                    },
                    {
                        id: 'Unknown',
                        name: this.$t('batteryOverview.title.noKnown'),
                    },
                ];
            }
            return options;
        },
    },
    watch: {
        winResize: function () {
            if (this.myChart) {
                this.$nextTick(() => {
                    this.myChart.resize();
                });
            }
        },
        xIndex(val) {
            if (val == '-0') {
                $emit(this, 'getTime', this.xData[0]);
            } else {
                console.log(this.xData[val]);
                $emit(this, 'getTime', this.xData[val]);
            }
        },
        widthChange() {
            if (this.myChart) {
                this.$nextTick(() => {
                    this.myChart.resize();
                });
            }
        },
        definedTime(val) {
            if (val && val[0] && val[1]) {
                this.formData.startTime = val[0];
                this.formData.endTime = val[1];
            }
        },
        formData: {
            handler() {
                this.getEchartsData();
            },
            deep: true,
        },
    },
    mounted() {
        this.clickShortcutTime(90, 'day');
    },
    methods: {
        setFocus(val) {
            this.shortcutTime !== '4' && (this.isFocus = val);
        },
        getEchartsData() {
            this.loading = true;
            HTTP.request('getLifeHistoryEcharts', {
                method: 'post',
                data: {
                    logicGroupId: this.nodeId,
                    evalTimeBegin: this.formData.startTime,
                    evalTimeEnd: this.formData.endTime,
                },
                complete: resp => {
                    this.loading = false;
                    if (resp.code === 0 && resp.data) {
                        this.echartData = resp.data;
                        if (this.echartData.length > 12) {
                            this.dataZoomShow = true;
                        } else {
                            this.dataZoomShow = false;
                        }
                    }
                    if (this.$refs.chart) {
                        this.initCharts(true);
                        this.$nextTick(() => {
                            this.initChartSinglePicture();
                        });
                    }
                    if (!this.echartData.length) {
                        $emit(this, 'getTime', '');
                    }
                },
                error: () => {
                    this.loading = false;
                },
            });
        },
        initCharts(_isEmit) {
            this.myChart = shallowRef(
                echarts.init(this.$refs.chart, this.skins, {
                    height: this.height + 'px',
                })
            );
            this.myChart.clear();
            this.myChart.setOption(this.getOptions(_isEmit));
            this.myChart.getZr().on('click', params => {
                let pointInPixel = [params.offsetX, params.offsetY];
                if (this.myChart.containPixel('grid', pointInPixel)) {
                    this.xIndex = this.myChart.convertFromPixel({ seriesIndex: 0 }, [
                        params.offsetX,
                        params.offsetY,
                    ])[0];
                }
            });
        },
        getOptions(_isEmit) {
            this.xData = [];
            let _data = {};
            this.lifeOptions.forEach(item => {
                _data[item.id] = {
                    name: item.name,
                    data: [],
                };
            });
            this.echartData.forEach(item => {
                this.xData.push(item.evalTime);
                for (let key in _data) {
                    let index = item.statisticsResultVo.findIndex(param => {
                        return param.id == key;
                    });
                    let _num = '--';
                    if (index > -1) {
                        _num = item.statisticsResultVo[index].number;
                    }
                    _data[key].data.push(_num);
                }
            });
            _isEmit && this.xData.length && $emit(this, 'getTime', this.xData[this.xData.length - 1]);
            let option = {};
            const colorType = this.isDark ? 'dark' : 'default';
            const chartColor = {
                default: {
                    noData: '#bfbfbf',
                    background: 'rgb(48, 49, 51, 0.75)',
                    border: '#303133',
                    color: '#fff',
                },
                dark: {
                    noData: '#666666',
                    background: 'rgb(48, 49, 51, 0.75)',
                    border: '#303133',
                    color: '#fff',
                },
            };
            if (!this.xData || !this.xData.length) {
                // 无数据时显示
                option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '80px' : '70px',
                        right: '4%',
                        bottom: '22',
                        containLabel: true,
                    },
                    xAxis: {
                        name: this.$t('standby.fields.evalData'),
                        nameLocation: 'start',
                        nameTextStyle: {
                            align: 'center',
                            verticalAlign: 'top',
                            padding: [10, 0, 0, 0],
                        },
                        type: 'category',
                        data: [],
                    },
                    yAxis: {
                        type: 'value',
                        name: this.$t('batteryOverview.fields.stateNum'),
                        nameTextStyle: {
                            align: 'right',
                        },
                        show: true,
                        axisLine: {
                            show: true,
                        },
                    },
                    series: [
                        {
                            type: 'bar',
                            data: [],
                        },
                    ],
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'middle',
                        silent: true,
                        invisible: false,
                        style: {
                            fill: chartColor[colorType].noData,
                            fontWeight: 'bold',
                            text: this.$t('common.noData'),
                            fontFamily: 'Microsoft Yahei',
                            fontSize: '25px',
                        },
                    },
                };
            } else {
                let color = [];
                let series = [];
                let colorMap = {
                    0: '#FF9852',
                    1: '#FFC850',
                    2: '#4BD3F3',
                    3: '#109EBF',
                    4: '#7CD180',
                    Unknown: '#BFBFBF',
                };
                for (let key in _data) {
                    color.push(colorMap[key]);
                    series.push({
                        name: _data[key].name,
                        type: 'bar',
                        barMaxWidth: '15',
                        data: _data[key].data,
                    });
                }
                if (!color.length) {
                    color = ['#FF9852', '#FFC850', '#4BD3F3', '#109EBF', '#7CD180', '#BFBFBF'];
                }
                option = {
                    backgroundColor: 'transparent',
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                        },
                        backgroundColor: chartColor[colorType].background,
                        borderColor: chartColor[colorType].border,
                        textStyle: { color: chartColor[colorType].color },
                    },
                    legend: {
                        show: true,
                        top: '0',
                        icon: 'roundRect',
                        itemGap: 21,
                        itemWidth: 12,
                        itemHeight: 12,
                        textStyle: {
                            fontSize: 12,
                            color: this.isDark ? '#a4a7b3' : '#333',
                        },
                    },
                    grid: {
                        left: this.$i18n.locale === 'en-US' ? '80px' : '70px',
                        right: '16px',
                        bottom: this.dataZoomShow ? '25' : '2',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: this.xData,
                        axisTick: {
                            alignWithLabel: true,
                        },
                        name: this.$t('standby.fields.evalData'),
                        nameLocation: 'start',
                        nameTextStyle: {
                            padding: [0, 8, 0, 0],
                        },
                        minInterval: 1,
                    },
                    yAxis: {
                        name: this.$t('batteryOverview.fields.stateNum'),
                        type: 'value',
                        nameTextStyle: {
                            align: 'right',
                        },
                        axisLabel: {
                            formatter: params => {
                                if (String(params).includes('.')) {
                                    return '';
                                } else {
                                    return params;
                                }
                            },
                        },
                        axisLine: {
                            show: true,
                        },
                        axisTick: {
                            show: true,
                        },
                        minInterval: 1,
                    },
                    color: color,
                    series: series,
                };
            }
            if (this.dataZoomShow) {
                option.dataZoom = [
                    {
                        type: 'slider',
                        xAxisIndex: 0,
                        filterMode: 'empty',
                        bottom: 10,
                        height: 24,
                        showDetail: false,
                        zoomLock: false,
                    },
                ];
            }
            return option;
        },
        async initChartSinglePicture() {
            this.initCharts(false);
            setTimeout(() => {
                const imgConfig = {
                    backgroundColor: '#fff',
                    pixelRatio: 2,
                    excludeComponents: ['dataZoom'],
                };
                this.base64Url = this.myChart.getDataURL(imgConfig);
                $emit(this, 'base64UrlsMap', {
                    base64Url: this.base64Url,
                    name: this.$t('battery.title.lifeHistory'),
                });
            }, 1000);
        },
        clickShortcutTime(diff, type) {
            this.definedTime = '';
            let timeObj = this.getLastTimeByDiff(diff, type);
            this.formData.startTime = timeObj.start;
            this.formData.endTime = timeObj.end;
        },
        getLastTimeByDiff(diffNum, type) {
            let mSeconds = diffNum * 24 * 60 * 60 * 1000;
            let defaultTime = {};
            // 今天
            let nowDate = new Date();
            nowDate.setTime(nowDate.getTime());
            let preDate = new Date();
            preDate.setTime(nowDate.getTime() - mSeconds);
            defaultTime.start = this.timeFormat(preDate);
            defaultTime.end = this.timeFormat(nowDate);

            return defaultTime;
        },
        timeFormat(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
            let o = {
                'M+': date.getMonth() + 1, // 月份
                'D+': date.getDate(), // 日
                'h+': date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, // 小时
                'H+': date.getHours(), // 小时
                'm+': date.getMinutes(), // 分
                's+': date.getSeconds(), // 秒
                'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
                S: date.getMilliseconds(), // 毫秒
            };
            if (/(Y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
            }
            for (let k in o) {
                if (new RegExp('(' + k + ')').test(fmt)) {
                    fmt = fmt.replace(
                        RegExp.$1,
                        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
                    );
                }
            }
            return fmt;
        },
    },
    emits: ['getTime', 'base64UrlsMap'],
};
</script>

<style lang="scss" scoped>
.echarts-view {
    height: 100%;
    position: relative;
    .time-select {
        position: absolute;
        top: -70px;
        right: -21px;
        margin: 12px;
        z-index: 20;
    }
    .hide-input {
        position: absolute !important;
        top: -70px;
        right: -29px;
        opacity: 1;
        margin: 12px 12px 0 0;
        z-index: 30;
        background: none!important;
        border: none!important;
        cursor: pointer;
        .el-input__inner {
            padding: 0;
        }
        :deep(.el-range-input) {
            cursor: pointer;
        }
        :deep(.el-date-editor) {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
        }
    }
    :deep(.zh-hide) .el-range-editor--small .el-range__icon {
        width: 70px;
        height: 30px;
    }
    :deep(.en-hide) .el-range-editor--small .el-range__icon {
        width: 120px;
        height: 30px;
    }
    .isFocus :deep(.el-radio-button__inner) {
        background: rgb(217, 217, 217);
    }
}
:deep(.el-date-editor) {
    width: 70px !important;
    cursor: pointer;
}
::v-deep .start-date:focus {
    outline: none !important;
}
::v-deep .end-date:focus {
    outline: none !important;
}
html.dark {
    .time-select {
        :deep(.el-radio-button__inner) {
            background: transparent;
            border-color: #474a59;
            color: #a4a7b3;
        }
    }
    .isFocus :deep(.el-radio-button__inner) {
        color: #1993ff;
    }
}
</style>
