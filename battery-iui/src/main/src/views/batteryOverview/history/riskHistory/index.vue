<template>
    <div v-loading="exportLoading" class="life-history">
        <div class="space">
            <div class="uedm-content-area">
                <div class="uedm-title-tab">
                    {{ $t('standby.title.historyTrend') }}
                </div>
                <risk-echarts
                    :node-id="nodeId"
                    :width-change="widthChange"
                    @getTime="getTime"
                    @base64UrlsMap="getBase64Urls"
                ></risk-echarts>
            </div>
            <div class="uedm-content-area">
                <div class="tableBar">
                    <div class="title">
                        <span>{{ $t('common.listDetail') }}</span>
                    </div>
                    <span class="evaluate-date">
                        {{ $t('standby.fields.evalData') }}：{{ evalTime || '--' }}
                        <span>{{ $t('battery.tipMessage.echartsClickTip') }}</span>
                    </span>
                    <span style="float: right">
                        <el-input
                            v-model="battName"
                            clearable
                            maxlength="50"
                            style="margin-right: 16px"
                            :placeholder="$t('battery-life-basicInfo-name')"
                            :prefix-icon="ElIconSearch"
                        ></el-input>
                        <el-select
                            v-model="riskName"
                            :teleported="false"
                            :placeholder="$t('batteryOverview.fields.riskName')"
                            filterable
                            clearable
                            multiple
                            collapse-tags
                        >
                            <el-option
                                v-for="item in riskNameOptions"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </span>
                </div>
                <div style="margin-top: 16px">
                    <el-table
                        ref="historyTable"
                        v-loading="tableLoading"
                        border
                        style="width: 100%"
                        :data="tableData"
                        @sort-change="sortChange"
                    >
                        <el-table-column
                            v-for="item in tableHeader"
                            :key="item.id"
                            :prop="item.id"
                            :sortable="item.sortable ? 'custom' : false"
                            :label="formatHeader(item)"
                            show-overflow-tooltip
                            :min-width="textSize(item)"
                        >
                            <template v-slot="scope">
                                <el-tag
                                    v-if="item.id === 'riskLevel'"
                                    :color="getRiskColor(scope.row.riskLevel?.id)"
                                    :style="{
                                        color: getRiskFontColor(scope.row.riskLevel?.id)
                                    }"
                                    :type="healthType(scope.row, 'riskLevel')"
                                >
                                    {{ scope.row.riskLevel ? scope.row.riskLevel?.name : '--' }}
                                </el-tag>
                                <span v-if="item.id !== 'riskLevel'">
                                    {{ formatValue(scope.row[item.id], item.id) }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :min-width="80"
                            fixed="right"
                            :label="$t('table.operation')"
                            :width="$i18n.locale === 'zh-CN' ? 85 : 120"
                        >
                            <template v-slot="scope">
                                <el-button size="mini" type="text" @click="toDetail(scope.row)">
                                    {{ $t('battery.title.riskHistory') }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        v-model:current-page="pageInfo.pageNo"
                        :page-sizes="[5, 10, 20, 30]"
                        v-model:page-size="pageInfo.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageInfo.total"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    ></el-pagination>
                </div>
                <!-- <div v-else class="no-data border" :style="{ height: 120 + 'px', 'line-height': 120 + 'px' }">
                    <span v-if="!rights['battery.b.standby.manager.assessment.view']">{{ $t('common.noRight') }}</span>
                    <span v-else>{{ $t('common.noData') }}</span>
                </div> -->
            </div>
        </div>
        <trend-line v-if="trendLine.show" v-model:visible="trendLine.show" :infos="trendLine.infos"></trend-line>
        <div id="temporary4"></div>
    </div>
</template>

<script>
import HTTP from '@/util/httpService.js';
import axios from 'axios';
import riskEcharts from './echarts.vue';
import TrendLine from './trendLine.vue';
import { Search as ElIconSearch } from '@element-plus/icons-vue';
import { antiShake } from '@/util/antiShake.js';
export default {
    components: {
        riskEcharts,
        'trend-line': TrendLine,
    },
    inject: {
        rights: {
            default: () => {
                return {};
            },
        },
    },
    props: {
        nodeId: {
            type: String,
            default: null,
        },
        widthChange: {
            type: Boolean,
            default: false,
        },
        height: {
            type: Number,
            default: 900,
        },
    },
    data() {
        return {
            riskName: [],
            battName: '',
            evalTime: '',
            pageInfo: {
                pageNo: 1, // 当前页码
                pageSize: 10, // 每页显示记录数
                total: 0, // 当前页总记录数
            },
            tableLoading: false,
            tableData: [],
            base64Url: {},
            sort: '',
            order: '',
            trendLine: {
                show: false,
                infos: {},
            },
            ElIconSearch,
            tableHeader: [],
            exportLoading: false,
        };
    },
    watch: {
        battName: antiShake(function () {
            this.pageInfo.pageNo = 1;
            this.getTableData();
        }, 500),
        riskName: {
            handler() {
                this.pageInfo.pageNo = 1;
                this.getTableData();
            },
            deep: true
        },
    },
    created() {},
    mounted() {
        this.tableHeader = [
            {
                id: 'position',
                name: this.$t('battery.fields.position'),
                sortable: true,
            },
            {
                id: 'name',
                name: this.$t('statistics.battery'),
                sortable: true,
            },
            {
                id: 'battType',
                name: this.$t('battery.fields.type'),
            },
            {
                id: 'riskName',
                name: this.$t('batteryOverview.fields.risk'),
            },
            {
                id: 'riskLevel',
                name: this.$t('batteryOverview.fields.riskLevels'),
                sortable: true,
            },
            {
                id: 'riskCause',
                name: this.$t('batteryOverview.fields.reason'),
            },
            {
                id: 'riskSuggestion',
                name: this.$t('batteryOverview.fields.handlingSuggestions'),
            },
        ];
    },
    computed: {
        isDark() {
            return this.$store.getters.getIsDark;
        },
        riskNameOptions() {
            return this.$store.getters.getRiskNameOptions || [];
        },
    },
    methods: {
        // 点击图表获取到的时间数据
        getTime(val) {
            this.evalTime = val;
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        getBase64Urls(url) {
            this.base64Url = url;
        },
        back() {
            this.$bus.emit('isHistory', '', false);
        },
        handleExport() {
            let nowTime = new Date().toLocaleDateString().split('/');
            let nowHour = new Date().getHours() + '';
            let nowMinute = new Date().getMinutes() + '';
            nowHour = nowHour.length == 1 ? '0' + nowHour : nowHour;
            nowMinute = nowMinute.length == 1 ? '0' + nowMinute : nowMinute;
            nowTime[1] = nowTime[1].length == 1 ? '0' + nowTime[1] : nowTime[1];
            nowTime[2] = nowTime[2].length == 1 ? '0' + nowTime[2] : nowTime[2];
            nowTime = nowTime.join('');
            let _name = `riskHistory_${nowTime}${nowHour}${nowMinute}.xlsx`;
            let images = [
                {
                    base64Str: this.base64Url.base64Url,
                    imageName: this.base64Url.name,
                    xline: 0,
                    yline: 1,
                    dim: 'History_Trend',
                },
            ];
            let queryParameter = {
                battName: this.battName,
                riskName: this.riskName,
                evaluateTime: this.evalTime,
                sort: this.order,
                order: this.sort,
                logicGroupId: this.nodeId,
                images,
            };
            const languageOption = (
                (localStorage['language-option'] && localStorage['language-option'].replace(/\"/g, '')) ||
                ''
            ).replace('-', '_');
            const forgerydefense = window.forgerydefense || '';
            let DOWNLOAD_URL = '/api/battery-manager/v1/risk/eval-statistics/export';
            let url = `${DOWNLOAD_URL}`;
            let config = {
                responseType: 'blob',
                headers: {
                    'language-option': languageOption,
                    'forgerydefense': forgerydefense
                },
            };
            this.exportLoading = true;
            axios
                .post(url, queryParameter, config)
                .then(res => {
                    this.exportLoading = false;
                    // 导出错误，返回json对象，需判断
                    if (res.data.type === 'application/json') {
                        let reader = new FileReader();
                        reader.onload = e => {
                            let result = JSON.parse(e.target.result);
                            if (result && result.code !== 0) {
                                let message = this.$message({
                                    message:
                                        this.$t('batteryView.exportFailed') + ': ' + result.message || result.error,
                                    duration: 5000,
                                    showClose: true,
                                    type: 'error',
                                });
                                this.$message.error(message);
                            }
                        };
                        reader.readAsText(res.data, ['utf-8']);
                    } else {
                        // 导出成功，返回数据流
                        let blob = new Blob([res.data]);
                        let url = window.URL.createObjectURL(blob); // 创建下载的链接
                        let link = document.createElement('a');
                        let fileName = _name;
                        if (res.headers['content-disposition']) {
                            let contentDisposition = res.headers['content-disposition'];
                            fileName = contentDisposition.split('filename=')[1];
                            fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                        }

                        link.style.display = 'none';
                        link.href = url;
                        link.download = `${fileName}`; // 下载后文件名
                        document.body.appendChild(link);
                        link.click(); // 点击下载
                        document.body.removeChild(link); // 下载完成移除元素
                        window.URL.revokeObjectURL(url); // 释放掉blob对象
                    }
                })
                .catch(() => {
                    this.exportLoading = false;
                });
        },
        /* Started by AICoder, pid:80df08259b484b58a91481d71657bd85 */
        healthType(row, val) {
            if (row && row[val]) {
                const id = row[val].id;
                if (id === 'level_1') {
                    return 'danger';
                }
                if (id === 'level_2') {
                    return 'warning';
                }
            }
            if (val === 'riskLevel' && !row[val]) {
                return 'success';
            }
            return '';
        },
        /* Ended by AICoder, pid:80df08259b484b58a91481d71657bd85 */
        getRiskColor(val) {
            let color = '';
            if (val === 'level_1') {
                color = 'rgba(245, 108, 108, 0.1)';
            } else if (val === 'level_2') {
                color = 'rgba(255, 200, 80, 0.1)';
            } else if (val === 'level_3') {
                color = 'rgba(16, 158, 191, 0.1)';
            }
            return color;
        },
        getRiskFontColor(val) {
            let color = '';
            if (val === 'level_1') {
                color = '#f56c6c';
            } else if (val === 'level_2') {
                color = '#ffc850';
            } else if (val === 'level_3') {
                color = '#109ebf';
            }
            return color;
        },
        handleSizeChange(pageSize) {
            this.pageInfo.pageSize = pageSize;
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        handleCurrentChange(pageNo) {
            this.pageInfo.pageNo = pageNo;
            this.getTableData();
        },
        sortChange(column) {
            // 排序
            this.sort = column.prop || '';
            if (column.order === 'descending') {
                this.order = 'desc';
            } else if (column.order === 'ascending') {
                this.order = 'asc';
            } else {
                this.order = '';
                this.sort = '';
            }
            this.pageInfo.pageNo = 1;
            this.getTableData();
        },
        getRequestData() {
            return {
                battName: this.battName,
                riskName: this.riskName,
                evaluateTime: this.evalTime,
                sort: this.order,
                order: this.sort,
                pageNo: this.pageInfo.pageNo,
                pageSize: this.pageInfo.pageSize,
                logicGroupId: this.nodeId,
            };
        },
        getTableData() {
            this.tableLoading = true;
            let queryCompareData = this.getRequestData();
            HTTP.request('getRiskHistoryTable', {
                method: 'post',
                data: queryCompareData,
                complete: resp => {
                    if (JSON.stringify(queryCompareData) !== JSON.stringify(this.getRequestData())) {
                        return;
                    }
                    this.tableData = [];
                    this.tableLoading = false;
                    if (resp.code === 0) {
                        this.tableData = resp.data || [];
                        this.pageInfo.total = resp.total;
                    } else {
                        this.pageInfo.total = 0;
                    }
                },
                error: () => {
                    if (JSON.stringify(queryCompareData) !== JSON.stringify(this.getRequestData())) {
                        return;
                    }
                    this.tableData = [];
                    this.tableLoading = false;
                    this.pageInfo.total = 0;
                },
            });
        },
        textSize(item) {
            let text = item.name;
            if (item.unit) {
                text = text + '(' + item.unit + ')';
            }
            let content = document.getElementById('temporary4');
            let span = document.createElement('span');
            let w = span.offsetWidth;
            span.style.visibility = 'hidden';
            span.style.fontSize = '14px';
            span.style.fontWeight = 'bolder';
            span.style.display = 'inline-block';
            content.appendChild(span);
            if (typeof span.textContent !== 'undefined') {
                span.textContent = text;
            } else {
                span.innerText = text;
            }
            w = parseFloat(window.getComputedStyle(span).width) - w;
            content.removeChild(span);
            if (['status', 'preStatus'].includes(item.id)) {
                return Math.max(w + 50, 120);
            } else if (item.id === 'position') {
                return Math.max(w + 50, 250);
            } else if (item.id === 'soh') {
                return w + 63;
            } else if (
                [
                    'evaluateTime',
                    'startDate',
                    'productionDate',
                    'gmtModified',
                    'collectionTime',
                    'sohThresholdRange',
                ].includes(item.id)
            ) {
                return Math.max(w + 50, 160);
            } else if (item.id === 'name') {
                return Math.max(w + 50, 120);
            } else if (item.id === 'life') {
                return Math.max(w + 50, 120);
            } else {
                return w + 50;
            }
        },
        formatHeader(item) {
            let name = item.name;
            if (item.unit) {
                name = name + '(' + item.unit + ')';
            }
            return name;
        },
        formatValue(val, id) {
            if (!val && val !== 0) {
                return '--';
            } else if (id === 'battType') {
                return val?.name || '--';
            } else {
                return val;
            }
        },
        toDetail(row) {
            this.trendLine = {
                show: true,
                infos: row,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.el-form {
    padding-top: 14px;
    :deep(.el-form-item__label) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
    }
}
.title {
    color: #333;
    font-size: 16px;
    position: relative;
    display: inline-block;
    .back-button {
        position: absolute;
        right: 0;
        top: 0;
    }
    .export-button {
        position: absolute;
        right: 70px;
        top: 0;
    }
}
.tableBar {
    padding-top: 8px;
    height: 32px;
    line-height: 32px;
    h4 {
        display: inline-block;
        padding: 0;
    }
    .evaluate-date {
        margin-left: 16px;
        font-size: 12px;
        color: #606266;
        font-weight: 700;
        span {
            color: #bfbfbf;
            font-weight: 400;
            margin-left: 16px;
        }
    }
    :deep(.el-input) {
        width: 300px;
    }
}
.icon-button {
    font-size: 14px;
    width: 32px;
    height: 32px;
    box-sizing: border-box;
    padding: 8px;
    margin: 0 8px;
    border: 1px solid #d9d9d9;
    color: #595959;
    cursor: pointer;
    border-radius: 4px;
    vertical-align: middle;
    a {
        text-decoration: none;
    }

    &:hover {
        border: 1px solid #0f8dfb;
    }
}
.active {
    &:before {
        color: rgb(25, 147, 255) !important;
    }
}
.connect-line {
    padding: 0 8px;
    color: #4c4d4f;
}
.filter-form {
    :deep(.el-form-item):nth-child(2),
    :deep(.el-form-item):nth-child(3) {
        .el-form-item__content {
            .el-input {
                width: 140px;
                .el-input__wrapper {
                    width: 140px;
                }
                .el-input__inner {
                    width: 100%;
                }
            }
        }
    }
}
html.dark {
    .title {
        color: #d9d9d9;
    }
    .tableBar .evaluate-date {
        color: #d9d9d9;
        span {
            color: #a4a7b3;
        }
    }
    .connect-line {
        color: #4c4d4f;
    }
}
</style>
