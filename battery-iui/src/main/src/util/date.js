import moment from 'moment';

let formatString = 'YYYY-MM-DD HH:mm:ss';

function getStartDate(date, type) {
    return date.startOf(type).format(formatString);
}

export function getFormatDate(date, formatStr = formatString) {
    return moment(date).format(formatStr);
}

function getCustomDate(startTime, endTime) {
    let start = moment(startTime);
    let end = moment(endTime);
    end.hour(23);
    end.minute(59);
    end.second(59);
    end.millisecond(0);
    end = end.format(formatString);
    start = start.format(formatString);

    return {
        start,
        end,
    };
}

// type可取 year，month，week, day
// 表示当年，当月，当周
export function getTimeFromStart(type, startTime, endTime) {
    if (type === 'custom') {
        return getCustomDate(startTime, endTime);
    }

    let start = moment();
    let end = moment(start);
    start = getStartDate(start, type);
    end = end.format(formatString);

    return {
        start,
        end,
    };
}

// 表示当前时间，最近最近一年，一月...
export function getTimeBy(type, startTime, endTime) {
    if (type === 'custom') {
        return getCustomDate(startTime, endTime);
    }

    let start = moment();
    let end = moment(start);
    end = end.format(formatString);

    switch (type) {
        case 'year':
            start.year(start.year() - 1);
            break;
        case 'week':
            start.subtract(7, 'days');
            break;
        case 'month':
            start.subtract(1, 'months');
            break;
        case 'day':
            start.hour(0);
            start.minute(0);
            start.second(0);
            start.millisecond(0);
            break;
    }

    start = start.format(formatString);

    return {
        start,
        end,
    };
}
// 表示从今天起前多少天的日期,0为当天
export function getDay(day, showAll) {
    let today = new Date();
    let targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
    today.setTime(targetday_milliseconds);
    let tYear = today.getFullYear();
    let tMonth = today.getMonth();
    let tDate = today.getDate();
    let tHour = today.getHours();
    let tMinute = today.getMinutes();
    let tSecond = today.getSeconds();

    tMonth = doHandleFormat(tMonth + 1);
    tDate = doHandleFormat(tDate);
    tHour = doHandleFormat(tHour);
    tMinute = doHandleFormat(tMinute);
    tSecond = doHandleFormat(tSecond);
    if (showAll) {
        return tYear + '-' + tMonth + '-' + tDate + ' ' + tHour + ':' + tMinute + ':' + tSecond;
    } else {
        return tYear + '-' + tMonth + '-' + tDate;
    }
}
function doHandleFormat(month) {
    let m = month;
    if (month.toString().length == 1) {
        m = '0' + month;
    }
    return m;
}
