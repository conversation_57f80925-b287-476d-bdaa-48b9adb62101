/**
 * 检验多个区间是否重叠
 * @param {*} arr 区间数组
 * @returns Boolean， true 重叠，false 不重叠
 */
export default function checkRangeOverlap(arr) {
    for (let i = 0; i < arr.length - 1; i++) {
        const maxStart = [arr[0][0], arr[i + 1][0]];
        const minEnd = [arr[0][1], arr[i + 1][1]];
        if (Math.max(...maxStart) <= Math.min(...minEnd)) {
            return true;
        }
    }

    if (arr.length > 2) {
        arr.shift();
        return checkRangeOverlap(arr);
    }

    return false;
}

// 示列
// const arr = [[1, 2], [3, 9], [11, 12]];
// console.log(check(arr));
