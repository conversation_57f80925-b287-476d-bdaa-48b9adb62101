export default [
    {
        prop: 'smpId',
        label: 'monitor.fields.smpId',
        tableShow: false,
        filterable: true,
        listShowOrder: 5,
    },
    {
        prop: 'name',
        label: 'monitor.fields.name',
        tableShow: true,
        filterable: true,
        sortable: true,
        listShowOrder: 6,
        formatter: function (row, column, cellValue, index) {
            return row.nameI18n ? row.nameI18n[this.$i18n.locale.replace('-', '_')] : row.name;
        },
    },
    {
        prop: 'value',
        label: 'monitor.fields.value',
        tableShow: true,
        filterable: true,
        sortable: true,
        listShowOrder: 7,
    },
    {
        prop: 'collectionTime',
        label: 'monitor.fields.collectionTime',
        tableShow: true,
        filterable: true,
        listShowOrder: 8,
    },
];
