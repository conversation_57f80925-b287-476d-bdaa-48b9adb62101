import i18n from '@/util/i18n.js';
export default [
    {
        prop: 'oid',
        label: 'ID',
        defaultValue: '',
    },
    {
        prop: 'location',
        label: i18n.global.t('battery-field-location'),
        listShowOrder: 1,
        sortable: true,
    },
    {
        prop: 'spName',
        label: i18n.global.t('battery-field-spName'),
        listShowOrder: 3,
    },
    {
        prop: 'assessTime',
        label: i18n.global.t('battery-field-assess-time'),
        listShowOrder: 4,
        sortable: true,
    },
    {
        prop: 'dur',
        label: i18n.global.t('battery-field-standby-duration'),
        listShowOrder: 5,
        sortable: true,
        sortable: true,
    },
    {
        prop: 'result',
        label: i18n.global.t('battery-field-standby-status'),
        listShowOrder: 6,
        sortable: true,
        formatter: function (row) {
            return i18n.global.t('battery-field-standby-status-' + row.result);
        },
    },
];
