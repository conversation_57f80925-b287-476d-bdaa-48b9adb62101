import i18n from '@/util/i18n.js';
import moment from 'moment';

export default [
    {
        prop: 'id',
        label: 'ID',
        defaultValue: '',
    },
    {
        prop: 'battName',
        label: i18n.global.t('battery-soh-field-battName'),
        listShowOrder: 2,
    },
    {
        prop: 'birthday',
        label: i18n.global.t('battery-life-basicInfo-birthday'),
        listShowOrder: 3,
        formatter: function (row) {
            return row.birthday != null ? moment(row.birthday).format('YYYY-MM-DD') : null;
        },
    },
    {
        prop: 'battLife',
        label: i18n.global.t('battery-life-expectedLife') + '(' + i18n.global.t('battery-life-unit-month') + ')',
        listShowOrder: 4,
    },
    {
        prop: 'expectedBattLife',
        label: i18n.global.t('battery-life-theoryExpectedLife') + '(' + i18n.global.t('battery-life-unit-month') + ')',
        listShowOrder: 5,
    },
    {
        prop: 'cycCount',
        label: i18n.global.t('battery-life-accumCycleTimes'),
        listShowOrder: 6,
    },
    {
        prop: 'expectedCycCount',
        label: i18n.global.t('battery-life-theoryCycleTimes'),
        listShowOrder: 7,
    },
    {
        prop: 'avgDepth',
        label: i18n.global.t('battery-life-avgDischargeDepth') + '(%)',
        listShowOrder: 8,
    },
    {
        prop: 'lifeState',
        label: i18n.global.t('battery-life-status'),
        listShowOrder: 9,
        formatter: function (row) {
            let result = '';
            if (row.status === 'normal') {
                result = i18n.global.t('battery-life-normal');
            } else if (row.status === 'obnormal') {
                result = i18n.global.t('battery-life-shortage');
            } else if (row.status === 'unknown') {
                result = i18n.global.t('battery-life-unknown');
            }
            return result;
        },
    },
    {
        prop: 'brand',
        label: i18n.global.t('battery-life-basicInfo-brand'),
        listShowOrder: 10,
    },
    {
        prop: 'ratedCapacity',
        label: i18n.global.t('battery-life-basicInfo-ratedCapacity') + '(V)',
        listShowOrder: 11,
    },
    {
        prop: 'electrodeMaterial',
        label: i18n.global.t('battery-life-basicInfo-electrodeMaterial'),
        listShowOrder: 12,
        formatter: function (row) {
            let type = row.electrodeMaterial;
            return type != null ? i18n.global.t('battery-life-basicInfo-electrodeMaterial-' + type) : null;
        },
    },
];
