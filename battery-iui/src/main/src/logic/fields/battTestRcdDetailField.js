import i18n from '@/util/i18n.js';
export default [
    {
        prop: 'location',
        label: i18n.global.t('battery-field-location'),
        listShowOrder: 1,
    },
    {
        prop: 'battName',
        label: i18n.global.t('battery-soh-field-battName'),
        listShowOrder: 3,
    },
    {
        prop: 'startTime',
        label: i18n.global.t('battery-test-startTime'),
        listShowOrder: 4,
    },
    {
        prop: 'duration',
        label: i18n.global.t('battery-test-detail-duration') + ' (H)',
        listShowOrder: 5,
    },
    {
        prop: 'initVolt',
        label: i18n.global.t('battery-test-detail-initVolt') + ' (V)',
        listShowOrder: 6,
    },
    {
        prop: 'finalVolt',
        label: i18n.global.t('battery-test-detail-finalVolt') + ' (V)',
        listShowOrder: 7,
    },
    {
        prop: 'initSOC',
        label: i18n.global.t('battery-test-detail-initSOC') + ' (%)',
        listShowOrder: 8,
    },
    {
        prop: 'finalSOC',
        label: i18n.global.t('battery-test-detail-finalSOC') + ' (%)',
        listShowOrder: 9,
    },
];
