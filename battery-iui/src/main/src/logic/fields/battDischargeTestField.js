import i18n from '@/util/i18n.js';
export default [
    {
        prop: 'siteId',
        label: 'siteId',
        defaultValue: '',
    },
    {
        prop: 'location',
        label: i18n.global.t('battery-field-location'),
        listShowOrder: 1,
    },
    {
        prop: 'siteName',
        label: i18n.global.t('battery-field-siteName'),
        listShowOrder: 2,
    },
    {
        prop: 'siteGrade',
        label: i18n.global.t('common-site-level'),
        listShowOrder: 3,
    },
    {
        prop: 'powerSupplyScene',
        label: i18n.global.t('common-site-powersupplyscene'),
        listShowOrder: 4,
    },
    {
        prop: 'sohState',
        label: i18n.global.t('common-batt-soh'),
        listShowOrder: 5,
        formatter: function (row) {
            let result = '--';
            if (row.sohState === 0) {
                result = i18n.global.t('battery-soh-health');
            } else if (row.sohState === 1) {
                result = i18n.global.t('battery-soh-subHealth');
            } else if (row.sohState === 2) {
                result = i18n.global.t('battery-soh-fault');
            }
            return result;
        },
    },
    {
        prop: 'lifeState',
        label: i18n.global.t('common-batt-lifeStatus'),
        listShowOrder: 6,
        formatter: function (row) {
            let result = '--';
            if (row.lifeState === 0) {
                result = i18n.global.t('battery-life-normal');
            } else if (row.lifeState === 1) {
                result = i18n.global.t('battery-life-shortage');
            } else if (row.lifeState === 2) {
                result = i18n.global.t('battery-life-unknown');
            }
            return result;
        },
    },
    {
        prop: 'battState',
        label: i18n.global.t('battery-test-battState'),
        listShowOrder: 7,
        formatter: function (row) {
            let result = '--';
            if (row.battState === 0) {
                result = i18n.global.t('battery-test-battState-float');
            } else if (row.battState === 1) {
                result = i18n.global.t('battery-test-battState-equal');
            } else if (row.battState === 2) {
                result = i18n.global.t('battery-test-battState-test');
            } else if (row.battState === 3) {
                result = i18n.global.t('battery-test-battState-discharge');
            } else if (row.battState === 4) {
                result = i18n.global.t('battery-test-battState-detect');
            } else if (row.battState === 5) {
                result = i18n.global.t('battery-test-battState-transition');
            } else if (row.battState === 6) {
                result = i18n.global.t('battery-test-battState-charge');
            }
            return result;
        },
    },
    {
        prop: 'acInputState',
        label: i18n.global.t('battery-test-acInputState'),
        listShowOrder: 8,
        formatter: function (row) {
            let result = '--';
            if (row.acInputState === 0) {
                result = i18n.global.t('battery-test-acInputState-normal');
            } else {
                result = i18n.global.t('battery-test-acInputState-abnormal');
            }
            return result;
        },
    },
    {
        prop: 'lastTest',
        label: i18n.global.t('battery-test-lastTest'),
        listShowOrder: 9,
        headerAlign: 'center',
        children: [
            {
                prop: 'lastTestTime',
                label: i18n.global.t('battery-test-startTime'),
                formatter: function (row) {
                    return row.lastTestTime == null ? '--' : row.lastTestTime;
                },
            },
            {
                // 0:start, 1:testing, 2:finish, 3:timeout, 4:cancel
                prop: 'taskState',
                label: i18n.global.t('battery-test-state'),
                formatter: function (row) {
                    let result = '--';
                    if (row.taskState === 0) {
                        result = i18n.global.t('battery-test-start');
                    } else if (row.taskState === 1) {
                        result = i18n.global.t('battery-test-testing');
                    } else if (row.taskState === 2) {
                        result = i18n.global.t('battery-test-finish');
                    } else if (row.taskState === 3) {
                        result = i18n.global.t('battery-test-timeout');
                    } else if (row.taskState === 4) {
                        result = i18n.global.t('battery-test-cancel');
                    }
                    return result;
                },
            },
        ],
    },
];
