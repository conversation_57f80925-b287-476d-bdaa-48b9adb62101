import i18n from '@/util/i18n.js';

export default [
    {
        prop: 'id',
        label: 'ID',
        defaultValue: '',
    },
    {
        prop: 'moId',
        label: 'moId',
        defaultValue: '',
    },
    {
        prop: 'location',
        label: i18n.global.t('battery-field-location'),
        listShowOrder: 1,
    },
    {
        prop: 'siteName',
        label: i18n.global.t('battery-field-siteName'),
        listShowOrder: 2,
    },
    {
        prop: 'name',
        label: i18n.global.t('battery-life-basicInfo-name'),
        listShowOrder: 3,
    },
    {
        prop: 'expectedLife',
        label: i18n.global.t('battery-life-expectedLife-threshold') + '(%)',
        listShowOrder: 4,
    },
    {
        prop: 'cycLifeOfPctThres',
        label: i18n.global.t('battery-life-cycLifeOfPctThres-threshold') + '(%)',
        listShowOrder: 5,
    },
    {
        prop: 'siteGrade',
        label: i18n.global.t('common-site-level'),
        listShowOrder: 6,
    },
    {
        prop: 'powerSupplyScene',
        label: i18n.global.t('common-site-powersupplyscene'),
        listShowOrder: 7,
    },
    {
        prop: 'brand',
        label: i18n.global.t('battery-life-basicInfo-brand'),
        listShowOrder: 8,
    },
    {
        prop: 'ratedCapacity',
        label: i18n.global.t('battery-life-basicInfo-ratedCapacity') + '(V)',
        listShowOrder: 9,
    },
    {
        prop: 'electrodeMaterial',
        label: i18n.global.t('battery-life-basicInfo-electrodeMaterial'),
        listShowOrder: 10,
        formatter: function (row) {
            let type = row.electrodeMaterial;
            return type != null ? i18n.global.t('battery-life-basicInfo-electrodeMaterial-' + type) : null;
        },
    },
];
