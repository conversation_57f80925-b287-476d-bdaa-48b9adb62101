import i18n from '@/util/i18n.js';
import moment from 'moment';

export default [
    {
        prop: 'id',
        label: 'ID',
        defaultValue: '',
    },
    {
        prop: 'moId',
        label: 'moId',
        defaultValue: '',
    },
    {
        prop: 'location',
        label: i18n.global.t('battery-field-location'),
        listShowOrder: 1,
    },
    {
        prop: 'name',
        label: i18n.global.t('battery-life-basicInfo-name'),
        listShowOrder: 2,
    },
    {
        prop: 'brand',
        label: i18n.global.t('battery-life-basicInfo-brand'),
        listShowOrder: 3,
        sortable: true,
    },
    {
        prop: 'birthday',
        label: i18n.global.t('battery-life-basicInfo-birthday'),
        listShowOrder: 4,
        formatter: function (row) {
            return row.birthday != null ? moment(row.birthday).format('YYYY-MM-DD') : null;
        },
    },
    {
        prop: 'openingDate',
        label: i18n.global.t('battery-life-basicInfo-openingDate'),
        listShowOrder: 5,
        formatter: function (row) {
            return row.openingDate != null ? moment(row.openingDate).format('YYYY-MM-DD') : null;
        },
    },
    {
        prop: 'ratedCapacity',
        label: i18n.global.t('battery-life-basicInfo-ratedCapacity') + '(V)',
        listShowOrder: 6,
    },
    {
        prop: 'electrodeMaterial',
        label: i18n.global.t('battery-life-basicInfo-electrodeMaterial'),
        listShowOrder: 7,
        formatter: function (row) {
            let type = row.electrodeMaterial;
            return type != null ? i18n.global.t('battery-life-basicInfo-electrodeMaterial-' + type) : null;
        },
    },
];
