export default [
    {
        prop: 'alarmcode',
        label: 'monitor.fields.alarmcode',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'mename',
        label: 'monitor.fields.mename',
        tableShow: true,
        filterable: true,
    },
    {
        prop: 'perceivedseverity',
        label: 'monitor.fields.perceivedseverity',
        sortable: true,
        tableShow: true,
        filterable: true,
    },
    {
        prop: 'codename',
        label: 'monitor.fields.codename',
        tableShow: true,
        filterable: true,
    },
    // {
    //     "prop": "alarmshowtime",
    //     "label": "monitor.fields.alarmshowtime",
    //     "tableShow": true,
    //     "filterable": true
    // },
    {
        prop: 'alarmraisedtimestr',
        label: 'monitor.raisedTime',
        tableShow: true,
        filterable: true,
    },
    {
        prop: 'alarmclearedtimestr',
        label: 'monitor.clearTime',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'ackstate',
        label: 'monitor.fields.ackstate',
        tableShow: true,
        filterable: true,
    },
    {
        prop: 'alarmstate',
        label: 'monitor.fields.alarmstate',
        tableShow: true,
        filterable: true,
    },
    {
        prop: 'mocname',
        label: 'monitor.fields.mocname',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'linkname',
        label: 'monitor.fields.link',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'commenttext',
        label: 'monitor.fields.comment',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'commentuserid',
        label: 'monitor.fields.commentUserId',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'commenttime',
        label: 'monitor.fields.commentTime',
        tableShow: false,
        filterable: true,
    },
    {
        prop: 'restypename',
        label: 'monitor.fields.restypeName',
        tableShow: false,
        filterable: true,
    },
];
