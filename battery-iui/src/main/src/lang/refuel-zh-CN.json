{"refuelRemind": {"title": {"refuelReminder": "加油提醒", "reminderRules": "设置提醒规则", "reminderRulesManagement": "提醒规则管理", "addRefuelRemindRule": "新增加油提醒规则", "editRefuelRemindRule": "编辑", "set": "设置", "addRefuelRecord": "新增加油记录", "ignoreRemind": "忽略提醒", "fuelOverview": "燃油总览", "fuelStatusRatio": "油位状态占比", "consumptionStatusRatio": "油耗状态分布占比"}, "fields": {"ruleName": "规则名称", "purposeType": "油机用途类型", "standbyThresholdHourUnit": "备电时长阈值(h)", "standbyThresholdDayUnit": "备电天数阈值(d)", "standbyThresholdHour": "备电时长阈值", "standbyThresholdDay": "备电天数阈值", "ruleDescription": "规则说明", "standybyOilEngine": "备电用油机", "dailyOilEngine": "日常循环用油机", "position": "位置", "siteName": "站点名称", "siteLevel": "站点等级", "supplyScene": "供电场景", "remindRule": "提醒规则", "oilRemindRule": "油机提醒规则", "dieselGenerator": "油机", "dgPosition": "位置", "status": "状态", "reminderRule": "提醒规则", "reminderReason": "提醒原因", "reminderTime": "提醒时间", "curRemainingOil": "当前剩余油量(L)", "curOilLevel": "当前油位(%)", "planRefuel": "计划加油量(L)", "curStandbyTime": "当前备电时长(h)", "curStandbyDay": "当前备电天数(d)", "addRefuelRecord": "增加加油记录", "ignoreReason": "忽略原因", "ignore": "忽略", "unhandled": "未处理", "handled": "已处理", "ignored": "已忽略", "refuelTime": "加油时间", "refuelVolume": "加油量(L)", "refuelMan": "加油人", "refuelOrganization": "加油组织", "fuelType": "燃油类型", "label": "标号", "remainingOil": "剩余油量(L)", "oilLevel": "油位(%)", "volumeAfterRefuel": "加油后油量(L)", "handler": "处理人", "addRefulvolPlan": "计划加油量到液位", "addRefulVolumnRange": "1-100的整数", "currentMonth": "本月", "currentYear": "本年", "custom": "自定义", "oilEngineNum": "油机数量", "quantityOfFuelLow": "油位低油机", "quantityOfLeakage": "漏油油机数量", "oilQuantity": "油量", "history": "历史", "analyze": "分析", "refuelNumber": "加油次数", "refuel": "加油量(L)", "comsumption": "耗油量(L)", "leakage": "漏油量(L)", "oilEngineName": "油机名称", "oilQuantityChange": "油量变化(L)", "consumptionRate": "耗油率(L/h)", "refuelRecord": "加油记录", "comsumptionRecord": "耗油记录", "leakageRecord": "漏油记录", "byDay": "按日", "byMonth": "按月", "byYear": "按年", "byAll": "全部", "exportCsv": "导出csv", "exportExcel": "导出excel", "startFuel": "开始油量(L)", "endFuel": "结束油量(L)", "oilQuantityHistory": "油量历史", "comsumptionAnalyze": "耗油分析", "refuelBtn": "加油", "consumeBtn": "耗油", "leakageBtn": "漏油", "fuelMode": "加油方式", "byFuelLevel": "按油位", "byFuelVolume": "按油量", "planRefuelVolume": "计划加油量", "hourUnit": "h", "dayUnit": "d"}, "tipMessage": {"confirmToDeleteRecord": "是否删除所选规则？", "ruleUsedCannotDelete": "该规则已被引用，无法删除。", "maxlength50": "支持最大长度为50", "maxlength": "长度不超过{length}个字符", "inputName": "请输入任务名称", "addRefulNotNull": "加油量不能为空", "setReful": "请输入1-100的整数", "setRefulNew": "请输入1-9999的整数", "exportLoading": "正在导出中，请稍候...", "fetchDataFailed": "获取数据失败", "loadMore": "加载更多", "fixedColumnNotChange": "油机、站点、日期、位置、操作排序固定，不可改变", "fuelLevelTip": "系统会根据加油量自动取整", "fuelVolumeTip": "为防止超过油箱容量，加油量会自动调整", "planFuelVolumeTip": "填写范围1~9999的整数", "standbyThresholdDayTip": "填写范围1~365的整数"}}, "refuelCheck": {"title": {"refuelCheck": "加油核查", "refuelUpload": "加油上报"}, "export": "导出", "fields": {}, "tipMessage": {}}}