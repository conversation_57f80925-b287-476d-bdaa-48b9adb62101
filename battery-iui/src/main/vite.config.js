import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import svgBuilder from './src/plugins/svg-builder';
const path = require('path');

const URL = '';
const COOKIE = '';

export default defineConfig({
    root: './',
    base: './',
    build: {
        outDir: '../iui/uedm-battery',
        emptyOutDir: true,
        chunkSizeWarningLimit: 3000,
    },
    plugins: [
        vue(),
        vueJsx({
            // options are passed on to @vue/babel-plugin-jsxs
        }),
        svgBuilder('./src/assets/img/svg/'),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
        extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    define: { 'process.env': {} },

    server: {
        proxy: {
            '^/api/.*': {
                // 匹配以 '/api/' 开头的请求。
                target: `https://${URL}/`, // 请求转发到服务器
                changeOrigin: true,
                ws: true,
                secure: false,
                configure(proxy) {
                    proxy.on('proxyReq', proxyReq => {
                        proxyReq.setHeader('Host', URL);
                        proxyReq.setHeader('Cookie', COOKIE);
                        proxyReq.setHeader('Referer', `https://${URL}/iui/uedm-battery/`);
                        proxyReq.setHeader('username', '');
                    });
                },
            },
            '^/webgis': {
                target: `https://${URL}/`,
                changeOrigin: true,
                // ws: true,
                secure: false,
                cookieDomainRewrite: {
                    '*': `${URL}`,
                },
                configure: proxy => {
                    proxy.on('proxyReq', req => {
                        req.setHeader('Cookie', COOKIE);
                        req.setHeader('Host', `${URL}`);
                        req.setHeader('Origin', `https://${URL}`);
                        req.setHeader('Referer', `https://${URL}/iui/uedm-monitor-reconstruct/`);
                    });
                },
            },
        },
    },
});
