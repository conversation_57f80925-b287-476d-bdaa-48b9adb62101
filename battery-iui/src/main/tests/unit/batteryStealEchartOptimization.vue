/* Started by AICoder, pid:426a620f11r35fb1479f0b01112f2b1e5ca0b841 */
<template>
    <div ref="chart" class="chart"></div>
</template>
<script>
import * as echarts from 'echarts';
const chartColor = {
    default: {
        background: 'rgb(48, 49, 51, 0.75)',
        border: 'transparent',
        color: '#fff',
        inactiveColor: '#d9d9d9', // 图例关闭时的颜色
    },
    dark: {
        background: 'rgb(255,255,255,0.75)',
        border: 'transparent',
        color: '#303133',
        inactiveColor: '#666666',
    },
};
export default {
    props: ['itemData'],
    data() {
        return {};
    },
    computed: {
        winResize() {
            return this.$store.getters.getResize;
        },
        isDark() {
            return this.$store.getters.getIsDark;
        },
        skins() {
            return this.isDark ? 'dark' : 'default';
        },
    },
    watch: {
        winResize() {
            if (this.myChart) {
                this.myChart.resize();
            }
        },
    },
    mounted() {
        if (this.$refs.chart) {
            this.initCharts();
        }
    },
    methods: {
        initCharts() {
            let a = window.devicePixelRatio;
            this.myChart = echarts.init(this.$refs.chart, this.skins, {
                height: '70px',
                devicePixelRatio: a,
                renderer: 'svg',
            });
            this.myChart.setOption(this.getOptions());
        },
        getOptions() {
            const colorType = this.isDark ? 'dark' : 'default';
            return {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'item',
                    formatter: '{b0}: {c0}',
                    backgroundColor: chartColor[colorType].background,
                    borderColor: chartColor[colorType].border,
                    textStyle: { color: chartColor[colorType].color },
                },
                legend: {
                    top: 'center',
                    orient: 'vertical',
                    itemWidth: 12,
                    itemHeight: 12,
                    itemGap: 6,
                    left: '40%',
                    textStyle: {
                        lineHeight: 14,
                        height: 14,
                    },
                    inactiveColor: chartColor[colorType].inactiveColor, // 图例关闭时的颜色
                    formatter: (name) => `   ${name}: ${this.getValByName(name)}`,
                },
                color: ['#92dd92', '#ffc850', '#bfbfbf'],
                series: [
                    {
                        type: 'pie',
                        radius: '100%',
                        center: [51, '50%'],
                        label: {
                            show: false,
                        },
                        data: this.itemData.data,
                        emphasis: {
                            scale: false,
                        },
                    },
                ],
            };
        },
        getValByName(name) {
            return this.itemData.data.find((item) => item.name === name).value;
        },
        i18nLabel(val) {
            return this.$t(`batteryMonitor.battStatisticLabel.${val}`);
        },
    },
};
</script>
/* Ended by AICoder, pid:426a620f11r35fb1479f0b01112f2b1e5ca0b841 */
