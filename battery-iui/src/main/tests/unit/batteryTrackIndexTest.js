/* Started by AICoder, pid:uefc0i1c99519ca141190aafa084df2844f3b7ee */
import { mount } from '@vue/test-utils';
import YourComponent from './YourComponent.vue';

describe('YourComponent', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = mount(YourComponent, {
            props: {
                systemInfo: {},
                height: 0,
            },
        });
    });

    afterEach(() => {
        wrapper.unmount();
    });

    it('should render correctly', () => {
        expect(wrapper.vm.$el).toMatchSnapshot();
    });
});
/* Ended by AICoder, pid:uefc0i1c99519ca141190aafa084df2844f3b7ee */

/* Started by AICoder, pid:hc4eejde698953514ea30b68b0042943d7b483f3 */
import { shallowMount } from '@vue/test-utils';
import BatteryForm from './BatteryForm';

describe('BatteryForm.vue', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = shallowMount(BatteryForm);
    });

    it('renders the component correctly', () => {
        expect(wrapper.exists()).toBe(true);
    });

    it('has the correct data properties', () => {
        expect(typeof wrapper.vm.activeNames).toBe('object');
        expect(typeof wrapper.vm.tableData).toBe('object');
        expect(typeof wrapper.vm.isExport).toBe('boolean');
        expect(typeof wrapper.vm.exportTableData).toBe('object');
        expect(typeof wrapper.vm.pageSize).toBe('number');
        expect(typeof wrapper.vm.pageNo).toBe('number');
        expect(typeof wrapper.vm.total).toBe('number');
        expect(typeof wrapper.vm.totalData).toBe('undefined');
        expect(typeof wrapper.vm.loading).toBe('boolean');
        expect(typeof wrapper.vm.oldFilter).toBe('object');
    });

    it('has the correct computed properties', () => {
        expect(typeof wrapper.vm.titleStyle).toBe('object');
    });

    it('has the correct methods', () => {
        expect(typeof wrapper.vm.degreeConvertBack).toBe('function');
        expect(typeof wrapper.vm.handleExportTableData).toBe('function');
        expect(typeof wrapper.vm.exportBegin).toBe('function');
        expect(typeof wrapper.vm.exportEnd).toBe('function');
        expect(typeof wrapper.vm.transferData).toBe('function');
        expect(typeof wrapper.vm.csvDataI18N).toBe('function');
        expect(typeof wrapper.vm.changeToGlobalMap).toBe('function');
        expect(typeof wrapper.vm.filterData).toBe('function');
        expect(typeof wrapper.vm.querySiteLevels).toBe('function');
        expect(typeof wrapper.vm.queryData).toBe('function');
    });
});
/* Ended by AICoder, pid:hc4eejde698953514ea30b68b0042943d7b483f3 */

/* Started by AICoder, pid:o5a263be28v9f97140ec097e90c9503512f5ce70 */
import { mount } from '@vue/test-utils';
import BatteryStatisticFilter from './BatteryStatisticFilter.vue';

describe('BatteryStatisticFilter', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = mount(BatteryStatisticFilter);
    });

    it('should render correctly', () => {
        expect(wrapper.element).toMatchSnapshot();
    });

    it('should reset form data when reset button is clicked', async () => {
        wrapper.vm.form.siteName = 'Test Site';
        await wrapper.find('.mt-16').trigger('click');
        expect(wrapper.vm.form.siteName).toBe('');
    });

    it('should emit filterData event with form data when filtering button is clicked', async () => {
        wrapper.vm.form.siteName = 'Test Site';
        wrapper.vm.$emit = jest.fn();
        await wrapper.findAll('.mt-16').at(0).trigger('click');
        expect(wrapper.vm.$emit).toHaveBeenCalledWith('filterData', wrapper.vm.form);
    });

    it('should not emit filterData event when begin time is greater than end time', async () => {
        wrapper.vm.form.ariseTimeBegin = '2022-01-02 12:00:00';
        wrapper.vm.form.ariseTimeEnd = '2022-01-01 12:00:00';
        wrapper.vm.$emit = jest.fn();
        await wrapper.findAll('.mt-16').at(0).trigger('click');
        expect(wrapper.vm.$emit).not.toHaveBeenCalled();
    });
});
/* Ended by AICoder, pid:o5a263be28v9f97140ec097e90c9503512f5ce70 */

/* Started by AICoder, pid:08696n5423ne768149a40a5e4007fb4479f2a616 */
import { shallowMount } from '@vue/test-utils';
import BatteryGlobalMap from './BatteryGlobalMap.vue';

describe('BatteryGlobalMap', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = shallowMount(BatteryGlobalMap);
    });

    it('renders correctly', () => {
        expect(wrapper.exists()).toBe(true);
    });

    it('toggles filter form on button click', async () => {
        const button = wrapper.find('.filter-img');
        await button.trigger('click');
        expect(wrapper.vm.filterFormShow).toBe(false);
        await button.trigger('click');
        expect(wrapper.vm.filterFormShow).toBe(true);
    });

    it('changes page on list statistics button click', async () => {
        const spy = jest.spyOn(wrapper.vm, '$emit');
        const button = wrapper.find('.list-statistics');
        await button.trigger('click');
        expect(spy).toHaveBeenCalledWith('changePage', 'BatteryForm');
    });

    it('closes collapse on close button click', async () => {
        wrapper.vm.showCollapse = true;
        const button = wrapper.find('.collapse-container-close-btn');
        await button.trigger('click');
        expect(wrapper.vm.showCollapse).toBe(false);
    });

    it('filters data on filterData call', async () => {
        const spy = jest.spyOn(wrapper.vm, 'queryData');
        wrapper.vm.filterData({ testFilter: 'test' });
        expect(spy).toHaveBeenCalledWith({ testFilter: 'test' });
    });
});
/* Ended by AICoder, pid:08696n5423ne768149a40a5e4007fb4479f2a616 */

/* Started by AICoder, pid:811e9rc8eaj3a42145e40a0c1045fb29cb99767f */
import { shallowMount } from '@vue/test-utils';
import BatteryStatistic from './BatteryStatistic.vue';

describe('BatteryStatistic.vue', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = shallowMount(BatteryStatistic, {
            propsData: {
                data: {
                    totalNumbers: 10,
                    onlineNumbers: 5,
                    offlineNumbers: 3,
                    communicationUnknownNumbers: 2,
                    staticNumbers: 4,
                    motionNumbers: 6,
                    motionUnknownNumbers: 1,
                    fortificationNumbers: 3,
                    withdrawNumbers: 2,
                    fortificationUnknownNumbers: 1,
                },
            },
        });
    });

    it('should render correctly', () => {
        expect(wrapper.element).toMatchSnapshot();
    });
});
/* Ended by AICoder, pid:811e9rc8eaj3a42145e40a0c1045fb29cb99767f */
/* Started by AICoder, pid:u6d367b451nc95d145cb0900a0ca5c4e6c475123 */
import { shallowMount } from '@vue/test-utils';
import BatteryStatisticBox from './BatteryStatisticBox.vue';

describe('BatteryStatisticBox.vue', () => {
    let wrapper;

    beforeEach(() => {
        wrapper = shallowMount(BatteryStatisticBox, {
            propsData: {
                battData: {
                    title: 'Test Title',
                    data: [
                        { label: 'Label 1', value: 10 },
                        { label: 'Label 2', value: 20 },
                        { label: 'Label 3', value: 30 },
                        { label: 'Label 4', value: 40 },
                    ],
                },
            },
        });
    });

    it('renders the correct title', () => {
        expect(wrapper.find('.title').text()).toBe('Test Title');
    });

    it('renders single data correctly', () => {
        const newBattData = {
            title: 'Single Data Title',
            data: [
                { label: 'Single Label', value: 50 },
            ],
        };
        wrapper.setProps({ battData: newBattData });
        expect(wrapper.find('.single-label').text()).toBe('Single Label');
        expect(wrapper.find('.single-value').text()).toBe('50');
    });

    it('renders double data correctly', () => {
        expect(wrapper.findAll('.double-label').at(0).text()).toBe('Label 1');
        expect(wrapper.findAll('.double-value').at(0).text()).toBe('10');
        expect(wrapper.findAll('.double-label').at(1).text()).toBe('Label 2');
        expect(wrapper.findAll('.double-value').at(1).text()).toBe('20');
        expect(wrapper.findAll('.double-label').at(2).text()).toBe('Label 3');
        expect(wrapper.findAll('.double-value').at(2).text()).toBe('30');
    });
});
/* Ended by AICoder, pid:u6d367b451nc95d145cb0900a0ca5c4e6c475123 */

