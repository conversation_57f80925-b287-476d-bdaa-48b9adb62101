package com.zte.uedm.battery.test.task;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.zte.uedm.common.consts.standardpoint.SpStandardPointConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.mapper.TaskBean;
import com.zte.uedm.battery.mapper.TaskState;
import com.zte.uedm.battery.test.refs.DataService;
import com.zte.uedm.battery.test.refs.PointData;
import com.zte.uedm.battery.test.refs.ResourceService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TaskChecker {
	/**
	 * 最长测试时间
	 */
	private static final long MAX_TEST_TIME = 24 * 3600 * 1000L;

	@Autowired
	private TaskService ts;

	@Autowired
	private DataService ds;

	@Autowired
	private ResourceService rs;

//	@Scheduled(cron = "0 0/10 * * * ?")
	public void check() {
		log.info("Start checking battery test task status...");
		List<TaskBean> tasks = ts.findTasksInExec();
		List<TaskBean> udps = new ArrayList<>();
		for (TaskBean task : tasks) {
			boolean upd = updTask(task);
			if (upd) {
				udps.add(task);
			}
		}
		ts.updTask(udps);
	}

	private boolean updTask(TaskBean task) {
		TaskState org = task.getState();
		String siteId = task.getSiteId();
		String spId = rs.findSubMoId(siteId, BattConst.SP_MOC);
		if (spId == null) {
			task.setState(TaskState.CANCEL);
			task.setRemark("Site [" + siteId + "] battery test canceled, for site or sp isn't found!");
		} else {
			updTaskByHis(task, spId);
			if (!TaskState.FINISH.equals(task.getState()) && isTimeout(task)) {
				task.setState(TaskState.TIMEOUT);
			}
		}

		return !org.equals(task.getState());
	}

	private boolean isTimeout(TaskBean task) {
		return System.currentTimeMillis() - task.getStartTime().getTime() >= MAX_TEST_TIME;
	}

	private void updTaskByHis(TaskBean task, String spId) {
		List<PointData> datas = ds.findHisData(spId, Arrays.asList(SpStandardPointConstants.SP_BATT_STATUS),
				task.getStartTime(), null);

		for (PointData data : datas) {
			int v = data.getIntVal();
			if (TaskState.TESTING.equals(task.getState()) && v != BattState.TEST.getVal()) {
				// 已经有测试状态，并且当前状态不为测试，则测试结束
				task.setState(TaskState.FINISH);
				task.setEndTime(data.getTime());
				break;
			}
			if (v == BattState.TEST.getVal()) {
				task.setState(TaskState.TESTING);
			}
		}
	}

	public void forIndex(String index)
	{
		if (index.equals("1"))
		{
			log.debug("1");
		}
		else if(index.equals("2"))
		{
			log.info("2");
		}
		else if(index.equals("3"))
		{
			log.info("3");
		}
		else if(index.equals("4"))
		{
			log.info("4");
		}
		else if (index.equals("5")) {
			log.info("5");
		} else
		{
			log.info("6");
		}
	}
}
