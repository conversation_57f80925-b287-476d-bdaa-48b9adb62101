package com.zte.uedm.battery.test.refs.rpc;

import java.util.List;

import com.zte.uedm.battery.test.view.bean.ResponseBean;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

@ServiceHttpEndPoint(serviceName = "monitor-manager", serviceVersion = "v1")
public interface MonitorRpc {
	@POST("history-data/digital")
	Call<ResponseBean<List<HisDigital>>> getHisDigital(@Query("moId") String moId, @Body List<String> smpId,
			@Query("startTime") String startTime, @Query("endTime") String endTime);

	@GET("remote-control-smp")
	Call<ResponseBean<Object>> remoteCtrl(@Query("moId") String moId, @Query("smpId") String smpId,
			@Query("value") String value);
}
