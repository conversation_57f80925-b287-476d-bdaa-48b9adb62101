package com.zte.uedm.battery.test.refs.rpc;

import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

@ServiceHttpEndPoint(serviceName = "fm-active", serviceVersion = "v1")
public interface FmActiveRpc
{
    /**
     * 查询当前告警
     * 
     * @param body
     *            请求body
     * @return 当前告警
     */
    @POST("north/openapi/v1/activealarms")
    Call<AlarmResponse> getActiveAlarm(@Body AlarmQryBoby body);
}
