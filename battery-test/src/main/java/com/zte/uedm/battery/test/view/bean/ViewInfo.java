package com.zte.uedm.battery.test.view.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ViewInfo {
	public static final int LIFE_NORMAL = 0, LIFE_OBNORMAL = 1, LIFE_UNKNOWN = 2;
	public static final int SOH_HEALTH = 0, SOH_SUBHEALTH = 1, SOH_UNHEALTH = 2;
	
	private String siteId;
	private String siteName;
	private String location;
	private String siteGrade;
	private String powerSupplyScene;

	private Integer battState;

	private Integer acInputState;

	/**
	 * 0，健康；1，亚健康；2，异常
	 */
	private Integer sohState;
	/**
	 * 0，足；1，不足；2，未知
	 */
	private Integer lifeState;
	
	/**
	 * 最近测试时间
	 */
	private String lastTestTime;
	
	/**
	 * 测试状态
	 */
	private Integer taskState;
}
