package com.zte.uedm.battery.test.env;

import java.util.ArrayList;
import java.util.List;

import org.mockito.Mock;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.api.service.DataService;
import com.zte.uedm.battery.life.service.BattLifeService;
import com.zte.uedm.battery.soh.service.BattSOHService;
import com.zte.uedm.battery.test.refs.rpc.FmActiveRpc;
import com.zte.uedm.battery.test.refs.rpc.MonitorRpc;

@Configuration
@SpringBootApplication(scanBasePackages = { "com.zte.uedm.battery.test" })
@MapperScan({ "com.zte.uedm.battery.mapper" })
public class Config {
	@Mock
	private ConfigurationService cfgSvc;
	@Mock
	private DataService ds;
	@Mock
	private MonitorRpc mRpc;
	@Mock
	private FmActiveRpc fmRpc;
	@Mock
	private BattLifeService life;
	@Mock
	private BattSOHService soh;

	public Config() {
		org.mockito.MockitoAnnotations.initMocks(this);

	}

	@Bean
	public ConfigurationService getConfigurationService() {
		return cfgSvc;
	}

	@Bean
	public MonitorRpc getMonitorRpc() {
		return mRpc;
	}

	@Bean
	public DataService getDataService() {
		return ds;
	}

	@Bean
	public BattLifeService getBattLifeService() {
		return life;
	}

	@Bean
	public BattSOHService getBattSOHService() {
		return soh;
	}

	@Bean
	public FmActiveRpc getFmActiveRpc() {
		return fmRpc;
	}

	public static List<MoObjectConfiguration> getMonitorObjs(String siteId, String... moIds) {
		List<MoObjectConfiguration> c = new ArrayList<>();
		for (String moId : moIds) {
			MoObjectConfiguration mo = new MoObjectConfiguration();
			mo.setId(moId);
			mo.setParentId(siteId);
			c.add(mo);
		}
		return c;
	}
}
