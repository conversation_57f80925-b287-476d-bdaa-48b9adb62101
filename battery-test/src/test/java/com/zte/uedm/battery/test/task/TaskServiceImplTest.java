package com.zte.uedm.battery.test.task;

import com.zte.uedm.battery.mapper.TaskBean;
import com.zte.uedm.battery.mapper.TaskDao;
import com.zte.uedm.battery.mapper.TaskState;
import com.zte.uedm.battery.test.refs.DataService;
import com.zte.uedm.battery.test.refs.PointData;
import com.zte.uedm.battery.test.refs.ResourceService;
import com.zte.uedm.common.consts.MocType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.when;

public class TaskServiceImplTest
{
	@InjectMocks
	private TaskServiceImpl taskServiceImpl;
	@Mock
	private TaskDao dao;
	@Mock
	private DataService ds;
	@Mock
	private ResourceService rs;

	private List<TaskBean> taskBeanList = new ArrayList<>();

	@Before
	public void before() throws Exception
	{
		MockitoAnnotations.initMocks(this);
		TaskBean taskBean = new TaskBean();
		taskBean.setState(TaskState.TESTING);
		taskBeanList.add(taskBean);
	}

	@Test
	public void testAddTask() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn("xxx").when(rs).findSubMoId(Mockito.any(), Mockito.any());
			PointData pd = new PointData();
			pd.setVal("x");
			Mockito.doReturn(pd).when(ds).findCurrData(Mockito.any(), Mockito.any());
			TaskBean taskBean = new TaskBean();
			taskBean.setState(TaskState.TESTING);
			taskBean.setSiteId("Site-xxx");
			Mockito.doReturn(null).when(dao).findLastTask(Mockito.any());
			taskServiceImpl.addTask("xxx");
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testAddTask1() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn("xxx").when(rs).findSubMoId(Mockito.any(), Mockito.any());
			PointData pd = new PointData();
			pd.setVal("0");
			Mockito.doReturn(pd).when(ds).findCurrData(Mockito.any(), Mockito.any());
			TaskBean taskBean = new TaskBean();
			taskBean.setState(TaskState.TESTING);
			taskBean.setSiteId("Site-xxx");
			Mockito.doReturn(null).when(dao).findLastTask(Mockito.any());
			List<String> battIds = new ArrayList<>();
			battIds.add("batt");
			Mockito.doReturn(battIds).when(rs).findSubMoIds("xxx", MocType.BATT);
			taskServiceImpl.addTask("xxx");
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testAddTask2() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn("xxx").when(rs).findSubMoId(Mockito.any(), Mockito.any());
			PointData pd = new PointData();
			pd.setVal("0");
			PointData pd1 = new PointData();
			pd1.setVal("100");
			when(ds.findCurrData(Mockito.any(), Mockito.any())).thenReturn(pd).thenReturn(pd1);
			TaskBean taskBean = new TaskBean();
			taskBean.setState(TaskState.TESTING);
			taskBean.setSiteId("Site-xxx");
			Mockito.doReturn(null).when(dao).findLastTask(Mockito.any());
			List<String> battIds = new ArrayList<>();
			battIds.add("batt");
			Mockito.doReturn(battIds).when(rs).findSubMoIds("xxx", MocType.BATT);
			taskServiceImpl.addTask("xxx");
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testAddTask_EX1() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn("xxx").when(rs).findSubMoId(Mockito.any(), Mockito.any());
			PointData pd = new PointData();
			pd.setVal("0");
			PointData pd1 = new PointData();
			pd1.setVal("100");
			when(ds.findCurrData(Mockito.any(), Mockito.any())).thenReturn(pd);
			TaskBean taskBean = new TaskBean();
			taskBean.setState(TaskState.TESTING);
			taskBean.setSiteId("Site-xxx");
			Mockito.doReturn(null).when(dao).findLastTask(Mockito.any());
			List<String> battIds = new ArrayList<>();
			battIds.add("batt");
			Mockito.doReturn(battIds).when(rs).findSubMoIds("xxx", MocType.BATT);
			taskServiceImpl.addTask("xxx");
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testFindTasks1() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn(taskBeanList).when(dao).findTask(Mockito.any());
			taskServiceImpl.findTasks("xxx");
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testFindTasksInExec() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn(taskBeanList).when(dao).findTasksInState(Mockito.any());
			taskServiceImpl.findTasksInExec();
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testUpdTask() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn(taskBeanList).when(dao).findTasksInState(Mockito.any());
			taskServiceImpl.updTask(taskBeanList);
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testFindByCond() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn(taskBeanList).when(dao).findByCond(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
			taskServiceImpl.findByCond(new String[1],new Date[1],new int[1]);
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testFindLastTasks() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doReturn(taskBeanList).when(dao).findLastTasks(Mockito.any());
			taskServiceImpl.findLastTasks(new String[1]);
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void testClear() throws Exception
	{
		Exception exception = null;
		Date d = new Date();
		try
		{
			Mockito.doNothing().when(dao).deleteTask(Mockito.any());
			taskServiceImpl.clear();
		} catch (Exception e) {
			exception = new Exception();
		}
		Assert.assertNotNull(d);
	}

	@Test
	public void forCiTest() throws Exception
	{
		try
		{
			taskServiceImpl.forIndex("1");
			taskServiceImpl.forIndex("2");
			taskServiceImpl.forIndex("3");
			taskServiceImpl.forIndex("4");
			taskServiceImpl.forIndex("5");
			taskServiceImpl.forIndex("6");
			taskServiceImpl.forIndex("7");
			taskServiceImpl.forIndex("8");
		}
		catch (Exception e)
		{
			Assert.assertNotNull(e);
		}
	}
}