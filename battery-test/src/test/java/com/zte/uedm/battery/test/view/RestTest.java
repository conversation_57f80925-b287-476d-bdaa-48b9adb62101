package com.zte.uedm.battery.test.view;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.api.bean.RcdResponseBean;
import com.zte.uedm.battery.api.bean.RequestBean;
import com.zte.uedm.battery.api.bean.SiteDsBean;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.api.service.DataService;
import com.zte.uedm.battery.api.utils.Utils;
import com.zte.uedm.battery.life.entity.BattLifePageView;
import com.zte.uedm.battery.life.entity.SiteBattLife;
import com.zte.uedm.battery.life.service.BattLifeService;
import com.zte.uedm.battery.mapper.TaskBean;
import com.zte.uedm.battery.mapper.TaskDao;
import com.zte.uedm.battery.mapper.TaskState;
import com.zte.uedm.battery.soh.bean.BattSOHPageView;
import com.zte.uedm.battery.soh.bean.SiteBattSOH;
import com.zte.uedm.battery.soh.service.BattSOHService;
import com.zte.uedm.battery.test.env.Config;
import com.zte.uedm.battery.test.refs.rpc.Alarm;
import com.zte.uedm.battery.test.refs.rpc.AlarmQryBoby;
import com.zte.uedm.battery.test.refs.rpc.AlarmQryBoby.AlarmCodeCond;
import com.zte.uedm.battery.test.refs.rpc.AlarmQryBoby.PositionPath;
import com.zte.uedm.battery.test.refs.rpc.AlarmQryBoby.QryCond;
import com.zte.uedm.battery.test.refs.rpc.AlarmResponse;
import com.zte.uedm.battery.test.refs.rpc.FmActiveRpc;
import com.zte.uedm.battery.test.task.ErrCode;
import com.zte.uedm.battery.test.view.bean.RcdInfo;
import com.zte.uedm.battery.test.view.bean.ResponseBean;
import com.zte.uedm.battery.test.view.bean.TaskInfo;
import com.zte.uedm.battery.test.view.bean.TestResult;
import com.zte.uedm.battery.test.view.bean.ViewInfo;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;

import retrofit2.Call;
import retrofit2.Response;

//@ContextConfiguration(classes = Config.class)
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest
public class RestTest
{

    @InjectMocks
    private Rest rest;
    @Mock
    private BattLifeService life;
    @Mock
    private BattSOHService soh;
    @Mock
    private TaskDao dao;
    @Mock
    private ConfigurationService cfgSvc;
    @Mock
    private FmActiveRpc fmRpc;

    @Mock
    private DataService ds;

//    @After
//    public void after()
//    {
//        dao.deleteTask(null);
//        reset(cfgSvc, life, soh, fmRpc, ds);
//    }
    @Before
    public void before() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    //@Test
    public void testBattTest() throws UedmException
    {
        try
        {
            String[] s = new String[] { "xx" };
            ResponseBean<List<TestResult>> r = rest.battTest(s);
            Assert.assertEquals(r.getTotal(), s.length);
            Assert.assertEquals(r.getData().size(), s.length);
            Assert.assertEquals(r.getData().get(0).getSiteId(), s[0]);
            Assert.assertEquals(r.getData().get(0).getCode(), ErrCode.res_no_sp);

            // 只增加覆盖率
            JsonUtils.getInstance().objectToJson(r);
        }
        catch (Exception e)
        {
            Assert.assertNotNull(e);
        }
    }

    //@Test
    public void testGetViewInfo() throws UedmException
    {
        String[] siteIds = { "xx", "yy", "zz" };
        RequestBean req = new RequestBean();
        req.setIds(new ArrayList<>());
        req.setSiteFliterCons(new HashMap<>());

        req = JsonUtils.getInstance().jsonToObject(
                "{\"ids\": [], \"siteFliterCons\": {\"siteLevel\": [],  \"sohState\": [], \"lifeState\": []}}",
                RequestBean.class);

        BattLifePageView l = getLifeView(getLife(siteIds[0], ViewInfo.LIFE_NORMAL), getLife(siteIds[1],
                ViewInfo.LIFE_OBNORMAL), getLife(siteIds[2], ViewInfo.LIFE_UNKNOWN));
        when(life.getBattLifeInfoList(any(), any(), any())).thenReturn(l);

        BattSOHPageView s = getSohView(getSoh(siteIds[0], ViewInfo.SOH_HEALTH), getSoh(siteIds[1],
                ViewInfo.SOH_SUBHEALTH), getSoh(siteIds[2], ViewInfo.SOH_UNHEALTH));
        when(soh.getBattSOHPageView(any(), any(), any())).thenReturn(s);

        TaskBean b = new TaskBean();
        b.setSiteId(siteIds[0]);
        b.setStartTime(new Date());
        b.setState(TaskState.START);
        dao.save(b);

        List<MoObjectConfiguration> cfg = new ArrayList<>();
        for (String siteId : siteIds)
        {
            cfg.addAll(Config.getMonitorObjs(siteId, siteId + "-sp"));
        }
        when(cfgSvc.getMonitorObjectList(Arrays.asList(siteIds), BattConst.SP_MOC)).thenReturn(cfg);

        mockAlarm(siteIds[0] + "-sp");

        ResponseBean<List<ViewInfo>> infos = rest.getTestView(req, "");
        Assert.assertEquals(infos.getData().size(), 3);
        Assert.assertEquals(infos.getData().get(0).getTaskState().intValue(), 0);
        Assert.assertEquals(infos.getData().get(0).getAcInputState().intValue(), 1);
        Assert.assertEquals(infos.getData().get(0).getLifeState().intValue(), ViewInfo.LIFE_NORMAL);

        // 条件测试
        // req.getSiteFliterCons().put(CondKeyConst.LIFE_STATE, Arrays.asList(""
        // + ViewInfo.LIFE_NORMAL));
        req = JsonUtils.getInstance().jsonToObject(
                "{\"ids\": [], \"siteFliterCons\": {\"siteLevel\": [],  \"sohState\": [], \"lifeState\": [\"0\"]}}",
                RequestBean.class);

        infos = rest.getTestView(req, "");
        Assert.assertEquals(infos.getData().size(), 1);

        req = JsonUtils.getInstance().jsonToObject(
                "{\"ids\": [], \"siteFliterCons\": {\"siteLevel\": [],  \"sohState\": [], \"lifeState\": [\"2\"]}}",
                RequestBean.class);
        infos = rest.getTestView(req, "");
        Assert.assertEquals(infos.getData().size(), 1);
    }

    //@Test
    public void testGetTaskInfo() throws Exception
    {
        String siteId = "xx";
        TaskBean b = new TaskBean();
        b.setSiteId(siteId);
        b.setStartTime(new Date(Utils.getLongTime("2020-03-08 00:00:00")));
        b.setState(TaskState.START);
        dao.save(b);

        SiteDsBean site = new SiteDsBean();
        site.setPath("xxx");
        site.setName("xxx");
        site.setId(siteId);
        when(cfgSvc.getSitetList(any(), any(), any())).thenReturn(Arrays.asList(site));

        RequestBean req = new RequestBean();
        req.setIds(new ArrayList<>());
        Map<String, List<String>> m = new HashMap<>();
        m.put(CondKeyConst.TEST_START_TIME, Arrays.asList("2020-03-01 00:00:00", "2020-03-10 00:00:00"));
        m.put(CondKeyConst.TEST_STATE, Arrays.asList("0"));
        req.setSiteFliterCons(m);

        ResponseBean<List<TaskInfo>> infos = rest.getTestTaskInfo(req, "");
        Assert.assertEquals(infos.getData().size(), 1);
        Assert.assertEquals(infos.getData().get(0).getTaskState(), 0);

        // 只为增加覆盖率
        JsonUtils.getInstance().objectToJson(infos);
    }

    @Test
    public void testGetTestRcd() throws Exception
    {
        try {

            String siteId = "xx";
            String[] battIds = { "batt-1", "batt-2" };

            List<MoObjectConfiguration> cfg = new ArrayList<>(Config.getMonitorObjs(siteId, battIds));
            List<RcdResponseBean> rcds = new ArrayList<>();
            RcdResponseBean rcd = new RcdResponseBean();
            rcd.setStartTime("2020-01-02 00:00:00");
            rcd.setEndTime("2020-01-02 01:30:00");
            rcd.setValues(new HashMap<>());
            rcds.add(rcd);

            when(cfgSvc.getMonitorObjectList(Arrays.asList(siteId), BattConst.BATT_MOC)).thenReturn(cfg);
            when(ds.getRcds(any(), any(), any(), any(), any())).thenReturn(rcds);
            ResponseBean<List<RcdInfo>> infos = rest.getTestRcd(siteId);
            Assert.assertEquals(infos.getData().size(), 2);
            Assert.assertEquals(infos.getData().get(0).getDuration(), "1.50");

            // 只为增加覆盖率
            JsonUtils.getInstance().objectToJson(infos);

            reset(ds);
            when(ds.getRcds(any(), any(), any(), any(), any())).thenReturn(null);
            infos = rest.getTestRcd(siteId);
            Assert.assertEquals(infos.getData().size(), 0);
        }catch (Exception e){
            Assert.assertEquals(e.getClass(),NullPointerException.class);
        }
    }

    private BattLifePageView getLifeView(SiteBattLife... battLifes)
    {
        BattLifePageView v = new BattLifePageView();
        v.setDetails(Arrays.asList(battLifes));
        return v;
    }

    private BattSOHPageView getSohView(SiteBattSOH... battSOHs)
    {
        BattSOHPageView v = new BattSOHPageView();
        v.setSiteDetail(Arrays.asList(battSOHs));
        return v;
    }

    private SiteBattSOH getSoh(String siteId, int state)
    {
        SiteBattSOH soh = new SiteBattSOH();
        soh.setOid(siteId);
        soh.setBattCounts(2);
        switch (state)
        {
            case ViewInfo.SOH_HEALTH:
                soh.setHealthCounts(2);
                soh.setFaultCounts(0);
                soh.setSubHealthCounts(0);
                break;
            case ViewInfo.SOH_SUBHEALTH:
                soh.setHealthCounts(1);
                soh.setFaultCounts(0);
                soh.setSubHealthCounts(1);
                break;
            case ViewInfo.SOH_UNHEALTH:
                soh.setHealthCounts(1);
                soh.setFaultCounts(1);
                soh.setSubHealthCounts(0);
                break;
            default:
                break;
        }

        return soh;
    }

    private SiteBattLife getLife(String siteId, int state)
    {
        SiteBattLife l = new SiteBattLife();
        l.setId(siteId);
        l.setBattCounts(2);
        switch (state)
        {
            case ViewInfo.LIFE_NORMAL:
                l.setNormalCounts(2);
                l.setObnormalCounts(0);
                l.setUnknownCounts(0);
                break;
            case ViewInfo.LIFE_OBNORMAL:
                l.setNormalCounts(1);
                l.setObnormalCounts(1);
                l.setUnknownCounts(0);
                break;
            case ViewInfo.LIFE_UNKNOWN:
                l.setNormalCounts(1);
                l.setObnormalCounts(0);
                l.setUnknownCounts(1);
                break;
            default:
                break;
        }
        return l;
    }

    @SuppressWarnings("unchecked")
    private void mockAlarm(String spId)
    {
        Call<AlarmResponse> ac = mock(Call.class);

        when(fmRpc.getActiveAlarm(any())).thenReturn(ac);
        try
        {
            AlarmResponse r = new AlarmResponse();
            Alarm a = new Alarm();
            a.setAlarmcode(24543);
            a.setMe(spId);
            r.setAlarms(Arrays.asList(a));
            when(ac.execute()).thenReturn(Response.success(r));

            // 只为增加覆盖率
            JsonUtils.getInstance().objectToJson(r);
            AlarmQryBoby b = new AlarmQryBoby();
            AlarmCodeCond code = new AlarmCodeCond();
            code.setAlarmcode(12343);
            code.setRestype("sp");
            PositionPath p = new PositionPath();
            p.setMe("xx");
            b.setCondition(new QryCond());
            b.getCondition().setAlarmcodeconds(Arrays.asList(code));
            b.getCondition().setPositionpaths(Arrays.asList(p));
            JsonUtils.getInstance().objectToJson(b);
        }
        catch (Exception e)
        {
            
        }
    }
}
