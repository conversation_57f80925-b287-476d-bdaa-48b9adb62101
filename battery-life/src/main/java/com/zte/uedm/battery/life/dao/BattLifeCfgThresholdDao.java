package com.zte.uedm.battery.life.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.zte.uedm.battery.life.entity.BattLifeCfgThres;


@CacheNamespace
@Mapper
public interface BattLifeCfgThresholdDao {
	@Insert("insert into batt_life_cfg_threshold(mo_id, expected_life,cycLifeOfPct_thres) "
			+ "values (#{moId}, #{expectedLife}, #{cycLifeOfPctThres})")
	@Options(useGeneratedKeys = true, keyProperty = "id")
	void save(BattLifeCfgThres b);
	@Update("update batt_life_cfg_threshold set expected_life=#{expectedLife} ,cycLifeOfPct_thres=#{cycLifeOfPctThres} where id = #{id}")
	void update(BattLifeCfgThres b);
	
	@Delete("delete from batt_life_cfg_threshold where mo_id = #{moId}")
	void deleteByMoId(String moId);
	@Delete("delete from batt_life_cfg_threshold")
	void deleteAll();
	@Select("select * from batt_life_cfg_threshold")
	List<BattLifeCfgThres> findAll();

	@Select("select * from batt_life_cfg_threshold where mo_id = #{moId}")
	List<BattLifeCfgThres> findBattLifeCfgThresholdByMoId(String moId);
	@Select("select * from batt_life_cfg_threshold where id = #{id}")
	BattLifeCfgThres findBattLifeCfgThresholdById(Integer id);
}
