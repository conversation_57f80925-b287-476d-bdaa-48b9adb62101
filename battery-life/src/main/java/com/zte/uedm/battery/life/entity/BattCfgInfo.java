package com.zte.uedm.battery.life.entity;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(description = "电池配置信息")
public class BattCfgInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5528392002053540334L;
    @ApiModelProperty(value = "配置标识")
	private Integer id;
    @ApiModelProperty(value = "监控对象标识")
    private String moId;
    /***
     * 出厂日期
     */
    @ApiModelProperty(value = "出厂日期")
    private Date birthday;
    /**
     * 启用日期
     */
    @ApiModelProperty(value = "启用日期")
    private Date openingDate;
    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 额定容量
     */
    @ApiModelProperty(value = "额定容量")
    private Integer ratedCapacity;
    /**
     * 电极材料
     */
    @ApiModelProperty(value = "电极材料")
    private String electrodeMaterial;
    /**
     * 不存库，给界面展示用
     */
    @ApiModelProperty(value = "定位")
    private String location;
    /**
     * 不存库，给界面展示用
     */
    @ApiModelProperty(value = "名称")
    private String name;
	private void writeObject(java.io.ObjectOutputStream out) throws IOException {
		out.defaultWriteObject();
	}

	private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();
	}
}
