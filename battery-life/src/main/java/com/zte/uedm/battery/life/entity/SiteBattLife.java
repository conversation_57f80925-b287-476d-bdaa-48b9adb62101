package com.zte.uedm.battery.life.entity;


import java.util.List;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@ToString
public class SiteBattLife {
	//站点id
	private String id;
	//站点位置
	private String location;
	//站点名称
	private String siteName;
	//供电场景
	private String powerSupplyScene;
	private String siteGrade;

	//电池组数量
	private Integer battCounts;
	private Integer obnormalCounts;
	private Integer normalCounts;
	private Integer unknownCounts;
	private List<BattLifeDetail> details;
}
