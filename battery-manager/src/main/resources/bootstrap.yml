debug: false
dexcloud:
  serviceinfo:
    serviceName: battery
  base:
    microservice:
      name: battery-manager
      version: v1
      organization: uedm
      metaInfo:
        scope: test
  discovery:
    msb:
      enabled: true
      server:
        address: ${msb_svrIp}
        port: ${msb_svrPort}
        namespace: ${msb_nameSpace}
      client:
        cache:
          enabled: false
        registry:
          enabled: true
          items:
            - serviceName: battery-manager
              version: v1
              url: /api/battery-manager/v1
              protocol: REST
              visualRange: 1|0
              namespace: uedm
              nodes:
                - port: 29117
  redismult:
    redisson:
      enabled: true
      items:
        - id: redis
          host: ${redis_host}                       #【单机、集群模式】redis服务器的地址。在PaaS环境中，redis_host会自动替换为环境变量OPENPALETTE_REDIS_ADDRESS的值
          port: ${redis_port}                       #【单机、集群模式】redis服务器的端口。在PaaS环境中，redis_port会自动替换为环境变量OPENPALETTE_REDIS_PORT的值
          password: ${redis_password}               #【所有模式】redis服务器的密码。在PaaS环境中，redis_password会自动替换为环境变量OPENPALETTE_REDIS_PASSWORD的值，并自动解密
          sentinelHost: ${redis_sentinel_host:}       #【HA模式】redis sentinel的地址。在PaaS环境中，redis_sentinel_host会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_ADDRESS的值
          sentinelPort: ${redis_sentinel_port:}       #【HA模式】redis sentinel的端口。在PaaS环境中，redis_sentinel_port会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_PORT的值
          masterName: ${redis_sentinel_mastername:}         #【HA模式】redis master节点的名称。在PaaS环境中，redis_sentinel_mastername会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_MASTERNAME的值
          # 下列参数需要从原！！！dexcloud.redis.redisson！！！中参考
          poolSize: 32
          poolMinIdleSize: 4
          slavePoolSize: 2
          slavePoolMinIdleSize: 1
          dnsMonitoringInterval: 5000
          readMode: MASTER
          scanInterval: 1000
          timeout: 3000
          connectTimeout: 10000
          idleConnectionTimeout: 10000
          retryAttempts: 3
          retryInterval: 1500
        - id: redis1
          host: ${OPENPALETTE_REDIS_ADDRESS_1}                       #【单机、集群模式】redis服务器的地址。在PaaS环境中，redis_host会自动替换为环境变量OPENPALETTE_REDIS_ADDRESS_1的值
          port: ${OPENPALETTE_REDIS_PORT_1}                       #【单机、集群模式】redis服务器的端口。在PaaS环境中，redis_port会自动替换为环境变量OPENPALETTE_REDIS_PORT_1的值
          password: ${OPENPALETTE_REDIS_PASSWORD_1}               #【所有模式】redis服务器的密码。在PaaS环境中，redis_password会自动替换为环境变量OPENPALETTE_REDIS_PASSWORD_1的值，并自动解密
          sentinelHost: ${OPENPALETTE_REDIS_SENTINEL_ADDRESS_1:}       #【HA模式】redis sentinel的地址。在PaaS环境中，redis_sentinel_host会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_ADDRESS_1的值
          sentinelPort: ${OPENPALETTE_REDIS_SENTINEL_PORT_1:}       #【HA模式】redis sentinel的端口。在PaaS环境中，redis_sentinel_port会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_PORT_1的值
          masterName: ${OPENPALETTE_REDIS_SENTINEL_MASTERNAME_1:}         #【HA模式】redis master节点的名称。在PaaS环境中，redis_sentinel_mastername会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_MASTERNAME_1的值
          confName: OPENPALETTE_REDIS_PASSWORD_1
          # 下列参数需要从原！！！dexcloud.redis.redisson！！！中参考
          poolSize: 32
          poolMinIdleSize: 4
          slavePoolSize: 2
          slavePoolMinIdleSize: 1
          dnsMonitoringInterval: 5000
          readMode: MASTER
          scanInterval: 1000
          timeout: 3000
          connectTimeout: 10000
          idleConnectionTimeout: 10000
          retryAttempts: 3
          retryInterval: 1500
  web:
    swagger:
      beanConfig:
        title: Measure Point Services API Documentation
        version: '1.0'
server:
  port: 29117
  jetty:
    acceptors: 1
    selectors: 1
    threadpool:
      maxThreads: 4
      minThreads: 4

kafkaclientconf:
  zkServers: ${kafka_zk_addresses}:${kafka_zk_port}
  bootstrapServers: ${kafka_brokers}:${kafka_port}
  kafkaServiceName: kafka
  kafkaServiceVersion: v1
  consumerConf:
    properties:
      value.deserializer: org.apache.kafka.common.serialization.StringDeserializer
  producerConf:
    properties:
      key.serializer: org.apache.kafka.common.serialization.StringSerializer
      value.serializer: org.apache.kafka.common.serialization.StringSerializer
  zkClientConf:
    sessionTimeout: 15000
    connectionTimeout: 10000
    zkSerializer: org.dexcloud.springboot.kafka.serializer.StringZkSerializer
  zkUtilsConf:
    secure: false
mybatis:
  configuration:
    map-underscore-to-camel-case: true
uedm:
  timer:
    cron_day: 0 10 0 * * ?
  db:
    init:
      forced-install: false
      module: battery
      current-version: v161829
      base-version: v1
  thread:
    pool:
      core-pool-size: 50
      max-pool-size: 200
      queue-capacity: 100
      keep-alive-time: 60
      name-prefix: battery-executor-
  scheduler:
    poolsize: 20
  cache:
    caches:
      - name: CACHE_NAME_REAL_GROUP      # 所有分组的缓存
        maxSize: 1000000
      - name: CACHE_NAME_BATT_PACK      # 所有电池组的缓存
        maxSize: 1000000
      - name: CACHE_NAME_SITE       # 站点缓存
        maxSize: 1000000
  kafka:
    consumer:
      topics:
        - uedm_start_notify
        - uedm_config_change
        - uedm_south_bcua_data
        - uedm_zenap-licensecenter-lcs-change
        - imop_zenap-licensecenter-lcs-change
        - uedm_recollect_data
        - uedm_configuration_change
  schedule:
    deviceState_timeout: 30
    pv_energy_storage_interval: 60
  battery:
    power-algorithm:
      coefficient: 1.5
      total_voltage: 48
      discharge_depth: 0.8
spring:
  datasource:
    url: jdbc:postgresql://${rdb_ip}:${rdb_port}/${rdb_dbname}
    username: ${rdb_user}
    password: ${rdb_password}
    driver-class-name: ${rdb_driver}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximumPoolSize: 10
  jersey:
    application-path: /api/battery-manager/v1


postgresql:
  ip: ${rdb_ip}
  port: ${rdb_port}
  db-name: ${rdb_dbname}
  username: ${rdb_user}
  password: ${rdb_password}