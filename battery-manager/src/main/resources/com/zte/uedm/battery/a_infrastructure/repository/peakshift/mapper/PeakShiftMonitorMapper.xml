<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.PeakShiftMonitorMapper">

    <select id="getDeviceAccumulatedKwhGain" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.DeviceAccumulatedKwhGainPo">
        select monitor_device_id,
        (sum(gain_discharge_delta::numeric) - sum(gain_charge_delta::numeric))::numeric(20,2)::text as total_benefit,
        sum(charge_delta::numeric)::numeric(20,2)::text as total_charge,
        sum(discharge_delta::numeric)::numeric(20,2)::text as total_discharge
        from power_system_kwh_gain
        where 1=1
        <if test="beginTime != null and beginTime != ''">
            and date_str &gt;= to_char(#{beginTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_str &lt;= to_char(#{endTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="deviceId != null and deviceId.size() > 0">
            and monitor_device_id in
            <foreach collection="deviceId" item="id" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>
        group by monitor_device_id
    </select>

    <select id="getDeviceStrategyTypeKwhGain" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.DeviceAccumulatedKwhGainPo">
        select monitor_device_id,strategy_type::text,
        sum(charge_delta::numeric)::numeric(20,2)::text as total_charge,
        sum(discharge_delta::numeric)::numeric(20,2)::text as total_discharge
        from power_system_kwh_gain
        where 1=1
        <if test="beginTime != null and beginTime != ''">
            and date_str &gt;= to_char(#{beginTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_str &lt;= to_char(#{endTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="deviceId != null and deviceId.size() > 0">
            and monitor_device_id in
            <foreach collection="deviceId" item="id" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>

        group by monitor_device_id,strategy_type
    </select>

    <select id="getDeviceDailyKwhGain" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.DeviceAccumulatedKwhGainPo">

        select monitor_device_id,date_str,
        (sum(gain_discharge_delta::numeric) - sum(gain_charge_delta::numeric))::numeric(20,2)::text as total_benefit,
        sum(charge_delta::numeric)::numeric(20,2)::text as total_charge,
        sum(discharge_delta::numeric)::numeric(20,2)::text as total_discharge
        from power_system_kwh_gain
        where 1=1
        <if test="beginTime != null and beginTime != ''">
            and date_str &gt;= to_char(#{beginTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_str &lt;= to_char(#{endTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="deviceId != null and deviceId.size() > 0">
            and monitor_device_id in
            <foreach collection="deviceId" item="id" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>
        group by monitor_device_id,date_str
    </select>

    <select id="getDeviceDailyStrategyTypeKwhGain" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.DeviceAccumulatedKwhGainPo">

        select monitor_device_id,date_str,strategy_type::text,
        sum(charge_delta::numeric)::numeric(20,2)::text as total_charge,
        sum(discharge_delta::numeric)::numeric(20,2)::text as total_discharge
        from power_system_kwh_gain
        where 1=1
        <if test="beginTime != null and beginTime != ''">
            and date_str &gt;= to_char(#{beginTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_str &lt;= to_char(#{endTime}::timestamp,'yyyy-mm-dd')
        </if>
        <if test="deviceId != null and deviceId.size() > 0">
            and monitor_device_id in
            <foreach collection="deviceId" item="id" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>
        group by monitor_device_id,date_str,strategy_type
    </select>
</mapper>
