<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BatteryManagerCfgMapper">
    <insert id="insert">
        <if test="list != null and list.size()>0">
            INSERT INTO battery_manager_cfg (mo_oid, cfg_type, cfg_value, cfg_value_unit, gmt_create, gmt_modified) VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.moOid}, #{bean.cfgType}, #{bean.cfgValue}, #{bean.cfgValueUnit}, #{bean.gmtCreate},
                 #{bean.gmtModified})
            </foreach>
        </if>
    </insert>

    <delete id="delete">
        delete from battery_manager_cfg where 1 = 1
        <if test="moOid != null and moOid !=''">
            and mo_oid = #{moOid}
        </if>
        <if test="cfgType != null and cfgType !=''">
            and cfg_type = #{cfgType}
        </if>
    </delete>

    <update id="update">
        update battery_manager_cfg set cfg_value = #{cfgValue},
        gmt_modified = #{gmtModified}
        where mo_oid = #{moOid} and cfg_type = #{cfgType}
   </update>

    <select id="select" resultType="com.zte.uedm.battery.bean.BatteryManagerCfgBean">
        select id, mo_oid, cfg_type, cfg_value, cfg_value_unit, gmt_modified, gmt_create from battery_manager_cfg where 1 = 1
        <if test="id != null and id !=''">
            and id = #{id}
        </if>
        <if test="moOid != null and moOid !=''">
            and mo_oid = #{moOid}
        </if>
        <if test="cfgType != null and cfgType !=''">
            and cfg_type = #{cfgType}
        </if>
    </select>
</mapper>