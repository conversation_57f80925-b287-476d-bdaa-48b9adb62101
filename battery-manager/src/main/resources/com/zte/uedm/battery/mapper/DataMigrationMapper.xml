<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.DataMigrationMapper">

    <update id="batchUpdateAllTables">
        <foreach collection="list" item="item" separator=";">
            UPDATE batt_backup_power_eval_d
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_backup_power_eval_m
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_health_status_eval
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_life_eval_d
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_life_eval_m
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_risk_eval
            SET path_ids = #{item.pathId}, path_names = #{item.pathName}
            WHERE batt_id = #{item.id};

            UPDATE battery_base_info
            SET path_id = #{item.pathId}, path_name = #{item.pathName}
            WHERE id = #{item.id};

            UPDATE batt_life_specific_config
            SET batt_path = LEFT(#{item.pathName},
            LENGTH(#{item.pathName}) -
            POSITION('/' IN REVERSE(#{item.pathName}))
            )
            WHERE batt_id = #{item.id};
        </foreach>
    </update>

</mapper>