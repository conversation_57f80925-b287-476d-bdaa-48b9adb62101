<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BatteryChargeDischargeHistoryR321Mapper">

    <select id="selectAll" resultType="com.zte.uedm.battery.bean.BatteryChargeDischargeHistoryBean">
        SELECT * FROM battery_charge_discharge_history_r321 where device_id = #{deviceId} and battery_seq = #{batterySeq}
    </select>

    <insert id="batchInsertBatteryChargeDischargeHistory">
        INSERT INTO battery_charge_discharge_history_r321
        (device_id, battery_seq, charge_value, discharge_value, inc_charge_value,
        inc_discharge_value, record_time, gmt_create, gmt_modified)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.batterySeq}, #{item.chargeValue}, #{item.dischargeValue},
            #{item.incChargeValue}, #{item.incDischargeValue}, #{item.recordTime}, #{item.gmtCreate}, #{item.gmtModified})
        </foreach>
        ON CONFLICT (battery_seq, record_time) DO UPDATE
        SET
        device_id = EXCLUDED.device_id,
        charge_value = EXCLUDED.charge_value,
        discharge_value = EXCLUDED.discharge_value,
        inc_charge_value = EXCLUDED.inc_charge_value,
        inc_discharge_value = EXCLUDED.inc_discharge_value,
        gmt_modified = EXCLUDED.gmt_modified
    </insert>


</mapper>