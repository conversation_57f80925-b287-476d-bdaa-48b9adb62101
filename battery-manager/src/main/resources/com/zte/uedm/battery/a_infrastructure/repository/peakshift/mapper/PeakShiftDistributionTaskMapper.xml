<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.PeakShiftDistributionTaskMapper">

    <select id="selectByCondition" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskPo">
        select a.id,a.name,a.description,a.creator,a.gmt_create,a.updater,a.gmt_modified,
        a.status, a.effective_date, a.expiration_date, a.effective_status, a.template_strategy_id, a.device_type
        deviceType,
        (case when c.all_detail is null or c.all_detail=0 then null
        else round(coalesce(b.finished_detail,0)*100.0/c.all_detail)||'%' end) progress
        from
        (select d.id, d.name,  d.description, d.creator, d.gmt_create, d.updater, d.gmt_modified,
        d.status ,d.effective_date ,d.expiration_date,d.template_strategy_id,e.device_type,
        (case when to_char(now(),'yyyy-mm-dd hh24:mi:ss') > d.expiration_date then 'expired'
        when to_char(now() + interval '7 day','yyyy-mm-dd hh24:mi:ss') > d.expiration_date then 'nearToExpired'
        else 'normal' end ) effective_status
        from peak_shift_task d
        left join template_strategy e on e.id = d.template_strategy_id
        where 1=1
        <if test="name != null and name != ''">
            and d.name ilike concat('%',#{name},'%')
        </if>
        <if test="creator != null and creator != ''">
            and d.creator ilike concat('%',#{creator},'%')
        </if>
        <if test="description != null and description != ''">
            and d.description ilike concat('%',#{description},'%')
        </if>
        <if test="effectiveDateStart != null and effectiveDateStart != ''">
            AND d.effective_date <![CDATA[>=]]> #{effectiveDateStart}
        </if>
        <if test="effectiveDateEnd != null and effectiveDateEnd != ''">
            AND d.effective_date <![CDATA[<=]]> #{effectiveDateEnd}
        </if>
        <if test="expirationDateStart != null and expirationDateStart != ''">
            AND d.expiration_date <![CDATA[>=]]> #{expirationDateStart}
        </if>
        <if test="expirationDateEnd != null and expirationDateEnd != ''">
            AND d.expiration_date <![CDATA[<=]]> #{expirationDateEnd}
        </if>
        <if test="status != null and status.size() > 0">
            and d.status in
            <foreach collection="status" item="iterm" open="(" separator="," close=")">
                #{iterm}
            </foreach>
        </if>
        <if test="deviceType != null and deviceType != ''">
            AND e.device_type ilike concat('%',#{deviceType},'%')
        </if>
        ) a
        left join
        (
        select task_id,count(1) finished_detail from peak_shift_task_detail
        where (status = 'success' or status = 'fail')
        group by task_id
        ) b
        on a.id = b.task_id
        left join
        (
        select task_id,count(1) all_detail from peak_shift_task_detail
        group by task_id
        ) c
        on a.id = c.task_id
        <choose>
            <when test="sortBy != null and sortBy != ''">
                <choose>
                    <when test="order != null and order != ''">
                        order by ${sortBy} ${order},gmt_create desc
                    </when>
                    <otherwise>
                        order by ${sortBy} desc,gmt_create desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by gmt_create desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertPeakShiftTask">
        INSERT INTO peak_shift_task
        (id, name, description, creator, gmt_create, updater, gmt_modified, status, effective_date,
        expiration_date,template_strategy_id,file_id)
        VALUES (#{bean.id}, #{bean.name}, #{bean.description}, #{bean.creator}, #{bean.gmtCreate},
        #{bean.updater}, #{bean.gmtModified}, #{bean.status}, #{bean.effectiveDate},
        #{bean.expirationDate},#{bean.templateStrategyId},#{bean.fileId});
    </insert>

    <select id="selectById" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskPo">
        select a.id, a.name,  a.description,a.creator, a.gmt_create,
        a.updater,a.gmt_modified,a.status,a.effective_date,a.expiration_date,b.device_type
        deviceType,a.template_strategy_id as templateStrategyId
        from peak_shift_task a
        LEFT JOIN template_strategy b on b.id = a.template_strategy_id
        where a.id =#{id}
    </select>

    <update id="updatePeakShiftTask">
        update peak_shift_task set
        name = #{bean.name},
        file_id = #{bean.fileId},
        description = #{bean.description},
        creator = #{bean.creator},
        gmt_create = #{bean.gmtCreate},
        updater = #{bean.updater},
        gmt_modified = #{bean.gmtModified},
        status = #{bean.status},
        effective_date = #{bean.effectiveDate},
        expiration_date = #{bean.expirationDate},
        template_strategy_id = #{bean.templateStrategyId}
        where id = #{bean.id}
    </update>

    <delete id="deleteById">
        delete from peak_shift_task where id = #{id}
    </delete>

    <select id="selectDuplicateNames" resultType="java.lang.String">
        select name from peak_shift_task where name = #{name}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <select id="checkTaskByname" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskPo">
        select * from peak_shift_task where name = #{name}
    </select>

    <update id="updateStatusById">
        update peak_shift_task set status = #{status},gmt_modified = to_char(now(),'yyyy-mm-dd hh24:mi:ss')  where id = #{id}
    </update>

</mapper>