<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BattCfgInfoMapper">

    <select id="findBattCfgInfoByMoId" resultType="com.zte.uedm.battery.bean.BattCfgInfo">
        select * from batt_cfg_info where mo_id = #{moId}
    </select>

    <select id="findBattCfgInfoByMoIds" resultType="com.zte.uedm.battery.bean.BattCfgInfo">
        <if test="moIds!=null and moIds.size()>0">
            select * from batt_cfg_info where 1=1
            and mo_id in
            <foreach collection="moIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>