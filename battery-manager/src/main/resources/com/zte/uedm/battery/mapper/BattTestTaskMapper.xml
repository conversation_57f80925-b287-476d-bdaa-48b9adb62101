<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BattTestTaskMapper">
    <insert id="insertBeans">
        <if test="list!=null and list.size()>0">
            insert into batt_test_task(id,name,period,status,start_time,remark,internal,creator,gmt_create)
            values
            <foreach collection="list" item="bean" separator=",">
                (
                #{bean.id},#{bean.name},#{bean.period},#{bean.status},#{bean.startTime},#{bean.remark},#{bean.internal},
                #{bean.creator},#{bean.gmtCreate}
                )
            </foreach>
        </if>
    </insert>

    <update id="updateTaskStatusById">
       update batt_test_task set status=#{status},updater=#{updater},gmt_modified=#{gmtModified}  where id=#{id}
    </update>

    <update id="update">
        update batt_test_task set name=#{po.name},period=#{po.period},start_time=#{po.startTime},remark=#{po.remark},updater=#{po.updater},gmt_modified=#{po.gmtModified} where id=#{po.id}
    </update>

    <delete id="deleteByIds">
        <if test="ids!=null and ids.size()>0">
            delete from batt_test_task where id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>


    <select id="selectById" resultType="com.zte.uedm.battery.bean.pojo.BattTestTaskPo">
        select * from batt_test_task where id=#{id}
    </select>
    <select id="selectTestTaskByCondition" resultType="com.zte.uedm.battery.bean.pojo.BattTestTaskPo">
        select a.id, a.name, a.period, a.status, a.start_time, a.remark, a.internal, a.creator, a.updater, a.gmt_create, a.gmt_modified
        from batt_test_task a
        <choose>
            <when test="queryBean.deviceIds != null and queryBean.deviceIds.size()>0">
                right join (select distinct task_id from batt_test_task_devices where id in
                <foreach collection="queryBean.deviceIds" item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
                ) c on a.id=c.task_id
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where 1=1
        <if test="queryBean.name != null and queryBean.name != ''">
            and a.name ilike concat('%',#{queryBean.name},'%')
        </if>
        <if test="queryBean.status != null and queryBean.status.size()>0">
            and a.status in
            <foreach collection="queryBean.status" item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRepeatNameExcludeId" resultType="java.lang.String">
        <if test="name!=null and name !=''">
            select name from batt_test_task where name=#{name}
            <if test="id!=null and id!=''">
                and id != #{id}
            </if>
        </if>
    </select>
    <select id="selectByStatus" resultType="com.zte.uedm.battery.bean.BattTestTaskPeriodBean">
        select id, period, status, start_time, creator from batt_test_task where status=#{status}
    </select>

    <select id="selectStatusByIds" resultType="com.zte.uedm.battery.bean.pojo.BattTestTaskPo">
        select id, status from batt_test_task where 1=1
        <if test="ids!=null and ids.size()>0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectPeriodByTaskId" resultType="com.zte.uedm.battery.bean.pojo.BattTestTaskPo">
        SELECT period FROM public.batt_test_task where
        <if test="taskId != null and taskId !=''">
            id = #{taskId}
        </if>
    </select>

    <select id="selectDeviceIdByCondition" resultType="java.lang.String">
        select c.id
        from batt_test_task a
        inner join batt_test_task_devices c on a.id=c.task_id
        where 1=1
        <if test="bean.name != null and bean.name != ''">
            and a.name ilike concat('%',#{bean.name},'%')
        </if>
        <if test="bean.status != null and bean.status.size()>0">
            and a.status in
            <foreach collection="bean.status" item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="bean.deviceIds != null and bean.deviceIds.size()>0">
            and c.id in
            <foreach collection="bean.deviceIds" item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
    </select>

</mapper>