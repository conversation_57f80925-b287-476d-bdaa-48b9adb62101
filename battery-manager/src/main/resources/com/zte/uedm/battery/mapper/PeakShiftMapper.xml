<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PeakShiftMapper">

    <!--删除拟制中的错峰用电策略表-->
    <delete id="deleteByIdAndStatus">
        delete from peak_shift_strategy where status = 0 and id = #{id}
    </delete>

    <delete id="deleteDetailStrategyById">
        delete from peak_shift_strategy_detail where strategy_id = #{strategyId}
    </delete>

    <!--条件查询错峰用电策略表-->
    <select id="selectByConditions" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyBean">
        select * from peak_shift_strategy where 1 = 1
        <if test="queryBean.name != null and queryBean.name !=''">
            and lower(name) like concat('%',#{queryBean.name},'%')
        </if>
        <if test="queryBean.status != null">
            and status = #{queryBean.status}
        </if>
        <if test="queryBean.startTime != null and queryBean.startTime != ''">
            and effective_time &gt;= #{queryBean.startTime}
        </if>
        <if test="queryBean.endTime != null and queryBean.endTime != ''">
            and expiration_time &lt;= #{queryBean.endTime}
        </if>
        <if test="queryBean.remark != null and queryBean.remark !=''">
            and lower(remark) like concat('%',#{queryBean.remark},'%')
        </if>
        <if test="queryBean.scopeIds != null and queryBean.scopeIds.size()>0">
            and effective_scope like any (array
            <foreach collection="queryBean.scopeIds" item="item" open="[" separator="," close="]">
                concat(#{item},'%')
            </foreach>
            )
        </if>
        order by status asc, effective_time desc nulls last
    </select>

    <!--新增错峰用电策略表-->
    <insert id="insertPeakShiftStrategy">
        INSERT INTO peak_shift_strategy
        (id, name, effective_scope, mode, status, start_date, end_date, remark, gmt_create)
        VALUES (#{bean.id}, #{bean.name}, #{bean.effectiveScope}, #{bean.mode}, #{bean.status}, #{bean.startDate}, #{bean.endDate}, #{bean.remark}, #{bean.gmtCreate});
    </insert>

    <!--新增错峰用电策略详情表-->
    <insert id="insertPeakShiftStrategyDetail">
        <if test="detailBeans != null and detailBeans.size() > 0">
            INSERT INTO peak_shift_strategy_detail
            (id, strategy_id, period_start, period_end, detail, gmt_create)
            VALUES
            <foreach collection="detailBeans" item="detailBean" separator=",">
                (#{detailBean.id}, #{detailBean.strategyId}, #{detailBean.periodStart}, #{detailBean.periodEnd}, #{detailBean.detail}, #{detailBean.gmtCreate})
            </foreach>
        </if>
    </insert>

    <!--编辑错峰用电策略表-->
    <update id="updatePeakShiftStrategy">
        UPDATE peak_shift_strategy
        <set>
            name = #{bean.name},
            effective_scope = #{bean.effectiveScope},
            <if test="bean.mode != null and bean.mode != ''">
                mode = #{bean.mode},
            </if>
            start_date = #{bean.startDate},
            end_date = #{bean.endDate},
            remark = #{bean.remark},
            gmt_modified = #{bean.gmtModified}
        </set>
        <where>
            id = #{bean.id}
        </where>
    </update>

    <!--编辑错峰用电策略详情表-->
    <update id="updatePeakShiftStrategyDetail">
        <if test="detailBeans != null and detailBeans.size() > 0">
            <foreach collection="detailBeans" item="detailBean" open="" separator=";" close="">
                UPDATE peak_shift_strategy_detail
                <set>
                    period_start = #{detailBean.periodStart},
                    period_end = #{detailBean.periodEnd},
                    detail = #{detailBean.detail},
                    gmt_modified = #{detailBean.gmtModified}
                </set>
                <where>
                    id = #{detailBean.id}
                </where>
            </foreach>
        </if>
    </update>

    <!--根据ID查询错峰用电策略表-->
    <select id="selectPeakShiftStrategyById" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyVo">
        SELECT * FROM peak_shift_strategy WHERE id = #{id}
    </select>

    <!--根据策略ID查询错峰用电策略详情表-->
    <select id="selectPeakShiftStrategyDetailByStrategyId" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyDetailBean">
        SELECT * FROM peak_shift_strategy_detail WHERE strategy_id = #{id}
    </select>

    <!--查询某种状态所有错峰用电策略(0：拟制中 1：启用 2：停用)-->
    <select id="selectPeakShiftStrategyByStatus" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyBean">
        SELECT * FROM peak_shift_strategy WHERE status = #{status}
    </select>

    <update id="changeStatus">
        update peak_shift_strategy set status=#{status} where id=#{id}
    </update>

    <!--入生效时-->
    <update id="updateEffectiveTime">
        update peak_shift_strategy set effective_time=#{currentTime} where id=#{id}
    </update>

    <update id="updateExpirationTime">
        update peak_shift_strategy set expiration_time=#{currentTime} where id=#{id}
    </update>

    <select id="duplicateNameCheck" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM peak_shift_strategy where name=#{name}
        <if test="id != null and id !=''">
            and id != #{id}
        </if>
    </select>

    <select id="selectStatusById" resultType="java.lang.Integer">
        select status from peak_shift_strategy where id=#{id}
    </select>
    <select id="selectPeakShiftStrategyBeanById" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyBean">
        SELECT * FROM peak_shift_strategy WHERE id = #{id}
    </select>

    <select id="selectDetailDataByCondition"
            resultType="com.zte.uedm.battery.bean.PeakShiftBatteryDetailDataBean">
        select * from peak_shift_battery_charge_discharge_detail where
        id =#{id}
        and #{startDate} <![CDATA[ <= ]]> end_time
        and #{endDate} <![CDATA[ >= ]]> start_time
    </select>

    <!--删除充放电详情表的时段内数据用作先删后插功能-->
    <delete id="deleteFromDetailByTime">
        delete from peak_shift_battery_charge_discharge_detail where
        gmt_create <![CDATA[ >= ]]> #{startTime} and gmt_create <![CDATA[ <= ]]> #{endTime}
    </delete>

    <!--新增充放电详情表-->
    <insert id="insertPeakShiftBatteryChargeDischargeDetail">
        <if test="beans != null and beans.size() > 0">
            INSERT INTO peak_shift_battery_charge_discharge_detail
            (site_id, batt_id, id, type, start_time, end_time, start_voltage, end_voltage, llvd1, gmt_create)
            VALUES
            <foreach collection="beans" item="bean" separator=",">
                (#{bean.siteId}, #{bean.battId}, #{bean.id}, #{bean.type},
                #{bean.startTime}, #{bean.endTime}, #{bean.startVoltage}, #{bean.endVoltage}, #{bean.llvd1}, #{bean.gmtCreate})
            </foreach>
        </if>
    </insert>

    <select id="selectStategyDetailByDay" resultType="com.zte.uedm.battery.bean.PeakShiftStrategyDetailBean">
        select id,strategy_id,period_start,period_end,detail,gmt_create,gmt_modified from peak_shift_strategy_detail
        where strategy_id in
            (select id from peak_shift_strategy where (status = 1 or status = 2)
            and substring(effective_time,0,11) &lt;= #{date}
            and (case when expiration_time is null then true else substring(expiration_time,0,11) &gt;= #{date} end)
            and substring(#{date},6,12) &gt;= start_date
            and substring(#{date},6,12) &lt;= end_date)
    </select>

    <select id="selectIntervalStrategyDetailByDateAndStrategyId" resultType="com.zte.uedm.battery.bean.IntervalStrategyDetailModeBean">
        select b.id ,b.interval_strategy_id,#{scopeStrategyId} as scope_strategy_id ,a."mode" ,b.period_start ,b.period_end ,b.week_str,b.detail
        from  interval_strategy a,interval_strategy_detail b
        where a.season_strategy_id in (select id from season_strategy ss
                        where scope_strategy_id = #{scopeStrategyId}
                        and effective_time  &lt;= #{date}
                        and (case when expiration_time is null then true else expiration_time &gt;= #{date} end))
        and a.id = b.interval_strategy_id
        and a.start_date &lt;= substring(#{date},6,12)
        and a.end_date  &gt;= substring(#{date},6,12)
        order by a."mode" desc
    </select>

    <!--新增向上融合到sp或者battpack的数据表-->
    <insert id="insertIntoSPorBattpackMergedData">
        <if test="mergedData != null and mergedData.size() > 0">
            INSERT INTO sp_or_battpack_data_after
            (parent_id, id, power, type, start_time, end_time, gmt_create,init_volt,final_volt)
            VALUES
            <foreach collection="mergedData" item="bean" separator=",">
                (#{bean.parentId}, #{bean.id}, #{bean.power}, #{bean.type}, #{bean.startTime},
                #{bean.endTime}, #{bean.gmtCreate},#{bean.initVolt},#{bean.finalVolt})
            </foreach>
        </if>
    </insert>

    <delete id="deleteSPorBattpackMergedData">
        delete from sp_or_battpack_data_after where gmt_create &lt; #{date};
    </delete>

    <insert id="insertIntoStatisticsData">
        WITH upsert AS (
            UPDATE peak_shift_battery_charge_discharge_statistics
                SET charge_value = charge_value + #{statisticsData.chargeValue},
                    discharge_value = discharge_value + #{statisticsData.dischargeValue},
                    gmt_modified = #{statisticsData.gmtModified}
                WHERE id = #{statisticsData.id}
                    AND strategy_type = #{statisticsData.strategyType}
                    AND datestr = #{statisticsData.datestr}
                    AND gmt_create = #{statisticsData.gmtCreate}
                RETURNING *
        )
        INSERT INTO peak_shift_battery_charge_discharge_statistics
        (site_id, id, discharge_times, discharge_duration, discharge_duration_max, charge_times,
         charge_duration, free_duration, llvd1_times, charge_value, discharge_value, strategy_type,
         datestr, strategy_id, strategy_mode, strategy_period_start, strategy_period_end, gmt_create, gmt_modified)
        SELECT
            #{statisticsData.siteId}, #{statisticsData.id}, #{statisticsData.dischargeTimes}, #{statisticsData.dischargeDuration},
            #{statisticsData.dischargeDurationMax}, #{statisticsData.chargeTimes}, #{statisticsData.chargeDuration},
            #{statisticsData.freeDuration}, #{statisticsData.llvd1Times}, #{statisticsData.chargeValue},
            #{statisticsData.dischargeValue}, #{statisticsData.strategyType}, #{statisticsData.datestr},
            #{statisticsData.strategyId}, #{statisticsData.strategyMode}, #{statisticsData.strategyPeriodStart},
            #{statisticsData.strategyPeriodEnd}, #{statisticsData.gmtCreate}, #{statisticsData.gmtModified}
        WHERE NOT EXISTS (SELECT 1 FROM upsert);
    </insert>

    <delete id="deleteStatisticsData">
        delete from peak_shift_battery_charge_discharge_statistics where id = #{id} and datestr = #{date} and
        strategy_type = #{strategyType} and strategy_id = #{strategyId}
        and gmt_create = #{gmtCreate};
    </delete>

    <select id="selectStaticsDataByCondition"
            resultType="com.zte.uedm.battery.bean.PeakShiftStatisticsBean">
        <if test="siteIds != null and siteIds.size()>0">
            select site_id,id,strategy_type,sum(discharge_times) as discharge_times,sum(discharge_duration) as discharge_duration,max(discharge_duration_max) as discharge_duration_max,
            sum(charge_times) as charge_times,sum(charge_duration) as charge_duration,sum(free_duration) as free_duration,sum(llvd1_times) as llvd1_times,
            sum(charge_value) as charge_value,sum(discharge_value) as discharge_value from peak_shift_battery_charge_discharge_statistics where
            #{start} <![CDATA[ <= ]]> datestr
            and #{end} <![CDATA[ >= ]]> datestr
            and site_id in
            <foreach collection="siteIds" item="siteId" open="("
                     separator="," close=")">
                #{siteId}
            </foreach>
            group by site_id,id,strategy_type
        </if>
    </select>

    <!--改变季节策略的状态为生效中-->
    <update id="changeSeasonStrategyStatusInto1">
        UPDATE season_strategy
        <set>
            status = 1,
            gmt_modified = to_char(now(),'yyyy-mm-dd hh24:mi:ss')
        </set>
        <where>
            status = 0 and effective_time = to_char(now(),'yyyy-mm-dd')
        </where>
    </update>

    <!--改变生效中的策略为已结束-->
    <update id="changeSeasonStrategyStatusInto2">
        UPDATE season_strategy
        <set>
            status = 2,
            expiration_time = to_char(now() - interval '1 day','yyyy-mm-dd'),
            gmt_modified = to_char(now(),'yyyy-mm-dd hh24:mi:ss')
        </set>
        <where>
            status = 1 and
            scope_strategy_id in (select scope_strategy_id from season_strategy
                                    where status = 0 and
                                    effective_time = to_char(now(),'yyyy-mm-dd'))
        </where>
    </update>

    <select id="selectScopeStrategyToChange" resultType="com.zte.uedm.battery.bean.ScopeStrategyResponseBean">
        select id,name,logic_group,gmt_create,gmt_modified from scope_strategy
        where id in (select scope_strategy_id from season_strategy
                    where status = 0 and
                    effective_time = to_char(now(),'yyyy-mm-dd'))
    </select>

    <select id="selectEffectiveTimeSeasonStrategyToExpirate" resultType="java.lang.String">
        select effective_time from season_strategy
        where scope_strategy_id = #{scopeStrategyId} and status = 1
    </select>

    <select id="calculateKwhGainByStatisticsDate" resultType="com.zte.uedm.battery.bean.KwhGainBean">
        SELECT A
	.ID AS power_system_id,
	A.site_id,
	A.strategy_type,
	b.price :: TEXT,
	A.datestr AS date_str,
	SUBSTRING ( A.datestr, 1, 7 ) AS month_str,
	SUBSTRING ( A.datestr, 1, 4 ) AS year_str,
	( COALESCE ( C.charge, 0 ) + A.charge_value ) :: NUMERIC ( 20, 2 ) :: TEXT AS charge,
	( COALESCE ( C.discharge, 0 ) + A.discharge_value ) :: NUMERIC ( 20, 2 ) :: TEXT AS discharge,
	( A.charge_value ) :: NUMERIC ( 20, 2 ) :: TEXT AS charge_delta,
	( A.discharge_value ) :: NUMERIC ( 20, 2 ) :: TEXT AS discharge_delta,
	( A.charge_value * b.price ) :: NUMERIC ( 20, 2 ) :: TEXT AS gain_charge_delta,
	( A.discharge_value * b.price ) :: NUMERIC ( 20, 2 ) :: TEXT AS gain_discharge_delta
FROM
	(
	SELECT
		site_id,ID,strategy_type,strategy_id,datestr,
		SUM ( charge_value ) charge_value,
		SUM ( discharge_value ) discharge_value
	FROM
		peak_shift_battery_charge_discharge_statistics
	WHERE
		datestr = #{today}
	GROUP BY
		site_id,
		ID,
		strategy_type,
		strategy_id,
		datestr
	)
	A INNER JOIN (
	SELECT
		E.strategy_type :: TEXT,
		E.price :: NUMERIC,
		scope_strategy_id
	FROM
		(
		SELECT
			price_strategy_id,
			strategy_type,
			price
		FROM
			(
			SELECT P
				.price_interval_strategy_id,
			CASE
					T.tiered
					WHEN 0 THEN
					P.low_price
					WHEN 1 THEN
					P.normal_price
					WHEN 2 THEN
					P.peak_price
					WHEN 3 THEN
					P.peakest_price
				END AS price,
				T.tiered AS strategy_type
			FROM
				tiered_price_strategy
				P CROSS JOIN ( VALUES ( 0 ), ( 1 ), ( 2 ), ( 3 ) ) AS T ( tiered )
			WHERE
				( T.tiered = 0 AND P.low_price IS NOT NULL )
				OR ( T.tiered = 1 AND P.normal_price IS NOT NULL )
				OR ( T.tiered = 2 AND P.peak_price IS NOT NULL )
				OR ( T.tiered = 3 AND P.peakest_price IS NOT NULL )
			ORDER BY
				P.price_interval_strategy_id,
				T.tiered
			) AS prices
                INNER JOIN price_interval_strategy pis ON prices.price_interval_strategy_id = pis.ID
                AND CASE
                        WHEN LENGTH(pis.start_date) = 2 THEN
                            TO_DATE(#{today}, 'YYYY-MM') BETWEEN TO_DATE(start_date || '-' || EXTRACT(YEAR FROM CURRENT_DATE), 'MM-YYYY')AND TO_DATE(end_date || '-' || EXTRACT(YEAR FROM CURRENT_DATE), 'MM-YYYY')
                        WHEN LENGTH(pis.start_date) = 5 THEN
                            TO_DATE(#{today}, 'YYYY-MM-DD') BETWEEN TO_DATE(start_date || '-' || EXTRACT(YEAR FROM CURRENT_DATE), 'MM-DD-YYYY')AND TO_DATE(end_date || '-' || EXTRACT(YEAR FROM CURRENT_DATE), 'MM-DD-YYYY')
                END
		)
		AS E LEFT JOIN price_strategy ps ON E.price_strategy_id = ps.ID
	WHERE
		ps.effective_time &lt;= #{today}
		AND ( ps.expiration_time &gt;= #{today} OR ps.expiration_time IS NULL )
        AND ps.rate_type = 1
    ) b ON A.strategy_type :: TEXT = b.strategy_type :: TEXT
	AND ( A.strategy_id = b.scope_strategy_id OR b.scope_strategy_id IS NULL )
	LEFT JOIN (
	SELECT
		power_system_id,
		charge :: NUMERIC,
		discharge :: NUMERIC,
		strategy_type
	FROM
		power_system_kwh_gain
	WHERE
		date_str = to_char( #{today} :: TIMESTAMP - INTERVAL '1 day', 'yyyy-mm-dd' )
	) C ON A.ID = C.power_system_id
	AND A.strategy_type = C.strategy_type
    </select>


    <select id="selectKwhAccumulatedGainByDate" resultType="com.zte.uedm.battery.bean.KwhGainAccumulatedBean">
        select coalesce (a.monitor_device_id,b.monitor_device_id) as monitor_device_id,
        coalesce (a.power_system_id,b.power_system_id) as power_system_id,
        #{today} as date_str,
        coalesce (a.gain_delta,0)::numeric(20,2)::text as gain_delta,
        (coalesce (b.gain_total,0) + coalesce (a.gain_delta,0))::numeric(20,2)::text as gain_total,
        (coalesce (b.charge,0) + coalesce (a.charge_delta,0))::numeric(20,2)::text as charge,
        (coalesce (b.discharge,0) + coalesce (a.discharge_delta,0))::numeric(20,2)::text as discharge
        from
        ( select power_system_id ,date_str ,monitor_device_id,
        sum(gain_discharge_delta::numeric ) - sum(gain_charge_delta::numeric ) as gain_delta,
        sum(charge_delta::numeric) as charge_delta,
        sum(discharge_delta::numeric) as discharge_delta
        from power_system_kwh_gain
        where date_str = #{today}
        group by power_system_id ,date_str,monitor_device_id
        ) a
        full join
        (select monitor_device_id,power_system_id ,gain_total::numeric ,charge::numeric ,discharge::numeric
        from power_system_daily_gain
        where date_str = to_char(#{today}::timestamp - interval '1 day','yyyy-mm-dd')
        ) b
        on a.power_system_id = b.power_system_id
    </select>

    <delete id="deleteKwhGainBeanByDate">
        delete from power_system_kwh_gain where date_str = #{date};
    </delete>

    <insert id="insertAllKwhGainBean">
        <if test="list != null and list.size() > 0">
            INSERT INTO power_system_kwh_gain
            (monitor_device_id,power_system_id,date_str,month_str,year_str,site_id,charge,discharge,charge_delta,discharge_delta,gain_charge_delta,
            gain_discharge_delta,price,strategy_type)   VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.monitorDeviceId},#{bean.powerSystemId}, #{bean.dateStr}, #{bean.monthStr}, #{bean.yearStr},
                #{bean.siteId},#{bean.charge},#{bean.discharge},#{bean.chargeDelta},#{bean.dischargeDelta},
                #{bean.gainChargeDelta},#{bean.gainDischargeDelta},#{bean.price},#{bean.strategyType})
            </foreach>
        </if>
    </insert>

    <delete id="deleteKwhGainAccumulatedBeanByDate">
        delete from power_system_daily_gain where date_str = #{date};
    </delete>

    <insert id="insertKwhGainAccumulatedBean">
        <if test="list != null and list.size() > 0">
            INSERT INTO power_system_daily_gain
            (monitor_device_id,power_system_id,date_str,gain_total,gain_delta,charge,discharge)   VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.monitorDeviceId},#{bean.powerSystemId}, #{bean.dateStr},
                #{bean.gainTotal},#{bean.gainDelta},#{bean.charge},#{bean.discharge})
            </foreach>
        </if>
    </insert>

    <select id="selectCsuStrategyByDeviceId" resultType="com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo">
        select * from peak_shift_csu5_strategy where device_id = #{deviceId} order by version desc limit 1;
    </select>

    <select id="selectCsuAllStrategyByDeviceId" resultType="com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo">
        select * from peak_shift_csu5_strategy where device_id = #{deviceId};
    </select>

    <update id="updateCsuStrategyEndTime">
        UPDATE peak_shift_csu5_strategy
        <set>
            end_time = #{bean.endTime}
        </set>
        <where>
            id = #{bean.id}
        </where>
    </update>

    <insert id="insertCsuStrategy">
        INSERT INTO peak_shift_csu5_strategy
        (id, device_id, version, start_time, end_time, weekend_flag)
        VALUES (#{bean.id}, #{bean.deviceId}, #{bean.version}, #{bean.startTime}, #{bean.endTime}, #{bean.weekendFlag});
    </insert>

    <insert id="insertCsuStrategyDetails">
        <if test="beans != null and beans.size()>0">
            INSERT INTO peak_shift_csu5_strategy_detail (id, csu5_strategy_id, strategy_type, holiday_flag, begin_date, end_date, begin_time, end_time)
            VALUES
            <foreach collection="beans" item="bean" separator=",">
                (#{bean.id}, #{bean.csu5StrategyId}, #{bean.strategyType}, #{bean.holidayFlag},
                #{bean.beginDate}, #{bean.endDate}, #{bean.beginTime}, #{bean.endTime})
            </foreach>
            on conflict (id)
            do update set csu5_strategy_id = EXCLUDED.csu5_strategy_id, strategy_type=EXCLUDED.strategy_type,
            holiday_flag=EXCLUDED.holiday_flag, begin_date= EXCLUDED.begin_date, end_date= EXCLUDED.end_date
            , begin_time= EXCLUDED.begin_time, end_time= EXCLUDED.end_time
        </if>
    </insert>

    <select id="selectCsuStrategyDetailByDeviceId" resultType="com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyDetailPo">
        select id, csu5_strategy_id, holiday_flag, strategy_type, begin_date, end_date, begin_time, end_time from
        peak_shift_csu5_strategy_detail where csu5_strategy_id = #{id}
    </select>

    <delete id="deleteKwhGainByDateAndSystemId">
        delete from power_system_kwh_gain where date_str = #{day}
        and power_system_id in
        <foreach collection="powerSystemIdList" item="systemId" open="(" close=")" separator=",">
            #{systemId}
        </foreach>
    </delete>

    <select id="selectLastKwhGainByDay" resultType="com.zte.uedm.battery.bean.KwhGainBean">
        select
        *
        from
        power_system_kwh_gain
        where
        date_str &gt;= #{startDay}
        and date_str &lt;= #{endDay}
        <if test="powerSystemIdList !=null and powerSystemIdList.size()!=0">
            and power_system_id in
            <foreach collection="powerSystemIdList" item="systemId" open="(" close=")" separator=",">
                #{systemId}
            </foreach>
        </if>
    </select>

    <select id="selectLastKwhGain" resultType="com.zte.uedm.battery.bean.KwhGainBean">
        select
            *
        from
            power_system_kwh_gain
        where
            date_str = (
                select
                    date_str
                from
                    power_system_kwh_gain pskg
                where
                    date_str &lt; #{day}
                order by date_str desc
                limit 1
            )
    </select>
    <select id="selectCsuAllStrategyByDeviceIds"
            resultType="com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo">
        select * from peak_shift_csu5_strategy
        where 1=1
        <if test="deviceIds !=null and deviceIds.size()!=0">
                and device_id in
                <foreach collection="deviceIds" item="deviceId" open="(" close=")" separator=",">
                               #{deviceId}
                       </foreach>
        </if>

    </select>
    <select id="selectCsuStrategyDetailByDeviceIds"
            resultType="com.zte.uedm.battery.bean.PeakShiftCsu5StrategyDetailBean">
        select id, csu5_strategy_id, holiday_flag, strategy_type, begin_date, end_date, begin_time, end_time from
        peak_shift_csu5_strategy_detail
        where 1=1
        <if test="deviceIds !=null and deviceIds.size()!=0">
                    and csu5_strategy_id in
                        <foreach collection="deviceIds" item="deviceId" open="(" close=")" separator=",">
                                              #{deviceId}
                               </foreach>
            </if>
    </select>

</mapper>