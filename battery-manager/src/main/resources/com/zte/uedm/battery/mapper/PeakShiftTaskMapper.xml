<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PeakShiftTaskMapper">
    <delete id="deleteById">
        delete from peak_shift_task where id = #{id}
    </delete>

    <select id="selectByCondition" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskPo">
        select a.id,a.name,a.file_id,a.description,a.creator,a.gmt_create,a.updater,a.gmt_modified,
                a.status, a.effective_date, a.expiration_date, a.effective_status, a.template_strategy_id, a.device_type deviceType,
                (case when c.all_detail is null or c.all_detail=0 then null
                 else round(coalesce(b.finished_detail,0)*100.0/c.all_detail)||'%' end) progress
        from
            (select d.id, d.name, d.file_id, d.description, d.creator, d.gmt_create, d.updater, d.gmt_modified,
        d.status ,d.effective_date ,d.expiration_date,d.template_strategy_id,e.device_type,
            (case when to_char(now(),'yyyy-mm-dd hh24:mi:ss') > d.expiration_date then 'expired'
                when to_char(now() + interval '7 day','yyyy-mm-dd hh24:mi:ss') > d.expiration_date then 'nearToExpired'
                else 'normal' end ) effective_status
            from peak_shift_task d
            left join template_strategy e on e.id = d.template_strategy_id
            where 1=1
            <if test="name != null and name != ''">
                and d.name ilike concat('%',#{name},'%')
            </if>
            <if test="creator != null and creator != ''">
                and d.creator ilike concat('%',#{creator},'%')
            </if>
            <if test="description != null and description != ''">
                and d.description ilike concat('%',#{description},'%')
            </if>
            <if test="effectiveDateStart != null and effectiveDateStart != ''">
                AND d.effective_date <![CDATA[>=]]> #{effectiveDateStart}
            </if>
            <if test="effectiveDateEnd != null and effectiveDateEnd != ''">
                AND d.effective_date <![CDATA[<=]]> #{effectiveDateEnd}
            </if>
            <if test="expirationDateStart != null and expirationDateStart != ''">
                AND d.expiration_date <![CDATA[>=]]> #{expirationDateStart}
            </if>
            <if test="expirationDateEnd != null and expirationDateEnd != ''">
                AND d.expiration_date <![CDATA[<=]]> #{expirationDateEnd}
            </if>
            <if test="status != null and status.size() > 0">
                and d.status in
                <foreach collection="status" item="iterm" open="(" separator="," close=")" >
                    #{iterm}
                </foreach>
            </if>
            <if test="deviceType != null and deviceType != ''">
                AND e.device_type ilike concat('%',#{deviceType},'%')
            </if>
        ) a
        left join
        (
            select task_id,count(1) finished_detail from peak_shift_task_detail
            where (status = 'success' or status = 'fail')
            group by task_id
        ) b
        on a.id = b.task_id
        left join
        (
            select task_id,count(1) all_detail from peak_shift_task_detail
            group by task_id
        ) c
        on a.id = c.task_id
        <choose>
            <when test="sortBy != null and sortBy != ''">
                <choose>
                    <when test="order != null and order != ''">
                        order by ${sortBy} ${order},gmt_create desc
                    </when>
                    <otherwise>
                        order by ${sortBy} desc,gmt_create desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by gmt_create desc
            </otherwise>
        </choose>
    </select>

    <select id="selectById" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskPo">
        select a.id, a.name, a.file_id, a.description,a.creator, a.gmt_create, a.updater,a.gmt_modified,a.status,a.effective_date,a.expiration_date,b.device_type deviceType,a.template_strategy_id as templateStrategyId
        from peak_shift_task a
        LEFT JOIN template_strategy b on  b.id = a.template_strategy_id
        where a.id =#{id}
    </select>

    <insert id="insertPeakShiftTask">
        INSERT INTO peak_shift_task
        (id, name, file_id, description, creator, gmt_create, updater, gmt_modified, status, effective_date,
        expiration_date,template_strategy_id)
        VALUES (#{bean.id}, #{bean.name}, #{bean.fileId}, #{bean.description}, #{bean.creator}, #{bean.gmtCreate},
        #{bean.updater}, #{bean.gmtModified}, #{bean.status}, #{bean.effectiveDate}, #{bean.expirationDate},#{bean.templateStrategyId});
    </insert>

    <update id="updatePeakShiftTask">
        update peak_shift_task set
            name = #{bean.name},
            file_id = #{bean.fileId},
            description = #{bean.description},
            creator = #{bean.creator},
            gmt_create = #{bean.gmtCreate},
            updater = #{bean.updater},
            gmt_modified = #{bean.gmtModified},
            status = #{bean.status},
            effective_date = #{bean.effectiveDate},
            expiration_date = #{bean.expirationDate},
            template_strategy_id = #{bean.templateStrategyId}
        where id = #{bean.id}
    </update>

    <update id="updatePeakShiftTaskExpirationDate">
        update peak_shift_task
        set expiration_date = #{expirationDate}
        where name like concat('%', #{name}, '%')
          and gmt_create = (
            select max(gmt_create)
            from peak_shift_task
            where name like concat('%', #{name}, '%')
        )
    </update>

    <select id="checkTaskByname" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskPo">
        select * from peak_shift_task where name = #{name}
    </select>

    <select id="selectDuplicateNames" resultType="java.lang.String">
        select name from peak_shift_task where name = #{name}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <update id="updateStatusById">
        update peak_shift_task set status = #{status},gmt_modified = to_char(now(),'yyyy-mm-dd hh24:mi:ss')  where id
        = #{id}
    </update>

    <select id="selectDeviceLatestTask" resultType="com.zte.uedm.battery.bean.peak.DeviceLatestPeakShiftTaskVo">
        select t3.device_id, t4.effective_date, t4.expiration_date from
        (
        select t1.device_id, t1.task_id, t2.max_time from peak_shift_task_detail t1
        left join
        (select device_id, max(exec_time) as max_time from peak_shift_task_detail where status = 'success' group by device_id) as t2
        on t1.device_id = t2.device_id
        where t1.exec_time = t2.max_time
        ) as t3
        join peak_shift_task t4
        on t3.task_id = t4.id
    </select>

    <select id="selectDeviceLatestTaskWithDevice" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskWithDevicePo">
        SELECT
            id, device_id, file_id, updater, effective_date, expiration_date
        FROM
            (
                SELECT
                    t1.device_id, t1.task_id, t2.max_time
                FROM
                    peak_shift_task_detail t1
                        LEFT JOIN (SELECT device_id, MAX(exec_time) AS max_time FROM peak_shift_task_detail WHERE status = 'success' GROUP BY device_id) AS t2 ON t1.device_id = t2.device_id
                WHERE
                    t1.exec_time = t2.max_time
            ) AS t3
                JOIN peak_shift_task t4 ON t3.task_id = t4.id and t4.creator != 'SYSTEM'
    </select>

    <select id="selectEffectivePendingTask" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskPo">
        select id, name, file_id, description,creator, gmt_create, updater,gmt_modified,status,effective_date,expiration_date,template_strategy_id from peak_shift_task
        where status = 'pending' and effective_date <![CDATA[<=]]> to_char(now(),'yyyy-mm-dd hh24:mi:ss')
    </select>
    <select id="selectExpirationTask" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskPo">
        select id, name, file_id, description,creator, gmt_create, updater,gmt_modified,status,effective_date,expiration_date from peak_shift_task
        where status <![CDATA[<>]]> 'invalid' and expiration_date <![CDATA[<=]]> to_char(now(),'yyyy-mm-dd hh24:mi:ss')
    </select>

    <select id="selectByIdList" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskTemplateDetailPo">
        select a.id,  c.version , c.all_detail
            from peak_shift_task a
            inner JOIN issued_task_strategy_relation b on  b.task_id = a.id
            inner JOIN issued_history_detail c on  c.template_strategy_id = b.template_strategy_id and c.version = b.version
            where 1 = 1
        <if test="idList != null and idList.size() > 0">
            AND a.id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectTaskIds" resultType="java.lang.String">
        select id from peak_shift_task where status = #{status} and
        gmt_modified <![CDATA[ <= ]]> #{timeout}
    </select>

    <select id="selectExecuteTaskIds" resultType="java.lang.String">
        select id from peak_shift_task where status = 'execute'
    </select>
</mapper>