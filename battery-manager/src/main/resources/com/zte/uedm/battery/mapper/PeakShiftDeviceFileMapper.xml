<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PeakShiftDeviceFileMapper">
    <!--根据设备ID查询设备-->
    <select id="selectPeakShiftDeviceStrategyBeanByDeviceId"
            resultType="com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyBean">
        SELECT *
        FROM peak_shift_device_strategy
        WHERE device_id = #{deviceId}
    </select>

    <!--根据设备ID和节假日查询设备详情-->
    <select id="selectPeakShiftDeviceStrategyDetailBeanById"
            resultType="com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyDetailBean">
        SELECT *
        FROM peak_shift_device_strategy_detail
        WHERE id = #{Id}
    </select>

    <select id="selectMaxVersionByDeviceId" resultType="java.lang.Integer">
        select version from peak_shift_device_strategy where device_id =#{deviceId} order By version desc limit 1
    </select>

    <select id="findStrategyByDeviceId" resultType="com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyBean">
        select * from peak_shift_device_strategy where device_id = #{deviceId}
    </select>

    <select id="findStrategyDetailById" resultType="com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyDetailBean">
        select * from peak_shift_device_strategy_detail where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updatePeakShiftDeviceStrategyEndDate">
        update peak_shift_device_strategy set end_date = #{endDate} where device_id = #{deviceId} and version =#{version}
    </update>
    <update id="setDeviceFileResult">
        INSERT INTO peak_shift_device_file (device_id, file_id, status, creator, gmt_create, updater, gmt_modified)
        values (#{uploadBean.deviceId}, #{uploadBean.fileId}, #{uploadBean.status}, #{uploadBean.creator}, #{uploadBean.gmtCreate}, #{uploadBean.updater}, #{uploadBean.gmtModified})
        on conflict(device_id) do update set
        device_id = #{uploadBean.deviceId},
        file_id = #{uploadBean.fileId},
        status = #{uploadBean.status},
        updater = #{uploadBean.updater},
        gmt_modified = #{uploadBean.gmtModified}
    </update>

    <!--查询status状态的文件ID与更新时间-->
    <select id="selectFileIdByDeviceId" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo">
        SELECT device_id AS id, file_id, gmt_modified
        FROM peak_shift_device_file
        WHERE status = #{status}
        <if test="deviceIds != null and deviceIds.size() > 0">
            AND device_id IN
            <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                #{deviceId}
            </foreach>
        </if>
    </select>

    <select id="selectDeviceFileIdAndTime" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo">
        SELECT device_id AS id, file_id, gmt_modified
        FROM peak_shift_device_file
        WHERE device_id = #{deviceId}
    </select>
    <select id="selectDeviceStrategyBeanByDeviceIds"
            resultType="com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyBean">
        SELECT *
        FROM peak_shift_device_strategy
        WHERE 1=1
        <if test="deviceIds != null and deviceIds.size() > 0">
            AND device_id IN
                        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                                #{deviceId}
                        </foreach>
        </if>
    </select>


    <!--根据设备id修改状态-->
    <update id="updateStatusByDeviceId">
        UPDATE peak_shift_device_file SET status = #{status}
        WHERE 1 = 1
        <if test="deviceIds != null and deviceIds.size() > 0">
            AND device_id IN
            <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                #{deviceId}
            </foreach>
        </if>
    </update>

    <insert id="insertPeakShiftDeviceStrategyBean">
        insert into peak_shift_device_strategy (id,device_id,interval_strategy_id,version,start_date,end_date)
        values (#{insertBean.id},#{insertBean.deviceId},#{insertBean.intervalStrategyId},#{insertBean.version},#{insertBean.startDate},#{insertBean.endDate})
    </insert>

    <insert id="insertPeakShiftDeviceStrategyDetail">
        insert into peak_shift_device_strategy_detail (id,template_num,mode,holiday_mode,version,holiday_date_str,dates_and_temp,template_detail_str)
        values (#{detailBean.id},#{detailBean.templateNum},#{detailBean.mode},#{detailBean.holidayMode},#{detailBean.version},#{detailBean.holidayDateStr},#{detailBean.datesAndTemp},#{detailBean.templateDetailStr})
    </insert>

</mapper>