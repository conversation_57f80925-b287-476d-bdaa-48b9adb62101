<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.DisconnectOriginalPointRecordMapper">

    <select id="selectAll" resultType="com.zte.uedm.battery.bean.DisconnectOriginalPointRecordBean">
        SELECT id, start_time, end_time, device_id, omp_id, omp_index, omp_value, creator, gmt_create, updater, gmt_modified
        FROM disconnect_original_point_record order by gmt_modified desc
    </select>

    <select id="selectAllNewData" resultType="com.zte.uedm.battery.bean.DisconnectOriginalPointRecordBean">
        SELECT id, start_time, end_time, device_id, omp_id, omp_index, omp_value, creator, gmt_create, updater, gmt_modified
        FROM disconnect_original_point_record where end_time is null
    </select>

    <insert id="insertList">
        INSERT INTO disconnect_original_point_record(
        id, start_time, end_time, device_id, omp_id, omp_index, omp_value, creator, gmt_create, updater, gmt_modified)
        VALUES
        <foreach collection="list" item="bean" separator=",">
            (
            #{bean.id}, #{bean.startTime}, #{bean.endTime}, #{bean.deviceId}, #{bean.ompId}, #{bean.ompIndex}, #{bean.ompValue},
            #{bean.creator}, #{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified}
            )
        </foreach>
    </insert>

    <update id="updateList">
        <if test="list != null and list.size()>0">
            <foreach collection="list" item="bean" open="" separator=";" close="">
                UPDATE disconnect_original_point_record
                <set>
                end_time=#{bean.endTime},updater=#{bean.updater}, gmt_modified=#{bean.gmtModified}
                </set>
                where id = #{bean.id}
            </foreach>
        </if>
    </update>

    <insert id="upsertList">
        INSERT INTO disconnect_original_point_record(
        id, start_time, end_time, device_id, omp_id, omp_index, omp_value, creator, gmt_create, updater, gmt_modified)
        VALUES
        <foreach collection="list" item="bean" separator=",">
            (
            #{bean.id}, #{bean.startTime}, #{bean.endTime}, #{bean.deviceId}, #{bean.ompId}, #{bean.ompIndex}, #{bean.ompValue},
            #{bean.creator}, #{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified}
            )
        </foreach>

        on conflict (id)
        do update set
        end_time=EXCLUDED.end_time,updater=EXCLUDED.updater, gmt_modified=EXCLUDED.gmt_modified


    </insert>

</mapper>
