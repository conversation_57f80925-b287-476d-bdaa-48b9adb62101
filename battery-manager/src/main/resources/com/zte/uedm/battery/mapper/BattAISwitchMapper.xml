<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BattAISwitchMapper">
    <update id="updateById">
        update batt_ai_compute_overall_config set value = #{value},modified_time = #{time} where id=#{id}
    </update>


    <select id="selectById" resultType="java.lang.String">
        select value from batt_ai_compute_overall_config where id = #{id}
    </select>
</mapper>