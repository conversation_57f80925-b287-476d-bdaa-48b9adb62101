<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.GridStrategyNewMapper">

    <select id="selectById" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.ScopeStrategyPo">
        select id,name,logic_group,energy_type,gmt_create,gmt_modified from scope_strategy where id = #{id}
    </select>

    <select id="selectIntervalStrategyBySeasonId" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.IntervalStrategyPo">
        select * from interval_strategy where season_strategy_id = #{seasonId}
    </select>

    <select id="selectIntervalStrategyDetailByIds"
            resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.IntervalStrategyDetailPo">
        select * from interval_strategy_detail where 1=1
        <if test="intervalStrategyIds != null and intervalStrategyIds.size()>0">
            and interval_strategy_id in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getStrategyCombination"
            resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.StrategyCombinationPo">
        select b.name as scope_strategy_name,
        a.effective_time ,
        a.expiration_time ,
        a.status ,
        a.id as season_strategy_id,
        c.mode
        from
        (select effective_time ,expiration_time ,status ,id ,scope_strategy_id
        from season_strategy
        where status &lt;&gt; 2) a
        inner join (SELECT DISTINCT ss.id, ss.name
                    FROM scope_strategy ss
                             JOIN price_strategy ps ON ss.id = ps.scope_strategy_id
                    WHERE ps.rate_type = 1
                      and energy_type = #{energyType}) b
        on a.scope_strategy_id = b.id
        inner join
        (select season_strategy_id,mode
        from interval_strategy
        where mode &lt;&gt; 3
        group by season_strategy_id,mode) c
        on a.id = c.season_strategy_id
    </select>

    <select id="queryById" resultType="com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.SeasonStrategyPo">
        select * from season_strategy where id = #{id}
    </select>

   </mapper>
