<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PeakShiftOperationRecordMapper">

    <select id="selectPeakShiftOperationRecord" resultType="com.zte.uedm.battery.bean.peak.PeakShiftOperationRecordPojo">
        SELECT id, gmt_create, operation_type, style, file_id, updater,exec_time, task_id
        FROM peak_shift_operation_record
        WHERE id = #{id}
        <if test="operationType != null and operationType != ''">
            AND operation_type = #{operationType}
        </if>
        <if test="updater != null and updater != ''">
            AND updater ilike concat('%', #{updater}, '%')
        </if>
        <if test="updateTimeBegin != null and updateTimeBegin != ''">
            AND TO_CHAR(gmt_create,'yyyy-MM-dd HH24:mi:ss') &gt;= #{updateTimeBegin}
        </if>
        <if test="updateTimeEnd != null and updateTimeEnd != ''">
            AND TO_CHAR(gmt_create,'yyyy-MM-dd HH24:mi:ss') &lt;= #{updateTimeEnd}
        </if>
        ORDER BY gmt_create DESC
    </select>

    <insert id="setOperationRecord">
        INSERT INTO peak_shift_operation_record(
        id, gmt_create, operation_type, style, file_id, updater,exec_time, task_id)
        VALUES (#{recordPojo.id}, #{recordPojo.gmtCreate}, #{recordPojo.operationType}, #{recordPojo.style}, #{recordPojo.fileId}, #{recordPojo.updater},#{recordPojo.execTime},#{recordPojo.taskId})
        on conflict on CONSTRAINT peak_shift_operation_record_key do nothing;
    </insert>

    <update id="updateExecTime">
        update peak_shift_operation_record set exec_time = #{execTime}
        where id = #{id} and operation_type = 'policy-setting' and gmt_create =
        (select gmt_create from peak_shift_operation_record where id = #{id}  and operation_type = 'policy-setting' order by gmt_create desc limit 1)
    </update>
</mapper>