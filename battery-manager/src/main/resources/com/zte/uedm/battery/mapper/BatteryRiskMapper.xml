<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BatteryRiskMapper">

    <select id="evalDetailSelect" resultType="com.zte.uedm.battery.controller.batteryrisk.vo.ResultRiskEvalVo">
        SELECT risk_id as riskId, batt_id as battId, "name" as name, path_names as position, batt_type as battType, risk_name as riskName,
               risk_level as riskLevel, risk_cause as riskCause, risk_suggestion as riskSuggestion, eval_time as evaluateTime
        FROM batt_risk_eval t where 1=1
        <if test="ids != null and ids.size > 0">
            and t.batt_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="vo.evaluateTime != null and vo.evaluateTime != ''">
            and t.eval_time = #{vo.evaluateTime}
        </if>

        <if test="vo.battName != null and vo.battName != ''">
            and t.name ILIKE concat('%',#{vo.battName},'%')
        </if>

        <if test="vo.riskName != null and vo.riskName.size > 0">
            <foreach collection="vo.riskName" item="riskid" open="and (" separator=" or " close=")">
                t.risk_id LIKE CONCAT('%', #{riskid}, '%')
            </foreach>
        </if>

        <if test="vo.order == null or vo.order == ''">
            order by t.risk_level ${sort}, t.risk_record_id ${sort}
        </if>

        <if test="vo.order != null and vo.order != ''">
            order by ${vo.order} ${sort}
        </if>
    </select>

    <select id="getBattRiskByTime" resultType="com.zte.uedm.battery.bean.BattRiskEvalPojo">
        select batt_id,risk_id,eval_time,risk_level from  batt_risk_eval where
        eval_time between #{startTime} and #{endTime}
    </select>

</mapper>