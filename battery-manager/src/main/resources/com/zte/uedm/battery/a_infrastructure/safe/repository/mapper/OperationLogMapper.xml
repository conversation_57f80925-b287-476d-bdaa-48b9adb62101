<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.OperationLogMapper">

    <!-- Started by AICoder, pid:dd2c2s7fc9mc349140aa0a22c02bd90d59d9e37c -->
    <select id="selectByMoIdListAndType" resultType="com.zte.uedm.common.bean.log.OperationLogBean">
        select * from operation_log
        where resource_id is not null
        and resource_id = ANY(
        <foreach collection="moIdList" item="id" open="ARRAY[" separator="," close="]">
            #{id}
        </foreach>
        ::text[]
        )
        <if test="typeList != null and typeList.size > 0">
            and type in
            <foreach collection="typeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

    <insert id="addOperationLog">
        insert into operation_log(id,type,resource_id,status,operator,detail,description,create_time,update_time)
        values(#{id},#{type},#{resourceId},#{status},#{operator},#{detail},#{description},#{createTime},#{updateTime})
    </insert>

    <update id="updateOperationLog">
        update operation_log set status = #{status}
        <if test="detail !=null and detail != ''">
            , detail = #{detail}
        </if>
        <if test="description !=null and description != ''">
            ,description = #{description}
        </if>
        ,update_time = #{updateTime}
        where id=#{id}
    </update>

    <select id="selectByCondition" resultType="com.zte.uedm.common.bean.log.OperationLogBean">
        select * from operation_log where 1=1
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="resourceId != null and resourceId != ''">
            and resource_id = #{resourceId}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="operator != null and operator != ''">
            and operator = #{operator}
        </if>
        <if test="beginTime != null and beginTime != ''">
            and update_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and update_time &lt;= #{endTime}
        </if>

    </select>
    <!-- Ended by AICoder, pid:dd2c2s7fc9mc349140aa0a22c02bd90d59d9e37c -->
</mapper>