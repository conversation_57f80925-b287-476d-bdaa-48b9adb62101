<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PvExceptionRecordMapper">
    <insert id="insert">
        <if test="list != null and list.size()>0">
            INSERT INTO pv_exception_record (id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
            gmt_modified) VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.pvId}, #{bean.alarmType}, #{bean.generateDate}, #{bean.endDate},
                #{bean.state}, #{bean.gmtCreate}, #{bean.gmtModified})
            </foreach>
        </if>
    </insert>

    <select id="select" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record where 1=1
        <!-- 多个站点数据筛选-->
        <if test="pvId != null and pvId !=''">
            and pv_id = #{pvId}
        </if>
        <if test="generateDate != null and generateDate !=''">
            and generate_date &gt;= #{generateDate}
        </if>
        <if test="endDate != null and endDate !=''">
            and end_date &lt;= #{endDate}
        </if>
        order by generate_date desc
    </select>

    <select id="selectNoEndAlarm" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record where 1=1
        <!-- 多个站点数据筛选-->
        <if test="pvId != null and pvId !=''">
            and pv_id = #{pvId}
        </if>
        and state = '0'
    </select>

    <select id="selectAlls" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select * from pv_exception_record where 1=1
    </select>

    <update id="updateInsufficientTime">
        update pv_exception_record set gmt_modified = #{gmtModified}
        where pv_id = #{pvId} and state = '0' and alarm_type = '1'
    </update>

    <select id="selectNotEndInsufficiePvId" resultType="java.lang.String">
        select pv_id from pv_exception_record where state = '0' and alarm_type = '1'
    </select>


    <update id="updateStateToThree" parameterType="com.zte.uedm.battery.bean.pv.PvAlarmCleanBean">
        <!--0 告警中 3人工清除 -->
        update pv_exception_record set end_date = current_date, state = '3', gmt_modified = now()::timestamp(0)without
        time zone
        where pv_id = #{pvId} and alarm_type in
        <foreach collection="alarmTypeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and state = '0'
    </update>

    <update id="updateInsufficientState">
        update pv_exception_record set gmt_modified = #{gmtModified}, end_date = #{endDate}, state = #{state}
        where pv_id = #{pvId} and state = '0' and alarm_type = '1'
    </update>

    <update id="clearAlarmByCondition">
        update pv_exception_record set gmt_modified = #{gmtModified}, end_date = #{endDate}, state = #{state}
        where id = #{id}
    </update>
    <update id="updateMissTime">
        <!--0 告警中 2 丢失告警 -->
        update pv_exception_record set gmt_modified = #{gmtModified}
        where pv_id = #{pvId} and state = '0' and alarm_type = '2'
    </update>

    <update id="updateMaintainTime">
        <!--0 告警中 3 维护告警-->
        update pv_exception_record set gmt_modified = #{gmtModified}
        where pv_id = #{pvId} and state = '0' and alarm_type = '3'
    </update>

    <select id="selectByIdList" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select pv_id, generate_date from pv_exception_record where state = '0'
        <if test="null != list and list.size() > 0">
            and pv_id in
            <foreach collection="list" item="pvId" open="(" separator="," close=")">
                #{pvId}
            </foreach>
        </if>
    </select>

    <select id="selectMissAlarmInfoById" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        <!--0告警中 2 丢失告警-->
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record where pv_id = #{pvId} and state = '0' and alarm_type = '2'
    </select>
    <select id="selectMaintainAlarmInfoById" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        <!--0 告警中 3 维护告警-->
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record where pv_id = #{pvId} and state = '0' and alarm_type = '3'
    </select>
    <select id="selectById" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record where id = #{id}
    </select>

    <select id="selectIdByPvAlarmCleanBean" resultType="java.lang.String">
        select id from pv_exception_record where pv_id = #{pvId} and alarm_type in
        <foreach collection="alarmTypeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and state = '0'
    </select>

    <select id="selectExceptionAlarmInfoById" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create,
        gmt_modified from pv_exception_record
        <where>
            <if test = "pvId != null ">
                pv_id = #{pvId}
            </if>
            <if test = "state != null ">
                and state = #{state}
            </if>
            <if test = "alarmType != null ">
                and alarm_type = #{alarmType}
            </if>
        </where>
    </select>

    <update id="updateExceptionAlarmTime">
        update pv_exception_record set gmt_modified = #{gmtModified}
        <where>
            <if test = "pvId != null ">
                pv_id = #{pvId}
            </if>
            <if test = "state != null ">
                and state = #{state}
            </if>
            <if test = "alarmType != null ">
                and alarm_type = #{alarmType}
            </if>
        </where>
    </update>

    <select id="selectExceptionAlarmByTime" resultType="com.zte.uedm.battery.bean.pv.PvExceptionRecordBean">
        select id, pv_id, alarm_type, generate_date, end_date, state, gmt_create, gmt_modified from pv_exception_record
        <where>
            <if test="null != pvIds and pvIds.size() > 0">
                pv_id in
                <foreach collection="pvIds" item="pvId" open="(" separator="," close=")">
                    #{pvId}
                </foreach>
            </if>
            <if test = "state != null ">
                and state = #{state}
            </if>
            <if test = "alarmType != null ">
                and alarm_type != #{alarmType}
            </if>
            <if test = "startTime != null ">
                and generate_date &gt;= #{startTime}
            </if>
            <if test = "endTime != null ">
                and generate_date &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>