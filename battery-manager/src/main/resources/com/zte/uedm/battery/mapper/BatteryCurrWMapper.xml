<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BatteryCurrWMapper">
    <insert id="insertList">
        <if test="list != null and list.size()>0">
            INSERT INTO batt_curr_w (id, mo_id,save_time, type, batt_id,avg_curr, creator, gmt_create, updater, gmt_modified)
            VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.moId},#{bean.saveTime}, #{bean.type}, #{bean.battId}, #{bean.avgCurr},
                 #{bean.creator}, #{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified})
            </foreach>
            ON conflict(id)  do update set
            mo_id = EXCLUDED.mo_id, save_time = EXCLUDED.save_time,  type = EXCLUDED.type, batt_id=EXCLUDED.batt_id,
            avg_curr = EXCLUDED.avg_curr,updater = EXCLUDED.updater, gmt_modified=EXCLUDED.gmt_modified
        </if>
    </insert>
    <select id="getList" resultType="com.zte.uedm.battery.bean.BatteryCurrentPojo">
        select * from batt_curr_w
        where mo_id = #{moId}
        and batt_id in
        <foreach collection="list" item="battId" open="(" separator="," close=")">
            #{battId}
        </foreach>
    </select>
    <select id="getAllList" resultType="com.zte.uedm.battery.bean.BatteryCurrentPojo">
        select * from batt_curr_w
        where batt_id in
        <foreach collection="list" item="battId" open="(" separator="," close=")">
            #{battId}
        </foreach>
    </select>

</mapper>