<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PvAlarmRecordMapper">

    <insert id="insert">
        <if test="id != null and id !=''">
            insert into pv_alarm_record(id, displayname, state, exception_id, gmt_create, gmt_modified)
            values(#{id}, #{displayname}, #{state}, #{exceptionId}, #{gmtCreate}, #{gmtModified})
            on conflict(id) do update set
            displayname=EXCLUDED.displayname, state=EXCLUDED.state, exception_id=EXCLUDED.exception_id,
            gmt_create = EXCLUDED.gmt_create, gmt_modified=EXCLUDED.gmt_modified
        </if>
    </insert>

    <update id="updateState">
        update pv_alarm_record set gmt_modified = #{gmtModified},
        <if test="displayname != null and displayname !=''">
            displayname = #{displayname},
        </if>
        state = #{state}
        where id = #{id} and state = '0'
    </update>

    <update id="manualUpdateState">
        update pv_alarm_record set gmt_modified = #{gmtModified}, state = #{state}
        where exception_id = #{exceptionId} and state = '0'
    </update>

    <select id="select" resultType="com.zte.uedm.battery.bean.pv.PvAlarmRecordBean">
        select id, displayname, state, exception_id, gmt_create, gmt_modified from pv_alarm_record where exception_id = #{exceptionId}
        and state = #{state}
    </select>

    <select id="selectExcepionIdById" resultType="java.lang.String">
        select exception_id from pv_alarm_record where id = #{id}
    </select>

</mapper>