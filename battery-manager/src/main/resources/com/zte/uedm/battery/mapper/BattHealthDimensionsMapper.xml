<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BattHealthDimensionsMapper">
    <insert id="insertBatch">
        <if test="list!=null and list.size()>0">
            INSERT INTO battery_health_dimensions (id, name, user_name, sequence, enable, creator, updater, gmt_create,
            gmt_modified)
            VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.name}, #{bean.userName}, #{bean.sequence},
                #{bean.enable},#{bean.userName}, #{bean.userName}, #{bean.gmtCreate}, #{bean.gmtModified})
            </foreach>
        </if>
    </insert>
    <update id="updateBatch">
        <if test="updateDBList != null and updateDBList.size > 0">
            <foreach collection="updateDBList" item="item">
                update battery_health_dimensions
                set
                id = #{item.id},
                sequence = #{item.sequence},
                enable = #{item.enable},
                updater = #{item.updater},
                gmt_modified = #{item.gmtModified}
                where
                id = #{item.id} and user_name = #{item.userName};
            </foreach>
        </if>
    </update>
    <delete id="deleteByName">
        DELETE FROM battery_health_dimensions WHERE user_name = #{userName}
    </delete>

    <select id="selectHealthListConfig" resultType="com.zte.uedm.battery.bean.BatteryHealthDimensionsBean">
        select * from battery_health_dimensions where user_name = #{userName} order by sequence
    </select>
    <select id="searchHealthConfig" resultType="com.zte.uedm.battery.bean.BatteryHealthDimensionsBean">
        select * from battery_health_dimensions where user_name = #{userName}
        <if test="name != null and name !=''">
            and name like concat('%',#{name},'%')
        </if>
        order by sequence
    </select>

</mapper>