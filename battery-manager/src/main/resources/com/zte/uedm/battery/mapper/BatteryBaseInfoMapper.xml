<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BatteryBaseInfoMapper">


    <insert id="insertList">
        <if test="list != null and list.size()>0">
            INSERT INTO battery_base_info (id, batt_redis_type, volt, curr, temp, soc, rated_cap, charge_status, remain_discharge_duration,
            llvd_discharge_duration, gmt_modified)
            VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.battRedisType}, #{bean.volt}, #{bean.curr}, #{bean.temp}, #{bean.soc}, #{bean.ratedCap}, #{bean.chargeStatus}, #{bean.remainDischargeDuration},
                #{bean.llvdDischargeDuration}, #{bean.gmtModified})
            </foreach>
            ON conflict(id)  do update set
            batt_redis_type=EXCLUDED.batt_redis_type, volt=EXCLUDED.volt, curr=EXCLUDED.curr, temp=EXCLUDED.temp,
            soc=EXCLUDED.soc, rated_cap=EXCLUDED.rated_cap, charge_status = EXCLUDED.charge_status, remain_discharge_duration=EXCLUDED.remain_discharge_duration, llvd_discharge_duration=EXCLUDED.llvd_discharge_duration, gmt_modified=EXCLUDED.gmt_modified
        </if>
    </insert>
    <select id="selectByLogicId" resultType="com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean">
        select id, batt_cfg_type, batt_asset_type, batt_redis_type,  volt, curr, "temp", soc, charge_status, remain_discharge_duration, manufacturer,
        supplier,brand, series, model, start_date, rated_cap, health_info, life_info, risk_info,maintenance_period
        <if  test="logicId!=null and logicId!='' and logicId!='r32.uedm.group-global'">, "name", path_id, path_name</if>
        from battery_base_info
        <where>
            <if test="logicId!=null and logicId!='' and logicId!='r32.uedm.group-global'">
                 #{logicId} = any(regexp_split_to_array(path_id, '/'))
            </if>
        </where>
        <if test="limit!=null and offset!=null">
            order by id
            limit #{limit} offset #{offset}
        </if>
    </select>

    <select id="getAllNum" resultType="int">
        select count(id) from battery_base_info
    </select>
    <select id="selectByIdList" resultType="com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean">
        <if test="list != null and list.size() > 0">
            select id,path_id, batt_cfg_type, batt_asset_type, batt_redis_type, volt, curr, "temp", soc, charge_status,
            remain_discharge_duration, llvd_discharge_duration, manufacturer,
            supplier,brand, series, model, start_date, rated_cap, health_info, life_info, risk_info,maintenance_period
            from battery_base_info
            where id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectAllId" resultType="java.lang.String">
        select id from battery_base_info
    </select>

    <update id="updateBatteryHealthLifeRiskInfo">
        <if test="list != null and list.size() > 0">
            <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
                update battery_base_info
                <set>
                    <if test="bean.healthInfo != null and bean.healthInfo != ''">
                        health_info=#{bean.healthInfo},
                    </if>
                    <if test="bean.lifeInfo != null and bean.lifeInfo != ''">
                        life_info=#{bean.lifeInfo},
                    </if>
                    <if test="bean.riskInfo != null and bean.riskInfo != ''">
                        risk_info=#{bean.riskInfo},
                    </if>
                    gmt_modified=#{bean.gmtModified}
                </set>
                where id=#{bean.id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatteryInfoAndAssetInfo">
        <if test="list != null and list.size()>0">
            insert into battery_base_info (id,name,path_id,path_name,batt_cfg_type,batt_asset_type,manufacturer,brand,series,model,start_date,rated_cap,gmt_create,maintenance_period,supplier)
            values
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id},#{bean.name},#{bean.pathId},#{bean.pathName},#{bean.battCfgType},#{bean.battAssetType},
                #{bean.manufacturer},#{bean.brand},#{bean.series},#{bean.model},#{bean.startDate},#{bean.ratedCap},
                #{bean.gmtCreate},#{bean.maintenancePeriod},#{bean.supplier})
            </foreach>
            on conflict(id) do update set name=EXCLUDED.name, path_id=EXCLUDED.path_id, path_name=EXCLUDED.path_name, batt_cfg_type=EXCLUDED.batt_cfg_type,
            batt_asset_type=EXCLUDED.batt_asset_type, manufacturer=EXCLUDED.manufacturer, brand=EXCLUDED.brand, series=EXCLUDED.series, model=EXCLUDED.model,
            start_date=EXCLUDED.start_date, rated_cap=EXCLUDED.rated_cap, maintenance_period=EXCLUDED.maintenance_period, supplier=EXCLUDED.supplier
        </if>
    </insert>

    <insert id="insertBatteryInfo">
        <if test="list != null and list.size()>0">
            insert into battery_base_info (id,name,path_id,path_name,gmt_create,batt_cfg_type)
            values
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id},#{bean.name},#{bean.pathId},#{bean.pathName},#{bean.gmtCreate},#{bean.battCfgType})
            </foreach>
            on conflict(id) do update set name=EXCLUDED.name, path_id=EXCLUDED.path_id, path_name=EXCLUDED.path_name, batt_cfg_type=EXCLUDED.batt_cfg_type
        </if>
    </insert>

    <update id="updateBatteryInfo">
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
                update battery_base_info
                <set>
                    name = #{bean.name},
                    path_id = #{bean.pathId},
                    path_name = #{bean.pathName},
                    batt_cfg_type = #{bean.battCfgType}
                </set>
                where id = #{bean.id}
            </foreach>
        </if>
    </update>

    <delete id="deleteBatteryInfo">
        <if test="list != null and list.size()>0">
            delete from battery_base_info where id in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </delete>

    <insert id="insertAssetInfo">
        <if test="list != null and list.size()>0">
            insert into battery_base_info (id,batt_asset_type,manufacturer,brand,series,model,start_date,maintenance_period,supplier)
            values
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id},#{bean.battAssetType},#{bean.manufacturer},#{bean.brand},#{bean.series},#{bean.model},#{bean.startDate},#{bean.maintenancePeriod},#{bean.supplier})
            </foreach>
            on conflict(id) do update set
            batt_asset_type=EXCLUDED.batt_asset_type, manufacturer=EXCLUDED.manufacturer, brand=EXCLUDED.brand,
            series=EXCLUDED.series, model=EXCLUDED.model, start_date=EXCLUDED.start_date, maintenance_period=EXCLUDED.maintenance_period,
            supplier=EXCLUDED.supplier
        </if>
    </insert>

    <update id="updateAssetInfo">
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
                update battery_base_info
                <set>
                    batt_asset_type = #{bean.battAssetType},
                    manufacturer = #{bean.manufacturer},
                    brand = #{bean.brand},
                    series = #{bean.series},
                    model = #{bean.model},
                    start_date = #{bean.startDate},
                    maintenance_period = #{bean.maintenancePeriod},
                    supplier = #{bean.supplier}
                </set>
                where id = #{bean.id}
            </foreach>
        </if>
    </update>

    <delete id="deleteAssetInfo">
        <if test="list != null and list.size()>0">
            delete from battery_base_info where id in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </delete>

    <delete id="clearBatteryInfo">
        vacuum full public.battery_base_info
    </delete>

    <delete id="deleteBatteryByIds">
        <if test="list != null and list.size()>0">
            delete from battery_base_info where id in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

</mapper>