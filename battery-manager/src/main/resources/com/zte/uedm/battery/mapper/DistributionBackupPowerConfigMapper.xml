<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.DistributionBackupPowerConfigMapper">

    <select id="selectDistributionBackupPowerConfig" resultType="com.zte.uedm.battery.bean.BackupPowerConfigPo">
        select id, name, user_name, sequence, enable from backup_power_distribution_dimensions
        where user_name = #{userName} order by sequence
    </select>

    <insert id="insertDistributionBackupPowerConfigByBeans">
        <if test="list!=null and list.size()>0">
            INSERT INTO backup_power_distribution_dimensions (id, name, user_name, sequence, enable, creator, updater, gmt_create,
            gmt_modified)
            VALUES
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.name}, #{bean.userName}, #{bean.sequence},
                #{bean.enable},#{bean.userName}, #{bean.userName}, #{bean.gmtCreate}, #{bean.gmtModified})
            </foreach>
        </if>
    </insert>

    <update id="updateDistributionBackupPowerConfigByBeans">
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="bean">
                update backup_power_distribution_dimensions
                <set>
                    sequence = #{bean.sequence},
                    enable = #{bean.enable},
                    updater = #{bean.updater},
                    gmt_modified = #{bean.gmtModified}
                </set>
                where id = #{bean.id} and user_name = #{bean.userName};
            </foreach>
        </if>
    </update>

    <update id="updateDistributionIdNameByBeans">
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="bean">
                update backup_power_distribution_dimensions
                <set>
                    name = #{bean.name},
                </set>
                where id = #{bean.id} and user_name = #{bean.userName};
            </foreach>
        </if>
    </update>

    <delete id="deleteDistributionBackupPowerConfigByUserName">
        delete from backup_power_distribution_dimensions where user_name = #{userName}
    </delete>

</mapper>
