<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.battery.mapper.PeakShiftTaskDetailMapper">

    <select id="selectByCondition" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailPo">
        select id, task_id, device_id, status,creator, gmt_create, updater,gmt_modified from peak_shift_task_detail
        where 1=1
        <if test="taskIds!=null and taskIds.size()>0">
            and task_id in
            <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
        <if test="deviceIds!=null and deviceIds.size()>0">
            and device_id in
            <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByTaskId" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailPo">
        select id, task_id, device_id, status,file_id,ftp_path,log_id,creator, gmt_create, updater,gmt_modified,exec_time,fail_timeout from
        peak_shift_task_detail
        where task_id=#{taskId}
        <if test="deviceStatus != null and deviceStatus.size() >0">
            and status in
            <foreach collection="deviceStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <insert id="insertPeakShiftTaskDetail">
        <if test="beans!=null and beans.size()>0">
            insert into peak_shift_task_detail
            (
            id,
            task_id,
            device_id,
            status,
            gmt_create,
            updater,
            creator,
            gmt_modified
            )
            values
            <foreach collection="beans" item="bean" separator=",">
                (#{bean.id},
                #{bean.taskId},
                #{bean.deviceId},
                #{bean.status},
                #{bean.gmtCreate},
                #{bean.updater},
                #{bean.creator},
                #{bean.gmtModified}
                )
            </foreach>
        </if>
    </insert>

    <delete id="deleteByTaskId">
        <!--<if test="deviceIds != null and deviceIds.size()>0">
            delete from peak_shift_task_detail where device_id in
            <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                #{deviceId}
            </foreach>
        </if>-->
        delete from peak_shift_task_detail where task_id = #{taskId}
    </delete>

    <update id="updateByTaskIdAndState">
        UPDATE peak_shift_task_detail SET status = 'fail',fail_timeout = true
        <where>
            task_id = #{taskId}
            and status = #{status}
        </where>
    </update>

    <select id="checkDeviceState" resultType="java.lang.String">
        select device_id from peak_shift_task_detail where 1 = 1
        <if test="status != null and status !=''">
            and status = #{status}
        </if>
        <if test="deviceIds != null">
            and device_id IN
            <foreach collection="deviceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectFileInfo" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo">
        SELECT ps.id as id, pst.file_id, ps.gmt_modified
        FROM peak_shift_task_detail AS ps
        INNER JOIN peak_shift_task AS pst
        ON ps.task_id = pst.id
        WHERE ps.device_id = #{id} AND ps.status = #{status}
        ORDER BY ps.gmt_modified DESC
        LIMIT 1
    </select>

    <!--查询设备最新的、状态为status的文件ID与更新时间;注：如果同一设备ID，且更新时间相同，则该设备ID会出现多条数据结果-->
    <select id="selectFileInfoList" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo">
        SELECT p.device_id AS id, pst.file_id, p.gmt_modified FROM
        (
        SELECT pst.task_id, ps.gmt_modified, ps.device_id FROM peak_shift_task_detail AS pst
        INNER JOIN
        (
        SELECT MAX(gmt_modified) AS gmt_modified, device_id FROM peak_shift_task_detail WHERE status = #{status}
        <if test="ids != null and ids.size() > 0">
            AND device_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY device_id
        ) AS ps
        ON ps.gmt_modified = pst.gmt_modified AND ps.device_id = pst.device_id
        ) AS p
        INNER JOIN peak_shift_task AS pst
        ON pst.id = p.task_id
    </select>

    <!--查询ids中状态为status的-->
    <select id="selectOptionalDeviceStatus" resultType="java.lang.String">
        SELECT device_id FROM peak_shift_task_detail WHERE status = #{status}
        <if test="ids != null and ids.size() > 0">
            AND device_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="updateDeviceStatus">
        UPDATE peak_shift_task_detail SET status = #{status}
        <where>
            id = #{detailId}
        </where>
    </update>

    <update id="updateDeviceStatusByTaskId">
        UPDATE peak_shift_task_detail SET status = #{status},
        gmt_modified = #{currentTime}
        <where>
            task_id = #{taskId} and device_id = #{deviceId}
        </where>
    </update>

    <update id="updateInProgressDevice">
        <if test="status != null and oldStatus != null">
            UPDATE peak_shift_task_detail SET status = #{status}
            <where>
                status = #{oldStatus}
            </where>
        </if>
    </update>
    <update id="updateStatusToProgress">
        UPDATE peak_shift_task_detail SET status = 'in_progress',
        exec_time = #{currentTime},
        gmt_modified = #{currentTime}
        where task_id = #{taskId}
        <if test="ids != null and ids.size() > 0">
            AND device_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
    <update id="updateByTimeAndState">
        UPDATE peak_shift_task_detail SET status = 'fail',fail_timeout = true
        where status = #{status} and
        gmt_modified <![CDATA[ <= ]]> #{timeout}
    </update>
    <update id="updateTaskLogId">
        UPDATE peak_shift_task_detail SET log_id = #{logId}
        <where>
            task_id = #{taskId}  and device_id = #{deviceId}
        </where>
    </update>
    <update id="updateFtpPath">
        UPDATE peak_shift_task_detail SET ftp_path = #{ftpPtah}
        <where>
            task_id = #{taskId}  and file_id = #{fileId}
        </where>
    </update>

    <select id="selectLatestTaskByDeviceId" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceTaskBo">
        select
        a.device_id,
        a.task_id,
        b.file_id,
        a.exec_time as issued_time
        from peak_shift_task_detail a , peak_shift_task b
        where a.task_id = b.id
        and a.status = 'success'
        and a.device_id = #{deviceId}
        order by issued_time desc
        limit 1
    </select>
    <select id="selectlDeviceByTaskIdAndStatus" resultType="java.lang.String">
        SELECT device_id FROM peak_shift_task_detail WHERE status = #{status}
        and task_id = #{taskId}
    </select>


    <select id="selectDeviceTaskInfoByCondition" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceTaskBean">
        select a.device_id as id,a.status as device_status,b.expiration_date,
        b.status as task_status,b.name as task_name,b.effective_status, b.effective_date, a.task_id
        from
        (
        select task_id,device_id,status,exec_time from peak_shift_task_detail
        where 1 = 1
        <if test="deviceStatus != null and deviceStatus.size() > 0">
            and status in
            <foreach collection="deviceStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
        inner join
        (
        select device_id,max(exec_time) as exec_time from peak_shift_task_detail
        where 1 = 1
        <choose>
            <when test="deviceIds!=null and deviceIds.size()>0">
                and device_id in
                <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1 = -1
            </otherwise>
        </choose>
        group by device_id
        ) c
        on a.device_id = c.device_id and a.exec_time = c.exec_time
        left join
        (select id, name, status, effective_date, expiration_date,
        (case when to_char(now(),'yyyy-mm-dd hh24:mi:ss') > expiration_date then 'expired'
        when to_char(now() + interval '7 day','yyyy-mm-dd hh24:mi:ss') > expiration_date then 'nearToExpired'
        else 'normal' end ) effective_status
        from peak_shift_task
        ) b
        on a.task_id = b.id
        where 1 = 1
        <if test="expirationDateStart != null and expirationDateStart != ''">
            AND b.expiration_date <![CDATA[>=]]> #{expirationDateStart}
        </if>
        <if test="expirationDateEnd != null and expirationDateEnd != ''">
            AND b.expiration_date <![CDATA[<=]]> #{expirationDateEnd}
        </if>
        <if test="taskStatus != null and taskStatus.size() > 0">
            and b.status in
            <foreach collection="taskStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectCountGroupByStatus" resultType="com.zte.uedm.battery.bean.DeviceStatusCountBean">
        select
        count(case when status ='pending' then 1 end) pending,
        count(case when status ='in_progress' then 1 end) progress,
        count(case when status ='success' then 1 end) success,
        count(case when status ='fail' then 1 end) fail
        from peak_shift_task_detail
        where task_id = #{taskId}
    </select>
    <select id="selectTotalByTaskId" resultType="java.lang.Integer">
        select count(0) from peak_shift_task_detail where task_id = #{taskId}
    </select>
    <select id="selectSuccessDevices" resultType="com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailPo">
        select id, task_id, device_id, status,creator, gmt_create, updater,gmt_modified,exec_time from
        peak_shift_task_detail
        where status = 'success' and task_id=#{taskId}
    </select>
    <select id="selectDeviceTaskInfoByNewCondition" resultType="com.zte.uedm.battery.bean.PeakShiftDeviceTaskBean">
        select a.device_id as id,a.status as device_status,b.expiration_date,b.effective_date,
        b.status as task_status,b.name as task_name,b.effective_status,a.task_id,d.season_strategy_id
        from
        (
        select task_id,device_id,status,exec_time from peak_shift_task_detail
        where 1 = 1
        <if test="deviceStatus != null and deviceStatus.size() > 0">
            and status in
            <foreach collection="deviceStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
        inner join
        (
        select device_id,max(exec_time) as exec_time from peak_shift_task_detail
        where 1 = 1
        <choose>
            <when test="deviceIds!=null and deviceIds.size()>0">
                and device_id in
                <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1 = -1
            </otherwise>
        </choose>
        group by device_id
        ) c
        on a.device_id = c.device_id and a.exec_time = c.exec_time
        left join
        (select id, name, status ,expiration_date,template_strategy_id,effective_date,
        (case when to_char(now(),'yyyy-mm-dd hh24:mi:ss') > expiration_date then 'expired'
        when to_char(now() + interval '7 day','yyyy-mm-dd hh24:mi:ss') > expiration_date then 'nearToExpired'
        else 'normal' end ) effective_status
        from peak_shift_task
        ) b
        on a.task_id = b.id
        left join
        template_strategy d
        on b.template_strategy_id = d.id
        where 1 = 1
        <if test="expirationDateStart != null and expirationDateStart != ''">
            AND b.expiration_date <![CDATA[>=]]> #{expirationDateStart}
        </if>
        <if test="expirationDateEnd != null and expirationDateEnd != ''">
            AND b.expiration_date <![CDATA[<=]]> #{expirationDateEnd}
        </if>
        <if test="priceIntervalId != null and priceIntervalId.size()>0">
            and d.season_strategy_id in
            <foreach collection="priceIntervalId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getLogicGroupByTemplateStrategyId" resultType="java.lang.String">
        select c.logic_group
        from template_strategy a
        inner join
        season_strategy b
        on a.season_strategy_id = b.id
        inner join (select id, logic_group from scope_strategy where energy_type = '0') c
        on b.scope_strategy_id = c.id
        where a.id = #{templateStrategyId}
    </select>
    <select id="selectTaskIdByDetailId" resultType="java.lang.String">
        select task_id from peak_shift_task_detail where id = #{detailId}
    </select>
    <select id="queryAllPendingDevice" resultType="com.zte.uedm.battery.bean.PendingBean">
        select ab.device_id,c.template_strategy_id,d.season_strategy_id  from (
        select a.device_id ,b.id, min(b.effective_date) effective_date  from peak_shift_task_detail a
        inner join peak_shift_task b
        on a.task_id = b.id
        where a.status = 'pending'
        group by a.device_id,b.id ) ab
        inner join peak_shift_task c
        on ab.id = c.id
        inner join template_strategy d
        on c.template_strategy_id = d.id
    </select>
    <select id="selectTaskIds" resultType="java.lang.String">
        select distinct task_id from peak_shift_task_detail where status = #{status} and
        gmt_modified <![CDATA[ <= ]]> #{timeout}
    </select>
    <select id="selectAnyTaskIdByFileId" resultType="java.lang.String">
        select task_id from peak_shift_task_detail where file_id =#{fileId} limit 1
    </select>
</mapper>
