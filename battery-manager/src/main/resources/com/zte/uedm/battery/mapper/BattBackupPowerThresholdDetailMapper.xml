<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.battery.mapper.BattBackupPowerThresholdDetailMapper">
    <insert id="upsert">
        <if test="list != null and list.size()>0">
            insert into batt_backup_power_threshold_detail(id, threshold, type, category_id, creator,
            gmt_create, updater, gmt_modified) values
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.threshold}, #{bean.type}, #{bean.categoryId}, #{bean.creator},
                #{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified})
            </foreach>
            on conflict(id) do update set threshold=EXCLUDED.threshold, type=EXCLUDED.type, category_id=EXCLUDED.category_id,
            updater=EXCLUDED.updater, gmt_modified=EXCLUDED.gmt_modified where batt_backup_power_threshold_detail.type != 'special'
        </if>
    </insert>

    <select id="selectById" resultType="com.zte.uedm.battery.bean.BattBackupPowerThresholdDetailPojo">
        select id, threshold, type, category_id, creator,
        gmt_create, updater, gmt_modified from batt_backup_power_threshold_detail
        where id = #{id}
    </select>

    <select id="deleteCategory" resultType="java.lang.String">
        <if test="list != null and list.size()>0">
            insert into batt_backup_power_threshold_detail(id, threshold, type, category_id, creator,
            gmt_create, updater, gmt_modified) values
            <foreach collection="list" item="bean" separator=",">
                (#{bean.id}, #{bean.threshold}, #{bean.type}, #{bean.categoryId}, #{bean.creator},
                #{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified})
            </foreach>
            on conflict(id) do update set threshold=EXCLUDED.threshold, type=EXCLUDED.type, category_id=EXCLUDED.category_id,
            updater=EXCLUDED.updater, gmt_modified=EXCLUDED.gmt_modified where batt_backup_power_threshold_detail.type = 'category'
            RETURNING id
        </if>
    </select>

    <insert id="insert">
        <if test="list != null and list.size>0">
            INSERT INTO batt_backup_power_threshold_detail (id, threshold, backup_power_scenario, type, category_id,  creator,gmt_create, updater,
            gmt_modified)
            VALUES
            <foreach collection="list" item="bean" open="" close="" separator=",">
                (#{bean.id}, #{bean.threshold}, #{bean.backupPowerScenario},#{bean.type}, #{bean.categoryId},
                #{bean.creator},#{bean.gmtCreate}, #{bean.updater}, #{bean.gmtModified})
            </foreach>
            on conflict (id) do UPDATE set
            threshold=EXCLUDED.threshold, type=EXCLUDED.type, category_id=EXCLUDED.category_id,
            updater= EXCLUDED.updater, gmt_modified=EXCLUDED.gmt_modified
        </if>
    </insert>

    <update id="update">
        <if test="list != null and list.size>0">
            <foreach collection="list" item="bean" open="" close="" separator=";">
            update batt_backup_power_threshold_detail
            set
                threshold=#{bean.threshold},type=#{bean.type}, category_id=#{bean.categoryId},
                updater=#{bean.updater}, gmt_modified=#{bean.gmtModified}
            where id=#{bean.id}
            </foreach>
        </if>
    </update>


    <delete id="delete">
        <if test="ids != null and ids.size>0">
            delete from batt_backup_power_threshold_detail where id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <select id="selectByCondition" resultType="com.zte.uedm.battery.bean.BattBackupPowerThresholdDetailPojo">
        select * from batt_backup_power_threshold_detail where type='special'
        <if test="backupSystemIds != null and backupSystemIds.size>0">
            and id in
            <foreach collection="backupSystemIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="updater != null and updater!=''">
            and updater=#{updater}
        </if>
        <if test="updateTimeBegin != null and updateTimeBegin!=''">
            and to_char(gmt_modified,'yyyy-MM-dd hh24:mi:ss') &gt;= #{updateTimeBegin}
        </if>
        <if test="updateTimeEnd != null and updateTimeEnd!=''">
            and to_char(gmt_modified,'yyyy-MM-dd hh24:mi:ss') &lt;= #{updateTimeEnd}
        </if>
    </select>

</mapper>