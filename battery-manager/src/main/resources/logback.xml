<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder
		by default
		<encoder>
			<pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%10.10X{TraceId}][%c{1}][%t] - %m%n)</pattern>
		</encoder>
	</appender>

	<appender name="INFO_FILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>./logs/info.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>./logs/info-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>30</maxHistory>
			<totalSizeCap>1024MB</totalSizeCap>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%10.10X{TraceId}][%c{1}][%t] - %m%n)</Pattern>
		</layout>
	</appender>

	<appender name="ERROR_FILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<File>./logs/error.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>./logs/error-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>30</maxHistory>
			<totalSizeCap>256MB</totalSizeCap>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%highlight(%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%10.10X{TraceId}][%c{1}][%t] - %m%n)</Pattern>
		</layout>
	</appender>
	<appender name="LOGSTASH" class="com.zte.ums.zenap.logback.core.SocketJsonAppender">
		<logstashServiceName>logstash</logstashServiceName>
		<logstashServiceVersion>v1</logstashServiceVersion>
	</appender>

	<appender name="ASYNC_LOGSTASH" class="com.zte.ums.zenap.logback.core.ZenapAsyncAppender">
		<appender-ref ref="LOGSTASH"/>
		<neverBlock>true</neverBlock>
	</appender>

	<root level="INFO">
		<appender-ref ref="STDOUT"/>
		<appender-ref ref="INFO_FILE"/>
		<appender-ref ref="ERROR_FILE"/>
		<appender-ref ref="LOGSTASH" />
	</root>
	<!-- 各模块根据自身情况配置重要模块路径信息 -->
	<logger name="com.zte.uedm" level="info"/>
</configuration>