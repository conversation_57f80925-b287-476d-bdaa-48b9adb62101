package com.zte.uedm.battery.pv.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.opti.domain.service.AuthorizationService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.bean.ImageInfo;
import com.zte.uedm.battery.bean.LogicIdAndMocQueryBean;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.export.manage.entity.ExportReportBO;
import com.zte.uedm.battery.export.manage.entity.ExportType;
import com.zte.uedm.battery.pv.bean.*;
import com.zte.uedm.battery.pv.consts.SolarRevenueConst;
import com.zte.uedm.battery.pv.dto.SolarMonitorQueryDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueOverviewDto;
import com.zte.uedm.battery.pv.enums.*;
import com.zte.uedm.battery.pv.mapper.SolarMaxPowerMapper;
import com.zte.uedm.battery.pv.mapper.SolarRevenueMapper;
import com.zte.uedm.battery.pv.service.CarbonReductionService;
import com.zte.uedm.battery.pv.service.SolarMonitorService;
import com.zte.uedm.battery.pv.vo.*;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.service.impl.AlarmServiceImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.ImageBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.enums.AlarmLevelEnum;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import com.zte.uedm.service.pma.rpc.dto.MakeupRegisterDto;
import com.zte.uedm.service.pma.rpc.impl.MakeupRpcImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.*;
import static com.zte.uedm.battery.consts.CommonConst.NO_ALARM;
import static com.zte.uedm.battery.consts.DateTypeConst.*;
import static com.zte.uedm.battery.pv.consts.SolarRevenueConst.*;
import static com.zte.uedm.battery.service.impl.PvDataExportServiceImpl.randomString;
import static com.zte.uedm.battery.util.SolarRevenueUtil.getFormatData;
/**
 * <AUTHOR>
 * @description
 * @date 2023/10/8
 **/
@Service
@Slf4j
public class SolarMonitorServiceImpl implements SolarMonitorService {
    @Autowired
    private CarbonReductionService carbonReductionService;
    @Autowired
    private SolarRevenueServiceImpl solarRevenueService;
    @Autowired
    private SolarRevenueMapper solarRevenueMapper;
    @Autowired
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;
    @Autowired
    private MonitorManagerRpcImpl monitorManagerRpc;
    @Autowired
    private JsonService jsonService;
    private ExecutorService solarMonitorThreadPool = new ThreadPoolExecutor(20, 100, 0L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
            new ThreadFactoryBuilder().setNameFormat("SolarMonitorCompletableProcessor-%d").build(), this::rejectedExecution);
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private WriterExportFactory wf;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Autowired
    private ConfigService configService;
    @Autowired
    private AlarmServiceImpl alarmServiceImpl;
    @Autowired
    private AssetRpcImpl assetRpcImpl;
    @Autowired
    private SolarRecollectionServiceImpl solarRecollectionServiceImpl;
    @Autowired
    private PmaServiceUtils pmaService;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private SolarMaxPowerMapper solarMaxPowerMapper;
    @Autowired
    private ResourceBaseCacheManager resourceBaseCacheManager;

    @Autowired
    private MakeupRpcImpl makeupRpc;

    @Autowired
    private AuthorizationService authorizationService;
    public static final int MAX_ALARM_QUERY_SIZE = 1000;

    private static final String ALL = "all";
    private static final String ROOT = GlobalOptional.GLOBAL_ROOT;
    private static final String OFFLINE_1 = "1";
    private static final String OFFLINE_2 = "2";
    private static final int SCALE = 2;
    private static final String RATED_POWER = "rated_power";

    //@PostConstruct
    //public void init() {
    //    if (solarMonitorThreadPool != null) {
    //        return;
    //    }
    //    // 如果出现线程任务被拒绝，则等待30s后，再次创建并提交到线程池中
    //    solarMonitorThreadPool = new ThreadPoolExecutor(20, 100, 0L,
    //            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
    //            new ThreadFactoryBuilder().setNameFormat("SolarMonitorCompletableProcessor-%d").build(), this::rejectedExecution);
    //}

    private void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        executor.execute(r);
    }

    @Override
    public PageInfo<SolarMonitorVo> solarMonitorSelectPage(SolarMonitorQueryDto solarMonitorQueryDto, String languageOption, String userName) throws UedmException, ExecutionException, InterruptedException {
        List<SolarMonitorVo> solarMonitorVos = solarMonitorSelect(solarMonitorQueryDto, languageOption, userName);
        PageInfo<SolarMonitorVo> solarMonitorVoPageInfo = new PageInfo<>();
        solarMonitorVoPageInfo.setTotal(solarMonitorVos.size());
        Integer pageNo = solarMonitorQueryDto.getPageNo();
        Integer pageSize = solarMonitorQueryDto.getPageSize();
        if (null != pageNo && null != pageSize) {
            List<SolarMonitorVo> pageList = PageUtil.getPageList(solarMonitorVos, pageNo, pageSize);
            solarMonitorVoPageInfo.setList(pageList);
        } else {
            solarMonitorVoPageInfo.setList(solarMonitorVos);
        }
        return solarMonitorVoPageInfo;
    }

    @Override
    public List<SolarMonitorVo> solarMonitorSelect(SolarMonitorQueryDto solarMonitorQueryDto, String languageOption, String userName) throws UedmException, ExecutionException, InterruptedException {
        //分域
        List<String> authList = authorizationService.getFullPermissionByResIds(Collections.singletonList(solarMonitorQueryDto.getLogicGroupId()));
        if (CollectionUtils.isEmpty(authList)) {
            log.error("No access permission : {}", solarMonitorQueryDto.getLogicGroupId());
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }
        Double coefficient = carbonReductionService.queryCarbonReductionCoefficient();
        String finalCarbonFactor = null != coefficient ? coefficient.toString() : null;
        // key:pvId;value:siteId
        Map<String, String> pvToSiteMap = solarRevenueService.getPvToSiteMap();
        // key:siteId;value:site
       // Map<String, SiteDsBean> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeans();
        Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeanMap();
        // 有权限的pvId
        List<String> pvIds = solarRevenueService.getPvListByUser(userName, solarMonitorQueryDto.getLogicGroupId());
        log.info("solar monitor get pv param:{}, authIds size:{}", userName, pvIds.size());
        List<String> grainList = Arrays.asList(YEAR, MONTH, DAY);
        // 当天 当月 当年 累积
        List<SolarRevenueBean> solarRevenueBeanListDay = new CopyOnWriteArrayList<>();
        List<SolarRevenueBean> solarRevenueBeanListMonth = new CopyOnWriteArrayList<>();
        List<SolarRevenueBean> solarRevenueBeanListYear = new CopyOnWriteArrayList<>();
        CompletableFuture<List<SolarRevenueBean>> accumulatedPowerCompletableFuture =
                getSolarRevenueOnGrain(pvIds, pvToSiteMap, grainList, solarRevenueBeanListDay, solarRevenueBeanListMonth, solarRevenueBeanListYear);
        // 异步获取 告警状态 发电功率 装机容量
        CompletableFuture<SolarMonitorInfoBean> solarMonitorInfoBeanFuture = getSolarMonitorInfoBeanCompletableFuture(pvIds,pvToSiteMap);
        // 异步获取当天 当月 当年 最大功率
         SolarMonitorMaxPowerBean solarMonitorMaxPowerBean = getSolarMonitorMaxPowerBean(pvIds, grainList, pvToSiteMap);
        // 日 月 年 累计 三个查出来的站点应该是一致的
        Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnDay =
                solarRevenueBeanListDay.parallelStream().collect(Collectors.groupingBy(SolarRevenueBean::getSiteId));
        Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnMonth =
                solarRevenueBeanListMonth.parallelStream().collect(Collectors.groupingBy(SolarRevenueBean::getSiteId));
        Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnYear =
                solarRevenueBeanListYear.parallelStream().collect(Collectors.groupingBy(SolarRevenueBean::getSiteId));
        Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnAccumulated =
                Optional.ofNullable(accumulatedPowerCompletableFuture.get()).orElse(new ArrayList<>())
                        .parallelStream().collect(Collectors.groupingBy(SolarRevenueBean::getSiteId));
        Set<String> siteId = getSiteIdsByPvIds(pvIds, pvToSiteMap);
        log.info("get SolarRevenueBean List on grain over site size{}", siteId.size());
        // 异步获取在线状态
        CompletableFuture<SolarMonitorInfoBean> solarMonitorInfoBeanCompletableFuture = getSolarMonitorInfoBeanCompletableFuture(siteId);
        List<String> currentRecollectionSiteList = solarRecollectionServiceImpl.getCurrentRecollectionSiteList();
        Set<String> currentRecollectionSiteSet = new HashSet<>(currentRecollectionSiteList);
        // 收集补充数据，得到最终结果
        List<SolarMonitorVo> result = new ArrayList<>();
        for (String siteIdKey : siteId) {
            SolarMonitorVo solarMonitorVo = new SolarMonitorVo();
            solarMonitorVo.setSiteId(siteIdKey);
            solarMonitorVo.setPathId(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).toStringPathId());
            solarMonitorVo.setPathName(Optional.ofNullable(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).getPathName()).orElse(EMPTY_STR).trim());
            solarMonitorVo.setLocation(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).getPathName());
            solarMonitorVo.setName(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).getName());
           //solarMonitorVo.setResourceType(Optional.ofNullable(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).getResourceType()).orElse(EMPTY_STR).trim());
            solarMonitorVo.setSiteGmtCreate(siteIdRelationSiteDsBeansMap.getOrDefault(siteIdKey, new FieldEntity()).getGmtCreateToString());
            solarMonitorVo.setSiteRecollectionStatus(currentRecollectionSiteSet.contains(siteIdKey));
            calculateDataOnGrain(siteIdKey, solarRevenueBeanGroupBySiteOnDay, solarRevenueBeanGroupBySiteOnMonth, solarRevenueBeanGroupBySiteOnYear, solarRevenueBeanGroupBySiteOnAccumulated, finalCarbonFactor, solarMonitorVo,languageOption);
            calculateMaxPower(siteIdKey,solarMonitorMaxPowerBean,solarMonitorVo);
            //异步获取 告警状态 发电功率 装机容量
            SolarMonitorInfoBean solarMonitorInfoBean = Optional.ofNullable(solarMonitorInfoBeanFuture.get()).orElse(new SolarMonitorInfoBean());
            solarMonitorVo.setAlarmState(getAlarmStatus(Optional
                    .ofNullable(solarMonitorInfoBean.getAlarmStatusMap()).orElse(new HashMap<>()), siteIdKey));
            solarMonitorVo.setAlarmStateCode(Optional
                    .ofNullable(solarMonitorInfoBean.getAlarmStatusMap()).orElse(new HashMap<>()).get(siteIdKey));
            solarMonitorVo.setGenerationPower(Optional
                    .ofNullable(solarMonitorInfoBean.getGenerationPowerMap()).orElse(new HashMap<>()).get(siteIdKey));
            solarMonitorVo.setInstallationCapacity(Optional
                    .ofNullable(solarMonitorInfoBean.getInstallationCapacityMap()).orElse(new HashMap<>()).get(siteIdKey));
            setEfficiency(solarMonitorVo);
            //异步获取在线状态
            SolarMonitorInfoBean solarMonitorInfoBean1 = Optional.ofNullable(solarMonitorInfoBeanCompletableFuture.get()).orElse(new SolarMonitorInfoBean());
            solarMonitorVo.setOnLine(Optional.ofNullable(solarMonitorInfoBean1.getOnLineStatusMap()).orElse(new HashMap<>()).get(siteIdKey));
            solarMonitorVo.setOnServiceDate(Optional.ofNullable(siteIdRelationSiteDsBeansMap.get(siteIdKey)).orElse(new FieldEntity()).getStartUsingDate());
            solarMonitorVo.setExpirationDate(Optional.ofNullable(siteIdRelationSiteDsBeansMap.get(siteIdKey)).orElse(new FieldEntity()).toStringExpiryDate());
            setLongitudeAndLatitude(Optional.ofNullable(siteIdRelationSiteDsBeansMap.get(siteIdKey)).orElse(new FieldEntity()).getLocation(), solarMonitorVo);
            result.add(solarMonitorVo);
        }
        log.info("SolarMonitorVo Assembly completed size:{}", result.size());
        List<SolarMonitorVo> resultFinal = getSolarMonitorVos(solarMonitorQueryDto, result);
        return resultFinal;
    }

    private void calculateMaxPower(String siteIdKey,SolarMonitorMaxPowerBean solarMonitorMaxPowerBean,SolarMonitorVo solarMonitorVo){
        Map<String, List<SolarMaxPowerBean>> todayMaxPowerMap = solarMonitorMaxPowerBean.getTodayMaxPowerMap();
        Map<String, List<SolarMaxPowerBean>> monthMaxPowerMap = solarMonitorMaxPowerBean.getMonthMaxPowerMap();
        Map<String, List<SolarMaxPowerBean>> yearMaxPowerMap = solarMonitorMaxPowerBean.getYearMaxPowerMap();
        List<SolarMaxPowerBean> todayMaxPowerList = Optional.ofNullable(todayMaxPowerMap.get(siteIdKey)).orElse(new ArrayList<>());
        List<SolarMaxPowerBean> monthMaxPowerList = Optional.ofNullable(monthMaxPowerMap.get(siteIdKey)).orElse(new ArrayList<>());
        List<SolarMaxPowerBean> yearMaxPowerList = Optional.ofNullable(yearMaxPowerMap.get(siteIdKey)).orElse(new ArrayList<>());
        Map<String, List<SolarMaxPowerBean>> todayMaxPowerGroupByPvMap = todayMaxPowerList.parallelStream()
                .collect(Collectors.groupingBy(SolarMaxPowerBean::getPvId));
        Map<String, List<SolarMaxPowerBean>> monthMaxPowerGroupByPvMap = monthMaxPowerList.parallelStream()
                .collect(Collectors.groupingBy(SolarMaxPowerBean::getPvId));
        Map<String, List<SolarMaxPowerBean>> yearMaxPowerGroupByPvMap = yearMaxPowerList.parallelStream()
                .collect(Collectors.groupingBy(SolarMaxPowerBean::getPvId));
        String siteTodayMaxPower = getSiteMaxPower(todayMaxPowerGroupByPvMap);
        String siteMonthMaxPower = getSiteMaxPower(monthMaxPowerGroupByPvMap);
        String siteYearMaxPower =  getSiteMaxPower(yearMaxPowerGroupByPvMap);
        solarMonitorVo.setTodayMaxPower(siteTodayMaxPower);
        solarMonitorVo.setMonthMaxPower(siteMonthMaxPower);
        solarMonitorVo.setYearMaxPower(siteYearMaxPower);
    }

    @NotNull
    private SolarMonitorMaxPowerBean getSolarMonitorMaxPowerBean(List<String> pvIds,
                                                                 List<String> grainList,
                                                                 Map<String, String> pvToSiteMap) {
        SolarMonitorMaxPowerBean solarMonitorMaxPowerBean = new SolarMonitorMaxPowerBean();

        Map<String, List<SolarMaxPowerBean>> solarMaxPowerGroupBySiteOnDay = new ConcurrentHashMap<>();
        Map<String, List<SolarMaxPowerBean>> solarMaxPowerGroupBySiteOnMonth = new ConcurrentHashMap<>();
        Map<String, List<SolarMaxPowerBean>> solarMaxPowerGroupBySiteOnYear = new ConcurrentHashMap<>();
        for (String grain : grainList) {
            SolarMaxPowerQueryBean queryBean = new SolarMaxPowerQueryBean();
            queryBean.setPvIds(pvIds);
            queryBean.setBeginTime(getStartTime(grain));
            queryBean.setEndTime(getCurrentDateTime());
            List<SolarMaxPowerBean> solarMaxPowerList = getSolarMaxPowerList(queryBean);

            solarMaxPowerList.parallelStream().filter(Objects::nonNull).forEach(solarMaxPowerBean -> {
                String siteId = pvToSiteMap.get(solarMaxPowerBean.getPvId());
                if (DAY.equals(grain)) {
                    solarMaxPowerGroupBySiteOnDay.computeIfAbsent(siteId, k -> new CopyOnWriteArrayList<>()).add(solarMaxPowerBean);
                }
                if (MONTH.equals(grain)) {
                    solarMaxPowerGroupBySiteOnMonth.computeIfAbsent(siteId, k -> new CopyOnWriteArrayList<>()).add(solarMaxPowerBean);
                }
                if (YEAR.equals(grain)) {
                    solarMaxPowerGroupBySiteOnYear.computeIfAbsent(siteId, k -> new CopyOnWriteArrayList<>()).add(solarMaxPowerBean);
                }

            });
        }

        solarMonitorMaxPowerBean.setTodayMaxPowerMap(solarMaxPowerGroupBySiteOnDay);
        solarMonitorMaxPowerBean.setMonthMaxPowerMap(solarMaxPowerGroupBySiteOnMonth);
        solarMonitorMaxPowerBean.setYearMaxPowerMap(solarMaxPowerGroupBySiteOnYear);
        return solarMonitorMaxPowerBean;
    }

    @Override
    @NotNull
    public Set<String> getSiteIdsByPvIds(List<String> pvIds, Map<String, String> pvToSiteMap) {
        List<SolarRevenueBean> solarRevenueBeanList = new ArrayList<>();
        for (String pvId : pvIds) {
            if (null != pvToSiteMap.get(pvId)) {
                SolarRevenueBean solarRevenueBean = new SolarRevenueBean();
                solarRevenueBean.setPvId(pvId);
                solarRevenueBean.setSiteId(pvToSiteMap.get(pvId));
                solarRevenueBeanList.add(solarRevenueBean);
            } else {
                log.info("pv no site, pv:{} ", pvId);
            }
        }
        Map<String, List<SolarRevenueBean>> solarRevenueGroupBySite = solarRevenueBeanList.parallelStream()
                .collect(Collectors.groupingBy(SolarRevenueBean::getSiteId));
        Set<String> siteId = solarRevenueGroupBySite.keySet();
        return siteId;
    }

    private String getAlarmStatus(Map<String, String> alarmStatusMap, String siteKey) {
        String alarmStatusCode = alarmStatusMap.get(siteKey);
        if (null == alarmStatusCode) {
            return null;
        }
        if (ZERO_STR.equals(alarmStatusCode)) {
            return NORMAL;
        } else {
            return ALARM;
        }
    }

    /* Started by AICoder, pid:ce4cbs1e9f07fa7146d40b8f70347925f9f98204 */
    @Override
    public void initPmaRegister(long waitTime) {
        int maxRetries = 5; // 设置最大重试次数
        int retryCount = 0; // 当前重试次数
        boolean isRegistered = false; // 标记是否注册成功

        while (retryCount < maxRetries && !isRegistered) {
            try {
                MakeupRegisterDto makeupRegisterDto = new MakeupRegisterDto();
                makeupRegisterDto.setMoc(MocOptional.PV.getId());
                makeupRegisterDto.setSpIdList(Arrays.asList(SOLAR_SMPID_ACCUM_PWR_SUPPLY));
                makeupRpc.makeupRegister(makeupRegisterDto);

                makeupRegisterDto.setMoc(MocOptional.BATTERY.getId());
                makeupRegisterDto.setSpIdList(Arrays.asList(BATTERY_SMPID_CUMULATIVE_DISCHARGE_QUANTITY, BATTERY_SMPID_CUMULATIVE_CHARGE_QUANTITY));
                makeupRpc.makeupRegister(makeupRegisterDto);
                isRegistered = true; // 如果没有异常发生，则认为注册成功
                log.info("================init Pma Register end");
            } catch (Exception e) {
                log.error("================init Runner register pma error, retry count: " + retryCount + ". ================!", e);
                retryCount++; // 增加重试次数
                if (retryCount >= maxRetries) {
                    log.error("================init Runner failed to register pma after " + maxRetries + " attempts. ================!");
                } else {
                    try {
                        Thread.sleep(waitTime); // 等待60秒后再次尝试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // 重新设置中断状态
                    }
                }
            }
        }
    }
    /* Ended by AICoder, pid:ce4cbs1e9f07fa7146d40b8f70347925f9f98204 */
    @NotNull
    private CompletableFuture<SolarMonitorInfoBean> getSolarMonitorInfoBeanCompletableFuture(Set<String> siteId) {
        CompletableFuture<SolarMonitorInfoBean> solarMonitorInfoBeanCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SolarMonitorInfoBean solarMonitorInfoBean = new SolarMonitorInfoBean();
            List<String> siteIdList = new ArrayList<>(siteId);
            try {
                Map<String, String> onLineStatusMap = monitorManagerRpc.queryObjectCommunicationStatus(siteIdList);
                solarMonitorInfoBean.setOnLineStatusMap(onLineStatusMap);
                log.info("solar monitor get onLine site size:{}", Optional.ofNullable(onLineStatusMap).orElse(new HashMap<>()).size());
            } catch (UedmException e) {
                log.error("solarMonitorSelect get onLineStatusMap UedmException, reason:{}", e.getMessage());
            } catch (Exception e) {
                log.error("solarMonitorSelect get onLineStatusMap Exception, reason:{}", e.getMessage());
            }
            return solarMonitorInfoBean;
        }, solarMonitorThreadPool);
        return solarMonitorInfoBeanCompletableFuture;
    }

    @NotNull
    private CompletableFuture<SolarMonitorInfoBean> getSolarMonitorInfoBeanCompletableFuture(List<String> pvIds,Map<String, String> pvToSiteMap) {
        CompletableFuture<SolarMonitorInfoBean> solarMonitorInfoBeanFuture = CompletableFuture.supplyAsync(() -> {
            SolarMonitorInfoBean solarMonitorInfoBean = new SolarMonitorInfoBean();
            try {
                Map<String, List<String>> sitePvRelMap = getSitePvRelMap(pvIds);
                List<SitePvOutTotalPowerBean> sitePvOutTotalPower = getSitePvOutTotalPower(sitePvRelMap);
                List<SitePpaMonitorAlarmBean> sitePpaMonitorAlarm = getSitePpaMonitorAlarm(sitePvRelMap);
                Map<String, String> sitePvOutTotalPowerMap = sitePvOutTotalPower.stream()
                        .collect(HashMap::new, (map, item) -> map.put(item.getId(), item.getPvOutTotalPower()), HashMap::putAll);
                log.info("sitePvOutTotalPowerMap size:{}", Optional.ofNullable(sitePvOutTotalPowerMap).orElse(new HashMap<>()).size());
                solarMonitorInfoBean.setGenerationPowerMap(sitePvOutTotalPowerMap);
                Map<String, String> sitePpaMonitorAlarmMap = sitePpaMonitorAlarm.stream().collect(HashMap::new,
                        (map, item) -> map.put(item.getId(), convertInteger(item.getSeverity())), HashMap::putAll);
                log.info("sitePpaMonitorAlarmMap size:{}", Optional.ofNullable(sitePpaMonitorAlarmMap).orElse(new HashMap<>()).size());
                solarMonitorInfoBean.setAlarmStatusMap(sitePpaMonitorAlarmMap);
                Map<String, String> siteInstallationCapacity = getSiteInstallationCapacity(pvIds,pvToSiteMap);
                solarMonitorInfoBean.setInstallationCapacityMap(siteInstallationCapacity);
            } catch (Exception e) {
                log.error("getSolarMonitorInfoBeanCompletableFuture Exception:{}", e.getMessage());
            }
            return solarMonitorInfoBean;
        }, solarMonitorThreadPool);
        return solarMonitorInfoBeanFuture;
    }

    private String convertInteger(Integer integer) {
        if (null != integer) {
            return integer.toString();
        } else {
            return null;
        }
    }

    private void calculateDataOnGrain(String siteIdKey, Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnDay,
                                      Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnMonth,
                                      Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnYear,
                                      Map<String, List<SolarRevenueBean>> solarRevenueBeanGroupBySiteOnAccumulated,
                                      String finalCarbonFactor, SolarMonitorVo solarMonitorVo,String languageOption) {
        List<SolarRevenueBean> solarRevenueBeanDayList = Optional.ofNullable(solarRevenueBeanGroupBySiteOnDay.get(siteIdKey)).orElse(new ArrayList<>());
        List<SolarRevenueBean> solarRevenueBeanMonthList = Optional.ofNullable(solarRevenueBeanGroupBySiteOnMonth.get(siteIdKey)).orElse(new ArrayList<>());
        List<SolarRevenueBean> solarRevenueBeanYearList = Optional.ofNullable(solarRevenueBeanGroupBySiteOnYear.get(siteIdKey)).orElse(new ArrayList<>());
        List<SolarRevenueBean> solarRevenueBeanAccumulatedList = Optional.ofNullable(solarRevenueBeanGroupBySiteOnAccumulated.get(siteIdKey)).orElse(new ArrayList<>());
        calculateDataOnGrain(solarRevenueBeanDayList, finalCarbonFactor, solarMonitorVo::setTodayPower,
                solarMonitorVo::setTodayRevenue, solarMonitorVo::setTodaySavings, solarMonitorVo::setTodayCO2);
        calculateDataOnGrain(solarRevenueBeanMonthList, finalCarbonFactor, solarMonitorVo::setMonthPower,
                solarMonitorVo::setMonthRevenue, solarMonitorVo::setMonthSavings, solarMonitorVo::setMonthCO2);
        calculateDataOnGrain(solarRevenueBeanYearList, finalCarbonFactor, solarMonitorVo::setYearPower,
                solarMonitorVo::setYearRevenue, solarMonitorVo::setYearSavings, solarMonitorVo::setYearCO2);
        calculateDataOnGrain(solarRevenueBeanAccumulatedList, finalCarbonFactor, solarMonitorVo::setAccumulatedPower,
                solarMonitorVo::setAccumulatedRevenue, solarMonitorVo::setAccumulatedSavings, solarMonitorVo::setAccumulatedCO2);
        // 补充计费方式,计费方式设置的最小粒度为站点,故取天表中最新一条数据的计费方式作为站点生效的计费方式
        if (org.apache.commons.collections.CollectionUtils.isEmpty(solarRevenueBeanDayList)){
            String nameByType = i18nUtils.getMapFieldByLanguageOption(SolarRateTypeEnums.getNameByType(null), languageOption);
            solarMonitorVo.setRateType(nameByType);
        } else {
            List<SolarRevenueBean> reverseSortedList = solarRevenueBeanDayList.stream()
                    .sorted(Comparator.comparing(SolarRevenueBean::getRecordDate).reversed())
                    .collect(Collectors.toList());
            SolarRevenueBean solarRevenueBean = reverseSortedList.get(0);
            String nameByType = i18nUtils.getMapFieldByLanguageOption(SolarRateTypeEnums.getNameByType(solarRevenueBean.getRateType()), languageOption);
            solarMonitorVo.setRateType(nameByType);
        }

    }

       public void setLongitudeAndLatitude(ResourceBaseEntity.Point location, SolarMonitorVo solarMonitorVo) {
        if(location!=null){
            /* Started by AICoder, pid:n35ccdb44e0445914c79092ba0a8180234b46771 */
            if (location.getLatitude() != null && !Double.isNaN(location.getLatitude())) {
                BigDecimal latitude = new BigDecimal(location.getLatitude());
                solarMonitorVo.setLatitude(latitude.setScale(SCALE_SIX, RoundingMode.DOWN).toPlainString());
            }

            if (location.getLongitude() != null && !Double.isNaN(location.getLongitude())) {
                BigDecimal longitude = new BigDecimal(location.getLongitude());
                solarMonitorVo.setLongitude(longitude.setScale(SCALE_SIX, RoundingMode.DOWN).toPlainString());
            }
            /* Ended by AICoder, pid:o35ccfb44et445914c79092ba0a8180234b46771 */
        }
    }

    /* Started by AICoder, pid:n6cb1s8a81e2c971428b08d0504d1e358545b2ad */
    public  void setEfficiency(SolarMonitorVo solarMonitorVo) {
        BigDecimal installationCapacityBigDecimal = new BigDecimal(Optional.ofNullable(solarMonitorVo.getInstallationCapacity()).orElse(ZERO_STR));
        if (installationCapacityBigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            solarMonitorVo.setEfficiency(null);
            solarMonitorVo.setTodayMaxEfficiency(null);
            solarMonitorVo.setMonthMaxEfficiency(null);
            solarMonitorVo.setYearMaxEfficiency(null);
        } else {
            solarMonitorVo.setEfficiency(calculateEfficiency(installationCapacityBigDecimal, solarMonitorVo.getGenerationPower()));
            solarMonitorVo.setTodayMaxEfficiency(calculateEfficiency(installationCapacityBigDecimal, solarMonitorVo.getTodayMaxPower()));
            solarMonitorVo.setMonthMaxEfficiency(calculateEfficiency(installationCapacityBigDecimal, solarMonitorVo.getMonthMaxPower()));
            solarMonitorVo.setYearMaxEfficiency(calculateEfficiency(installationCapacityBigDecimal, solarMonitorVo.getYearMaxPower()));
        }
    }


    private String calculateEfficiency(BigDecimal installationCapacity, String power) {
        if (power == null || installationCapacity.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        } else {
            BigDecimal efficiencyResult = new BigDecimal(power).divide(installationCapacity, SCALE_FOUR, RoundingMode.HALF_UP);
            return efficiencyResult.toPlainString();
        }
    }
    /* Ended by AICoder, pid:n6cb1s8a81e2c971428b08d0504d1e358545b2ad */

    @NotNull
    private List<SolarMonitorVo> getSolarMonitorVos(SolarMonitorQueryDto solarMonitorQueryDto, List<SolarMonitorVo> result) {
        Stream<SolarMonitorVo> monitorVoStream = result.parallelStream();
        if (DESC.equals(solarMonitorQueryDto.getSort())) {
            monitorVoStream = solarMonitorDataOrderDesc(solarMonitorQueryDto, monitorVoStream);
        } else {
            monitorVoStream = solarMonitorDataOrderAsc(solarMonitorQueryDto, monitorVoStream);
        }
        List<SolarMonitorVo> resultFinal = monitorVoStream.collect(Collectors.toList());
        return resultFinal;
    }

    @NotNull
    private CompletableFuture<List<SolarRevenueBean>> getSolarRevenueOnGrain(List<String> pvIds, Map<String, String> pvToSiteMap, List<String> grainList, List<SolarRevenueBean> solarRevenueBeanListDay, List<SolarRevenueBean> solarRevenueBeanListMonth, List<SolarRevenueBean> solarRevenueBeanListYear) {
        // 异步获取 累计发电量 发电收益等
        CompletableFuture<List<SolarRevenueBean>> accumulatedPowerCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SolarRevenueQueryBean solarRevenueQueryBean = new SolarRevenueQueryBean();
            solarRevenueQueryBean.setPvIds(pvIds);
            solarRevenueQueryBean.setGrain(YEAR);
            List<SolarRevenueBean> solarRevenueList = solarRevenueService.getSolarRevenueList(solarRevenueQueryBean);
            Map<String, List<SolarRevenueBean>> accumulatedPowerGroupByPvId = solarRevenueList.stream()
                    .collect(Collectors.groupingBy(SolarRevenueBean::getPvId));
            List<SolarRevenueBean> solarRevenueBeanGroup = new CopyOnWriteArrayList<>();
            accumulatedPowerGroupByPvId.values().parallelStream().forEach(solarRevenueBean ->
                    calculateAccumulatedData(solarRevenueBean, solarRevenueBeanGroup, pvToSiteMap));
            return solarRevenueBeanGroup;
        }, solarMonitorThreadPool);
        grainList.parallelStream().forEach(grain -> {
            SolarRevenueQueryBean solarRevenueQueryBean = new SolarRevenueQueryBean();
            solarRevenueQueryBean.setPvIds(pvIds);
            solarRevenueQueryBean.setGrain(grain);
            solarRevenueQueryBean.setBeginTime(getNowDataByGrain(grain));
            solarRevenueQueryBean.setEndTime(getNowDataByGrain(grain));
            List<SolarRevenueBean> solarRevenueList = solarRevenueService.getSolarRevenueList(solarRevenueQueryBean);
            solarRevenueList.parallelStream().forEach(solarRevenueBean -> solarRevenueBean.setSiteId(pvToSiteMap.get(solarRevenueBean.getPvId())));
            if (DAY.equals(grain)) {
                solarRevenueBeanListDay.addAll(solarRevenueList);
            }
            if (MONTH.equals(grain)) {
                solarRevenueBeanListMonth.addAll(solarRevenueList);
            }
            if (YEAR.equals(grain)) {
                solarRevenueBeanListYear.addAll(solarRevenueList);
            }
        });
        return accumulatedPowerCompletableFuture;
    }

    @NotNull
    private Stream<SolarMonitorVo> solarMonitorDataOrderAsc(SolarMonitorQueryDto solarMonitorQueryDto, Stream<SolarMonitorVo> monitorVoStream) {
        String order = Optional.ofNullable(solarMonitorQueryDto.getOrder()).orElse(EMPTY_STR);
        switch (order) {
            case ON_LINE:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getOnLine, solarRevenueService.getStringComparator())));
                break;
            case ALARM_STATE_CODE:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getAlarmStateCode, solarRevenueService.getStringComparator())));
                break;
            case GENERATION_POWER:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getGenerationPower, solarRevenueService.getStringComparator())));
                break;
            case INSTALLATION_CAPACITY:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getInstallationCapacity, solarRevenueService.getStringComparator())));
                break;
            case EFFICIENCY:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getEfficiency, solarRevenueService.getStringComparator())));
                break;
            default:
                Collator collator = Collator.getInstance();
                collator.setStrength(Collator.TERTIARY);
                monitorVoStream = monitorVoStream.sorted(Comparator.nullsLast(Comparator.comparing(SolarMonitorVo::getPathName, collator)));
        }
        return monitorVoStream;
    }

    @NotNull
    private Stream<SolarMonitorVo> solarMonitorDataOrderDesc(SolarMonitorQueryDto solarMonitorQueryDto, Stream<SolarMonitorVo> monitorVoStream) {
        String order = Optional.ofNullable(solarMonitorQueryDto.getOrder()).orElse(EMPTY_STR);
        switch (order) {
            case ON_LINE:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getOnLine, solarRevenueService.getStringComparator2()).reversed()));
                break;
            case ALARM_STATE_CODE:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getAlarmStateCode, solarRevenueService.getStringComparator2()).reversed()));
                break;
            case GENERATION_POWER:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getGenerationPower, solarRevenueService.getStringComparator2()).reversed()));
                break;
            case INSTALLATION_CAPACITY:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getInstallationCapacity, solarRevenueService.getStringComparator2()).reversed()));
                break;
            case EFFICIENCY:
                monitorVoStream = monitorVoStream
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(SolarMonitorVo::getEfficiency, solarRevenueService.getStringComparator2()).reversed()));
                break;
            default:
                Collator collator = Collator.getInstance();
                collator.setStrength(Collator.TERTIARY);
                monitorVoStream = monitorVoStream.sorted(Comparator.nullsLast(Comparator.comparing(SolarMonitorVo::getPathName, collator).reversed()));
        }
        return monitorVoStream;
    }

    private void calculateDataOnGrain(List<SolarRevenueBean> solarRevenueBeanDayList, String finalCarbonFactor, Consumer<String> consumerPower,
                                      Consumer<String> consumerRevenue, Consumer<String> consumerSavings, Consumer<String> consumerCO2) {
        AtomicReference<BigDecimal> totalSolarRevenueResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalGridFeeResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalEnergyGenerationResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalSavingsResult = new AtomicReference<>();
        solarRevenueBeanDayList.parallelStream().forEach(solarRevenueBean -> {
            totalSolarRevenueResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSolarRevenue()));
            totalGridFeeResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalGridFee()));
            totalEnergyGenerationResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalEnergyGeneration()));
            totalSavingsResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSavings()));
        });

        consumerPower.accept(getNumStr(totalEnergyGenerationResult.get()));
        consumerRevenue.accept(getNumStr(totalSolarRevenueResult.get()));
        consumerSavings.accept(getNumStr(totalSavingsResult.get()));
        if (null != totalEnergyGenerationResult.get() && null != finalCarbonFactor) {
            BigDecimal carbonReduction = totalEnergyGenerationResult.get()
                    .multiply(new BigDecimal(finalCarbonFactor)).setScale(SCALE, RoundingMode.HALF_UP);
            consumerCO2.accept(getNumStr(carbonReduction));
        }
    }

    private void calculateAccumulatedData(List<SolarRevenueBean> solarRevenueBeanList,
                                          List<SolarRevenueBean> solarRevenueBeanGroup, Map<String, String> pvToSiteMap) {
        AtomicReference<BigDecimal> totalSolarRevenueResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalGridFeeResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalEnergyGenerationResult = new AtomicReference<>();
        AtomicReference<BigDecimal> totalSavingsResult = new AtomicReference<>();
        SolarRevenueBean solarRevenueBeanNew = new SolarRevenueBean();
        solarRevenueBeanList.parallelStream().forEach(solarRevenueBean -> {
            solarRevenueBeanNew.setId(solarRevenueBean.getId());
            solarRevenueBeanNew.setPvId(solarRevenueBean.getPvId());
            solarRevenueBeanNew.setSiteId(pvToSiteMap.get(solarRevenueBean.getPvId()));
            totalSolarRevenueResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSolarRevenue()));
            totalGridFeeResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalGridFee()));
            totalEnergyGenerationResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalEnergyGeneration()));
            totalSavingsResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSavings()));
        });
        solarRevenueBeanNew.setTotalSolarRevenue(getNumStr(totalSolarRevenueResult.get()));
        solarRevenueBeanNew.setTotalGridFee(getNumStr(totalGridFeeResult.get()));
        solarRevenueBeanNew.setTotalEnergyGeneration(getNumStr(totalEnergyGenerationResult.get()));
        solarRevenueBeanNew.setTotalSavings(getNumStr(totalSavingsResult.get()));
        solarRevenueBeanGroup.add(solarRevenueBeanNew);
    }

    private String getNumStr(BigDecimal bigDecimal) {
        if (null != bigDecimal) {
            return bigDecimal.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
        } else {
            return null;
        }
    }

    private String getNowDataByGrain(String grain) {
        LocalDate currentDate = LocalDate.now();
        if (YEAR.equals(grain)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_6);
            return currentDate.format(formatter);
        }
        if (MONTH.equals(grain)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_3);
            return currentDate.format(formatter);
        }
        if (DAY.equals(grain)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_2);
            return currentDate.format(formatter);
        }
        return EMPTY_STR;
    }

    private BigDecimal add(BigDecimal num1, String num2) {
        if (StringUtils.isNotEmpty(num2)) {
            if (null == num1) {
                num1 = BigDecimal.ZERO;
            }
            num1 = num1.add(new BigDecimal(num2));
        }
        return num1;
    }

    /**
     * 太阳能站点通信状态、告警状态、发电功率、装机容量---查询统计
     * 根据逻辑组id，查询统计太阳能站点通信状态、告警状态、发电功率、装机容量
     *
     * @param logicGroupId
     * @param userName
     * @param languageOption
     * @return
     */
    @Override
    public SolarMonitorStatisticsVo queryAlarmAndOtherStatistics(String logicGroupId, String userName, String languageOption) throws UedmException {
        if (StringUtils.isBlank(logicGroupId)) {
            logicGroupId = ROOT;
        }
        //分域
        List<String> authList = authorizationService.getFullPermissionByResIds(Collections.singletonList(logicGroupId));
        if (CollectionUtils.isEmpty(authList)) {
            log.error("No access permission : {}", logicGroupId);
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }
        try {
            Map<String, String> headers = HeaderUtils.buildUserNameHeaders(Tools.escape(userName));
            List<String> positions = new ArrayList<>(1);
            positions.add(logicGroupId);

            //  获取有权限的太阳能监控对象列表 todo:保留权限
            List<MonitorObjectDsBean> monitorObjectList = configurationManagerRpcImpl.selectAuthMoByMocAndPosition(new LogicIdAndMocQueryBean(positions, MocOptional.PV.getId()), headers);
            log.debug("monitorObjectList: {}", jsonService.objectToJson(monitorObjectList));

            List<String> pvMonitorObjectIds = new ArrayList<>();
            if (CollectionUtils.isEmpty(monitorObjectList)) {
                log.error("SolarMonitorServiceImpl queryAlarmAndOtherStatistics : user {} has no authority site and pvObject.", userName);
                return null;
            } else {
                pvMonitorObjectIds = monitorObjectList.stream().map(MonitorObjectDsBean::getId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                log.info("SolarMonitorServiceImpl queryAlarmAndOtherStatistics pvMonitorObjectIds size: {}", pvMonitorObjectIds.size());
            }

            // 查询太阳能监控对象Id和站点关联关系
            Map<String, String> pvToSiteMap = solarRevenueService.getPvToSiteMap();

            //  获取太阳能站点与其下监控对象映射
            Map<String, List<String>> siteMonitorObjectMap = getSitePvRelMap(pvMonitorObjectIds);
            log.debug("siteMonitorObjectMap : {}, size = {}", jsonService.objectToJson(siteMonitorObjectMap), siteMonitorObjectMap.size());
            List<String> solarSiteIds = new ArrayList<>(siteMonitorObjectMap.keySet());
            log.debug("SolarMonitorServiceImpl queryAlarmAndOtherStatistics has-auth solarSiteIds:{}, size={}", jsonService.objectToJson(solarSiteIds), solarSiteIds.size());

            SolarMonitorStatisticsVo vo = new SolarMonitorStatisticsVo();
            log.info("SolarMonitorServiceImpl queryAlarmAndOtherStatistics SolarMonitorStatisticsVo result package start.");
            //  站点总数
            vo.setTotalSite(solarSiteIds.size());
            //  离线站点数
            vo.setTotalOfflineSite(getSolarMonitorTotalOfflineSiteQuantity(solarSiteIds));
            //  总功率
            vo.setTotalPower(getSolarMonitorTotalPower(siteMonitorObjectMap));
            //  总装机容量
            vo.setTotalCapacity(getSolarMonitorTotalCapacity(pvMonitorObjectIds,pvToSiteMap));
            //  告警
            vo.setAlarmInfoVo(getSolarMonitorAlarmInfo(siteMonitorObjectMap));
            return vo;
        } catch (Exception e) {
            log.error("SolarMonitorServiceImpl queryAlarmAndOtherStatistics error", e);
            throw new UedmException(-305, "SolarMonitorServiceImpl queryAlarmAndOtherStatistics failed.");
        }
    }

    /**
     * 获取离线站点总数
     * @param siteIds
     * @return offlineSiteQuantity
     * @throws UedmException
     */
    private Integer getSolarMonitorTotalOfflineSiteQuantity(List<String> siteIds) throws UedmException {
        log.info("getSolarMonitorTotalOfflineSiteQuantity starts.");
        Integer offlineSiteQuantity = 0;
        //通过RPC获取太阳能站点在线离线状态,key为站点Id，Value为状态-0:正常
        Map<String, String> objectCommunicationStatusMap = monitorManagerRpc.queryObjectCommunicationStatus(siteIds);
        log.debug("objectCommunicationStatusMap : {}, size = {}", jsonService.objectToJson(objectCommunicationStatusMap), objectCommunicationStatusMap.size());
        for (Map.Entry<String, String> siteStatus : objectCommunicationStatusMap.entrySet()) {
            if (siteStatus.getValue().equals(OFFLINE_1) || siteStatus.getValue().equals(OFFLINE_2)) {
                offlineSiteQuantity++;
            }
        }

        return offlineSiteQuantity;
    }

    /**
     * 获取站点总功率（单位kWh，两位小数）
     * @param siteMonitorObjectMap
     * @return totalPower
     * @throws UedmException
     */
    private String getSolarMonitorTotalPower(Map<String, List<String>> siteMonitorObjectMap) {
        log.info("getSolarMonitorTotalPower starts.");
        List<SitePvOutTotalPowerBean> sitePvOutTotalPowerList = getSitePvOutTotalPower(siteMonitorObjectMap);
        List<String> powerList = sitePvOutTotalPowerList.stream().map(SitePvOutTotalPowerBean::getPvOutTotalPower).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(powerList)) {
            return null;
        }
        BigDecimal totalPower = BigDecimal.ZERO;
        for (String power : powerList) {
            totalPower = totalPower.add(new BigDecimal(power));
        }
        return totalPower.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 获取站点总装机容量（单位kWh，两位小数）
     * @param pvMoIds
     * @return totalCapacity
     */
    private String getSolarMonitorTotalCapacity(List<String> pvMoIds,Map<String, String> pvToSiteMap) {
        log.info("getSolarMonitorTotalCapacity starts.");
        Map<String, String> siteInstallationCapacityMap = getSiteInstallationCapacity(pvMoIds,pvToSiteMap);
        List<String> capacityList = siteInstallationCapacityMap.values().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(capacityList)) {
            return null;
        }
        BigDecimal totalCapacity = BigDecimal.ZERO;
        for (String capacity : capacityList) {
            totalCapacity = totalCapacity.add(new BigDecimal(capacity));
        }
        return totalCapacity.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
    }

    private AlarmInfoVo getSolarMonitorAlarmInfo(Map<String, List<String>> siteMonitorObjectMap) throws UedmException {
        log.info("getSolarMonitorAlarmInfo starts.");
        AlarmInfoVo alarmInfoVo = new AlarmInfoVo();

        List<SitePpaMonitorAlarmBean> list = getSitePpaMonitorAlarm(siteMonitorObjectMap);
        log.debug("getSolarMonitorAlarmInfo List<SitePpaMonitorAlarmBean> is {}", jsonService.objectToJson(list));
        if (CollectionUtils.isEmpty(list)) {
            return alarmInfoVo;
        }
        //  筛选站点本身或其下监控对象存在告警信息的站点
        list = list.stream().filter(bean -> !bean.getSeverity().equals(CommonConst.NO_ALARM)).collect(Collectors.toList());
        log.debug("getSolarMonitorAlarmInfo after filter List<SitePpaMonitorAlarmBean> is {}", jsonService.objectToJson(list));
        //  获取全部站点级别告警统计信息
        List<PpaMonitorAlarmBean> siteLevel = list.stream()
                .flatMap(bean -> bean.getSiteStatisticsSeverities().parallelStream()).collect(Collectors.toList());
        Map<Integer, Integer> siteLevelCountMap = getCountBySeverityLevel(siteLevel);
        //  获取全部监控对象级别告警统计信息
        List<PpaMonitorAlarmBean> pvObjectLevel = list.stream()
                .flatMap(bean -> bean.getPvStatisticsSeverities().parallelStream()).collect(Collectors.toList());
        Map<Integer, Integer> pvLevelCountMap = getCountBySeverityLevel(pvObjectLevel);
        //  合并站点及监控对象级别告警统计信息
        Stream<Map.Entry<Integer, Integer>> concat = Stream.concat(siteLevelCountMap.entrySet().stream(), pvLevelCountMap.entrySet().stream());
        Map<Integer, Integer> levelCountMap = concat.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));

        alarmInfoVo = packageAlarmInfoVo(levelCountMap);
        //  告警站点总数
        alarmInfoVo.setTotalAlarmSite(list.size());
        return alarmInfoVo;
    }

    private AlarmInfoVo packageAlarmInfoVo(Map<Integer, Integer> levelCountMap) {
        log.info("packageAlarmInfoVo starts.");
        AlarmInfoVo alarmInfoVo = new AlarmInfoVo();
        AlarmQuantityPercentageByLevelVo critical = new AlarmQuantityPercentageByLevelVo();
        AlarmQuantityPercentageByLevelVo major = new AlarmQuantityPercentageByLevelVo();
        AlarmQuantityPercentageByLevelVo minor = new AlarmQuantityPercentageByLevelVo();
        AlarmQuantityPercentageByLevelVo warning = new AlarmQuantityPercentageByLevelVo();

        Integer totalAlarm = 0;
        Integer criticalCount = 0;
        Integer majorCount = 0;
        Integer minorCount = 0;
        Integer warningCount = 0;

        for (Map.Entry<Integer, Integer> entry : levelCountMap.entrySet()) {
            totalAlarm += entry.getValue();
            if (entry.getKey().equals(AlarmLevelEnum.critical.getId())) {
                criticalCount += entry.getValue();
            } else if (entry.getKey().equals(AlarmLevelEnum.major.getId())) {
                majorCount += entry.getValue();
            } else if (entry.getKey().equals(AlarmLevelEnum.minor.getId())) {
                minorCount += entry.getValue();
            } else if (entry.getKey().equals(AlarmLevelEnum.warning.getId())) {
                warningCount += entry.getValue();
            }
        }

        log.info("calculate percentage starts.");
        String criticalPercentage = getAlarmLevelPercentage(criticalCount, totalAlarm);
        String majorPercentage = getAlarmLevelPercentage(majorCount, totalAlarm);
        String minorPercentage = getAlarmLevelPercentage(minorCount, totalAlarm);
        String warningPercentage = getAlarmLevelPercentage(warningCount, totalAlarm);

        log.info("set alarmInfoVo starts.");
        critical.setValue(criticalCount);
        critical.setPercent(criticalPercentage);
        major.setValue(majorCount);
        major.setPercent(majorPercentage);
        minor.setValue(minorCount);
        minor.setPercent(minorPercentage);
        warning.setValue(warningCount);
        warning.setPercent(warningPercentage);

        alarmInfoVo.setTotalAlarm(totalAlarm);
        alarmInfoVo.setCritical(critical);
        alarmInfoVo.setMajor(major);
        alarmInfoVo.setMinor(minor);
        alarmInfoVo.setWarning(warning);
        return alarmInfoVo;
    }

    private Map<Integer, Integer> getCountBySeverityLevel(List<PpaMonitorAlarmBean> list) {
        Map<Integer, Integer> map = new HashMap<>(4);

        Map<Integer, List<PpaMonitorAlarmBean>> levelToCountMap = list.stream().collect(Collectors.groupingBy(PpaMonitorAlarmBean::getAlarmSeverity));
        for (Map.Entry<Integer, List<PpaMonitorAlarmBean>> entry : levelToCountMap.entrySet()) {
            int sum = entry.getValue().stream().mapToInt(PpaMonitorAlarmBean::getTotal).sum();
            map.put(entry.getKey(), sum);
        }

        return map;
    }

    private String getAlarmLevelPercentage(Integer value, Integer total) {
        if (value == null || total == null || total.equals(0)) {
            return null;
        }

        BigDecimal val = new BigDecimal(100);
        String percent = val.multiply(new BigDecimal(value)).divide(new BigDecimal(total), SCALE, RoundingMode.HALF_UP).toPlainString();
        return percent + "%";
    }

    /**
     * 太阳能发电量和收益---查询统计
     * 根据时间和逻辑组id，查询统计太阳能发电量和收益
     *
     * @param dto
     * @param languageOption
     * @param userName
     * @return SolarRevenueOverviewVo
     */
    @Override
    public SolarMonitorRevenueVo queryStatistics(SolarRevenueOverviewDto dto, String userName, String languageOption) throws UedmException {
        if (dto == null || checkNotAllGrainTimeEmpty(dto)) {
            log.error("param error : dto is null or not-all-grain query has no time param.");
            return null;
        }

        if (StringUtils.isBlank(dto.getLogicGroupId())) {
            dto.setLogicGroupId(ROOT);
        }
        //分域
        List<String> authList = authorizationService.getFullPermissionByResIds(Collections.singletonList(dto.getLogicGroupId()));
        if (CollectionUtils.isEmpty(authList)) {
            log.error("No access permission : {}", dto.getLogicGroupId());
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }

        try {
            String grain = TimeGrainEnum.getTableNameByCode(dto.getGrain());
            if (StringUtils.isEmpty(grain)) {
                log.error("solarRevenueOverview, grain err: {}", dto.getGrain());
                return null;
            }
            // 获取有权限的太阳能监控对象列表
            List<String> pvMonitorObjectIds = new ArrayList<>();
            List<MonitorObjectDsBean> monitorObjectList = getPvMonitorObjectDsBeanListByUser(userName, dto.getLogicGroupId());
            if (CollectionUtils.isEmpty(monitorObjectList)) {
                log.error("solarRevenueOverview getPvListByUser userName: {}, no authority", userName);
            } else {
                pvMonitorObjectIds = monitorObjectList.stream()
                        .map(MonitorObjectDsBean::getId)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());
                log.info("solarRevenueOverview pvIdSize: {}", pvMonitorObjectIds.size());
            }

//            // 根据用户权限获取太阳能峰谷收益统计信息
//            SolarRevenueOverviewVo preVo = getSolarRevenueInfoByUser(dto, pvMonitorObjectIds);
//
//            return packageSolarMonitorRevenueVo(preVo);
             // 根据用户权限获取太阳能峰谷收益统计信息
            return getSolarMonitorRevenueVoByUser(dto, pvMonitorObjectIds);

        } catch (Exception e) {
            log.error("SolarMonitorServiceImpl queryStatistics error", e);
            throw new UedmException(-305, "Function queryStatistics failed.");
        }
    }

    /**
     * 非累计时间粒度需要检查开始结束时间
     * 若选择时间粒度为年/月/日粒度，则检查开始结束时间是否有查询参数
     * 若无开始结束时间，返回true
     *
     * @param dto
     * @return boolean
     */
    private boolean checkNotAllGrainTimeEmpty(SolarRevenueOverviewDto dto) {
        List<String> notAllGrainList = new ArrayList<>(3);
        notAllGrainList.add(TimeGrainEnum.YEAR.getCode());
        notAllGrainList.add(TimeGrainEnum.MONTH.getCode());
        notAllGrainList.add(TimeGrainEnum.DAY.getCode());

        if (notAllGrainList.contains(dto.getGrain())) {
            if(StringUtils.isBlank(dto.getBeginTime()) || StringUtils.isBlank(dto.getEndTime())) {
                log.error("param err : not-all-grain query has no time param.");
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private List<MonitorObjectDsBean> getPvMonitorObjectDsBeanListByUser(String userName, String position) throws UedmException {

        Map<String, String> headers = HeaderUtils.buildUserNameHeaders(Tools.escape(userName));

        List<String> positions = new ArrayList<>();
        if (!StringUtils.isEmpty(position)) {
            positions.add(position);
        }
        //todo：保留权限
        return configurationManagerRpcImpl.selectAuthMoByMocAndPosition(new LogicIdAndMocQueryBean(positions, MocOptional.PV.getId()), headers);
    }

    private SolarRevenueOverviewVo getSolarRevenueInfoByUser(SolarRevenueOverviewDto dto, List<String> pvIds) {
        List<SolarRevenueBean> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(pvIds)) {
            log.error("SolarMonitorServiceImpl getSolarRevenueInfoByUser pvIdList is empty");
        } else {
            String grain = dto.getGrain();
            String position = dto.getLogicGroupId();
            LocalDateTime localDateTime = LocalDateTime.now();
            DateTimeFormatter pattern = DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_7);
            String endTime = localDateTime.truncatedTo(ChronoUnit.HOURS).format(pattern);
            log.info("SolarMonitorServiceImpl getSolarRevenueInfoByUser grain: {}, position: {}, beginTime: {}, endTime: {}",
                    grain, position, dto.getBeginTime(), endTime);

            // 获取太阳能峰谷收益
            if (ALL.equals(grain)) {
                result = solarRevenueService.getSolarRevenueListAll(pvIds);
            } else {
                SolarRevenueQueryBean queryBean = new SolarRevenueQueryBean();
                queryBean.setPvIds(pvIds);
                queryBean.setGrain(grain);
                queryBean.setBeginTime(dto.getBeginTime());
                queryBean.setEndTime(endTime);

                result = solarRevenueService.getSolarRevenueList(queryBean);
            }
        }

        // 计算减碳量等统计信息
        return solarRevenueService.calcResult(result,true);
    }

    private SolarMonitorRevenueVo getSolarMonitorRevenueVoByUser(SolarRevenueOverviewDto dto, List<String> pvIds) {
        List<SolarRevenueBean> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(pvIds)) {
            log.error("SolarMonitorServiceImpl getSolarMonitorRevenueVoByUser pvIdList is empty");
        } else {
            String grain = dto.getGrain();
            String position = dto.getLogicGroupId();
            LocalDateTime localDateTime = LocalDateTime.now();
            DateTimeFormatter pattern = DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_7);
            String endTime = localDateTime.truncatedTo(ChronoUnit.HOURS).format(pattern);
            log.info("SolarMonitorServiceImpl getSolarMonitorRevenueVoByUser grain: {}, position: {}, beginTime: {}, endTime: {}",
                    grain, position, dto.getBeginTime(), endTime);

            // 获取太阳能峰谷收益
            if (ALL.equals(grain)) {
                result = solarRevenueService.getSolarRevenueListAll(pvIds);
            } else {
                SolarRevenueQueryBean queryBean = new SolarRevenueQueryBean();
                queryBean.setPvIds(pvIds);
                queryBean.setGrain(grain);
                queryBean.setBeginTime(dto.getBeginTime());
                queryBean.setEndTime(endTime);

                result = solarRevenueService.getSolarRevenueList(queryBean);
            }
        }

        // 计算减碳量等统计信息
        return solarRevenueService.calculateResult(result);
    }

    private SolarMonitorRevenueVo packageSolarMonitorRevenueVo(SolarRevenueOverviewVo preVo) {
        SolarMonitorRevenueVo result = new SolarMonitorRevenueVo();
        if (preVo != null) {
            BeanUtils.copyProperties(preVo, result);
//            RevenueVo revenueVo = preVo.getSolarRevenue();
//            String solarRevenue = ObjectUtils.isEmpty(revenueVo) ? null : revenueVo.getTotal();
            result.setSolarRevenue(preVo.getTotalSolarRevenue());
            result.setEnergyGeneration(StringUtils.isEmpty(preVo.getTotalEnergyGeneration()) ? null : preVo.getTotalEnergyGeneration());
            result.setSavings(StringUtils.isEmpty(preVo.getTotalSavings()) ? null : preVo.getTotalSavings());
        }
        return result;
    }

    /**
     * 获取发电及收益-全部维度数据
     * 从solar_revenue_year年表中获取数据
     *
     * @param
     * @return
     */
//    private List<SolarRevenueBean> getSolarRevenueListAll(List<String> pvIds) {
//        List<SolarRevenueBean> all = new ArrayList<>();
//        if (CollectionUtils.isEmpty(pvIds)) {
//            log.error("SolarMonitorServiceImpl getSolarRevenueListAll pvIds is empty");
//            return all;
//        }
//
//        int pageNum = 0;
//        int step = SolarRevenueConst.MAX_PV_SIZE;
//        do {
//            pageNum += 1;
//            int endIndex = Math.min(pageNum * step, pvIds.size());
//
//            List<String> subIds = pvIds.subList((pageNum - 1) * step, endIndex);
//
//            log.info("SolarMonitorServiceImpl RevenueOverview query subIdSize: {}", subIds.size());
//            List<SolarRevenueBean> resp = solarRevenueMapper.selectOverviewWithGrainAll(subIds);
//            if (resp != null && !resp.isEmpty()) {
//                log.info("SolarMonitorServiceImpl RevenueOverview query respSize: {}", resp.size());
//                all.addAll(resp);
//            }
//        } while (pageNum * step < pvIds.size());
//
//        return all;
//    }

    @Override
    public List<SolarSummaryStatisticsDetailVo> getSolarSummaryStatisticsDetail(SolarRevenueOverviewDto solarRevenueOverviewDto, String languageOption, String userName) throws UedmException {
        log.info("getSolarSummaryStatisticsDetail: start");
        List<SolarSummaryStatisticsDetailVo> solarSummary = new ArrayList<>();

        //获取当前时间
        LocalDateTime currentDateTime = LocalDateTime.now();

        if (solarRevenueOverviewDto == null || StringUtils.isEmpty(solarRevenueOverviewDto.getBeginTime()) || StringUtils.isEmpty(solarRevenueOverviewDto.getEndTime())) {
            log.error("getSolarSummaryStatisticsDetail, param err");
            throw new UedmException(ParameterExceptionEnum.WRONGFUL.getCode(), "parameter is error");
        }

        //分域
        List<String> authList = authorizationService.getFullPermissionByResIds(Collections.singletonList(solarRevenueOverviewDto.getLogicGroupId()));
        if (CollectionUtils.isEmpty(authList)) {
            log.error("No access permission : {}", solarRevenueOverviewDto.getLogicGroupId());
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }

        String grain = solarRevenueOverviewDto.getGrain();
        String tableName = SolarSummaryGrainEnum.getTableNameByCode(grain);
        log.info("getSolarSummaryStatisticsDetail: grain = {}, tableName = {}", grain, tableName);
        if (StringUtils.isBlank(tableName)) {
            log.error("getSolarSummaryStatisticsDetail, grain err: {}", grain);
            throw new UedmException(ParameterExceptionEnum.WRONGFUL.getCode(), "parameter is error");
        }

        // 获取有权限的太阳能列表
        List<String> pvIds = solarRevenueService.getPvListByUser(userName, solarRevenueOverviewDto.getLogicGroupId());
        if (pvIds.isEmpty()) {
            return solarSummary;
        }
        //获取太阳能收益
        SolarRevenueQueryBean queryBean = new SolarRevenueQueryBean();
        queryBean.setPvIds(pvIds);
        queryBean.setGrain(tableName);
        Map<String, String> timeMap = getTimeMap(solarRevenueOverviewDto);
        String beginTime = timeMap.get(BEGIN_TIME);
        String endTime = timeMap.get(END_TIME);
        queryBean.setBeginTime(beginTime);
        queryBean.setEndTime(endTime);

        List<SolarRevenueBean> solarRevenueList = solarRevenueService.getSolarRevenueList(queryBean);

        //根据记录时间汇聚
        Map<String, List<SolarRevenueBean>> solarRevenueListGroup = solarRevenueList.parallelStream().collect(Collectors.groupingBy(SolarRevenueBean::getRecordDate));

        //计算收益
        TreeMap<String, SolarRevenueBean> solarRevenueMap = calcSolarRevenue(solarRevenueListGroup);

        //根据时间映射后返回
        return mapToStatisticsDetailList(solarRevenueMap, grain, beginTime, endTime, currentDateTime);
    }

    private List<SolarSummaryStatisticsDetailVo> mapToStatisticsDetailList(Map<String, SolarRevenueBean> solarRevenueMap, String grain, String beginTime, String endTime, LocalDateTime currentDateTime) throws UedmException {
        List<SolarSummaryStatisticsDetailVo> statisticsDetailList = new ArrayList<>();
        log.info("mapToStatisticsDetailList: solarRevenueMap.size = {}", solarRevenueMap.size());

        //减碳系数
        Double coefficient = carbonReductionService.queryCarbonReductionCoefficient();
        log.info("mapToStatisticsDetailList: coefficient = {}", coefficient);
        String finalCarbonFactor = null != coefficient ? coefficient.toString() : null;

        List<String> timeList = getTimeList(beginTime, endTime, grain, currentDateTime);

        int index = 0;
        if (ALL.equals(grain)) {
            if (CollectionUtils.isEmpty(solarRevenueMap)) {
                log.info("mapToStatisticsDetailList: solarRevenueMap is empty");
                return statisticsDetailList;
            }
            String firstKey = solarRevenueMap.keySet().iterator().next();
            index = timeList.indexOf(firstKey);
            log.info("mapToStatisticsDetailList: index = {}", index);
        }

        for (int i = index; i < timeList.size(); i++) {
            String str = timeList.get(i);
            String time = str;
            if (str.length() > YEAR_MONTH_DAY_HOUR_STR_LEN) {
                str = str.substring(0, YEAR_MONTH_DAY_HOUR_STR_LEN);
            }
            SolarRevenueBean solarRevenueBean = solarRevenueMap.get(str);
            SolarSummaryStatisticsDetailVo solarSummaryDetail = new SolarSummaryStatisticsDetailVo();
            solarSummaryDetail.setTime(time);
            setSolarSummaryDetail(solarRevenueBean, finalCarbonFactor, solarSummaryDetail);
            statisticsDetailList.add(solarSummaryDetail);
        }
        return statisticsDetailList;
    }

    /**
     * 组装返回的实体
     *
     * @param solarRevenueBean
     * @param finalCarbonFactor
     * @param solarSummaryDetail
     */
    private void setSolarSummaryDetail(SolarRevenueBean solarRevenueBean, String finalCarbonFactor, SolarSummaryStatisticsDetailVo solarSummaryDetail) {
        if (solarRevenueBean != null) {
            solarSummaryDetail.setEnergyGeneration(solarRevenueBean.getTotalEnergyGeneration());
            solarSummaryDetail.setSolarRevenue(solarRevenueBean.getTotalSolarRevenue());
            solarSummaryDetail.setSavings(solarRevenueBean.getTotalSavings());
        }
        //减碳量
        if (solarRevenueBean != null && solarRevenueBean.getTotalEnergyGeneration() != null && finalCarbonFactor != null) {
            BigDecimal carbonReduction = new BigDecimal(solarRevenueBean.getTotalEnergyGeneration())
                    .multiply(new BigDecimal(finalCarbonFactor)).setScale(DEF_SCALE, RoundingMode.HALF_UP);
            solarSummaryDetail.setCarbonReduction(carbonReduction.toPlainString());
        }
        solarSummaryDetail.setCurrency(solarRevenueService.getCurrency());
    }

    /**
     * 返回的时间list
     *
     * @param beginTime
     * @param endTime
     * @param grain
     * @return java.util.List<java.lang.String>
     */
    public List<String> getTimeList(String beginTime, String endTime, String grain, LocalDateTime currentDateTime) {
        log.info("getTimeList: endTime = {}, grain = {}, currentDateTime = {}", endTime, grain, currentDateTime);
        List<String> timeList = new ArrayList<>();

        if (DAY.equals(grain)) {
            handleTimeListOfDay(endTime, timeList, currentDateTime);
        }
        if (MONTH.equals(grain)) {
            handleTimeListOfMonth(endTime, timeList, currentDateTime);

        }
        if (YEAR.equals(grain)) {
            handleTimeListOfYear(endTime, timeList, currentDateTime);
        }

        if (ALL.equals(grain)) {
            handleTimeListOfAccum(beginTime, endTime, timeList);
        }
        return timeList;
    }

    private void handleTimeListOfDay(String endTime, List<String> timeList, LocalDateTime currentDateTime) {
        String time;
        int endHour;

        LocalDateTime roundedDateTime = currentDateTime.truncatedTo(ChronoUnit.HOURS);
        LocalDate currentLocalDate = roundedDateTime.toLocalDate();
        int currentHour = roundedDateTime.getHour();

        LocalDate endLocalDate = LocalDate.parse(endTime.substring(0, 10), DateTimeFormatter.ofPattern(DATE_FORMAT_2));
        DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern(DATE_FORMAT_2);
        if (endLocalDate != null) {
            String localDate = endLocalDate.format(formatterDay);
            if (currentLocalDate.isEqual(endLocalDate)) {
                endHour = currentHour;
            } else {
                endHour = SECOND_LAST_HOUR;
                time = localDate + SPACE + LAST_HOUR_ONE;
                timeList.add(time);
            }

            for (int i = 1; i <= endHour; i++) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_8);
                LocalTime localTime = LocalTime.of(i, 0);
                String localHour = localTime.format(formatter);
                time = localDate + SPACE + localHour;
                timeList.add(time);
            }
            Collections.sort(timeList);
        }
    }

    private void handleTimeListOfMonth(String endTime, List<String> timeList, LocalDateTime currentDateTime) {
        String time;

        LocalDate current = currentDateTime.toLocalDate();
        int currentYear = current.getYear();
        int currentMonth = current.getMonthValue();
        int currentDay = current.getDayOfMonth();

        LocalDate endTimeDate = LocalDate.parse(endTime, DateTimeFormatter.ISO_LOCAL_DATE);
        if (endTimeDate != null) {
            int endMonth = endTimeDate.getMonthValue();
            int endDay = endTimeDate.getDayOfMonth();
            int endYear = endTimeDate.getYear();
            if (currentMonth == endMonth && currentYear == endYear) {
                endDay = currentDay;
            }
            for (int i = 1; i <= endDay; i++) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_2);
                LocalDate localDate = LocalDate.of(endYear, endMonth, i);
                time = localDate.format(formatter);
                timeList.add(time);
            }
        }
    }

    private void handleTimeListOfYear(String endTime, List<String> timeList, LocalDateTime currentDateTime) {
        String time;

        LocalDate current = currentDateTime.toLocalDate();
        int currentYear = current.getYear();
        int currentMonth = current.getMonthValue();

        //endTime = 2023-09  => 2023-09-01
        LocalDate endTimeDate = LocalDate.parse(endTime + ZERO_ONE_SUFFIX, DateTimeFormatter.ISO_LOCAL_DATE);
        if (endTimeDate != null) {
            int endMonth = endTimeDate.getMonthValue();
            int endYear = endTimeDate.getYear();
            if (currentYear == endYear) {
                endMonth = currentMonth;
            }
            for (int i = 1; i <= endMonth; i++) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_3);
                LocalDate localDate = LocalDate.of(endYear, i, 1);
                time = localDate.format(formatter);
                timeList.add(time);
            }
        }
    }

    private void handleTimeListOfAccum(String beginTime, String endTime, List<String> timeList) {
        int begin = Integer.parseInt(beginTime);
        int end = Integer.parseInt(endTime);
        for (int i = begin; i <= end; i++) {
            timeList.add(Integer.toString(i));
        }
    }

    private TreeMap<String, SolarRevenueBean> calcSolarRevenue(Map<String, List<SolarRevenueBean>> respMap) {
        TreeMap<String, SolarRevenueBean> solarRevenueMap = new TreeMap<>();
        respMap.forEach((k, v) -> {
            AtomicReference<BigDecimal> totalEnergyGenerationResult = new AtomicReference<>();  //总发电量
            AtomicReference<BigDecimal> totalSolarRevenueResult = new AtomicReference<>(); //太阳能发电总收益
            AtomicReference<BigDecimal> totalSavingsResult = new AtomicReference<>(); //总节省电费
            v.parallelStream().forEach(solarRevenueBean -> {
                totalEnergyGenerationResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalEnergyGeneration()));
                totalSolarRevenueResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSolarRevenue()));
                totalSavingsResult.getAndUpdate(bigDecimal -> add(bigDecimal, solarRevenueBean.getTotalSavings()));
            });

            SolarRevenueBean totalSolarRevenueBean = new SolarRevenueBean();
            totalSolarRevenueBean.setRecordDate(k);
            totalSolarRevenueBean.setTotalEnergyGeneration(getNumStr(totalEnergyGenerationResult.get()));
            totalSolarRevenueBean.setTotalSolarRevenue(getNumStr(totalSolarRevenueResult.get()));
            totalSolarRevenueBean.setTotalSavings(getNumStr(totalSavingsResult.get()));
            solarRevenueMap.put(k, totalSolarRevenueBean);
        });
        return solarRevenueMap;
    }

    public Map<String, String> getTimeMap(SolarRevenueOverviewDto solarRevenueOverviewDto) {
        Map<String, String> timeMap = new HashMap<>();
        String beginTime = EMPTY_STR;
        String endTime = EMPTY_STR;
        if (DAY.equals(solarRevenueOverviewDto.getGrain())) {
            beginTime = solarRevenueOverviewDto.getBeginTime() + SPACE + FIRST_HOUR;
            endTime = solarRevenueOverviewDto.getBeginTime() + SPACE + LAST_HOUR;
        }
        if (MONTH.equals(solarRevenueOverviewDto.getGrain())) {
            beginTime = solarRevenueOverviewDto.getBeginTime() + ZERO_ONE_SUFFIX;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_2);
            LocalDate beginLocalDate = LocalDate.parse(beginTime, DateTimeFormatter.ISO_LOCAL_DATE);
            if (beginLocalDate != null) {
                LocalDate endTimeLocalDate = beginLocalDate.with(TemporalAdjusters.lastDayOfMonth());
                endTime = endTimeLocalDate.format(formatter);
            }
        }
        if (YEAR.equals(solarRevenueOverviewDto.getGrain())) {
            beginTime = solarRevenueOverviewDto.getBeginTime() + ZERO_ONE_SUFFIX;
            endTime = solarRevenueOverviewDto.getBeginTime() + LAST_MONTH_SUFFIX;
        }
        if (ALL.equals(solarRevenueOverviewDto.getGrain())) {
            beginTime = solarRevenueOverviewDto.getBeginTime();
            endTime = solarRevenueOverviewDto.getEndTime();
        }
        timeMap.put(BEGIN_TIME, beginTime);
        timeMap.put(END_TIME, endTime);
        return timeMap;
    }

    @Override
    public Map<String, List<String>> getSitePvRelMap(List<String> moIdList) throws UedmException {
        Map<String, List<String>> map;
        if (null == moIdList || moIdList.isEmpty()) {
            log.info("getSitePvRelMap:moIds is empty.");
            return new HashMap<>(16);
        }
    /*    Map<String, List<MonitorObjectDsBean>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                .getsiteRelatedPvMap(); */
        Map<String, List<DeviceDSEntity>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                .getsiteRelatedPvMap_new();
        if (null == allSiteRelatedPvMaps || allSiteRelatedPvMaps.isEmpty()) {
            log.info("getSitePvRelMap:allSiteRelatedPvMaps is empty.");
            return new HashMap<>(16);
        }
        List<DeviceDSEntity> monitorObjectDsBeanList = new ArrayList<>();
        Set<String> keySet = allSiteRelatedPvMaps.keySet();
        for (String key : keySet) {
            monitorObjectDsBeanList.addAll(allSiteRelatedPvMaps.get(key));
        }
        //按照mobIds过滤
        List<DeviceDSEntity> monitorObjectDsBeanListF = monitorObjectDsBeanList.stream().filter(item -> moIdList.contains(item.getId())).collect(Collectors.toList());
        log.info("getSitePvRelMap:monitorObjectDsBeanListF.size is {}.", monitorObjectDsBeanListF.size());
        map = monitorObjectDsBeanListF.stream().filter(bean -> StringUtils.isNotBlank(bean.getLogicGroupId()))
                .collect(Collectors.groupingBy(DeviceDSEntity::getLogicGroupId, Collectors.mapping(DeviceEntity::getId, Collectors.toList())));
        return map;
    }

    public String getSiteMaxPower(Map<String, List<SolarMaxPowerBean>> pvIdMaxPowerMap){
        BigDecimal sum = BigDecimal.ZERO;
        for (Map.Entry<String, List<SolarMaxPowerBean>> entry : pvIdMaxPowerMap.entrySet()) {
            List<SolarMaxPowerBean> solarMaxPowerList = entry.getValue();
            BigDecimal max = solarMaxPowerList.stream()
                    .map(SolarMaxPowerBean::getMaxOutTotalPower)
                    .filter(maxValue -> maxValue != null && !maxValue.isEmpty())
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::max);
            sum = sum.add(max);
        }
        return sum.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
    }

    private String getStartTime(String grain) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_2);
        if (DAY.equals(grain)) {
            return dateFormat.format(DateTimeService.getStartTimeOfCurrentDay(new Date()));
        }
        if (MONTH.equals(grain)) {
            return dateFormat.format(DateTimeService.getStartTimeOfCurrentMonth(new Date()));
        }
        if (YEAR.equals(grain)) {
            return dateFormat.format(DateTimeService.getStartTimeOfCurrentYear(new Date()));
        }
        return EMPTY_STR;
    }

    @Override
    public List<SitePvOutTotalPowerBean> getSitePvOutTotalPower(Map<String, List<String>> sitePvRelMap) {
        log.info("getSitePvOutTotalPower in.");
        List<SitePvOutTotalPowerBean> sitePvOutTotalPowerList = new ArrayList<>();
        if (null == sitePvRelMap || sitePvRelMap.isEmpty()) {
            log.info("getSitePvOutTotalPower:SiteRelatedPvMaps is empty.");
            return sitePvOutTotalPowerList;
        }
        sitePvOutTotalPowerList = getPvOutTotalPower(sitePvRelMap);
        log.info("getSitePvOutTotalPower end.");
        return sitePvOutTotalPowerList;
    }

    private List<SitePvOutTotalPowerBean> getPvOutTotalPower(Map<String, List<String>> map) {
        log.info("getPvOutTotalPower begin.");
        List<SitePvOutTotalPowerBean> rtn = new ArrayList<>();
        //获取监控对象idList
        List<String> moIdList = new ArrayList<>();
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            moIdList.addAll(map.get(key));
        }
        Map<String,String> moPowerValue = getPowerValueFromRedis(moIdList);
        keySet.forEach(key ->{
            SitePvOutTotalPowerBean bean = new SitePvOutTotalPowerBean();
            bean.setId(key);
            bean.setPvOutTotalPower(getAccuValueN(map.get(key),moPowerValue));
            rtn.add(bean);
        });
        log.info("getPvOutTotalPower end.");
        return rtn;
    }

    private Map<String, String> getPowerValueFromRedis(List<String> moIdList) {
        Map<String, String> valueMap = new HashMap<>();
        try {
            /* Started by AICoder, pid:v3876i04acvce211430d0b20c0ef500fbdc15ea8 */
            valueMap = dataRedis.batchGetSmpValueByKey(moIdList, SOLAR_SMPID_OUT_TOTAL_POWER);
            /* Ended by AICoder, pid:v3876i04acvce211430d0b20c0ef500fbdc15ea8 */
        } catch (UedmException e) {
            log.error("dataRedis.batchGetSmpValueByKey error!", e);
        }
        return valueMap;
    }

    private String getAccuValueN(List<String> moIds,Map<String,String> moPowerValue) {
        log.info("getAccuValue in.");
        List<String> valueList = new ArrayList<>();
        if(null == moPowerValue || moPowerValue.isEmpty()){
            return null;
        }
        moIds.forEach(item -> {
            if(StringUtils.isNotBlank(moPowerValue.get(item))){
                valueList.add(moPowerValue.get(item));
            }
        });
        //如果全部为空，直接返回
        if (CollectionUtils.isEmpty(valueList)) {
            return null;
        }
        BigDecimal sum = BigDecimal.ZERO;
        for (String s : valueList) {
            sum = sum.add(new BigDecimal(s));
        }
        String rtn = sum.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
        log.info("getAccuValue end.");
        return rtn;
    }

    @Override
    public String exportGenerationAndRevenue(SolarRevenueOverviewExportBean exportBean, HttpServletRequest request, HttpServletResponse response, ServiceBaseInfoBean serviceBean) throws UedmException {
        //获取文件名称
        String fileName = parseFileName(exportBean, serviceBean, "_SolarMonitor_Summary_");

        //汇总数据
        SolarRevenueOverviewDto solarRevenueOverviewDto = new SolarRevenueOverviewDto();
        BeanUtils.copyProperties(exportBean, solarRevenueOverviewDto);
        List<SolarSummaryStatisticsDetailVo> solarRevenueStatisticsDetailList =
                getSolarSummaryStatisticsDetail(solarRevenueOverviewDto, serviceBean.getLanguageOption(), serviceBean.getUserName());
        List<Map<String, String>> solarResultList = new ArrayList<>();
        List<String> solarIdList = SolarGenerationRevenueEnums.getAllSolarGenerationRevenue();

        if (!CollectionUtils.isEmpty(solarRevenueStatisticsDetailList)) {
            buildGenerationAndRevenueResultList(solarRevenueStatisticsDetailList, solarResultList, solarIdList, serviceBean);
        }
        //获取图片列表
        List<ImageInfo> imageList = getImageInfoList(exportBean, serviceBean);

        FileExportWriter fw = wf.getWriter(ExportType.PICTURE);
        if (fw == null) {
            log.info("exportGenerationAndRevenue fw is null");
            return "";
        }

        ExportReportBO exportReportBO = new ExportReportBO();
        try {
            //构造EXCEL
            exportReportBO = constructGenerationRevenueData(solarResultList, fw, fileName, imageList, serviceBean);

            fw.setFormat(exportReportBO);
            fw.batchesGroupWriteData();

        } catch (Exception e) {
            log.error("exportGenerationAndRevenue ", e);
            throw new UedmException(-1, e.getMessage());
        }
        finally {
            closeFile(fw);
        }

        File file = new File(FileUtils.pathManipulation(exportReportBO.getOutputFile()));
        if (file.exists()) {
            String srcStr = exportReportBO.getOutputFile();
            FileUtils.downloadPictureFile(srcStr, response, request);
        }

        return fileName;
    }

    public static void closeFile(FileExportWriter wb) {
        try {
            if (wb!= null)
            {
                wb.closeFile();
            }
        } catch (IOException e) {
            log.error("ExcelFileExportUtils close error!", e);
        }
    }


    public void buildGenerationAndRevenueResultList(List<SolarSummaryStatisticsDetailVo> solarSummaryStatisticsDetailList, List<Map<String, String>> resultList, List<String> idList, ServiceBaseInfoBean serviceBean) {
        for (SolarSummaryStatisticsDetailVo bean : solarSummaryStatisticsDetailList) {
            Map<String, String> attributes;
            try {
                attributes = org.apache.commons.beanutils.BeanUtils.describe(bean);
            } catch (Exception e) {
                log.error("Format conversion exception:{}", e.getMessage());
                attributes = new HashMap<>();
            }
            if (null != attributes) {
                buildResultMap(resultList, idList, attributes, serviceBean);
            }
        }
    }

    /**
     * 构建数据
     *
     * @param solarGenerationRevenueList
     * @param fw
     * @param fileName
     * @param images
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    public ExportReportBO constructGenerationRevenueData(List<Map<String, String>> solarGenerationRevenueList,
                                                         FileExportWriter fw, String fileName, List<ImageInfo> images,
                                                         ServiceBaseInfoBean serviceBean) throws UedmException {
        try {
            //构造ExportReportBO
            ExportReportBO exportReportBO = getExportGenerationRevenueReportBO(solarGenerationRevenueList, images, serviceBean);

            String random = randomString(4);
            String mills = System.currentTimeMillis() + "" + random;
            String filePathStr1 = MASTER_DIRECTORY + mills + File.separator + fileName + fw.getExtension();
            String filePathStr = filePathStr1.replace("\\", "/");
            String filedirS = MASTER_DIRECTORY + mills + "/";
            File filedir = new File(filedirS);
            if (!filedir.exists()) {
                boolean isMkdir = filedir.mkdirs();
                log.info("is mkdired:" + isMkdir);
            }
            exportReportBO.setFileName(fileName);
            exportReportBO.setOutputFile(filePathStr);
            exportReportBO.setMills(mills);
            return exportReportBO;
        } catch (Exception e) {
            log.error("constructData error", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    @NotNull
    public ExportReportBO getExportGenerationRevenueReportBO(List<Map<String, String>> solarGenerationRevenueResultList, List<ImageInfo> images, ServiceBaseInfoBean serviceBean) {
        ExportReportBO exportReportBO = new ExportReportBO();
        List<String[]> headerList = new ArrayList<>();
        List<String[][]> dataList = new ArrayList<>();
        List<String> titleList = new ArrayList<>();

        //收益详情
        List<String> idList = SolarGenerationRevenueEnums.getAllSolarGenerationRevenue();
        String[] solarHeader = getSolarGenerationRevenueheader(idList, serviceBean);
        String[][][] solarData = getFormatData(solarGenerationRevenueResultList, idList);
        if (solarData.length <= 1) {
            dataList.add(solarData[0]);
            headerList.add(solarHeader);
            titleList.add(i18nUtils.getMapFieldByLanguageOption(SOLAR_SHEET_NAME, serviceBean.getLanguageOption()));
        } else {
            for (int i = 0; i < solarData.length; i++) {
                dataList.add(solarData[i]);
                headerList.add(solarHeader);
                titleList.add(i18nUtils.getMapFieldByLanguageOption(SOLAR_SHEET_NAME, serviceBean.getLanguageOption()) + (i + 1));
            }
        }

        exportReportBO.setDataMessList(dataList);
        exportReportBO.setColHeaderList(headerList);
        exportReportBO.setTitles(titleList);
        exportReportBO.setImages(images);
        return exportReportBO;
    }

    private String[] getSolarGenerationRevenueheader(List<String> idList, ServiceBaseInfoBean serviceBean) {
        //获取单位
        int index = idList.size();

        String[] res = new String[index];

        for (int i = 0; i < index; i++) {
            String key = idList.get(i);
            String name = "";
            if (null != SolarGenerationRevenueEnums.getUnitById(key)) {
                String nameById = i18nUtils.getMapFieldByLanguageOption(SolarGenerationRevenueEnums.getNameById(key), serviceBean.getLanguageOption());
                name = nameById + "(" + getCurrency() + ")";
            } else {
                name = i18nUtils.getMapFieldByLanguageOption(SolarGenerationRevenueEnums.getNameById(key), serviceBean.getLanguageOption());
            }
            res[i] = name;
        }
        return res;
    }

    @NotNull
    private List<ImageInfo> getImageInfoList(SolarRevenueOverviewExportBean exportBean, ServiceBaseInfoBean serviceBean) {
        List<ImageInfo> imageList = new ArrayList<>();
        List<ImageBean> images = exportBean.getImages();
        if (!CollectionUtils.isEmpty(images)) {
            for (ImageBean imageBean : images) {
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setImageName(imageBean.getImageName());
                imageInfo.setBase64Str(imageBean.getBase64Str());
                imageInfo.setYLine(imageBean.getYLine());
                imageInfo.setXLine(imageBean.getXLine());
                imageInfo.setDim(imageBean.getDim());
                imageInfo.setLang(serviceBean.getLanguageOption());
                imageList.add(imageInfo);
            }
        }
        return imageList;
    }


    private void buildResultMap(List<Map<String, String>> resultList, List<String> dimIds, Map<String, String> attributes, ServiceBaseInfoBean serviceBean) {
        Map<String, String> everyAttrValueMap = new HashMap<>();
        dimIds.forEach(dimId -> {
            if (SolarMonitorOverviewDimEnums.ON_OFF_LINE.getId().equals(dimId)) {
                buildOnlineStatusResultMap(everyAttrValueMap, attributes, dimId, serviceBean);
            } else if (Stream.of(SolarMonitorOverviewDimEnums.EFFICIENCY.getId(), SolarMonitorOverviewDimEnums.TODAY_MAX_EFFICIENCY.getId(),
                            SolarMonitorOverviewDimEnums.MONTH_MAX_EFFICIENCY.getId(), SolarMonitorOverviewDimEnums.YEAR_MAX_EFFICIENCY.getId())
                    .anyMatch(dimId::equals)) {
                buildEfficiencyResultMap(everyAttrValueMap, attributes, dimId, serviceBean);
            } else if (SolarMonitorOverviewDimEnums.ALARM_STATE.getId().equals(dimId)) {
                buildAlarmStateResultMap(everyAttrValueMap, attributes, dimId, serviceBean);
            } else {
                everyAttrValueMap.put(dimId, attributes.get(dimId));
            }
        });
        resultList.add(everyAttrValueMap);
    }

    private void buildAlarmStateResultMap(Map<String, String> everyAttrValueMap, Map<String, String> attributes, String dimId, ServiceBaseInfoBean serviceBean) {
        if (StringUtils.isBlank(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, attributes.get(dimId));
            return;
        }
        if (NORMAL.equals(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, i18nUtils.getMapFieldByLanguageOption(NORMAL_STATUS, serviceBean.getLanguageOption()));
        } else if (ALARM.equals(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, i18nUtils.getMapFieldByLanguageOption(ALARM_STATUS, serviceBean.getLanguageOption()));
        }
    }

    private void buildOnlineStatusResultMap(Map<String, String> everyAttrValueMap, Map<String, String> attributes, String dimId, ServiceBaseInfoBean serviceBean) {
        if (ZERO_STR.equals(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, i18nUtils.getMapFieldByLanguageOption(ON_LINE_STATUS, serviceBean.getLanguageOption()));
        } else if (ONE_STR.equals(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, i18nUtils.getMapFieldByLanguageOption(OFF_LINE_STATUS, serviceBean.getLanguageOption()));
        }
    }

    private void buildEfficiencyResultMap(Map<String, String> everyAttrValueMap, Map<String, String> attributes, String dimId, ServiceBaseInfoBean serviceBean) {
        if (StringUtils.isBlank(attributes.get(dimId))) {
            everyAttrValueMap.put(dimId, attributes.get(dimId));
            return;
        }
        BigDecimal efficiency = new BigDecimal(attributes.get(dimId));
        //转化率值乘以100,用百分制表示.
        BigDecimal result = efficiency.multiply(BigDecimal.valueOf(100)).setScale(DEF_SCALE, RoundingMode.HALF_UP);
        everyAttrValueMap.put(dimId, result.toPlainString());
    }


    private String getCurrency() {
        String currency = configService.getGlobalProperty(SolarRevenueConst.CFG_CENTER_CURRENCY_KEY);
        return currency;
    }


    private String parseFileName(SolarRevenueOverviewExportBean exportBean, ServiceBaseInfoBean serviceBean, String partialPath) throws UedmException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String path = "";
        //String parPath = configurationManagerRpcImpl.getPathNameByLogicGroupId(exportBean.getLogicGroupId(), serviceBean);
        String parPath = resourceBaseCacheManager.getPathNameByLogicGroupId(exportBean.getLogicGroupId());
        log.info("parseFileName parPath:" + parPath);
        if (parPath != null && StringUtils.isNotBlank(parPath)) {
            path = parPath.replace("/", "-")
                    .replace("\\", "");
        }
        // Linux文件名的长度限制是255个字符
        int length = path.length();
        if (length > FILE_MAX_LENGTH){
            log.warn("SolarMonitorServiceImpl parseFileName file name too long");
            // 截取前面字符,大于限制的文件名舍弃
            path = path.substring(0,FILE_MAX_LENGTH);
        }

        String fileName = path + partialPath + (sdf.format(new Date()));
        log.info("parseFileName fileName:" + fileName);
        return fileName;
    }

    @Override
    public List<SitePpaMonitorAlarmBean> getSitePpaMonitorAlarm(Map<String, List<String>> sitePvRelMap) {
        List<SitePpaMonitorAlarmBean> sitePpaMonitorAlarms = new ArrayList<>();
        if (null == sitePvRelMap || sitePvRelMap.isEmpty()) {
            log.info("getSitePpaMonitorAlarm:SiteRelatedPvMaps is empty.");
            return sitePpaMonitorAlarms;
        }
        //get alarm info
        List<String> alarmMocIdList = getAlarmMocIdList(sitePvRelMap);
        log.info("getSitePpaMonitorAlarm:alarmMocIdList.size:{}.", alarmMocIdList.size());
        List<Alarm> allAlarmList = getAlarmInfoParallel(alarmMocIdList);
        log.debug("getSitePpaMonitorAlarm:allAlarmList size:{}-{}.", allAlarmList.size(), JSON.toJSONString(allAlarmList));
        sitePpaMonitorAlarms = getStatisticalAlarmInfo(sitePvRelMap, allAlarmList);
        return sitePpaMonitorAlarms;
    }

    private List<String> getAlarmMocIdList(Map<String, List<String>> sitePvRelMap) {
        List<String> alarmMocIdList = new ArrayList<>(sitePvRelMap.keySet());
        Set<String> keySet = sitePvRelMap.keySet();
        for (String key : keySet) {
            alarmMocIdList.addAll(sitePvRelMap.get(key));
        }
        return alarmMocIdList;
    }

    private List<SitePpaMonitorAlarmBean> getStatisticalAlarmInfo(Map<String, List<String>> map, List<Alarm> allAlarmList) {
        List<SitePpaMonitorAlarmBean> rtn = new ArrayList<>();
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            rtn.add(getStatisticalAlarms(key, map, allAlarmList));
        }
        return rtn;
    }

    private SitePpaMonitorAlarmBean getStatisticalAlarms(String alarmObjectId, Map<String, List<String>> map, List<Alarm> allAlarmList) {
        SitePpaMonitorAlarmBean bean = new SitePpaMonitorAlarmBean();
        //告警查询接口无告警
        if (CollectionUtils.isEmpty(allAlarmList)) {
            bean.setId(alarmObjectId);
            bean.setSeverity(NO_ALARM);
        } else {
            List<String> alarmObjectIdList = new ArrayList<>();
            alarmObjectIdList.add(alarmObjectId);
            if (null != map && !CollectionUtils.isEmpty(map.get(alarmObjectId))) {
                alarmObjectIdList.addAll(map.get(alarmObjectId));
            }
            List<Alarm> allAlarmListF = allAlarmList.stream().filter(item -> alarmObjectIdList.contains(item.getMe())).collect(Collectors.toList());
            //site and pv both have no alarms
            if (CollectionUtils.isEmpty(allAlarmListF)) {
                bean.setId(alarmObjectId);
                bean.setSeverity(NO_ALARM);
            } else {
                bean = getStatisticalAlarm(alarmObjectId, allAlarmListF);
            }
        }
        return bean;
    }

    private SitePpaMonitorAlarmBean getStatisticalAlarm(String alarmObjectId, List<Alarm> allAlarmList) {
        SitePpaMonitorAlarmBean rtn = new SitePpaMonitorAlarmBean();
        rtn.setId(alarmObjectId);
        rtn.setSeverity(getHighestAlarmSeverity(allAlarmList));
        //站点告警
        List<Alarm> siteAlarms = allAlarmList.stream()
                .filter(item -> StringUtils.equals(alarmObjectId, item.getMe()))
                .collect(Collectors.toList());
        rtn.setSiteStatisticsSeverities(getSitePvStatisticalAlarmInfo(siteAlarms));
        //pv 监控对象告警
        List<Alarm> pvAlarms = allAlarmList.stream()
                .filter(item -> !StringUtils.equals(alarmObjectId, item.getMe()))
                .collect(Collectors.toList());
        rtn.setPvStatisticsSeverities(getSitePvStatisticalAlarmInfo(pvAlarms));
        return rtn;
    }

    private Integer getHighestAlarmSeverity(List<Alarm> allAlarmList) {
        return allAlarmList.stream()
                .mapToInt(Alarm::getPerceivedseverity)
                .min()
                .orElse(0);
    }

    private List<PpaMonitorAlarmBean> getSitePvStatisticalAlarmInfo(List<Alarm> allAlarmList) {
        List<PpaMonitorAlarmBean> siteAlarms = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allAlarmList)) {
            siteAlarms.add(getSiteStatisticalAlarmInfo(AlarmLevelEnum.critical.getId(), allAlarmList));
            siteAlarms.add(getSiteStatisticalAlarmInfo(AlarmLevelEnum.major.getId(), allAlarmList));
            siteAlarms.add(getSiteStatisticalAlarmInfo(AlarmLevelEnum.minor.getId(), allAlarmList));
            siteAlarms.add(getSiteStatisticalAlarmInfo(AlarmLevelEnum.warning.getId(), allAlarmList));
        }
        return siteAlarms;
    }

    private PpaMonitorAlarmBean getSiteStatisticalAlarmInfo(Integer alarmSeverity, List<Alarm> siteAlarmList) {
        PpaMonitorAlarmBean ppaMonitorAlarmBean = new PpaMonitorAlarmBean();
        ppaMonitorAlarmBean.setAlarmSeverity(alarmSeverity);
        if (CollectionUtils.isEmpty(siteAlarmList)) {
            ppaMonitorAlarmBean.setTotal(0);
        } else {
            ppaMonitorAlarmBean.setTotal((int) siteAlarmList.stream()
                    .filter(item -> alarmSeverity.equals(item.getPerceivedseverity())).count());
        }
        return ppaMonitorAlarmBean;
    }

    private List<Alarm> getAlarmInfoParallel(List<String> moIdList) {
        List<List<String>> parallelList = new ArrayList<>();
        List<Alarm> result = new CopyOnWriteArrayList<>();
        int pageNum = 0;
        int step = MAX_ALARM_QUERY_SIZE;
        do {
            pageNum += 1;
            int endIndex = Math.min(pageNum * step, moIdList.size());
            List<String> subAlarmMocIds = moIdList.subList((pageNum - 1) * step, endIndex);
            parallelList.add(subAlarmMocIds);
            log.info("getSitePpaMonitorAlarm query subAlarmMocIds size: {}", subAlarmMocIds.size());
        } while (pageNum * step < moIdList.size());
        parallelList.parallelStream().forEach(subIds -> {
            List<Alarm> alarmList = getAlarmInfo(subIds);
            if (!CollectionUtils.isEmpty(alarmList)) {
                result.addAll(alarmList);
            }
        });
        return result;
    }

    private List<Alarm> getAlarmInfo(List<String> moIdList) {
        List<Alarm> alarms = new ArrayList<>();
        int totalCount = getQueryTotalCount(moIdList,alarms);
        if (MAX_ALARM_QUERY_SIZE >= totalCount) {
            return alarms;
        }
        alarms.addAll(getAlarmInfoDetail(moIdList, totalCount));
        log.info("getAlarmInfo query moIdList:{}-alarm size: {}.", moIdList, alarms.size());
        return alarms;
    }

    private List<Alarm> getAlarmInfoDetail(List<String> moIdList, int totalCount) {
        List<Alarm> alarms = new ArrayList<>();
        //分页查询alarm，一次1000
        int pageNum = 2;
        for (int i = MAX_ALARM_QUERY_SIZE; i < totalCount; i += MAX_ALARM_QUERY_SIZE) {
            int end = (Math.min(MAX_ALARM_QUERY_SIZE, totalCount - i));
            try {
                AlarmResponse alarmResponseT = alarmServiceImpl.getAlarmListForSolarMonitor(moIdList, pageNum, end);
                if (null != alarmResponseT && null != alarmResponseT.getAlarms()) {
                    alarms.addAll(alarmResponseT.getAlarms());
                }
            } catch (UedmException e) {
                log.error("getAlarmInfoDetail pageNum:{},alarm call error!", pageNum, e);
            }
            pageNum += 1;
        }
        return alarms;
    }

    private int getQueryTotalCount(List<String> moIdList, List<Alarm> alarms) {
        int alarmTotal = 0;
        try {
            AlarmResponse alarmResponse = alarmServiceImpl.getAlarmListForSolarMonitor(moIdList, 1, MAX_ALARM_QUERY_SIZE);
            if (null != alarmResponse) {
                alarmTotal = (null == alarmResponse.getTotalcount()) ? 0 : alarmResponse.getTotalcount();
                log.info("getQueryTotalCount total count: {}.", alarmTotal);
                if (null != alarmResponse.getAlarms()) {
                    if (alarmTotal > 0 && alarmTotal <= MAX_ALARM_QUERY_SIZE) {
                        alarms.addAll(alarmResponse.getAlarms());
                    } else if (alarmTotal > MAX_ALARM_QUERY_SIZE) {
                        alarms.addAll(alarmResponse.getAlarms().subList(0, Math.min(MAX_ALARM_QUERY_SIZE,alarmResponse.getAlarms().size())));
                    }
                }
            }
        } catch (UedmException e) {
            log.error("getQueryTotalCount alarm call error!", e);
        }
        return alarmTotal;
    }

    @Override
    public String exportDetail(SolarRevenueOverviewExportBean exportBean, HttpServletRequest request, HttpServletResponse response, ServiceBaseInfoBean serviceBean) throws UedmException, InterruptedException, ExecutionException {

        //获取文件名称
        String fileName = parseFileName(exportBean, serviceBean, "_SolarMonitor_");
        //汇总数据
        SolarMonitorQueryDto solarMonitorQueryDto = new SolarMonitorQueryDto();
        BeanUtils.copyProperties(exportBean, solarMonitorQueryDto);
        List<SolarMonitorVo> solarMonitorVoList =
                solarMonitorSelect(solarMonitorQueryDto, serviceBean.getLanguageOption(), serviceBean.getUserName());
        List<Map<String, String>> solarResultList = new ArrayList<>();
        List<String> solarIdList = SolarMonitorOverviewDimEnums.getIdsWithIdName();

        if (!CollectionUtils.isEmpty(solarMonitorVoList)) {
            buildDetailResultList(solarMonitorVoList, solarResultList, solarIdList, serviceBean);
        }
        //获取图片列表
        List<ImageInfo> imageList = getImageInfoList(exportBean, serviceBean);

        FileExportWriter fw = wf.getWriter(ExportType.PICTURE);
        if (fw == null) {
            log.info("exportDetail fw is null.");
            return "";
        }

        ExportReportBO exportReportBO = new ExportReportBO();
        try {
            //构造EXCEL
            exportReportBO = constructDetailData(solarResultList, fw, fileName, imageList, serviceBean);

            fw.setFormat(exportReportBO);
            fw.batchesGroupWriteData();
        } catch (Exception e) {
            log.error("exportBattCellData ", e);
            throw new UedmException(-1, e.getMessage());
        }
        finally {
            closeFile(fw);
        }

        File file = new File(FileUtils.pathManipulation(exportReportBO.getOutputFile()));
        if (file.exists()) {
            String srcStr = exportReportBO.getOutputFile();
            FileUtils.downloadPictureFile(srcStr, response, request);
        }

        return fileName;
    }

    public void buildDetailResultList(List<SolarMonitorVo> solarMonitorVoList, List<Map<String, String>> resultList, List<String> idList, ServiceBaseInfoBean serviceBean) {
        for (SolarMonitorVo bean : solarMonitorVoList) {
            Map<String, String> attributes;
            try {
                attributes = org.apache.commons.beanutils.BeanUtils.describe(bean);
            } catch (Exception e) {
                log.error("Format conversion exception:{}", e.getMessage());
                attributes = new HashMap<>();
            }
            if (null != attributes) {
                buildResultMap(resultList, idList, attributes, serviceBean);
            }
        }
    }

    /**
     * 构建数据
     *
     * @param solarResultList
     * @param fw
     * @param fileName
     * @param images
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    public ExportReportBO constructDetailData(List<Map<String, String>> solarResultList,
                                              FileExportWriter fw, String fileName, List<ImageInfo> images,
                                              ServiceBaseInfoBean serviceBean) throws UedmException {
        try {
            //构造ExportReportBO
            ExportReportBO exportReportBO = getDetailReportBO(solarResultList, images, serviceBean);

            String random = randomString(4);
            String mills = System.currentTimeMillis() + "" + random;
            String filePathStr1 = MASTER_DIRECTORY + mills + File.separator + fileName + fw.getExtension();
            String filePathStr = filePathStr1.replace("\\", "/");
            String filedirS = MASTER_DIRECTORY + mills + "/";
            File filedir = new File(filedirS);
            if (!filedir.exists()) {
                boolean isMkdir = filedir.mkdirs();
                log.info("is mkdired:" + isMkdir);
            }
            exportReportBO.setFileName(fileName);
            exportReportBO.setOutputFile(filePathStr);
            exportReportBO.setMills(mills);
            return exportReportBO;
        } catch (Exception e) {
            log.error("constructData error", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    @NotNull
    public ExportReportBO getDetailReportBO(List<Map<String, String>> solarResultList, List<ImageInfo> images, ServiceBaseInfoBean serviceBean) {
        ExportReportBO exportReportBO = new ExportReportBO();
        List<String[]> headerList = new ArrayList<>();
        List<String[][]> dataList = new ArrayList<>();
        List<String> titleList = new ArrayList<>();

        //站点详情
        List<String> idList = SolarMonitorOverviewDimEnums.getIdsWithIdName();
        String[] solarHeader = getDetailheader(idList, serviceBean);
        String[][][] solarData = getFormatData(solarResultList, idList);
        if (solarData.length <= 1) {
            dataList.add(solarData[0]);
            headerList.add(solarHeader);
            titleList.add(i18nUtils.getMapFieldByLanguageOption(SOLAR_SHEET_NAME, serviceBean.getLanguageOption()));
        } else {
            for (int i = 0; i < solarData.length; i++) {
                dataList.add(solarData[i]);
                headerList.add(solarHeader);
                titleList.add(i18nUtils.getMapFieldByLanguageOption(SOLAR_SHEET_NAME, serviceBean.getLanguageOption()) + (i + 1));
            }
        }


        exportReportBO.setDataMessList(dataList);
        exportReportBO.setColHeaderList(headerList);
        exportReportBO.setTitles(titleList);
        exportReportBO.setImages(images);
        return exportReportBO;
    }

    private String[] getDetailheader(List<String> idList, ServiceBaseInfoBean serviceBean) {
        //获取单位
        int index = idList.size();

        String[] res = new String[index];

        for (int i = 0; i < index; i++) {
            String key = idList.get(i);
            String name = "";
            if (SolarRevenuePriceEnums.getAllIds().contains(key)) {
                String nameById = i18nUtils.getMapFieldByLanguageOption(SolarMonitorOverviewDimEnums.getNameById(key), serviceBean.getLanguageOption());
                name = nameById + "(" + getCurrency() + ")";
            } else if (SolarMonitorOverviewDimEnums.getUnitById(key) != null) {
                String nameById = i18nUtils.getMapFieldByLanguageOption(SolarMonitorOverviewDimEnums.getNameById(key), serviceBean.getLanguageOption());
                name = nameById + "(" + SolarMonitorOverviewDimEnums.getUnitById(key) + ")";
            } else {
                name = i18nUtils.getMapFieldByLanguageOption(SolarMonitorOverviewDimEnums.getNameById(key), serviceBean.getLanguageOption());
            }
            res[i] = name;
        }
        return res;
    }

    @Override
    public Map<String, String> getSiteInstallationCapacity(List<String> moIdList,Map<String, String> pvToSiteMap) {
        Map<String, String> siteInstallationCapacityMap = new HashMap<>();
        if (CollectionUtils.isEmpty(moIdList)) {
            log.info("getSiteInstallationCapacity:moNames is empty");
            return siteInstallationCapacityMap;
        }
        log.info("getSiteInstallationCapacity:moIdList size is:{}", moIdList.size());
        try {
            // List<MonitorObjectBean> powerRatings = configurationManagerRpcImpl.getPowerRatings(item);
            //List<MonitorObjectEntity> monitorObjectEntitys = pvCacheManager.getMonitorObjectEntitysByIds(moIdList);
             List<DeviceDSEntity> monitorObjectEntitys = realGroupRelationSiteUtils.getsiteRelatedPvMap_new().values().stream().flatMap(Collection::stream).filter(bean -> moIdList.contains(bean.getId())).collect(Collectors.toList());
            log.info("getSiteInstallationCapacity monitorObjectEntitys={}.", monitorObjectEntitys.size());
            // List<MonitorObjectEntity> powerRatings = monitorObjectEntitys.stream().filter(id -> moIdList.contains(id.getId())).collect(Collectors.toList());
            // log.info("getSiteInstallationCapacity powerRatings={}", powerRatings.size());

            List<SolarInstallationCapBean> solarCapBeanList = monitorObjectEntitys.stream().map(power -> {
                SolarInstallationCapBean solarCapBean = new SolarInstallationCapBean();
                if (null != pvToSiteMap.get(power.getId())) {
                    // pvId->siteId
                    solarCapBean.setId(pvToSiteMap.get(power.getId()));
                    solarCapBean.setValue(power.getPowerRating());
                }
                return solarCapBean;
            }).collect(Collectors.toList());
            /* Started by AICoder, pid:e7d2389c187f46eaa2f4c9588e0bc369 */
            Map<String, List<BigDecimal>> installationCapSiteMap = solarCapBeanList.stream().filter(bean -> StringUtils.isNotBlank(bean.getId()))
                    .collect(Collectors.groupingBy(SolarInstallationCapBean::getId, Collectors.mapping(SolarInstallationCapBean::getValue, Collectors.toList())));
            /* Ended by AICoder, pid:e7d2389c187f46eaa2f4c9588e0bc369 */
            for (Map.Entry<String, List<BigDecimal>> entry : installationCapSiteMap.entrySet()) {
                String key = entry.getKey();
                siteInstallationCapacityMap.put(key, getSiteAggValue(entry.getValue()));
            }
        } catch (Exception ex) {
            log.error("getSiteInstallationCapacity error={}", ex);
        }
        log.debug("getSiteInstallationCapacity:siteInstallationCapacityMap is:{}", siteInstallationCapacityMap.size());
        return siteInstallationCapacityMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getSolarMaxOutTotalPowerFromPma() {
        List<SolarMaxPowerBean> maxPowerOutFromPma = getMaxPowerOutFromPma();
        try {
            solarMaxPowerMapper.addBatchAndUpdate(maxPowerOutFromPma);
        } catch (Exception e) {
            log.error("SolarMonitorServiceImpl [getSolarMaxOutTotalPowerFromPma] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    private String getSiteAggValue(List<BigDecimal> values) {
        List<BigDecimal> tempList = new ArrayList<>();
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal s : values) {
            if(s != null) {
                sum = sum.add(s);
            }else{
                tempList.add(s);
            }
        }
        // 处理额定功率都为Null的情况
        if(tempList.size() == values.size()){
            return null;
        }
        if(sum.compareTo(BigDecimal.ZERO)<0){
            sum = BigDecimal.ZERO;
        }
        return sum.setScale(SCALE, RoundingMode.HALF_UP).toPlainString();
    }

    public List<SolarMaxPowerBean> getMaxPowerOutFromPma()  {
        List<String> allPvId = getAllPvId();
        String startTime = getStartTimeOfCurrentDay();
        String endTime = dateTimeService.getCurrentTime();
        List<SolarMaxPowerBean> pvMaxPower = new ArrayList<>();
        try {
            // pma标准测点历史数据
            List<HistoryAiBean> batchHisData = getBatchHistoryAiBeans(allPvId,
                    Collections.singletonList(SOLAR_SMPID_OUT_TOTAL_POWER), startTime, endTime);
            Map<String, List<HistoryAiBean>> pvIdHistoryAiMap = batchHisData.stream().collect(Collectors.groupingBy(HistoryAiBean::getResId));
            pvMaxPower = getPvMaxPower(pvIdHistoryAiMap, allPvId);
        } catch (Exception e){
            log.error("SolarMonitorServiceImpl getMaxPowerOutFromPma error",e);
        }
        return pvMaxPower;
    }

    private List<SolarMaxPowerBean> getPvMaxPower(Map<String, List<HistoryAiBean>> pvIdHistoryAiMap,List<String> allPvId){
        List<SolarMaxPowerBean> solarMaxPowerBeanList = new ArrayList<>();
        allPvId.forEach(id->{
            SolarMaxPowerBean solarMaxPowerBean = new SolarMaxPowerBean();
            List<HistoryAiBean> historyAiBean = pvIdHistoryAiMap.get(id);
            if (historyAiBean == null || historyAiBean.isEmpty()){
                return;
            }
            String max = historyAiBean.stream()
                    .map(HistoryAiBean::getMaxValue)
                    .filter(maxValue -> maxValue != null && !maxValue.isEmpty())
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::max)
                    .setScale(SCALE, RoundingMode.HALF_UP)
                    .toPlainString();
            solarMaxPowerBean.setMaxOutTotalPower(max);
            solarMaxPowerBean.setPvId(id);
            solarMaxPowerBean.setGmtCreate(dateTimeService.getCurrentDateTime());
            solarMaxPowerBean.setRecordDate(getCurrentDateTime());
            solarMaxPowerBeanList.add(solarMaxPowerBean);
        });
        return solarMaxPowerBeanList;
    }

    public List<HistoryAiBean> getBatchHistoryAiBeans(List<String> moIds, List<String> smpIds,
                                               String startTime,String endTime)  {
        List<HistoryAiBean> historyAiBeansPerDay = new ArrayList<>();
        log.info("SolarMonitorServiceImpl getBatchHistoryAiBeans moIds size is:{}.startTime is :{},endTime is:{}",moIds.size(),startTime,endTime);
        BatchUtils.doInBatch(800, moIds, (items) -> {
            try {
                List<SpIdDto> max = smpIds.parallelStream().map(spid->new SpIdDto(spid, GlobalConstants.MAX,null)).collect(Collectors.toList());
                List<HistoryAiBean> historyAiBeans = pmaService.selectDataByCondition(items, MocOptional.PV.getId(), max, startTime,
                        endTime,BattConst.DATA_TYPE_COLLECT, BattConst.GR_MINUTE);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(historyAiBeans)) {
                    historyAiBeansPerDay.addAll(historyAiBeans);
                }
            } catch (Exception e) {
                RuntimeException runtimeException = new RuntimeException(e);
                runtimeException.setStackTrace(e.getStackTrace());
                throw runtimeException;
            }
        });
        log.info("SolarMonitorServiceImpl getBatchHistoryAiBeans end to query history data from pma, size: [{}]", historyAiBeansPerDay.size());
        log.debug("SolarMonitorServiceImpl getBatchHistoryAiBeans end to query history data from pma, data: [{}]", historyAiBeansPerDay);
        return historyAiBeansPerDay;
    }

    private List<String> getAllPvId(){
        List<String> pvIdList = new ArrayList<>();
        try {
            Map<String, String> pvToSiteMap = solarRevenueService.getPvToSiteMap();
            pvIdList = new ArrayList<>(pvToSiteMap.keySet());
        } catch (UedmException e){
            log.error("SolarMonitorServiceImpl getAllPvId error",e);
        }
        return pvIdList;
    }

    private String getCurrentDateTime(){
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_2);
        return dateFormat.format(dateTimeService.getCurrentDateTime());
    }

    private String getStartTimeOfCurrentDay(){
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_1);
        return dateFormat.format(DateTimeService.getStartTimeOfCurrentDay(new Date()));
    }

    public List<SolarMaxPowerBean> getSolarMaxPowerList(SolarMaxPowerQueryBean queryBean) {
        log.info("SolarMonitorServiceImpl getSolarMaxPowerList query beginTime: {}, endTime: {}.",
                queryBean.getBeginTime(), queryBean.getEndTime());

        List<SolarMaxPowerBean> all = new ArrayList<>();
        List<String> pvList = queryBean.getPvIds();
        if (CollectionUtils.isEmpty(pvList)) {
            log.error("SolarMonitorServiceImpl getSolarMaxPowerList pvIds null");
            return all;
        }

        List<List<String>> parallelList = new ArrayList<>();
        List<SolarMaxPowerBean> result = new CopyOnWriteArrayList<>();
        int pageNum = 0;
        int step = SolarRevenueConst.MAX_PV_SIZE;
        do {
            pageNum += 1;
            int endIndex = Math.min(pageNum * step, pvList.size());
            List<String> subIds = pvList.subList((pageNum - 1) * step, endIndex);
            parallelList.add(subIds);
            log.info("SolarMonitorServiceImpl query subIdSize: {}.", subIds.size());
        } while (pageNum * step < pvList.size());
        parallelList.parallelStream().forEach(subIds -> {
            SolarMaxPowerQueryBean queryBeanSub = new SolarMaxPowerQueryBean();
            BeanUtils.copyProperties(queryBean, queryBeanSub);
            queryBeanSub.setPvIds(subIds);
            List<SolarMaxPowerBean> solarMaxPowerBeanList = null;
            try {
                solarMaxPowerBeanList = solarMaxPowerMapper.queryByPvIdAndDate(queryBeanSub);
            } catch (Exception e) {
                log.error("SolarMonitorOverviewDimServiceImpl [getSolarMaxPowerList] occur error message = {}", e.getMessage(), e);
                throw new RuntimeException(DatabaseExceptionEnum.OPERATEDB.getDesc());
            }
            if (solarMaxPowerBeanList != null) {
                log.info("SolarMonitorServiceImpl query respSize: {}", solarMaxPowerBeanList.size());
                result.addAll(solarMaxPowerBeanList);
            }
        });
        return result;
    }

}
