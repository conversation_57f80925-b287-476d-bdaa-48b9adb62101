package com.zte.uedm.battery.bean.pv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@ApiModel(description = "太阳能异常记录")
public class ExceptionHistoryQueryRequestBean
{
    /**
     * 筛选条件-位置（逻辑组id列表）
     */
    @ApiModelProperty(value = "位置")
    private List<String> positions;

    /**
     * 筛选条件-站点等级
     */
    @ApiModelProperty(value = "站点等级")
    private List<String> siteLevels;

    /**
     * 筛选条件-供电方式
     */
    @ApiModelProperty(value = "供电方式")
    private List<String> supplyScenes;

    /**
     * 筛选条件-开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 赛选条件-结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;
}
