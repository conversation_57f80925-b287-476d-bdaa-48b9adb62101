package com.zte.uedm.battery.util;

import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * xlsx文件导出
 *
 */
@Slf4j
public class ExcelFileOutputUtils
{
    private static Workbook wb;
    private static Sheet sheet;
    private static int row;

    private final static String FILE_NAME = "attachment;filename=";

    public synchronized static  void  getXlsxFile(String[] head,String[][] datas, String fileName, HttpServletResponse response) throws UedmException
    {
        wb = new XSSFWorkbook();
        sheet = wb.createSheet();
        row = 0;
        writeHeader(head, sheet);
        for (String[] data : datas) {
            writeRow(data, null, sheet);
        }
        goTryCatch(fileName, response);
    }

    public synchronized static void getXlsxFile(String[] head, String[][] datas, String fileName,
                                                HttpServletResponse response, HttpServletRequest request) throws UedmException
    {
        wb = new XSSFWorkbook();
        sheet = wb.createSheet();
        row = 0;
        writeHeader(head, sheet);
        if (datas != null && datas.length > 0)
        {
            for (String[] data : datas)
            {
                writeRow(data, null, sheet);
            }
        }
        try
        {
            // 清空reponse
            response.reset();
            String agent = request.getHeader("USER-AGENT").toLowerCase();
            response.setContentType("application/vnd.ms-excel");
            //            String codedFileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            String codedFileName = java.net.URLEncoder.encode(fileName, "UTF-8");

            if (agent.contains("firefox"))
            {
                response.setCharacterEncoding("utf-8");
                response.setHeader("content-disposition", FILE_NAME + new String(codedFileName.getBytes("UTF-8"), "ISO8859-1"));
            } else
            {
                response.setHeader("content-disposition", FILE_NAME + codedFileName);
            }
            wb.write(response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("ExcelFileOutputUtils getXlsxFile error.", e);
            throw new UedmException(-601, e.getMessage());
        }
        finally {
            try {
                wb.close();
            } catch (IOException e) {
                log.error("getXlsxFile io close is error : {}",e.getMessage());
            }
        }
    }


    public synchronized static void  getXlsxFileList(String[][] head,String[][][] datas, String fileName,String[] sheetList, HttpServletResponse response) throws UedmException
    {
        wb = new XSSFWorkbook();
        for (int i = 0; i < head.length; i++) {
            sheetWriteH(head[i], datas[i], sheetList, i);
        }
        goTryCatch(fileName, response);
    }

    public synchronized static void  getXlsxFileAndNoPasswordList(String[][] head,String[][][] datas, String fileName,String[] sheetList, HttpServletResponse response) throws UedmException
    {
        wb = new XSSFWorkbook();
        for (int i = 0; i < head.length; i++) {
            sheetWriteAndNoPassword(head[i], datas[i], sheetList, i);
        }
        goTryCatch(fileName, response);
    }

    private static void goTryCatch(String fileName, HttpServletResponse response) throws UedmException {
        try {
            setHeader(fileName, response);
        } catch (IOException e) {
            log.error("wb close fail .");
            throw new UedmException(-1, "wb close fail");
        }
    }

    private static void setHeader(String fileName, HttpServletResponse response) throws IOException {
        try {
            // 清空reponse
            response.reset();
            // 告诉浏览器用什么软件可以打开此文件，设置response的Header
            response.setHeader("content-Type", "application/vnd.ms-excel");
            fileName = new String(fileName.getBytes("utf-8"), "ISO8859-1");
            response.setHeader("Content-Disposition", FILE_NAME.concat(fileName));
            wb.write(response.getOutputStream());
        } catch (IOException e) {
            log.error("setHeader io close error :{}" ,e.getMessage());
        } finally {
            wb.close();
        }
    }

    private static void sheetWriteH(String[] hd, String[][] data1, String[] sheetList, int i) {
        sheet = wb.createSheet(sheetList[i]);
        sheet.protectSheet("zte");
        sheet.setSelected(false);
        row = 0;
        newWriteHeader(hd, sheet);
        for (String[] data : data1) {
            CellStyle cs = wb.createCellStyle();
            cs.setLocked(true);
            writeRow(data, cs, sheet);
        }
    }

    private static void sheetWriteAndNoPassword(String[] hd, String[][] data1, String[] sheetList, int i) {
        sheet = wb.createSheet(sheetList[i]);
        //        sheet.protectSheet("zte");
        //        sheet.setSelected(false);
        row = 0;
        newWriteHeader(hd, sheet);
        for (String[] data : data1) {
            CellStyle cs = wb.createCellStyle();
            cs.setLocked(true);
            writeRow(data, cs, sheet);
        }
    }

    private synchronized static void writeHeader(String[] hd, Sheet sheet) {
        CellStyle cs = wb.createCellStyle();
        cs.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cs.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cs.setBorderBottom(BorderStyle.THIN);
        writeRow(hd, cs, sheet);
    }

    private synchronized static void newWriteHeader(String[] hd, Sheet sheet) {
        CellStyle cs = wb.createCellStyle();
        cs.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cs.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cs.setBorderBottom(BorderStyle.THIN);
        cs.setLocked(true);
        writeRow(hd, cs, sheet);
    }

    private synchronized static void writeRow(String[] data, CellStyle cs, Sheet sheet) {
        Row r = sheet.createRow(row++);
        int col = 0;
        if (data != null) {
            for (String s : data) {
                Cell c = r.createCell(col++);
                c.setCellValue(s);
                if (cs != null) {
                    c.setCellStyle(cs);
                }
            }
        }
    }

    /**
     * 属性值(value)的map组成的list
     * */
    public static synchronized  List<Map<String, Object>> getFiledsValue(Object obj,Map<String, Object> map) throws UedmException {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            if (obj != null) {
                getList(obj, map, list);
            }
            return list;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new UedmException(-1, obj + " getFiledsValue is failed.");
        }
    }

    private static void getList(Object obj, Map<String, Object> map, List<Map<String, Object>> list) throws UedmException {
        Map<String, Object> infoMap;
        for (Map.Entry<String, Object> keymap: map.entrySet()) {
            infoMap = new HashMap();
            infoMap.put("value", getValueByName(obj,keymap.getKey()));
            list.add(infoMap);
        }
    }


    public static boolean checkBoolean(Object o) {
        if (o.equals("true") || o.equals("TRUE") ) {
            return true;
        } else if (o.equals("false") || o.equals("FALSE")) {
            return false;
        }
        return false;
    }

    /**
     * 根据属性名取值
     * @param object
     * @param name
     * @throws UedmException
     */
    private static synchronized Object getValueByName(Object object, String name) throws UedmException
    {
        //Object value = null;
        try
        {
            return getObjectByName(object, name);
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(-1,  object + " getValue is failed.");
        }
        //return value;
    }

    private static Object getObjectByName(Object object, String name) throws NoSuchFieldException, IllegalAccessException {
        Object value = null;
        Class<?> clazz = getLegalClass(object.getClass(), name);
        if (clazz != null) {
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            value = field.get(object);
        }
        return value;
    }

    public static synchronized Class<?> getLegalClass(Class<?> clazz, String field)
    {
        if(clazz != null)
        {
            return getaClass(clazz, field);
        }
        return null;
    }

    @Nullable
    private static Class<?> getaClass(Class<?> clazz, String field) {
        if(isExistField(clazz.getDeclaredFields(), field))
        {
            return clazz;
        }
        return getLegalClass(clazz.getSuperclass(), field);
    }

    private static synchronized boolean isExistField(Field[] fields, String field)
    {
        if (fieldIsTure(fields, field)) return true;
        return false;
    }

    private static boolean fieldIsTure(Field[] fields, String field) {
        for (int i = 0; i < fields.length; i++)
        {
            if (fieldEquals(fields, field, i)) return true;
        }
        return false;
    }

    private static boolean fieldEquals(Field[] fields, String field, int i) {
        if (fields[i].getName().equals(field))
        {
            return true;
        }
        return false;
    }
}

