package com.zte.uedm.battery.opti.domain.service.bean.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池风险规则应用类型
 */
public enum BattRiskRuleApplicationTypeEnum
{
    /**
     * 铅酸通用规则
     */
    PBAC_COMMON_RISK_RULE("pbacRiskRule"),

    /**
     * 锂电通用规则
     */
    LITHIUM_COMMON_RISK_RULE("liCommonRiskRule"),

    /**
     * 自研锂电风险规则
     */
    LITHIUM_SELF_RISK_RULE("liSelfRiskRule"),

    /**
     * 外购锂电规则
     */
    LITHIUM_OUT_RISK_RULE("liOutRiskRule");
    private String id;

    public String getId()
    {
        return id;
    }

    BattRiskRuleApplicationTypeEnum(String id)
    {
        this.id = id;
    }

    public static List<String> getAllIds() {
        List<String> allIds = new ArrayList<>();
        BattRiskRuleApplicationTypeEnum[] arr = BattRiskRuleApplicationTypeEnum.values();
        for (BattRiskRuleApplicationTypeEnum enu : arr) {
            if (StringUtils.isNotBlank(enu.getId())) {
                allIds.add(enu.getId());
            }
        }
        return allIds;
    }
}
