package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.github.pagehelper.PageInfo;
import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftDistributionService;
import com.zte.uedm.battery.a_application.peakshift.executor.StrategyTemplateService;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.factory.PeakShiftFactory;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftDistributionTaskDomainService;
import com.zte.uedm.battery.a_domain.service.peakshift.TemplateStrategyService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftConfigServiceImpl;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.persistence.PeakShiftConfigRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftConfigPo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.*;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.PeakShiftConfigVo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.SeasonStrategyForTemplateVo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.TemplateStrategyVo;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.IntervalStrategyDetailModeBean;
import com.zte.uedm.battery.bean.IntervalStrategyQueryBean;
import com.zte.uedm.battery.bean.SeasonStrategyBean;
import com.zte.uedm.battery.bean.peak.DetailHistoryResposeBean;
import com.zte.uedm.battery.bean.peak.TemplateDetailBaseDto;
import com.zte.uedm.battery.bean.scopeStrategy.TieredPriceStrategyBean;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.mapper.PeakShiftTaskMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.AutoPeakShiftStrategyService;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.enums.asset.BatteryTypeEnums;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants.CHARGE_TIME;
import static com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants.DIS_CHARGE_TIME;

@Service
@Slf4j
public class AutoPeakShiftStrategyServiceImpl implements AutoPeakShiftStrategyService {

    /* Started by AICoder, pid:g27a2ob16bs0c3014b4408ecf204259a2c55489c */
    @Autowired
    public PeakShiftFactory peakShiftFactory;

    @Autowired
    public PeakShiftConfigServiceImpl peakShiftConfigService;

    @Autowired
    public DeviceCacheManager deviceCacheManager;

    @Autowired
    public PeakShiftServiceImpl peakShiftService;

    @Autowired
    public StrategyTemplateService strategyTemplateService;

    @Autowired
    public TemplateStrategyService templateStrategyService;

    @Autowired
    public PeakShiftDistributionService peakShiftDistributionService;

    @Autowired
    public PeakShiftDistributionTaskDomainService peakShiftDistributionTaskDomainService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private PeakShiftConfigRepositoryImpl peakShiftConfigRepositoryImpl;

    @Autowired
    private GridStrategyMapper gridStrategyMapper;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private PeakShiftTaskMapper peakShiftTaskMapper;

    @Autowired
    private MsgSenderService msgSenderService;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    public static final String DEFAULT_USER = "SYSTEM";
    public static final String DEFAULT_LANGUAGE = "zh_CN";
    public static final String DEFAULT_IP = "127.0.0.1";
    public static final String PENDING_STATUS = "pending";
    public static final String AUTOSTRATEGY = "AutoStrategy";
    public static final String AUTOTASK = "AutoTask";
    private static final String PEAK_SHIFT_STRATEGY_MODULE_ZH = "智能错峰-错峰模版";
    private static final String PEAK_SHIFT_STRATEGY_MODULE_EN = "Peakshift-Strategy";

    private static final String PEAK_SHIFT_STRATEGY_OPERATION_ZH = "生成自动错峰模版";
    private static final String PEAK_SHIFT_STRATEGY_OPERATION_EN = "Product Peakshift Strategy";

    // 策略模板id
    public static String id = null;
    public static final ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(DEFAULT_USER, DEFAULT_IP, DEFAULT_LANGUAGE);
    public static Map<String, PeakShiftConfigPo> peakShiftConfigPoMap = new HashMap<>();
    ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 执行峰谷调度配置的自动调度策略。
     *
     * @throws UedmException                如果在UEDM中发生错误
     * @throws com.zte.uedm.common.exception.UedmException 如果发生通用UEDM错误
     * @throws ParseException               如果日期解析出错
     * @throws InterruptedException         如果线程被中断
     */
    @Override
    public void autoDispatchStrategy() {
        try {
            String day = getCurrentDate();
            PageInfo<PeakShiftConfigVo> peakShiftConfigVoPageInfo = peakShiftConfigService.selectBatchPeakShiftConfig(new PeakShiftConfigQueryDto(), DEFAULT_LANGUAGE, "admin");
            List<PeakShiftConfigVo> collect = peakShiftConfigVoPageInfo.getList().stream().filter(bean -> "自动".equals(bean.getStrategyType())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(collect)) {
                return;
            }
            List<DevicePeakCacheInfoBean> devicePeakCacheInfoBeanList = new ArrayList<>();
            for (PeakShiftConfigVo peakShiftConfigVo : collect) {
                DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
                devicePeakCacheInfoBean.setDeviceId(peakShiftConfigVo.getId());
                devicePeakCacheInfoBean.setDeviceType(peakShiftConfigVo.getDeviceType());
                devicePeakCacheInfoBean.setDeviceName(peakShiftConfigVo.getName());
                devicePeakCacheInfoBeanList.add(devicePeakCacheInfoBean);
            }
            List<PeakShiftConfigPo> peakShiftConfigPoList = peakShiftConfigRepositoryImpl.selectAllPeakShiftConfig();
            peakShiftConfigPoMap.clear();
            peakShiftConfigPoMap = peakShiftConfigPoList.stream().collect(Collectors.toMap(PeakShiftConfigPo::getDeviceId, peakShiftConfigPo -> peakShiftConfigPo));
            for (DevicePeakCacheInfoBean devicePeakCacheInfoBean : devicePeakCacheInfoBeanList) {
                if (!processDevicePeakCacheInfo(devicePeakCacheInfoBean, day)) {
                    log.info("Skipped processing for device: {}", JSON.toJSONString(devicePeakCacheInfoBean));
                }
            }
        } catch (Exception e) {
            log.error("Error occurred during autoDispatchStrategy execution: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取当前日期，格式为'yyyy-MM-dd'。
     *
     * @return 当前日期的字符串格式
     */
    public String getCurrentDate() {
        return new SimpleDateFormat("yyyy-MM-dd").format(Calendar.getInstance().getTime());
    }

    /**
     * 处理单个DevicePeakCacheInfoBean以确定是否可以创建并执行峰谷任务。
     *
     * @param devicePeakCacheInfoBean 设备峰值缓存信息
     * @param day                     当前日期，格式为'yyyy-MM-dd'
     * @return 如果任务成功创建并执行，则返回true，否则返回false
     * @throws UedmException        如果在UEDM中发生错误
     * @throws ParseException       如果日期解析出错
     * @throws InterruptedException 如果线程被中断
     */
    public boolean processDevicePeakCacheInfo(DevicePeakCacheInfoBean devicePeakCacheInfoBean, String day) throws Exception {
        PeakShiftCommonService peakShiftCommonService = peakShiftFactory.generateByDeviceType(devicePeakCacheInfoBean.getDeviceType());
        if (peakShiftCommonService == null) {
            log.warn("No PeakShiftCommonService found for device type: {}", devicePeakCacheInfoBean.getDeviceType());
            return false;
        }

        List<String> batteryIds = getBatteryIds(peakShiftCommonService, devicePeakCacheInfoBean);
        if (CollectionUtils.isEmpty(batteryIds)) {
            log.warn("No batteries found for device: {}", JSON.toJSONString(devicePeakCacheInfoBean));
            return false;
        }

        List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(batteryIds);
        if (deviceListByIds.isEmpty()) {
            log.warn("No devices found for battery IDs: {}", JSON.toJSONString(batteryIds));
            return false;
        }

        String parentId = deviceListByIds.get(0).getParentId();
        String scopeId = getScopeId(parentId);
        if (StringUtils.isBlank(scopeId)) {
            log.warn("Scope ID not found for battery parent ID: {}", parentId);
            return false;
        }

        if (!validateAndProcessStrategy(devicePeakCacheInfoBean, peakShiftCommonService, day, scopeId, parentId)) {
            return false;
        }

        return createAndExecuteTask(devicePeakCacheInfoBean, day);
    }

    /**
     * 获取指定设备的电池ID。
     *
     * @param peakShiftCommonService 峰谷调度公共服务
     * @param devicePeakCacheInfoBean 设备峰值缓存信息
     * @return 电池ID列表
     */
    public List<String> getBatteryIds(PeakShiftCommonService peakShiftCommonService, DevicePeakCacheInfoBean devicePeakCacheInfoBean) throws UedmException {
        Map<String, List<String>> batteryByCollectIdAndFilterLoop = peakShiftCommonService.getBatteryByCollectIdAndFilterLoop(devicePeakCacheInfoBean.getDeviceId());
        List<String> batteryIds = batteryByCollectIdAndFilterLoop.getOrDefault(BatteryTypeEnums.li_ion.getId(), batteryByCollectIdAndFilterLoop.get(BatteryTypeEnums.vrla.getId()));
        log.info("Retrieved battery IDs for device: {}, battery IDs: {}", devicePeakCacheInfoBean.getDeviceId(), JSON.toJSONString(batteryIds));
        return batteryIds;
    }

    /**
     * 验证并处理给定设备的峰谷策略。
     *
     * @param devicePeakCacheInfoBean 设备峰值缓存信息
     * @param peakShiftCommonService  峰谷调度公共服务
     * @param day                     当前日期，格式为'yyyy-MM-dd'
     * @param scopeId                 范围ID
     * @param parentId                电池父ID
     * @return 如果策略有效且已处理，则返回true，否则返回false
     * @throws UedmException 如果在UEDM中发生错误
     */
    public boolean validateAndProcessStrategy(DevicePeakCacheInfoBean devicePeakCacheInfoBean, PeakShiftCommonService peakShiftCommonService, String day, String scopeId, String parentId) throws Exception {
        Map<String, List<IntervalStrategyDetailModeBean>> intervalStrategyMap = peakShiftService.getIntervalStrategyByScopeId(Collections.singletonList(scopeId), day);
        Map<String, TieredPriceStrategyBean> priceMap = peakShiftService.getPriceByScopeIdAndDate(Collections.singletonList(scopeId), day);

        if (!validateStrategyData(devicePeakCacheInfoBean, scopeId, parentId, intervalStrategyMap, priceMap)) {
            return false;
        }

        Map<String, BigDecimal> chargeDischargeDuration = peakShiftCommonService.calculateChargeDischargeDuration(devicePeakCacheInfoBean);
        BigDecimal chargeDuration = Optional.ofNullable(chargeDischargeDuration).orElse(new HashMap<>()).getOrDefault(CHARGE_TIME, BigDecimal.ZERO).setScale(0, RoundingMode.UP);
        BigDecimal dischargeDuration = Optional.ofNullable(chargeDischargeDuration).orElse(new HashMap<>()).getOrDefault(DIS_CHARGE_TIME, BigDecimal.ZERO).setScale(0, RoundingMode.DOWN);
        if(!operationInfo(devicePeakCacheInfoBean, chargeDuration, dischargeDuration)){
            return false;
        }

        IntervalStrategyQueryBean intervalStrategyQueryBean = new IntervalStrategyQueryBean();
        intervalStrategyQueryBean.setScopeStrategyId(scopeId);
        intervalStrategyQueryBean.setStatus(Collections.singletonList(1));
        List<SeasonStrategyBean> seasonStrategyBeans = gridStrategyMapper.queryIntervalStrategyList(intervalStrategyQueryBean);
        SeasonStrategyForTemplateVo strategy = strategyTemplateService.queryOneSeasonStrategyFotTemplate(seasonStrategyBeans.get(0).getId(), DEFAULT_LANGUAGE);
        id = UUID.randomUUID().toString();
        TemplateStrategyAddDto templateStrategyAddDto = peakShiftCommonService.processStrategy(strategy, devicePeakCacheInfoBean, dischargeDuration, chargeDuration);
        if (templateStrategyAddDto == null) {
            log.warn("Failed to process template strategy for device: {}", devicePeakCacheInfoBean.getDeviceId());
            return false;
        }

        log.info("Processed template strategy for device: {}, strategy: {}", devicePeakCacheInfoBean.getDeviceId(), JSON.toJSONString(templateStrategyAddDto));
        PeakShiftConfigPo peakShiftConfigPo = peakShiftConfigPoMap.get(devicePeakCacheInfoBean.getDeviceId());
        String strategyJson = peakShiftConfigPo.getStrategyCode();

        if (extracted(devicePeakCacheInfoBean, strategyJson, templateStrategyAddDto)) return false;


        return true;
    }



    /**
     * 获取给定电池父ID的范围ID。
     *
     * @param parentId 电池父ID
     * @return 范围ID
     */
    public String getScopeId(String parentId) throws com.zte.uedm.common.exception.UedmException, UedmException {
        Map<String, String> battParentIdScopeId = peakShiftService.getBattParentIdScopeId(Collections.singleton(parentId));
        String scopeId = battParentIdScopeId.get(parentId);
        log.info("Retrieved scope ID for parent ID: {}, scope ID: {}", parentId, scopeId);
        return scopeId;
    }




    /**
     * 为给定设备创建并执行峰谷任务。
     *
     * @param devicePeakCacheInfoBean 设备峰值缓存信息
     * @param day                     当前日期，格式为'yyyy-MM-dd'
     * @return 如果任务成功创建并执行，则返回true，否则返回false
     * @throws InterruptedException         如果线程被中断
     * @throws com.zte.uedm.common.exception.UedmException 如果发生通用UEDM错误
     * @throws UedmException                如果在UEDM中发生错误
     */
    public boolean createAndExecuteTask(DevicePeakCacheInfoBean devicePeakCacheInfoBean, String day) throws InterruptedException, com.zte.uedm.common.exception.UedmException, UedmException {
        TemplateQueryDto templateQueryDto = new TemplateQueryDto();
        templateQueryDto.setId(id);
        PageInfo<TemplateStrategyVo> templateStrategyVoPageInfo = strategyTemplateService.selectStrategyByCondition(templateQueryDto, DEFAULT_LANGUAGE);
        if (templateStrategyVoPageInfo.getList().isEmpty()) {
            log.warn("No template strategy found for device: {}", JSON.toJSONString(devicePeakCacheInfoBean));
            return false;
        }

        TemplateStrategyVo templateStrategyVo = templateStrategyVoPageInfo.getList().get(0);
        PeakShiftTaskDto peakShiftTaskDto = createPeakShiftTaskDto(devicePeakCacheInfoBean, templateStrategyVo, day);
        log.info("Creating peak shift task: {}", JSON.toJSONString(peakShiftTaskDto));

        updatePeakShiftTaskExpirationDate(devicePeakCacheInfoBean);
        log.info("Inserted peak shift task into distribution service for task ID: {}", peakShiftTaskDto.getId());
        List<String> applyRangeById = PeakDeviceTypeEnum.getApplyRangeById(PeakDeviceTypeEnum.DIRECT.id);
        if(applyRangeById.contains(devicePeakCacheInfoBean.getDeviceType())){
            List<DetailHistoryResposeBean> detailHistoryResposeBeans = configurationManagerRpc.detailHistoryExport(id);
            log.info("configurationManagerRpc.detailHistoryExport result:{}, id:{}", JSON.toJSONString(detailHistoryResposeBeans), id);
            //获取detailHistoryResposeBeans中所有fileId
            List<String> fileIds = detailHistoryResposeBeans.stream().map(DetailHistoryResposeBean::getFileId).collect(Collectors.toList());
            peakShiftTaskDto.setFileIds(fileIds);
            peakShiftDistributionService.insert(peakShiftTaskDto, DEFAULT_USER, serviceBean, DEFAULT_LANGUAGE);
            log.info("fileIds:{}", JSON.toJSONString(fileIds));
//            configurationManagerRpc.fileInfoSynchronization(fileIds);
        }else {
            peakShiftDistributionService.insert(peakShiftTaskDto, DEFAULT_USER, serviceBean, DEFAULT_LANGUAGE);
        }

        try {
            Pair<Integer, String> statusFlipResult = peakShiftDistributionTaskDomainService.doStatusFlip(peakShiftTaskDto.getId(), PENDING_STATUS, serviceBean);
            if (statusFlipResult.getLeft() != 0) {
                log.error("doStatusFlip error, taskId: {}, status: {}, message: {}", peakShiftTaskDto.getId(), PENDING_STATUS, statusFlipResult.getRight());
                return false;
            }
        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.error("doStatusFlip error, taskId: {}, status: {}, message: {}", peakShiftTaskDto.getId(), PENDING_STATUS, e.getMessage());
            return false;
        }
        return true;
    }

    public boolean extracted(DevicePeakCacheInfoBean devicePeakCacheInfoBean, String strategyJson, TemplateStrategyAddDto newTemplateStrategyAddDto) throws Exception {
        if(strategyJson != null){
            TemplateStrategyAddDto oldTemplateStrategyAddDto = jsonService.jsonToObject(strategyJson, TemplateStrategyAddDto.class);
            newTemplateStrategyAddDto.setId(oldTemplateStrategyAddDto.getId());
            TemplateDetailBaseDto oldTemplateDetailBaseDto = oldTemplateStrategyAddDto.getDetail().get(0);
            TemplateDetailBaseDto newTemplateDetailBaseDto = newTemplateStrategyAddDto.getDetail().get(0);
            newTemplateDetailBaseDto.setTimeRange(oldTemplateDetailBaseDto.getTimeRange());
            String json = jsonService.objectToJson(newTemplateStrategyAddDto);
            boolean result = areJsonsEqualIgnoringOrder(json, strategyJson);
            if (result) {
                log.info("The strategy is the same as the old one, no need to update.");
                return true;
            }
            id = oldTemplateStrategyAddDto.getId();
            log.info("id:{}, oldTemplateStrategyAddDto:{}, newTemplateStrategyAddDto:{}", id, JSON.toJSONString(oldTemplateStrategyAddDto), JSON.toJSONString(newTemplateStrategyAddDto));
            TemplateStrategyEditDto templateStrategyEditDto = new TemplateStrategyEditDto();
            //准备更新模版的bean
            BeanUtils.copyProperties(newTemplateStrategyAddDto, templateStrategyEditDto);
            templateStrategyEditDto.setId(oldTemplateStrategyAddDto.getId());
            //更新自动模版
            templateStrategyService.editTemplateStrategy(templateStrategyEditDto, DEFAULT_USER);
            //准备更新peakshift-config数据库的jsonbean
            PeakShiftConfigPo po = new PeakShiftConfigPo();
            po.setDeviceId(devicePeakCacheInfoBean.getDeviceId());
            po.setStrategyCode(json);
            peakShiftConfigRepositoryImpl.updateStrategyCode(po);

        }else {
            newTemplateStrategyAddDto.setId(id);
            String json = jsonService.objectToJson(newTemplateStrategyAddDto);
            PeakShiftConfigPo po = new PeakShiftConfigPo();
            po.setDeviceId(devicePeakCacheInfoBean.getDeviceId());
            po.setStrategyCode(json);
            peakShiftConfigRepositoryImpl.updateStrategyCode(po);
            templateStrategyService.addTemplateStrategy(newTemplateStrategyAddDto, DEFAULT_USER);
        }
        return false;
    }

    /**
     * 为给定设备和策略创建PeakShiftTaskDto。
     *
     * @param devicePeakCacheInfoBean 设备峰值缓存信息
     * @param templateStrategyVo      模板策略
     * @param day                     当前日期，格式为'yyyy-MM-dd'
     * @return 创建的PeakShiftTaskDto
     */
    public PeakShiftTaskDto createPeakShiftTaskDto(DevicePeakCacheInfoBean devicePeakCacheInfoBean, TemplateStrategyVo templateStrategyVo, String day) {
        String currentTime = dateTimeService.getCurrentTime();
        //去掉currentTime中的空格和:和-
        currentTime = currentTime.replaceAll(" ", "").replaceAll(":", "").replaceAll("-", "");
        PeakShiftTaskDto peakShiftTaskDto = new PeakShiftTaskDto();
        peakShiftTaskDto.setId(UUID.randomUUID().toString());
        String deviceId = devicePeakCacheInfoBean.getDeviceId();
        String deviceNewId = deviceId.substring(deviceId.lastIndexOf("-") + 1);
        peakShiftTaskDto.setName(devicePeakCacheInfoBean.getDeviceName() + "-" + deviceNewId + "-" + AUTOTASK + "-" + currentTime);
        peakShiftTaskDto.setTemplateStrategyId(templateStrategyVo.getId());
        peakShiftTaskDto.setDeviceType(devicePeakCacheInfoBean.getDeviceType());
        peakShiftTaskDto.setDeviceIds(Collections.singletonList(devicePeakCacheInfoBean.getDeviceId()));
        peakShiftTaskDto.setEffectiveDate(day + " 00:00:00");
        peakShiftTaskDto.setExpirationDate(day.substring(0, 4) + "-12-31 23:59:59");
        log.info("Created PeakShiftTaskDto: {}", JSON.toJSONString(peakShiftTaskDto));
        return peakShiftTaskDto;
    }
    /* Ended by AICoder, pid:g27a2ob16bs0c3014b4408ecf204259a2c55489c */

    /* Started by AICoder, pid:xb217nb3fbr59eb14ff708abf0061867db48f988 */
    /**
     * 验证给定范围和父ID的策略数据。
     *
     * @param scopeId           范围ID
     * @param parentId          父ID
     * @param intervalStrategyMap 时段策略映射
     * @param priceMap          价格映射
     * @return 如果策略数据有效，则返回true，否则返回false
     */
    public boolean validateStrategyData(DevicePeakCacheInfoBean devicePeakCacheInfoBean, String scopeId, String parentId,
                                        Map<String, List<IntervalStrategyDetailModeBean>> intervalStrategyMap,
                                        Map<String, TieredPriceStrategyBean> priceMap) throws com.zte.uedm.common.exception.UedmException {
        TieredPriceStrategyBean priceStrategyBean = priceMap.get(scopeId);
        List<IntervalStrategyDetailModeBean> intervalStrategyBeans = intervalStrategyMap.get(scopeId);

        if (priceStrategyBean == null || CollectionUtils.isEmpty(intervalStrategyBeans)) {
            log.error("Price or interval strategy not found for battery pack:  {}, price: {}, interval: {}",
                    parentId, JSON.toJSONString(priceStrategyBean), JSON.toJSONString(intervalStrategyBeans));
            return handleOperationFailure(devicePeakCacheInfoBean, "电价未满足自动策略生成条件",
                    "Price does not meet the conditions for automatic generation.");
        }
        return true;
    }

    private boolean handleOperationFailure(DevicePeakCacheInfoBean devicePeakCacheInfoBean, String detailZhMessage, String detailEnMessage) throws com.zte.uedm.common.exception.UedmException {
        OperlogBean oper = createOperLog();
        StringBuilder detailZh = new StringBuilder("采集器：").append(devicePeakCacheInfoBean.getDeviceName()).append(detailZhMessage).append("\n");
        StringBuilder detailEn = new StringBuilder("Collector：").append(devicePeakCacheInfoBean.getDeviceName()).append(detailEnMessage).append("\n");

        boolean operStatus = refreshOperStatus(oper, detailZh, detailEn);
        sendOperLogAsync(oper);
        return false;
    }

    public static boolean isDetailArray(ArrayNode arrayNode) {
        // 判断是否为 "detail" 数组，依据其元素结构判断
        return arrayNode.size() > 0 && arrayNode.get(0).has("beginTime");
    }

    public boolean operationInfo(DevicePeakCacheInfoBean devicePeakCacheInfoBean, BigDecimal chargeDuration, BigDecimal dischargeDuration) throws com.zte.uedm.common.exception.UedmException {
        if (chargeDuration.compareTo(BigDecimal.ZERO) == 0 || dischargeDuration.compareTo(BigDecimal.ZERO) == 0) {
            log.error("Charge duration or discharge duration is 0, device: {}, charge duration: {}, discharge duration: {}",
                    devicePeakCacheInfoBean.getDeviceId(), chargeDuration, dischargeDuration);
            return handleOperationFailure(devicePeakCacheInfoBean, "充电时长或放电时长为0",
                    "Charge duration or discharge duration is 0.");
        }

        if (chargeDuration.compareTo(BigDecimal.valueOf(24)) >= 0) {
            log.error("Charge duration is greater than 24, device: {}, charge duration: {}, discharge duration: {}",
                    devicePeakCacheInfoBean.getDeviceId(), chargeDuration, dischargeDuration);
            return handleOperationFailure(devicePeakCacheInfoBean, "充电时长超过24小时",
                    "Charge duration is greater than 24 hours.");
        }

        return true;
    }

    public static void sortDetailArray(ArrayNode arrayNode) {
        // 按 beginTime 字段排序
        var sortedList = StreamSupport.stream(arrayNode.spliterator(), false)
                .sorted(Comparator.comparing(node -> node.get("beginTime").asText()))
                .collect(Collectors.toList());

        arrayNode.removeAll(); // 清空原数组
        sortedList.forEach(arrayNode::add); // 添加排序后的元素
    }

    public void updatePeakShiftTaskExpirationDate(DevicePeakCacheInfoBean devicePeakCacheInfoBean) throws com.zte.uedm.common.exception.UedmException {
        PeakShiftConfigPo peakShiftConfigPo = peakShiftConfigPoMap.get(devicePeakCacheInfoBean.getDeviceId());
        String strategyJson = peakShiftConfigPo.getStrategyCode();
        if (strategyJson == null) {
            return;
        }
        TemplateStrategyAddDto templateStrategyAddDto = jsonService.jsonToObject(strategyJson, TemplateStrategyAddDto.class);
        // 用 "-" 分割字符串
        String[] parts = templateStrategyAddDto.getName().split("-");

        String taskName = parts[parts.length - 2];

        String currentTime = dateTimeService.getCurrentTime();


        peakShiftTaskMapper.updatePeakShiftTaskExpirationDate(taskName, currentTime);
    }

    private OperlogBean createOperLog() {
        return new OperlogBean(GlobalConstants.SYSTEM, GlobalConstants.SYSTEM_IP, UserThreadLocal.getLoginType(),
                PEAK_SHIFT_STRATEGY_MODULE_ZH, PEAK_SHIFT_STRATEGY_MODULE_EN,
                OperlogBean.LogRank.operlog_rank_important,
                PEAK_SHIFT_STRATEGY_OPERATION_ZH, PEAK_SHIFT_STRATEGY_OPERATION_EN);
    }

    private boolean refreshOperStatus(OperlogBean oper, StringBuilder detailZh, StringBuilder detailEn) {
        oper.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
        return true;
    }

    private void sendOperLogAsync(OperlogBean oper) throws com.zte.uedm.common.exception.UedmException {
        String operMsg = jsonService.objectToJson(oper);
        msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
    }
    /* Ended by AICoder, pid:xb217nb3fbr59eb14ff708abf0061867db48f988 */

    public boolean areJsonsEqualIgnoringOrder(String json1, String json2) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode tree1 = objectMapper.readTree(json1);
        JsonNode tree2 = objectMapper.readTree(json2);

        // 对两个 JSON 树进行排序处理
        sortJsonTree(tree1);
        sortJsonTree(tree2);

        // 比较排序后的 JSON
        return tree1.equals(tree2);
    }

    public static void sortJsonTree(JsonNode node) {
        if (node.isObject()) {
            // 遍历对象中的所有字段
            node.fields().forEachRemaining(entry -> sortJsonTree(entry.getValue()));
        } else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            if (isDetailArray(arrayNode)) {
                // 如果是 "detail" 数组，按 beginTime 排序
                sortDetailArray(arrayNode);
            }
            // 递归处理数组中的每个元素
            arrayNode.forEach(AutoPeakShiftStrategyServiceImpl::sortJsonTree);
        }
    }

}
