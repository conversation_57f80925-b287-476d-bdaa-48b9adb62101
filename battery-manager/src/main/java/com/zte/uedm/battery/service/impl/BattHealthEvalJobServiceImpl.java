package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.bean.BatteryEvalBean;
import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.battery.bean.MocHistoryQueryToolBean;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.bean.pv.HistoryDataRequestConditionBean;
import com.zte.uedm.battery.bean.pv.HistoryDataResponseConditionBean;
import com.zte.uedm.battery.controller.battAiConfig.vo.AiConfigVo;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.po.OverviewAssetConditionPo;
import com.zte.uedm.battery.domain.po.OverviewConditionPo;
import com.zte.uedm.battery.rpc.dto.BatterySohPredictionDto;
import com.zte.uedm.battery.rpc.impl.AiForecastServiceRpcImpl;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.BattOverviewAssetConditionVo;
import com.zte.uedm.battery.rpc.vo.SohMsgBean;
import com.zte.uedm.battery.service.BattHealthEvalJobService;
import com.zte.uedm.battery.service.BattHealthStatusEvaluateService;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryHealthyBeanUtils;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBaseBean;
import com.zte.uedm.common.configuration.point.bean.RecordIndexBean;
import com.zte.uedm.common.enums.RoleNameTypeEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.*;
import static com.zte.uedm.battery.consts.CommonConst.*;

@Service
@Slf4j
public class BattHealthEvalJobServiceImpl implements BattHealthEvalJobService
{
    @Setter
    @Autowired(required = false)
    private Map<String, BattHealthStatusEvaluateService> hs;

    @Autowired
    private BattTypeDomain battTypeDomain;

    @Autowired
    private BattAlarmDomain battAlarmDomain;

    @Autowired
    private BattConfigurationDomain battConfigurationDomain;

    @Autowired
    private MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Autowired
    private MoHistoryDataQueryDomain moHistoryDataQueryDomain;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private AssetRpcImpl assetRpcImpl;

    @Autowired
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;

    @Autowired
    private BatteryHisDataDomain batteryHisDataDomain;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private BatteryHealthyBeanUtils batteryHealthyBeanUtils;
    @Autowired
    private AiForecastServiceRpcImpl aiForecastServiceRpc;
    @Autowired
    private BattAISwitchService battAISwitchService;

    //
    private static final int PAGE_SIZE = 1000;

    private static final int PAGE_SIZE_AI = 3000;

    //电池类型 电池健康状态
    private static String[] standPoints = {BATTERY_SMPID_HEALTH};

    private static final String BATT_SOC = "batt.batt_charge.final.soc";

    private static final String ZH_CN = "zh_CN";
    private static final String EN_US = "en_US";

    @Override
    public void evalBatteryHealth(String type)
    {
        try
        {
            List<BattTypeBean> battTypeBeans = battTypeDomain.getBatteryTypeByMoId();
            if(CollectionUtils.isNotEmpty(battTypeBeans))
            {
                List<MonitorObjectBaseBean> monitorObjectBaseBeans = new ArrayList<>(battConfigurationDomain.selectBattByLogicId(GlobalOptional.GLOBAL_ROOT, new ServiceBaseInfoBean(HeaderUtils.ROOT_USER, "", "", null, null)));
                List<String> moIds = monitorObjectBaseBeans.stream().map(MonitorObjectBaseBean::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(monitorObjectBaseBeans))
                {
                    // 构建电池idpath和namepath
                    Map<String, String> idPathMap = monitorObjectBaseBeans.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getPathId()))
                            .collect(Collectors.toMap(MonitorObjectBaseBean::getId, MonitorObjectBaseBean::getPathId, (key1,key2)->key2));
                    Map<String, String> idNameMap = monitorObjectBaseBeans.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getPath()))
                            .collect(Collectors.toMap(MonitorObjectBaseBean::getId, MonitorObjectBaseBean::getPath, (key1,key2)->key2));
                    log.info("BattHealthEvalJobServiceImpl->evalBatteryHealth, idPathMap.size={}, idNameMap.size={} ", idPathMap.size(), idNameMap.size());
                    List<HistoryDataRequestConditionBean> conditionBeans = buildAllQueryConditions(standPoints);
                    Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = batteryHisDataDomain.getHistoryDataByConditions(conditionBeans);
                    hisDataResponseMap.forEach((key, list) -> log.info("BattHealthEvalJobServiceImpl hisDataResponseMap key:{}, size=:{}", key, list.size()));
                    //对象关联资产信息
                    // bug batch
                    List<BattOverviewAssetConditionVo> assetInfoByCondition=new ArrayList<>();
                    BatchUtils.doInBatch(5000,moIds,(item)->{
                        try {
                            assetInfoByCondition.addAll(assetRpcImpl.getAssetInfoByCondition(new OverviewAssetConditionPo(new OverviewConditionPo(), item)));
                        } catch (Exception e) {
                            log.error("evalBatteryHealth selectPreHealthStatusByMoIds is message:{},error:{}",e.getMessage(),e);
                        }
                    });
                    Map<String, BattOverviewAssetConditionVo> idAssetInfoMap = assetInfoByCondition.stream().filter(bean-> StringUtils.isNotBlank(bean.getId()))
                            .collect(Collectors.toMap(BattOverviewAssetConditionVo::getId, Function.identity(), (key1,key2)->key2));
                    Date date = new Date();
                    //前一次评估状态
                    // bug  batch
                    List<BattHealthStatusEvalPo> battpreStatusEvalPos =new ArrayList<>();
                    BatchUtils.doInBatch(5000,moIds,(item)->{
                        try {
                            battpreStatusEvalPos.addAll(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(item));
                        } catch (Exception e) {
                            log.error("evalBatteryHealth selectPreHealthStatusByMoIds is message:{},error:{}",e.getMessage(),e);
                        }
                    });
                    Map<String, BattHealthStatusEvalPo> idPrestateMap = battpreStatusEvalPos.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).collect(Collectors
                            .toMap(BattHealthStatusEvalPo::getId, bean->bean, (key1, key2) -> key2));
                    log.debug("idPrestateMap is : {}", idPrestateMap);
                    //初始化数据
                    List<BattHealthStatusEvalPo> battHealthStatusEvalPos = initialBattEvalData(moIds, idAssetInfoMap, idPathMap, idNameMap,  date, type);
                    log.info("battHealthStatusEvalPos is : {}", battHealthStatusEvalPos.size());
                    Map<String, BattHealthStatusEvalPo> battHealthStatusEvalPoMap = battHealthStatusEvalPos.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).collect(Collectors.toMap(
                            BattHealthStatusEvalPo::getId, a -> a,(k1,k2)->k1));
                    evaluateStatus(battTypeBeans,hs,moIds,battHealthStatusEvalPoMap,hisDataResponseMap,idPrestateMap);//开始评估
                }
            }
        }
        catch (Exception e)
        {
            log.error("evalBatteryHealth is error.", e);
        }
    }

    private void evaluateStatus(List<BattTypeBean> battTypeBeans, Map<String, BattHealthStatusEvaluateService> hs,List<String> moIds,
                                Map<String, BattHealthStatusEvalPo> battHealthStatusEvalPoMap,Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap,
                                Map<String, BattHealthStatusEvalPo> idPrestateMap) throws UedmException, ParseException
    {
        Map<String, Map<String, String>> nameMap = getStandardPointNameMap();
        log.debug("evaluateStatus nameMap is:{}",nameMap);
        Map<String, BatteryEvalDTO> dtoMap = new HashMap<>();
        moIds.forEach(m->{
            dtoMap.putIfAbsent(m,new BatteryEvalDTO());
        });
        //获取告警信息
        Map<String, Boolean> battAlarmInfo = battAlarmDomain.selectHealthAlarmByMoIds(moIds,dtoMap,nameMap);

        //获取SOH-license和AI预测数据
        Pair<Boolean, Map<String, SohMsgBean>> sohMsgBeanMap = getAiSohData(battTypeBeans);
        log.info("BattHealthEvalJobServiceImpl sohMsgBeanMap size=:{}", sohMsgBeanMap.getValue().size());

        for(BattTypeBean bean : battTypeBeans)
        {
            BattHealthStatusEvaluateService battHealthStatusEvaluateService = hs.get(bean.getBattType().getNameEn());
            if(null != battHealthStatusEvaluateService && moIds.contains(bean.getId()))
            {
                String moId = bean.getId();
                Boolean isAlarm = battAlarmInfo.get(moId);
                BattHealthStatusEvalPo battHealthStatusEvalPo = battHealthStatusEvalPoMap.get(moId);
                BatteryEvalDTO dto = dtoMap.get(moId);

                log.debug("[{}]  alarm is :{},dto is:{}",moId,isAlarm,dtoMap.get(moId));
                if (null != dto && null != battHealthStatusEvalPo) {
                    //添加电池类型
                    battHealthStatusEvalPo.setBattType(bean.getBattType().getCode());
                    //增加告警评估原因
                    if (isAlarm) {
                        dto.setAlarm(TRUE);
                        battHealthStatusEvalPo.setEvalReason(batteryHealthyBeanUtils.beanToJson(dto, BatteryEvalBean::buildBeanAlarm));
                    }{
                        dto.setAlarm(FALSE);
                    }
                    //根据初始化数据、告警信息、历史数据评估电池健康
                    //增加异常保护机制
                    try {
                        battHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo, isAlarm, hisDataResponseMap, moId, idPrestateMap, nameMap, dto, sohMsgBeanMap);
                    }catch (Exception e){
                        log.error("[{}] battery health eval exists exception.",moId,e);
                    }
                }
            }
        }
    }

    /**
     * 获取健康标准测点名称map
     * @return map k:标准测点key v:标准测点名称
     */
    private Map<String, Map<String, String>> getStandardPointNameMap() {
        List<StandardPointEntity> pointByIds = new ArrayList<>();
        Map<String, Map<String, String>> map = new HashMap<>();

        //调用rpc查询标准测点名称
        try {
            pointByIds.addAll(configurationManagerRpc.getStandardPointByIds(Arrays.asList(BATTERY_SMPID_HEALTH,
                    BATTERY_SMPID_CELL_POOR_ALARM, BATTERY_SMPID_RATED_CAPACITY), MocOptional.BATTERY.getId()));
            pointByIds.addAll(configurationManagerRpc.getStandardPointByIds(Arrays.asList(BATTERYSET_SMPID_BATT_CURR_IMBL_ALARM,
                   BATTERYSET_SMPID_BATT_DETECT_ABNORMAL_ALARM), MocOptional.BATTERY_SET.getId()));
            if (CollectionUtils.isNotEmpty(pointByIds)) {
                pointByIds.forEach(p -> {
                    if (StringUtils.isNoneBlank(p.getId())) {
                        try {
                            map.putIfAbsent(p.getId(), JsonUtils.jsonToObject(p.getName(), Map.class));
                        } catch (com.zte.uedm.basis.exception.UedmException e) {
                            log.error("point:{} getName:{} error", p.getId(), p.getName());
                        }
                    }
                });
            }

            //调用rpc记录测点表获取电池组终止容量比率
            List<RecordIndexBean> indexByIds = configurationManagerRpc.getRecordIndexByIds(BATT_SOC);
            if (CollectionUtils.isNotEmpty(indexByIds)){
                indexByIds.forEach(i->{
                    if (StringUtils.isNoneBlank(i.getId())) {
                        log.info("indexByIds i18Name:{}",i.getNameI18n());
                        Map<String, String> stringMap = new HashMap<>();
                        stringMap.putIfAbsent(ZH_CN, i18nUtils.getMapFieldByLanguageOption(i.getNameI18n(), ZH_CN));
                        stringMap.putIfAbsent(EN_US, i18nUtils.getMapFieldByLanguageOption(i.getNameI18n(), EN_US));
                        map.put(i.getId(), stringMap);
                    }
                });
            }
        } catch (UedmException e) {
            log.error("BattHealthEvalJobService config rpc getStandardPointByIds is error ", e);
        }

        return map;
    }


    /**
     * 初始化评估
     * @param moIds
     * @param idAssetInfoMap
     * @param idPathMap
     * @param idNameMap
     * @param date
     * @throws UedmException
     */
    public List<BattHealthStatusEvalPo> initialBattEvalData(List<String> moIds, Map<String, BattOverviewAssetConditionVo> idAssetInfoMap, Map<String, String> idPathMap,
                                                            Map<String, String> idNameMap, Date date, String evalOrigin) throws UedmException
    {
        List<BattHealthStatusEvalPo> battHealthStatusEvalPos = new ArrayList<>();
        Date yearMonDayDate = DateUtils.getYearMonDayDate(date);
        Date yesterday = DateUtils.getYesTerDay(new Date());
        for(String moId: moIds)
        {
            BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
            battHealthStatusEvalPo.setId(moId);
            battHealthStatusEvalPo.setEvaluateTime(yearMonDayDate);
            battHealthStatusEvalPo.setPathIds(idPathMap.get(moId));
            battHealthStatusEvalPo.setPathNames(idNameMap.get(moId));
            battHealthStatusEvalPo.setCollectionTime(yesterday);

            battHealthStatusEvalPo.setOriginateFrom(evalOrigin);
            if(null != idAssetInfoMap.get(moId))
            {
                battHealthStatusEvalPo.setStartDate(Optional.ofNullable(idAssetInfoMap.get(moId)).map(BattOverviewAssetConditionVo::getStartDate).map(DateUtils::getYearMonDayDate).orElse(null));
                battHealthStatusEvalPo.setProductionDate(Optional.ofNullable(idAssetInfoMap.get(moId)).filter(bean -> StringUtils.isNotBlank(bean.getProductionDate()))
                        .map(BattOverviewAssetConditionVo::getProductionDate).map(DateUtils::getYearMonDayDate).orElse(null));
            }
            battHealthStatusEvalPo.setCreator(RoleNameTypeEnum.getSystemName());
            battHealthStatusEvalPo.setGmtCreate(date);
            battHealthStatusEvalPo.setUpdater(RoleNameTypeEnum.getSystemName());
            battHealthStatusEvalPo.setGmtModified(date);
            battHealthStatusEvalPos.add(battHealthStatusEvalPo);
        }
        return battHealthStatusEvalPos;
    }

    public List<HistoryDataRequestConditionBean> buildAllQueryConditions(String[] standPoints)
    {
        List<HistoryDataRequestConditionBean> conditionBeans = new ArrayList<>();
        //查询昨天的数据,组装查询条件
        MocHistoryQueryToolBean mocHistoryQueryToolBean = moHistoryDataQueryDomain.buildYesterdayQueryBean(MocOptional.BATTERY.getId());
        if(standPoints.length > 0)
        {
            for(int i = 0; i < standPoints.length; i++)
            {
                HistoryDataRequestConditionBean battHisDataConditionBean = moHistoryDataQueryDomain
                        .buildHistoryDataConditionBean(MocOptional.BATTERY.getId(), standPoints[i], mocHistoryQueryToolBean.getStartTime(), mocHistoryQueryToolBean.getEndTime());
                conditionBeans.add(battHisDataConditionBean);
            }
        }
        return conditionBeans;
    }

    private Pair<Boolean, Map<String, SohMsgBean>> getAiSohData(List<BattTypeBean> battTypeBeans) throws UedmException {
        AiConfigVo aiConfigVo = battAISwitchService.selectConfigById(AI_SWITCH_ID_SOH, ZH_CN);
        if (!aiConfigVo.getFlag() || StringUtils.equals(AI_SWITCH_OFF, aiConfigVo.getValue())) {
            return Pair.of(false, new HashMap<>());
        }
        List<String> lepList = battTypeBeans.stream().filter(battTypeBean -> StringUtils.equals("1", battTypeBean.getBattType().getCode())).map(BattTypeBean::getId).distinct().collect(Collectors.toList());

        log.info("aiSohData lepList:{}", lepList.size());
        List<SohMsgBean> sohMsgBeanList = new ArrayList<>();
        BatchUtils.doInBatch(PAGE_SIZE_AI, lepList, (item)->{
            try {
                sohMsgBeanList.addAll(aiForecastServiceRpc.queryPreSohData(new BatterySohPredictionDto(item, TimeUtils.getDayStartAndEndTime().getLeft(), TimeUtils.getDayStartAndEndTime().getRight())));
            } catch (Exception e) {
                log.error("evalBatteryHealth getAiSohData error:{}", e);
            }
        });

        Map<String, SohMsgBean> sohMsgBeanMap = sohMsgBeanList.stream().filter(bean -> StringUtils.isNotBlank(bean.getBatt())).collect(Collectors.toMap(SohMsgBean::getBatt, bean -> bean, (key1, key2) -> key2));
        return Pair.of(true, sohMsgBeanMap);
    }
}
