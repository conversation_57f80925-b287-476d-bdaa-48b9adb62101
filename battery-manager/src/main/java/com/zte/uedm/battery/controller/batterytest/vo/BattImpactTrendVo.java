/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattImpactTrendVo
 * 文件作者：00248587
 * 开发时间：2023/3/14
 */
package com.zte.uedm.battery.controller.batterytest.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BattImpactTrendVo
{

    /**
     * 测试概览维度key
     */
    private String id;
    /**
     * 维度名称  -中英文国际化
     */
    private String name;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 顺序
     */
    private Integer sequence ;
    /**
     * 是否启用
     */
    private Boolean enable ;
    /**
     *  默认顺序
     */
    private Integer defaultIndex;
    /**
     *  默认是否启动
     */
    private Boolean defaultEnable ;
    /**
     * 是否固定（指true时不可能变动，包括顺序和是否勾选）
     */
    private Boolean defaultFixed ;

    private Boolean sortable;

    /**
     * 是否隐藏
     */
    private Boolean assetAttributeShow = true;
}
