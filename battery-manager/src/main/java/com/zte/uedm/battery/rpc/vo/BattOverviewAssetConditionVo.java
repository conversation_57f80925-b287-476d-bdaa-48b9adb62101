package com.zte.uedm.battery.rpc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class BattOverviewAssetConditionVo
{
    /**
     * 电池id
     */
    private String id;
    /**
     * 电池资产id
     */
    private String assetNumber;
    /**
     * 电池种类
     */
    private String battType;
    /**
     * 电池额定容量
     */
    private String ratedCap;
    /**
     * 电池供应商
     */
    private String supplier;
    /**
     * 电池制造商
     */
    private String manufacture;
    /**
     * 电池品牌
     */
    private String brand;
    /**
     * 电池系列
     */
    private String series;
    /**
     * 电池型号
     */
    private String model;
    /**
     * 电池启用日期
     */
    private String startDate;

    /**
     * 电池生产日期
     */
    private String productionDate;

    /**
     * 电池维保日期
     */
    private String maintenancePeriod;

    private String manufactureName;

    private String brandName;

    private String seriesName;

    private String modelName;

    private String assetModelId;

}
