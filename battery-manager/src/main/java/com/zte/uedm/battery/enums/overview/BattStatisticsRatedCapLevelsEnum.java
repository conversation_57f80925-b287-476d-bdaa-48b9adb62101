package com.zte.uedm.battery.enums.overview;


/**
 * @FileName : BattStatisticsTypesEnum
 * @FileDesc : TODO
 * <AUTHOR> wc 10263256
 * @date Date : 2022年05月16日 20:27
 * @Version : 1.0
 */

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池的统计维度
 * 使用场景为电池总览页面
 */
public enum BattStatisticsRatedCapLevelsEnum {

    TYPE_1("1","30A" ),
    TYPE_2("2","50A" ),
    TYPE_3("3","75A" ),
    TYPE_4("other","其他" );



    private String id;
    private String name;

    public String getId()
    {
        return this.id;
    }
    public String getName()
    {
        return this.name;
    }

    BattStatisticsRatedCapLevelsEnum(String id, String name)
    {
        this.id = id;
        this.name = name;
    }


    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getAllBattStatisticsRatedCapLevelsIds()
    {
        List<String> allIds = new ArrayList<>();

        BattStatisticsRatedCapLevelsEnum[] values = BattStatisticsRatedCapLevelsEnum.values();
        for(BattStatisticsRatedCapLevelsEnum value : values)
        {
            if(StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }
    /**
     * 根据id获取name
     * @return
     */
    public static String getNameById(String id)
    {
        String name = null;
        BattStatisticsRatedCapLevelsEnum[] values = BattStatisticsRatedCapLevelsEnum.values();
        for(BattStatisticsRatedCapLevelsEnum value : values)
        {
            if( id.equals(value.getId()) ){
                name = value.getName();
            }

        }


        return name;
    }


}
