package com.zte.uedm.battery.util;

import com.zte.uedm.battery.bean.BatteryEvalBean;
import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Slf4j
@Service
public class BatteryHealthyBeanUtils {

    @Autowired
    private JsonService jsonService;

    /**
     * 根据电池健康dto构建bean并转json
     * @param dto 电池健康dto
     * @param function func
     */
    public String beanToJson (BatteryEvalDTO dto, Function<BatteryEvalDTO, BatteryEvalBean> function){
        try {
            BatteryEvalBean bean = function.apply(dto);
            return jsonService.objectToJson(bean);
        }catch (UedmException e){
            log.error("BatteryHealthyBeanUtils beanToJson error", e);
        }
        return "";
    }

    /**
     * 根据电池健康规则rule构建bean并转json
     * @param rule 电池健康规则
     * @param function func
     */
    public String beanToJson (String rule, Function<String, BatteryEvalBean> function){
        try {
            BatteryEvalBean bean = function.apply(rule);
            return jsonService.objectToJson(bean);
        }catch (UedmException e){
            log.error("BatteryHealthyBeanUtils beanToJson error",e);
        }
        return "";
    }
}
