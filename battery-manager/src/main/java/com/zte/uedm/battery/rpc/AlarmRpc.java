package com.zte.uedm.battery.rpc;

import com.zte.uedm.battery.bean.alarm.ActiveAlarmBody;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * 告警rpc
 */
@ServiceHttpEndPoint(serviceName = "fm-active", serviceVersion = "v1")
public interface AlarmRpc
{
    /**
     * 查询当前告警
     *
     * @param alarmBody
     *            请求body
     * @return 当前告警
     */

    @POST("north/openapi/v1/activealarms")
    Call<AlarmResponse> getActiveAlarm(@Body ActiveAlarmBody alarmBody, @Header("language-option") String lang);




}
