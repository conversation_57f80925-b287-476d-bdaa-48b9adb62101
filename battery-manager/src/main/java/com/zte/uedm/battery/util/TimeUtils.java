package com.zte.uedm.battery.util;

import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.common.exception.UedmException;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zte.uedm.battery.consts.DateTypeConst.*;

/**
 * 时间转换的工具类
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class TimeUtils
{
    private TimeUtils()
    {

    }

    /**
     * yyyy-MM-dd
     * @param strTime
     * @return
     * @throws ParseException
     */
    public static Long getLongTime_d(String strTime) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        Date date = format.parse(strTime);
        return date.getTime();
    }

    /**
     * yyyy-MM
     * @param strTime
     * @return
     * @throws ParseException
     */
    public static Long getLongTime_m(String strTime) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = format.parse(strTime);
        return date.getTime();
    }

    /**
     * 日期字符串转long（毫秒级和秒级）
     * @param strTime
     * @return
     */
    public static Long getLongTime(String strTime) throws ParseException {
        SimpleDateFormat format;
        if (StringUtils.isNotBlank(strTime) && strTime.length() == DateTypeConst.DATE_FORMAT_5.length())
        {
            format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_5);
        }
        else
        {
            format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        }
        Date date = format.parse(strTime);
        return date.getTime();
    }

    public static String getStrTime(Long millisecond)
    {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date date = new Date(millisecond);
        return format.format(date);
    }

    /**
     * 判断字符串是否是 yyyy-MM-dd 格式
     * @param mes
     * @return
     */
    public static boolean isRqSjFormat_d(String mes){

        if (StringUtils.isEmpty(mes)) {
            return false;
        }
        String s = mes.replace("-","")
                .replace(":","")
                .replace(" ","");

        String format = "([0-9]{4})(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])";
        Pattern pattern = Pattern.compile(format);
        Matcher matcher = pattern.matcher(s);
        if (matcher.matches()) {
            pattern = Pattern.compile("(\\d{4})(\\d{2})(\\d{2}).*");
            matcher = pattern.matcher(s);
            if (matcher.matches()) {
                int y = Integer.valueOf(matcher.group(1));
                int m = Integer.valueOf(matcher.group(2));
                int d = Integer.valueOf(matcher.group(3));
                if (d > 28) {
                    Calendar c = Calendar.getInstance();
                    c.set(y, m-1, 1);
                    // 每个月的最大天数
                    int lastDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
                    return (lastDay >= d);
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 判断字符串是否是 yyyy-MM-dd HH:mm:ss格式
     * @param mes
     * @return
     */
    public static boolean isRqSjFormat_min(String mes){

        if (StringUtils.isEmpty(mes)) {
            return false;
        }
        String s = mes.replace("-", "")
                .replace(":", "")
                .replace(" ", "");

        String format = "([0-9]{4})(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])"
                + "([01][0-9]|2[0-3])[0-5][0-9][0-5][0-9]";
        Pattern pattern = Pattern.compile(format);
        Matcher matcher = pattern.matcher(s);
        if (matcher.matches()) {
            pattern = Pattern.compile("(\\d{4})(\\d{2})(\\d{2}).*");
            matcher = pattern.matcher(s);
            if (matcher.matches()) {
                int y = Integer.valueOf(matcher.group(1));
                int m = Integer.valueOf(matcher.group(2));
                int d = Integer.valueOf(matcher.group(3));
                if (d > 28) {
                    Calendar c = Calendar.getInstance();
                    c.set(y, m-1, 1);
                    // 每个月的最大天数
                    int lastDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
                    return (lastDay >= d);
                }
            }
            return true;
        }
        return false;
    }

    /*
     * 比较两个日期之前相差的天数
     */
    public static Integer differentDays(String timeBegin, String timeEnd) throws UedmException, ParseException
    {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        Date date1 = format.parse(timeBegin);

        Date date2 = format.parse(timeEnd);

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) // 不同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++)
            {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) // 闰年
                {
                    timeDistance += 366;
                }
                else // 不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        }
        else // 同一年
        {
            return day2 - day1;
        }
    }

    /**
     * 获取两个日期之间的所有time集合,defult "d"
     * @param startDate
     * @param endDate
     * @param type
     * @return
     */
    public static List<String> splitDateList(String startDate, String endDate ,String type) throws ParseException
    {

        SimpleDateFormat dateFormat = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);

        List<String> listDate = new ArrayList<>();

        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            return listDate;
        }

        Integer ctype = Calendar.DAY_OF_MONTH;

        if (type.equals("min")) {
            ctype = Calendar.MINUTE;
            dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        }

        try {

            Date parse1 = dateFormat.parse(startDate);

            Date parse2 = dateFormat.parse(endDate);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse1);
            while (calendar.getTime().before(parse2) || calendar.getTime().equals(parse2)) {
                listDate.add(dateFormat.format(calendar.getTime()));
                calendar.add(ctype, 1);
            }
            return listDate;
        } catch (Exception e) {
            log.error("splitDateList is error!", e);
        }

        return listDate;
    }

    public static String dateFormat (String date,String pattern) throws ParseException
    {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);

        String format = dateFormat.format(dateFormat.parse(date));

        return format;
    }

    /**
     * 当前日期加days天数
     */
    public static String addDays(String date, Integer days) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date parse = format.parse(date);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(parse);
        calendar.add(Calendar.DATE, days);
        return format.format(calendar.getTime());
    }

    /**
     * 当前日期加days天数 -- yyyy-mm-dd
     */
    public static String addYearMonDayDateDays(String date, Integer days) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        Date parse = format.parse(date);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(parse);
        calendar.add(Calendar.DATE, days);
        return format.format(calendar.getTime());
    }

    /**
     * 获取前一秒时间
     * @param date
     * @return
     */
    public static String getOneSecondBefore(String date) throws ParseException
    {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date oldDate = format.parse(date);
        Calendar c = Calendar.getInstance();
        c.setTime(oldDate);
        c.add(Calendar.SECOND, -1);
        Date newDate = c.getTime();
        String res = format.format(newDate);
        return res;
    }

    /**
     * 获取前一秒时间
     * @param date
     * @return
     */
    public static String getOneSecondAfter(String date)
    {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date oldDate = null;
        try {
            oldDate = format.parse(date);
            Calendar c = Calendar.getInstance();
            c.setTime(oldDate);
            c.add(Calendar.SECOND, 1);
            Date newDate = c.getTime();
            String res = format.format(newDate);
            return res;
        } catch (Exception e) {
            log.error("getOneSecondAfter is error!", e);
        }

        return date;
    }

    /**
     * 获取前几分钟时间
     * @param dateTime
     * @param minute
     * @return
     * @throws ParseException
     */
    public static String getSomeMinuteBefore(String dateTime, Integer minute) throws ParseException{
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date oldDateTime = format.parse(dateTime);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(oldDateTime);
        calendar.add(Calendar.MINUTE, minute);
        return format.format(calendar.getTime());
    }

    /**
     * 获取前几天时间
     * @param dateTime
     * @param day
     * @return
     * @throws ParseException
     */
    public static Date getDayBefore(Date dateTime, Integer day)
    {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(dateTime);
        calendar.add(Calendar.DAY_OF_YEAR, -day);
        return calendar.getTime();
    }

    public static Long getDate_d(String strTime)
    {
        try
        {
            SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
            return format.parse(strTime).getTime();
        }
        catch (Exception e)
        {
            log.warn("{} transfer failed", strTime, e);
        }
        return null;
    }

    /**
     * 判断日期是否周末
     * @param date
     * @return
     */
    public static boolean isWeekend(String date) throws ParseException
    {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        Date parse = format.parse(date);
        Calendar cal = Calendar.getInstance();
        cal.setTime(parse);
        if (cal.get(Calendar.DAY_OF_WEEK)==Calendar.SATURDAY||cal.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 判断字符串是否是 yyyy-MM-dd 格式
     * @param date
     * @return
     */
    public static boolean isValidDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        sdf.setLenient(false);

        try {
            sdf.parse(date);
        } catch (ParseException e) {
            return false;
        }

        return true;
    }

    /**
     * 把时分字符串转化为"hh:mm:ss"格式的字符串
     * 如：输入12:34 输出: 12:34:00
     *    输入3:45 输出: 03:45:00
     * @param time
     * @return
     */
    public static String convertToHHMMSS(String time) {
        String[] parts = time.split(":");
        String hours = parts[0];
        String minutes = parts[1];

        // 根据给定的输入格式，判断是否需要补充秒数
        if (hours.length() == 1) {
            hours = "0" + hours;
        }
        if (minutes.length() == 1) {
            minutes = "0" + minutes;
        }

        return hours + ":" + minutes + ":00";
    }


    public static boolean checkTimeInterval(Date begin, Date end, int interval, String type)
    {
        if(begin == null || end == null)
        {
            return false;
        }
        Long beginTime = begin.getTime();
        Long endTime = end.getTime();
        Long diff =  endTime - beginTime;
        Long intervalTime = -1L;
        switch(type)
        {
            case "min":
                intervalTime = (long) interval * 60 * 1000;
                break;
            case "h":
                intervalTime = (long) interval * 60 * 60 * 1000;
                break;
            case "d":
                intervalTime = (long) interval * 24 * 60 * 60 * 1000;
                break;
            default:
                break;
        }
        if(Objects.equals(intervalTime, -1L))
        {
            return false;
        }
        return diff >= intervalTime;
    }

    /**
     * 入参 yyyy-MM
     * 返回这个月的第一毫秒到最后一毫秒
     * @param monthString
     * @return
     */
    public static Tuple2<Long, Long> getMonthTimestamps(String monthString) throws UedmException {
        YearMonth yearMonth = YearMonth.parse(monthString);
        if (yearMonth==null){
            throw new UedmException(-200,"parse monthString error, monthString:{}",monthString);
        }
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        long startTimestamp = startDateTime.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();
        return new Tuple2<>(startTimestamp, endTimestamp);
    }

    public static Long getLongTimeLast_d(String strTime) throws UedmException {
        LocalDate date = LocalDate.parse(strTime);
        if (date==null){
            throw new UedmException(-200,"parse string to longtime error, Time string is:{}",strTime);
        }
        LocalDateTime endDateTime = date.atTime(23, 59, 59);
        return  endDateTime.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();
    }

    public static Pair<String, String> getFirstTimeLastDay(String beginTime, String endTime)
            throws ParseException, UedmException
    {
        String beginMonth = dateFormat(beginTime, DATE_FORMAT_3);
        String endMonth = dateFormat(endTime, DATE_FORMAT_3);
        if(!beginMonth.equals(endMonth))
        {
            YearMonth yearMonth = YearMonth.parse(beginMonth);
            YearMonth yearMonth1 = YearMonth.parse(endMonth);
            if (yearMonth==null || yearMonth1 == null){
                throw new UedmException(-200,"parse monthString error, monthString:{}!", beginMonth+endMonth);
            }
            LocalDate endDate = yearMonth.atEndOfMonth();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
            LocalDate beginDate = yearMonth1.atDay(1);
            LocalDateTime beginDateTime = beginDate.atTime(00, 00, 00);
            return Pair.of(endDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT_1)), beginDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT_1)));
        }
        return Pair.of("", "");
    }

    public static String getDateByGr(String time, String gr) throws ParseException
    {
        if(BattConst.GR_DAY.equals(gr) && StringUtils.isNotBlank(time))
        {
            return dateFormat(time, DATE_FORMAT_2);
        }
        return time;
    }

    /**
     * 指定格式转换
     * @param dateTime
     * @return
     * @throws ParseException
     */
    public static Date getDateByFormat(Date dateTime, String dateFormat) throws ParseException
    {
        String time = getStrDateByDateType(dateTime, dateFormat);
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        return format.parse(time);
    }

    public static String getStrDateByDateType(Date date, String dateType)
    {
        if(null == date || StringUtils.isBlank(dateType))
        {
            return "";
        }
        SimpleDateFormat format = new SimpleDateFormat(dateType);
        return format.format(date);
    }

    public static Pair<String, String> getDayStartAndEndTime(){
        LocalDate date = LocalDate.now().minusDays(1);
        LocalDateTime beginDateTime = date.atTime(00, 00, 00);
        LocalDateTime endDateTime = date.atTime(23, 59, 59);
        return Pair.of(beginDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT_1)), endDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT_1)));
    }

    public static String getStrDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT_1);
        return format.format(date);
    }

    /**
     * 获取当前时间的前number小时时间
     * @param d
     * @param number
     * @return
     */
    public static Date getBeforeNumberHourByNowTime(Date d, Integer number)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.HOUR, cal.get(Calendar.HOUR) - number);
        return cal.getTime();
    }

    /**
     * 获取当前时间的后number小时时间
     * @param d
     * @param number
     * @return
     */
    public static Date getAfterNumberMinuteByNowTime(Date d, Integer number)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) + number);
        return cal.getTime();
    }

    /**
     * 比较两个日期大小
     * @param date1
     * @param date2
     * @return int
     */
    public static int compareTo(Date date1, Date date2) {
        long date1Time = date1.getTime();
        long date2Time = date2.getTime();
        return (Long.compare(date2Time, date1Time));
    }
    public static String beforeSecondTime(int second){
        LocalDateTime pastTime = LocalDateTime.now().minus(Duration.ofSeconds(second));
        return pastTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT_1));
    }

    /**
     * 判断是否为整点数据
     * @param timeStr
     * @return
     */

    /* Started by AICoder, pid:i8c24afdc6td7ed14e090a2630bdc61d6113a3d2 */
    public static boolean isWholeHour(String timeStr) {
        // 定义时间字符串的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_1);
        // 将时间字符串转换为LocalDateTime对象
        LocalDateTime timeObj = LocalDateTime.parse(timeStr, formatter);
        if (timeObj == null) {
           return false;
        }
        // 获取分钟数
        int currentMinute = timeObj.getMinute();
        // 判断是否为整点
        return currentMinute == 0;
    }

    /**
     * @param time
     * @return
     * @throws UedmException
     * @throws ParseException
     */

    /* Started by AICoder, pid:d78ce2362f0a4c2146e4083d5017ee1dbfb7537d */
    public static Pair<String, String> getTimeRange(int time) throws UedmException, ParseException {
        Date currDate = getCurrentDateTime();
        if (currDate == null) {
            throw new UedmException(-1, "Time transfer error!");
        }

        String endDate = getStrDateByDateType(currDate, DATE_FORMAT_10);
        Date beginDate = getDayBefore(currDate, time);

        if (beginDate == null) {
            throw new UedmException(-1, "Time transfer error!");
        }

        String startDate = getStrDateByDateType(beginDate, DATE_FORMAT_10);

        return Pair.of(startDate, endDate);
    }
    /* Ended by AICoder, pid:d78ce2362f0a4c2146e4083d5017ee1dbfb7537d */

    /**
     * 获取当前时间
     * @return
     */
    public static Date getCurrentDateTime() {
        LocalDateTime localDateTime = LocalDateTime.now();
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
