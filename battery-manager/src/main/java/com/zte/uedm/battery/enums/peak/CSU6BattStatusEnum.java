/* Started by AICoder, pid:q525dc268a9fc001410e092940cf613feb015bab */
package com.zte.uedm.battery.enums.peak;

public enum CSU6BattStatusEnum {
    /**
     * 获取电池状态
     */
    BATTFLOAT("0", "{\"zh_CN\":\"浮充\",\"en_US\":\"float\"}"),
    BATTEQUAL("1", "{\"zh_CN\":\"均充\",\"en_US\":\"equal\"}"),
    BATTTEST("2", "{\"zh_CN\":\"放电测试\",\"en_US\":\"test\"}"),
    // 检测需要放电
    BATTDETECT("4", "{\"zh_CN\":\"检测\",\"en_US\":\"detect\"}"),
    BATTCHARGE("6", "{\"zh_CN\":\"充电\",\"en_US\":\"charge\"}"),
    // 错峰需要放电
    BATTPEAK("7", "{\"zh_CN\":\"错峰\",\"en_US\":\"peak shift\"}");

    private String id;
    private String name;

    CSU6BattStatusEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getId() {
        return this.id;
    }
}
/* Ended by AICoder, pid:q525dc268a9fc001410e092940cf613feb015bab */