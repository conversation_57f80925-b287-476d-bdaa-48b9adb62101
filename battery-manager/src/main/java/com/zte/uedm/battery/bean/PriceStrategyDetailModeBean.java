package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class PriceStrategyDetailModeBean {
    /**
     * 主键id
     */
    private String id;

    /**
     * 单价周期策略id
     */
    private String priceIntervalStrategyId;

    /**
     * 挡位: 1：挡位1，2：挡位2，...
     */
    private Integer tiered;

    /**
     * 累计开始
     */
    private String tieredStart;

    /**
     * 累计结束
     */
    private String tieredEnd;

    /**
     * 尖时段单价
     */
    private String peakestPrice;

    /**
     * 峰时段单价
     */
    private String peakPrice;

    /**
     * 平时段单价
     */
    private String normalPrice;

    /**
     * 谷时段单价
     */
    private String lowPrice;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 范围策略id
     */
    private String scopeStrategyId;

    /**
     * 计费方式: 0：单一，1：峰谷，2：阶梯，3：阶梯峰谷
     */
    private Integer rateType;

    /**
     * 生效时间
     */
    private String effectiveTime;

    /**
     * 失效时间
     */
    private String expirationTime;

    /**
     * 累计周期: 单一、峰谷：null  阶梯或阶梯+峰谷：0：月 1：年
     */
    private Integer mode;

    /**
     * 开始日期: MM-dd（年累计周期）  MM（月累计周期）
     */
    private String startDate;

    /**
     * 结束日期: MM-dd（年累计周期）  MM（月累计周期）
     */
    private String endDate;
}
