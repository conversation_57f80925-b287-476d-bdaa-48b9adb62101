package com.zte.uedm.battery.a_infrastructure.safe.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @FileDesc :
 * <AUTHOR> 6774001720
 * @date Date : 2024年07月24日 15:30
 * @Version : 1.0
 */
/* Started by AICoder, pid:216a1ja4fdi1df614a900b3540191b123df73312 */
@Getter
@Setter
@ToString
public class BatteryProtectRequestBean {
    /**
     * 电池监控对象id列表
     */
    private List<String> ids;
    /**
     * 布防状态：1.0表示设防，0.0表示撤防
     */
    private String protectState;
    /**
     * 操作信息
     */
    private String msg;
}
/* Ended by AICoder, pid:216a1ja4fdi1df614a900b3540191b123df73312 */
