package com.zte.uedm.battery.controller.backuppowerconfig.enums;

import com.zte.uedm.battery.enums.overview.BattHealthListDimEnums;
import com.zte.uedm.common.consts.asset.AssetModelAttributeIdConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

public enum BackupPowerStatisticsDimEnums {
    Power_Supply_Equipment("powerSupplyEquipment","{\"en_US\":\"Power Supply Equipment\",\"zh_CN\":\"电源设备\"}",1,true,false, null,true),
    BATTERY_PACK("batteryPack", "{\"en_US\":\"Battery Pack(Independent Monitoring)\",\"zh_CN\":\"电池组(独立监控)\"}",2,true,false, null,true);
    private String id;
    private String name;
    private Integer defaultIndex;
    private Boolean defaultEnable;
    private Boolean defaultFixed;
    private String unit;
    private Boolean sortable;

    //表头增加-无法评估原因
    public static String UN_KNOWN_REASON_KEY = "unknownReason";
    //无法评估原因
    public static String UN_KNOWN_REASON_KEY_I18 = "{\"en_US\":\"Reason\",\"zh_CN\":\"原因\"}";



    BackupPowerStatisticsDimEnums(String id, String name, Integer defaultIndex, Boolean defaultEnable, Boolean defaultFixed, String unit, Boolean sortable) {
        this.id = id;
        this.name = name;
        this.defaultIndex = defaultIndex;
        this.defaultEnable = defaultEnable;
        this.defaultFixed = defaultFixed;
        this.unit = unit;
        this.sortable = sortable;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public Integer getDefaultIndex() {
        return defaultIndex;
    }

    public Boolean getDefaultEnable() {
        return defaultEnable;
    }

    public Boolean getDefaultFixed() {
        return defaultFixed;
    }

    public String getUnit() {
        return unit;
    }

    public Boolean getSortable() {
        return sortable;
    }

    /**
     * 获取所有维度id
     * @return
     */
    public static List<String> getAllDimIds()
    {
        List<String> allIds = new ArrayList<>();

        BackupPowerStatisticsDimEnums[] values = BackupPowerStatisticsDimEnums.values();
        for(BackupPowerStatisticsDimEnums value : values)
        {
            if(StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }

    /**
     * 根据维度id获取名字
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        if(StringUtils.isBlank(id))
        {
            return "";
        }

        BackupPowerStatisticsDimEnums[] values = BackupPowerStatisticsDimEnums.values();
        for(BackupPowerStatisticsDimEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.getName();
            }
        }
        return "";
    }

    /**
     * 根据id获取单位
     * @param id
     * @return
     */
    public static String getUnitById(String id)
    {
        if(StringUtils.isBlank(id))
        {
            return null;
        }

        BackupPowerStatisticsDimEnums[] values = BackupPowerStatisticsDimEnums.values();
        for(BackupPowerStatisticsDimEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.getUnit();
            }
        }
        return null;
    }

    /**
     * 获取电池寿命导出初始化表头
     * @return
     */
    public static Map<String,String> initHeader()
    {
        LinkedHashMap<String, String> header = new LinkedHashMap<>();
        BackupPowerStatisticsDimEnums[] values = BackupPowerStatisticsDimEnums.values();
        for(BackupPowerStatisticsDimEnums value : values)
        {
            header.put(value.getId(),value.getName());
            //定制表头
            if (value.getId().equals(BattHealthListDimEnums.STATUS.getId()))
            {
                header.put(UN_KNOWN_REASON_KEY,UN_KNOWN_REASON_KEY_I18);
            }
        }
        return header;
    }

    /**
     * 获取全部的枚举id以及对应的是否排序
     * @return
     */
    public static Map<String, Boolean> getIdSortableMap()
    {
        BackupPowerStatisticsDimEnums[] values = BackupPowerStatisticsDimEnums.values();

        return Arrays.stream(values).collect(Collectors.toMap(BackupPowerStatisticsDimEnums::getId,BackupPowerStatisticsDimEnums::getSortable));
    }
}
