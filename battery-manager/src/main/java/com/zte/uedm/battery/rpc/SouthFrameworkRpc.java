package com.zte.uedm.battery.rpc;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;


/**
 * <AUTHOR>
 */
@ServiceHttpEndPoint(serviceName = "south-framework-ms", serviceVersion = "v1")
public interface SouthFrameworkRpc
{
    /**
     * 查询所有链路id和通讯状态
     * @return
     */
    @GET("link-monitor")
    Call<ResponseBean> queryAllLinkMonitor();
    @GET("link-monitor")
    Call<ResponseBean> getLinkMonitorStatus(@Query("linkId") String linkId);
    @GET("link-monitor/used-link")
    Call<ResponseBean> getUsedLink();
}
