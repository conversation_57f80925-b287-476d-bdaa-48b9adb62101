/* Started by AICoder, pid:89669bac0ambfdc14fe7099370b6ae11ee0809bd */
package com.zte.uedm.battery.a_application.vpp.executor;

import com.zte.uedm.battery.a_infrastructure.kafka.bean.Item;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/04/23
 * @description
 */
public interface BatteryHistoryForKafkaService {
    /**
     * 监听mp性能数据 kafka
     * @param timestamp 时间戳
     * @param items 数据项列表
     */
    public void handleRealtimeData(Long timestamp, List<Item> items);
}
/* Ended by AICoder, pid:89669bac0ambfdc14fe7099370b6ae11ee0809bd */