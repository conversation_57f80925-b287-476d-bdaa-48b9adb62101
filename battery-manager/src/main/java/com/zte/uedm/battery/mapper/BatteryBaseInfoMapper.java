package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatteryBaseInfoMapper {
    /**
     * 根据id插入
     * @param batteryBaseInfoBeans
     * @return
     */
    Integer insertList(@Param("list") List<BatteryBaseInfoBean> batteryBaseInfoBeans);

    List<BatteryBaseInfoBean> selectByLogicId(@Param("logicId") String logicId,@Param("limit") Integer limit,@Param("offset") Integer offset);
    List<BatteryBaseInfoBean> selectByIdList(List<String> idList);

    int getAllNum();

    List<String> selectAllId();

    /**
     * 更新健康、寿命、风险信息
     * @param batteryBaseInfoBeanList
     * @return
     * @throws UedmException
     */
    Integer updateBatteryHealthLifeRiskInfo(List<BatteryBaseInfoBean> batteryBaseInfoBeanList) throws UedmException;

    Integer insertBatteryInfoAndAssetInfo(@Param("list") List<BatteryBaseInfoBean> list);

    Integer insertBatteryInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    Integer updateBatteryInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    Integer deleteBatteryInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    Integer deleteBatteryByIds(@Param("list") List<String> ids);

    Integer insertAssetInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    Integer updateAssetInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    Integer deleteAssetInfo(@Param("list") List<BatteryBaseInfoBean> baseInfo);

    void clearBatteryInfo();
}
