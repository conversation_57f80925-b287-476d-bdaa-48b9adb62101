package com.zte.uedm.battery.enums.peak;

public enum TaskFailReasonEnum
{
    NETWORK("{\"en_US\":\"All\",\"zh_CN\":\"全网\"}"),

    TIME_OUT("{\"en_US\":\"Timeout\",\"zh_CN\":\"超时\"}"),

    OTHER("{\"en_US\":\"Battery sending strategy failed\",\"zh_CN\":\"电池下发策略失败\"}");
    private String name;

    TaskFailReasonEnum(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return this.name;
    }
}
