package com.zte.uedm.battery.opti.infrastructure.pma.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * pma历史数据
 */
@Setter
@Getter
@ToString
public class PmaDataBean
{
    /**
     * curValue 时间范围内当前值
     */
    private String currValue;

    /**
     * curValueTime 时间范围内当前值的时间
     */
    private String curValueTime;

    /**
     * smpId 标准测点id
     */
    private String smpId;

    /**
     * resId 监控对象id
     */
    private String resId;
}

