package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 季节策略bean
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SeasonStrategyBean
{
    /**
     * 主键id
     */
    private String id;

    /**
     * 范围策略id
     */
    private String scopeStrategyId;

    /**
     * 状态 0：待生效 1：生效中 2：已结束
     */
    private Integer status;

    /**
     * 生效时间：启用时间 yyyy-mm-dd
     */
    private String effectiveTime;

    /**
     * 失效时间：停用时间 yyyy-mm-dd
     */
    private String expirationTime;

    /**
     * 生成时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
