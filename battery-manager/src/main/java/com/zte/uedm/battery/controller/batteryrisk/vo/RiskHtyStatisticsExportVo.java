package com.zte.uedm.battery.controller.batteryrisk.vo;

import com.zte.uedm.battery.bean.ImageBean;
import com.zte.uedm.battery.bean.ImageInfo;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.enums.SortEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
@ToString
@Api("风险历史导出")
@Slf4j
public class RiskHtyStatisticsExportVo {
    @ApiModelProperty(value = "逻辑位置标识")
    private String logicGroupId;
    @ApiModelProperty(value = "电池名称")
    private String battName;
    @ApiModelProperty(value = "风险名称")
    private List<String> riskName;
    @ApiModelProperty(value = "评估时间")
    private String evaluateTime;
    @ApiModelProperty(value = "排序")
    private String order;
    @ApiModelProperty(value = "顺序")
    private String sort;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "图片信息列表")
    private List<ImageBean> images;
    @ApiModelProperty(value = "位置")
    private String position;

    public Boolean isPage(){
        if (pageNo != null){
            return true;
        }
        return false;
    }

    public ResponseBean checkParamNormal(RiskHtyStatisticsExportVo vo, ResponseBean responseBean, String languageOption) {
        boolean check = false;
        List<String> errorList = new ArrayList<>();
        if (StringUtils.isBlank(vo.getLogicGroupId())){
            errorList.add("logicGroupId");
            check = true;
        }
        if (StringUtils.isNotBlank(vo.getBattName()) && vo.getBattName().length()>=50){
            responseBean.setCode(-303);
            responseBean.setMessage("battName length not exceeding 50");
            return responseBean;
        }
        if (CollectionUtils.isEmpty(vo.getImages())){
            errorList.add("images");
            check =true;
        }
        if (check){
            responseBean.setCode(-301);
            responseBean.setError(errorList.toString());
            responseBean.setMessage("param is blank");
            return responseBean;
        }
        //sort 校验，为空升序
        String sort = vo.getSort();
        if (StringUtils.isBlank(sort)){
            vo.setSort(SortEnum.getAscSortID());
        }
        //检验合法性
        List<String> sortIdList = SortEnum.getSortIdList();
        if (!sortIdList.contains(vo.getSort())){
            Map<String,List<String>> errMap = new HashMap<>();
            errMap.put("sort",sortIdList);
            responseBean.setCode(-304);
            responseBean.setError(errMap.toString());
            responseBean.setMessage("param is not allowed.");
            return responseBean;
        }
        //order 校验，为空设置按照等级排序
        if (StringUtils.isBlank(vo.getOrder())){
            vo.setOrder(OrderSortEnum.RISK_LEVEL.id);
        }
        //校验合法性
        if (!OrderSortEnum.getIds().contains(vo.getOrder())){
            Map<String,List<String>> errMap = new HashMap<>();
            errMap.put("order",OrderSortEnum.getIds());
            responseBean.setCode(-304);
            responseBean.setError(errMap.toString());
            responseBean.setMessage("param is not allowed.");
            return responseBean;
        }
        vo.setOrder(OrderSortEnum.getColById(vo.getOrder()));

        responseBean.setMessage("export message");
        return responseBean;
    }

   enum OrderSortEnum{
       NAME("name","名称","name"),
       POSITION("position","位置","position"),
       RISK_LEVEL("riskLevel","等级","risk_level");
       OrderSortEnum(String id, String name, String col) {
           this.id = id;
           this.name = name;
           this.col = col;
       }

       private String id;
       private String name;
       private String col;

       public String getId() {
           return id;
       }
       
       public static List<String> getIds(){
           List<String> ids = new ArrayList<>();
           Arrays.asList(OrderSortEnum.values()).forEach(item -> ids.add(item.id));
           return ids;
       }
       public static String getColById(String key){
           for (OrderSortEnum value : OrderSortEnum.values()) {
               if (value.id.equals(key)){
                   return value.col;
               }
           }
           return OrderSortEnum.RISK_LEVEL.col;
       }
    }
}
