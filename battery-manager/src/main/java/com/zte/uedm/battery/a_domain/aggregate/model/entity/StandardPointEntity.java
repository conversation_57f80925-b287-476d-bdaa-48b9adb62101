package com.zte.uedm.battery.a_domain.aggregate.model.entity;

import com.zte.uedm.basis.bean.IdNameBean;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.battery.a_domain.aggregate.model.obj.PointTypeOptional;
import com.zte.uedm.battery.a_domain.aggregate.model.obj.ValueOptionsOptional;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@ToString
@Slf4j
public class StandardPointEntity extends BaseCacheBean {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "模型标识")
    private String moc;

    @ApiModelProperty(value = "测点类型")
    private String pointType;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "精度")
    private Integer precision;

    @ApiModelProperty(value = "是否可扩展")
    private Boolean expandable;

    @ApiModelProperty(value = "扩展次数")
    private Integer expandTimes;

    @ApiModelProperty(value = "值定义国际化")
    private List<ValueOptionsOptional> valueOptions;

    @ApiModelProperty(value = "汇聚模式")
    private List<String> convergeMode;

    @ApiModelProperty(value = "归属标准测点id")
    private String belongTo;

    @ApiModelProperty(value = "测点参数")
    private List<PointFunctionParameterEntity> parameters = new ArrayList<>();


    /**
     * 汇聚周期
     * {"minute":{"flag":false,"value":0},"day":{"flag":true,"value":1},"week":{"flag":true,"value":1},"month":{"flag":true,"value":1}}
     */
    @ApiModelProperty(value = "汇聚粒度")
    private String convergeGranularity;

    @Override
    public String getPrimaryKey() {
        return this.getMoc();
    }

    public IdNameBean<String> covertPointType(String pointType) {
        IdNameBean<String> pointTypeIdNameBean = new IdNameBean<>();
        PointTypeOptional pointTypeOptional = PointTypeOptional.getById(pointType);
        String pointTypeName = I18nUtils.getLanguageField(pointTypeOptional != null ? pointTypeOptional.getName() : null);
        pointTypeIdNameBean.setId(pointType);
        pointTypeIdNameBean.setName(pointTypeName);
        return pointTypeIdNameBean;
    }


}
