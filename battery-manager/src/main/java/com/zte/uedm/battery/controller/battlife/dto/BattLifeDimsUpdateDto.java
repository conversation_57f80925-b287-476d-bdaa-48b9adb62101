/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattLifeDimsUpdateDto
 * 文件作者：00248587
 * 开发时间：2023/3/3
 */
package com.zte.uedm.battery.controller.battlife.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@ApiModel(description = "电池寿命维度")
public class BattLifeDimsUpdateDto
{
    @ApiModelProperty(value = "标识")
    private String id;
    @ApiModelProperty(value = "维度")
    private Integer sequence ;
    @ApiModelProperty(value = "是否开启")
    private Boolean enable ;
}
