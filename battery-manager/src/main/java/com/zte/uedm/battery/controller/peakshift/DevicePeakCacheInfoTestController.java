/* Started by AICoder, pid:jfb79nd7afdefa0144df0910804614834268f604 */
package com.zte.uedm.battery.controller.peakshift;

import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.service.DevicePeakCacheInfoService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

@Path("device-peak-cache-test")
@Component
@Api(value = "device-peak-cache-test")
@Slf4j
public class DevicePeakCacheInfoTestController {

    @Autowired
    private DevicePeakCacheInfoService devicePeakCacheInfoService;

    @POST
    @Path("/init")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "错峰设备缓存初始化", notes = "错峰设备缓存初始化", httpMethod = "POST")
    public ResponseBean init() {
        devicePeakCacheInfoService.initCache();
        return ResponseBeanUtils.getNormalResponseBean(0, null, 1);
    }

    @POST
    @Path("/query-list")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "错峰设备缓存列表查询", notes = "错峰设备缓存列表查询", httpMethod = "POST")
    public ResponseBean queryAllList() {
        List<DevicePeakCacheInfoBean> list;
        long total;
        try {
            list = devicePeakCacheInfoService.getAllList();
            total = list.size();
        } catch (UedmException e) {
            log.error("queryAllList failed", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0, list, (int) total);
    }

    @POST
    @Path("/query-map")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "错峰设备缓存map查询", notes = "错峰设备缓存map查询", httpMethod = "POST")
    public ResponseBean queryAllMap() {
        Map<String, DevicePeakCacheInfoBean> map;
        long total;
        try {
            map = devicePeakCacheInfoService.getAllMap();
            total = map.size();
        } catch (UedmException e) {
            log.error("queryAllMap failed", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0, map, (int) total);
    }

    @POST
    @Path("/query-by-id")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "错峰设备缓存map查询单个设备信息", notes = "错峰设备缓存map查询单个设备信息", httpMethod = "POST")
    public ResponseBean queryById(@QueryParam("id") String id) {
        DevicePeakCacheInfoBean bean;
        try {
            bean = devicePeakCacheInfoService.getInfoByDeviceId(id);
        } catch (UedmException e) {
            log.error("queryById failed", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0, bean, 0);
    }
}
/* Ended by AICoder, pid:jfb79nd7afdefa0144df0910804614834268f604 */