package com.zte.uedm.battery.domain;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.pojo.BattTestTaskTemDevicesPo;
import com.zte.uedm.battery.controller.batterytesttask.dto.DeleteTemDeviceDto;
import com.zte.uedm.battery.controller.batterytesttask.dto.DeleteTemDeviceSaveDto;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;


public interface BattTestTaskTemDevicesDomain
{
    /**
     * 根据任务id查询其关联的监控对象
     * @param taskId
     * @return
     * @throws UedmException
     */
    PageInfo<BattTestTaskTemDevicesPo> selectByTaskId(String taskId, ServiceBaseInfoBean baseInfoBean) throws UedmException;

    /**
     * 任务的关联临时设备列表 - 删除
     * @param deleteTemDeviceDto
     * @return
     * @throws UedmException
     */
    int deleteTemDevice(DeleteTemDeviceDto deleteTemDeviceDto,ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 任务的关联临时设备列表 - 保存
     * @param insertList
     * @return
     * @throws UedmException
     */
    int saveTemDevice(List<BattTestTaskTemDevicesPo> insertList) throws UedmException;
}
