/* Started by AICoder, pid:7cd93q5635x89c41428009e29063be2539b076a7 */
package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 配置中心删除项的JavaBean。
 */
@Getter
@Setter
@ToString
public class ConfigCenterDeleteItemBean {
    /**
     * 配置中心项列表。
     */
    private List<ConfigCenterItem> items;
}
/* Ended by AICoder, pid:7cd93q5635x89c41428009e29063be2539b076a7 */
