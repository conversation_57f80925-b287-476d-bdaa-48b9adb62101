package com.zte.uedm.battery.controller.batterytesttask.dto;

import com.zte.uedm.battery.enums.BattTestTaskStatusEnums;
import com.zte.uedm.battery.enums.batttest.AutoTestDimsEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Getter
@Setter
@ToString
@Slf4j
@ApiModel(description = "电池任务测试")
public class BattTestTaskStatisticDto
{
    @ApiModelProperty("任务状态")
    private List<String> status;
    @ApiModelProperty("任务名称")
    private String name;
    @ApiModelProperty("设备id列表")
    private List<String> deviceIds;
    @ApiModelProperty("自动测试统计维度列表")
    private List<String> autoStatisticsDims;
    @ApiModelProperty("页码")
    private Integer pageNo;
    @ApiModelProperty("每页数量")
    private Integer pageSize;

    public Boolean checkBlank()
    {
        return CollectionUtils.isEmpty(autoStatisticsDims);
    }

    public Pair<Boolean, Map<String, List<String>>> checkParam()
    {
        Boolean flag = true;
        Map<String, List<String>> errorMsg = new HashMap<>();

        Set<String> allIdSet = BattTestTaskStatusEnums.getAllIds();
        if (!CollectionUtils.isEmpty(this.status)) {
            List<String> subtract = ListUtils.subtract(status, new ArrayList<>(allIdSet));
            if(subtract.size() > 0)
            {
                log.error("status is Not in the range of optional values");
                flag = false;
                errorMsg.put("status", new ArrayList<>(allIdSet));
            }
        }

        if (StringUtils.isNotBlank(name) && name.length()>50)
        {
            log.error("status is Not in the range of optional values");
            flag = false;
            errorMsg.put("name", Arrays.asList("name length should not over 50!"));
        }

        Set<String> allAutoTestDims = AutoTestDimsEnums.getAllIds();
        if (!CollectionUtils.isEmpty(this.autoStatisticsDims)) {
            List<String> subtract = ListUtils.subtract(autoStatisticsDims, new ArrayList<>(allAutoTestDims));
            if(subtract.size() > 0)
            {
                log.error("autoStatisticsDims is Not in the range of optional values");
                flag = false;
                errorMsg.put("autoStatisticsDims", new ArrayList<>(allAutoTestDims));
            }
        }

        return Pair.of(flag, errorMsg);
    }

}
