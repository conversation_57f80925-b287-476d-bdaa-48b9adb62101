/* Started by AICoder, pid:2df57u9a7764b781435c0b462014716c1319bf51 */
package com.zte.uedm.battery.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.rpc.MpServiceRpc;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.mp.bean.AllRecordsRequestMsg;
import com.zte.uedm.common.configuration.mp.bean.RecordData;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MpServiceRpcImpl2 {

    @Autowired
    private MpServiceRpc mpServiceRpc;

    @Autowired
    private JsonService jsonService;


    // 返回对应记录数据
    public List<RecordData> eventConvergeRecordLists(List<AllRecordsRequestMsg> allRecordsRequestMsgs) throws UedmException, IOException {
        Response<ResponseBean> response = mpServiceRpc.eventConvergeRecordLists(allRecordsRequestMsgs).execute();
        if (response.isSuccessful()) {
            if (response.body() != null && response.body().getCode() == 0) {
                log.debug("OringinDataMeasurePointRpcImpl eventConvergeRecordLists Success: {}", response.body().getData());
                List<RecordData> data = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), List.class, RecordData.class);
                return data;
            }
        }
        log.error("eventConvergeRecordLists return fail: {}", JSON.toJSONString(response));
        return Collections.emptyList();
    }

    public List<RecordData> eventConvergeRecordListsByEndTime(List<AllRecordsRequestMsg> allRecordsRequestMsgs) throws UedmException, IOException {
        Response<ResponseBean> response = mpServiceRpc.eventConvergeRecordListsByEndTime(allRecordsRequestMsgs).execute();
        if (response.isSuccessful()) {
            if (response.body() != null && response.body().getCode() == 0) {
                log.debug("OringinDataMeasurePointRpcImpl eventConvergeRecordListsByEndTime Success: {}", response.body().getData());
                List<RecordData> data = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), List.class, RecordData.class);
                return data;
            }
        }
        return Collections.emptyList();
    }
}
/* Ended by AICoder, pid:2df57u9a7764b781435c0b462014716c1319bf51 */