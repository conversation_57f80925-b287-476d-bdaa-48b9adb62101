package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class PeakShiftCsuAllStrategyBean
{
    /**
     * 主键
     */
    private String id;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 策略开始时间
     */
    private String startTime;

    /**
     * 策略结束时间
     */
    private String endTime;

    /**
     * 周末错峰使能
     */
    private Boolean weekendFlag;

    /**
     * 策略详情
     */
    private List<PeakShiftCsu5StrategyDetailBean> peakShiftCsu5StrategyDetailBos;
}
