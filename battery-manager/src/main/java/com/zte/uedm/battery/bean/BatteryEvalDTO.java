package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.enums.battlife.BatteryHealthEvalEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
public class BatteryEvalDTO {

    /**
     * 测点名称
     */
    private Map<String,String> nameMap;
    /**
     * 评估枚举
     */
    private BatteryHealthEvalEnums enums;


    /**
     * 评估日期
     */
    private String date;

    /**
     * 当前值
     */
    private double value;

    /**
     * 最大值
     */
    private double max;

    /**
     * 最小值
     */
    private double min;


    /**
     * 日期dto
     */
    private List<DateDTO> dateDTOS;

    /**
     * 同组电池异常名称
     */
    private String moName;

    /**
     * 是否告警
     */
    private String alarm;

    @Getter
    @Setter
    public static class DateDTO {

        /**
         * 当前值
         */
        private double value;

        /**
         * 日期
         */
        private String date;

    }

    public BatteryEvalDTO(){}

    /**
     * set 标准测点名称国际化map值 当map不为null时，不会更新map
     * @param map 更新值
     */
    public void setNameMap (Map<String,String> map){
        if (MapUtils.isNotEmpty(this.nameMap) && nameMap.size() > 0){
            return;
        }
        if (MapUtils.isNotEmpty(map)){
            this.nameMap = map;
        }
    }

    /**
     * set 枚举值 当枚举不为null时，不会更新枚举值
     * @param enums 更新值
     */
    public void setEnum (BatteryHealthEvalEnums enums){
        if (null != this.enums){
            return;
        }
        if (null != enums){
            this.enums = enums;
        }
    }

    /**
     * set 评估日期 当日期不为null时，不会更新日期
     * @param date 更新值
     */
    public void setDate (String date){
        if (StringUtils.isNoneBlank(this.date)){
            return;
        }
        if (StringUtils.isNoneBlank(date)){
            this.date = date;
        }
    }
}
