/* Started by AICoder, pid:ff6bfxd319v68f2142d3094b803f273d4af31717 */
package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftDeviceFileUploadPO;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyBean;
import com.zte.uedm.battery.bean.peak.PeakShiftDeviceStrategyDetailBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 错峰设备实时配置文件
 */
@Mapper
public interface PeakShiftDeviceFileMapper {

    /**
     * 根据设备ID查询设备
     *
     * @param deviceId 设备ID
     * @return 设备策略列表
     * @throws UedmException 异常信息
     */
    List<PeakShiftDeviceStrategyBean> selectPeakShiftDeviceStrategyBeanByDeviceId(String deviceId);
    List<PeakShiftDeviceStrategyBean> selectDeviceStrategyBeanByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 根据设备ID和节假日查询设备详情
     *
     * @param id 设备ID或节假日ID
     * @return 设备策略详情
     * @throws UedmException 异常信息
     */
    PeakShiftDeviceStrategyDetailBean selectPeakShiftDeviceStrategyDetailBeanById(String id);

    Integer selectMaxVersionByDeviceId(@Param("deviceId") String deviceId);

    Integer updatePeakShiftDeviceStrategyEndDate(@Param("deviceId") String deviceId, @Param("version") Integer version, @Param("endDate") String endDate);

    Integer insertPeakShiftDeviceStrategyBean(@Param("insertBean") PeakShiftDeviceStrategyBean insertBean);

    Integer insertPeakShiftDeviceStrategyDetail(@Param("detailBean") PeakShiftDeviceStrategyDetailBean detailBean);

    Integer setDeviceFileResult(@Param("uploadBean") PeakShiftDeviceFileUploadPO uploadBean) throws UedmException;

    List<PeakShiftDeviceStrategyBean> findStrategyByDeviceId(@Param("deviceId") String deviceId);

    List<PeakShiftDeviceStrategyDetailBean> findStrategyDetailById(@Param("ids") List<String> ids);

    /**
     * 根据设备id修改状态
     * @param deviceIds
     * @return
     * @throws UedmException
     */
    int updateStatusByDeviceId(@Param("deviceIds") List<String> deviceIds, @Param("status") boolean status) throws UedmException;

    /**
     * 查询status状态的文件ID与更新时间
     * @param deviceIds
     * @param status
     * @return
     * @throws UedmException
     */
    List<PeakShiftDeviceChildBeanVo> selectFileIdByDeviceId(@Param("deviceIds") List<String> deviceIds, @Param("status") boolean status) throws UedmException;

    /**
     * 根据设备选择设备策略下发ID和时间
     * @param deviceId
     * @return
     * @throws UedmException
     */
    PeakShiftDeviceChildBeanVo selectDeviceFileIdAndTime(String deviceId);
}
/* Ended by AICoder, pid:ff6bfxd319v68f2142d3094b803f273d4af31717 */