package com.zte.uedm.battery.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.StandardPointCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.bean.RealtimeDataBean;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zte.uedm.battery.api.bean.Alarm;
import com.zte.uedm.battery.api.bean.RcdReqBody;
import com.zte.uedm.battery.api.bean.RcdResponseBean;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.api.service.DataService;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.MonitorManagerRpc;
import com.zte.uedm.battery.rpc.MpRpc;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.point.bean.DataType;
import com.zte.uedm.common.configuration.point.bean.StandardPointBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.JsonService;

import lombok.extern.slf4j.Slf4j;
import retrofit2.Call;
import retrofit2.Response;
@Component
@Slf4j
public class DataServiceImpl implements DataService {
	@Autowired
	private DataRedis dataRedis;
	@Autowired
    private BlankService blankService;
	@Autowired
	private MpRpc mpRpc;
	@Autowired
	private JsonService jsonService;
	@Autowired
	private MonitorManagerRpc monitorManagerRpc;
	@Autowired
	private ConfigurationService configurationService;

	@Autowired
	private StandardPointCacheManager standardPointCacheManager;

	@Autowired
	private StandardDataService standardDataService;

	@Override
	public String getStandPointRealDataByKey(String moId, String key) throws UedmException{
		Map<String, Map<String, String>> map = dataRedis.selectRealData(moId);
		String keyValue = "";
		if (!blankService.isBlank(map) && map.containsKey(key)) {
			Map<String, String> pointMap = map.get(key);
			keyValue = pointMap.containsKey("value") ? pointMap.get("value") : "";

		}
		return keyValue;
	}

	@Override
    public String parseStandardPointVal(String val,String id) throws UedmException{
		String convertVal=val;
		StandardPointBean spb=configurationService.getStandardPointBeanById(id);
		if(!blankService.isBlank(val)&&DataType.number==spb.getDataType()){
			Integer precision=spb.getPrecision();
			BigDecimal b = new BigDecimal(val);
			b = b.setScale(precision, BigDecimal.ROUND_HALF_UP);
			convertVal = String.valueOf(b);
		}
		return convertVal;
    }

	/* Started by AICoder, pid:g60ca864acfc6af147cc081d3088ce14dff2d7cd */
	@Override
	public String parseStandardPointVal(String val, String id, String moc) throws UedmException {
		String convertVal = val;
		StandardPointEntity standardPointEntity = null;
		try {
			standardPointEntity = standardPointCacheManager.getStandardByIdMoc(id, moc);
		} catch (com.zte.uedm.basis.exception.UedmException e) {
			log.error("parseStandardPointVal get cache error", e);
			throw new UedmException(e.getErrorId(), "get cache error!");
		}
		if (standardPointEntity != null && !blankService.isBlank(val) &&
				GlobalConstants.STANDARD_POINT_DATA_TYPE.equals(standardPointEntity.getDataType())) {
			Integer precision = standardPointEntity.getPrecision();
			BigDecimal b = BigDecimal.valueOf(Double.parseDouble(val));
			b = b.setScale(precision, BigDecimal.ROUND_HALF_UP);
			convertVal = String.valueOf(b);
		}
		return convertVal;
	}
	/* Ended by AICoder, pid:g60ca864acfc6af147cc081d3088ce14dff2d7cd */

	@Override
	public List<RcdResponseBean> getRcds(String moId, String eventType, String startTime, String endTime,
										 String[] recPoints) throws UedmException {
		RcdReqBody req=new RcdReqBody(moId,eventType,startTime,endTime,recPoints);
		List<RcdResponseBean> list = new ArrayList<RcdResponseBean>();
		log.info("Request record body :{} " ,req);

		Call<ResponseBean> response = mpRpc.getAllRcds(req);
		try {
			Response<ResponseBean> responseBean = response.execute();
			log.info("Response record result :{} " , jsonService.objectToJson(responseBean));
			if (responseBean.isSuccessful()) {
				list = jsonService.jsonToObject(jsonService.objectToJson(responseBean.body().getData()), List.class, RcdResponseBean.class);
			}
			log.info("Request rcd:{}",list);
		} catch (Exception e) {
			log.error("getRcds in mp is error!", e);
			throw new UedmException(-1, e.getMessage());
		}
		return list;
	}



	/* Started by AICoder, pid:53db8d919e778e714a4b0898c04abe214374f85e */
	@Override
	public Map<String, Map<String, String>> getRealtimeDataByIdAndType(String id, String smpType) {
		if (StringUtils.isAnyBlank(id, smpType)) {
			log.warn("getRealtimeDataById id or smpType is blank");
			return Collections.emptyMap();
		}

		// 根据类型获取标准测点id
		Map<String, List<StandardPointEntity>> standardMap;
		try {
			standardMap = standardPointCacheManager.getStandardByPointType(Collections.singleton(smpType));
		} catch (com.zte.uedm.basis.exception.UedmException e) {
			log.error("getRealtimeDataById get cache error: {}", e.getMessage(), e);
			throw new RuntimeException("Error retrieving standard point data", e);
		}

		List<StandardPointEntity> standardPointEntities = Optional.ofNullable(standardMap.get(smpType)).orElse(Collections.emptyList());
		if (standardPointEntities.isEmpty()) {
			log.warn("getRealtimeDataById not get standard by type {}", smpType);
			return Collections.emptyMap();
		}

		List<String> smpIds = standardPointEntities.stream().map(StandardPointEntity::getId).collect(Collectors.toList());
		return Optional.ofNullable(standardDataService.getByResourceIdAndStandPointId(id, smpIds)).orElse(Collections.emptyMap());
	}
	/* Ended by AICoder, pid:53db8d919e778e714a4b0898c04abe214374f85e */
}
