package com.zte.uedm.battery.rpc;

import com.zte.uedm.battery.bean.ConfigCenterDeleteItemBean;
import com.zte.uedm.battery.bean.ConfigCenterItem;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;

import java.util.List;

@ServiceHttpEndPoint(serviceName = "configcenter-configserver-inner", serviceVersion = "v232006")
public interface ConfigCenterRpc
{
    /**
     * items
     * @param items
     * @return
     */
    @POST("deleteProperties")
    Call<Object> deleteProperties(@Body ConfigCenterDeleteItemBean items);
}
