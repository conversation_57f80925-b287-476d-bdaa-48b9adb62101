package com.zte.uedm.battery.controller.batteryrisk.vo;

import io.swagger.annotations.Api;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Api("风险历史导出")
public class BattRiskEvalBean {
    private String riskId;
    private String battId;
    private String name;
    private String position;
    private String battType;
    private String riskName;
    private String riskLevel;
    private String riskCause;
    private String riskSuggestion;
    private String evaluateTime;

}
