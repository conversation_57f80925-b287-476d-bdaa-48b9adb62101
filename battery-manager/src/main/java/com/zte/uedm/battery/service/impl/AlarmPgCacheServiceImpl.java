package com.zte.uedm.battery.service.impl;

import com.zte.oes.dexcloud.cache.pg.service.MultiCacheService;
import com.zte.uedm.battery.bean.alarm.ActiveAlarmQueryEntity;
import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.battery.enums.ActiveAlarmFieldEnums;
import com.zte.uedm.battery.service.AlarmPgCacheService;
import com.zte.ums.zenap.cache.client.api.ICache;
import com.zte.ums.zenap.cache.client.api.ICacheService;
import com.zte.ums.zenap.cache.client.api.query.CacheFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/22
 **/
@Service
@Slf4j
public class AlarmPgCacheServiceImpl implements AlarmPgCacheService {

    private static final String ZENAP_PG_CACHE = "zenap_pg_cache";

    private static final String ACTIVE_ALARM_TABLE_NAME = "ZENAP_FM_ACTIVEALARM";

    @Resource
    private MultiCacheService multiCacheService;

    @Override
    public List<AlarmDTO> getByCondition(ActiveAlarmQueryEntity activeAlarmQueryEntity) {
        try {
            // 拿缓存管理
            ICacheService cacheService = multiCacheService.getCacheService(ZENAP_PG_CACHE);

            // 拿到实时告警缓存
            ICache activeAlarmCache = cacheService.getICacheByName(ACTIVE_ALARM_TABLE_NAME);

            // 查询
            List<CacheFilter> conditions = getConditions(activeAlarmQueryEntity);
            long time0 = System.currentTimeMillis();
            // 此处可以使用page进行分页查询，后续有人需要请修改此处
            List<AlarmDTO> result = activeAlarmCache.query(AlarmDTO.class, -1, -1, null, conditions.toArray(new CacheFilter[0]));
            log.info("get  result.size:{}",result.size());
            long time1 = System.currentTimeMillis();
            log.info("get alarm consume time:{}", time1 - time0);

            boolean containsNullValue = result.stream().anyMatch(Objects::isNull);
            // 返回数据，修复如果没有满足条件的告警，该工具类会返回一个元素为null的list导致的问题
            return containsNullValue ? new ArrayList<>() : result;
        }catch (Exception ex) {
            log.error("Get alarms from pg cache error", ex);
            // 查询异常返回空list，防止上游业务nullpointer
            return new ArrayList<>();
        }
    }


    private List<CacheFilter> getConditions(ActiveAlarmQueryEntity activeAlarmQueryEntity) {
        List<CacheFilter> conditions = new ArrayList<>();

        addIfNotEmptyIn(conditions, ActiveAlarmFieldEnums.ME.getField(), activeAlarmQueryEntity.getMeList());
        addIfNotEmptyIn(conditions, ActiveAlarmFieldEnums.ALARM_CODE.getField(), activeAlarmQueryEntity.getAlarmCodeList());
        addIfNotEmptyIn(conditions, ActiveAlarmFieldEnums.ALARM_VISIBLES.getField(), activeAlarmQueryEntity.getVisibles());
        addIfNotEmptyIn(conditions, ActiveAlarmFieldEnums.ALARM_ACKSTATES.getField(), activeAlarmQueryEntity.getAckstates());
        addIfNotEmptyIn(conditions, ActiveAlarmFieldEnums.ALARM_SERVERITIES.getField(), activeAlarmQueryEntity.getSeverities());
        if (activeAlarmQueryEntity.getStartTime() != null && activeAlarmQueryEntity.getEndTime() != null) {
            addIfNotNullBetween(conditions, ActiveAlarmFieldEnums.ALARM_RAISED_TIME.getField(),activeAlarmQueryEntity.getStartTime(), activeAlarmQueryEntity.getEndTime());
        }

        return conditions;
    }


    private void addIfNotNullBetween(List<CacheFilter> conditions, String field, Long startTime, Long endTime) {
        conditions.add(CacheFilter.lt(field, endTime));
        conditions.add(CacheFilter.gt(field, startTime));
    }

    private void addIfNotEmptyIn(List<CacheFilter> conditions, String field, Collection<?> values) {
        if (CollectionUtils.isNotEmpty(values)) {
            conditions.add(CacheFilter.in(field, values));
        }
    }



}
