package com.zte.uedm.battery.service;

import java.util.List;

import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmBatchSchBean;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.uedm.battery.bean.alarm.AlarmSchBean;
import com.zte.uedm.common.exception.UedmException;

/**
 * 告警服务接口
 *
 * <AUTHOR>
public interface AlarmService {

    /**
     * 根据oid获取当前告警信息,不查子类
     *
     * @param oid 逻辑分组oid
     * @return 告警List
     * @throws UedmException
     **/
    List<Alarm> getAlarmByOid(List<String> oids, List<Integer> visibles) throws UedmException;

    /**
     * 查询所有告警（包括当前告警和历史告警）
     *
     * @param alarmSchBean 查询条件
     * @return 所有告警
     * @throws UedmException
     */
    List<Alarm> getAlarmData(AlarmSchBean alarmSchBean) throws UedmException;

    /**
     * 查询前告警
     *
     * @param mocIdList mocIdList
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 所有告警
     * @throws UedmException 异常
     */
    List<Alarm> getAlarmListForBattery(List<String> mocIdList, String startTime, String endTime) throws UedmException;

    /**
     * 获取最新的一条历史告警数据
     *
     * @param alarmSchBean 查询条件
     * @return 所有告警
     * @throws UedmException
     */
    List<Alarm> getLastHistoryAlarmData(AlarmSchBean alarmSchBean) throws UedmException;

    /**
     * 批量查询所有告警（包括当前告警和历史告警）
     *
     * @param alarmBatchSchBean 查询条件
     * @return 所有告警
     * @throws UedmException
     */
    List<Alarm> getAlarmDataBatch(AlarmBatchSchBean alarmBatchSchBean) throws UedmException;

    /**
     * 查询前告警(监控详情调用，过滤条件仅为业务对象)
     *
     * @param mocIdList 业务对象Id列表
     * @param page      页  默认 1
     * @param pageSize  页大小 默认 30
     * @return 所有告警
     * @throws UedmException 异常
     */
    AlarmResponse getAlarmListForSolarMonitor(List<String> mocIdList, Integer page, Integer pageSize) throws UedmException;
}
