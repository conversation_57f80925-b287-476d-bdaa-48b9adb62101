package com.zte.uedm.battery.controller.backuppowerthreshold.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 特殊设置查询
 */
@Getter
@Setter
@ToString
public class SpecialSelectVo
{
    /**
     * 开关电源id
     */
    private String id;
    /**
     * 开关电源名称
     */
    private String name;
    /**
     * 位置
     */
    private String position;
    /**
     * 站点等级
     */
    private String siteLevel;
    /**
     * v
     */
    private String powerSupplyScene;
    /**
     * 阈值
     */
    private Integer threshold;
    /**
     * 设置人
     */
    private String create;
    /**
     * 设置时间
     */
    private String gmtCreate;


}
