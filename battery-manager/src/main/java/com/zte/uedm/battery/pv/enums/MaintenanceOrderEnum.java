package com.zte.uedm.battery.pv.enums;

import lombok.Getter;

@Getter
public enum MaintenanceOrderEnum {
    EFFICIENCY("efficiency"),
    RATIO_SUPPLY("ratioSupply"),
    POWER_RATING("powerRating");

    private String order;

    MaintenanceOrderEnum(String order) {
        this.order = order;
    }

    public static boolean exist(String value) {
        MaintenanceOrderEnum[] values = MaintenanceOrderEnum.values();
        for (MaintenanceOrderEnum maintenanceOrderEnum : values) {
            if (maintenanceOrderEnum.order.equals(value)) {
                return true;
            }
        }
        return false;
    }
}
