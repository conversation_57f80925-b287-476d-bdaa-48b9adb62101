/* Started by AICoder, pid:kbbeft4afap3c65148610b1be05cf93389a341d9 */
package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;

import java.util.List;

@Getter
@Setter
@ToString
public class SiteDeviceInfoDto {

    private String siteId;

    private String siteName;

    private String position;

    private String siteStatus;

    private List<DevicePeakCacheInfoBean> devices;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
/* Ended by AICoder, pid:kbbeft4afap3c65148610b1be05cf93389a341d9 */