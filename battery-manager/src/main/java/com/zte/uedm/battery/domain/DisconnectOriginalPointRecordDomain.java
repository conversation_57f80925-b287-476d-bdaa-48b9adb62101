package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.DisconnectOriginalPointRecordBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface DisconnectOriginalPointRecordDomain
{

    /**
     * 查询所有数据
     * @return
     * @throws UedmException
     */
    List<DisconnectOriginalPointRecordBean> selectAll() throws UedmException;

    /**
     * 查询所有最新的数据
     * @return
     */
    List<DisconnectOriginalPointRecordBean> selectAllNewData() throws UedmException;

    int insertList(List<DisconnectOriginalPointRecordBean> disconnectOriginalPiontRecordBeans) throws UedmException;

    int updateList(List<DisconnectOriginalPointRecordBean> disconnectOriginalPiontRecordBeans) throws UedmException;

    int upsertList(List<DisconnectOriginalPointRecordBean> disconnectOriginalPiontRecordBeans) throws UedmException;

}
