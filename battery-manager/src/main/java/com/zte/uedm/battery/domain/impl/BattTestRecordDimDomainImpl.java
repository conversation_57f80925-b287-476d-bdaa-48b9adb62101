package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.pojo.BattRecordIndexDimPo;
import com.zte.uedm.battery.domain.BattTestRecordDimDomain;
import com.zte.uedm.battery.mapper.BattTestRecordDimensionsMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.configuration.point.bean.RecordIndexBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ Author     ：10260977
 * @ Date       ：11:01 2022/8/10
 * @ Description：记录维度领域实现类
 * @ Modified By：
 * @ Version: 1.0
 */
@Service
@Slf4j
public class BattTestRecordDimDomainImpl implements BattTestRecordDimDomain
{
    @Autowired
    private BattTestRecordDimensionsMapper battTestRecordDimensionsMapper;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Override
    public List<BattRecordIndexDimPo> selectByUserName(String scene,String userName) throws UedmException
    {
        if(StringUtils.isBlank(userName))
        {
            log.warn("BattTestRecordDimensionsDomainImpl selectByUserName userName is empty!");
            return new ArrayList<>();
        }
        try
        {
            return battTestRecordDimensionsMapper.selectByUserName(scene,userName);
        }
        catch (Exception e)
        {
            log.error("BattTestRecordDimDomainImpl selectByUserName error", e);
            throw UedmErrorCodeDatabaseUtil.databaseSelectFailed("Data query error!");
        }
    }

    @Override
    public Integer insertBattRecordIndexDims(List<BattRecordIndexDimPo> battRecordIndexDimBeans) throws UedmException
    {
        if(CollectionUtils.isEmpty(battRecordIndexDimBeans))
        {
            log.warn("BattTestRecordDimensionsDomainImpl insertBattRecordIndexDims data is empty!");
            return 0;
        }
        try
        {
            return battTestRecordDimensionsMapper.insertBeans(battRecordIndexDimBeans);
        }
        catch (Exception e)
        {
            log.error("BattTestRecordDimDomainImpl insertBattRecordIndexDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed("Data insert error!");
        }
    }

    @Override
    public Integer updateBattRecordIndexDims(List<BattRecordIndexDimPo> battRecordIndexDimBeans) throws UedmException
    {
        if(CollectionUtils.isEmpty(battRecordIndexDimBeans))
        {
            log.warn("BattTestRecordDimensionsDomainImpl updateBattRecordIndexDims data is empty!");
            return 0;
        }
        try
        {
            return battTestRecordDimensionsMapper.updateBeans(battRecordIndexDimBeans);
        }
        catch (Exception e)
        {
            log.error("BattTestRecordDimDomainImpl updateBattRecordIndexDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }

    @Override
    public List<RecordIndexBean> selectRecordIndexByCondition(RecordIndexBean recordIndexBean, Integer pageNo,
            Integer pageSize, String languageOption) throws UedmException
    {
        return configurationManagerRpcImpl.selectRecordIndexByCondition(recordIndexBean, pageNo, pageSize, languageOption);
    }

    @Override
    public List<RecordIndexBean> selectRecordIndexByConditionAllName(RecordIndexBean recordIndexBean, Integer pageNo,
            Integer pageSize) throws UedmException
    {
        return configurationManagerRpcImpl.selectRecordIndexByConditionAllName(recordIndexBean, pageNo, pageSize);
    }
}
