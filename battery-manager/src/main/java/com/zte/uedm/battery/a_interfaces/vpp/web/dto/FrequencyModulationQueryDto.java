package com.zte.uedm.battery.a_interfaces.vpp.web.dto;

import com.zte.uedm.battery.a_domain.common.vpp.FMOverviewEnums;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

/* Started by AICoder, pid:m540cu8f96d89ba1469f0945f16caf035c777f94 */
@Setter
@Getter
@ToString(callSuper = true)
public class FrequencyModulationQueryDto {
    @ApiModelProperty("逻辑位置id")
    private String logicGroupId;

    @ApiModelProperty("调频响应")
    private Boolean freqResponse;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("工作模式")
    private String workModel;

    @ApiModelProperty("站点名称")
    private String siteName;

    @ApiModelProperty("是否支持调频")
    private Boolean enableFreq;

    @ApiModelProperty("排序字段")
    private String sortBy;

    @ApiModelProperty("asc/desc")
    private String order;

    @ApiModelProperty("自定义列表")
    private List<FMOverviewPo> fmMonitorDims;

    public Pair<Boolean, List<String>> checkDimAvailable() {
        List<String> errorList = new ArrayList<>();
        boolean checkResult = checkArgs(errorList, true);
        if (!checkResult) {
            return Pair.of(false, errorList); // 用来存储一对对象，观察方法返回类型
        }
        // 根据概览维度列表，获取 id集合
        List<FMOverviewEnums> dimsDefault = new ArrayList<>(Arrays.asList(FMOverviewEnums.values()));
        List<String> queryIdList = dimsDefault.stream().map(FMOverviewEnums::getId).collect(Collectors.toList());
        // 根据概览维度列表，获取用户序列集合
        List<Integer> querySeqList = dimsDefault.stream().map(FMOverviewEnums::getDefaultIndex).collect(Collectors.toList());
        checkResult = checkIdAndSeq(queryIdList, querySeqList, errorList, true);
        if (!checkResult) {
            return Pair.of(false, errorList);
        }
        Map<String, FMOverviewPo> collect = fmMonitorDims.stream().collect(Collectors.toMap(FMOverviewPo::getId, item -> item));
        // 遍历 概览维度列表 校验不可变动的设定 <4>
        checkResult = checkEnable(dimsDefault, errorList, collect, true);
        return Pair.of(checkResult, errorList);
    }

    public boolean checkArgs(List<String> errorList, boolean checkResult) {
        // 非空判断 <1>
        if (CollectionUtils.isEmpty(fmMonitorDims)) {
            errorList.add("params is blank");
            return false;
        }
        // 获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
        if (checkReqSeq()) {
            checkResult = false;
            errorList.add("params is blank");
        }
        return checkResult;
    }

    private boolean checkReqSeq() {
        List<Integer> reqSeqList = fmMonitorDims.stream().map(FMOverviewPo::getSequence).distinct().collect(Collectors.toList());
        if (reqSeqList.size() != fmMonitorDims.size()) {
            return true;
        }
        return false;
    }

    private boolean checkIdAndSeq(List<String> queryIdList, List<Integer> querySeqList, List<String> errorList, boolean checkResult) {
        List<String> updateIds = fmMonitorDims.stream().map(FMOverviewPo::getId).collect(Collectors.toList());
        List<Integer> updateSeqs = fmMonitorDims.stream().map(FMOverviewPo::getSequence).collect(Collectors.toList());
        updateIds.removeAll(queryIdList);
        if (CollectionUtils.isNotEmpty(updateIds)) {
            checkResult = false;
            errorList.add("The parameter id is not within the optional range");
        }
        updateSeqs.removeAll(querySeqList);
        if (CollectionUtils.isNotEmpty(updateSeqs)) {
            checkResult = false;
            errorList.add("The parameter sequence is not within the optional range");
        }
        return checkResult;
    }

    public boolean checkEnable(List<FMOverviewEnums> dimsDefault, List<String> errorList, Map<String, FMOverviewPo> collect, boolean checkResult) {
        for (FMOverviewEnums enums : dimsDefault) {
            String id = enums.getId();
            Boolean defaultFixed = enums.getDefaultFixed();
            Boolean enable = enums.getDefaultEnable();
            if (null != defaultFixed && defaultFixed && null != enable) {
                FMOverviewPo dim = collect.get(id);
                if (null != dim && !Objects.equals(enable, dim.getEnable())) {
                    errorList.add(dim.getId() + " Default immutable setting verification failed");
                    checkResult = false;
                    break;
                }
            }
        }
        return checkResult;
    }
}
/* Ended by AICoder, pid:m540cu8f96d89ba1469f0945f16caf035c777f94 */
