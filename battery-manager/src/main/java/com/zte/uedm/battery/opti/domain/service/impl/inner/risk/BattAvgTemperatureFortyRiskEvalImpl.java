package com.zte.uedm.battery.opti.domain.service.impl.inner.risk;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.bean.pv.HistoryDataResponseConditionBean;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_AMBIENT_TEMPERATURE;

/**
 * 环境温度低于40
 */
@Component
@Slf4j
public class BattAvgTemperatureFortyRiskEvalImpl
{
    @Autowired
    private PmaServiceUtils pmaServiceUtils;
    @Autowired
    private BattTypeDomain battTypeDomain;
    @Autowired
    private DateTimeService dateTimeService;

    /* Started by AICoder, pid:b9a7fn618ae4f0c14cb1081050ee1641f1386cb2 */
    public Map<String, Boolean> innerRiskRuleEval(int  value) {
        Map<String, Boolean> resMap = new HashMap<>();
        try {
            // 过滤出非回路 锂电池
            List<BattTypeBean> battTypeBeans = battTypeDomain.getBatteryTypeByMoIdFilterLoop();
            List<String> battTypeList = battTypeBeans.stream()
                    .filter(batt -> BattTypeEnum.LFP.getCode().equals(batt.getBattType().getCode()))
                    .map(BattTypeBean::getId)
                    .collect(Collectors.toList());

            // 获取时间范围7天
            Pair<String, String> timeRange =TimeUtils.getTimeRange(7);
            log.info("innerRiskRuleEval_rule090 startTime:{}   endTime:{}",timeRange.getLeft(),timeRange.getRight());
            List<SpIdDto> spIdList = Collections.singletonList(
                    //环境测点，平均值
                    new SpIdDto(BATTERY_SMPID_AMBIENT_TEMPERATURE, GlobalConstants.AVG,null)
            );

            // PMA历史数据
            List<HistoryDataResponseConditionBean> historyDataList = pmaServiceUtils.selectHistoryDataByCondition(
                    battTypeList,
                    MocOptional.BATTERY.getId(),
                    spIdList,
                    timeRange.getLeft() ,
                    timeRange.getRight(),
                    "collect",
                    GlobalConstants.TIME_TYPE_DAY_WHOLE
            );
           log.debug("innerRiskRuleEval_rule090 historyDataList: {}", JSON.toJSONString(historyDataList));
            Map<String, List<Double>> result = historyDataList.stream()
                    .collect(Collectors.groupingBy(
                            HistoryDataResponseConditionBean::getMoId,
                            Collectors.mapping(bean -> Double.parseDouble(bean.getAvgValue()), Collectors.toList())
                    ));

            // 遍历map，计算平均值并判断是否大于40 或45
            result.forEach((moId, avgValues) -> {
                double average = avgValues.stream().mapToDouble(Double::doubleValue).average().orElse(Double.NaN);
                resMap.put(moId, average > value );
            });
        } catch (UedmException | ParseException e) {
            log.error("BattAvgChargeExitRiskEvalImpl occur error, exception is: {}--{}", e.getMessage(), e);
            return Collections.emptyMap();
        }
        log.debug("innerRiskRuleEval_rule090 resMap: {}", JSON.toJSONString(resMap));
        return resMap;
    }
    /* Ended by AICoder, pid:b9a7fn618ae4f0c14cb1081050ee1641f1386cb2 */

}
