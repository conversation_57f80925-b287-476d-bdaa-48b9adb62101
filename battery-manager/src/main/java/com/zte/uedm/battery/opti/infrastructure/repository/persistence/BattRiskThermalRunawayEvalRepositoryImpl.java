package com.zte.uedm.battery.opti.infrastructure.repository.persistence;

import com.zte.uedm.battery.opti.domain.aggregate.repository.BattThermalRunawayRiskEvalRepository;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskThermalRunawayEvalMapper;
import com.zte.uedm.battery.opti.infrastructure.repository.po.BattRiskThermalRunawayEvalPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BattRiskThermalRunawayEvalRepositoryImpl implements BattThermalRunawayRiskEvalRepository {

    @Autowired
    private BattRiskThermalRunawayEvalMapper battRiskThermalRunawayEvalMapper;

    private static final String SYSTEM = "SYSTEM";

    private static final String RISK_ID = "rule044";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> insertOrUpdate(@NotNull Map<String, Integer> riskBatteryMap,List<BattRiskThermalRunawayEvalPo> evalPos) {
        List<String> exceedList = new ArrayList<>();
        try {
            log.info("exits risks ids:{}", riskBatteryMap.keySet());
            List<BattRiskThermalRunawayEvalPo> insertPos = new ArrayList<>();
            List<BattRiskThermalRunawayEvalPo> updatePos = new ArrayList<>();
            List<BattRiskThermalRunawayEvalPo> pos = filterBattRisPo(riskBatteryMap,evalPos);

            if (CollectionUtils.isEmpty(pos)) {
                riskBatteryMap.forEach((k, v) -> {
                    BattRiskThermalRunawayEvalPo evalPo = buildBean(k, v, 0);
                    evalPo.setCreator(SYSTEM);
                    evalPo.setGmtCreate(new Date());
                    insertPos.add(evalPo);
                });
            } else {
                Map<String, Integer> oldTimesMap = pos.stream().filter(p -> StringUtils.isNoneBlank(p.getBattId()))
                        .collect(Collectors.toMap(BattRiskThermalRunawayEvalPo::getBattId, BattRiskThermalRunawayEvalPo::getTimes, (k1, k2) -> k2));
                riskBatteryMap.forEach((k, v) -> {
                    if (oldTimesMap.containsKey(k)) {
                        BattRiskThermalRunawayEvalPo bean = buildBean(k, v, oldTimesMap.get(k));
                        bean.setUpdater(SYSTEM);
                        bean.setGmtModified(new Date());
                        updatePos.add(bean);
                        if (bean.getTimes() >= 3 && !exceedList.contains(bean.getBattId())) {
                            exceedList.add(bean.getBattId());
                        }
                    } else {
                        BattRiskThermalRunawayEvalPo bean = buildBean(k, v, 0);
                        bean.setCreator(SYSTEM);
                        bean.setGmtCreate(new Date());
                        insertPos.add(bean);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(insertPos)) {
                log.info("[create]exists risks battery insertPos size:{}", insertPos.size());
                log.debug("[create]exists risks battery insertPos:{}", insertPos);
                battRiskThermalRunawayEvalMapper.insertBatch(insertPos);
            }

            if (CollectionUtils.isNotEmpty(updatePos)) {
                log.info("[update]exists risks battery updatePos size:{}", updatePos.size());
                log.debug("[update]exists risks battery updatePos:{}", updatePos);
                battRiskThermalRunawayEvalMapper.updateBatch(updatePos);
            }
        } catch (Exception e) {
            log.error("[battRiskThermalRunawayEvalMapper] insertOrUpdate has an error,{},{}", e.getMessage(),e);
        }
        return exceedList;
    }

    @Override
    public List<BattRiskThermalRunawayEvalPo> selectRiskBatterys() {
        return battRiskThermalRunawayEvalMapper.selectPoById(new ArrayList<>());
    }

    private List<BattRiskThermalRunawayEvalPo> filterBattRisPo( Map<String, Integer> riskBatteryMap,List<BattRiskThermalRunawayEvalPo> evalPos) {
        if (CollectionUtils.isEmpty(evalPos)) {
            return evalPos;
        } else {
            return evalPos.stream().filter(p -> riskBatteryMap.containsKey(p.getBattId())).collect(Collectors.toList());
        }
    }

    private BattRiskThermalRunawayEvalPo buildBean(String id, int times, int oldTimes) {
        BattRiskThermalRunawayEvalPo po = new BattRiskThermalRunawayEvalPo();
        po.setBattId(id);
        po.setRiskId(RISK_ID);
        //次数累计
        po.setTimes(times + oldTimes);
        return po;
    }
}
