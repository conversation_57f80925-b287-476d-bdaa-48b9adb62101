package com.zte.uedm.battery.pv.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum SolarCoefficientEnum {
    /**
     * 环境系数
     */
    ENVIRONMENT("environment","{\"en_US\":\"environment coefficient\",\"zh_CN\":\"环境系数\"}","{\"min\":0.1,\"containMin\":\"true\",\"max\":1.0,\"containMax\":true,\"unit\":null}"),
    /**
     * 效率系数
     */

    EFFICIENCY("efficiency","{\"en_US\":\"efficiency coefficient\",\"zh_CN\":\"效率系数\"}","{\"min\":0.1,\"containMin\":\"true\",\"max\":1.0,\"containMax\":true,\"unit\":null}"),
    /**
     * 衰减系数
     */

    ATTENUATION("attenuation","{\"en_US\":\"attenuation coefficient\",\"zh_CN\":\"衰减系数\"}","{\"min\":0.0,\"containMin\":\"true\",\"max\":10.0,\"containMax\":true,\"unit\":\"%\"}");
    private String id;
    private String name;

    private String thread;
    SolarCoefficientEnum(String id, String name,String thread) {
        this.id=id;
        this.name=name;
        this.thread =thread;
    }

    public String getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }
    public String getThread() {
        return this.thread;
    }
    /**
     * 获取系数维度所有ids
     *
     * @return 列表
     */
    public static List<String> getAllPriceDims() {
        List<String> allIds = new ArrayList<>();
        SolarCoefficientEnum[] values = SolarCoefficientEnum.values();
        for (SolarCoefficientEnum value : values) {
            if (StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }
    public static Map<String,SolarCoefficientEnum> byId() {
        Map<String,SolarCoefficientEnum> map = new HashMap<>();
        SolarCoefficientEnum[] values = SolarCoefficientEnum.values();
        for (SolarCoefficientEnum value : values) {
           map.put(value.getId(),value);
        }
        return  map;

    }


}
