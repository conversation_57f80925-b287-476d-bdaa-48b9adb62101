package com.zte.uedm.battery.bean;

import com.zte.uedm.common.bean.log.OperlogBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 发送日志message组成的携带信息的Bean
 */
@Setter
@Getter
@ToString
public class LogInputBean
{

	String user;
	String host;
	String moduleNameZh;
	String moduleNameEn;
	String operationZh;
	String operationEn;
	String operateResult;
	String detailZh;
	String detailEn;

	String[] operateResource;
	String[] resourceIP;

	OperlogBean.LogRank rank;
}
