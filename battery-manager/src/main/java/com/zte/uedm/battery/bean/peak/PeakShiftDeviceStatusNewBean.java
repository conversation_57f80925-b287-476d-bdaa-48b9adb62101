package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Setter
@Getter
@ToString
public class PeakShiftDeviceStatusNewBean
{
    @NotBlank(message = "id can not be blank")
    @Length(max=100, message = "id length limit 100")
    private String id;//监控设备id

    @NotBlank(message = "status can not be blank")
    @Length(max=20, message = "status length limit 20")
    private String status;//错峰策略启停状态

    @NotNull(message = "gmtCreate can not be null")
    private Date gmtCreate;
    private Date gmtModified;
}
