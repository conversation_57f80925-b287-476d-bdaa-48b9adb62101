/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattLifeConfigService
 * 文件作者：00248587
 * 开发时间：2023/3/1
 */
package com.zte.uedm.battery.service.battlife;

import com.zte.uedm.battery.controller.battlife.dto.BattLifeDimsUpdateDto;
import com.zte.uedm.battery.controller.battlife.vo.BattLifeConfigVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BattLifeConfigService
{
    /**
     * 电池寿命列表表头查询
     * @param userName
     * @param languageOption
     * @return
     * @throws UedmException
     */
    List<BattLifeConfigVo> selectBattLifeListConfig(String userName, String languageOption) throws UedmException;

    /**
     * 寿命列表维度更新
     * @param updateBeanList
     * @param userName
     * @param languageOption
     * @return 更新成功数量
     */
    Integer updateBattLifeConfig(List<BattLifeDimsUpdateDto> updateBeanList, String userName, String languageOption) throws UedmException;

    /**
     * 寿命列表维度搜索
     * @param name
     * @param userName
     * @param languageOption
     * @return
     * @throws UedmException
     */
    List<BattLifeConfigVo> searchBattLifeConfig(String name, String userName, String languageOption) throws UedmException;
}
