package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.SeveritySuperBean;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmSchBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

public interface MonitorAlarmService {
    /**
     * 查询所有告警（包括当前告警和历史告警）
     *
     * @param alarmSchBean
     *            查询条件
     * @return 所有告警
     * @throws UedmException
     */
    List<Alarm> getAlarmData(AlarmSchBean alarmSchBean, String lang, String userName) throws UedmException;

    /**
     * 分页查询所有告警
     *
     * @param alarmList
     *            所有告警列表
     * @param pageNo
     *            当前页
     * @param pageSize
     *            页大小
     * @return 分页后的告警
     */
    List<Alarm> getPageList(List<Alarm> alarmList, int pageNo, int pageSize);

    /**
     * 获取告警对应的最高告警级别
     *
     * @param ids
     *            要查询的所有告警List
     * @return 对应的告警级别List
     * @throws UedmException
     */
    List<SeveritySuperBean> getStatisticsTree(List<String> ids, String lang) throws UedmException;

    /**
     * 分等级查询告警数量
     * @param allAlarmList
     * @return
     */
    Map<String, Integer> getAlarmLevelCount(List<Alarm> allAlarmList);

}
