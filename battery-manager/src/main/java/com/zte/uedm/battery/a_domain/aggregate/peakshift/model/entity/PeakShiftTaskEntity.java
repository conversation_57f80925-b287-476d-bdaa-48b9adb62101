package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftTaskDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/* Started by AICoder, pid:nb473h83aaoefb7146e908c720d7f9152ae1ef66 */
@Getter
@Setter
@ToString
public class PeakShiftTaskEntity {
    /**
     * 任务id
     */
    private String id;
    /**
     * 任务名称BattTestTaskPo
     */
    private String name;
    /**
     * 策略文件id
     */
    private List<String> fileIds;
    /**
     * 备注
     */
    private String description;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private String gmtModified;
    /**
     * 任务状态
     */
    private String status;
    /**
     * 生效日期
     */
    private String effectiveDate;
    /**
     * 失效日期
     */
    private String expirationDate;

    /**
     * 进度：任务下发成功或者失败的设备数量/总设备数量
     */
    private String progress;

    /**
     * 生效状态  expired，nearToExpired ，normal
     * 当前时间>失效时间？失效; 当前时间+7d>失效时间？即将失效; 其余：正常
     */
    private String effectiveStatus;

    /**
     * 策略模板ID
     */
    private String templateStrategyId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    public PeakShiftTaskEntity() {

    }

    public PeakShiftTaskEntity(PeakShiftTaskDto peakShiftTaskDto) {
        this.id = peakShiftTaskDto.getId();
        this.name = peakShiftTaskDto.getName();
        this.description = peakShiftTaskDto.getDescription();
        this.effectiveDate = peakShiftTaskDto.getEffectiveDate();
        this.expirationDate = peakShiftTaskDto.getExpirationDate();
        this.templateStrategyId = peakShiftTaskDto.getTemplateStrategyId();
        this.deviceType = peakShiftTaskDto.getDeviceType();
    }

}
/* Ended by AICoder, pid:nb473h83aaoefb7146e908c720d7f9152ae1ef66 */
