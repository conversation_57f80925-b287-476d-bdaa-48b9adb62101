package com.zte.uedm.battery.opti.domain.aggregate.model;

import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeGroupOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceValueTypeOptional;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Getter
@Setter
@ToString
@Slf4j
public class BattRiskRuleSourceEntity
{
    /**
     * 风险参数id
     */
    private String id;
    /**
     * 风险参数名称
     */
    private String name;
    /**
     * 来源类型
     */
    private BattRiskRuleSourceTypeOptional sourceType;
    /**
     * 来源类型分组
     */
    private BattRiskRuleSourceTypeGroupOptional sourceTypeGroup;
    /**
     * 值类型
     */
    private BattRiskRuleSourceValueTypeOptional valueType;
    /**
     * 值定义
     */
    private String valueDefine;
    /**
     * 映射字段
     */
    private String mappingId;
    /**
     * 值映射
     */
    private Map<String, String> valueMapping;
    /**
     * 是否内置
     */
    private Boolean internal;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date gmtModified;


    /**
     * 值映射的转换
     * @param value
     * @return
     * @throws UedmException
     */
    public String transferValue(String value) throws UedmException
    {
        if(StringUtils.isBlank(value))
        {
            return value;
        }

        if(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_D.equals(this.valueType))
        {
            log.debug("[BattRiskRuleSourceEntity-transferValue] valueType={}", this.valueType);
            return transferValueWithNumberD(value);
        }
        else if(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_DATE_STRING.equals(this.valueType))
        {
            log.debug("[BattRiskRuleSourceEntity-transferValue] valueType={}", this.valueType);
            return String.valueOf(TimeUtils.getDate_d(value));
        }
        else if(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_TIME_STRING.equals(this.valueType))
        {
            log.debug("[BattRiskRuleSourceEntity-transferValue] valueType={}", this.valueType);
            return String.valueOf(DateTimeService.convertStrToDate(value).getTime());
        }
        else if (BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_EXIST_D.equals(this.valueType)) {
            return ObjectUtils.isEmpty(value) ? CommonConst.NOT_EXIST : CommonConst.EXIST;
        }
        else if (BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_DURATION.equals(this.valueType)) {
            /* Started by AICoder, pid:27db9l271dla03114be50ab490c206064d21fdfc */
            long currentTimeMillis = System.currentTimeMillis();
            /* Ended by AICoder, pid:27db9l271dla03114be50ab490c206064d21fdfc */
            /* Started by AICoder, pid:j44635af02r8f7914fd70a7300305b044dc1ea3b */
            long alarmTime = Long.valueOf(value);
            /* Ended by AICoder, pid:j44635af02r8f7914fd70a7300305b044dc1ea3b */
            Double duration = (double)(currentTimeMillis - alarmTime);
            BigDecimal value1 = BigDecimal.valueOf((duration));
            BigDecimal time = value1.divide(BigDecimal.valueOf(1000 * 60 * 60), 4, BigDecimal.ROUND_HALF_UP);
            return time.toString();
        }
        else
        {
            return value;
        }
    }

    private String transferValueWithNumberD(String key) throws UedmException
    {
        if(valueMapping == null)
        {
            log.error("valueMapping is null, can't support getValueMappingByKey, valueMapping={}", this.valueMapping);
            throw new UedmException(-1,"valueType is not SOURCE_TYPE_NUMBER_D or SOURCE_TYPE_NUMBER_DATE_STRING, can't support getValueMappingByKey");
        }

        if(!valueMapping.containsKey(key))
        {
            log.error("valueMapping can't contains key, key={}", key);
            throw new UedmException(-1,"valueMapping can't contains key");
        }

        return valueMapping.get(key);
    }

    public String transferValueWithExistD(Object object) {
        if (ObjectUtils.isEmpty(object)) {
            return CommonConst.NOT_EXIST;
        }
        return CommonConst.EXIST;
    }

    public String getSourceTypeId()
    {
        return this.sourceType.getId();
    }
}
