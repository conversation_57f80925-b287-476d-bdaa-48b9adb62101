package com.zte.uedm.battery.domain;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattBackupPowerThresholdDetailPojo;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.SpecialSelectByConditionDto;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import java.util.Map;
import java.util.List;

public interface BackupPowerThresholdDetailDomain
{
    BattBackupPowerThresholdDetailPojo selectById(String id) throws
            UedmException;

    /**
     * 更新特殊设置
     * @param beanList
     * @return
     * @throws UedmException
     */
    Integer insert(List<BattBackupPowerThresholdDetailPojo> beanList) throws UedmException;

    /**
     * 更新特殊设置
     * @param beanList
     * @return
     * @throws UedmException
     */
    Integer update(List<BattBackupPowerThresholdDetailPojo> beanList) throws UedmException;

    /**
     * 删除特殊设置--恢复默认设置
     * @param ids
     * @return
     * @throws UedmException
     */
    Integer delete(List<String> ids) throws UedmException;

    /**
     * 根据开关电源ids获取特殊设置
     * 返回值map :key为开关电源id，value为阈值值
     * @param ids
     * @param userName
     * @return
     * @throws UedmException
     */
    Map<String,Integer> getDefaultThreshold(List<String> ids,String userName) throws UedmException;

    /**
     * 条件查询detail表
     * @param specialSelectByConditionDto
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerThresholdDetailPojo> selectByCondition(
            SpecialSelectByConditionDto specialSelectByConditionDto,
            ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;
}
