package com.zte.uedm.battery.pv.bean;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/30
 **/
@Getter
@Setter
@ToString
@ApiModel(description = "太阳能收益统计需要的对象")
public class SolarRevenueStatisticsDayBean {

    /**
     * 太阳能策略
     */
    SolarRevenueScopeStrategyBean solarRevenueScopeStrategyBean;

    /**
     * 市电策略
     */
    SolarRevenueScopeStrategyBean gridRevenueScopeStrategyBean;

    /**
     * 汇聚周期
     */
    private Integer minuteConvergeCycle;

    /**
     * 通用小时timeRange key 小时 value当前小时按照汇聚周期分割
     */
    private Map<String, Map<String, Integer>> commonTimeRangeMapHours;

    /**
     * 太阳能站点当前计费模式下面发电量(针对阶梯计费模式(月/年))
     */
    private Map<String, String> sitePowerGenerationMap;
}
