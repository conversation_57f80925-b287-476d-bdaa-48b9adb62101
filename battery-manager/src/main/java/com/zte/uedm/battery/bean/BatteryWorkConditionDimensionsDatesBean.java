package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class BatteryWorkConditionDimensionsDatesBean {
    private boolean change;
    private String name;
    private int defaultIndex;
    private boolean defaultEnable;
    private boolean defaultFixed;

    public BatteryWorkConditionDimensionsDatesBean(boolean change,String name,int defaultIndex,boolean defaultEnable,boolean defaultFixed){
        this.change=change;
        this.name=name;
        this.defaultIndex=defaultIndex;
        this.defaultEnable=defaultEnable;
        this.defaultFixed=defaultFixed;
    }

}
