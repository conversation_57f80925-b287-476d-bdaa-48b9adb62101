package com.zte.uedm.battery.util;


import com.zte.uedm.battery.consts.DateTypeConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @FileName : DateUtils.java
 * @FileDesc : TODO
 * @Version : 1.0
 * <AUTHOR> 何杰10253457
 */
@Slf4j
public class DateUtils
{

    /**
     * 获取指定日期所在月份开始的时间
     *
     * @return
     */
    public static String getMonthBegin(String specifiedDay)
    {
        Date data = null;

        try
        {
            data = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1).parse(specifiedDay);
        }
        catch (ParseException e1)
        {
            // TODO Auto-generated catch block
            log.error("getMonthBegin is error!", e1);
        }

        Calendar c = Calendar.getInstance();
        if (null == data)
        {
            return null;
        }
        c.setTime(data);
        // 设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 本月第一天的时间戳转换为字符串
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date date;
        try
        {
            date = sdf.parse(sdf.format(new Date(c.getTimeInMillis())));
            // Date date = sdf.parse(sdf.format(new Long(s)));// 等价于
            return sdf.format(date);
        }
        catch (Exception e)
        {

        }
        return null;
    }

    /**
     * 获取某天的开始
     *
     * @param specifiedDay
     * @return
     */
    public static String getDayBegin(String specifiedDay)
    {
        Date data = null;

        try
        {
            data = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1).parse(specifiedDay);
        }
        catch (ParseException e1)
        {
            // TODO Auto-generated catch block
            log.error("getDayBegin is error!", e1);
        }

        if (null == data)
        {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(data);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        // 将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 本月第一天的时间戳转换为字符串
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date date;
        try
        {
            date = sdf.parse(sdf.format(new Date(c.getTimeInMillis())));
            // Date date = sdf.parse(sdf.format(new Long(s)));// 等价于
            return sdf.format(date);
        }
        catch (Exception e)
        {
            // TODO 自动生成的 catch 块
        }
        return null;
    }

    /**
     * 获取月的最小时间。
     *
     * @return 时间
     */
    public static String getMinTimeOfYear(Date d) throws ParseException
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int currentYear = cal.getActualMinimum(Calendar.YEAR);
        cal.set(Calendar.DAY_OF_YEAR, currentYear);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date date = cal.getTime();
        date = sdf.parse(sdf.format(date));
        // Date date = sdf.parse(sdf.format(new Long(s)));// 等价于
        return sdf.format(date);
    }

    /**
     * 获取月的最大时间。
     *
     * @return 时间
     */
    public static String getMaxTimeOfYear(Date d) throws ParseException
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_YEAR);
        cal.set(Calendar.DAY_OF_YEAR, lastDay);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date date = cal.getTime();

        date = sdf.parse(sdf.format(date));
        return sdf.format(date);
    }

    /**
     * 获取月的最小时间。
     *
     * @return 时间
     */
    public static Date getMinTimeOfMonth(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取月的最大时间。
     *
     * @return 时间
     */
    public static Date getMaxTimeOfMonth(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        cal.set(Calendar.DAY_OF_MONTH, lastDay);

        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取月的最大时间的字符串。
     *
     * @return 时间字符串
     */
    public static String getMaxTimeStrOfMonth(Date d)
    {
        Date date = getMaxTimeOfMonth(d);
        return getStrDate(date);
    }

    /**
     * 获取月的最小时间的字符串。
     *
     * @return 时间字符串
     */
    public static String getMinTimeStrOfMonth(Date d)
    {
        Date date = getMinTimeOfMonth(d);
        return getStrDate(date);
    }

    /**
     * 获取天的最小时间。
     *
     * @return 时间
     */
    public static Date getMinTimeOfDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取前一天的最小时间
     *
     * @param d
     * @return
     */
    public static Date getMinTimeOfDayBeforeOneDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取前两天的最小时间
     *
     * @param d
     * @return
     */
    public static Date getMinTimeOfTwoDayBeforeOneDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, -2);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取前一天的最大时间
     *
     * @param d
     * @return
     */
    public static Date getMaxTimeOfDayBeforeOneDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取前两天的最大时间
     *
     * @param d
     * @return
     */
    public static Date getMaxTimeOfDayBeforeTwoDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DAY_OF_MONTH, -2);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取天的最大时间。
     *
     * @return 时间
     */
    public static Date getMaxTimeOfDay(Date d)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取天的最小时间的字符串。
     *
     * @return 时间字符串
     */
    public static String getMinTimeStrOfDay(Date d)
    {
        Date date = getMinTimeOfDay(d);
        return getStrDate(date);
    }
    /**
     * 获取天的最小时间的字符串。
     *
     * @return 时间字符串
     */
    public static String getMaxTimeStrOfDay(Date d)
    {
        Date date = getMaxTimeOfDay(d);
        return getStrDate(date);
    }

    public static String getStrDate(Date date)
    {
        if(null == date)
        {
            return "";
        }
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        return format.format(date);
    }

    public static Date getDate(String date, String dateType)
    {
        SimpleDateFormat format = new SimpleDateFormat(dateType);
        try {
            return format.parse(date);
        }
        catch (ParseException e) {
            return null;
        }
    }

    public static String getStrDateByDateType(Date date, String dateType)
    {
        if(null == date || StringUtils.isBlank(dateType))
        {
            return "";
        }
        SimpleDateFormat format = new SimpleDateFormat(dateType);
        return format.format(date);
    }

    public static Date getYearMonDayDate(Date date)
    {
        LocalDate localDate=date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date yearMonDate =java.sql.Date.valueOf(localDate);
        return yearMonDate;
    }

    /**
     * 转为年月日日期
     * @param time
     * @return
     */
    public static Date getYearMonDayDate(String time)
    {
        try
        {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(time);
            return date;
        }
        catch (ParseException e)
        {
            log.error("getYearMonDayDate error", e);
            return null;
        }
    }

    /**
     * 获取前一天的最小时间字符串
     *
     * @param d
     * @return
     */
    public static String getMinTimeStrOfDayBeforeOneDay(Date d)
    {
        Date date = getMinTimeOfDayBeforeOneDay(d);
        return getStrDate(date);
    }



    /**
     * 增加日期，以天为单位
     */
    public static Date addDay(Date date, Integer day)
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    /**
     * 获取yyyy-MM-dd格式的日期
     */
    public static String getYearMonDayDateStr(Date date)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formatStr = sdf.format(date);
        return formatStr;
    }

    /**
     * 获取yyyy-MM格式的日期
     */
    public static String getYearMonDateStr(Date date)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String formatStr = sdf.format(date);
        return formatStr;
    }


    /**
     * 获取yyyy-MM-dd格式的日期
     */
    public static Date getStringDateStr(String date) throws ParseException
    {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Date formatStr = sdf.parse(date);
        return formatStr;
    }

    /**
     * 获取yyyy-MM-dd.SSS格式的日期
     */
    public static Date getDateFromStr(String date) throws ParseException
    {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_5);
        Date formatStr = sdf.parse(date);
        return formatStr;
    }

    /**
     * @description   获取*单位后的时间
     * @param num 时间单位数（如果num数为负数,说明是此日期前的天数）
     * @param date：小时
     */
    public static String getDateByAdd(Date date, int num) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, num);
        Date time = cal.getTime();
        return sdf.format(time);
    }

    /**
     * @description   获取*单位后的时间
     * @param num 时间单位数（如果num数为负数,说明是此日期前的天数）
     * @param date：小时
     */
    public static String getDateByAddMillisecond(Date date, int num) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_5);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, num);
        Date time = cal.getTime();
        return sdf.format(time);
    }

    /**
     * @description   获取*单位后的时间
     * @param num 时间单位数（如果num数为负数,说明是此日期前的月份）
     * @param date：月份
     */
    public static String getDateByAddOfMonth(Date date, int num) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, num);
        Date time = cal.getTime();
        return sdf.format(time);
    }

    /**
     * @description   获取*单位后的时间
     * @param num 时间单位数（如果num数为负数,说明是此日期前几天）
     * @param date：天
     */
    public static String getDateByAddOfDay(Date date, int num) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, num);
        Date time = cal.getTime();
        return sdf.format(time);
    }

    /**
     * 获取前一天日期
     * @param today
     * @return
     */
    public static Date getYesTerDay(Date today)
    {
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();//这是昨天
        LocalDate localDate=yesterday.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date yearMonDate =java.sql.Date.valueOf(localDate);
        return yearMonDate;
    }

    /**
     * 获取前几天  或 后几天 日期 -  +
     * @param today
     * @return
     */
    public static Date getYesTerDay(Date today,int day)
    {
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DAY_OF_MONTH, day);
        Date yesterday = c.getTime();
        LocalDate localDate=yesterday.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date yearMonDate =java.sql.Date.valueOf(localDate);
        return yearMonDate;
    }

    public static Long getTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).getEpochSecond() * 1000 + date.getTime() % 1000;
    }
    /**
     * 获取当天年月日 yyyy-MM-dd
     */
    public static String getCurrentDay(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        String currentDay = format.format(new Date());
        return currentDay;
    }

    /**
     * 根据粒度把时间进行分段
     * @param startTime
     * @param endTime
     * @param grain
     * @return
     */
    public static List<Pair<String, String>> getSegmentTime(String startTime, String endTime, Integer grain){

        List<Pair<String, String>> timeList = new ArrayList<>();
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            return timeList;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_2);
        LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), formatter);
        LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), formatter);
        if (startDate == null)
        {
            return timeList;
        }
        while (!startDate.isAfter(endDate)){
            LocalDate tempDate = startDate.plusDays(grain - 1);
            String start = startDate.format(formatter);
            String end;
            if (tempDate.isAfter(endDate)) {
                tempDate = endDate;
                end = endDate.format(formatter);
            }else {
                end = tempDate.format(formatter);
            }
            timeList.add(Pair.of(start, end));
            startDate = tempDate.plusDays(1);
        }

        return timeList;
    }

    public static String getFormateTime(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1, Locale.getDefault());
        return sdf.format(new Date(time));
    }


}
