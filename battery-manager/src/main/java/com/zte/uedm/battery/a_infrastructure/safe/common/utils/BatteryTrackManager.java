package com.zte.uedm.battery.a_infrastructure.safe.common.utils;

import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.OperationLogMapper;
import com.zte.uedm.common.bean.log.LogResultEnum;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.bean.log.OperationLogQueryBean;
import com.zte.uedm.common.bean.south.DeviceLinkCommStatusDTO;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.component.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.GlobalConstants.BAD_STATUS;

@Service
@Slf4j
public class BatteryTrackManager {

    private static String NA_STATUS = "2.0"; // 未知状态标志

    @Resource
    private RedisService commonRedisService;
    @Resource
    private BlankService blankService;
    @Resource
    private OperationLogMapper operationLogMapper;
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Resource
    private CollectorCacheManager collectorCacheManager;

    //查询moId的通讯状态(旧)
//    public String findCommStatus(String moId) throws UedmException {
//        Map<String, String> linkStatusMap = southFrameworkRpcImpl.queryAllLinkMonitor();
//        Set<String> list = resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId);
//        int connectStatus = -1;
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (String collectorId : list) {
//                // 根据监控设备id从缓存查询监控设备的信息
//                List<CollectorEntity> entityList = collectorCacheManager.getCollectorById(Collections.singletonList(collectorId));
//                log.debug("findCommStatus entityList={}",entityList);
//                // 根据监控设备的链路 找到链路的状态
//                connectStatus = entityList.stream()
//                        .filter(Objects::nonNull)
//                        .flatMap(entity -> {
//                            List<Map<String, Object>> linkInfo = (List<Map<String, Object>>) entity.getLinkInfo();
//                            return linkInfo.stream()
//                                    .filter(y -> y.containsValue("master"))
//                                    .map(map -> {
//                                        String linkId = map.keySet().iterator().next();
//                                        return Integer.parseInt(linkStatusMap.get(linkId));
//                                    });
//                        })
//                        .findFirst()
//                        .orElse(-1);
//            }
//        }
//        String communicationStatus = NA_STATUS;
//        if (connectStatus == 0) {
//            communicationStatus = "0.0";
//        }
//        else if (connectStatus == 1){
//            communicationStatus = "1.0";
//        }
//        return communicationStatus;
//    }

    public Integer getBatteryOperationStatus(String moId){
        OperationLogQueryBean operationLogQueryBean = new OperationLogQueryBean();
        operationLogQueryBean.setResourceId(moId);
        operationLogQueryBean.setStatus(LogResultEnum.proceeding.getValue());
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        } catch (Exception e) {
            log.error("BatteryTrackManager [getBatteryOperationStatus] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        List<Integer> statusList = operationLogBeans.stream().map(OperationLogBean::getStatus).collect(Collectors.toList());
        //1为执行中，若包含则直接返回1
        if(CollectionUtils.isNotEmpty(statusList) && statusList.contains(1)){
            return 1;
        }
        return 2;
    }

    public String getDeviceStatus(String moId)
    {
        String communicationStatus = NA_STATUS;
        if (StringUtils.isBlank(moId))
        {
            return communicationStatus;
        }
        try
        {
            Map<String,Object> map;
            Set<String> list = resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId);
            map = commonRedisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO);
            if (map != null && CollectionUtils.isNotEmpty(list))
            {
                for (String collector : list)
                {
                    DeviceLinkCommStatusDTO deviceCommDto = (DeviceLinkCommStatusDTO) map.get(collector);
                    Integer status = (deviceCommDto != null) ?
                            Optional.ofNullable(deviceCommDto.getStatus()).orElse(BAD_STATUS) :
                            BAD_STATUS;

                    //有一采集器正常时break
                    if (status == 0)
                    {
                        communicationStatus = "0.0";
                        break;
                    }
                    else if (status == 1)
                    {
                        communicationStatus = "1.0";
                    }
                }

            }
        }
        catch (Exception e)
        {
            log.error("RedisEventServiceImpl getCacheMapNotNull error",e);
        }

        return communicationStatus;
    }

    public String getDeviceStatus(Map<String,Object> map,List<String> collectorIdList)
    {
        String communicationStatus = NA_STATUS;
        try
        {
            if (map != null && CollectionUtils.isNotEmpty(collectorIdList))
            {
                for (String collector : collectorIdList)
                {
                    DeviceLinkCommStatusDTO deviceCommDto = (DeviceLinkCommStatusDTO) map.getOrDefault(collector,new DeviceLinkCommStatusDTO());
                    Integer status = (deviceCommDto != null) ?
                            Optional.ofNullable(deviceCommDto.getStatus()).orElse(BAD_STATUS) :
                            BAD_STATUS;

                    //有一采集器正常时break
                    if (status == 0)
                    {
                        communicationStatus = "0.0";
                        break;
                    }
                    else if (status == 1)
                    {
                        communicationStatus = "1.0";
                    }
                }

            }
        }
        catch (Exception e)
        {
            log.error("RedisEventServiceImpl getCacheMapNotNull error",e);
        }

        return communicationStatus;
    }
}
