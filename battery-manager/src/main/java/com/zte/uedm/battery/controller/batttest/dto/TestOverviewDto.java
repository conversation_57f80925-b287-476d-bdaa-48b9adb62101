package com.zte.uedm.battery.controller.batttest.dto;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zte.uedm.battery.bean.BatteryHealthDimensionsBean;
import com.zte.uedm.battery.bean.BatteryOverviewDimsVo;
import com.zte.uedm.battery.bean.ImageBean;
import com.zte.uedm.battery.enums.backuppower.BackupPowerStateEnum;
import com.zte.uedm.battery.enums.batttest.*;
import com.zte.uedm.battery.enums.overview.BattHealthListDimEnums;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
@Slf4j
@ApiModel(description = "测试概览")
public class TestOverviewDto {

    /**
     * 位置id
     */
    @NotBlank(message = "logicGroupId can not be blank")
    @ApiModelProperty(value = "位置标识")
    private String logicGroupId;
    /**
     *测试状态列表
     */
    @ApiModelProperty(value = "测试状态列表")
    private List<String> testStatus;
    /**
     *站点等级列表
     */
    @ApiModelProperty(value = "站点等级列表")
    private List<String> siteLevels;
    /**
     *供电场景列表
     */
    @ApiModelProperty(value = "供电场景列表")
    private List<String> powerSupplyScene;
    /**
     *备电状态列表
     */
    @ApiModelProperty(value = "备电状态列表")
    private List<String> backupPowerStatus;
    /**
     *健康状态列表
     */
    @ApiModelProperty(value = "健康状态列表")
    private List<String> healthStatus;
    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String order;
    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String sort;
    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer pageNo;
    /**
     * 	每页数量
     */
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
    @ApiModelProperty(value = "排序字段")
    private boolean orderB;
    /**
     * 	电源概览维度列表
     */
    @ApiModelProperty(value = "电源概览维度列表")
    private List<BatteryOverviewDimsVo> overviewDims;
    /**
     * 	电池组概览维度列表
     */
    @ApiModelProperty(value = "电池组概览维度列表")
    private List<BatteryOverviewDimsVo> overviewBattSetDims;
    /**
     * 	测试类型列表
     */
    @ApiModelProperty(value = "测试类型列表")
    private List<String> testTypes;
    /**
     * 	测试结果列表
     */
    @ApiModelProperty(value = "测试结果列表")
    private List<String> testResult;
    /**
     * 	交流输入状态列表
     */
    @ApiModelProperty(value = "交流输入状态")
    private List<String> acInputStatus;
    /**
     * 下发时间起
     */
    @ApiModelProperty(value = "下发时间起")
    private String deliveryStartTime;
    /**
     * 下发时间止
     */
    @ApiModelProperty(value = "下发时间止")
    private String deliveryEndTime;
    /**
     * 开关电源名称或位置
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 50,message = "name length cannot exceed 50")
    private String name;
    @ApiModelProperty(value = "图片")
    List<ImageBean> images;
    @ApiModelProperty(value = "位置")
    private String position;

    /**
     * 统计维度列表
     */
    @ApiModelProperty(value = "统计维度列表")
    private List<String> statisticsDims;


    /**
     * 检查传入参数order以及sort是否可用
     */
    public Pair<Boolean, Map<String, Set<String>>> checkOrderAndSortAvailable() throws UedmException
    {
        boolean state = true;
        Map<String,Set<String>> map = new HashMap<>();
        if (StringUtils.isNotBlank(this.order)){
            boolean checkOrder = TestOverviewDto.TestOverviewEnum.checkOrder(this.order);
            if (!checkOrder){
                state=false;
                log.error("The parameter order is not in the optional range.");
                map.put("order", TestOverviewDto.TestOverviewEnum.allOrderKeySet());
            }
            this.order = TestOverviewDto.TestOverviewEnum.getOrderValue(order);
            orderB=false;
        }else {
            this.order =getDefaultValue();
            orderB=true;
        }

        if (StringUtils.isNotBlank(this.sort)){
            boolean checkSort = TestOverviewDto.TestOverviewEnum.checkSort(this.sort);
            if (!checkSort){
                state = false;
                map.put("sort", SortEnum.getSortIds());
            }
        }else {
            this.sort=SortEnum.getAscSortID();
        }

        log.info("TestOverviewDto -> checkOrderAndSortAvailable order:{},sort:{}",order,sort);
        return Pair.of(state,map);
    }

    /**
     * 获取默认order - 默认排序：开始时间+位置
     * @return
     */
    public  String getDefaultValue(){
        return TestOverviewEnum.TEST_START_TIME.orderValue+","+TestOverviewEnum.POSITION.orderValue;
    }

    private static final String STATUS = "testStatus";
    private static final String TYPE = "testType";
    private static final String RESULT = "testResult";
    private static final String AC_INPUT_STATUS = "acInputStatus";

    public boolean checkDeliveryTime()
    {
        if(StringUtils.isNotEmpty(deliveryStartTime) && StringUtils.isNotEmpty(deliveryEndTime))
        {
            if(deliveryStartTime.compareTo(deliveryEndTime) > 0)
            {
                log.warn("deliveryEndTime <= deliveryStartTime.");
                return false;
            }
        }
        return true;
    }

    public ResponseBean checkDimAvailable()
    {
        List<BatteryOverviewDimsVo> dims = this.overviewDims;
        ResponseBean response = checkArgs(dims);
        if (response != null)
        {
            return response;
        }
        //获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
        if (checkReqSeq(dims))
        {
            return ResponseBeanUtils.getNormalResponseBean(-302, null, null, null, null);
        }
        List<String> errorList = new ArrayList<>();
        //根据概览维度列表，获取 id集合
        List<BatteryOverviewDimsVo> dimsDefault = getAllDims();
        log.info("BatteryTestDimensionsBean dimsDefault={}", dimsDefault);
        List<String> queryIdList =
                dimsDefault.stream().map(BatteryOverviewDimsVo::getId).collect(Collectors.toList());
        //根据概览维度列表，获取用户序列集合
        List<Integer> querySeqList =
                dimsDefault.stream().map(BatteryOverviewDimsVo::getDefaultIndex).collect(Collectors.toList());
        response = checkIdAndSeq(dims, queryIdList, querySeqList, errorList);
        if (response != null)
        {
            return response;
        }

        //遍历 概览维度列表 校验不可变动的设定 <4>
        if (checkEnable(dims, errorList, dimsDefault))
        {
            return ResponseBeanUtils.getNormalResponseBean(-208, JSON.toJSONString(errorList), null, null, null);
        }
        return null;
    }

    private boolean checkEnable(List<BatteryOverviewDimsVo> dims, List<String> errorList, List<BatteryOverviewDimsVo> dimsDefault)
    {
        for (BatteryOverviewDimsVo queryBean : dims) {
            String id = queryBean.getId();
            Boolean enable = queryBean.getDefaultEnable();
            if(queryBean.getDefaultFixed()){
                List<BatteryOverviewDimsVo> filterList =
                        dims.stream().filter(item -> item.getId().equals(id)).collect(Collectors.toList());
                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(filterList))
                {
                    BatteryOverviewDimsVo dim = filterList.get(0);
                    if(!dim.getEnable().equals(enable)){
                        log.error("BatteryTestConfig -> DefaultFixed is true");
                        errorList.add(dim.getId());
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private ResponseBean checkIdAndSeq(List<BatteryOverviewDimsVo> dims, List<String> queryIdList, List<Integer> querySeqList, List<String> errorList)
    {
        for (BatteryOverviewDimsVo dim : dims) {
            //入参对象id
            if (checkId(queryIdList, errorList, dim))
            {
                return ResponseBeanUtils.getNormalResponseBean(-305, JSON.toJSONString(errorList), null, null, null);
            }

            //sequence 每个元素的int唯一 <2>
            if (checkSequence(querySeqList, dim))
            {
                return ResponseBeanUtils.getNormalResponseBean(-302, null, null, null, null);
            }
        }
        return null;
    }

    public boolean checkSequence(List<Integer> querySeqList, BatteryOverviewDimsVo dim) {
        Integer sequence = dim.getSequence();
        if (!querySeqList.contains(sequence)) {
            log.error("BatteryTestConfig -> sequence is not a given data, sequence={}, querySeqList={}", sequence,
                    querySeqList);
            return true;
        }
        return false;
    }

    public boolean checkId(List<String> queryIdList, List<String> errorList,
            BatteryOverviewDimsVo dims) {
        String id = dims.getId();
        //id 在可选值范围内判断 <3>
        if (!queryIdList.contains(id)) {
            log.error("BatteryTestConfig -> id is not a given data");
            errorList.add(id);
            return true;
        }
        return false;
    }

    public List<BatteryOverviewDimsVo> getAllDims()
    {
        List<BatteryOverviewDimsVo> dims = new ArrayList<>();
        BatteryOverviewDimsEnums[] values = BatteryOverviewDimsEnums.values();
        for(BatteryOverviewDimsEnums value : values)
        {
            BatteryOverviewDimsVo dim = new BatteryOverviewDimsVo();
            dim.setId(value.getId());
            dim.setDefaultFixed(value.getDefaultFixed());
            dim.setDefaultEnable(value.getDefaultEnable());
            dim.setDefaultIndex(value.getDefaultIndex());
            dim.setName(value.getName());
            dims.add(dim);
        }
        return dims;
    }

    private boolean checkReqSeq(List<BatteryOverviewDimsVo> dims)
    {
        List<Integer> reqSeqList = dims.stream().map(BatteryOverviewDimsVo::getSequence).distinct()
                .collect(Collectors.toList());
        if(reqSeqList.size() != dims.size()){
            log.error("BatteryTestConfig -> sequence is not unique");
            return true;
        }
        return false;
    }

    private ResponseBean checkArgs(List<BatteryOverviewDimsVo> dims)
    {
        //非空判断 <1>
        if (ObjectUtils.isEmpty(dims)) {
            log.error("BatteryTestConfig -> parameter is blank");
            return ResponseBeanUtils.getNormalResponseBean(-301, null, null, null, null);
        }
        return null;
    }

    /**
     * 测试概览-排序字段可选值枚举量
     */
    @Slf4j
    enum TestOverviewEnum {
        NAME("name", "name"),
        POSITION("position", "position"),
        DELIVERY_TIME("deliveryTime", "deliveryTime"),
        TEST_START_TIME("testStartTime","testStartTime"),
        DURATION("duration","durationTotalMin");

        private String orderKey;
        private String orderValue;

        TestOverviewEnum(String orderKey, String orderValue) {
            this.orderKey = orderKey;
            this.orderValue = orderValue;
        }

        public String getOrderKey() {
            return orderKey;
        }

        public String getOrderValue() {
            return orderValue;
        }

        /**
         * 校验order
         * order在默认值范围内，则返回true，否则返回false
         * @param order
         * @return
         */
        static boolean checkOrder(String order) throws UedmException
        {
            Set<String> orderKeys = TestOverviewDto.TestOverviewEnum.allOrderKeySet();
            //默认值不包含order
            return orderKeys.contains(order);
        }

        /**
         * 校验sort
         * sort为空且在默认值范围内，则返回true，否则返回false
         * @param sort
         * @return
         */
        static boolean checkSort(String sort) throws UedmException
        {
            Set<String> sortIds = SortEnum.getSortIds();
            //sort值不在可选范围内
            return sortIds.contains(sort);
        }



        /**
         * 将所有枚举key组成set
         *
         * @return
         */
        static Set<String> allOrderKeySet() {
            Set<String> orderKeySet = new HashSet<>();
            TestOverviewDto.TestOverviewEnum[] orderEnum = TestOverviewDto.TestOverviewEnum.values();
            for (TestOverviewDto.TestOverviewEnum testOverviewEnum : orderEnum) {
                orderKeySet.add(testOverviewEnum.getOrderKey());
            }
            return orderKeySet;
        }

        /**
         * 根据orderKey获取对应的orderValue
         *
         * @param orderKey
         * @return
         */
        static String getOrderValue(String orderKey) {
            TestOverviewDto.TestOverviewEnum[] enums = TestOverviewDto.TestOverviewEnum.values();
            for (TestOverviewDto.TestOverviewEnum orderEnum : enums) {
                if (orderEnum.getOrderKey().equals(orderKey)) {
                    return orderEnum.getOrderValue();
                }
            }
            return "";
        }
    }


    /**
     * 检查传入参数是否可用
     */
    public Pair<Boolean, Map<String, List<String>>> checkListAvailable() throws UedmException
    {
        boolean state = true;
        Map<String,List<String>> map = new HashMap<>();

        //检查testStatus
        List<String> allIds = BattTestStatusEnums.getAllId();
        log.info("allIds.size():{}",allIds.size());

        if (!CollectionUtils.isEmpty(this.testStatus)) {
            for (String str : testStatus) {
                if (!allIds.contains(str)) {
                    state = false;
                    log.error("The parameter testStatus is not in the optional range.");
                    map.put(STATUS, allIds);
                }
            }
        }
        log.info("TestOverviewDto -> checkListAvailable testStatus:{}",testStatus);

        //检查backupPowerStatus
        List<String> backupPowerStatusAllIds = BackupPowerStateEnum.getAllackupPowerStateIds();
        if (!CollectionUtils.isEmpty(this.backupPowerStatus)) {
            for (String str : backupPowerStatus) {
                if (!backupPowerStatusAllIds.contains(str)) {
                    state = false;
                    log.error("The parameter backupPowerStatus is not in the optional range.");
                    map.put(STATUS, backupPowerStatusAllIds);
                }
            }
        }
        log.info("TestOverviewDto -> checkListAvailable backupPowerStatus:{}",backupPowerStatus);

        //检查testTypes
        state = checkTestType(state, map);
        //检查testResult
        state = checkTestResult(state, map);
        //检查acInputStatus
        state = checkAcInputStatus(state, map);
        return Pair.of(state,map);
    }

    private boolean checkAcInputStatus(boolean state, Map<String, List<String>> map)
    {
        List<String> acInputStatusIds = AcInputStatusEnums.getAllId();
        if (!CollectionUtils.isEmpty(this.acInputStatus)) {
            for (String str : acInputStatus) {
                if (!acInputStatusIds.contains(str)) {
                    state = false;
                    log.error("The parameter acInputStatus is not in the optional range.");
                    map.put(AC_INPUT_STATUS, acInputStatusIds);
                }
            }
        }
        log.info("TestOverviewDto -> checkListAvailable acInputStatus:{}",acInputStatus);
        return state;
    }

    private boolean checkTestResult(boolean state, Map<String, List<String>> map)
    {
        List<String> testResultIds = BatteryTestResultEnums.getAllIds();
        if (!CollectionUtils.isEmpty(this.testResult)) {
            for (String str : testResult) {
                if (!testResultIds.contains(str)) {
                    state = false;
                    log.error("The parameter testResult is not in the optional range.");
                    map.put(RESULT, testResultIds);
                }
            }
        }
        log.info("TestOverviewDto -> checkListAvailable testResult:{}",testResult);
        return state;
    }

    private boolean checkTestType(boolean state, Map<String, List<String>> map)
    {
        List<String> testTypeIds = BattTestTypeEnums.getAllIds();
        if (!CollectionUtils.isEmpty(this.testTypes)) {
            for (String str : testTypes) {
                if (!testTypeIds.contains(str)) {
                    state = false;
                    log.error("The parameter testTypes is not in the optional range.");
                    map.put(TYPE, testTypeIds);
                }
            }
        }
        log.info("TestOverviewDto -> checkListAvailable testTypes:{}",testTypes);
        return state;
    }

}
