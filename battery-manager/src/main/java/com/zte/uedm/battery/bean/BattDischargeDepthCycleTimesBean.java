package com.zte.uedm.battery.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 电池放电深度循环次数配置表 batt_discharge_depth_cycle_times
 */
@Setter
@Getter
@ToString
@TableName("batt_discharge_depth_cycle_times")
public class BattDischargeDepthCycleTimesBean {

    /**
     * 电池ID
     */
    private String battId;

    /**
     * 放电深度和循环次数的对应关系
     */
    private String dischargeDepthCycleTimes;

    /**
     * 理论寿命（月）
     */
    private Integer theoreticalLife;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date gmtModified;
}
