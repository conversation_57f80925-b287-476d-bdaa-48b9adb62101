package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter
@Setter
@ToString
public class DevicePeakCacheInfoBean
{

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型：BCUA，CSU5
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 父ID
     */
    private String parentId;

    /**
     * 对应site ID
     */
    private String siteId;

    /**
     * 对应site名称
     */
    private String siteName;

    /**
     * 设备位置
     */
    private String position;

    /**
     * 累计电费收益
     */
    private String electricBenefit;

    /**
     * 累计充电量
     */
    private String totalCharge;

    /**
     * 累计放电量
     */
    private String totalDischarge;

    /**
     * 电池容量
     */
    private String battCapacity;

    /**
     * 执行状态(已不会定时更新，慎用)
     */
    private String execStatus;

    /**
     * 运行状态
     */
    private String runningStatus;

    /**
     * 运行状态详情
     */
    private Map<String, String> runningStatusMap;

    /**
     * 支持错峰
     */
    private Boolean enablePeak;

    /**
     * 当前策略(已不会定时更新，慎用)
     */
    private String currStrategy;

    /**
     * 策略生效日期
     */
    private String effectiveDate;

    /**
     * 策略失效日期
     */
    private String expirationDate;

    /**
     * 电池充放电状态(careful)
     */
    private String battStatus;

    /**
     * moduleId  用来后续判断六代电源的是否支持错峰
     */
    private String moduleId;
}

