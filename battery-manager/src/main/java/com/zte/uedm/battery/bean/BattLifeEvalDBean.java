package com.zte.uedm.battery.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 电池寿命评估记录天表 batt_life_eval_d
 */
@Setter
@Getter
@ToString
@TableName("batt_life_eval_d")
public class BattLifeEvalDBean {

    /**
     * 电池id
     */
    private String id;

    /**
     * 评估时间
     */
    private String evalTime;

    /**
     * 电池名称（中英文）
     */
    private String name;

    /**
     * 路径id串
     */
    private String pathIds;

    /**
     * 路径名称串
     */
    private String pathNames;

    /**
     * 电池类型
     */
    private String battType;

    /**
     * 寿命（月）
     */
    private Integer life;

    /**
     * 无法评估原因
     */
    private String unknownReason;

    /**
     * 额定循环次数
     */
    private Integer ratedCycleTimes;

    /**
     * 累计循环次数
     */
    private Integer accumCycleTimes;

    /**
     * 描述
     */
    private String description;

    /**
     * 累计放电容量
     */
    private Double accumDischargeCap;

    /**
     * 额定容量
     */
    private Double ratedCap;

    /**
     * 累计放电次数
     */
    private Integer accumDischargeTimes;

    /**
     * 平均放电深度
     */
    private Double avgDischargeDepth;

    /**
     * 循环次数
     */
    private Integer cycleTimes;

    /**
     * 运行总天数
     */
    private Integer operatingDays;

    /**
     * 运行总天数来源
     */
    private String operatingDaysFrom;
    /**
     * 启用日期
     */
    private Date startDate;
    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date gmtModified;


    /**
     * 评估来源
     */
    private String source;

    /**
     * 剩余循环次数
     */
    private Integer leftCycleTimes;

    /**
     * 健康度
     */
    private Float soh;

    private Integer ratedUsefulLife;
}
