package com.zte.uedm.battery.controller.batterytest;

import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.battery.controller.batterytest.vo.BattTestOverviewDetailVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 电池放电测试概览-操作记录日志管理类
 */
@Slf4j
public class BatteryTestOverviewOperationMgr {

    private static final String BATTERY_DISCHARGE_TEST_MODULE_ZH = "电池放电测试";
    private static final String BATTERY_DISCHARGE_TEST_MODULE_EN = "Battery Test";
    private static final String BATTERY_DISCHARGE_TEST_OVERVIEW_DIMENSIONS_CHANGE_ZH = "电池放电测试概览维度变更：";
    private static final String BATTERY_DISCHARGE_TEST_OVERVIEW_DIMENSIONS_CHANGE_EN = "Battery Discharge Test Overview Dimensions Change：";
    private static final String DIMENSION_NAME_ZH = "维度名称：";
    private static final String DIMENSION_NAME_EN = "Dimension Name：";
    private static final String ORIGINAL_SEQUENCE_ZH = "原顺序：";
    private static final String ORIGINAL_SEQUENCE_EN = "Original Sequence：";
    private static final String NEW_SEQUENCE_ZH = "新顺序：";
    private static final String NEW_SEQUENCE_EN = "New Sequence：";
    private static final String ORIGINAL_SELECT_ZH = "原来是否选择：";
    private static final String ORIGINAL_SELECT_EN = "Original Select：";
    private static final String NEW_SELECT_ZH = "现在是否选择：";
    private static final String NEW_SELECT_EN = "New Select：";
    private static final String BATTERY_TEST_CONFIG_OVERVIEW_UPDATE_DIM_ZH = "电池放电测试概览列定制更新";
    private static final String BATTERY_TEST_CONFIG_OVERVIEW_UPDATE_DIM_EN = "Battery test config overview update";

    /**
     * 操作记录初始化
     */
    public static void initBatTestConfigOperBean(ServiceBaseInfoBean baseInfoBean) {
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(baseInfoBean.getUserName(), baseInfoBean.getRemoteHost(), connectMode,
                BATTERY_DISCHARGE_TEST_MODULE_ZH, BATTERY_DISCHARGE_TEST_MODULE_EN, OperlogBean.LogRank.operlog_rank_important,
                BATTERY_TEST_CONFIG_OVERVIEW_UPDATE_DIM_ZH, BATTERY_TEST_CONFIG_OVERVIEW_UPDATE_DIM_EN);
        baseInfoBean.initOperlogBean(operlogBean);
    }

    /**
     * 记录操作成功日志
     */
    public static void refreshBattTestConfigOperSuccessDetail(ServiceBaseInfoBean serviceBean,
                                                              List<BattTestOverviewDetailVo> detailVoList) throws UedmException {
        if (Objects.nonNull(serviceBean) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailVoList)) {
            Optional.ofNullable(serviceBean.getOperlogBean()).ifPresent(item -> {
                StringBuilder detailZh = new StringBuilder();
                StringBuilder detailEn = new StringBuilder();
                detailZh.append(BATTERY_DISCHARGE_TEST_OVERVIEW_DIMENSIONS_CHANGE_ZH);
                detailEn.append(BATTERY_DISCHARGE_TEST_OVERVIEW_DIMENSIONS_CHANGE_EN);
                for (BattTestOverviewDetailVo detailVo : detailVoList) {
                    detailZh.append(DIMENSION_NAME_ZH).append(detailVo.getNameZh()).append(",")
                            .append(ORIGINAL_SEQUENCE_ZH).append(detailVo.getOriginalSequence()).append(",")
                            .append(NEW_SEQUENCE_ZH).append(detailVo.getSequence()).append(",")
                            .append(ORIGINAL_SELECT_ZH).append(detailVo.isOriginalEnabled()).append(",")
                            .append(NEW_SELECT_ZH).append(detailVo.isEnable()).append(".");
                    detailEn.append(DIMENSION_NAME_EN).append(detailVo.getNameEn()).append(",")
                            .append(ORIGINAL_SEQUENCE_EN).append(detailVo.getOriginalSequence()).append(",")
                            .append(NEW_SEQUENCE_EN).append(detailVo.getSequence()).append(",")
                            .append(ORIGINAL_SELECT_EN).append(detailVo.isOriginalEnabled()).append(",")
                            .append(NEW_SELECT_EN).append(detailVo.isEnable()).append(".");
                }
                item.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            });
        }
    }

    /**
     * 记录操作失败日志
     */
    public static void refreshStatusEditOperFailDetail(ServiceBaseInfoBean serviceBean, UedmException e) {
        if (Objects.nonNull(serviceBean) && Objects.nonNull(e)) {
            Optional.ofNullable(serviceBean.getOperlogBean()).ifPresent(item -> {
                StringBuilder detailZh = new StringBuilder();
                StringBuilder detailEn = new StringBuilder();
                detailZh.append(e.getMessage()).append("\n");
                detailEn.append(e.getMessage()).append("\n");
                item.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            });
        }
    }

}
