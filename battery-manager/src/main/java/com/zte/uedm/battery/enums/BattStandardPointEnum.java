package com.zte.uedm.battery.enums;

import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.*;

public enum BattStandardPointEnum {
    /* Started by AICoder, pid:qb3d7w3ff4mbd2614131099a10f5551d5f0879e7 */
    // 充放电状态
    CHARGE_DISCHARGE_STATUS(BATTERY_SMPID_CHARGE_DISCHARGE_STATUS),
    // 额定容量
    RATED_CAPACITY(BATTERY_SMPID_RATED_CAPACITY),
    // SOH
    HEALTH(BATTERY_SMPID_HEALTH),
    // 当前容量比率 SOC
    PRST_SOC(BATTERY_SMPID_PRST_SOC),
    // 当前容量
    PRST_CAP(BATTERY_SMPID_PRST_CAP),
    // 电流
    CURR(BATTERY_SMPID_CURR),
    // 电压
    VOLT(BATTERY_SMPID_VOLTAGE),
    // 温度
    TEMP(GlobalConstants.BATTERY_TEMPERATURE),
    // 电池类型
    BATT_SMPID_TYPE(BATTERY_SMPID_TYPE),
    BATTPACK_SMPID_CAPACITY_RATE(BATTERYSET_SMPID_CAPACITY_RATE),

    BATTERY_SMPID_DISCHARGE_TIME_REMAINING(SMPID_DISCHARGE_TIME_REMAINING);

    private String value;
    public String getValue() {
        return this.value;
    }

    private BattStandardPointEnum(String value) {
        this.value = value;

    }




    /* Ended by AICoder, pid:qb3d7w3ff4mbd2614131099a10f5551d5f0879e7 */
}
