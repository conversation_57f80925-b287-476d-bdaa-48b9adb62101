package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 设备模板策略PO，与数据库一一对应
 */

@Getter
@Setter
@ToString
public class TemplateStrategyPo
{
    /**
     * 主键
     */
    private String id;
    /**
     * 策略模板名称
     */
    private String name;
    /**
     * BCUA，CSU5
     */
    private String deviceType;
    /**
     * web, excel
     */
    private String source;
    /**
     * 季节策略ID（web配置时候专用），Excel导入的时候为null
     */
    private String seasonStrategyId;
    /**
     * 文件ID（Excel导入的时候专用），web配置的时候为null
     */
    private String fileId;
    /**
     * 0：日模式 1：周模式 2：月模式
     */
    private String mode;

    /**
     * 是否需要周末错峰
     */
    private Boolean weekendFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本
     */
    private Double version;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 创建用户
     */
    private String userCreate;
    /**
     * 更新时间
     */
    private String gmtModified;

    /**
     * 修改用户
     */
    private String userModified;

}
