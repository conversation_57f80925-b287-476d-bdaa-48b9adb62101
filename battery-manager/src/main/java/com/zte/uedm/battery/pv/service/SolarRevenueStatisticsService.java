package com.zte.uedm.battery.pv.service;

import com.zte.uedm.battery.pv.dto.SolarRevenueScopeStatisticsDto;
import com.zte.uedm.common.exception.UedmException;

import java.text.ParseException;
import java.util.List;

public interface SolarRevenueStatisticsService {

    /**
     * 计算太阳能收益
     * @param solarRevenueScopeStatisticsDto
     * @throws UedmException
     */
    void solarRevenueStatistics(SolarRevenueScopeStatisticsDto solarRevenueScopeStatisticsDto) throws UedmException, InterruptedException, ParseException, com.zte.uedm.basis.exception.UedmException;

    /**
     * 太阳能收益统计定时任务
     */
    void solarRevenueStatisticsJob();

    /**
     * 手动接口
     *
     * @param solarRevenueScopeStatisticsDto
     * @throws UedmException
     * @throws ParseException
     * @throws InterruptedException
     */
    void solarRevenueStatisticsManual(SolarRevenueScopeStatisticsDto solarRevenueScopeStatisticsDto) throws UedmException, ParseException, InterruptedException;


    /**
     * 执行矫正任务
     */
    void exeCorrectiveTasks();

    /**
     * 执行kafka补采汇聚任务
     * @param pvId
     * @param dates
     */
    void exeSolarRevenueKafkaTask(String pvId, List<String> dates);

    /**
     * 同步更新旧的太阳能收益数据：策略ID，计费类型，档位
     */
    void synOldDataOfRevenue();
}