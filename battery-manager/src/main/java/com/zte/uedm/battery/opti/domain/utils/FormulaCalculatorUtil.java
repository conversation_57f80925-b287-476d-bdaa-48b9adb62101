package com.zte.uedm.battery.opti.domain.utils;

import com.zte.uedm.battery.opti.domain.utils.function.bean.FormulaBean;
import com.zte.uedm.battery.opti.domain.utils.function.bean.FuncParameterBean;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.uedm.battery.opti.domain.utils.exception.FormulaExceptionEnum.FORMULA_ERROR;
import static com.zte.uedm.battery.opti.domain.utils.exception.FormulaExceptionEnum.VALUE_ERROR;

/**
 * 逻辑计算
 */
@Slf4j
public class FormulaCalculatorUtil
{
    private static final String NULL_STRING = "null";
    public static boolean calculateExpression(FormulaBean formulaBean, Map<String, String> parameterValueMap)
            throws UedmException
    {
        log.debug("CalculateExpression : FormulaBean is {}, parameterValueMap is {}", formulaBean, parameterValueMap);
        //公式校验
        if(checkFormula(formulaBean))
        {
            throw new UedmException(FORMULA_ERROR.getCode(), FORMULA_ERROR.getDesc());
        }

        //对公式进行变形和计算参数
        Map<Integer, Object> resultValueMap = transferValue(formulaBean.getParameters(), parameterValueMap);
        log.debug("CalculateExpression : function {},  parameters {} --- resultValueMap is {}", formulaBean.getFunc(), formulaBean.getParameters(), resultValueMap);

        boolean result = false;
        //公式计算
        try
        {
            result = FelUtil.compute(formulaBean.getFunc(), resultValueMap);
            log.debug("CalculateExpression : function {} result is {}", formulaBean.getFunc(), result);
        }
        catch (Exception e)
        {
            log.warn("[{}]calculateExpression is error", formulaBean.getFunc());
        }
        return result;
    }
    /**
     * 校验公式内容
     * @param formulaBean
     * @return
     */
    private static boolean checkFormula(FormulaBean formulaBean)
    {
        boolean result = true;
        if(null != formulaBean)
        {
            List<String> checkResult = formulaBean.checkInvalid();
            if(CollectionUtils.isNotEmpty(checkResult))
            {
                log.warn("calculateExpression : formulaBean is invalid! formulaBean is {}, error parameter is  {}", formulaBean, checkResult);
            }
            else
            {
                result = false;
            }
        }
        return result;
    }

    private static Map<Integer, Object> transferValue(List<FuncParameterBean> parameters, Map<String, String> parameterValueMap)
            throws UedmException
    {
        Map<Integer, Object> valueMap = new HashMap<>();
        int nullValueSize = 0;
        for(FuncParameterBean parameterBean : parameters)
        {
            String value = parameterValueMap.get(parameterBean.getId());
            Integer index = parameterBean.getIndex();
            //为空特殊处理
            if(StringUtils.isBlank(value) || NULL_STRING.equals(value))
            {
                valueMap.put(index, null);
                nullValueSize++;
                if(nullValueSize == parameters.size())
                {
                    return new HashMap<>();
                }
                continue;
            }
            //不为空，必须为数字
            if(!NumberUtils.isNumber(value))
            {
                throw new UedmException(VALUE_ERROR.getCode(), VALUE_ERROR.getDesc());
            }
            try
            {
                valueMap.put(index, Double.valueOf(value));
            }
            catch (Exception e)
            {
                log.warn("TransferValue occur error, value is {}", value);
            }
        }
        return valueMap;
    }
}
