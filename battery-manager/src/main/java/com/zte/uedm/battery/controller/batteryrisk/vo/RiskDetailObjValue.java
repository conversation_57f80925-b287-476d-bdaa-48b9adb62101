package com.zte.uedm.battery.controller.batteryrisk.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RiskDetailObjValue {
    /**
     * 风险名称-国际化
     */
    private String riskName;
    /**
     *  原因-国际化
     */
    private String riskCause;
    /**
     * 建议-国际化
     */
    private String riskSuggestion;

    /**
     * 风险等级
     */
    private String riskLevel;

}
