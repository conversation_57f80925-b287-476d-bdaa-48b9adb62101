package com.zte.uedm.battery.bean.peak;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@ApiModel(description = "错峰任务详情")
public class PeakShiftTaskDetailQueryDTO
{
    @ApiModelProperty(value = "详情标识")
    private String detailId;
    @ApiModelProperty(value = "设备标识")
    private String deviceId;
    @ApiModelProperty(value = "状态")
    private String status;
}
