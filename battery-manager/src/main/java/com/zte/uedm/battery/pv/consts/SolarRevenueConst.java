package com.zte.uedm.battery.pv.consts;

public class SolarRevenueConst {
    public static final String CFG_CENTER_CURRENCY_KEY = "configuration.show.currency.unit";

    public static final String CFG_CENTER_SOLAR_REVENUE_STATISTICS_SITE_GROUP_COUNT = "configuration.solar.revenue.statistics.site.group.count";

    /*
    小数点后保留的位数
     */
    public static final int DEF_SCALE = 2;
    public final static String MOC_PV = "pv";


    public static final int MAX_PV_SIZE = 300;

    public static final int SCALE = 2;
    public static final int SCALE_SIX = 6;

    public static final int SCALE_FOUR = 4;

    public static final String ON_LINE = "onLine";
    public static final String ALARM_STATE_CODE = "alarmStateCode";
    public static final String GENERATION_POWER = "generationPower";
    public static final String INSTALLATION_CAPACITY = "installationCapacity";
    public static final String EFFICIENCY = "efficiency";
    public static final String YEAR = "year";
    public static final String MONTH = "month";
    public static final String DAY = "day";
    public static final String ALL = "all";
    public static final String EMPTY_STR = "";
    public static final String DESC = "desc";
    public static final String NORMAL = "正常";
    public static final String ALARM = "告警";
    public static final String ZERO_STR = "0";

    public static final String BEGIN_TIME = "beginTime";
    public static final String END_TIME = "endTime";
    public static final String SPACE = " ";
    public static final String LAST_MONTH_SUFFIX = "-12";
    public static final String FIRST_HOUR = "01";
    public static final String LAST_HOUR = "24";
    public static final String LAST_HOUR_ONE = "24:00";
    public static final int SECOND_LAST_HOUR = 23;
    public static final int YEAR_MONTH_DAY_HOUR_STR_LEN = 13;
    public static final String ZERO_ONE_SUFFIX = "-01";

    /**
     * 整数0
     */
    public static final Integer INTEGER_0 = 0;

    /**
     * 整数1
     */
    public static final Integer INTEGER_1 = 1;

    /**
     * 24小时
     */
    public static final Integer INTEGER_HOURS_COUNT = 24;

    /**
     * 分钟数
     */
    public static final Integer HOUR_MINUTE_COUNT = 60;

    /**
     * PMA数据每次查询条数
     */
    public static final Integer PMA_HISTORY_QUERY_COUNT = 10000;

    /**
     * 0分0秒
     */
    public static final String MINUTE_ZERO_SECOND_ZERO = ":00:00";

    /**
     * 整数1
     */
    public static final Integer THREE_DECIMAL_PLACES = 3;

    //太阳能监控导出太阳能供电管理-中文
    public static final String SOLAR_MONITOR_EXPORT_ZH="太阳能监控-导出";
    //太阳能供电管理导出-英文
    public static final String SOLAR_MONITOR_EXPORT_EN="Solar Monitor-Export";
    //太阳能供电管理模块-中文
    public static final String SOLAR_REVENUE_OVERVIEW_ZH="太阳能供电管理";
    //太阳能供电管理模块-英文
    public static final String SOLAR_REVENUE_OVERVIEW_EN="Solar";
    //太阳能供电管理导出-中文
    public static final String SOLAR_REVENUE_OVERVIEW_EXPORT_ZH="太阳能收益-导出";
    //太阳能供电管理导出-英文
    public static final String SOLAR_REVENUE_OVERVIEW_EXPORT_EN="Solar Revenue-Export";
    public static final String SOLAR_SHEET_NAME = "{\"en_US\":\"Detail\",\"zh_CN\":\"详情\"}";
    public final static String MASTER_DIRECTORY = "/home/<USER>/local/";

    public static final String ON_LINE_STATUS = "{\"en_US\":\"online\",\"zh_CN\":\"在线\"}";
    public static final String OFF_LINE_STATUS = "{\"en_US\":\"offline\",\"zh_CN\":\"离线\"}";

    public static final String NORMAL_STATUS = "{\"en_US\":\"Normal\",\"zh_CN\":\"正常\"}";
    public static final String ALARM_STATUS = "{\"en_US\":\"Alarm\",\"zh_CN\":\"告警\"}";
    public static final String ONE_STR = "1";
    public static final Integer DEFAULT_CONVERGE_CYCLE = 5;

    public static final String MANUAL_SUPPLEMENTARY_COLLECTION = "{\"en_US\":\"Manually Recollect Solar Energy\",\"zh_CN\":\"手动补采太阳能电量\"}";

    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_SITE_ID_EMPTY = "{\"en_US\":\"SiteIdList is null.\",\"zh_CN\":\"站点列表为空\"}";
    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_RECOLLECTING = "{\"en_US\":\"The site is being recollected.\",\"zh_CN\":\"该站点正在补采中\"}";
    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_NO_ABILITY = "{\"en_US\":\"The site has no recollection capability.\",\"zh_CN\":\"该站点无补采能力\"}";
    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_NO_DEVICES = "{\"en_US\":\"The site recollection device is not obtained.\",\"zh_CN\":\"未获取到站点补采设备\"}";
    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_GET_CURRENT_SITE_NUM = "{\"en_US\":\"System error. Please try again later!\",\"zh_CN\":\"系统异常，请稍后重试！\"}";
    public static final String MANUAL_SUPPLEMENTARY_EXCEPTION_UPPER_LIMIT = "{\"en_US\":\"The number of sites recollected has reached the upper limit. Please try again later!\",\"zh_CN\":\"补采站点数量已达上限，请稍后重试！\"}";
    public static final String MANUAL_SUPPLEMENTARY_SITE = "{\"en_US\":\"Recollection Sites：\",\"zh_CN\":\"补采站点：\"}";

    public static final String LOG_NAME_SOLAR_REVENUE_SCHEDULED_TASKS = "{\"en_US\":\"Solar revenue aggregation execution exception\",\"zh_CN\":\"太阳能收益汇聚执行异常\"}";

    public static final String LOG_NAME_SOLAR_REVENUE_SCHEDULED_JOB_TASKS = "{\"en_US\":\"Solar revenue statistics aggregation timing task failed to obtain lock\",\"zh_CN\":\"太阳能收益统计汇聚定时任务获取锁失败\"}";
    public static final String LOG_NAME_SOLAR_REVENUE_SCHEDULED_KAFKA_TASKS = "{\"en_US\":\"Solar revenue statistics aggregation Kafka task failed to obtain lock\",\"zh_CN\":\"太阳能收益统计汇聚kafka任务获取锁失败\"}";
    public static final String LOG_NAME_SOLAR_REVENUE_SCHEDULED_MANUAL_TASKS = "{\"en_US\":\"Solar revenue statistics aggregation manual compensation task failed to obtain lock\",\"zh_CN\":\"太阳能收益统计汇聚手动补偿任务获取锁失败\"}";

    public static final String NUMBER_OF_SINGLE_CORRECTION_SITES_OVERSIZE = "{\"en_US\":\"Cannot be corrected! The number of sites in the group exceeds %s. Please select again.\",\"zh_CN\":\"不可矫正！分组内站点数超“%s”，请重新选择。\"}";

    public static final String NUMBER_OF_SINGLE_CORRECTION_SITES_EVERY_ADY_OVERSIZE = "{\"en_US\":\"Cannot be corrected! The number of corrected sites currently exceeds %s. Please select again.\",\"zh_CN\":\"不可矫正！当前矫正的站点数超“%s”，请重新选择。\"}";

    public static final String NUMBER_OF_CONCURRENT_TASKS_OVERSIZE = "{\"en_US\":\"Cannot be corrected! The number of tasks exceeds %s. Please try again later.\",\"zh_CN\":\"不可矫正！任务数超“%s”，请稍后重试。\"}";

    public static final String NUMBER_FORMAT_EXCEPTION = "{\"en_US\":\"get configuration center data fail.\",\"zh_CN\":\"获取配置中心数据失败。\"}";

    public static final String GRID_STRATEGY_EXCEPTION = "{\"en_US\":\"Cannot be corrected! The selected electricity pricing strategy is invalid, please select again.\",\"zh_CN\":\"不可矫正！选择的市电电价策略无效，请重新选择。\"}";

    public static final String SOLAR_STRATEGY_EXCEPTION = "{\"en_US\":\"Cannot be corrected! The selected solar power pricing strategy is invalid, please select again.\",\"zh_CN\":\"不可矫正！选择的太阳能电价策略无效，请重新选择。\"}";

    public static final String ALL_STRATEGY_EXCEPTION = "{\"en_US\":\"Cannot be corrected! The selected all pricing strategy is invalid, please select again.\",\"zh_CN\":\"不可矫正！选择的市电电价策略和太阳能电价策略无效，请重新选择。\"}";

    public static final String STRATEGY_TOU_TIME_EXCEPTION = "{\"en_US\":\"Cannot be corrected! The selected strategy period is invalid, please select again.\",\"zh_CN\":\"不可矫正！选择的策略时段无效，请重新选择。\"}";

    public static final String STRATEGY_COMPARE_EXCEPTION = "{\"en_US\":\"Cannot be corrected! The selected grid electricity pricing strategy does not match the solar electricity pricing strategy. Please confirm if you want to proceed with the correction?\",\"zh_CN\":\"不可矫正！选择的市电电价策略和太阳能电价策略不匹配，请确认是否继续进行矫正？\"}";




    /**
     * 计费类型
     */
    public static final String FLAT_T = "Flat";
    public static final String TIERED_T = "Tiered";
    public static final String CRITICAL_PEAK = "Critical Peak";
    public static final String ON_PEAK = "On-Peak";
    public static final String OFF_PEAK = "Off-Peak";
    public static final String SUPPER_OFF_PEAK = "Super Off-Peak";
    public static final String COMMA = ",";
    public static final int INT_19 = 19;
    public static final int INT_29 = 29;
    public static final int INT_13 = 13;
    public static final int INT_23 = 23;
    public static final int INT_0 = 0;
    public static final int INT_7 = 7;
    public static final int INT_4 = 4;


    //太阳能供电管理矫正-中文
    public static final String SOLAR_REVENUE_OVERVIEW_CORRECTION_ZH="太阳能收益-矫正";
    //太阳能供电管理矫正-英文
    public static final String SOLAR_REVENUE_OVERVIEW_CORRECTION_EN="Solar Revenue-Correction";

    public final static String DETAIL = "detail";

    public final static String DETAIL_NAME = "{\"zh_CN\":\"详情\",\"en_US\":\"Details\"}";

    public final static String DETAIL_CONTENT_KEY = "rateTypeRevenueDetail";

    public final static int MAX_SHEET_ROW = 60000;

    /**
     * 电价矫正记录
     */
    public final static String  CREATE_DATE = "{\"zh_CN\":\"创建时间\",\"en_US\":\"Create Time\"}";
    public final static String  TIME_FRAME = "{\"zh_CN\":\"时间范围\",\"en_US\":\"Start/End Time\"}";
    public final static String  TASK_STATUS = "{\"zh_CN\":\"任务状态\",\"en_US\":\"Task Status\"}";
    public final static String  SITE_COUNT = "{\"zh_CN\":\"站点数量\",\"en_US\":\"Number of Sites\"}";
    public final static String  GRID_ELECTRICITY_PRICE_STRATEGY = "{\"zh_CN\":\"电价策略(市电)\",\"en_US\":\"Electricity Price（Grid）\"}";
    public final static String  SOLAR_ELECTRICITY_PRICE_STRATEGY = "{\"zh_CN\":\"电价策略(太阳能)\",\"en_US\":\"Electricity Price（Solar）\"}";
    public final static String  OPERATOR = "{\"zh_CN\":\"操作人\",\"en_US\":\"Operator\"}";


    /**
     * 电价矫正详情记录
     */
    public final static String  SITE = "{\"zh_CN\":\"名称\",\"en_US\":\"Name\"}";
    public final static String  POSITION = "{\"zh_CN\":\"位置\",\"en_US\":\"Location\"}";
    public final static String  SOLAR_REVENUE = "{\"zh_CN\":\"太阳能\",\"en_US\":\"Solar\"}";
    public final static String  GRID_ELECTRICITY_COSTS = "{\"zh_CN\":\"市电电费\",\"en_US\":\"Grid\"}";
    public final static String SAVE_ELECTRICITY_COSTS = "{\"zh_CN\":\"节省电费\",\"en_US\":\"Savings\"}";
    public final static String OLD = "{\"zh_CN\":\"旧\",\"en_US\":\"Before\"}";
    public final static String NEW = "{\"zh_CN\":\"新\",\"en_US\":\"After\"}";

    public final static String ZH_CN = "zh_CN";
    public final static String EN_US = "en_US";

    public static final String NUMBER_REGEX = "\\d+";
    public static final int FILE_MAX_LENGTH = 65;

}
