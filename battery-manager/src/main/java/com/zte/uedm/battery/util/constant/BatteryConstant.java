package com.zte.uedm.battery.util.constant;

import com.zte.uedm.battery.bean.BatteryUseLifeBeanVO;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.service.config.optional.MocOptional;

import java.util.*;

public class BatteryConstant
{
    public static final String BATTERY_MOC = MocOptional.BATTERY.getId();
    public static final String DC_MOC = MocOptional.SP.getId();
    public static final String SP_MOC = MocOptional.SP.getId();

    public static final String TIME = " 00:00:00";
    public static final String DATA_LIST = "dateList";
    public static final String BATTERY_OID_EX = "mo-batts.batt";

    public static final String GLOBAL_BATTERYCFG_STANDBYTIME = "global_batteryCfg_standbyTime";

    public static final String PREFIX_MO_SITE = "Site";

    public static final String SP_NORMAL = "normal";
    public static final String SP_SHORTAGE = "shortage";
    public static final String SP_UNKNOWN = "unknown";
    public static final String SP_COUNT_NUM = "num";
    public static final String SP_EVALUATERESULT = "evaluateresult";

    public static final String BATTERY_STANDBY_TYPE = "0001";
    public static final Long BATTERY_LLVD1_ALARM = 50001004L;

    public static final double BATTERY_YU_VALUE = 4;
    public static final String SP_RATED_CAPACITY = "rated_capacity";

    public static final String SP_RESTYPE = "sp";

    /**
     * PV
     */
    public final static String HISTORY_DATA_TYPE_COLLECT = "collect";

    public final static String PV_TYPE_PV= "pv";

    public final static String DEFAULT_VALUE = "0.00";

    public final static String DEFAULT_STRING_FORMAT = "%.2f";

    public final static String DEFAULT_DOUBLE_VALUE_STR =  "0.00";

    public final static Double DEFAULT_DOUBLE_VALUE =  0.00;

    public final static Double DEFAULT_DOUBLE_VALUE_ONE =  0.0;

    public final static String PV_DEFAULT_VALUE = "95";

    public final static Integer MAX_INPUT_SIGLE = 1000;

    public final static String PV_SUPPLY_SCENE = "sc0011";
    /**
     * 阴雨天redis信息
     */
    public final static String PV_INFO = "pv_info";

    public final static String LONGEST_RAIN_DAY = "longest_rain_day";
    /**
     * PV告警描述
     */
    public final static String INSUFFICIENT_POWER_SUPPLY = "1";

    /**
     * PV告警状态
     */
    public final static String ALARM_STATE_NOT_END = "0";

    public final static String POWER_SUPPLY_NORMAL = "2";
    /**
     *
     * 错峰用电策略status
     *
     */
    public final static int STATUS_FICTITIOUS = 0;

    public final static int STATUS_START_UP = 1;

    public final static int STATUS_DISABLE = 2;
    /**
     *
     *错峰用电策略操作状态码
     *
     */
    public final static String OPERATE_START_UP = "1";

    public final static String OPERATE_DISABLE = "2";

    public final static String LEAD_ACID = "0";

    public final static String LITHIUM_IRON = "1";

    /**
     * 电池使用寿命（月）
     */
    public final static String LEAD_ACID_LIFE = "lead_acid_life";

    public final static String LITHIUM_IRON_LIFE = "lithium_iron_life";

    /**
     * 电池默认理论循环次数（80%
     */
    public final static String LEAD_ACID_CYCLES = "lead_acid_cycles";

    public final static String LITHIUM_IRON_CYCLES = "lithium_iron_cycles";

    public final static String BATTERY = "battery";

    /**
     * BCUA放电终止soc阈值
     */
    public final static String BCUA_SOC = "030016";

    public static final Map<String,BatteryUseLifeBeanVO> leadAcidMap()
    {
        Map<String, BatteryUseLifeBeanVO> map = new HashMap<>();
        map.put("5%",new BatteryUseLifeBeanVO("（0%-5%]",0.0,true,0.05,false,7000));
        map.put("10%",new BatteryUseLifeBeanVO("（5%-10%]",0.05,true,0.1,false,6800));
        map.put("15%",new BatteryUseLifeBeanVO("（10%-15%]",0.1,true,0.15,false,6000));
        map.put("20%",new BatteryUseLifeBeanVO("（15%-20%]",0.15,true,0.2,false,5200));
        map.put("25%",new BatteryUseLifeBeanVO("（20%-25%]",0.2,true,0.25,false,4600));
        map.put("30%",new BatteryUseLifeBeanVO("（25%-30%]",0.25,true,0.3,false,4150));
        map.put("35%",new BatteryUseLifeBeanVO("（30%-35%]",0.3,true,0.35,false,3700));
        map.put("40%",new BatteryUseLifeBeanVO("（35%-40%]",0.35,true,0.4,false,3200));
        map.put("45%",new BatteryUseLifeBeanVO("（40%-45%]",0.4,true,0.45,false,2900));
        map.put("50%",new BatteryUseLifeBeanVO("（45%-50%]",0.45,true,0.5,false,2600));
        map.put("55%",new BatteryUseLifeBeanVO("（50%-55%]",0.5,true,0.55,false,2250));
        map.put("60%",new BatteryUseLifeBeanVO("（55%-60%]",0.55,true,0.60,false,2000));
        map.put("65%",new BatteryUseLifeBeanVO("（60%-65%]",0.6,true,0.65,false,1800));
        map.put("70%",new BatteryUseLifeBeanVO("（65%-70%]",0.65,true,0.7,false,1400));
        map.put("75%",new BatteryUseLifeBeanVO("（70%-75%]",0.7,true,0.75,false,1150));
        map.put("80%",new BatteryUseLifeBeanVO("（75%-80%]",0.75,true,0.80,false,1000));
        map.put("85%",new BatteryUseLifeBeanVO("（80%-85%]",0.8,true,0.85,false,800));
        map.put("90%",new BatteryUseLifeBeanVO("（85%-90%]",0.85,true,0.9,false,650));
        map.put("95%",new BatteryUseLifeBeanVO("（90%-95%]",0.9,true,0.95,false,500));
        map.put("100%",new BatteryUseLifeBeanVO("（95%-100%]",0.95,true,1.0,false,400));
        return map;
    }

    public static final Map<String,BatteryUseLifeBeanVO> lithiumIronMap()
    {
        Map<String, BatteryUseLifeBeanVO> map = new HashMap<>();
        map.put("10%",new BatteryUseLifeBeanVO("（0%-10%]",0.0,true,0.1,false,25000));
        map.put("20%",new BatteryUseLifeBeanVO("（10%-20%]",0.1,true,0.2,false,15000));
        map.put("30%",new BatteryUseLifeBeanVO("（20%-30%]",0.2,true,0.3,false,9000));
        map.put("40%",new BatteryUseLifeBeanVO("（30%-40%]",0.3,true,0.4,false,6500));
        map.put("50%",new BatteryUseLifeBeanVO("（40%-50%]",0.4,true,0.5,false,5350));
        map.put("60%",new BatteryUseLifeBeanVO("（50%-60%]",0.5,true,0.60,false,4600));
        map.put("70%",new BatteryUseLifeBeanVO("（60%-70%]",0.6,true,0.7,false,4000));
        map.put("80%",new BatteryUseLifeBeanVO("（70%-80%]",0.7,true,0.80,false,3500));
        map.put("90%",new BatteryUseLifeBeanVO("（80%-90%]",0.8,true,0.9,false,3000));
        map.put("100%",new BatteryUseLifeBeanVO("（90%-100%]",0.9,true,1.0,false,2600));
        return map;
    }

    /**
     * 电池测试记录数据库字段
     */
    public final static String TEST_START_TIME = "startTime";
    public final static String TEST_END_TIME = "endTime";
    public final static String TEST_TIME = "batt_batt_test_time";
    public final static String TEST_TYPE = "batt_batt_test_type";
    public final static String TEST_INIT_SOC = "batt_batt_test_init_soc";
    public final static String TEST_FINAL_SOC = "batt_batt_test_final_soc";
    public final static String TEST_CHANGED_SOC= "batt_batt_test_changed_soc";
    public final static String TEST_DURATION = "batt_batt_test_duration";
    public final static String TEST_START_CAUSE = "batt_batt_test_start_cause";
    public final static String TEST_STOP_CAUSE = "batt_batt_test_stop_cause";
    public final static String TEST_INIT_VOLT = "batt_batt_test_init_volt";
    public final static String TEST_FINAL_VOLT = "batt_batt_test_final_volt";
    public final static String TEST_AVG_CURR = "batt_batt_test_avg_curr";
    public final static String TEST_TEMP= "batt_batt_test_temp";
    public final static String TEST_INIT_STATUS = "batt_batt_test_init_status";
    public final static String TEST_FINAL_STATUS = "batt_batt_test_final_status";
    public final static String TEST_INIT_POWER = "batt_batt_test_init_power";
    public final static String TEST_FINAL_POWER = "batt_batt_test_final_power";
    public final static String TEST_RESULT = "batt_batt_test_result";

    /**
     * 电池测试记录bean字段
     */
    public final static String TEST_START_TIME_BEAN = "testStartTime";
    public final static String TEST_END_TIME_BEAN = "testEndTime";
    public final static String TEST_TIME_BEAN = "testTime";
    public final static String TEST_TYPE_BEAN = "testType";
    public final static String TEST_INIT_SOC_BEAN = "testInitSoc";
    public final static String TEST_FINAL_SOC_BEAN = "testFinalSoc";
    public final static String TEST_CHANGED_SOC_BEAN = "testChangedSoc";
    public final static String TEST_DURATION_BEAN = "testDuration";
    public final static String TEST_START_CAUSE_BEAN = "testStartCause";
    public final static String TEST_STOP_CAUSE_BEAN = "testStopCause";
    public final static String TEST_INIT_VOLT_BEAN = "testInitVolt";
    public final static String TEST_FINAL_VOLT_BEAN = "testFinalVolt";
    public final static String TEST_AVG_CURR_BEAN = "testAvgCurr";
    public final static String TEST_TEMP_BEAN = "testTemp";
    public final static String TEST_INIT_STATUS_BEAN = "testInitStatus";
    public final static String TEST_FINAL_STATUS_BEAN = "testFinalStatus";
    public final static String TEST_INIT_POWER_BEAN = "testInitPower";
    public final static String TEST_FINAL_POWER_BEAN = "testFinalPower";
    public final static String TEST_RESULT_BEAN = "testResult";

    /**
     * 电池充电记录数据库字段
     */
    public static final String CHARGE_RECORD_FIELD_HISTORY_SERIAL_NUMBER = "history_record_serial_number";
    public static final String CHARGE_RECORD_FIELD_CHARGE_TIME = "batt_batt_charge_time";
    public static final String CHARGE_RECORD_FIELD_CHARGE_DURATION = "batt_batt_charge_duration";
    public static final String CHARGE_RECORD_FIELD_CHARGE_CAP = "batt_batt_charge_cap";
    public static final String CHARGE_RECORD_FIELD_CHARGE_FINAL_STATUS = "batt_batt_charge_final_status";
    public static final String CHARGE_RECORD_FIELD_FINAL_POWER = "batt_batt_charge_final_power";


    /**
     * 电池放电soc校验
     */
    public static final String FAIL = "{\"zh_CN\":\"当前电池组存在电池SOC不满足大于等于99%，不能进行放电测试\",\"en_US\":\"The battery set exist battery SOC is not ≥99%，so the discharge test cannot be performed.\"}";
    public static final String SUCCESS = "{\"zh_CN\":\"当前电池组可以进行放电测试\",\"en_US\":\"The current battery set can undergo discharge testing\"}";
    public static final String HOUR = "{\"zh_CN\":\"小时\",\"en_US\":\"h\"}";

    /**
     * 电池临时结果缓存
     */
    public final static String REDIS_CACHE_NAME_BATT_TEST_CACHE = "_BATT_TEST_CACHE";
    public final static String REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME = "KEY_CAUSE_TIME";
    public final static String REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE = "KEY_STATE";
    public final static String REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME = "KEY_STATE_USER_NAME";
    public final static String REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID = "KEY_STATE_TASK_ID";
    public final static String TEMPORARY = "TEMPORARY";
    public final static String PERIOD = "PERIOD";

    /**
     * 电池记录事件类型
     */
    public static final String RCD_TYPE_BATT_TEST = "batt_test";  //电池测试记录
    public static final String RCD_TYPE_BATT_CHARGE= "batt_charge";  //电池充电记录
    public static final String RCD_TYPE_BATT_DISCHARGE = "batt_discharge";  //电池放电记录
    public static final String RCD_TYPE_BATT_AVGCHARGE= "batt_avgcharge";  //电池均充记录
    public static final String RCD_TYPE_BATT_LOG = "batt_log";  //蓄电池日志记录

    /**
     * 获取记录数据时间缓存
     */
    public final static String REDIS_CACHE_NAME_RECORD_QUERY_TIME_CACHE = "RECORD_QUERY_TIME_CACHE";
    public final static String REDIS_CACHE_NAME_RECORD_LAST_QUERY_TIME_KEY = "KEY_LAST_QUERY_TIME";

    /**
     * 持续时长拼接
     */
    public final static String DAY_ZH="天";
    public final static String HOUR_ZH="小时";
    public final static String MIN_ZH="分钟";
    public final static String DAY_EN="Day";
    public final static String HOUR_EN="Hour";
    public final static String MIN_EN="Minute";

    /**
     * 过滤电池告警列表
     */
    public static final List<AlarmCode> filterAlarmCodeList = Collections.unmodifiableList(new ArrayList<AlarmCode>() {
        {
            add(new AlarmCode(MocOptional.BATTERY.getId(), 50003008L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003011L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003014L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003022L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003023L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003025L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003026L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302901L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302902L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302903L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302904L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302905L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302906L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302907L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302908L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302909L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302910L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302911L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302912L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302913L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302914L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302915L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302916L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302917L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302918L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302919L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302920L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302921L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302922L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302923L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000302924L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003038L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003039L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304501L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304502L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304503L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304504L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304505L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304506L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304507L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304508L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304509L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304510L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304511L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304512L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304513L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304514L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304515L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304516L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304517L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304518L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304519L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304520L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304521L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304522L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304523L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304524L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304701L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304702L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304703L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304704L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304705L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304706L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304707L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304708L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304709L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304710L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304711L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304712L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304713L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304714L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304715L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304716L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304717L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304718L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304719L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304720L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304721L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304722L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304723L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304724L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304901L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304902L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304903L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304904L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304905L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304906L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304907L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304908L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304909L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304910L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304911L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304912L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304913L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304914L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304915L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304916L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304917L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304918L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304919L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304920L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304921L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304922L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304923L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000304924L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305001L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305002L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305003L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305004L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305005L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305006L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305007L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305008L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305009L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305010L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305011L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305012L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305013L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305014L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305015L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305016L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305017L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305018L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305019L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305020L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305021L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305022L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305023L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305024L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305101L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305102L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305103L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305104L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305105L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305106L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305107L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305108L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305109L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305110L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305111L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305112L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305113L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305114L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305115L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305116L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305117L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305118L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305119L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305120L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305121L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305122L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305123L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305124L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305801L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305802L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305803L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305804L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305805L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305806L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305807L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305808L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305809L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305810L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305811L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305812L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305813L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305814L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305815L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305816L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305817L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305818L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305819L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305820L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305821L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305822L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305823L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000305824L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306601L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306602L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306603L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306604L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306605L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306606L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306607L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306608L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306609L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306610L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306611L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306612L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306613L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306614L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306615L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306616L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306617L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306618L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306619L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306620L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306621L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306622L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306623L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000306624L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003068L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003071L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003072L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003073L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003074L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003075L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003076L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003077L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003078L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308201L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308202L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308203L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308204L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308205L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308206L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308207L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308208L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308209L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308210L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308211L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308212L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308213L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308214L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308215L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308216L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308217L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308218L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308219L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308220L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308221L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308222L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308223L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),5000308224L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003083L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003088L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003089L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003090L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003091L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003093L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003094L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003096L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003098L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003100L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003101L));
            add(new AlarmCode(MocOptional.BATTERY.getId(),50003102L));
        }
    });

    /**
     * 字符串为空的默认值
     */
    public final static String DEFAULT_NULL_VALUE = "--";

    public final static String BATT = MocOptional.BATTERY.getId();

    public final static String LFP = "LFP";

    public final static String MIN_TIME = "00:00:00";
    public final static String MAX_TIME = "23:59:59";

    public static final int VISIBLE = 1; // 可见

    public final static String OVER = "over";

    public final static String OVP = "ovp";

    public final static String UNDER = "under";

    public final static String UVP = "uvp";


    // 告警可见性筛选条件
    public final static Integer VISIBLE_YES = 1;
    public final static Integer VISIBLE_NO = 0;

}
