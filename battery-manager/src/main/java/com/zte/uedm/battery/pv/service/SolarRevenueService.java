package com.zte.uedm.battery.pv.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.pv.bean.SolarRevenueBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueOverviewExportBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueTieredPvGenerationBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueTieredPvGenerationQueryBean;
import com.zte.uedm.battery.pv.dto.SiteHistoryScopeIdQueryDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueOverviewDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueStatisticsQueryDto;
import com.zte.uedm.battery.pv.vo.SolarRevenueOverviewVo;
import com.zte.uedm.battery.pv.vo.SolarRevenueStatisticsDetailVo;

import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SolarRevenueService {

    /**
     * 分页获取太阳能收益数据
     *
     * @param solarRevenueStatisticsQueryBean
     * @param languageOption
     * @return
     */
    PageInfo<SolarRevenueStatisticsDetailVo> solarRevenueStatisticsSelectPage(SolarRevenueStatisticsQueryDto solarRevenueStatisticsQueryBean, String languageOption, String userName) throws UedmException;

    /**
     * 获取指定条件下的太阳能收益数据
     *
     * @param solarRevenueStatisticsQueryBean
     * @param languageOption
     * @return
     */
    List<SolarRevenueStatisticsDetailVo> solarRevenueStatisticsSelect(SolarRevenueStatisticsQueryDto solarRevenueStatisticsQueryBean, String languageOption, String userName) throws UedmException;

    /**
     * 获取指定时间范围内站点的策略ids
     *
     * @param siteHistoryScopeIdQueryDto 查询条件
     * @param languageOption             语言
     * @param userName                   用户
     * @return
     */
    List<String> siteHistoryScopeIdSelect(SiteHistoryScopeIdQueryDto siteHistoryScopeIdQueryDto, String languageOption, String userName) throws UedmException;

    /**
     * 到处指定条件下的太阳能收益数据
     * @param exportBean
     * @param request
     * @param response
     * @param serviceBean
     * @return
     */
    String exportOverview(SolarRevenueOverviewExportBean exportBean, HttpServletRequest request, HttpServletResponse response, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 收益统计汇总查询
     * @param dto
     * @param userName
     * @param languageOption
     * @return
     */
    SolarRevenueOverviewVo queryStatistics(SolarRevenueOverviewDto dto, String userName, String languageOption) throws UedmException;

    /**
     * 收取收益数据
     *
     * @param pvIds
     * @return 收益数据
     */
    List<SolarRevenueBean> getSolarRevenueListAll(List<String> pvIds);

    /**
     * 更新汇聚收益数据库数据
     * @param solarRevenueBeans
     * @param hour
     * @param source
     * @param taskId
     */
    public void updateSolarRevenueBean(List<SolarRevenueBean> solarRevenueBeans, Integer hour, String source, String taskId);

    /**
     * 查询阶梯档的pv当前档发电量
     *
     * @param solarRevenueTieredPvGenerationQueryBeans
     * @param startDate
     * @param endDate
     * @return
     */
    List<SolarRevenueTieredPvGenerationBean> selectTieredPvGeneration(List<SolarRevenueTieredPvGenerationQueryBean> solarRevenueTieredPvGenerationQueryBeans,
                                                                      String startDate, String endDate);
}
