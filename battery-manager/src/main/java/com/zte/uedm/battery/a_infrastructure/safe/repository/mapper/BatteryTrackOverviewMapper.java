package com.zte.uedm.battery.a_infrastructure.safe.repository.mapper;

import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryTrackOverviewBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryTrackOverviewBeanVo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatteryTrackOverviewMapper
{
    List<BatteryTrackOverviewBean> selectOverviewConfig(@Param("userName") String userName) throws UedmException;

    void insertOverviewConfig(@Param("bean") BatteryTrackOverviewBeanVo bean) throws UedmException;

    Integer updateOverviewConfig(@Param("bean") BatteryTrackOverviewBeanVo bean) throws UedmException;
}
