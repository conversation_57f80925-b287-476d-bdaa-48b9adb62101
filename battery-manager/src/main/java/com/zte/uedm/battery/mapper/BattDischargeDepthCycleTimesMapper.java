package com.zte.uedm.battery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.battery.bean.BattDischargeDepthCycleTimesBean;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BattDischargeDepthCycleTimesMapper extends BaseMapper<BattDischargeDepthCycleTimesBean>
{

    void insert(List<BattDischargeDepthCycleTimesBean> list);

    Integer deleteBybattIdList(List<String> battIdList);

    Integer update(BattDischargeDepthCycleTimesBean bean);

    List<BattDischargeDepthCycleTimesBean> selectBybattIdList(List<String> battIdList);

}
