package com.zte.uedm.battery.opti.domain.aggregate.repository;

import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BattRiskRuleSourceRepository
{
    List<BattRiskRuleSourceEntity> selectByIds(List<String> ids) throws UedmException;

    List<BattRiskRuleSourceEntity> selectAll() throws UedmException;
}
