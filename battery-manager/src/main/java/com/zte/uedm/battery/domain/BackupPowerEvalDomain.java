package com.zte.uedm.battery.domain;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattBackupPowerEvalPojo;
import com.zte.uedm.battery.bean.BattBackupPowerEvalTrendPojo;
import com.zte.uedm.battery.bean.BattBackupPowerOverviewPojo;
import com.zte.uedm.battery.controller.backuppower.dto.DecreaseStatisticsNewDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalDetailDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalTrendDto;
import com.zte.uedm.battery.controller.backuppower.dto.TableDetailSelectDto;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

public interface BackupPowerEvalDomain
{
    void initBackPowerUp();

    /**
     * 备电评估结果详情-条件查询
     * @param evalDetailDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerEvalPojo> selectByCondition(EvalDetailDto evalDetailDto, ServiceBaseInfoBean serviceBean) throws UedmException;
 /**
     * 备电状态-条件查询
     * @param logicGroupId
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    List<BattBackupPowerOverviewPojo> selectByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException;
    /**
     * 备电状态增减-条件查询
     * @param logicGroupId
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    List<BattBackupPowerOverviewPojo> selectInDesByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 备电详情-条件查询
     * @param logicGroupId
     * @return
     * @throws UedmException
     */
    List<BattBackupPowerOverviewPojo> selectInDecreaseDetailByLogicGroupId(String logicGroupId) throws UedmException;


    /**
     * 备电评估结果历史趋势 - 查询
     * @param evalTrendDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerEvalTrendPojo> selectTrendByCondition(EvalTrendDto evalTrendDto,
                                                                  ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 根据监控对象ids查询备电状态
     * @param moIds
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerEvalPojo> selectEvalDByMoIds(List<String> moIds,ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 条件查询备电详情天表
     * @param evalDetailSelectDto
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerEvalPojo> selectEvalDetailByCondition(TableDetailSelectDto evalDetailSelectDto, Map<String, SiteBean> siteMap, Map<String, String> siteLevelMap, Map<String, String> powerSupplySceneMap, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;


    /**
     * 根据逻辑id和评估时间查询月表信息
     * @param dto
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattBackupPowerEvalPojo> selectBackupPowerEvalMByCondition(DecreaseStatisticsNewDto dto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 根据站点等级和供应场景过滤 并 过滤调已经删除的开关电源监控对象详情
     * @param detailSelectDto
     * @param allBackupPowerEvalPojo
     * @param serviceBean
     * @return
     */
    PageInfo<BattBackupPowerEvalPojo> filterDeletedSPDetailAndPage(TableDetailSelectDto detailSelectDto, List<BattBackupPowerEvalPojo> allBackupPowerEvalPojo, Map<String, SiteBean> siteMap, Map<String, String> siteLevelMap, Map<String, String> powerSupplySceneMap,ServiceBaseInfoBean serviceBean) throws UedmException;
}
