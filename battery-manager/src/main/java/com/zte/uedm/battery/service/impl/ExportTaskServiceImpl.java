package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.controller.battexport.dto.ExportTaskDto;
import com.zte.uedm.battery.controller.battexport.vo.ExportTaskVO;
import com.zte.uedm.battery.enums.taskexport.ExportStatusEnums;
import com.zte.uedm.battery.export.manage.ExportHandlerFactory;
import com.zte.uedm.battery.export.manage.entity.ExportTaskPO;
import com.zte.uedm.battery.export.manage.entity.ServiceBaseInfo;
import com.zte.uedm.battery.mapper.ExportTaskMapper;
import com.zte.uedm.battery.service.ExportTaskService;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExportTaskServiceImpl implements ExportTaskService {
    @Autowired
    private ExportTaskMapper exportTaskMapper;
    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private JsonService jsonService;
    @Autowired
    ExportHandlerFactory exportHandlerFactory;
    @Override
    public boolean createTask(Object exportBean, ServiceBaseInfoBean serviceBean,String fileName,String type,String fileType,String exportKey) throws UedmException {
        /* Started by AICoder, pid:h65685bdf0m4365143930807d068e52aa6f8f9c5 */
        ExportTaskPO task = new ExportTaskPO();
        try {
            task.setId(UUID.randomUUID().toString());
            task.setStatus(ExportStatusEnums.INIT.getStatus());
            task.setCreateUser(serviceBean.getUserName());
            task.setFileName(fileName);
            task.setExportKey(exportKey);
            //查询条件
            task.setParams(jsonService.objectToJson(exportBean));
            ServiceBaseInfo serviceBaseInfo  =new ServiceBaseInfo();
            serviceBaseInfo.setLanguageOption(serviceBean.getLanguageOption());
            serviceBaseInfo.setUserName(serviceBean.getUserName());
            serviceBaseInfo.setExportType(type);
            serviceBaseInfo.setFileType(fileType);
            task.setServiceBean(jsonService.objectToJson(serviceBaseInfo));
            task.setGmtCreate(dateTimeService.getCurrentDateTime());
            task.setFilePath(GlobalConstants.DEFAULT_VALUE);
            task.setProgress(GlobalConstants.DEFAULT_VALUE);
            // 入库操作
            exportTaskMapper.insertTask(task);
            List<ExportTaskPO> tasks = Collections.singletonList(task);
            exportHandlerFactory.addTaskToQue(tasks);
        } catch (Exception e) {
            // 异常处理
            log.error("constructData error", e);
            throw new UedmException(-1, e.getMessage());
        }

        // 进入队列

        /* Ended by AICoder, pid:h65685bdf0m4365143930807d068e52aa6f8f9c5 */
        return true;
    }
    @Override
    /* Started by AICoder, pid:y44act094e4f7df14bfb0a23b06e491467b51b6a */
    public List<ExportTaskVO> selectByCondition(ExportTaskDto query)throws UedmException {
        log.info("ExportTaskServiceImpl  selectByCondition  {}", JSON.toJSONString(query));
        return exportTaskMapper.selectByCondition(query.getExportKey(), query.getCreateUser())
                .stream()
                .map(taskInfo -> {
                    ExportTaskVO exportTaskVO = new ExportTaskVO();
                    exportTaskVO.setTaskId(taskInfo.getId());
                    exportTaskVO.setFileName(taskInfo.getFileName());
                    Optional.ofNullable(DateUtils.getStrDateByDateType(taskInfo.getGmtCreate(), DateTypeConst.DATE_FORMAT_1)).ifPresent(exportTaskVO::setGmtCreate);
                    exportTaskVO.setProgress(taskInfo.getProgress());
                    exportTaskVO.setStatus(taskInfo.getStatus());
                    Optional.ofNullable(DateUtils.getStrDateByDateType(taskInfo.getCompleteTime(), DateTypeConst.DATE_FORMAT_1)).ifPresent(exportTaskVO::setCompleteTime);
                    return exportTaskVO;
                })
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:y44act094e4f7df14bfb0a23b06e491467b51b6a */
    @Override
    public String taskFileDownload(String taskId, HttpServletResponse response , HttpServletRequest request)  throws  UedmException{
        ExportTaskPO taskInfo =null;
        try {

            taskInfo=  exportTaskMapper.selectById(taskId);
            if(taskInfo == null){
                log.info("exporBatteryHisData fw is null");
                return "" ;
            }

            // 任务状态异常不能下载
            if(!ExportStatusEnums.SUCCESS.getStatus().equals(taskInfo.getStatus())){
                log.info("exporBatteryHisData fw is null");
                return "";
            }
            // 获取上传文件夹配置 并 得到需要下载的文件的绝对路径
            String uploadDir = taskInfo.getFilePath();
            FileUtils.downloadPictureFile(uploadDir, response, request);
        } catch (Exception e) {
            log.error("exportBattCellData ", e);
            throw new UedmException(-1, "exportBattCellData fail");
        }

        exportTaskMapper.deleteById(taskId);
        return taskInfo.getFileName();
    }
    /* Started by AICoder, pid:96d6ew473bx4ae6146ed0876f09e49321b06636a */
    public List<String> taskBatchDelete(String exportKey, List<String> needDelTaskIds, String userName) {
        log.info("taskBatchDelete start");
        // 获取当前用户和导出类型的任务映射
        Map<String, ExportTaskPO> thisUserExportKeyTaskMap = exportTaskMapper.selectByCreateUserAndExportKey( userName,exportKey)
                .stream()
                .collect(Collectors.toMap(
                        ExportTaskPO::getId,
                        Function.identity(),
                        (o1, o2) -> o1
                ));
        log.info("taskBatchDelete thisUserExportKeyTaskMap.size={}", thisUserExportKeyTaskMap.size());
        log.debug("taskBatchDelete thisUserExportKeyTaskMap={}", thisUserExportKeyTaskMap);

        // 如果被删除的任务id为空，则根据当前用户以及导出类型找到需要删除的任务id
        if (CollectionUtils.isEmpty(needDelTaskIds)) {
            needDelTaskIds = new ArrayList<>(thisUserExportKeyTaskMap.keySet());
        }
        log.info("taskBatchDelete needDelTaskIds.size={}", needDelTaskIds.size());
        log.debug("taskBatchDelete needDelTaskIds={}", needDelTaskIds);

        // 根据id批量删除任务
        exportTaskMapper.removeBatchByIds(needDelTaskIds);

        // 并行流处理，筛选出任务完成的并删除文件
        needDelTaskIds.parallelStream()
                .map(thisUserExportKeyTaskMap::get)
                .filter(Objects::nonNull)
                .forEach(task -> deleteFile(task));

        // 并行流处理，停止正在进行的任务
        needDelTaskIds.parallelStream()
                .forEach(taskId -> exportHandlerFactory.stopTaskByTaskId(taskId));

        log.info("taskBatchDelete end");
        return needDelTaskIds;
    }
    /* Ended by AICoder, pid:96d6ew473bx4ae6146ed0876f09e49321b06636a */

    /* Started by AICoder, pid:qc0cfu315c84c5914303087f90ebf4189af102a0 */
    private void deleteFile(ExportTaskPO taskInfo) {
        if (ExportStatusEnums.SUCCESS.getStatus().equals(taskInfo.getStatus())) {
            File file = new File(FileUtils.pathManipulation(taskInfo.getFilePath()));
            File parentFile = file.getParentFile();
            if (parentFile != null) {
                if (!FileUtils.delFile(parentFile)) {
                    log.error("Failed to delete file: {}", parentFile.getAbsolutePath());
                }
            }
        }
    }
    /* Ended by AICoder, pid:qc0cfu315c84c5914303087f90ebf4189af102a0 */




}
