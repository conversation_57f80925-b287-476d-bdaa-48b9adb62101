package com.zte.uedm.battery.a_interfaces.peakshift.web.converter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.IssHistoryEntity;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.IssHistoryVo;
/* Started by AICoder, pid:d8dbfbd6a28d4a7da5d9ae01ef4061a9 */
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface IssHistoryVoConverter {
    IssHistoryVoConverter INSTANCE = Mappers.getMapper(IssHistoryVoConverter.class);

    @Mappings({})
    IssHistoryVo issHistoryEntityToIssHistoryVo(IssHistoryEntity issHistoryEntity);

    List<IssHistoryVo> listIssHistoryEntityToIssHistoryVo(List<IssHistoryEntity> entityList);

    @Mappings({})
    IssHistoryEntity issHistoryVoToIssHistoryEntity(IssHistoryVo issHistoryVo);

    @Mappings({})
    List<IssHistoryEntity> listIssHistoryVoToIssHistoryEntity(List<IssHistoryVo> voList);
}
/* Ended by AICoder, pid:d8dbfbd6a28d4a7da5d9ae01ef4061a9 */
