package com.zte.uedm.battery.pv.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 太阳能收益概览维度bean
 * @date 2023/9/4
 **/
@Setter
@Getter
@ToString
public class SolarRevenueOverviewDimensionsBean {

    /**
     * 太阳能的收益概览维度
     */
    private String id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 顺序
     */
    private Integer sequence;

    /**
     * 维度名称
     */
    private String name;

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date gmtModified;

}
