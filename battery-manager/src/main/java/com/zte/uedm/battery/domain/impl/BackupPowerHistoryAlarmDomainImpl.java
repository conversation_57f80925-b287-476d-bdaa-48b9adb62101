package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmBatchSchBean;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.battery.domain.BackupPowerHistoryAlarmDomain;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.po.BackupPowerBean;
import com.zte.uedm.battery.domain.po.BatteryBackupPowerBean;
import com.zte.uedm.battery.enums.AlarmTypeEnum;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.enums.backuppower.BatteryBackupPowerStateDetailEnum;
import com.zte.uedm.battery.enums.record.BattAlarmConstant;
import com.zte.uedm.battery.mapper.BattAlarmMapper;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.AlarmService;
import com.zte.uedm.battery.service.SystemConfigService;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorDeviceObjectRelationBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.config.optional.StandPointOptional;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.api.BattConst.DATA_TYPE_COLLECT;
import static com.zte.uedm.battery.enums.record.BattAlarmConstant.BACKUP_POWER_VALUE_FLAG;
import static com.zte.uedm.battery.enums.record.BattAlarmConstant.ERROR_CODE_FLAG;

@Service
@Slf4j
public class BackupPowerHistoryAlarmDomainImpl implements BackupPowerHistoryAlarmDomain {
    private static final String ALARM_RAISED_TIME = "alarmraisedtime";
    // true
    private static final String ERROR_CODE_0 = "0";

    // ACDP_02/ACDP_03/ACDP_04/DISTMAINS_04  no monitorObject data
    private static final String ERROR_CODE_1 = "-3";

    // ACDP_02/ACDP_03/ACDP_04/DISTMAINS_04  no alarm data
    private static final String ERROR_CODE_2 = "-4";

    // ACDP_02/ACDP_03/ACDP_04/DISTMAINS_04  within 10 hours exist alarm
    private static final String ERROR_CODE_3 = "-5";

    // ACDP_02/ACDP_03/ACDP_04/DISTMAINS_04  no monitorObject data
    private static final String ERROR_CODE_4 = "-6";

    // ACDP_02/ACDP_03/ACDP_04/DISTMAINS_04  no alarm data
    private static final String ERROR_CODE_5 = "-7";
    private static final BigDecimal NINETY_NINE = new BigDecimal(99);
    private static final String BATTPACK_CHG_COEFF = StandPointOptional.BATTERYSET_SMPID_CHG_COEFF.getId();
    private static final String LONG1 = "long1";
    private static final String LONG2 = "long2";

    private static final String FULL_DURATION = "full";

    private static final String NOT_FULL_DURATION = "notFull";

    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private PmaServiceUtils pmaService;
    @Autowired
    private BatteryMaximumChargingCapacityDomainImpl batteryMaximumChargingCapacityDomainImpl;
    @Autowired
    private BattLifeEvalDomain battLifeEvalDomain;
    @Autowired
    private AlarmService alarmService;

    @Autowired
    private JsonService jsonService;
    @Autowired
    private BattAlarmMapper battAlarmMapper;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Autowired
    private SystemConfigService configService;

    @Autowired
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Autowired
    private CollectorCacheManager collectorCacheManager;

    /**
     * return code
     * return Long
     * 备电时长计算-告警
     */
    @Override
    public Map<String, Object> getSiteAlrmTimeByMoList(BackupPowerBean backupPowerBean, List<DeviceEntity> moList, String id, List<String> reasons, BattBackupPowerEvalResultBean battBackupPowerEvalResultBean, BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean)
            throws ParseException, UedmException, IOException {
        log.info("====================getSiteAlrmTimeByMoList start {} ====================",id);
        log.debug("moList {} ", moList);
        Map<String, Object> map = new HashMap<>();

        //获取监控对象关联的监控设备id
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorObjectId(id);
//        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = configurationManagerRpcImpl.getMonitorDeviceObjectRelation(
//                monitorDeviceObjectRelationBean, null, null);
        Set<String> collectorId = new HashSet<>();
        try {
            collectorId = resourceCollectorRelationCacheManager.getCollectorIdByResourceId(id);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("getSiteAlrmTimeByMoList ->resourceCollectorRelationCacheManager getCollectorIdByResourceId error");
        }
        if (collectorId == null || collectorId.contains(null)){
            log.error("resourceCollectorRelationCacheManager getCollectorIdByResourceId -> collectorId is null or contains null");
            return map;
        }
        List<String> collectorRelationIds = collectorId.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<CollectorEntity> collectorListByIds = collectorCacheManager.getCollectorById(collectorRelationIds);
        collectorListByIds = collectorListByIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (collectorListByIds.isEmpty()){
            log.error("collectorCacheManager getCollectorById  -> collectorListByIds is empty");
            return map;
        }
        String collector = collectorListByIds.get(0).getId();
        log.info("getSiteAlrmTimeByMoList : collector {} : collectorListByIds size is {}", collector, collectorListByIds.size());

        //按照moc分组
        List<String> battPackList = new ArrayList<>();
        List<String> dcpowerList = new ArrayList<>(Collections.singleton(id));
        Map<String, List<DeviceEntity>> mapMo = moList.stream()
                .collect(Collectors.groupingBy(DeviceEntity::getMoc));
        dealGroupMoc(mapMo,battPackList);


        //默认查询最近7天的下电告警,可修改
        List<Alarm> LLVDAlarmList = new ArrayList<>();
        queryLlvdAlarmList(LLVDAlarmList, dcpowerList, battPackList, collector);
        if(CollectionUtils.isEmpty(LLVDAlarmList))
        {
            map.put(ERROR_CODE_FLAG, ERROR_CODE_5);
            reasons.add(BatteryBackupPowerStateDetailEnum.NO_DCDP_ALARM_DATA.getId());
            return map;
        }

        //查询交流停电告警
        List<Alarm> acPowerOffAlarmList = new ArrayList<>();
        queryAcPowerOffAlarmList(acPowerOffAlarmList, dcpowerList, collector, LLVDAlarmList);
        if(CollectionUtils.isEmpty(acPowerOffAlarmList))
        {
            map.put(ERROR_CODE_FLAG, ERROR_CODE_2);
            reasons.add(BatteryBackupPowerStateDetailEnum.NO_ACDP_ALARM_DATA.getId());
            return map;
        }

        //交流停电和下电告警均存在,计算备电时长
        Map<String, Double> backupPowerDurations = new HashMap<>();
        calculateBackupPowerDuration(LLVDAlarmList, acPowerOffAlarmList, collector, battPackList, backupPowerBean, reasons, backupPowerDurations,battBackupPowerEvalConditionBean);

        if(null == backupPowerDurations.get(FULL_DURATION))
        {
            log.info("[getSiteAlarmTimeByMoList][{}] sp-{} Not meeting the full charge condition", collector, id);
            battBackupPowerEvalResultBean.setMax(0);
            map.put(BACKUP_POWER_VALUE_FLAG, backupPowerDurations.get(NOT_FULL_DURATION));
        }
        else
        {
            log.info("[getSiteAlarmTimeByMoList][{}] sp-{} meeting the full charge condition", collector, id);
            battBackupPowerEvalResultBean.setMax(1);
            map.put(BACKUP_POWER_VALUE_FLAG, backupPowerDurations.get(FULL_DURATION));
        }
        log.info("map: {}", map);
        return map;
    }




    private void queryLlvdAlarmList(List<Alarm> LLVDAlarmList, List<String> dcpowerList, List<String> battPackList, String deviceId)
            throws ParseException
    {
        //下电告警查询时间
        String currentTime = dateTimeService.getCurrentTime();
        String startTime = DateUtils.getDateByAddOfDay(DateUtils.getStringDateStr(currentTime), -(configService.getLlvdTimeRange()));


        try
        {
            //普通空开下电告警告警码
            List<AlarmCode> commonAlarmCodes = new ArrayList<>();
            getCommonAlarmCode(commonAlarmCodes, AlarmTypeEnum.BATTPACK_08, battPackList);
            getCommonAlarmCode(commonAlarmCodes, AlarmTypeEnum.DCDP_11, dcpowerList);
            log.debug("getSiteAlrmTimeByMoList : [{}] dcdpList size is {}, battPackList size is {}, commonAlarmCodes is {}", deviceId, dcpowerList.size(), battPackList.size(), commonAlarmCodes);
            //组装普通空开下电告警查询监控对象
            battPackList.addAll(dcpowerList);
            log.debug("getSiteAlrmTimeByMoList : [{}] all object ids size is {}, time range is {} -- {}", deviceId, battPackList.size(), startTime, currentTime);
            //获取普通空开下电告警,来源实时和历史告警
            List<Alarm> commonLlvdAlarmList = getALarmListByMolist(battPackList, commonAlarmCodes, startTime,
                    currentTime, 0, ALARM_RAISED_TIME);
            LLVDAlarmList.addAll(commonLlvdAlarmList);
            log.info("getSiteAlrmTimeByMoList : [{}] commonLlvdAlarmList szie is {}", deviceId, commonLlvdAlarmList.size());
            log.debug("getSiteAlrmTimeByMoList : [{}] commonLlvdAlarmList is {}", deviceId, commonLlvdAlarmList);
        }
        catch (Exception e)
        {
            log.warn("[{}] query common llvd alarm failed!", deviceId, e);
        }

        try
        {
            //智能空开下电告警码
            List<AlarmCode> smartAlarmCodes = getDcdpBranchAlarmCode(dcpowerList);
            log.debug("getSiteAlrmTimeByMoList : [{}] dcdpList size is {}, smartAlarmCodes is {}", deviceId, dcpowerList.size(), smartAlarmCodes);
            //获取智能空开下电告警,来源实时和历史告警
            List<Alarm> smartLlvdAlarmList = getALarmListByMolist(dcpowerList, smartAlarmCodes, startTime,
                    currentTime, 0, ALARM_RAISED_TIME);
            log.debug("getSiteAlrmTimeByMoList : [{}] before filter smartLlvdAlarmList is {}", deviceId, smartLlvdAlarmList);
            //智能空开下电告警过滤
//            filterSmartBranchAlarmByCondition(smartLlvdAlarmList, deviceId);
            LLVDAlarmList.addAll(smartLlvdAlarmList);
            log.info("getSiteAlrmTimeByMoList : [{}] smartLlvdAlarmList szie is {}", deviceId, smartLlvdAlarmList.size());
            log.debug("getSiteAlrmTimeByMoList : [{}] smartLlvdAlarmList is {}", deviceId, smartLlvdAlarmList);
        }
        catch (Exception e)
        {
            log.warn("[{}] query smart llvd alarm failed!", deviceId, e);
        }
    }

    public List<AlarmCode> getDcdpBranchAlarmCode(List<String> dcdpList)
    {
        if(CollectionUtils.isEmpty(dcdpList))
        {
            return new ArrayList<>();
        }
        List<AlarmCode> alarmCodes = new ArrayList<>();
        for (Long l : BattAlarmConstant.CODE_LIST){
            AlarmCode alarmCode = new AlarmCode();
            alarmCode.setRestype(MocOptional.DC_POWER.getId());
            alarmCode.setAlarmcode(l);
            alarmCodes.add(alarmCode);
        }
        return alarmCodes;
    }

    private void queryAcPowerOffAlarmList(List<Alarm> acPowerOffAlarmList, List<String> dcPowerList, String deviceId, List<Alarm> LLVDAlarmList)
            throws ParseException
    {
        //下电告警按照时间排序,获取交流停电告警查询时间
        Collections.sort(LLVDAlarmList, Comparator.comparing(Alarm::getAlarmraisedtime).reversed());
        Long endTime = LLVDAlarmList.get(0).getAlarmraisedtime();
        String endStrTime = dateTimeService.getStrTime(endTime);
        Long startTime = LLVDAlarmList.get(LLVDAlarmList.size() - 1).getAlarmraisedtime();
        String startStrTime = DateUtils.getDateByAddOfDay(DateUtils.getStringDateStr(dateTimeService.getStrMillisecondTime(startTime)), -(configService.getAcPowerOffTimeRange()));
        log.debug("getSiteAlrmTimeByMoList : [{}] query ac power off time range is {}---{}", deviceId, startStrTime, endStrTime);

        //交流停电告警告警码
        List<AlarmCode> acPowerOffAlarmCodes = new ArrayList<>();
        getCommonAlarmCode(acPowerOffAlarmCodes, AlarmTypeEnum.ACDP, dcPowerList);
        log.debug("getSiteAlrmTimeByMoList : [{}] acPowerOffAlarmCodes is {}", deviceId, acPowerOffAlarmCodes);
        //获取交流停电下电告警,来源实时和历史告警
        List<Alarm> commonAlarmList = getALarmListByMolist(dcPowerList, acPowerOffAlarmCodes, startStrTime,
                endStrTime, 0, ALARM_RAISED_TIME);
        acPowerOffAlarmList.addAll(commonAlarmList);
        log.info("getSiteAlrmTimeByMoList : [{}] commonAlarmList is {}", deviceId, commonAlarmList);
    }

    private void calculateBackupPowerDuration(List<Alarm> LLVDAlarmList, List<Alarm> acPowerOffAlarmList, String deviceId, List<String> battPackList, BackupPowerBean backupPowerBean, List<String> reasons, Map<String, Double> backupPowerDurations,BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean)
    {
        //下电告警和交流停电告警合并后，按照时间逆序排序
        List<Alarm> totalAlarmList = new ArrayList<>();
        totalAlarmList.addAll(LLVDAlarmList);
        totalAlarmList.addAll(acPowerOffAlarmList);
        log.info("[{}] getSiteAlrmTimeByMoList -- calculateBackupPowerDuration totalAlarmList szie is {}", deviceId, totalAlarmList.size());
        log.debug("[{}] getSiteAlrmTimeByMoList -- calculateBackupPowerDuration totalAlarmList is {}", deviceId, totalAlarmList);
        Collections.sort(totalAlarmList, Comparator.comparing(Alarm::getAlarmraisedtime).reversed());
        Alarm tempAlarm = null;

        //计算备电时长
        for(Alarm alarm : totalAlarmList)
        {
            if(!checkAcPowerOffAlarm(alarm) && tempAlarm == null)
            {
                tempAlarm = alarm;
            }
            else if (!checkAcPowerOffAlarm(alarm) && tempAlarm != null)
            {
                tempAlarm = alarm;
            }
            else if(checkAcPowerOffAlarm(alarm) && tempAlarm != null)
            {
                calculateSingleBackupPowerDuration(tempAlarm, alarm, backupPowerDurations, deviceId, battPackList, backupPowerBean, reasons,battBackupPowerEvalConditionBean);
                tempAlarm = null;
                if(null != backupPowerDurations.get(FULL_DURATION))
                {
                    break;
                }
            }
        }

        log.info("[{}] getSiteAlrmTimeByMoList -- backupPowerDurations is {}", deviceId, backupPowerDurations);
    }

    private void calculateSingleBackupPowerDuration(Alarm llvdAlarm, Alarm acPowerOffAlarm, Map<String, Double> backupPowerDurations, String deviceId, List<String> battPackList, BackupPowerBean backupPowerBean, List<String> reasons,BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean)
    {
        Double fullValue = backupPowerDurations.get(FULL_DURATION);
        Double notFullValue = backupPowerDurations.get(NOT_FULL_DURATION);
        BattBackupPowerEvalAlarmConditionBean battBackupPowerEvalAlarmConditionBean = new BattBackupPowerEvalAlarmConditionBean();
        try
        {
            if(!isMaxCapacity(backupPowerBean, acPowerOffAlarm, battPackList, reasons))
            {
                log.info("[{}] full condition : LLVD Alarm time is {}, ac power off time is {}", deviceId, dateTimeService.getStrTime(llvdAlarm.getAlarmraisedtime()), dateTimeService.getStrTime(acPowerOffAlarm.getAlarmraisedtime()));
                fullValue = (double) (llvdAlarm.getAlarmraisedtime() - acPowerOffAlarm.getAlarmraisedtime());
                battBackupPowerEvalAlarmConditionBean.setLlvdAlarmAlarmraisedtime(new Date(llvdAlarm.getAlarmraisedtime()));
                battBackupPowerEvalAlarmConditionBean.setAcPowerOffAlarmAlarmraisedtime(new Date(acPowerOffAlarm.getAlarmraisedtime()));
                battBackupPowerEvalConditionBean.setBattBackupPowerEvalAlarmConditionBean(battBackupPowerEvalAlarmConditionBean);
                backupPowerDurations.put(FULL_DURATION, fullValue);
            }
            else
            {
                if(fullValue == null && notFullValue == null)
                {
                    log.info("[{}] not full condition : LLVD Alarm time is {}, ac power off time is {}", deviceId, dateTimeService.getStrTime(llvdAlarm.getAlarmraisedtime()), dateTimeService.getStrTime(acPowerOffAlarm.getAlarmraisedtime()));
                    notFullValue = (double) (llvdAlarm.getAlarmraisedtime() - acPowerOffAlarm.getAlarmraisedtime());
                    battBackupPowerEvalAlarmConditionBean.setLlvdAlarmAlarmraisedtime(new Date(llvdAlarm.getAlarmraisedtime()));
                    battBackupPowerEvalAlarmConditionBean.setAcPowerOffAlarmAlarmraisedtime(new Date(acPowerOffAlarm.getAlarmraisedtime()));
                    battBackupPowerEvalConditionBean.setBattBackupPowerEvalAlarmConditionBean(battBackupPowerEvalAlarmConditionBean);
                    backupPowerDurations.put(NOT_FULL_DURATION, notFullValue);
                }
            }

        }
        catch (Exception e)
        {
            log.warn("[{}] calculateSingleBackupPowerDuration is error!, error info is {}.", deviceId, e.getMessage());
        }
    }

    private boolean checkAcPowerOffAlarm(Alarm alarm)
    {
        Set<Long> alarmCodes = AlarmTypeEnum.getAcdpAlarmCodes();
        if(alarmCodes.contains(alarm.getAlarmcode()))
        {
            return true;
        }
        return false;
    }



    public boolean checkAlarm(List<BattAlarmBean> alarmBeanRemote, List<BattAlarmBean> alarmBeanTime, List<BattAlarmBean> alarmBeanExemption, String ompIndex, Long alarmraisedtime) {
        log.info("alarmraisedtime {}", dateTimeService.getStrTime(alarmraisedtime));

        //检查远程下电
        log.debug("alarmBeanRemote {}", alarmBeanRemote);
        if (checkRemote(alarmBeanRemote, alarmraisedtime, ompIndex)) {
            log.info("checkRemote return true");
            return true;
        }
        //检查定时下电
        log.debug("alarmBeanTime {}", alarmBeanTime);
        if (checkTimeAlarm(alarmBeanTime, alarmraisedtime, ompIndex)) {
            log.info("checkTimeAlarm return true");
            return true;
        }
        //检查免责下电
        log.debug("alarmBeanExemption {}", alarmBeanExemption);
        if (checkExemption(alarmBeanExemption, alarmraisedtime, ompIndex)) {
            log.info("checkExemption return true");
            return true;
        }


        return false;
    }

    private boolean checkExemption(List<BattAlarmBean> alarmBeanExemption, Long alarmraisedtime, String ompIndex) {
        List<BattAlarmBean> battAlarmBeanswith90s = alarmBeanExemption.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpIndex(), ompIndex)).
                filter(battAlarmBean -> ((alarmraisedtime + 90 * 1000) >= (battAlarmBean.getStartTime().getTime())) && ((alarmraisedtime) <= (battAlarmBean.getStartTime().getTime()))).collect(Collectors.toList());
        log.info("battAlarmBeanswithExemption90s {}", battAlarmBeanswith90s);
        if (battAlarmBeanswith90s.size() > 0) {
            log.info("battAlarmBeanswithExemption90s {}", battAlarmBeanswith90s);
            if(checkExemptionInCommonUse(battAlarmBeanswith90s,alarmraisedtime)){
                return true;
            }
        } else {

            List<BattAlarmBean> alarmBeanExemption1 = alarmBeanExemption.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpIndex(), ompIndex)).
                    filter(battAlarmBean -> alarmraisedtime > battAlarmBean.getStartTime().getTime()).collect(Collectors.toList());
            log.info("alarmBeanExemption1 {}", alarmBeanExemption1);

            if (!alarmBeanExemption1.isEmpty()) {
                log.info("alarmBeanExemption2 {}", alarmBeanExemption1);
                if(checkExemptionInCommonUse(alarmBeanExemption1,alarmraisedtime)){
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkTimeAlarm(List<BattAlarmBean> alarmBeanTime, Long alarmraisedtime, String ompIndex) {
        List<BattAlarmBean> battAlarmBeanswith90s = alarmBeanTime.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpIndex(), ompIndex)).
                filter(battAlarmBean -> ((alarmraisedtime + 90 * 1000) >= (battAlarmBean.getStartTime().getTime())) && ((alarmraisedtime) <= (battAlarmBean.getStartTime().getTime()))).collect(Collectors.toList());
        log.debug("battAlarmBeanswithTime90s {}", battAlarmBeanswith90s);
        if (!battAlarmBeanswith90s.isEmpty()) {
            log.debug("battAlarmBeanswithTime90s {}", battAlarmBeanswith90s);
            if (checkTimeInCommonUse(battAlarmBeanswith90s, alarmraisedtime)) {
                return true;
            }

    }else {
            List<BattAlarmBean> alarmBeanTime1 = alarmBeanTime.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpIndex(), ompIndex)).
                filter(battAlarmBean -> alarmraisedtime > battAlarmBean.getStartTime().getTime()).collect(Collectors.toList());
        log.debug("alarmBeanTime1 {}", alarmBeanTime1);
        if (!alarmBeanTime1.isEmpty()) {
            if (checkTimeInCommonUse(alarmBeanTime1, alarmraisedtime)) {
                return true;
                }
            }
        }
        return false;
    }






    public boolean checkTimeInCommonUse(List<BattAlarmBean> alarmBeanTime, Long alarmraisedtime){
        if(checkTimeSwitch(alarmBeanTime,BattConst.TIME_SWITCH) ||
                checkTimeSwitch(alarmBeanTime,BattConst.TIME_DC_SWITCH) ||
                checkTimeSwitch(alarmBeanTime,BattConst.TIME_UNIT_SWITCH)){
            if(checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.TIME_START1,BattConst.TIME_END1,
                    BattConst.TIME_START2,BattConst.TIME_END2,BattConst.TIME_START3,BattConst.TIME_END3,
                    BattConst.TIME_START4,BattConst.TIME_END4,BattConst.TIME_START5,BattConst.TIME_END5)||
                    checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.TIME_DC_START1,BattConst.TIME_DC_END1,
                            BattConst.TIME_DC_START2,BattConst.TIME_DC_END2,BattConst.TIME_DC_START3,BattConst.TIME_DC_END3,
                            BattConst.TIME_DC_START4,BattConst.TIME_DC_END4,BattConst.TIME_DC_START5,BattConst.TIME_DC_END5)||
                    checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.TIME_UNIT_START1,BattConst.TIME_UNIT_END1,
                            BattConst.TIME_UNIT_START2,BattConst.TIME_UNIT_END2,BattConst.TIME_UNIT_START3,BattConst.TIME_UNIT_END3,
                            BattConst.TIME_UNIT_START4,BattConst.TIME_UNIT_END4,BattConst.TIME_UNIT_START5,BattConst.TIME_UNIT_END5)){
                return true;
            }
        }
        return false;
    }
    public boolean checkExemptionInCommonUse(List<BattAlarmBean> alarmBeanTime, Long alarmraisedtime){
        if(checkTimeSwitch(alarmBeanTime,BattConst.EXEMPTION_SWITCH) ||
                checkTimeSwitch(alarmBeanTime,BattConst.EXEMPTION_DC_SWITCH) ||
                checkTimeSwitch(alarmBeanTime,BattConst.EXEMPTION_UNIT_SWITCH)){
            if(checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.EXEMPTION_START1, BattConst.EXEMPTION_END1,
                    BattConst.EXEMPTION_START2, BattConst.EXEMPTION_END2, BattConst.EXEMPTION_START3, BattConst.EXEMPTION_END3,
                    BattConst.EXEMPTION_START4, BattConst.EXEMPTION_END4, BattConst.EXEMPTION_START5, BattConst.EXEMPTION_END5)||
                    checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.EXEMPTION_DC_START1, BattConst.EXEMPTION_DC_END1,
                            BattConst.EXEMPTION_DC_START2, BattConst.EXEMPTION_DC_END2, BattConst.EXEMPTION_DC_START3, BattConst.EXEMPTION_DC_END3,
                            BattConst.EXEMPTION_DC_START4, BattConst.EXEMPTION_DC_END4, BattConst.EXEMPTION_DC_START5, BattConst.EXEMPTION_DC_END5)||
                    checkAllPairTime(alarmBeanTime,alarmraisedtime,BattConst.EXEMPTION_UNIT_START1, BattConst.EXEMPTION_UNIT_END1,
                            BattConst.EXEMPTION_UNIT_START2, BattConst.EXEMPTION_UNIT_END2, BattConst.EXEMPTION_UNIT_START3, BattConst.EXEMPTION_UNIT_END3,
                            BattConst.EXEMPTION_UNIT_START4, BattConst.EXEMPTION_UNIT_END4, BattConst.EXEMPTION_UNIT_START5, BattConst.EXEMPTION_UNIT_END5)){
                return true;
            }
        }
        return false;

    }

    public boolean checkRemote(List<BattAlarmBean> alarmBeanRemote,Long alarmraisedtime,String ompIndex){
        log.info("alarmraisedtime {}",alarmraisedtime);
        List<BattAlarmBean> alarmBeanswith90s = alarmBeanRemote.stream().filter(battAlarmBean ->StringUtils.equals(battAlarmBean.getOmpIndex(), ompIndex) ).
                filter(battAlarmBean ->((alarmraisedtime + 180*1000) >= (battAlarmBean.getStartTime().getTime() ))
                        && ((alarmraisedtime - 180*1000 ) <= (battAlarmBean.getStartTime().getTime() ))).collect(Collectors.toList());
        log.info("alarmBeanswith90s {}",alarmBeanswith90s);
        if(alarmBeanswith90s.size()>0){
            Collections.sort(alarmBeanswith90s, Comparator.comparing(BattAlarmBean::getStartTime).reversed());
            Date startTime = alarmBeanswith90s.get(0).getStartTime();
            alarmBeanswith90s.stream().filter(battAlarmBean -> Objects.equals(battAlarmBean.getStartTime(), startTime)).collect(Collectors.toList());
            log.info("alarmBeanswith90s {}",alarmBeanswith90s);
            if(checkRemoteSwitch(alarmBeanswith90s,BattConst.REMOTE_SWITCH,BattConst.REMOTE_STATUS) ||
                    checkRemoteSwitch(alarmBeanswith90s,BattConst.REMOTE_DC_SWITCH,BattConst.REMOTE_DC_STATUS) ||
                    checkRemoteSwitch(alarmBeanswith90s,BattConst.REMOTE_UNIT_SWITCH,BattConst.REMOTE_UNIT_STATUS)||
                    checkRemoteSwitch(alarmBeanswith90s,BattConst.REMOTE_SMART_UNIT_SWITCH,BattConst.REMOTE_SMART_UNIT_STATUS)){
                return true;
            }
        }
        return false;
    }
    private boolean checkRemoteSwitch(List<BattAlarmBean> alarmBeanswith90s,String remoteSwitch,String remoteStatus){
        List<BattAlarmBean> alarmBeanRemote1 =alarmBeanswith90s.stream().filter(battAlarmBean -> {
            return StringUtils.equals(battAlarmBean.getOmpId(), remoteSwitch) && StringUtils.equals(battAlarmBean.getOmpValue(), "1");
        }).collect(Collectors.toList());
        log.info("alarmBeanRemote1 {}",alarmBeanRemote1);
        if(alarmBeanRemote1.size()>0){
            List<BattAlarmBean> alarmBeanRemote2 =alarmBeanswith90s.stream().filter(battAlarmBean -> {
                return StringUtils.equals(battAlarmBean.getOmpId(), remoteStatus) && StringUtils.equals(battAlarmBean.getOmpValue(), "1");
            }).collect(Collectors.toList());
            log.info("alarmBeanRemote2 {}",alarmBeanRemote2);
            if(alarmBeanRemote2.size()>0){
                return true;
            }
        }
        return false;
    }
    private boolean checkTimeSwitch(List<BattAlarmBean> alarmBeanswith90s,String timeSwitch){
        alarmBeanswith90s.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpId(), timeSwitch)).collect(Collectors.toList());
        Collections.sort(alarmBeanswith90s, Comparator.comparing(BattAlarmBean::getStartTime).reversed());
        BattAlarmBean battAlarmBean = alarmBeanswith90s.get(0);
        log.info("battAlarmBean {}",battAlarmBean);
        if(StringUtils.equals(battAlarmBean.getOmpValue(), "1")){
                return true;
        }
        return false;
    }
    private boolean checkAllPairTime(List<BattAlarmBean> alarmBeanTime,Long alarmraisedtime,String startStr1,String endStr1,String startStr2,String endStr2,
                                     String startStr3,String endStr3,String startStr4,String endStr4,String startStr5,String endStr5){
        if(checkPairTime(alarmBeanTime,alarmraisedtime,startStr1,endStr1)){
            return true;
        }
        if(checkPairTime(alarmBeanTime,alarmraisedtime,startStr2,endStr2)){
            return true;
        }
        if(checkPairTime(alarmBeanTime,alarmraisedtime,startStr3,endStr3)){
            return true;
        }
        if(checkPairTime(alarmBeanTime,alarmraisedtime,startStr4,endStr4)){
            return true;
        }
        if(checkPairTime(alarmBeanTime,alarmraisedtime,startStr5,endStr5)){
            return true;
        }
        return false;


    }
    private boolean checkPairTime(List<BattAlarmBean> alarmBeanTime,Long alarmraisedtime,String startStr,String endStr){

        //获取对应原始测点最新的值
        BattAlarmBean battAlarmBeanStart = filterNewAndSpecificAlarmBean(alarmBeanTime,startStr);
        BattAlarmBean battAlarmBeanEnd =filterNewAndSpecificAlarmBean(alarmBeanTime,endStr);
        //检验是否落在对应的时间段内
            if(checkTime(battAlarmBeanStart,battAlarmBeanStart,alarmraisedtime)){
                log.info("checkPairTime return true, startTimeOmpId,{},endTimeOmpId{}",startStr,endStr);
                return true;
            }

        return false;
    }
    private BattAlarmBean filterNewAndSpecificAlarmBean(List<BattAlarmBean> alarmBeanTime,String OmpId){
        //获取对应原始测点最新的值
        List<BattAlarmBean> alarmBeanTimeresult = alarmBeanTime.stream().filter(battAlarmBean -> StringUtils.equals(battAlarmBean.getOmpId(), OmpId)).collect(Collectors.toList());
        Collections.sort(alarmBeanTimeresult, Comparator.comparing(BattAlarmBean::getStartTime).reversed());
        BattAlarmBean battAlarmBean = alarmBeanTime.get(0);
        log.info("the newest battAlarmBean {}",battAlarmBean);
        return battAlarmBean;
    }

    private boolean checkTime(BattAlarmBean alarmBeanRemoteStart, BattAlarmBean alarmBeanRemoteEnd,Long alarmraisedtime) {

        Date date = new Date(alarmraisedtime);
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String dateString = format.format(date);
        try {
            Date currentTime = format.parse(dateString);
            Date startTime = format.parse(alarmBeanRemoteStart.getOmpValue());
            Date midTime = format.parse("23:59");
            Date dayTime = format.parse("00:00");
            Date endTime  = format.parse(alarmBeanRemoteEnd.getOmpValue());
            log.info("currentTime {}",currentTime);
            log.info("startTime {}",startTime);
            log.info("endTime {}",endTime);
            if(startTime.before(endTime)){
            if(currentTime.after(startTime) && currentTime.before(endTime)){
                log.info("checkTime return true");
                return true;
                }
            }else{
                if((currentTime.after(startTime) && currentTime.before(midTime)) || (currentTime.after(dayTime) && currentTime.before(endTime))){
                    return true;
                }

            }
            return false;
        } catch (ParseException e) {
            log.error("checkTime error, alarmBeanRemoteStart {},alarmBeanRemoteEnd{}",alarmBeanRemoteStart,alarmBeanRemoteEnd);
            return false;
        }
    }

    @NotNull
    public Double getCoefficient(List<Alarm> dcdpAndBattAndattPackList)
    {
        Long alarmcode = dcdpAndBattAndattPackList.get(0).getAlarmcode();
        Double coefficient = 0.0;
        switch (alarmcode + "")
        {
            case "50031030":
                coefficient = 2.0;
                break;
            case "50003005":
                coefficient = 1.5;;
                break;
            case "50008008":
            case "50031034":
                coefficient = 1.0;
                break;
            default:
                break;
        }
        return coefficient;
    }

    private void dealGroupMoc(Map<String, List<DeviceEntity>> mapMo, List<String> battPackList)
    {
        for (Entry<String, List<DeviceEntity>> m : mapMo.entrySet()) {
            String key = m.getKey();
            if (key.contains(AlarmTypeEnum.BATTPACK_08.getType())){
                battPackList.addAll(m.getValue().stream().map(DeviceEntity::getId).collect(Collectors.toList()));
            }
        }
    }


    private void getCommonAlarmCode(List<AlarmCode> alarmCodes, AlarmTypeEnum monitorObjectType, List<String> monitorObjectIds)
    {
        if(!CollectionUtils.isEmpty(monitorObjectIds))
        {
            alarmCodes.addAll(AlarmTypeEnum.getAlarmCodesByType(monitorObjectType.getType()));
        }
    }

    // search history alarm by moIdList
    public List<Alarm> getALarmListByMolist(List<String> idList,List<AlarmCode> alarmCodes,String startRaisedTime,String endRaisedTime,int sortdirection,String sortfield)
    {
        if(CollectionUtils.isEmpty(idList) || CollectionUtils.isEmpty(alarmCodes))
        {
            return new ArrayList<>();
        }
        List<Alarm> list = new ArrayList<>();
        try
        {
            AlarmBatchSchBean alarmBatchSchBean = new AlarmBatchSchBean();
            alarmBatchSchBean.setIds(idList);
            alarmBatchSchBean.setAlarmCodes(alarmCodes);
            alarmBatchSchBean.setAlarmRaisedTimeStart(startRaisedTime);
            alarmBatchSchBean.setAlarmRaisedTimeEnd(endRaisedTime);
            alarmBatchSchBean.setSortdirection(sortdirection);
            alarmBatchSchBean.setSortfield(sortfield);
            log.debug("alarmBatchSchBean:{} ",alarmBatchSchBean);
            list = alarmService.getAlarmDataBatch(alarmBatchSchBean);
        }
        catch (Exception e)
        {
            log.error("getALarmListByMolist:{} ",e.getMessage());
        }
        log.debug("list:{} ",list);
        return list;
    }

    public boolean isMaxCapacity(BackupPowerBean backupPowerBean, Alarm alarm, List<String> battPackList,List<String> reasons)
    {
        // 查询当前分组下所有电池的寿命最新评估结果
        String id = backupPowerBean.getId();
        try{
            Long acPowerOfflAlarmRaisedTime = alarm.getAlarmraisedtime();
            log.info("alarm is {}, arise time is {}", alarm, dateTimeService.getStrTime(acPowerOfflAlarmRaisedTime));
            List<BatteryBackupPowerBean> allbatteryBackupPowerBeans = new ArrayList<>();
            allbatteryBackupPowerBeans.addAll(backupPowerBean.getLiBatteryBackupPowerBeans());
            allbatteryBackupPowerBeans.addAll(backupPowerBean.getLeadAcidBatteryBackupPowerBeans());
            List<String> batteryMoIdList = allbatteryBackupPowerBeans.stream()
                    .map(BatteryBackupPowerBean::getId).filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
            Map<String, Integer> lifeMap = battLifeEvalDomain.getBattLifeMap(batteryMoIdList);
            log.info("[isMaxCapacity] [L={}] battery lifeMap {}", id, lifeMap);
            if (MapUtils.isEmpty(lifeMap)) {
                reasons.add(BatteryBackupPowerStateDetailEnum.NO_LIFE_DATA.getId());
                log.info("[isMaxCapacity] [L={}] No life data", id);
                return true;
            }
            // 查询所有告警信息的列表
            Map<String, List<Alarm>> allAlarmsMap = batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(batteryMoIdList);
            log.info("[isMaxCapacity] [L={}] battery allAlarmsMap {}", id, allAlarmsMap);
            // 获取满足条件的电池list
            List<BatteryBackupPowerBean> meetConditionsBattery = allbatteryBackupPowerBeans.stream()
                    .filter(e -> batteryMaximumChargingCapacityDomainImpl.isBatteryValid(e.getId(), lifeMap, allAlarmsMap)).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(meetConditionsBattery)) {
                log.info("[isMaxCapacity] [L={}] No meted battery, aborted!", id);
                return true;
            }
            List<HistoryAiBean> historyAiBeans = new ArrayList<>();
            List<String> moIds = meetConditionsBattery.stream().map(BatteryBackupPowerBean::getId)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            filterBatteryGetSOC(historyAiBeans,moIds,acPowerOfflAlarmRaisedTime);
            log.info("[isMaxCapacity] [L={}] historyAiBeans {}", id, historyAiBeans);
            Map<String, String> batteryHistoryAiMap = new HashMap<>();
            dealWithHistoryAiBeans(historyAiBeans, batteryHistoryAiMap);
            log.info("[isMaxCapacity] [L={}]batteryHistoryAiMap {}", id, batteryHistoryAiMap);
            for(BatteryBackupPowerBean batteryBackupPowerBean : meetConditionsBattery){
                String battId = batteryBackupPowerBean.getId();
                // 当前容量比率 SOC
                String batteryPrstSocStr = batteryHistoryAiMap.get(batteryBackupPowerBean.getId());
                boolean isParamValid = StringUtils.isNotBlank(batteryPrstSocStr);
                log.info("[isMaxCapacity] [L={}] [battId={}] batteryPrstSocStr={},isParamValid={}",id, battId, batteryPrstSocStr, isParamValid);
                if(isParamValid) {
                    BigDecimal prstSoc = new BigDecimal(batteryPrstSocStr);
                    // 数据必须大于0 不大于0 认为数据不合法 无法评估
                    boolean isValid = BigDecimal.ZERO.compareTo(prstSoc) < 0;
                    log.info("[isMaxCapacity] [L={}] [battId={}] prstSoc={},isPrstSocValid={}",id, battId, prstSoc, isValid);
                    if (isValid) {
                        if (NINETY_NINE.compareTo(prstSoc) > 0) {
                            //  SOC<99%
                            log.info("[[isMaxCapacity][L={}][battId={}] Get max capacity by soc<99%  {}", id, battId, prstSoc);
                            return true;
                        }
                    } else {
                        log.info("[isMaxCapacity] [L={}][battId={}] isMaxCapacityFromCap  prstSoc < 0 no valid",id, battId);
                        return isMaxCapacityFromCap(backupPowerBean,batteryBackupPowerBean,battPackList,acPowerOfflAlarmRaisedTime);
                    }
                } else{
                    log.info("[isMaxCapacity] [L={}][battId={}] isMaxCapacityFromCap no batteryPrstSocStr",id, battId);
                    return isMaxCapacityFromCap(backupPowerBean,batteryBackupPowerBean,battPackList,acPowerOfflAlarmRaisedTime);
                }
            }}catch (UedmException e){
            log.error("lose some data about calculating Maxsoc ");
        }
        return false;
    }

    private void dealWithHistoryAiBeans(List<HistoryAiBean> historyAiBeans, Map<String, String> batteryHistoryAiMap) {
        for(HistoryAiBean historyAiBean : historyAiBeans){
            batteryHistoryAiMap.put(historyAiBean.getResId(),historyAiBean.getCurValue());
        }
    }

    private List<HistoryAiBean> filterBatteryGetSOC(List<HistoryAiBean> historyAiBeans,
                                      List<String> moIds,Long acdpAndDistmains_alarmraisedtime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeBegin = sdf.format(new Date(acdpAndDistmains_alarmraisedtime-5*60*1000));
        String timeEnd = sdf.format(new Date(acdpAndDistmains_alarmraisedtime+5*60*1000));
        List<String> smpIds = new ArrayList<>();
        smpIds.add(StandPointOptional.BATTERY_SMPID_PRST_SOC.getId());
        List<SpIdDto> last = smpIds.stream().map(smpid -> new SpIdDto(smpid, GlobalConstants.LAST,null)).collect(Collectors.toList());
        // 一次查询1000条
        BatchUtils.doInBatch(1000, moIds, (item) -> {
            List<HistoryAiBean> idBatch = new ArrayList<>();
            try
            {
                idBatch =  pmaService.selectDataByCondition(item, MocOptional.BATTERY.getId(), last, timeBegin, timeEnd, DATA_TYPE_COLLECT, GlobalConstants.TIME_TYPE_MINUTE_WHOLE);
            }
            catch (Exception e)
            {
                throw new RuntimeException();
            }
            historyAiBeans.addAll(idBatch);
        });
        return historyAiBeans;
    }

    public boolean isMaxCapacityFromCap(BackupPowerBean backupPowerBean, BatteryBackupPowerBean battBackupPowerBean, List<String> battPackList, Long acdpAndDistmains_alarmraisedtime) {
        String spId = backupPowerBean.getId();
        String batteryMoId = getRecordMoId(battBackupPowerBean);
        try {
            String battPackId = battPackList.get(0);
            Map<String, Map<String, String>> smpDataMapOfBattPack = dataRedis.selectRealData(battPackId);
            Map<String, Map<String, String>> smpDataMapOfBatt = dataRedis.selectRealData(batteryMoId);
            //电池组充电系数
            String battPackCoeff = dataRedis.getSmpValueFromMap(smpDataMapOfBattPack, BATTPACK_CHG_COEFF);
            log.info("[isMaxCapacityFromCap][L={}][B={}] battPackCoeff={}", spId, batteryMoId, battPackCoeff);
            if (StringUtils.isBlank(battPackCoeff)) {
                return true;
            }
            Double coeff = Double.parseDouble(battPackCoeff);
            if (coeff == 0) {
                return true;
            }
            // 充放电状态
            String batteryChargeStatus = dataRedis.getSmpValueFromMap(smpDataMapOfBatt, StandPointOptional.BATTERY_SMPID_CHARGE_DISCHARGE_STATUS.getId());
            Date date = new Date(acdpAndDistmains_alarmraisedtime);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String endTime = sdf.format(date);
            log.info("[isMaxCapacityFromCap][L={}][B={}] battPack={} batteryChargeStatus={} battPackCoeff={}",
                    spId, batteryMoId, battPackId, batteryChargeStatus, battPackCoeff);
            //记录数据时长字段单位为min，充电系数是每小时，需做单位转换
            Double chargeTime = (1 / coeff) * 60;

            boolean isCharged3Hours = batteryMaximumChargingCapacityDomainImpl.isBatteryHoursCharge(batteryMoId, endTime, chargeTime);
            log.info("[isMaxCapacityFromCap][L={}][B={}] battPack={} batteryChargeStatus={}" +
                            " battPackCoeff={} chargeTime={} isCharged3Hours={}",
                    spId, batteryMoId, battPackId, batteryChargeStatus, battPackCoeff, chargeTime, isCharged3Hours);
            if (isCharged3Hours) {
                return false;
            } else {
                return true;
            }
        } catch (UedmException e) {
            log.error("[isMaxCapacityFromCap][L={}][B={}] Unable to determine charging time,e={}", spId, batteryMoId, e);
            return true;
        }
    }

    /**
     * 铁锂取父节点id，铅酸取自身id
     * @param battBackupPowerBean
     * @return
     */
    private String getRecordMoId(BatteryBackupPowerBean battBackupPowerBean)
    {
        if(BattTypeEnum.LFP.getCode().equals(battBackupPowerBean.getBattType()))
        {
            return battBackupPowerBean.getParentId();
        }
        return battBackupPowerBean.getId();
    }
}
