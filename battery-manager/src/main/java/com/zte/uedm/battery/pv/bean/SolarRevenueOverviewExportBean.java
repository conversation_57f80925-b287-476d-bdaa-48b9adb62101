package com.zte.uedm.battery.pv.bean;

import com.zte.uedm.common.bean.ImageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4
 **/
@Setter
@Getter
@ToString
@ApiModel(description = "导出太阳能收益总览")
public class SolarRevenueOverviewExportBean {
    /**
     *  图片信息
     */
    @ApiModelProperty(value = "图片信息")
    private List<ImageBean> images;
    /**
     *  逻辑组id
     */
    @ApiModelProperty(value = "逻辑组id")
    private String logicGroupId;
    /**
     *  粒度
     */
    @ApiModelProperty(value = "粒度")
    private String grain;
    /**
     *  是否历史记录
     */
    @ApiModelProperty(value = "是否历史记录")
    private String isHistory;
    /**
     *  开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String beginTime ;
    /**
     *  结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime ;
    /**
     *  排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String order;
    /**
     *  顺序
     */
    @ApiModelProperty(value = "顺序")
    private String sort;
    @ApiModelProperty(value = "行列导出格式")
    private String exportType;
}
