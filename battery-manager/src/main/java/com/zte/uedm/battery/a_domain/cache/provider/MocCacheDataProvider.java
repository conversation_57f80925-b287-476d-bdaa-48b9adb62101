package com.zte.uedm.battery.a_domain.cache.provider;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.MocEntity;
import com.zte.uedm.battery.a_domain.cache.conveter.MocConverter;
import com.zte.uedm.battery.a_domain.cache.conveter.ModelConverter;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.service.config.api.model.MocService;
import com.zte.uedm.service.config.api.model.ModelService;
import com.zte.uedm.service.config.api.model.vo.MocVo;
import com.zte.uedm.service.config.api.model.vo.ModelVo;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@ToString
@Service
@Slf4j
public class MocCacheDataProvider implements CacheDataProvider<String, MocEntity> {

    @Autowired
    private MocService mocService;

    @Autowired
    private ModelService modelService;

    @Override
    public List<MocEntity> getCacheDataForCacheProvider(Set<String> keys) throws UedmException {
        /* Started by AICoder, pid:ldc4c9e360tec58141530baee00b97255998c77f */
        Map<String, MocEntity> mocEntityMap = new HashMap<>();
        if (CollectionUtils.isEmpty(keys)) {
            // 查询系统级模型
            List<ModelVo> allModel = modelService.getAllModel();
            allModel.stream()
                    .map(ModelConverter.INSTANCE::modelRpcVoToVo)
                    .forEach(entity -> mocEntityMap.put(entity.getId(), entity));
            // 查询应用级模型
            List<MocVo> allMoc = mocService.getAllMoc();
            allMoc.stream()
                    .map(MocConverter.INSTANCE::mocRpcVoToVo)
                    .forEach(entity -> mocEntityMap.put(entity.getId(), entity));
        } else {
            // 查询系统级模型
            List<ModelVo> allModel = modelService.getAllModelByIds(new ArrayList<>(keys));
            allModel.stream()
                    .map(ModelConverter.INSTANCE::modelRpcVoToVo)
                    .forEach(entity -> mocEntityMap.put(entity.getId(), entity));
            // 查询应用级模型
            List<MocVo> allMoc = mocService.getAllMocByIds(new ArrayList<>(keys));
            allMoc.stream()
                    .map(MocConverter.INSTANCE::mocRpcVoToVo)
                    .forEach(entity -> mocEntityMap.put(entity.getId(), entity));
        }
        return new ArrayList<>(mocEntityMap.values());
        /* Ended by AICoder, pid:ldc4c9e360tec58141530baee00b97255998c77f */
    }

}
