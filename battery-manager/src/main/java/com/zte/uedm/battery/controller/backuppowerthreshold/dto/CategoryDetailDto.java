package com.zte.uedm.battery.controller.backuppowerthreshold.dto;

import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Getter
@Setter
@ToString
@Slf4j
@ApiModel(description = "分类详情")
public class CategoryDetailDto {
    @ApiModelProperty(value = "逻辑组标识")
    private String logicGroupId;
    @ApiModelProperty(value = "站点级别")
    private List<String> siteLevels;
    @ApiModelProperty(value = "")
    private List<String> powerSupplyScene;
    @ApiModelProperty(value = "排序字段")
    private String order;
    @ApiModelProperty(value = "顺序")
    private String sort;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页数量")
    private Integer pageSize;

    /**
     * 检查传入参数order以及sort是否可用
     */
    public void checkOrderAndSortAvailable() throws UedmException
    {
        //获取所有order的orderKey组成Set
        Set<String> orderKeys = BackupPowerThresholdOrderEnum.allOrderKeySet();
        Set<String> sortIds = SortEnum.getSortIds();
        log.info("CategoryDetailDto checkOrderAndSortAvailable -> orderKeys:{}",orderKeys);
        //order不为空
        if (StringUtils.isNotBlank(this.order))
        {
            //可选值范围内不包含此order
            if (!orderKeys.contains(this.order))
            {
                log.error("order is not in the range of optional values");
                throw new UedmException(-304,"order is not in the range of optional values");
            }
            //可选值范围包含此order，则获取对应的数据库中的values
            this.order = BackupPowerThresholdOrderEnum.getOrderValue(order);
        }
        else
        {
        // order为空的处理
            this.order = BackupPowerThresholdOrderEnum.getDefaultValue();
        }
        log.info("CategoryDetailDto checkOrderAndSortAvailable -> sortIds:{}",sortIds);
        //sort不为空
        if (StringUtils.isNotBlank(this.sort))
        {
            //sort值不在可选范围内
            if (!sortIds.contains(this.sort))
            {
                log.error("sort is not in the range of optional values");
                throw new UedmException(-304,"sort is not in the range of optional values");
            }
        }
        //sort为空
        else
        {
         // sort为空的处理
            this.sort=SortEnum.getAscSortID();
        }
    }

    /**
     * 排序字段可选值枚举量
     */
    @Slf4j
    enum BackupPowerThresholdOrderEnum
    {
        SITE_LEVEL("siteLevel","site_level"),
        POWER_SUPPLY_SCENE("powerSupplyScene","power_supply_scene"),
        THRESHOLD("threshold","threshold"),
        UPDATER("updater","updater"),
        GMT_MODIFIED("gmtModified","gmt_modified");

        private String orderKey;
        private String orderValue;

        BackupPowerThresholdOrderEnum(String orderKey, String orderValue) {
            this.orderKey = orderKey;
            this.orderValue = orderValue;
        }

        public String getOrderKey()
        {
            return orderKey;
        }
        public String getOrderValue(){ return orderValue; }

        /**
         * 获取默认order
         * @return
         */
        static String getDefaultValue(){
            return getOrderValue(SITE_LEVEL.orderKey);
//            return siteLevel.orderKey;
        }

        /**
         * 将所有枚举key组成set
         * @return
         */
        static Set<String> allOrderKeySet()
        {
            Set<String> orderKeySet = new HashSet<>();
            BackupPowerThresholdOrderEnum[] orderEnum = BackupPowerThresholdOrderEnum.values();
            for(BackupPowerThresholdOrderEnum thresholdOrderEnum: orderEnum)
            {
                orderKeySet.add(thresholdOrderEnum.getOrderKey());
            }
            return orderKeySet;
        }

        /**
         * 根据orderKey获取对应的orderValue
         * @param orderKey
         * @return
         */
        static String getOrderValue(String orderKey)
        {
            BackupPowerThresholdOrderEnum[] enums = BackupPowerThresholdOrderEnum.values();
            for (BackupPowerThresholdOrderEnum orderEnum:enums)
            {
                if (orderEnum.getOrderKey().equals(orderKey))
                {
                    return orderEnum.getOrderValue();
                }
            }
            return "";
        }

    }
}
