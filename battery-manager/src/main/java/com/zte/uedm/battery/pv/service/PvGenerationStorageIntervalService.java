package com.zte.uedm.battery.pv.service;

import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 太阳能发电量保存间隔设置，补采场景
 * @date 2023/9/6
 **/
public interface PvGenerationStorageIntervalService {
    /**
     * 批量设置太阳能站点下的监控设备的历史数据保存间隔
     *
     * @return int 已下发修改历史户数据保存间隔的监控设备的个数
     * @throws UedmException
     */
    int setPvGenerationStorageInterval() throws UedmException;

    /**
     * 批量设置太阳能站点下的监控设备的历史数据保存间隔
     *
     * @param list 监控设备列表
     * @throws UedmException
     */
    void sendMsgToDevice(List<MonitorDeviceBaseBean> list) throws UedmException;

    /**
     * 根据条件查询原始测点数据（mp-manager）
     * @param mdIds
     * @param ompIds
     * @return 《mdid,《ompid,listompdata》》
     * @throws UedmException
     */
    Map<String, Map<String, List<Object>>> queryOriginPointDataByCondition(List<String> mdIds, List<String> ompIds) throws UedmException;
}
