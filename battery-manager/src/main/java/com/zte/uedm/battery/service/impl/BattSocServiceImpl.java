package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.domain.BattSocDomain;
import com.zte.uedm.battery.service.BattSocService;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BattSocServiceImpl implements BattSocService
{
    @Autowired
    private BattSocDomain battSocDomain;

    @Override
    public PageInfo<IdNameBean> selectLevels(ServiceBaseInfoBean serviceBean) throws UedmException
    {
        return battSocDomain.selectSocLevels(serviceBean);
    }
}
