package com.zte.uedm.battery.controller.batterytesttask.vo;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DeviceSelectedInfoVo
{
    /**
     * 设备id
     */
    private String id;
    /**
     *设备名称
     */
    private String name;
    /**
     *位置
     */
    private String pathName;
    /**
     * 测试状态
     */
    private IdNameBean testStatus;
    /**
     *备电状态
     */
    private IdNameBean backupPowerStatus;
    /**
     *健康状态
     */
    private IdNameBean healthStatus;
    /**
     *是否被其他任务选择
     */
    private boolean selectedByOther;
    /**
     *是否被当前任务选择
     */
    private boolean selectedBySelf;
}
