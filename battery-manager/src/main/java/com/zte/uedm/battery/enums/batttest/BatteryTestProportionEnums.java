package com.zte.uedm.battery.enums.batttest;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum BatteryTestProportionEnums
{
    MANUAL("manual", "{\"zh_CN\":\"手动测试结果\",\"en_US\":\"Manual Test Result\"}","1"),
    AUTO("auto", "{\"zh_CN\":\"自动测试结果\",\"en_US\":\"Automatic Test Result\"}","2");

    private String id;
    private String name;
    private String showSequence;

    public String getId()
    {
        return this.id;
    }
    public String getName()
    {
        return this.name;
    }
    public String getShowSequence()
    {
        return this.showSequence;
    }

    BatteryTestProportionEnums(String id, String name, String showSequence)
    {
        this.id = id;
        this.name = name;
        this.showSequence = showSequence;
    }

    /**
     * 根据id获取名称
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BatteryTestProportionEnums[] arr = BatteryTestProportionEnums.values();
        for(BatteryTestProportionEnums enu : arr)
        {
            if (enu.getId().equals(id))
            {
                return enu.getName();
            }
        }
        return "";
    }

    /**
     * 取枚举值所有id
     * @return
     */
    public static Set<String> getAllIds()
    {
        BatteryTestProportionEnums[] arr = BatteryTestProportionEnums.values();
        List<BatteryTestProportionEnums> list = Arrays.asList(arr);
        Set<String> res = list.stream().map(BatteryTestProportionEnums::getId).collect(Collectors.toSet());
        return res;
    }
}
