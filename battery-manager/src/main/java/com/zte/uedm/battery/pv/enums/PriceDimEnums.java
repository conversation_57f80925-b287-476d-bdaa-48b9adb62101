package com.zte.uedm.battery.pv.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public enum PriceDimEnums {
    PRICETYPE("priceType", "{\"en_US\":\"Mode\",\"zh_CN\":\"类型\"}", null),
    NAME("name", "{\"en_US\":\"Name\",\"zh_CN\":\"名称\"}", null),
    EFFECTIVETIME("effectiveTime", "{\"en_US\":\"Time\",\"zh_CN\":\"时间\"}", null),
    TIPPRICE("tipPrice", "{\"en_US\":\"Tip\",\"zh_CN\":\"尖\"}", "¥"),
    PEAKPRICE("peakPrice", "{\"en_US\":\"Peak\",\"zh_CN\":\"峰\"}", "¥"),
    PLATPRICE("platPrice", "{\"en_US\":\"Flat\",\"zh_CN\":\"平\"}", "¥"),
    VALLEYPRICE("valleyPrice", "{\"en_US\":\"Valley\",\"zh_CN\":\"谷\"}", "¥");

    private String id;
    private String name;
    private String unit;

    public String getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getUnit() {
        return unit;
    }

    PriceDimEnums(String id, String name, String unit) {
        this.id = id;
        this.name = name;
        this.unit = unit;
    }

    /**
     * 获取价格维度所有ids
     *
     * @return 列表
     */
    public static List<String> getAllPriceDims() {
        List<String> allIds = new ArrayList<>();

        PriceDimEnums[] values = PriceDimEnums.values();
        for (PriceDimEnums value : values) {
            if (StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }

    /**
     * 根据id获取单位
     *
     * @param id
     * @return
     */
    public static String getUnitById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        PriceDimEnums[] values = PriceDimEnums.values();
        for (PriceDimEnums value : values) {
            if (value.id.equals(id)) {
                return value.getUnit();
            }
        }
        return null;
    }

    /**
     * 根据ID获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(String id) {
        if (StringUtils.isBlank(id)) {
            return "";
        }

        PriceDimEnums[] values = PriceDimEnums.values();
        for (PriceDimEnums value : values) {
            if (value.id.equals(id)) {
                return value.getName();
            }
        }
        return "";
    }
}
