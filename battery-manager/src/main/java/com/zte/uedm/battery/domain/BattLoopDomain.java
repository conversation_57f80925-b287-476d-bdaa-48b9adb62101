package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.MoBasicInfoVo;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

public interface BattLoopDomain
{
    /**
     * 获取关联的回路信息
     * @param idList
     * @return
     * @throws UedmException
     */
    Map<String, List<MoBasicInfoVo>> getRelationLoops(List<String> idList) throws UedmException;

    /**
     * 获取关联的电池信息信息
     * @param idList
     * @return
     * @throws UedmException
     */
    Map<String, List<MoBasicInfoVo>> getRelationBatts(List<String> idList) throws UedmException;
}
