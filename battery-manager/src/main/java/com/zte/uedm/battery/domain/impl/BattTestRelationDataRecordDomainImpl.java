package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPojo;
import com.zte.uedm.battery.domain.BattTestRelationDataRecordDomain;
import com.zte.uedm.battery.mapper.BattTestMapper;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BattTestRelationDataRecordDomainImpl implements BattTestRelationDataRecordDomain
{
    @Autowired
    private BattTestMapper battTestMapper;
    @Override
    public List<BattTestRelationDataRecordPojo> selectDataRecords(List<String> recordIds) throws UedmException
    {
        if(CollectionUtils.isEmpty(recordIds))
        {
            return new ArrayList<>();
        }
        try
        {
            List<BattTestRelationDataRecordPojo> relationDataRecordPos = battTestMapper.selectDataRecord(new ArrayList<>());
            Set<String> set = new HashSet<>(recordIds);
            List<BattTestRelationDataRecordPojo> collect = relationDataRecordPos.parallelStream().filter(bean -> set.contains(bean.getRecordId())).collect(Collectors.toList());
            return collect;
        }
        catch (Exception e)
        {
            log.error("BattTestRelationDataRecordDomainImpl -> selectDataRecords from DB is error.",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    @Override
    public Integer updateDataRecords(List<BattTestRelationDataRecordPojo> relationDataRecordPos) throws UedmException
    {
        try
        {
            Integer number = battTestMapper.updateBeans(relationDataRecordPos);
            return number;
        }
        catch (Exception e)
        {
            log.error("BattTestRelationDataRecordDomainImpl -> updateDataRecords is error.",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }
}
