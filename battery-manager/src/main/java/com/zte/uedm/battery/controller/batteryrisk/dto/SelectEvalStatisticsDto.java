package com.zte.uedm.battery.controller.batteryrisk.dto;

import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@ToString
public class SelectEvalStatisticsDto
{
    @NotBlank(message = "logicGroupId can not be blank")
    @ApiModelProperty(value = "逻辑分组id")
    private String logicGroupId;

    @NotBlank(message = "evaluateTimeStart can not be blank")
    @ApiModelProperty(value = "评估时间起")
    private String evaluateTimeStart;

    @NotBlank(message = "evaluateTimeEnd can not be blank")
    @ApiModelProperty(value = "评估时间止")
    private String evaluateTimeEnd;

    //非空校验
    public Pair<Boolean, Set<String>> checkNotEmpty()
    {
        Set<String> blankSets = new HashSet<>();
        Boolean result = true;
        ValidationResult validationResult = ValidationUtils.validateForDefalut(this);
        if(validationResult.isHasErrors())
        {
            result=false;
            blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
        }
        return Pair.of(result,blankSets);
    }

    /**
     * 两者有值,检查启用时间筛选范围
     */
    public boolean checkStartDateRange(){
        int i = evaluateTimeStart.compareTo(evaluateTimeEnd);
        //开始时间大于结束时间 i>0
        return i <= 0;
    }
}
