package com.zte.uedm.battery.a_interfaces.peakshift.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Setter
@Getter
@ToString
public class DeviceAccumulatedKwhGainVo {

    @ApiModelProperty(value = "监控设备ID")
    private String monitorDeviceId;

    @ApiModelProperty(value = "累计收益")
    private String totalBenefit;

    @ApiModelProperty(value = "充电量累积量")
    private String totalCharge;

    @ApiModelProperty(value = "放电量累积量")
    private String totalDischarge;

    /**
     * 累计充电量详情：key-strategy_type,value-累计充电量
     */
    @ApiModelProperty(value = "累计充电量详情")
    private Map<String, String> totalChargeDetail;

    /**
     * 累计放电量详情：key-strategy_type,value-累计放电量
     */
    @ApiModelProperty(value = "累计放电量详情")
    private Map<String, String> totalDischargeDetail;

    /**
     * 策略类型，0:低谷 1：平 2：峰 3：尖峰
     */
    @ApiModelProperty(value = "策略类型")
    private String StrategyType;

    @ApiModelProperty(value = "日期")
    private String dateStr;
}
