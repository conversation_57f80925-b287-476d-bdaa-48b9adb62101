package com.zte.uedm.battery.enums.record;

import com.zte.uedm.common.consts.batt.BattTestIndexDefineConstants;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ Author     ：10260977
 * @ Date       ：9:55 2022/8/9
 * @ Description：电池测试索引枚举
 * @ Modified By：
 * @ Version: 1.0
 */
@Slf4j
public enum BattSetTestIndexDefineEnums
{
    BEGINTIME("beginTime", "{\"zh_CN\":\"开始时间\",\"en_US\":\"Begin Time\"}",1,true, true,true,true,""),
    ENDTIME("endTime", "{\"zh_CN\":\"结束时间\",\"en_US\":\"End Time\"}",2,true, true,true,true,""),
    TESTSTATUS("testStatus", "{\"zh_CN\":\"测试状态\",\"en_US\":\"Test Status\"}",3,true, true,true,true,""),
    TESTTYPE("testType", "{\"zh_CN\":\"测试类型\",\"en_US\":\"Test Type\"}",4,true, true,true,true,""),
    BACKUP("backup", "{\"zh_CN\":\"本次测试预估备电时长\",\"en_US\":\"Backup power duration for this test\"}",5,true, true,true,true,"h"),
    BACKUP_AVG("avgBackup", "{\"zh_CN\":\"平均备电评估时长\",\"en_US\":\"Backup power duration average\"}",6,true, true,true,true,"h"),
    NAME("name", "{\"zh_CN\":\"电池名称\",\"en_US\":\"Battery Name\"}",7,true, false,true,true,""),
    TIME(BattTestIndexDefineConstants.BATT_TEST_TIME,"{\"zh_CN\":\"记录测试的起始和停止时间\",\"en_US\":\"Battery Test History Time\"}",8,true, false, true,true,""),
    DURATION(BattTestIndexDefineConstants.BATT_TEST_DURATION, "{\"zh_CN\":\"电池测试持续时间\",\"en_US\":\"Battery Test Duration\"}",9,true, false,true,true,""),
    INIT_SOC(BattTestIndexDefineConstants.BATT_TEST_INIT_SOC,"{\"zh_CN\":\"电池起始容量比率\",\"en_US\":\"Battery Initial SOC\"}", 10,false, false,true,true,"%"),
    FINAL_SOC(BattTestIndexDefineConstants.BATT_TEST_FINAL_SOC, "{\"zh_CN\":\"电池终止容量比率\",\"en_US\":\"Battery Final SOC\"}",11,true, false,true,true,"%"),
    CHANGED_SOC(BattTestIndexDefineConstants.BATT_TEST_CHANGED_SOC, "{\"zh_CN\":\"电池变化容量比率\",\"en_US\":\"Battery Changed SOC\"}",12,false, false,true,true,"%");

    BattSetTestIndexDefineEnums(String id, String name, Integer defaultShowSequence, boolean defaultSelected, boolean canModified, boolean sortable, boolean assetAttributeShow, String unit)
    {
        this.id = id;
        this.name = name;
        this.defaultShowSequence = defaultShowSequence;
        this.defaultSelected = defaultSelected;
        this.canModified = canModified;
        this.sortable = sortable;
        this.assetAttributeShow = assetAttributeShow;
        this.unit= unit;
    }

    private String id;
    private String name;
    private Integer defaultShowSequence;
    private boolean defaultSelected;
    private boolean canModified;
    private boolean sortable;
    private boolean assetAttributeShow;
    private String unit;

    public String getName() {
        return name;
    }

    public String getId()
    {
        return this.id;
    }
    public Integer getDefaultShowSequence()
    {
        return this.defaultShowSequence;
    }

    public boolean isDefaultSelected() {
        return defaultSelected;
    }

    public boolean isCanModified() {
        return canModified;
    }

    public boolean isSortable() {
        return sortable;
    }

    public boolean isAssetAttributeShow() {
        return assetAttributeShow;
    }

    public boolean getDefaultSelected()
    {
        return this.defaultSelected;
    }
    public boolean getCanModified()
    {
        return this.canModified;
    }
    public String getUnit()
    {
        return this.unit;
    }

    public static String getTimeId()
    {
        return TIME.id;
    }


    public static String getInitSocId()
    {
        return INIT_SOC.id;
    }

    public static String getFinalSocId()
    {
        return FINAL_SOC.id;
    }



    public static String getDurationId()
    {
        return DURATION.id;
    }

    public static String getBackUpId()
    {
        return BACKUP.id;
    }
    public static String getBackUpAvgId()
    {
        return BACKUP_AVG.id;
    }

















    /**
     * 获取枚举量的所有id
     *
     * @return
     */
    public static List<String> getAllIds()
    {
        List<String> enumIds = new ArrayList<>();
        BattSetTestIndexDefineEnums[] arr = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums enu : arr)
        {
            enumIds.add(enu.getId());
        }
        return enumIds;
    }
    public static void checkIdIsNeeded(List<String> ids) throws UedmException
    {
        if(CollectionUtils.isEmpty(ids))
        {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim ids is empty!");
            throw new UedmException(-200, "Dim ids is empty!");
        }
        ids.removeAll(BattSetTestIndexDefineEnums.getAllIds());
        if(ids.size() > 0)
        {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim dim id beyond limit!");
            throw new UedmException(-3042, "Dim id beyond limit");
        }
    }


    /**
     * 返回枚举值 - defaultShowSequence
     *
     * @param id
     * @return
     */
    public static Integer getDefaultShowSequenceById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.getDefaultShowSequence();
            }
        }
        return 0;
    }

    public static boolean getAssetAttributeShowById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.isAssetAttributeShow();
            }
        }
        return true;
    }
    /**
     * 返回枚举值 - 根据id获取单位
     *
     * @param id
     * @return
     */
    public static String getUnitById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.getUnit();
            }
        }
        return "";
    }

    /**
     * 根据id获取名称
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.getName();
            }
        }
        return "";
    }
    /**
     * 返回枚举值 - getDefaultSelected
     *
     * @param id
     * @return
     */
    public static boolean getDefaultSelectedById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.getDefaultSelected();
            }
        }
        return false;
    }

    /**
     * 返回枚举值 - getDefaultSelected
     *
     * @param id
     * @return
     */
    public static boolean getCanModifiedById(String id)
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        for (BattSetTestIndexDefineEnums value : values)
        {
            if (value.getId().equals(id))
            {
                return value.getCanModified();
            }
        }
        return false;
    }

    public static List<BattSetTestIndexDefineEnums> getAllTestIndexDefineEnums()
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        return Arrays.asList(values);
    }

    public static Map<String, BattSetTestIndexDefineEnums> getAllCannotModifyEnums()
    {
        BattSetTestIndexDefineEnums[] values = BattSetTestIndexDefineEnums.values();
        return Arrays.stream(values).filter(BattSetTestIndexDefineEnums::getCanModified).filter(bean -> StringUtils.isNotBlank(bean.getId())).
                collect(Collectors.toMap(BattSetTestIndexDefineEnums::getId, Function.identity(), (key1, key2)->key2));
    }

}
