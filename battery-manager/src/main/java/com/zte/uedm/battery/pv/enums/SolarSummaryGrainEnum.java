package com.zte.uedm.battery.pv.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/09/28
 */
public enum SolarSummaryGrainEnum {
    // 时间粒度
    DAY("day", "hour"),
    MONTH("month", "day"),
    YEAR("year", "month"),
    ALL("all", "year");

    /**
     * 粒度类型
     */
    private String code;

    /**
     * 表名
     */
    private String tableName;

    public String getCode() {
        return this.code;
    }

    public String getTableName() {
        return this.tableName;
    }

    SolarSummaryGrainEnum(String code, String tableName) {
        this.code = code;
        this.tableName = tableName;
    }

    /**
     * 根据code查询表名
     *
     * @param code
     * @return
     */
    public static String getTableNameByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        SolarSummaryGrainEnum[] enums = SolarSummaryGrainEnum.values();
        for (SolarSummaryGrainEnum grainEnum : enums) {
            if (grainEnum.getCode().equals(code)) {
                return grainEnum.getTableName();
            }
        }
        return null;
    }

    /**
     * 根据统计编号获取对应枚举实例
     *
     * @param code 编码
     * @return 枚举
     */
    public static SolarSummaryGrainEnum getEnum(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (SolarSummaryGrainEnum result : SolarSummaryGrainEnum.values()) {
            if (result.code.equals(code)) {
                return result;
            }
        }
        return null;
    }
}
