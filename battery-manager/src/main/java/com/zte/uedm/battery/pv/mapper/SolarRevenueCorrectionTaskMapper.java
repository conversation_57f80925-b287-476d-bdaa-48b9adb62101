package com.zte.uedm.battery.pv.mapper;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectionTaskPO;
import com.zte.uedm.battery.pv.dto.SolarCorrectTaskQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 太阳能收益矫正任务mapper
 *
 * <AUTHOR>
 * @version 1.0, 2023/11/24 10:32
 */
@Mapper
public interface SolarRevenueCorrectionTaskMapper {

    /**
     * 根据矫正任务状态获取任务数量
     *
     * @param taskStatus :
     * @return java.lang.Integer
     * <AUTHOR> 2023/11/28 - 上午10:48
     **/
    Integer getNumberOfTaskByTaskStatus(@Param("taskStatus") String taskStatus) ;

    /**
     * 获取当日矫正站点数量
     *
     * @return java.lang.Integer
     * <AUTHOR> 2023/11/28 - 上午10:48
     **/
    Integer getNumberOfSitesToday();

    /**
     * 插入矫正任务
     *
     * @param solarRevenueCorrectionTaskPo
     * @return int
     * <AUTHOR>
     * @date 2023-11-27
     **/
    Integer insert(SolarRevenueCorrectionTaskPO solarRevenueCorrectionTaskPo);

    /**
     * 更新矫正任务状态
     * @param solarRevenueCorrectionTaskPo
     * @return
     */
    Integer updateSolarRevenueCorrectionTask(SolarRevenueCorrectionTaskPO solarRevenueCorrectionTaskPo);

    /**
     * 查询所有待执行的任务
     * @return
     */
    List<SolarRevenueCorrectionTaskPO> selectSolarRevenueCorrectionTaskByStatus(@Param("taskStatus") String taskStatus);


    List<SolarRevenueCorrectionTaskPO> selectSolarRevenueCorrectionTaskByPage(SolarCorrectTaskQueryDto solarCorrectTaskQueryDto);
}
