package com.zte.uedm.battery.pv.dto;

import com.zte.uedm.battery.pv.bean.SolarRevenueBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class SolarRevenueUpdateStrategyDto {
    /**
     * pvId
     */
    private String pvId;
    /**
     * 太阳能策略id
     */
    private String solarScopeStrategyId;
    /**
     * 市电策略id
     */
    private String gridScopeStrategyId;
    /**
     * 档位
     */
    private Integer tiered;
    /**
     * 计费模式
     */
    private Integer rateType;
}
