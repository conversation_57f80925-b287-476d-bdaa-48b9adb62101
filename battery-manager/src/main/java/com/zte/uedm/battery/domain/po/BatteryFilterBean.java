package com.zte.uedm.battery.domain.po;


import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.battery.controller.batteryrisk.vo.RiskDetailObjValue;
import com.zte.uedm.battery.controller.batthealth.vo.SohDetailObjValue;
import com.zte.uedm.battery.rpc.vo.BattOverviewAssetConditionVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@ToString
public class BatteryFilterBean
{

    /**
     * 电池id
     */
    private String id;
    /**
     * 名称
      */
    private String name;

    // 来源于建模
    /**
     * 位置
     */
    private String position;
    /**
     * 位置id
     */
    private String pathId;


    // 来源于标准测点
    /**
     * 健康状态
     */
    private String soh;
    /**
     * 当前容量
     */
    private Double soc;
    /**
     * 充放电状态（运行状态）
     */
    private String chargeStatus;

    /**
     * 剩余放电时长
     */
    private String remainDischargeDuration;

    /**
     * 电压
     */
    private Double volt;
    /**
     * 电流
     */
    private Double curr;
    /**
     * 温度
     */
    private Double temp;
    /**
     * 健康度
     */
    private Double health;

    /**
     * 剩余寿命描述字符串
     */
    private String leftLife;
    /**
     * 剩余寿命数字,月为单位，integer类型
     */
    private Integer leftLifeInteger;

    /**
     * 剩余寿命来源
     */
    private String leftLifeSource;

    /**
     * 剩余寿命评估规则信息(用于前端展示)
     */
    private String leftLifeEvalInfo;

    // 来源于平台告警
    /**
     * 告警信息
     */
    private List<AlarmDTO> alarms = new ArrayList<>();
    /**
     * 告警等级
     */
    private Integer alarm;


    // 来源于资产
    /**
     * 电池资产id
     */
    private String assetNumber;
    /**
     * 电池种类
     */
    private String type;
    /**
     * 电池额定容量
     */
    private String ratedCap;
    /**
     * 电池供应商
     */
    private String supplier;
    /**
     * 电池制造商
     */
    private String manufacturer;
    /**
     * 电池品牌
     */
    private String brand;
    /**
     * 电池系列
     */
    private String series;
    /**
     * 电池型号
     */
    private String model;
    /**
     * 电池启用日期
     */
    private String startDate;
    /**
     * 风险等级
     */
    private String riskLevel;
    /**
     * 风险详情
     */
    private List<RiskDetailObjValue> riskDetail;
    /**
     * 电池健康状态详情
     */
    private SohDetailObjValue sohDetail;;
    /**
     * soh评估来源
     */
    private String sohSource;

    public BatteryFilterBean(){}

    public BatteryFilterBean(String id)
    {
        this.id = id;
    }

    public BatteryFilterBean(BattFilterMonitorBean monitorBean, BattOverviewAssetConditionVo assetBean)
    {
        this.id = monitorBean.getId();
        this.name = monitorBean.getName();
        this.position = monitorBean.getPosition();
        this.soh = monitorBean.getSoh();
        this.soc = monitorBean.getSoc();
        this.chargeStatus = monitorBean.getChargeStatus();
        this.volt = monitorBean.getVolt();
        this.curr = monitorBean.getCurr();
        this.temp = monitorBean.getTemp();
        this.health = monitorBean.getHealth();
        this.leftLife = monitorBean.getLeftLife();
        this.leftLifeInteger = monitorBean.getLeftLifeInteger();
        this.leftLifeSource = monitorBean.getLeftLifeSource();
        this.leftLifeEvalInfo = monitorBean.getLeftLifeEvalInfo();
        this.riskLevel = monitorBean.getRiskLevel();

        this.alarms = monitorBean.getAlarms();
        this.alarm = monitorBean.getAlarm();
        this.riskDetail = monitorBean.getRiskDetail();
        this.sohDetail = monitorBean.getSohDetail();
        this.sohSource=monitorBean.getSohSource();
        if (assetBean != null)
        {
            this.assetNumber = assetBean.getAssetNumber();
            this.ratedCap = assetBean.getRatedCap();
            this.supplier = assetBean.getSupplier();
            this.manufacturer = assetBean.getManufacture();
            this.brand = assetBean.getBrand();
            this.series = assetBean.getSeries();
            this.model = assetBean.getModel();
            this.startDate = assetBean.getStartDate();
        }
    }
}
