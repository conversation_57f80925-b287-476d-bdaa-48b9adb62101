package com.zte.uedm.battery.controller.batttest.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @ Author     ：10260977
 * @ Date       ：10:32 2022/8/10
 * @ Description：电池记录索引维度
 * @ Modified By：
 * @ Version: 1.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class BattRecordIndexDimVo
{
    /**
     * 记录属性id
     */
    @ApiModelProperty("测试历史维度key")
    private String id;

    /**
     * 记录属性名称-国际化
     */
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("用户名")
    private String userName;
    /**
     * 展示顺序
     */
    @ApiModelProperty("展示顺序")
    private Integer sequence;

    @ApiModelProperty("是否启用")
    private Boolean enable;
    @ApiModelProperty("默认顺序")
    private Integer defaultIndex;

    /**
     * 默认是否被勾选
     */
    @ApiModelProperty("默认是否启动")
    private boolean defaultEnable;

    /**
     * 是否固定
     */
    @ApiModelProperty("是否固定")
    private boolean defaultFixed;

    /**
     * 支持排序
     */
    @ApiModelProperty("支持排序")
    private boolean sortable;
    @ApiModelProperty("资产属性")
    private boolean assetAttributeShow;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;
}
