package com.zte.uedm.battery.a_infrastructure.repository.peakshift.coverter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.TemplateStrategyDetailPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:290ea025954c4321a54e12cffad7ba4a */
@Mapper
public interface TemplateStrategyDetailPoConverter {
    TemplateStrategyDetailPoConverter INSTANCE = Mappers.getMapper(TemplateStrategyDetailPoConverter.class);

    @Mappings({})
    TemplateStrategyDetailEntity templateStrategyDetailPoToTemplateStrategyDetailEntity(TemplateStrategyDetailPo templateStrategyDetailPo);

    List<TemplateStrategyDetailEntity> listTemplateStrategyDetailPoToTemplateStrategyDetailEntity(List<TemplateStrategyDetailPo> poList);

    @Mappings({})
    TemplateStrategyDetailPo templateStrategyDetailEntityToTemplateStrategyDetailPo(TemplateStrategyDetailEntity templateStrategyDetailEntity);

    @Mappings({})
    List<TemplateStrategyDetailPo> listTemplateStrategyDetailEntityToTemplateStrategyDetailPo(List<TemplateStrategyDetailEntity> entityList);
}
/* Ended by AICoder, pid:290ea025954c4321a54e12cffad7ba4a */
