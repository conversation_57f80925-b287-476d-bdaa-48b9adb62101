package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

/**
 * 电池品牌业务层
 */
public interface BattBrandService
{
    /**
     * 搜索电池的品牌信息
     * @param name - 模糊匹配
     * @param serviceBean - 分页/国际化
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> search(String name, ServiceBaseInfoBean serviceBean) throws UedmException;

}
