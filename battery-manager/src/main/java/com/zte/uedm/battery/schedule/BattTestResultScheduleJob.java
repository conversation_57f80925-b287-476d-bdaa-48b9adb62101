package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.service.BattTestRecordResultUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 电池测试记录回写定时任务
 */
@Component
@Slf4j
public class BattTestResultScheduleJob
{
    @Autowired
    private BattTestRecordResultUpdateService battTestResultUpdateService;

    @Scheduled(cron = "0 0 6 * * ?") //每天的凌晨6点触发,备电、健康评估之后
    public void execute()
    {
        try
        {
            log.info("BatteryTestResultScheduleJob execute job *****************begin.********************");
            battTestResultUpdateService.battTestRecordResultUpdateJob();
            log.info("BatteryTestResultScheduleJob execute job *****************end.********************");
        }
        catch (Exception e)
        {
            log.error("BatteryTestResultScheduleJob execute occur exception:{}", e);
        }
    }
}
