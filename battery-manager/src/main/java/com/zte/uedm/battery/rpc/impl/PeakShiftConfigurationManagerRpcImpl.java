package com.zte.uedm.battery.rpc.impl;

import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.bean.PathInfoBean;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PeakShiftConfigurationManagerRpcImpl
{
    @Autowired
    private ConfigurationRpc configurationRpc;

    @Autowired
    private JsonService jsonService;
    @Autowired
    private ResourceBaseCacheManager resourceBaseCacheManager;

    public Map<String, PathInfoBean> getAllLogicGroupPathsByIdS(List<String> idList) throws UedmException {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        List<ResourceBaseEntity> all = resourceBaseCacheManager.getAllResourceBase();
        if (CollectionUtils.isEmpty(all)) {
            return new HashMap<>();
        }
        Map<String, ResourceBaseEntity> map = all.stream().collect(Collectors.toMap(ResourceBaseEntity::getId, Function.identity(),
                (oldValue, newValue) -> newValue));
        Map<String, PathInfoBean> result = new HashMap<>();
        for (String id : idList) {
            ResourceBaseEntity resourceBaseEntity = map.get(id);
            if (null != resourceBaseEntity) {
                String[] pathId = resourceBaseEntity.getPathId();
                StringBuilder strBuffer = new StringBuilder();
                for (String s : pathId) {
                    ResourceBaseEntity resourceBase = map.get(s);
                    if (null != resourceBase && null != resourceBase.getName()) {
                        strBuffer.append(resourceBase.getName()).append(">");
                    }
                }
                String pathName = strBuffer.substring(0, strBuffer.length() - 1);
                PathInfoBean pathInfoBean = new PathInfoBean();
                pathInfoBean.setId(id);
                pathInfoBean.setNamePath(pathName);
                pathInfoBean.setIdPath(resourceBaseEntity.toStringPathId());
                result.put(id, pathInfoBean);
            }
        }
        return result;
    }





}
