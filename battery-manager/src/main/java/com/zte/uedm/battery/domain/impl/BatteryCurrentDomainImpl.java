package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.BatteryCurrentPojo;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BatteryCurrentDomain;
import com.zte.uedm.battery.domain.BatteryCurrentStorageDomin;
import com.zte.uedm.battery.domain.LeadAcidBatteryDomain;
import com.zte.uedm.battery.enums.AlarmTypeEnum;
import com.zte.uedm.battery.enums.record.BattAlarmConstant;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_CURR;
import static com.zte.uedm.battery.api.BattConst.*;

@Service
@Slf4j
public class BatteryCurrentDomainImpl implements BatteryCurrentDomain {
    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5);
    private static final String ALARM_RAISED_TIME = "alarmraisedtime";
    private static final String df = "yyyy-MM-dd HH:mm:ss";
    @Autowired
    private LeadAcidBatteryDomain leadAcidBatteryDomain;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private PmaServiceUtils pmaService;
    @Autowired
    private BatteryCurrentStorageDomin batteryCurrentStorageDomin;
    @Autowired
    private BatteryMaximumChargingCapacityDomainImpl batteryMaximumChargingCapacityDomainImpl;
    @Autowired
    private BattLifeEvalDomain battLifeEvalDomain;
    @Autowired
    private LeadAcidBatteryDomainImpl leadAcidBatteryDomainImpl;
    @Autowired
    private BackupPowerHistoryAlarmDomainImpl backupPowerHistoryAlarmDomain;
    @Autowired
    private ConfigurationManagerRpcImpl cfgRpc;
    @Autowired
    private BattTypeDomainImpl battTypeDomainImpl;

    @Autowired
    private JsonService jsonService;

    @Override
    public Integer countBatteryCurrent(List<MoObjectConfiguration> moObjectConfigurations, String mocType) {

        List<BatteryCurrentPojo> batteryCurrentPojoList = new ArrayList<>();
        Map<String, String> timeMap = new HashMap<>();
        Map<String, String> timeMapMonth = new HashMap<>();
        try {
            for (MoObjectConfiguration moObjectConfiguration : moObjectConfigurations) {
                if (null == moObjectConfiguration || StringUtils.isEmpty(moObjectConfiguration.getId())) {
                    continue;
                }
                log.info("Calculate the average current value of this site {} within a week", moObjectConfiguration.getId());
                
                if(mocType.equals(MocOptional.DC_POWER.getId())){
                    //查询一周之内告警(一次下电，交流停电，分路下电)
                    List<DeviceEntity> moListBySp = leadAcidBatteryDomainImpl.getMoListBySp(moObjectConfiguration.getId());
                     timeMap = getSiteAlrmTimeByMoList(moObjectConfiguration.getId(), moListBySp);
                    //查询一个月前到一周前之间的告警（一次下电，交流停电，分路下电）
                     timeMapMonth = getSiteAlrmTimeByMoListFromMonth(moObjectConfiguration.getId(), moListBySp);
                }

                //查询下属所有电池信息
                List<HistoryAiBean> historyAiBeans = new ArrayList<>();
                List<DeviceEntity> batteryMoObjectBeans = leadAcidBatteryDomain.getMoListBySpAndMoc(moObjectConfiguration.getId(), MocOptional.BATTERY.getId());

                List<DeviceEntity> batteryMoObjectBeanlist = filterLoopBatter(batteryMoObjectBeans);

                if (batteryMoObjectBeanlist == null || batteryMoObjectBeanlist.isEmpty()) {
                    log.info("this site don't have any battery");
                    continue;
                }
                log.info("After filter Loop, the battery size changed,:{} --- {}",batteryMoObjectBeans.size(),batteryMoObjectBeanlist.size());
                List<String> moIds = batteryMoObjectBeanlist.stream().map(DeviceEntity::getId)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                log.info("Count the batteries associated with this site {} {} ", moIds.size(), moIds);
                obtainCurrent(historyAiBeans, moIds, timeMap,timeMapMonth);


                Map<String, BigDecimal> batteryHistoryAiMap = new HashMap<>();
                dealWithHistoryAiBeans(historyAiBeans, batteryHistoryAiMap);

                DeviceEntity monitorBean = new DeviceEntity();
                monitorBean.setId(moObjectConfiguration.getId());
                batteryMoObjectBeanlist.add(monitorBean);

                for (DeviceEntity monitorObjectBean : batteryMoObjectBeanlist) {
                    BigDecimal avgCurrent = batteryHistoryAiMap.get(monitorObjectBean.getId());
                    //构建单个数据入库bean
                    BatteryCurrentPojo batteryCurrentPojo = createBatteryBackupPowerEvalPojoBean(moObjectConfiguration, monitorObjectBean, avgCurrent, mocType);
                    batteryCurrentPojoList.add(batteryCurrentPojo);
                }

            }
            if (CollectionUtils.isNotEmpty(batteryCurrentPojoList)) {
                //插入周表
                batteryCurrentStorageDomin.addBattCurrWResult(batteryCurrentPojoList);
            }


        } catch (UedmException e) {
            throw new RuntimeException(e);
        }
        return batteryCurrentPojoList.size();

    }

    public Map<String, String> getSiteAlrmTimeByMoListFromMonth(String id, List<DeviceEntity> moList) {
        log.info("-------Count the alarms generated by this site within the last month-------- {}  ", id);
        Map<String, String> timeMap = new HashMap<>();
        List<String> dcpowerList = new ArrayList<>(Collections.singleton(id));
        Map<String, List<DeviceEntity>> mapMo = moList.stream()
                .collect(Collectors.groupingBy(DeviceEntity::getMoc));

        List<String> battPackList = new ArrayList<>();
        List<String> dcdpAndBattPackMoList = new ArrayList<>();
        // group by moc
        dealGroupMoc(mapMo,  battPackList);
        log.info("dcpowerList: {}, ,battPackList: {} ",dcpowerList, battPackList);
        //检查该站点下无交流停电和一次下电告警的监控对象
        if (CollectionUtils.isEmpty(dcpowerList)  && CollectionUtils.isEmpty(battPackList)) {
            log.info("There are no monitoring objects generating alarms at this site,Unable to remove current. ");
            return timeMap;
        }
        // 交流电停电告警和告警码list
        List<Alarm> acdpAlarmList;
        List<AlarmCode> alarmAcdpCodes = new ArrayList<>();
        // 一次下电告警和告警码list
        dcdpAndBattPackMoList.addAll(battPackList);
        dcdpAndBattPackMoList.addAll(dcpowerList);
        List<AlarmCode> alarmDcdpAndBattPackCodes = new ArrayList<>();
        //分路下电告警和告警码 list
        List<AlarmCode> alarmDcdpBranchCodes = new ArrayList<>();
        //塞入所需要告警的告警码
        getAlarmCodes(alarmDcdpBranchCodes, alarmAcdpCodes, alarmDcdpAndBattPackCodes);
        String endTime = before1WeekOfNow();
        String startTime = before1MonthOfNow();
        log.debug("startTime,endTime{}{}",startTime,endTime);
        //查询一月之内交流停电的实时与历史告警
        acdpAlarmList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcpowerList, alarmAcdpCodes, startTime,
                endTime, 0, ALARM_RAISED_TIME);
        if(acdpAlarmList.isEmpty()){
            log.info("None AC power outage alarms within a month ");
            log.info("timeMapMonth1 {}", timeMap);
            return timeMap;
        }
        Collections.sort(acdpAlarmList, Comparator.comparing(Alarm::getAlarmraisedtime).reversed());
        log.info("Number of AC power outage alarms within a month {}", acdpAlarmList.size());
        //距离七天前最近的一次交流停电
        Alarm newAlarm = acdpAlarmList.get(0);
        //根据该交流停电告警的产生与清除时间查找期间产生的一次下电
        log.info("Detailed information on AC power outage alarms exchanged within a month {}{}{}", newAlarm.getAlarmshowtime(), newAlarm.getPositionname(), newAlarm.getAlarmclearedtime());
        long acdpRaisedTime = newAlarm.getAlarmraisedtime();
        String alarmEndTime;
        String alarmStartTime = dateTimeService.getStrMillisecondTime(acdpRaisedTime);
        if (newAlarm.getAlarmclearedtime() != null) {
            alarmEndTime = dateTimeService.getStrMillisecondTime(newAlarm.getAlarmclearedtime());
        } else {
            alarmEndTime = dateTimeService.getCurrentTime();
        }
        //判断交流停电恢复时间是否在七天内
        try {
            SimpleDateFormat format = new SimpleDateFormat(df);
            Date alarmEndDate = format.parse(alarmEndTime);
            Date endTimeDate = format.parse(endTime);
            if(alarmEndDate.before(endTimeDate)){
                log.info("no need to consider this alarm before one week,alarmEndDate before endTimeDate {} < {}",alarmEndDate,endTimeDate);
                log.info("timeMapMonth2 {}", timeMap);
                return timeMap;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        timeMap = getTimeMonthMap(dcpowerList, dcdpAndBattPackMoList, alarmDcdpAndBattPackCodes,
                alarmDcdpBranchCodes, endTime, alarmEndTime, alarmStartTime);
        log.info("timeMapMonth {}", timeMap);
        return timeMap;
    }


    private Map<String, String> getTimeMonthMap(List<String> dcdpList, List<String> dcdpAndBattPackMoList, List<AlarmCode> alarmDcdpAndBattPackCodes,
                                                   List<AlarmCode> alarmDcdpBranchCodes, String endTime, String alarmEndTime, String alarmStartTime) {
        List<Alarm> dcdpBranchList;
        List<Alarm> dcdpAndBattPackList;
        Map<String, String> timeMap = new HashMap<>();
                //查询交流停电产生后的一次下电告警(非智能)
        dcdpAndBattPackList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcdpAndBattPackMoList, alarmDcdpAndBattPackCodes, alarmStartTime,
                alarmEndTime, 0, ALARM_RAISED_TIME);

        //查询交流停电产生后的一次下电告警(智能)
        dcdpBranchList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcdpList, alarmDcdpBranchCodes, alarmStartTime,
                alarmEndTime, 0, ALARM_RAISED_TIME);
        //交流停电产生后到交流停电恢复期间产生的所有一次下电告警
        List<Alarm> powerDownAlarms = new ArrayList<>();
        powerDownAlarms.addAll(dcdpAndBattPackList);
        powerDownAlarms.addAll(dcdpBranchList);
        if (CollectionUtils.isEmpty(powerDownAlarms)) {
            log.info("No powerDown Alarm, There is no need to remove the current");
            return timeMap;
        } else {
            Collections.sort(powerDownAlarms, Comparator.comparing(Alarm::getAlarmraisedtime));
            Alarm powerDownAlarm = powerDownAlarms.get(0);
            log.info("Detailed information on power down alarms exchanged within this AC alarm time Range {}{}{}", powerDownAlarm.getAlarmshowtime(), powerDownAlarm.getPositionname(), powerDownAlarm.getAlarmclearedtime());
            String startCurrTime = dateTimeService.getStrMillisecondTime(powerDownAlarm.getAlarmraisedtime());
            //判断一次下电产生是否发生在七天内
            try {
                SimpleDateFormat format = new SimpleDateFormat(df);
                Date startCurrDate = format.parse(startCurrTime);
                Date endTimeDate = format.parse(endTime);
                if(startCurrDate.before(endTimeDate)){
                    log.info("this alarm before one week,startCurrDate before endTimeDate {} < {}",startCurrDate,endTimeDate);
                    timeMap.put(endTime, alarmEndTime);
                    log.info("timeMapMonth3 {}", timeMap);
                    return timeMap;
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            timeMap.put(startCurrTime, alarmEndTime);
            return timeMap;
        }

    }

    public List<DeviceEntity> filterLoopBatter(List<DeviceEntity> batteryMoObjectBeans) {
        List<DeviceEntity> batteryMoObjectBeanlist = new ArrayList<>();
        try {
            List<String> ids = batteryMoObjectBeans.stream().map(DeviceEntity -> DeviceEntity.getId()).collect(Collectors.toList());
            List<IdNameBean> allBattery = cfgRpc.selectAllBatteryExtendAttribute(ids);
            log.info("createSPBackupPowerBean ->  allBattery size is {}", allBattery.size());
            log.debug("createSPBackupPowerBean ->  allBattery is {}", allBattery);

            List<IdNameBean> idNameResult = battTypeDomainImpl.filterLoop(allBattery);

            batteryMoObjectBeanlist = batteryMoObjectBeans.stream().filter(DeviceEntity -> idNameResult.stream()
                            .map(IdNameBean::getId)
                            .collect(Collectors.toList())
                            .contains(DeviceEntity.getId()))
                    .collect(Collectors.toList());
            log.info("batteryMoObjectBeans filter loop->  batteryMoObjectBeanlist size is {}", batteryMoObjectBeanlist.size());
            log.debug("batteryMoObjectBeans filter loop ->  batteryMoObjectBeanlist is {}", batteryMoObjectBeanlist);
            return batteryMoObjectBeanlist;
        } catch (UedmException e) {
            log.error("filter battery loop fail!");
            return batteryMoObjectBeanlist;
        }

    }



    public List<MonitorObjectBean> filterBattery(List<MonitorObjectBean> batteryMoObjectBeans
    ) throws UedmException {
        // 查询当前开关电源下所有电池的寿命最新评估结果
        List<String> batteryMoIdList = batteryMoObjectBeans.stream()
                .map(MonitorObjectBean::getId).filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());
        Map<String, Integer> lifeMap = battLifeEvalDomain.getBattLifeMap(batteryMoIdList);
        log.info("[calculateBackupPowerByACH]  battery lifemap {}", lifeMap);
        if (MapUtils.isEmpty(lifeMap)) {
            throw new UedmException(-1, "No life data"); // -1 自定义，开关电源 无电池寿命数据
        }
        // 查询所有告警信息的列表
        Map<String, List<Alarm>> allAlarmsMap = batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(batteryMoIdList);
        log.info("[calculateBackupPowerByACH] battery allAlarmsMap {}", allAlarmsMap);
        // 获取满足条件的电池list
        List<MonitorObjectBean> meetConditionsBattery = batteryMoObjectBeans.stream()
                .filter(e -> batteryMaximumChargingCapacityDomainImpl.isBatteryValid(e.getId(), lifeMap, allAlarmsMap)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(meetConditionsBattery)) {
            throw new UedmException(-8, "No battery meted conditions");

        }
        return meetConditionsBattery;
    }



    public void obtainCurrent(List<HistoryAiBean> historyAiBeans, List<String> moIds, Map<String, String> timeMap,Map<String, String> timeMapMonth) {
        String timeBegin = before1WeekOfNow();
        String timeEnd = dateTimeService.getCurrentTime();
        String acAlarmEndTime = null;

        if(timeMapMonth.size() == 0){
            log.info("no timeMapMonth,no need consider before last week time");
            acAlarmEndTime = timeBegin;
        } else {
            List<String> timeList = new ArrayList<>(timeMapMonth.keySet());
            log.info("powerDown Alarm occured Time {}", timeList.get(0));
            filterBatteryGetCurr(historyAiBeans, moIds, timeBegin, timeList.get(0));
            acAlarmEndTime = timeMapMonth.get(timeList.get(0));
        }
        log.info("acAlarmEndTime: {}",acAlarmEndTime);
        if (timeMap.size() == 0) {
            log.info("no timeMap,obtain curr from last AC alarm end to now");
            filterBatteryGetCurr(historyAiBeans, moIds, acAlarmEndTime, timeEnd);
        } else {
            List<String> timeList = new ArrayList<>(timeMap.keySet());
            for (int i = 0; i < timeList.size(); i++) {
                log.info("powerDown Alarm occured Time {} {}", acAlarmEndTime,timeList.get(i));
                filterBatteryGetCurr(historyAiBeans, moIds, acAlarmEndTime, timeList.get(i));
                if (i + 1 < timeList.size()) {
                    log.info("AC power Alarm cleaned Time,next powerDown Alarm occured Time {}{}", timeMap.get(timeList.get(i)), timeList.get(i + 1));
                    acAlarmEndTime = timeMap.get(timeList.get(i));
                } else if (i == timeList.size() - 1) {
                    filterBatteryGetCurr(historyAiBeans, moIds, timeMap.get(timeList.get(i)), timeEnd);
                }

            }


        }


    }

    public Map<String, String> getSiteAlrmTimeByMoList(String id, List<DeviceEntity> moList) {
        log.info("-------Count the alarms generated by this site within the last week--------{}", id);
        Map<String, String> timeMap = new HashMap<>();
        Map<String, List<DeviceEntity>> mapMo = moList.stream()
                .collect(Collectors.groupingBy(DeviceEntity::getMoc));

        List<String> dcpowerList = new ArrayList<>(Collections.singleton(id));

        List<String> battPackList = new ArrayList<>();
        List<String> dcdpAndBattPackMoList = new ArrayList<>();
        // group by moc
        dealGroupMoc(mapMo,  battPackList);
        log.info("dcpowerList: {}, battPackList: {} ", dcpowerList, battPackList);
        //检查该站点下无交流停电和一次下电告警的监控对象
        if (CollectionUtils.isEmpty(dcpowerList)  && CollectionUtils.isEmpty(battPackList)) {
            log.info("There are no monitoring objects generating alarms at this site,Unable to remove current. ");
            return timeMap;
        }
        // 交流电停电告警和告警码list
        List<Alarm> acdpAlarmList;
        List<AlarmCode> alarmAcdpCodes = new ArrayList<>();
        // 一次下电告警和告警码list
        dcdpAndBattPackMoList.addAll(battPackList);
        dcdpAndBattPackMoList.addAll(dcpowerList);
        List<AlarmCode> alarmDcdpAndBattPackCodes = new ArrayList<>();
        //分路下电告警和告警码 list
        List<AlarmCode> alarmDcdpBranchCodes = new ArrayList<>();

        //塞入所需要告警的告警码
        getAlarmCodes(alarmDcdpBranchCodes, alarmAcdpCodes, alarmDcdpAndBattPackCodes);

        String endTime = dateTimeService.getCurrentTime();
        String startTime = before1WeekOfNow();
        //查询一周之内交流停电的实时与历史告警
        acdpAlarmList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcpowerList, alarmAcdpCodes, startTime,
                endTime, 0, ALARM_RAISED_TIME);
        if(acdpAlarmList.isEmpty()){
            log.info("None AC power outage alarms within a week ");
            log.info("timeMap {}", timeMap);
            return timeMap;
        }
        Collections.sort(acdpAlarmList, Comparator.comparing(Alarm::getAlarmraisedtime));
        log.info("Number of AC power outage alarms within a week {}", acdpAlarmList.size());
        timeMap = getStringStringMap(dcpowerList, dcdpAndBattPackMoList, acdpAlarmList, alarmDcdpAndBattPackCodes, alarmDcdpBranchCodes, endTime);
        log.info("timeMap {}", timeMap);
        return timeMap;
    }


    private Map<String, String> getStringStringMap(List<String> dcdpList, List<String> dcdpAndBattPackMoList, List<Alarm> acdpAlarmList, List<AlarmCode> alarmDcdpAndBattPackCodes, List<AlarmCode> alarmDcdpBranchCodes, String endTime) {
        List<Alarm> dcdpBranchList;
        List<Alarm> dcdpAndBattPackList;
        Map<String, String> timeMap = new HashMap<>();
        //根据每个交流停电告警的产生与清除时间查找期间产生的一次下电
        for (Alarm alarm : acdpAlarmList) {
            log.info("Detailed information on AC power outage alarms exchanged within a week {}{}{}", alarm.getAlarmshowtime(), alarm.getPositionname(), alarm.getAlarmclearedtime());
            long acdpRaisedTime = alarm.getAlarmraisedtime();
            String alarmEndTime;
            String alarmStartTime = dateTimeService.getStrMillisecondTime(acdpRaisedTime);
            if (alarm.getAlarmclearedtime() != null) {
                alarmEndTime = dateTimeService.getStrMillisecondTime(alarm.getAlarmclearedtime());
            } else {
                alarmEndTime = endTime;
            }
            //查询交流停电产生后的一次下电告警(非智能)
            dcdpAndBattPackList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcdpAndBattPackMoList, alarmDcdpAndBattPackCodes, alarmStartTime,
                    alarmEndTime, 0, ALARM_RAISED_TIME);

            //查询交流停电产生后的一次下电告警(智能)
            dcdpBranchList = backupPowerHistoryAlarmDomain.getALarmListByMolist(dcdpList, alarmDcdpBranchCodes, alarmStartTime,
                    alarmEndTime, 0, ALARM_RAISED_TIME);
            //交流停电产生后到交流停电恢复期间产生的所有一次下电告警
            List<Alarm> powerDownAlarms = new ArrayList<>();
            powerDownAlarms.addAll(dcdpAndBattPackList);
            powerDownAlarms.addAll(dcdpBranchList);
            if (CollectionUtils.isEmpty(powerDownAlarms)) {
                log.info("No powerDown Alarm, There is no need to remove the current");
                return timeMap;
            } else {
                Collections.sort(powerDownAlarms, Comparator.comparing(Alarm::getAlarmraisedtime));
                Alarm powerDownAlarm = powerDownAlarms.get(0);
                log.info("Detailed information on power down alarms exchanged within this AC alarm time Range {}{}{}", powerDownAlarm.getAlarmshowtime(), powerDownAlarm.getPositionname(), powerDownAlarm.getAlarmclearedtime());
                String startCurrTime = dateTimeService.getStrMillisecondTime(powerDownAlarm.getAlarmraisedtime());
                timeMap.put(startCurrTime, alarmEndTime);

            }
        }
        return timeMap;
    }

    public void getAlarmCodes(List<AlarmCode> alarmDcdpBranchCodes, List<AlarmCode> alarmAcdpCodes, List<AlarmCode> alarmDcdpAndBattPackCodes) {
        //分路下电告警码
        for (Long l : BattAlarmConstant.CODE_LIST) {
            AlarmCode alarmCode = new AlarmCode();
            alarmCode.setRestype(MocOptional.DC_POWER.getId());
            alarmCode.setAlarmcode(l);
            alarmDcdpBranchCodes.add(alarmCode);
        }
        //交流停电告警码
        AlarmCode alarmCode = new AlarmCode();
        alarmCode.setRestype(MocOptional.DC_POWER.getId());
        alarmCode.setAlarmcode(AlarmTypeEnum.ACDP.getAlarmCode());
        alarmAcdpCodes.add(alarmCode);
        //一次下电告警码
        AlarmCode alarmDcdpCode = new AlarmCode();
        alarmDcdpCode.setRestype(MocOptional.DC_POWER.getId());
        alarmDcdpCode.setAlarmcode(AlarmTypeEnum.DCDP_11.getAlarmCode());
        AlarmCode alarmBattpackCode = new AlarmCode();
        alarmBattpackCode.setRestype(MocOptional.BATTERY_SET.getId());
        alarmBattpackCode.setAlarmcode(AlarmTypeEnum.BATTPACK_08.getAlarmCode());
        alarmDcdpAndBattPackCodes.add(alarmBattpackCode);
        alarmDcdpAndBattPackCodes.add(alarmDcdpCode);

    }

    private void dealGroupMoc(Map<String, List<DeviceEntity>> mapMo,  List<String> battPackList) {
        for (Map.Entry<String, List<DeviceEntity>> m : mapMo.entrySet()) {
            String key = m.getKey();
             if (key.contains(AlarmTypeEnum.BATTPACK_08.getType())) {
                battPackList.addAll(m.getValue().stream().map(DeviceEntity::getId).collect(Collectors.toList()));
            }
        }
    }

    private BatteryCurrentPojo createBatteryBackupPowerEvalPojoBean(MoObjectConfiguration moObjectConfiguration, DeviceEntity monitorObjectBean, BigDecimal avgCurrent, String mocType) {
        BatteryCurrentPojo batteryCurrentPojo = new BatteryCurrentPojo();
        String id = moObjectConfiguration.getId() + monitorObjectBean.getId();
        batteryCurrentPojo.setId(id);
        batteryCurrentPojo.setMoId(moObjectConfiguration.getId());
        batteryCurrentPojo.setSaveTime(DateUtils.getYearMonDayDateStr(new Date()));
        batteryCurrentPojo.setType(mocType);
        batteryCurrentPojo.setBattId(monitorObjectBean.getId());
        batteryCurrentPojo.setAvgCurr(avgCurrent);
        batteryCurrentPojo.setCreator(USERNAME);
        batteryCurrentPojo.setGmtCreate(new Date());
        batteryCurrentPojo.setUpdater(USERNAME);
        batteryCurrentPojo.setGmtModified(new Date());

        return batteryCurrentPojo;
    }

    private List<HistoryAiBean> filterBatteryGetCurr(List<HistoryAiBean> historyAiBeans,
                                                     List<String> moIds, String timeBegin, String timeEnd) {

        List<String> smpIds = new ArrayList<>();
        smpIds.add(BATTERY_SMPID_CURR);
        List<SpIdDto> avg = smpIds.stream().map(smpid -> new SpIdDto(smpid, GlobalConstants.AVG,null)).collect(Collectors.toList());
        // 一次查询1000条
        BatchUtils.doInBatch(1000, moIds, (item) -> {
            List<HistoryAiBean> idBatch = new ArrayList<>();
            try {
                idBatch = pmaService.selectDataByCondition(item, MocOptional.BATTERY.getId(),avg, timeBegin, timeEnd, DATA_TYPE_COLLECT, GR_MINUTE);
            } catch (Exception e) {
                throw new RuntimeException();
            }
            historyAiBeans.addAll(idBatch);
        });
        return historyAiBeans;
    }

    public String before1WeekOfNow() {
        Date parse = new Date();
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(parse);
        // 当前时间往前1个星期
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 7);
        return format.format(calendar.getTime());
    }
    public String before1MonthOfNow() {
        Date parse = new Date();
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_1);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(parse);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 12);
        return format.format(calendar.getTime());
    }

    public void dealWithHistoryAiBeans(List<HistoryAiBean> historyAiBeans, Map<String, BigDecimal> batteryHistoryAiMap) throws UedmException {
        // 将查询到的电流历史数据分组 过滤保留电流均值为负数的电流数据
        Map<String, List<HistoryAiBean>> historyBeanMap = historyAiBeans.stream().collect(Collectors.groupingBy(HistoryAiBean::getResId));
        log.info("historyBeanMap key size {}", historyBeanMap.keySet().size());
        Map<String, Future<BigDecimal>> futureResult = new HashMap<>();
        for (Map.Entry<String, List<HistoryAiBean>> entry : historyBeanMap.entrySet()) {
            Future<BigDecimal> future = fixedThreadPool.submit(() -> getAvgResult(entry));
            futureResult.put(entry.getKey(), future);
        }
        // 等待任务的结果
        try {
            for (Map.Entry<String, Future<BigDecimal>> entry : futureResult.entrySet()) {
                if (entry.getValue() != null) {
                    batteryHistoryAiMap.put(entry.getKey(), entry.getValue().get());
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
        }
    }

    public BigDecimal getAvgResult(Map.Entry<String, List<HistoryAiBean>> entry) {
        List<HistoryAiBean> historyAiBeanList = entry.getValue();
        if (CollectionUtils.isNotEmpty(historyAiBeanList)) {
            List<HistoryAiBean> historyAiBeanList02 = historyAiBeanList.stream()
                    .filter(historyAiBean -> StringUtils.isNotBlank(historyAiBean.getAvgValue()) &&
                            (Double.parseDouble(historyAiBean.getAvgValue()) + 2.0d) < -(1e-8)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(historyAiBeanList02)) {
                log.info("Total number of this battery current data after filtering {},{}", entry.getKey(), historyAiBeanList02.size());
                BigDecimal sumCurrent = historyAiBeanList02.stream()
                        .map(e -> new BigDecimal(e.getAvgValue()).abs())
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                return sumCurrent.divide(new BigDecimal(historyAiBeanList02.size()), 2, RoundingMode.HALF_UP);
            }
            return null;
        } else {
            return null;
        }
    }
}
