package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.BatteryTemporaryTestVo;
import com.zte.uedm.battery.bean.PathInfoBean;
import com.zte.uedm.battery.controller.batterytest.vo.BattTestCheckBeforeVo;
import com.zte.uedm.battery.domain.BatteryTestDomain;
import com.zte.uedm.battery.domain.LeadAcidBatteryDomain;
import com.zte.uedm.battery.domain.impl.BattTypeDomainImpl;
import com.zte.uedm.battery.enums.batttest.BattTestExceptionEnum;
import com.zte.uedm.battery.enums.batttest.BattTestCommandResultEnums;
import com.zte.uedm.battery.enums.batttest.BattTestStatusEnums;
import com.zte.uedm.battery.enums.batttest.BattTestTypeEnums;
import com.zte.uedm.battery.opti.domain.service.bean.CalaRiskData;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.service.BatteryTemporaryTestService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

import com.zte.uedm.service.config.optional.GlobalOptional;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_PRST_SOC;

@Service
@Slf4j
public class BatteryTemporaryTestServiceImpl implements BatteryTemporaryTestService
{
    @Autowired
    private BatteryTestDomain batteryTestDomain;
    @Autowired
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpc;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private ConfigurationManagerRpcImpl configurationRpcImpl;
    @Autowired
    private LeadAcidBatteryDomain leadAcidBatteryDomain;
    @Autowired
    private ConfigurationManagerRpcImpl cfgRpc;
    @Autowired
    private BattTypeDomainImpl battTypeDomainImpl;
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Override
    @Transactional(rollbackFor = UedmException.class)
    public List<BatteryTemporaryTestVo> startBatteryTemporaryTest(List<String> ids, ServiceBaseInfoBean serviceBean)
            throws UedmException
    {
        if(CollectionUtils.isEmpty(ids))
        {
            log.warn("BatteryTemporaryTestServiceImpl -> startBatteryTemporaryTest : device ids is empty!");
            throw new UedmException(BattTestExceptionEnum.PARAMS_IS_BLANK.getCode(), BattTestExceptionEnum.PARAMS_IS_BLANK.getDesc());
        }

        // 防止设备响应时间过快，把入库的下发时间往前推迟2s
        String nowTime = TimeUtils.beforeSecondTime(2);
        log.info("BatteryTemporaryTestServiceImpl start battery time: {}", nowTime);

        List<BatteryTemporaryTestVo> temporaryTestInfoeBeans = new ArrayList<>();
        Map<Integer, String> result = batteryTestDomain.startBatteryTest(ids, serviceBean);
        log.info("BatteryTemporaryTestServiceImpl ->startBatteryTemporaryTest result :{}",result);
        for(Map.Entry<Integer, String> entry : result.entrySet())
        {
            if(!BattTestExceptionEnum.NORMAL.getCode().equals(entry.getKey()))
            {
                throw new UedmException(entry.getKey(), entry.getValue());
            }
        }
        //根据监控对象id查询名称
        List<MonitorObjectBean> monitorObjectList = siteSpBatteryRelatedRpc.getMonitorObjectList(ids);
        Map<String, String> idNameMap = monitorObjectList.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getName()))
                .collect(Collectors.toMap(MonitorObjectBean::getId, MonitorObjectBean::getName, (key1,key2)->key2));

        for (String id : ids)
        {
            //存入redis
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME, nowTime);
            valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, BattTestCommandResultEnums.COMMAND_ISSUING.getId());
            valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME, serviceBean.getUserName());
            redisService.put(BatteryConstant.TEMPORARY.concat(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE), id, valueMap);
            BatteryTemporaryTestVo temporaryTestInfoBean = new BatteryTemporaryTestVo(id, i18nUtils.getMapFieldByLanguageOption(BattTestStatusEnums.COMMAND_ISSUING.getName(), serviceBean.getLanguageOption()), idNameMap.get(id));
            temporaryTestInfoeBeans.add(temporaryTestInfoBean);
        }
        return temporaryTestInfoeBeans;
    }
    @Override
    public List<BatteryTemporaryTestVo> startBatterySetSoc(List<String> ids, ServiceBaseInfoBean serviceBean)
            throws UedmException
    {
        if(CollectionUtils.isEmpty(ids))
        {
            log.warn("BatteryTemporaryTestServiceImpl -> startBatteryTemporaryTest : device ids is empty!");
            throw new UedmException(BattTestExceptionEnum.PARAMS_IS_BLANK.getCode(), BattTestExceptionEnum.PARAMS_IS_BLANK.getDesc());
        }

        // 防止设备响应时间过快，把入库的下发时间往前推迟2s
        String nowTime = TimeUtils.beforeSecondTime(2);
        log.info("BatteryTemporaryTestServiceImpl start battery time: {}", nowTime);

        List<BatteryTemporaryTestVo> temporaryTestInfoeBeans = new ArrayList<>();
        Map<Integer, String> result = batteryTestDomain.startBatterySoc(ids, serviceBean, BattTestTypeEnums.TEMPORARY.getId(), new HashMap<>());
        log.info("BatteryTemporaryTestServiceImpl ->startBatteryTemporaryTest result :{}",result);
        for(Map.Entry<Integer, String> entry : result.entrySet())
        {
            if(!BattTestExceptionEnum.NORMAL.getCode().equals(entry.getKey()))
            {
                throw new UedmException(entry.getKey(), entry.getValue());
            }
        }
        return temporaryTestInfoeBeans;
    }
    @Override
    public List<BattTestCheckBeforeVo> checkBatterySoc(List<String> ids, ServiceBaseInfoBean serviceBean) throws UedmException {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("BatteryTemporaryTestServiceImpl -> checkBatterySoc : device ids is empty!");
            throw new UedmException(BattTestExceptionEnum.PARAMS_IS_BLANK.getCode(), BattTestExceptionEnum.PARAMS_IS_BLANK.getDesc());
        }

        // 去重设备ID并批量查询电池组基本信息
        Set<String> uniqueIds = new HashSet<>(ids);
        List<DeviceEntity> devices = deviceCacheManager.selectDeviceById(uniqueIds);
        Map<String, String> idToNameMap = devices.stream()
                .collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName, (a, b) -> a));
        Map<String, String> idToPathNameMap = devices.stream()
                .collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getPathName, (a, b) -> a));
        // 批量获取每个设备关联的电池列表
        Map<String, List<DeviceEntity>> deviceBatteriesMap = new HashMap<>();
        for (String id : uniqueIds) {
            List<DeviceEntity> batteries = leadAcidBatteryDomain.getMoListBySpAndMoc(id, MocOptional.BATTERY.getId());
            deviceBatteriesMap.put(id, batteries);
        }

        // 收集所有电池ID并批量查询扩展属性
        Set<String> allBatteryIds = deviceBatteriesMap.values().stream()
                .flatMap(List::stream)
                .map(DeviceEntity::getId)
                .collect(Collectors.toSet());
        List<IdNameBean> batteryAttributes = cfgRpc.selectAllBatteryExtendAttribute(new ArrayList<>(allBatteryIds));
        List<IdNameBean> result = battTypeDomainImpl.filterLoop(batteryAttributes);
        Set<String> validBatteryIds = result.stream()
                .map(IdNameBean::getId)
                .collect(Collectors.toSet());

        // 预过滤有效电池并预加载实时数据
        Map<String, List<DeviceEntity>> filteredBatteriesMap = new HashMap<>();
        deviceBatteriesMap.forEach((deviceId, batteries) -> {
            List<DeviceEntity> validBatteries = batteries.stream()
                    .filter(b -> validBatteryIds.contains(b.getId()))
                    .collect(Collectors.toList());
            filteredBatteriesMap.put(deviceId, validBatteries);
        });

        // 批量获取SOC实时数据（假设直接存储Double值）
        Map<String, Double> batterySocMap = dataRedis.batchSelectRealData(
                        new ArrayList<>(allBatteryIds),
                        Collections.singletonList(BATTERY_SMPID_PRST_SOC)
                ).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> parseSocValue(e.getValue().get(BATTERY_SMPID_PRST_SOC))
                ));

        // 构建结果集
        return ids.stream().map(id -> {
            BattTestCheckBeforeVo vo = new BattTestCheckBeforeVo();
            vo.setId(id);
            vo.setName(idToNameMap.get(id));
            vo.setPathName(idToPathNameMap.get(id));
            List<DeviceEntity> batteries = filteredBatteriesMap.getOrDefault(id, Collections.emptyList());
            long lowSocCount = batteries.stream()
                    .map(DeviceEntity::getId)
                    .mapToDouble(batteryId -> batterySocMap.getOrDefault(batteryId, 0.0))
                    .filter(soc -> soc < 99.0)
                    .count();

            vo.setResult(lowSocCount == 0);
            vo.setReason(lowSocCount == 0 ? i18nUtils.getMapFieldByLanguageOption(BatteryConstant.SUCCESS,serviceBean.getLanguageOption()) : i18nUtils.getMapFieldByLanguageOption(BatteryConstant.FAIL,serviceBean.getLanguageOption()));
            return vo;
        }).collect(Collectors.toList());
    }

    // 辅助方法：优化SOC值解析逻辑
    private double parseSocValue(Object socData) {
        if (socData != null) {
            String jsonSocData = JSON.toJSONString(socData);
            CalaRiskData calaRiskData = JSON.parseObject(jsonSocData, CalaRiskData.class);
            log.debug("calaRiskData value:{}", calaRiskData.getValue());
            return Double.parseDouble(calaRiskData.getValue());
        }
        return Double.parseDouble("0.0");
    }
    @Override
    public List<BatteryTemporaryTestVo> queryTemporaryTestCommandResult(ServiceBaseInfoBean serviceBean, String id) throws UedmException
    {
        if(null == serviceBean)
        {
            log.warn("BatteryTemporaryTestServiceImpl -> queryTemporaryTestCommandResult : serviceBean is empty.");
            return new ArrayList<>();
        }
        log.info("id {}, serviceBean is {}", id, serviceBean);
        String cacheName = BatteryConstant.TEMPORARY.concat(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE);
        //取出所有缓存
        Map<String, Object> allValueMap = redisService.getCacheMap(cacheName);
        if(allValueMap.isEmpty())
        {
            log.info("BatteryTemporaryTestServiceImpl -> queryTemporaryTestCommandResult allValueMap is empty.");
            return new ArrayList<>();
        }
        log.info("BatteryTemporaryTestServiceImpl -> allValueMap size is {}", allValueMap.size());
        log.debug("BatteryTemporaryTestServiceImpl -> allValueMap is {}", allValueMap);

        List<String> ids = new ArrayList<>(allValueMap.keySet());
        //根据监控对象id查询名称
        List<PathInfoBean> pathByIdList = configurationRpcImpl.getPathByIdList(ids, serviceBean.getUserName());
        Map<String, String> idPathMap = filterIdPathMap(pathByIdList, id);
        log.info("BatteryTemporaryTestServiceImpl -> idPathMap size is {}", idPathMap.size());
        Map<String, String> namePathMap = pathByIdList.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getNamePath()))
                .collect(Collectors.toMap(PathInfoBean::getId, PathInfoBean::getNamePath, (key1,key2)->key2));
        log.info("BatteryTemporaryTestServiceImpl -> namePathMap size is {}", namePathMap.size());
        List<MonitorObjectBean> monitorObjectList = siteSpBatteryRelatedRpc.getMonitorObjectList(ids);
        Map<String, String> idNameMap = monitorObjectList.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getName()))
                .collect(Collectors.toMap(MonitorObjectBean::getId, MonitorObjectBean::getName, (key1,key2)->key2));
        log.info("BatteryTemporaryTestServiceImpl -> idNameMap size is {}", idNameMap.size());
        List<BatteryTemporaryTestVo> result = new ArrayList<>();
        for(Map.Entry<String, Object> entry : allValueMap.entrySet())
        {
            String idPath = idPathMap.get(entry.getKey());
            if(null != entry.getValue() && StringUtils.isNotBlank(idPath))
            {
                Map<String, String> valueMap = (Map<String, String>) entry.getValue();
                BatteryTemporaryTestVo temporaryTestVo = new BatteryTemporaryTestVo();
                temporaryTestVo.setId(entry.getKey());
                temporaryTestVo.setName(idNameMap.get(entry.getKey()));
                temporaryTestVo.setPosition(dealDevicePosition(namePathMap.get(entry.getKey()), idNameMap.get(entry.getKey())));
                String commandStatusId = "";
                if(null != valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE))
                {
                    commandStatusId = String.valueOf(valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE));
                }
                temporaryTestVo.setCommandStatusId(commandStatusId);
                log.debug("commandStatusId is {}", commandStatusId);
                String commandStatus = i18nUtils.getMapFieldByLanguageOption(BattTestCommandResultEnums.getNameById(commandStatusId),
                        serviceBean.getLanguageOption());
                log.debug("commandStatus is {}", commandStatus);
                temporaryTestVo.setCommandTime(String.valueOf(valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME)));
                temporaryTestVo.setCommandStatus(commandStatus);
                result.add(temporaryTestVo);
            }
        }
        //按照时间排序
        Collections.sort(result,(a,b)-> b.getCommandTime().compareTo(a.getCommandTime()));
        log.info("BatteryTemporaryTestServiceImpl -> result size is {}", result.size());
        log.debug("BatteryTemporaryTestServiceImpl -> result is {}", result);
        return result;
    }

    private String dealDevicePosition(String position, String name)
    {
        if(!StringUtils.isAnyBlank(position, name))
        {
            position = position.replace("/".concat(name), "/");
        }
        return position;
    }

    private Map<String, String> filterIdPathMap(List<PathInfoBean> pathByIdList, String logicGroupId)
    {
        if(null == logicGroupId || logicGroupId.equalsIgnoreCase(GlobalOptional.GLOBAL_ROOT))
        {
            logicGroupId = "";
        }
        String finalLogicGroupId = logicGroupId;
        Map<String, String> idPathMap = pathByIdList.stream().filter(bean-> StringUtils.isNotBlank(bean.getId()) && StringUtils.isNotBlank(bean.getNamePath()) && StringUtils.isNotBlank(bean.getIdPath()) && bean.getIdPath().contains(
                finalLogicGroupId)).collect(Collectors.toMap(PathInfoBean::getId, PathInfoBean::getNamePath, (key1,key2)->key2));
        return idPathMap;
    }
}
