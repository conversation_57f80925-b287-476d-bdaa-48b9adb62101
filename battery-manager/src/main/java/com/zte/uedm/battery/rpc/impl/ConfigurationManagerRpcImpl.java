package com.zte.uedm.battery.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.uedm.function.license.exception.LicenseException;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_application.peakshift.converter.UpDownloadFileConverter;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftFileService;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.MocEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.UpDownloadFileEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.UpDownloadFileRepository;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftTemplateFileService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.DetailHistoryVo;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.DetailHistoryResposeBean;
import com.zte.uedm.battery.bean.pv.PvNumberBean;
import com.zte.uedm.battery.controller.backuppower.dto.LogicIdsDto;
import com.zte.uedm.battery.controller.backuppower.vo.TreeAssetAuthorityQueryVo;
import com.zte.uedm.battery.controller.batterytesttask.dto.GetMoByConditionDto;
import com.zte.uedm.battery.domain.ConfigurationDataDomain;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.dto.FieldRecursiveParamsDto;
import com.zte.uedm.battery.rpc.dto.RecollectionCapabilityQueryDto;
import com.zte.uedm.battery.rpc.vo.SiteRecollectionCapabilityBean;
import com.zte.uedm.battery.service.DevicePeakCacheInfoService;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.battery.util.constant.PvConstant;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceSfBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.configuration.point.bean.RecordIndexBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeOtherUtil;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeParameterUtil;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeRpcUtil;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.common.util.PageUtils;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.service.config.api.configuraiton.AlarmCodeCustomService;
import com.zte.uedm.service.config.api.configuraiton.vo.AlarmFunctionParameterVo;
import com.zte.uedm.service.config.api.configuraiton.vo.CustomAlarmCodeMappingVo;
import com.zte.uedm.service.config.api.configuraiton.vo.ResourceAlarmVo;
import com.zte.uedm.service.config.api.model.AlarmCodeService;
import com.zte.uedm.service.config.api.model.vo.AlarmCodeVo;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.config.optional.ModelOptional;
import com.zte.uedm.service.config.optional.protocol.ProtocolTypeOptional;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.uedm.battery.service.impl.AutoPeakShiftStrategyServiceImpl.DEFAULT_USER;

@Service
@Slf4j
public class ConfigurationManagerRpcImpl {
    @Autowired
    private ConfigurationRpc configurationRpcs;

    @Autowired
    private UpDownloadFileRepository upDownloadFileRepository;

    @Autowired
    private PeakShiftTemplateFileService peakShiftTemplateFileService;

    @Autowired
    private PeakShiftFileService peakShiftFileService;

    @Autowired
    private LicenseSwitchService licenseMgr;

    @Autowired
    private JsonService jsonService;
    @Autowired
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Autowired
    private CollectorCacheManager collectorCacheManager;

    private Map<String, List<String>> pvDcloadRelationMap;

    private static final Integer PAGE_SIZE = 5000;

    @Setter
    private Map<String, Map<String, String>> pvAlarmCodeRelationMap;

    @Autowired
    private FieldCacheManager fieldCacheManager;
    @Autowired
    private AlarmCodeService alarmCodeService;
    @Autowired
    private AlarmCodeCustomService alarmCodeCustomService;

    @Autowired
    private ResourceBaseCacheManager resourceBaseCacheManager;
    @Autowired
    private AdapterCacheManager adapterCacheManager;
    @Autowired
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;
    @Autowired
    private BatteryAttributeUtils batteryAttributeUtils;

    @Autowired
    private MocCacheManager mocCacheManager;

    @Autowired
    private StandardPointCacheManager standardPointCacheManager;
    @Autowired
    private ConfigurationDataDomain configurationDataDomain;

    @Autowired
    private DevicePeakCacheInfoService devicePeakCacheInfoService;
    @Value("${site.solar.recollection.protocol}")
    private String SUPPLY_PROTOCOL_TYPE;
    public List<MoObjectConfiguration> getMonitorObjectListWhenIdsBig(List<String> logicGroupIds, String moc) throws UedmException {
        log.debug("oids={}", logicGroupIds.toString());
        log.info("oids.size={},moc={}", logicGroupIds.size(), moc);
        List<DeviceEntity> deviceByIdsAndMoc = deviceCacheManager.getDeviceByLogicGroupIdsAndMoc(logicGroupIds, moc);
        List<MoObjectConfiguration> result = deviceByIdsAndMoc.stream().map(entity -> {
            MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
            moObjectConfiguration.setId(entity.getId());
            moObjectConfiguration.setName(entity.getName());
            moObjectConfiguration.setParentId(entity.getParentId());
            moObjectConfiguration.setPath(entity.getPathName());
            moObjectConfiguration.setPathId(entity.toStringPathId());
            return moObjectConfiguration;
        }).collect(Collectors.toList());
        log.debug("getMonitorObjectListWhenIdsBig result:{}", result);
        log.info("getMonitorObjectListWhenIdsBig result size:{}", result.size());
        return result;
    }

    //分域
    public List<MoObjectConfiguration> getMonitorObjectListWhenIdsBigByAuth(List<String> ids, String moc, String userName) throws UedmException {
        log.info("getMonitorObjectListWhenIdsBigByAuth oids={},moc={}, username={}", ids.toString(), moc, userName);
        List<MoObjectConfiguration> monitorObjectList = getMonitorObjectListWhenIdsBig(ids, moc);
        return monitorObjectList;
    }

    //分域--电池独立组网
    // 查询idList下类型为moc的所有电池组独立组网监控对象,同时过滤逻辑为先查询出所有电池组，根据关联设备存在开关电源moc进行过滤
    public List<MoObjectConfiguration> getBpMonitorObjectListWhenIdsBigByAuth(List<String> ids, String moc, String userName) throws UedmException
    {
        log.info("getBpMonitorObjectListWhenIdsBigByAuth oids={},moc={}, username={}", ids.toString(), moc, userName);
        List<MoObjectConfiguration> monitorObjectList = getMonitorObjectListWhenIdsBig(ids, moc);
        List<MoObjectConfiguration> result = new ArrayList<>();

        if (null != monitorObjectList && !monitorObjectList.isEmpty()) {
            Set<String> idSet = monitorObjectList.stream().map(MoObjectConfiguration::getId).collect(Collectors.toSet());

            List<DeviceEntity> deviceEntities = deviceCacheManager.selectAllDevice();
            List<String> mocList = Arrays.asList(MocOptional.DCDP.getId(), MocOptional.DC_POWER.getId());
            Set<String> deviceIdSet = deviceEntities.stream().filter(entity -> mocList.contains(entity.getMoc())).map(DeviceEntity::getId).collect(Collectors.toSet());

            List<CollectorEntity> collectorEntities = collectorCacheManager.getAllCollector();
            List<String> modelList = Arrays.asList("MonitorDeviceBms", "MonitorDevicePad");
            Set<String> collectIdSet = collectorEntities.stream().filter(entity -> modelList.contains(entity.getModel())).map(CollectorEntity::getId).collect(Collectors.toSet());

            List<ResourceCollectorRelationEntity> relationEntities = resourceCollectorRelationCacheManager.getAllEntity();
            Set<String> oppositeSet = relationEntities.stream()
                    .filter(entity -> idSet.contains(entity.getResourceId()))
                    .filter(entity -> relationEntities.stream()
                            .filter(relation -> StringUtils.equals(entity.getCollectorId(), relation.getCollectorId()))
                            .anyMatch(relation -> deviceIdSet.contains(relation.getResourceId()) || collectIdSet.contains(relation.getCollectorId())))
                    .map(ResourceCollectorRelationEntity::getResourceId)
                    .collect(Collectors.toSet());

            result = monitorObjectList.stream().filter(mo -> !oppositeSet.contains(mo.getId())).collect(Collectors.toList());

        }
        return result;
    }

    public List<MoObjectConfiguration> getSpMonitorObjectListWhenIdsBigByAuth(List<String> ids, String moc, String userName) throws UedmException
    {
        log.info("getSpMonitorObjectListWhenIdsBigByAuth oids={},moc={}, username={}", ids.toString(), moc, userName);
        List<MoObjectConfiguration> monitorObjects = getMonitorObjectListWhenIdsBigByAuth(ids, moc, userName);
        Set<String> resourceIds = resourceCollectorRelationCacheManager.getAllEntity().stream()
                .map(ResourceCollectorRelationEntity::getResourceId)
                .collect(Collectors.toSet());
        return monitorObjects.stream()
                .filter(entity -> resourceIds.contains(entity.getId()))
                .collect(Collectors.toList());
    }



    /* Started by AICoder, pid:90c37m63ecia3011463808cef0acf717fd949dc7 */
    public void selectAllPvDcloadRelation() throws UedmException {
        try {
            pvDcloadRelationMap = new HashMap<>();
            List<DeviceEntity> allPvs = deviceCacheManager.getDevicesByMoc(MocOptional.PV.getId());
            List<String> allPvIds = allPvs.stream().map(DeviceEntity::getId).collect(Collectors.toList());
            this.pvDcloadRelationMap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(allPvIds, MocOptional.DCLOAD.getId());
        } catch (Exception e) {
            log.error("ConfigurationManagerRpcImpl selectAllPvDcloadRelation failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }


    public List<String> getDcloadListByPvId(String pvId) throws UedmException
    {
        if (StringUtils.isBlank(pvId) || null == pvDcloadRelationMap)
        {
            throw new UedmException(-200, "ConfigurationManagerRpcImpl pvId or pvDcloadRelationMap is null");
        }
        return pvDcloadRelationMap.get(pvId);
    }



    public Map<String, Map<String, String>> getPvAlarmCode() throws UedmException
    {
        if (pvAlarmCodeRelationMap == null || pvAlarmCodeRelationMap.isEmpty())
        {
            log.error("ConfigurationManagerRpcImpl getPvAlarmCode pvAlarmCodeRelationMap is empty.");
            return new HashMap<>();
        }
        return pvAlarmCodeRelationMap;

    }

    /* Started by AICoder, pid:e732289718oe787144e90a99c0ba912e373849bd */
    public void cachePvAlarmCode() {
        try {
            pvAlarmCodeRelationMap = new ConcurrentHashMap<>();
            Map<String, Map<String, String>> alarmCodeMapPoint = new HashMap<>();
            String[] pvIds = PvConstant.initPvAlarmCode();
            String[] spuIds = PvConstant.initSpuAlarmCode();
            String[] spcuIds = PvConstant.initSpcuAlarmCode();

            List<AlarmCodeVo> alarmConfigBeans = alarmCodeService.getAllAlarmCode();
            List<ResourceAlarmVo> resourceAlarmVos = alarmCodeCustomService.queryAll();
            List<CustomAlarmCodeMappingVo> tempAlarmCodeMappingBeans = resourceAlarmVos.stream()
                    .flatMap(vo -> vo.getCustomAlarmCodeMappings().stream())
                    .collect(Collectors.toList());

            Map<String, Map<String, String>> pvPointAlarmMap = buildMap(pvIds, MocOptional.PV.getId(), tempAlarmCodeMappingBeans, alarmConfigBeans);
            log.debug("ConfigurationManagerRpcImpl  cachePvAlarmCode  pvPointAlarmMap:{}",pvPointAlarmMap);
            Map<String, Map<String, String>> spuPointAlarmMap = buildMap(spuIds, MocOptional.SPU.getId(), tempAlarmCodeMappingBeans, alarmConfigBeans);
            log.debug("ConfigurationManagerRpcImpl  cachePvAlarmCode  spuPointAlarmMap:{}",spuPointAlarmMap);
            Map<String, Map<String, String>> spcuPointAlarmMap = buildMap(spcuIds, MocOptional.SPCU.getId(), tempAlarmCodeMappingBeans, alarmConfigBeans);
            log.debug("ConfigurationManagerRpcImpl  cachePvAlarmCode  spcuPointAlarmMap:{}",spcuPointAlarmMap);

            alarmCodeMapPoint.putAll(pvPointAlarmMap);
            alarmCodeMapPoint.putAll(spuPointAlarmMap);
            alarmCodeMapPoint.putAll(spcuPointAlarmMap);

            log.info("ConfigurationManagerRpcImpl cachePvAlarmCode success!");
            pvAlarmCodeRelationMap = alarmCodeMapPoint;
        } catch (Exception e) {
            log.error("Error occurred while cachePvAlarmCode", e);
        }
    }
    /* Ended by AICoder, pid:e732289718oe787144e90a99c0ba912e373849bd */
    /* Started by AICoder, pid:6e382l5f84f0eb8145080a52b0177f3ae4f0620f */
    public Map<String, Map<String, String>> buildMap(String[] ids, String moc, List<CustomAlarmCodeMappingVo> tempAlarmCodeMappingBeans,
                                                     List<AlarmCodeVo> alarmConfigBeans) {
        Set<String> mocSet = new HashSet<>();
        mocSet.add(moc);
        Map<String, Map<String, String>> alarmCodeMapPoint = new HashMap<>();

        try {

            List<StandardPointEntity> pointListByMoc = standardPointCacheManager.getStandardByMocs(mocSet).stream()
                    .filter(entity -> Arrays.asList(ids).contains(entity.getId()) || Arrays.asList(ids).contains(entity.getBelongTo()))
                    .collect(Collectors.toList());

            for (StandardPointEntity bean : pointListByMoc) {
                /* Started by AICoder, pid:8b30cz4624c14ba142500a12f07c190abbd895e4 */
                String id = moc;
                if (bean.getBelongTo() == null || bean.getBelongTo().isEmpty()) {
                    // 如果bean.getBelongTo()为空，则使用bean.getId()作为id值
                    id = id + PvConstant.UNDERLINE + bean.getId();
                } else {
                    // 否则，根据条件判断id值
                    id = bean.getId().equals(bean.getBelongTo()) ? id + PvConstant.UNDERLINE + bean.getId() : id + PvConstant.UNDERLINE + bean.getBelongTo();
                }
                /* Ended by AICoder, pid:8b30cz4624c14ba142500a12f07c190abbd895e4 */

                // 先存模板，再用自定义覆盖模板
                for (CustomAlarmCodeMappingVo tempAlarmCodeMappingBean : tempAlarmCodeMappingBeans) {
                    buildResponseMap(alarmCodeMapPoint, id, bean, tempAlarmCodeMappingBean,moc);
                }
                for (AlarmCodeVo alarmConfigBean : alarmConfigBeans) {
                    buildFinaleResponseMap(alarmCodeMapPoint, id, bean, alarmConfigBean,moc);
                }
            }

            return alarmCodeMapPoint;
        } catch (Exception e) {
            log.error("Error occurred while buildMap", e);
            return alarmCodeMapPoint;
        }
    }
    /* Ended by AICoder, pid:6e382l5f84f0eb8145080a52b0177f3ae4f0620f */






    public void buildResponseMap(Map<String, Map<String, String>> alarmCodeMapPoint, String id, StandardPointEntity bean,
                                 CustomAlarmCodeMappingVo tempAlarmCodeMappingBean,String moc) throws UedmException
    {
        List<AlarmFunctionParameterVo> parameters = tempAlarmCodeMappingBean.getParameters();
        List<String> ids = parameters.stream().map(AlarmFunctionParameterVo::getPointId).collect(Collectors.toList());

        if (ids.contains(bean.getId()))
        {
            String alarmCode = tempAlarmCodeMappingBean.getCode();
            Map<String, String> alarmCodes = alarmCodeMapPoint.get(id);
            if (null == alarmCodes || alarmCodes.isEmpty())
            {
                alarmCodes = new HashMap<>();
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(parameters))
            {
                AlarmFunctionParameterVo parameterBean = parameters.get(0);
                String mocId = parameterBean.getMocId();
                if (Objects.equals(moc,mocId)) {
                    alarmCodes.put(alarmCode, parameterBean.getPointId());
                }
            }
            alarmCodeMapPoint.put(id, alarmCodes);
        }
    }
    private void buildFinaleResponseMap(Map<String, Map<String, String>> alarmCodeMapPoint, String id, StandardPointEntity bean,
                                        AlarmCodeVo alarmConfigBean,String moc) throws UedmException
    {
        List<AlarmFunctionParameterVo> parameters = alarmConfigBean.getParameters();
        Set<String> pointIdList = parameters.stream().map(AlarmFunctionParameterVo::getPointId).collect(Collectors.toSet());
        if (pointIdList.contains(bean.getId()))
        {
            String alarmCode = alarmConfigBean.getCode();
            Map<String, String> alarmCodes = alarmCodeMapPoint.get(id);
            if (null == alarmCodes || alarmCodes.isEmpty())
            {
                alarmCodes = new HashMap<>();
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(parameters))
            {
                AlarmFunctionParameterVo parameterBean = parameters.get(0);
                String mocId = parameterBean.getMocId();
                if (Objects.equals(moc,mocId)) {
                    alarmCodes.put(alarmCode, parameterBean.getPointId());
                }
            }

            alarmCodeMapPoint.put(id, alarmCodes);
        }
    }

    /* Started by AICoder, pid:8d271q4807jb1fa148060a4ee06644230018e166 */
    public List<MonitorObjectDsBean> selectMoByMocAndPosition(LogicIdAndMocQueryBean logicIdAndMocQueryBean,
                                                              Map<String, String> headers) throws UedmException {
        List<String> logicGroupIds = logicIdAndMocQueryBean.getLogicIds();
        String moc = logicIdAndMocQueryBean.getMoc();

        // 使用try-catch块来处理可能的异常，并记录错误信息
        try {
            List<DeviceEntity> deviceByIdsAndMoc = deviceCacheManager.getDeviceByLogicGroupIdsAndMoc(logicGroupIds, moc);
            return deviceByIdsAndMoc.stream().map(entity -> {
                MonitorObjectDsBean moObjectConfiguration = new MonitorObjectDsBean();
                moObjectConfiguration.setId(entity.getId());
                moObjectConfiguration.setName(entity.getName());
                moObjectConfiguration.setParentId(entity.getParentId());
                moObjectConfiguration.setPath(entity.getPathName());
                moObjectConfiguration.setTopo(entity.getTopo());
                String[] pathId = entity.getPathId();
                if (null != pathId) {
                    String pathIdStr = String.join("/", pathId);
                    moObjectConfiguration.setPathId(pathIdStr);
                }
                return moObjectConfiguration;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error occurred while selecting monitor objects by MOC and position", e);
            throw new UedmException(-1, "Failed to select monitor objects");
        }
    }
    /* Ended by AICoder, pid:8d271q4807jb1fa148060a4ee06644230018e166 */

    public List<MonitorObjectDsBean> selectAuthMoByMocAndPosition(LogicIdAndMocQueryBean logicIdAndMocQueryBean,
                                                                  Map<String, String> headers) throws UedmException
    {
        List<MonitorObjectDsBean> list = new ArrayList<>();
        try
        {
            List<DeviceEntity> mocByLogicGroupId = deviceCacheManager.getMocByLogicGroupId(logicIdAndMocQueryBean.getLogicIds().get(0), logicIdAndMocQueryBean.getMoc());
            list= mocByLogicGroupId.stream().map(bean->{
                MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
                monitorObjectDsBean.setId(bean.getId());
                return  monitorObjectDsBean;
            }).collect(Collectors.toList());
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectMoByMocAndPosition faield:", e);
            throw new UedmException(-200, e.getMessage());
        }
        return  list;
    }

    /* Started by AICoder, pid:9ba0c146eesad2b1459609ad10a80c3417261553 */
    public List<SiteDsBean> selectSiteByPosition(LogicIdAndMocQueryBean logicIdAndMocQueryBean,
                                                 Map<String, String> headers) throws UedmException {
        List<String> logicGroupIds = logicIdAndMocQueryBean.getLogicIds();
        String moc = logicIdAndMocQueryBean.getMoc();
        List<SiteDsBean> target = new ArrayList<>();

        // 使用try-catch块来处理可能的异常，并记录错误信息
        try {
            for (String logicGroupId : logicGroupIds) {
                List<FieldEntity> fieldById = fieldCacheManager.getSiteByLogicGroupId(logicGroupId);
                List<SiteDsBean> result = fieldById.stream()
                        .filter(entity -> entity.getMoc().equals(moc)) // 添加过滤条件
                        .map(entity -> {
                            SiteDsBean moObjectConfiguration = new SiteDsBean();
                            moObjectConfiguration.setId(entity.getId());
                            moObjectConfiguration.setName(entity.getName());
                            moObjectConfiguration.setParentId(entity.getParentId());
                            moObjectConfiguration.setPath(entity.getPathName());
                            String[] pathId = entity.getPathId();
                            if (null != pathId) {
                                String pathIdStr = String.join("/", pathId);
                                moObjectConfiguration.setPathId(pathIdStr);
                            }
                            return moObjectConfiguration;
                        })
                        .collect(Collectors.toList());

                target.addAll(result);
            }

            return target;
        } catch (Exception e) {
            log.error("Error occurred while selecting site by MOC and position", e);
            throw new UedmException(-1, "Failed to select monitor objects");
        }
    }
    /* Ended by AICoder, pid:9ba0c146eesad2b1459609ad10a80c3417261553 */

    public PvNumberBean getPvSpcuSpuNumber() throws UedmException
    {
        try
        {

            PvNumberBean pvNumberBean = new PvNumberBean();
            int pvNumber = deviceCacheManager.getDevicesByMoc(MocOptional.PV.getId()).size();
            //spcu
            int spcuNumber = deviceCacheManager.getDevicesByMoc(MocOptional.SPCU.getId()).size();
            //spu
            int spuNumber = deviceCacheManager.getDevicesByMoc(MocOptional.SPU.getId()).size();
            pvNumberBean.setPvNumber(pvNumber);
            pvNumberBean.setSpcuNumber(spcuNumber);
            pvNumberBean.setSpuNumber(spuNumber);
            return pvNumberBean;


        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectSiteByPosition faield:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }

    public List<MonitorDeviceBaseBean> getNameByIdList(List<String> deviceIds, String userName) throws UedmException
    {
        List<MonitorDeviceBaseBean> list = new ArrayList<>();

        try
        {
            List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(deviceIds);
            List<MonitorDeviceBaseBean> collect = collectorById.stream().map(bean -> {
                MonitorDeviceBaseBean monitorDeviceBaseBean = new MonitorDeviceBaseBean();
                monitorDeviceBaseBean.setId(bean.getId());
                monitorDeviceBaseBean.setMoc(bean.getMoc());
                monitorDeviceBaseBean.setProtocolType(bean.getProtocolId());
                return monitorDeviceBaseBean;
            }).collect(Collectors.toList());

            list.addAll(collect);

        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl getNameByIdList faield:", e);
            throw new UedmException(-200, e.getMessage());
        }
        return  list;
    }

    public List<PathInfoBean> getPathByIdList(List<String> deviceIds, String userName) throws UedmException
    {
        List<PathInfoBean> result = new ArrayList<>();
        List<ResourceBaseEntity> entities = resourceBaseCacheManager.getEntityByIds(new HashSet<>(deviceIds));
        for (ResourceBaseEntity entity : entities) {
            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setId(entity.getId());
            pathInfoBean.setIdPath(entity.toStringPathId());
            pathInfoBean.setNamePath(entity.getPathName());
            result.add(pathInfoBean);
        }
        return result;
    }

    public List<UpDownloadFileBean> selectFileById(List<String> fileIds, String userName) throws UedmException
    {
        try
        {
            List<UpDownloadFileEntity> upDownloadFileEntities = upDownloadFileRepository.selectByIds(fileIds);
            return UpDownloadFileConverter.INSTANCE.listUpDownloadFileEntityToUpDownloadFileBean(upDownloadFileEntities);
        }
        catch (Exception e)
        {
            log.error("selectFileById faield:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }

    /*删除文件*/
    public Integer deleteFileInfo(List<String> fileIds) throws UedmException
    {
        try
        {
            return peakShiftTemplateFileService.deleteFiles(fileIds);
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl deleteFileInfo faield:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }



    public MonitorObjectBean selectById(String id) throws UedmException
    {
        try {
            DeviceEntity deviceEntity = deviceCacheManager.selectDeviceById(Collections.singleton(id))
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (deviceEntity == null) {
                return new MonitorObjectBean();
            }

            MonitorObjectBean moObject = new MonitorObjectBean();
            BeanUtils.copyProperties(deviceEntity, moObject);
            return moObject;
        } catch (Exception e) {
            log.error("getMonitorObjectByOid in confS is error!", e);
            throw new UedmException(-1, e.getMessage());
        }
    }



    public PageInfo<IdNameBean> selectBattExtendAttribute(ServiceBaseInfoBean serviceBean) throws UedmException
    {
        if (serviceBean == null)
        {
            log.error("ConfigurationManagerRpcImpl->selectBattExtendAttribute param is blank. serviceBean={}",
                    serviceBean);
            return new PageInfo<>(new ArrayList<>());
        }
        List<IdNameBean> result = deviceCacheManager.getDevicesByMoc(MocOptional.BATTERY.getId()).stream()
                .map(entity -> {
                    IdNameBean idNameBean = new IdNameBean();
                    idNameBean.setId(entity.getId());
                    try {
                        idNameBean.setName(JsonUtils.objectToJson(entity.getExattribute()));
                    } catch (com.zte.uedm.basis.exception.UedmException e) {
                        log.error("selectBattExtendAttribute setName error, id:{}, attribute:{}, message:", entity.getId(), entity.getExattribute(), e);
                    }
                    return idNameBean;
                }).collect(Collectors.toList());
        return new PageInfo<>(result);
    }

    public List<IdNameBean> selectAllBatteryExtendAttribute(List<String> ids) throws UedmException
    {
        if(CollectionUtils.isEmpty(ids))
        {
            log.warn("configurationRpcs -> selectAllBatteryExtendAttribute ids is empty!");
            return new ArrayList<>();
        }
        List<IdNameBean> result = deviceCacheManager.getDeviceByIdsAndMoc(ids, MocOptional.BATTERY.getId()).stream()
                .map(entity -> {
                    IdNameBean idNameBean = new IdNameBean();
                    idNameBean.setId(entity.getId());
                    try {
                        idNameBean.setName(JsonUtils.objectToJson(entity.getExattribute()));
                    } catch (com.zte.uedm.basis.exception.UedmException e) {
                        log.error("selectAllBatteryExtendAttribute setName error, id:{}, attribute:{}, message", entity.getId(), entity.getExattribute(), e);
                    }
                    return idNameBean;
                }).collect(Collectors.toList());
        return result;
    }

    /**
     * 查询分域后的开关电源树
     * 根据逻辑组id获取所有子节点
     * resource-base/getChildrenLogicGroupId
     *
     * @param logicGroupId
     * @param userName
     * @param languageOption
     * @return
     */
    public List<ResourceBaseBean> getChildrenLogicGroupId(String logicGroupId, String siteLevel,
                                                          String powerSupplyScene, String userName, String languageOption)
    {
        /* Started by AICoder, pid:i08fdt12c7w2dab145860b7710e81c31ad85eb98 */
        // 初始化列表以存储结果

        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        if(CollectionUtils.isNotEmpty(allResourceBase)) {
            List<ResourceBaseEntity> tmpEntityList = allResourceBase.stream().filter(entity -> {
                // 检查逻辑组ID是否为“Root”
                // 从数据库中获取具有指定逻辑组ID的资源
                if (GlobalOptional.GLOBAL_ROOT.equals(logicGroupId) && GlobalOptional.GLOBAL_ROOT.equals(entity.getParentId())) {
                    // 从数据库中获取所有具有“Root”作为parent_id的资源
                    return true;
                } else {
                    return entity.getId().equals(logicGroupId);
                }
            }).collect(Collectors.toList());

            FieldRecursiveParamsDto recursiveDto = new FieldRecursiveParamsDto(logicGroupId, siteLevel, powerSupplyScene);

            // 查询站点并过滤
            queryFieldAndFilter(recursiveDto);

            // 递归遍历结果列表并过滤结果
            getChildEntityWithRecursive(tmpEntityList, allResourceBase, recursiveDto);

            // 过滤资源类型为“Site”、“RealGroup”或“MonitorObject”的资源
            List<ResourceBaseBean> resultList = tmpEntityList.stream()
                    .filter(entity -> checkModel(entity.getModel()))
                    .map(entity ->{
                        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
                        resourceBaseBean.setId(entity.getId());
                        resourceBaseBean.setMoc(entity.getMoc());
                        return resourceBaseBean;
                    })
                    .collect(Collectors.toList());
            // resultList存在相同的bean，根据id进行去重
            Map<String, ResourceBaseBean> collect = resultList.stream().collect(Collectors.toMap(ResourceBaseBean::getId, Function.identity(), (v1, v2) -> v1));
            return new ArrayList<>(collect.values());
        }

        return new ArrayList<>();
        /* Ended by AICoder, pid:i08fdt12c7w2dab145860b7710e81c31ad85eb98 */
    }

    public void queryFieldAndFilter( FieldRecursiveParamsDto recursiveDto) {
        String logicGroupId = recursiveDto.getLogicGroupId(), siteLevel = recursiveDto.getSiteLevel(), powerSupplyScene = recursiveDto.getPowerSupplyScene();
        try {
            List<BaseCacheBean> fieldCacheBeans = fieldCacheManager.queryAll();
            if(CollectionUtils.isNotEmpty(fieldCacheBeans)) {
                List<FieldEntity> tmpFieldEntityList = fieldCacheBeans.stream().filter(Objects::nonNull)
                        .map(FieldEntity.class::cast).collect(Collectors.toList());
                List<FieldDSEntity> fieldDSEntities = realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(tmpFieldEntityList);

                if(StringUtils.isNotBlank(siteLevel)) {
                    Set<String> result01 = fieldDSEntities.stream().filter(dsEntity ->  {
                        String level = dsEntity.getSiteLevel();
                        List<String> powerSupplySceneSet = dsEntity.getFieldPowerSupply();
                        return !siteLevel.equals(level) || (StringUtils.isNotBlank(powerSupplyScene) &&
                                powerSupplySceneSet != null && !powerSupplySceneSet.contains(powerSupplyScene));
                    }).map(ResourceBaseEntity::getId).collect(Collectors.toSet());
                    recursiveDto.setSiteLevelFilterIds(result01);
                }
                if(StringUtils.isNotBlank(siteLevel)) {
                    Set<String> result02 = fieldDSEntities.stream().filter(dsEntity -> {
                        List<String> powerSupplySceneSet = dsEntity.getFieldPowerSupply();
                        return powerSupplySceneSet != null && !powerSupplySceneSet.contains(powerSupplyScene);
                    }).map(ResourceBaseEntity::getId).collect(Collectors.toSet());
                    recursiveDto.setSupplySceneFilterIds(result02);
                }
            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("[getChildrenLogicGroupId] query field error", e);
        }
    }

    public void getChildEntityWithRecursive(List<ResourceBaseEntity> tmpEntityList,
                                            List<ResourceBaseEntity> allResourceBase,
                                            FieldRecursiveParamsDto recursiveDto) {
        if (CollectionUtils.isNotEmpty(tmpEntityList) && CollectionUtils.isNotEmpty(allResourceBase)) { // 为空说明没有节点需要处理了
            List<ResourceBaseEntity> tmpEntityList01 = new ArrayList<>();
            loopForSubEntities(tmpEntityList, allResourceBase, tmpEntityList01); // 遍历所有已有节点，找到他们的子节点
            if (CollectionUtils.isNotEmpty(tmpEntityList01)) { // 为空说明到达叶子节点
                tmpEntityList01 = tmpEntityList01.stream() // 过滤符合条件的节点
                        .filter(getResourceBaseEntityPredicate(recursiveDto)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tmpEntityList01)) { // 为空说明到达叶子节点
                    // 递归查找已找到的节点的下级的节点，直到到达最底层的叶子节点
                    getChildEntityWithRecursive(tmpEntityList01, allResourceBase, recursiveDto);
                    tmpEntityList.addAll(tmpEntityList01); // 将在这一层找到的节点添加到结果中
                }
            }
        }
    }

    private void loopForSubEntities(List<ResourceBaseEntity> tmpEntityList, List<ResourceBaseEntity> allResourceBase, List<ResourceBaseEntity> tmpEntityList01) {
        for (ResourceBaseEntity resEntity : tmpEntityList) {
            List<ResourceBaseEntity> collect = allResourceBase.stream()
                    .filter(entity -> entity.getParentId() != null && entity.getParentId().equals(resEntity.getId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)) {
                tmpEntityList01.addAll(collect);
            }
        }
    }

    @NotNull
    private Predicate<ResourceBaseEntity> getResourceBaseEntityPredicate(FieldRecursiveParamsDto recursiveDto) {
        String logicGroupId = recursiveDto.getLogicGroupId(), siteLevel = recursiveDto.getSiteLevel(), powerSupplyScene = recursiveDto.getPowerSupplyScene();
        Set<String> siteLevelFilterIds = recursiveDto.getSiteLevelFilterIds(), supplySceneFilterIds = recursiveDto.getSupplySceneFilterIds();
        return childNode -> {
            if (StringUtils.isNotBlank(siteLevel)) {
                return siteLevelFilterIds == null || !siteLevelFilterIds.contains(childNode.getId());
            } else if (StringUtils.isNotBlank(powerSupplyScene)) {
                return supplySceneFilterIds == null || !supplySceneFilterIds.contains(childNode.getId());
            } else {
                return true;
            }
        };
    }

    /**
     * 查询分域后孩子站点下的开关电源树 (工程配置适配后只返回id moc parentId)
     *
     * @param logicGroupId
     * @param userName
     * @param languageOption
     * @return
     */
    public List<ResourceBaseBean> getSiteChildrenLogicGroupId(String logicGroupId, String siteLevel,
                                                              String powerSupplyScene, String userName, String languageOption)
    {
        if (StringUtils.isBlank(logicGroupId)){
            return new ArrayList<>();
        }
        ResourceBaseEntity resourceBaseById = resourceBaseCacheManager.getResourceBaseById(logicGroupId);
        if (null==resourceBaseById || StringUtils.isBlank(resourceBaseById.getId())){
            return new ArrayList<>();
        }

        List<FieldEntity> fieldEntities = fieldCacheManager.getFieldMapBeans().values().stream()
                .filter(entity -> logicGroupId.equals(entity.getParentId()) && MocOptional.SITE.getId().equals(entity.getMoc()))
                .collect(Collectors.toList());
        fieldEntities = filterByPriorityAndPowerSupply(fieldEntities, siteLevel, powerSupplyScene);
        List<String> siteIds = fieldEntities.stream().map(ResourceBaseEntity::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        List<DeviceEntity> deviceEntities = deviceCacheManager.selectAllDevice().stream()
                .filter(entity -> null != entity.getPathId() && Arrays.stream(entity.getPathId()).anyMatch(siteIds::contains))
                .collect(Collectors.toList());

        List<ResourceBaseEntity> allEntities = new ArrayList<>();
        allEntities.add(resourceBaseById);
        allEntities.addAll(fieldEntities);
        allEntities.addAll(deviceEntities);

        try {
            List<String> authPositionsByUser = getAuthPositionsByUser(userName);
            if (CollectionUtils.isNotEmpty(authPositionsByUser)) {
                allEntities = allEntities.stream().filter(entity -> authPositionsByUser.contains(entity.getId())).collect(Collectors.toList());
            }
        } catch (UedmException e) {
            log.warn("getSiteChildrenLogicGroupId getAuthPositionsByUser error, userName:{}, message:", userName, e);
        }
        return toResourceBaseBean(allEntities);
    }
    public List<ResourceBaseBean> toResourceBaseBean(List<ResourceBaseEntity> entities){
        List<ResourceBaseBean> result = new ArrayList<>();
        for (ResourceBaseEntity entity : entities) {
            ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
            resourceBaseBean.setId(entity.getId());
            resourceBaseBean.setMoc(entity.getMoc());
            resourceBaseBean.setParentId(entity.getParentId());
            result.add(resourceBaseBean);
        }
        return result;
    }

    public List<FieldEntity> filterByPriorityAndPowerSupply(List<FieldEntity> fieldEntities, String siteLevel, String powerSupplyScene) {
        // 过滤站点等级
        List<FieldEntity> priorityList = new ArrayList<>();
        if (StringUtils.isNotBlank(siteLevel)){
            for (FieldEntity entity : fieldEntities) {
                FieldDSEntity fieldDSEntity = new FieldDSEntity();
                fieldDSEntity.setExattribute(entity.getExattribute());
                try {
                    fieldDSEntity.buildPriority();
                    if (siteLevel.equals(fieldDSEntity.getSiteLevel())){
                        priorityList.add(entity);
                    }
                }catch (com.zte.uedm.basis.exception.UedmException e){
                    log.error("getSiteChildrenLogicGroupId fieldDSEntity buildPriority error:", e);
                }
            }
        }else {
            priorityList = fieldEntities;
        }
        // 过滤供电场景
        List<FieldEntity> powerSupplyList = new ArrayList<>();
        if (StringUtils.isNotBlank(powerSupplyScene)){
            for (FieldEntity entity : priorityList) {
                FieldDSEntity fieldDSEntity = new FieldDSEntity();
                fieldDSEntity.setExattribute(entity.getExattribute());
                try {
                    fieldDSEntity.buildFieldPowerSupply();
                    List<String> powerSupply = fieldDSEntity.getFieldPowerSupply();
                    if (null == powerSupply){
                        continue;
                    }
                    Collections.sort(powerSupply);
                    String powerSupplyString = String.join("-", powerSupply);
                    if (powerSupplyScene.equals(powerSupplyString)){
                        powerSupplyList.add(entity);
                    }
                }catch (com.zte.uedm.basis.exception.UedmException e){
                    log.error("getSiteChildrenLogicGroupId fieldDSEntity buildFieldPowerSupply or buildPriority error:", e);
                }
            }
        }else {
            powerSupplyList = priorityList;
        }
        return powerSupplyList;
    }

    /**
     * 根据监控对象ids找到关联的站点
     *
     * @param moIdList
     * @param userName
     * @return
     * @throws UedmException
     */
    public Map<String, SiteBean> getSiteByMoIds(List<String> moIdList, String userName) throws UedmException
    {
        /* Started by AICoder, pid:y9db57ec06jbdb01438809b0f05ee4003a420a29 */
        Map<String, SiteBean> resultMap = new HashMap<>();
        /* Ended by AICoder, pid:y9db57ec06jbdb01438809b0f05ee4003a420a29 */
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getResourceBaseByIds(new HashSet<>(moIdList));
        allResourceBase = Optional.ofNullable(allResourceBase).orElse(Collections.emptyList());
        log.info("allResourceBase size:{}",allResourceBase.size());
        for (ResourceBaseEntity resourceBaseEntity : allResourceBase) {
            log.info("resourceBaseEntity.getId():{}",resourceBaseEntity.getId());
            SiteBean siteBean = getSiteInfoByMoIds(resourceBaseEntity);
            resultMap.put(resourceBaseEntity.getId(), siteBean);
        }
        return resultMap;
    }

    /* Started by AICoder, pid:o4fb8ib435p6dca14c3109bb20477820e1a8f154 */
    public SiteBean getSiteInfoByMoIds(ResourceBaseEntity monitorObject) {
        String[] pathId = monitorObject.getPathId(); // pathId 用于查询站点的信息
        if (pathId != null && pathId.length > 0) {
            List<FieldEntity> fieldEntities = fieldCacheManager.selectByIds(new HashSet<>(Arrays.asList(pathId)));
            if (CollectionUtils.isNotEmpty(fieldEntities)) {
                List<FieldDSEntity> fieldDSEntities = realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(fieldEntities);
                if (CollectionUtils.isNotEmpty(fieldDSEntities)) {
                    for (FieldDSEntity fieldDSEntity : fieldDSEntities) {
                        if (MocOptional.SITE.getId().equals(fieldDSEntity.getMoc())) {
                            SiteBean siteBean = new SiteBean();
                            siteBean.setId(fieldDSEntity.getId());
                            siteBean.setMoc(fieldDSEntity.getMoc());
                            siteBean.setGmtCreate(fieldDSEntity.getGmtCreateToString());
                            siteBean.setName(fieldDSEntity.getName());
                            String level = Optional.ofNullable(fieldDSEntity.getSiteLevel()).orElse("");
                            siteBean.setSiteLevel(level);
                            List<String> powerSupplyScene = Optional.ofNullable(fieldDSEntity.getFieldPowerSupply()).orElse(new ArrayList<>());
                            Collections.sort(powerSupplyScene);
                            String powerSupplySceneId = CollectionUtils.isEmpty(powerSupplyScene) ? null : String.join("-", powerSupplyScene);
                            siteBean.setPowerSupplyScene(powerSupplySceneId);
                            return siteBean;
                        }
                    }
                }
            }
        }
        return null;
    }
    /* Ended by AICoder, pid:o4fb8ib435p6dca14c3109bb20477820e1a8f154 */

    /**
     * 根据逻辑组id获取子节点id
     *
     * @param logicGroupId
     * @return
     * @throws UedmException
     */
    public LogicIdsDto getChildByLogicGroupId(String logicGroupId, String userName) throws UedmException
    {
        try
        {
            LogicIdsDto logicIdsDto = new LogicIdsDto();
            List<String> idList = new ArrayList<>();
            TreeAssetAuthorityQueryVo treeAssetAuthorityQueryVo = new TreeAssetAuthorityQueryVo();
            treeAssetAuthorityQueryVo.setId(logicGroupId);
            treeAssetAuthorityQueryVo.setScene("1");
            treeAssetAuthorityQueryVo.setFlag(true);
            treeAssetAuthorityQueryVo.setExcludeMo(true);
            Map<String, String> headerMap = HeaderUtils.buildUserNameHeaders(Tools.escape(userName));
            Response<ResponseBean> response = configurationRpcs.getChildByLogicGroupId(treeAssetAuthorityQueryVo,
                    headerMap).execute();
            log.info("==========getChildByLogicGroupId response : {}==========", JSONObject.toJSONString(response));
            int total = response.body().getTotal();
            log.info("==========getChildByLogicGroupId total : {}==========", JSONObject.toJSONString(total));
            if (response.isSuccessful())
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl getPathNameByLogicGroupId() failed, code is " + response.body()
                                    .getCode());
                }

                log.info("ConfigurationManagerRpcImpl getChildByLogicGroupId success!");
                List<TreeBean> treeList = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()),
                        ArrayList.class, TreeBean.class);
                log.info("==========getChildByLogicGroupId treeList : {}==========", JSONObject.toJSONString(treeList));
                for (TreeBean bean : treeList)
                {
                    String id = bean.getId();
                    idList.add(id);
                }
                logicIdsDto.setIds(idList);
                logicIdsDto.setTotal(total);
                return logicIdsDto;
            }
            else
            {
                log.info("ConfigurationManagerRpcImpl getChildByLogicGroupId failed!");
                return logicIdsDto;
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl getChildByLogicGroupId failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }

    /**
     * resource-base/getSiteInfoByMoId
     * 根据监控对象id查找对应的站点信息
     */
    public SiteBean getSiteInfoByMoId(String moId) throws UedmException
    {
        // 从资源树中查询资源对象
        ResourceBaseEntity monitorObject = resourceBaseCacheManager.getResourceBaseById(moId);
        if (monitorObject == null) {
            return null;
        }
        String[] pathId = monitorObject.getPathId(); // pathId去查询站点的信息
        if (pathId != null && pathId.length > 0) {
            List<FieldEntity> fieldEntities = fieldCacheManager.selectByIds(new HashSet<>(Arrays.asList(pathId)));
            if(CollectionUtils.isNotEmpty(fieldEntities)) {
                List<FieldDSEntity> fieldDSEntities = realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(fieldEntities);
                if(CollectionUtils.isNotEmpty(fieldDSEntities)) {
                    for (FieldDSEntity fieldDSEntity : fieldDSEntities) {
                        if(MocOptional.SITE.getId().equals(fieldDSEntity.getMoc())) {
                            SiteBean siteBean = new SiteBean();
                            siteBean.setId(fieldDSEntity.getId());
                            siteBean.setMoc(fieldDSEntity.getMoc());
                            siteBean.setGmtCreate(fieldDSEntity.getGmtCreateToString());
                            siteBean.setName(fieldDSEntity.getName());
                            String level = Optional.ofNullable(fieldDSEntity.getSiteLevel()).orElse("");
                            siteBean.setSiteLevel(level);
                            List<String> powerSupplyScene = Optional.ofNullable(fieldDSEntity.getFieldPowerSupply()).orElse(new ArrayList<>());
                            Collections.sort(powerSupplyScene);
                            String powerSupplySceneId =CollectionUtils.isEmpty(powerSupplyScene) ? null : String.join("-", powerSupplyScene);
                            siteBean.setPowerSupplyScene(powerSupplySceneId);
                            return siteBean;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 电池监控对象id查询其对应的电池组
     *
     * @param moId 监控对象ID
     * @return 电池组对象信息
     * @throws UedmException
     */
    public String selectBattPackObject(String moId, String languageOption) throws UedmException
    {
        if(StringUtils.isBlank(moId))
        {
            log.warn("ConfigurationManagerPpcImpl selectBattPackObject -> param is empty");
            return null;
        }
        //查找电池parentid是否为电池组
        DeviceEntity deviceEntity = deviceCacheManager.getDeviceById(moId);
        if (deviceEntity != null && StringUtils.isNotEmpty(deviceEntity.getParentId())){
            DeviceEntity deviceParentEntity = deviceCacheManager.getDeviceById(deviceEntity.getParentId());
            if (deviceParentEntity != null && MocOptional.BATTERY_SET.getId().equals(deviceParentEntity.getMoc())){
                return deviceParentEntity.getId();
            }
        }

        //parentid没查到电池组,就查关联的设备
        /* Started by AICoder, pid:26797653fa192e814c120a50f039cd1b6653198f */
        try {
            Map<String, List<String>> relationmap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Arrays.asList(moId),null);
            if (MapUtils.isEmpty(relationmap)){
                log.warn("resourceCollectorRelationCacheManager getRelationResourceIdByResourceId is empty");
                return null;
            }
            List<String> relationResourceId = relationmap.getOrDefault(moId, Collections.emptyList());
            log.info("relationResourceId size: {}", relationResourceId.size());
            List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(relationResourceId);
            deviceListByIds = deviceListByIds.stream()
                    .filter(bean -> MocOptional.BATTERY_SET.getId().equals(bean.getMoc()))
                    .collect(Collectors.toList());
            log.info("deviceListByIds size: {}", deviceListByIds.size());
            return deviceListByIds.stream().findFirst().map(DeviceEntity::getId).orElse(null);
            /* Ended by AICoder, pid:26797653fa192e814c120a50f039cd1b6653198f */
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("resourceCollectorRelationCacheManager getRelationResourceIdByResourceId is empty");
            return null;
        }
    }

    /**
     * 根据站点id查询站点下指定moc监控对象
     * resource-base/getMoListBySiteId
     *
     * @param selectMoListBySiteIdVo 参数
     * @param userName 参数
     * @return List<MonitorObjectBean> 返回值
     * @throws UedmException 异常
     */
    public List<MonitorObjectBean> getMoListBySiteId(SelectMoListBySiteIdVo selectMoListBySiteIdVo, String userName)
            throws UedmException
    {
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        String siteId = selectMoListBySiteIdVo.getSiteId();
        List<String> moList = selectMoListBySiteIdVo.getMoList();
        if(StringUtils.isNotBlank(siteId) && CollectionUtils.isNotEmpty(allResourceBase)) {
            return allResourceBase.stream().filter(entity -> {
                // 一个节点的pathId中包含siteId，且siteId不是本身，则site是entity的父级
                boolean isSiteSubNode = entity.toStringPathId().contains(siteId) && !siteId.equals(entity.getId());
                return CollectionUtils.isEmpty(moList) ? isSiteSubNode :
                        isSiteSubNode && moList.contains(entity.getMoc());
            }).map(entity->{
                MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
                monitorObjectBean.setId(entity.getId());
                return monitorObjectBean;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 查询所有站点
     *
     * @return
     */
    public Map<String, FieldEntity> getAllSite()
    {
        Map<String, FieldEntity> fieldEntityMap = new HashMap<>();
        List<FieldEntity> fieldEntities = fieldCacheManager.queryAllField();
        if (!CollectionUtils.isEmpty(fieldEntities)){
            fieldEntityMap = fieldEntities.stream()
                    .filter(entity-> StringUtils.isNotBlank(entity.getId()))
                    .collect(Collectors.toMap(FieldEntity::getId, entity -> entity, (key1,key2)->key2));
        }
        return fieldEntityMap;
    }

    /**
     * resource-base/getRootGroupSiteChildren
     * 查询Root下的所有分组站点及监控对象
     *
     * 1.所有parent_id是root的元素
     * 2.所有上一级的元素的子元素：需要符合是site、realgroup、monitor object之一
     * 3.重复2步骤，直到叶子节点
     *
     * @param userName 用户名
     * @param languageOption 国际化
     * @return List<ResourceBaseBean> 资源bean
     */
    public List<ResourceBaseBean> getRootAllChildren(String userName, String languageOption)
    {
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        /* Started by AICoder, pid:ff83f493b590cf314566091c204c871b8f67999d */
        if(CollectionUtils.isNotEmpty(allResourceBase)) {
            return allResourceBase.stream()
                    .filter(entity -> {
                        String[] pathId = entity.getPathId();
                        return null != pathId && Arrays.asList(pathId).contains(GlobalOptional.GLOBAL_ROOT);
                    })
                    .filter(entity -> ModelOptional.DEVICE.getId().equals(entity.getModel())
                            || MocOptional.COMMON_GROUP.getId().equals(entity.getMoc())
                            || MocOptional.SITE.getId().equals(entity.getMoc()))
                    .map(entity ->{
                        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
                        resourceBaseBean.setId(entity.getId());
                        resourceBaseBean.setMoc(entity.getMoc());
                        return resourceBaseBean;
                    }).collect(Collectors.toList());
        }
        /* Ended by AICoder, pid:ff83f493b590cf314566091c204c871b8f67999d */
        return new ArrayList<>();
    }

    /* Started by AICoder, pid:f6fe4f4da8886e5146890a606096901dd1462b37 */
    public List<ResourceBaseEntity> collectValidNodes(List<ResourceBaseEntity> allResourceBase, String rootId) {
        List<ResourceBaseEntity> validNodes = new ArrayList<>();
        for (ResourceBaseEntity node : allResourceBase) {
            if (rootId.equals(node.getParentId())) {
                if (checkModel(node.getModel())) {
                    validNodes.add(node);
                    validNodes.addAll(collectValidNodes(allResourceBase, node.getId()));
                }
            }
        }
        return validNodes;
    }

    public boolean checkModel(String model) {
        return ModelOptional.GROUP.getId().equals(model) ||
                ModelOptional.FIELD.getId().equals(model) ||
                ModelOptional.DEVICE.getId().equals(model);
    }
    /* Ended by AICoder, pid:f6fe4f4da8886e5146890a606096901dd1462b37 */

    /**
     * 根据监控对象id查询关联的监控设备下指定moc类型监控对象
     *
     * @param getRelationDeviceMoListDto
     * @return
     * @throws UedmException
     */
    public List<DeviceEntity> getRelationDeviceMoListByMoId(GetRelationDeviceMoListDto getRelationDeviceMoListDto)
    {
        if(StringUtils.isBlank(getRelationDeviceMoListDto.getMoId()))
        {
            log.warn("ConfigurationManagerRpcImpl getRelationDeviceMoListByMoId -> param is empty");
            return new ArrayList<>();
        }
        String moId = getRelationDeviceMoListDto.getMoId();
        List<String> mocList = getRelationDeviceMoListDto.getMocList();
        List<DeviceEntity> deviceEntityList = new ArrayList<>();
        try {
            Map<String, List<String>> relationMap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Arrays.asList(moId),null);

            if (relationMap != null && relationMap.containsKey(moId)) {
                List<String> resourceId = new ArrayList<>(relationMap.get(moId));
                resourceId.add(moId);
                List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(resourceId);
                if (deviceListByIds != null) {
                    deviceEntityList = deviceListByIds.stream()
                            .filter(Objects::nonNull)
                            .filter(entity -> entity.getMoc() != null && mocList.contains(entity.getMoc()))
                            .collect(Collectors.toList());
                }

            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("ConfigurationManagerRpcImpl getRelationDeviceMoListByMoId ->resourceCollectorRelationCacheManager error");
        }
        return deviceEntityList;
    }

    /**
     * 根据id列表查询路径
     *
     * @param ids
     * @return
     * @throws UedmException
     */
    public List<ResourceBaseBean> getPathsByIds(List<String> ids) throws UedmException
    {
        try
        {
            List<ResourceBaseBean> resourceBaseBeans = new ArrayList<>();
            Response<ResponseBean> response = configurationRpcs.getPathsByIds(ids).execute();

            if (response.isSuccessful())
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl getPathsByIds() failed, code is " + response.body()
                                    .getCode());
                }

                log.info("ConfigurationManagerRpcImpl getPathsByIds success!");
                resourceBaseBeans = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()),
                        List.class, ResourceBaseBean.class);
                return resourceBaseBeans;
            }
            log.info("ConfigurationManagerRpcImpl getPathsByIds failed!");
            return resourceBaseBeans;
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl getPathsByIds failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }



    public List<RecordIndexBean> selectRecordIndexByCondition(RecordIndexBean recordIndexBean, Integer pageNo,
                                                              Integer pageSize, String languageOption) throws UedmException
    {
        List<RecordIndexBean> recordIndexBeans = new ArrayList<>();
        try
        {
            Response<ResponseBean> response = configurationRpcs.selectRecordIndexByCondition(recordIndexBean, pageNo,
                    pageSize, languageOption).execute();
            if (response.isSuccessful())
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl selectRecordIndexByCondition() failed, code is "
                                    + response.body().getCode());
                }
                log.info("ConfigurationManagerRpcImpl selectRecordIndexByCondition success!");
                recordIndexBeans = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()),
                        ArrayList.class, RecordIndexBean.class);
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectRecordIndexByCondition failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
        return recordIndexBeans;
    }

    public List<RecordIndexBean> selectRecordIndexByConditionAllName(RecordIndexBean recordIndexBean, Integer pageNo,
                                                                     Integer pageSize) throws UedmException
    {
        List<RecordIndexBean> recordIndexBeans = new ArrayList<>();
        try
        {
            Response<ResponseBean> response = configurationRpcs.selectRecordIndexByConditionAllName(recordIndexBean,
                    pageNo, pageSize).execute();
            if (response.isSuccessful())
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl selectRecordIndexByConditionAllName() failed, code is "
                                    + response.body().getCode());
                }
                log.info("ConfigurationManagerRpcImpl selectRecordIndexByConditionAllName success!");
                recordIndexBeans = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()),
                        ArrayList.class, RecordIndexBean.class);
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectRecordIndexByConditionAllName failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
        return recordIndexBeans;
    }

    /**
     * 根据ids获取其子节点下所有的指定类型监控对象
     *
     * @param idList
     * @param moc
     * @return
     * @throws UedmException
     */
    public Map<String, List<MoBasicInfoVo>> getChildMonitorObjects(List<String> idList, String moc) throws UedmException
    {
        if (CollectionUtils.isEmpty(idList) || StringUtils.isBlank(moc)) {
            log.warn("ConfigurationManagerRpcImpl getChildMonitorObjects -> param is empty");
            return new HashMap<>();
        }

        Map<String, List<MoBasicInfoVo>> result = new HashMap<>();
        try {
            Map<String, List<DeviceEntity>> deviceEntityMap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(idList,null)
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                        List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(e.getValue());
                        return deviceListByIds.stream()
                                .filter(Objects::nonNull)
                                .filter(device -> device.getMoc().equals(moc))
                                .collect(Collectors.toList());
                    }));
            for(Map.Entry<String,List<DeviceEntity>> entry : deviceEntityMap.entrySet())
            {
                String id = entry.getKey();
                List<DeviceEntity> value = entry.getValue();
                List<MoBasicInfoVo> moBasicInfoVos = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(value))
                {
                    for(DeviceEntity entity : value)
                    {
                        moBasicInfoVos.add(new MoBasicInfoVo(entity));
                    }
                }
                result.put(id, moBasicInfoVos);
            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("ConfigurationManagerRpcImpl getChildMonitorObjects ->resourceCollectorRelationCacheManager error");
        }
        log.info("ConfigurationManagerRpcImpl getChildMonitorObjects -> map size: {}", result.size());
        return result;
    }

    /**
     * 根据开关电源id获取其子节点下所有的电池回路
     *
     * @param idList
     * @return
     * @throws UedmException
     */
    public Map<String, List<MoBasicInfoVo>> getChildBattLoops(List<String> idList) throws UedmException
    {
        if (CollectionUtils.isEmpty(idList) ) {
            log.warn("ConfigurationManagerRpcImpl getChildBattLoops -> param is empty");
            return new HashMap<>();
        }

        Map<String, List<MoBasicInfoVo>> result = new HashMap<>();
        try {
            Map<String, List<DeviceEntity>> deviceEntityMap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(idList,null)
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                        List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(e.getValue());
                        return deviceListByIds.stream()
                                .filter(Objects::nonNull)
                                .filter(device -> device.getMoc().equals(MocOptional.BATTERY.getId()))
                                .collect(Collectors.toList());
                    }));
            for(Map.Entry<String,List<DeviceEntity>> entry : deviceEntityMap.entrySet())
            {
                String id = entry.getKey();
                List<DeviceEntity> value = entry.getValue();
                List<MoBasicInfoVo> moBasicInfoVos = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(value))
                {
                    for(DeviceEntity entity : value)
                    {
                        Object exattribute = entity.getExattribute();
                        if (batteryAttributeUtils.getIsLoop(exattribute)){
                            moBasicInfoVos.add(new MoBasicInfoVo(entity));
                        }
                    }
                }
                result.put(id, moBasicInfoVos);
            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("ConfigurationManagerRpcImpl getChildBattLoops ->getRelatedDeviceIdbyDeviceIdsAndMoc error");
        }
        log.info("ConfigurationManagerRpcImpl getChildMonitorObjects -> map size: {}", result.size());
        return result;
    }

    /**
     * 根据逻辑组id、name、moc查询逻辑组下指定moc类型的监控对象id，name，namepath
     *
     * @param getMoByConditionDto
     * @param userName
     * @return
     * @throws UedmException
     */
    public Pair<List<PathInfoBean>, Integer> getMoByLogIdAndName(GetMoByConditionDto getMoByConditionDto,
                                                                 String userName) throws UedmException
    {
        /* Started by AICoder, pid:cc42ardbbc56e52141380ba9d06e563fe592e934 */
        Integer total = 0;
        String logroupId = Optional.ofNullable(getMoByConditionDto.getLogroupId()).orElse("");
        String moc = Optional.ofNullable(getMoByConditionDto.getMoc()).orElse("");
        String name = Optional.ofNullable(getMoByConditionDto.getName()).orElse("");
        log.info("ConfigurationManagerRpcImpl getMoByLogIdAndName logroupId:{},moc:{},name:{}", logroupId, moc, name);
        List<PathInfoBean> monitorObjectBeans = new ArrayList<>();
        List<ResourceBaseEntity> allResourceBase = Optional.ofNullable(resourceBaseCacheManager.getAllResourceBase())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(ResourceBaseEntity.class::cast)
                .filter(e -> (StringUtils.isNotBlank(logroupId) && !Objects.equals(logroupId, MocOptional.GLOBAL.getId())
                        ? Arrays.asList(e.getPathId()).contains(logroupId)
                        : true)
                        && moc.equals(e.getMoc())
                        && e.getName().toUpperCase().contains(name.toUpperCase()))
                .collect(Collectors.toList());
        log.info("resourceBaseCacheManager getAllResourceBase filter->allResourceBase:{}", allResourceBase.size());
        if (!allResourceBase.isEmpty()) {
            monitorObjectBeans = allResourceBase.stream()
                    .map(entity -> {
                        PathInfoBean bean = new PathInfoBean();
                        bean.setId(entity.getId());
                        bean.setNamePath(entity.getPathName());
                        bean.setIdPath(StringUtils.join("/", entity.getPathId()));
                        log.info("getMoByLogIdAndName -> monitorObjectBean:{}", bean);
                        return bean;
                    })
                    .collect(Collectors.toList());
        }
        log.info("ConfigurationManagerRpcImpl getMoByLogIdAndName monitorObjectBeans :{}", monitorObjectBeans.size());
        return Pair.of(monitorObjectBeans, total);
        /* Ended by AICoder, pid:cc42ardbbc56e52141380ba9d06e563fe592e934 */
    }


    /**
     * 根据监控对象ids查询acdp的ids
     * resource-base/selectAcdpIdsByMoids
     *
     * 1.先从关联表中查询对应moId的数据,获取对应的设备id
     * 2.再从关联表中获取这些设备id关联的moId
     * 3.再用这些moId去resource里查询对应的数据
     * 4.结果是moId（初始提供的moid）,和moId(根据设备关联查询出来的moId)的集合的映射
     *
     * @param moIdList
     * @return
     */
    public Map<String, List<String>> selectAcdpIdsByDeviceIds(List<String> moIdList, String moc) throws UedmException
    {
        Map<String, List<String>> result = new HashMap<>();
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        if(CollectionUtils.isNotEmpty(moIdList) && CollectionUtils.isNotEmpty(allResourceBase)) {
            List<ResourceCollectorRelationEntity> allEntity = resourceCollectorRelationCacheManager.getAllEntity();
            // 准备需要的数据
            Map<String, List<ResourceCollectorRelationEntity>> moRelationMap =
                    allEntity.stream().collect(Collectors.groupingBy(ResourceCollectorRelationEntity::getResourceId));
            Map<String, List<ResourceCollectorRelationEntity>> coRelationMap =
                    allEntity.stream().collect(Collectors.groupingBy(ResourceCollectorRelationEntity::getCollectorId));
            Map<String, ResourceBaseEntity> resBaseEntitiesMap = allResourceBase.stream()
                    .collect(Collectors.toMap(ResourceBaseEntity::getId, value -> value, (e1, e2) -> e2));

            Set<String> moIdSet = new HashSet<>(moIdList);
            for (String moId : moIdSet) {
                if(StringUtils.isNotBlank(moId)) {
                    Set<String> moIds = loopForResult(moc, moId, moRelationMap, coRelationMap, resBaseEntitiesMap);
                    if(moIds != null && moIds.size() > 0) {
                        result.put(moId, new ArrayList<>(moIds));
                    }
                }
            }
        }
        return result;
    }

    private Set<String> loopForResult(String moc, String moId,
                                      Map<String, List<ResourceCollectorRelationEntity>> moRelationMap,
                                      Map<String, List<ResourceCollectorRelationEntity>> coRelationMap,
                                      Map<String, ResourceBaseEntity> resBaseEntitiesMap) {
        // 1.先从关联表中查询对应moId的数据,获取对应的设备id
        List<ResourceCollectorRelationEntity> resRelEntities = moRelationMap.get(moId);
        if (CollectionUtils.isNotEmpty(resRelEntities)) {
            Set<String> collectIdSet = resRelEntities.stream().map(ResourceCollectorRelationEntity::getCollectorId)
                    .collect(Collectors.toSet());
            // 2.再从关联表中获取这些设备id关联的moId
            if (CollectionUtils.isNotEmpty(collectIdSet)) {
                Map<String, List<ResourceCollectorRelationEntity>> m1 = collectIdSet.stream()
                        .filter(coRelationMap::containsKey)
                        .collect(Collectors.toMap(key -> key, coRelationMap::get));
                return m1.values().stream()
                        .flatMap(Collection::stream)
                        .map(relationEntity -> relationEntity == null ? null : relationEntity.getResourceId())
                        .filter(resId -> {
                            if (StringUtils.isBlank(resId)) {
                                return false; // resId为空排除
                            }
                            ResourceBaseEntity entity = resBaseEntitiesMap.get(resId);
                            if (entity == null) {
                                return false; // 根据id找不到对应资源实例，排除
                            }
                            // 没有moc表示不需要通过moc判断
                            return StringUtils.isBlank(moc) || moc.equals(entity.getMoc());
                        })
                        .collect(Collectors.toSet());
            }
        }
        return null;
    }



    /**
     * 监控对象查询其关联的监控对象列表,包含moc
     *
     * @param moId
     * @param moc
     * @return
     * @throws UedmException
     */
    public List<MoBasicInfoVo> selectMonitorObjectCorrelativeObjects(String moId, String moc) throws UedmException
    {
        /* Started by AICoder, pid:074be1a72eiba5c1485e09fc8036d82b3be88b25 */
        List<MoBasicInfoVo> result = new ArrayList<>();
        if(StringUtils.isAnyBlank(moId, moc))
        {
            log.warn("ConfigurationManagerRpcImpl selectMonitorObjectCorrelativeObjects -> param is empty");
            return result;
        }
        try {
            Map<String, List<String>> relationMap = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Arrays.asList(moId),null);
            /* Started by AICoder, pid:xb3579c6b5q3ee114c310964f0d6120bbb53ed24 */
            List<String> ids = new ArrayList<>(relationMap.getOrDefault(moId, Collections.emptyList()));
            ids.add(moId);
            /* Ended by AICoder, pid:xb3579c6b5q3ee114c310964f0d6120bbb53ed24 */
            List<DeviceEntity> deviceListByIds = deviceCacheManager.getDeviceListByIds(ids);
            deviceListByIds = deviceListByIds.stream()
                    .filter(Objects::nonNull)
                    .filter(e -> e.getMoc().equals(moc))
                    .collect(Collectors.toList());
            for (DeviceEntity entity : deviceListByIds){
                MoBasicInfoVo vo = new MoBasicInfoVo(entity);
                result.add(vo);
            }

            if (MocOptional.BATTERY.getId().equals(moc)){
                log.info("selectMonitorObjectCorrelativeObject--resultList:{}", result);
                result = result.stream()
                        .filter(bean -> MocOptional.BATTERY.getId().equals(bean.getMoc()))
                        .collect(Collectors.toList());
            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("ConfigurationManagerRpcImpl selectMonitorObjectCorrelativeObjects ->getRelatedDeviceIdbyDeviceIdsAndMoc error");
        }
        log.info("ConfigurationManagerRpcImpl selectMonitorObjectCorrelativeObject -> resultList size: {}", result.size());
        return result;
        /* Ended by AICoder, pid:074be1a72eiba5c1485e09fc8036d82b3be88b25 */
    }



    /**
     * 查询所有错峰设备缓存信息
     *
     * @return
     * @throws UedmException
     */
    public List<DevicePeakCacheInfoBean> queryAllList() throws UedmException
    {
        return devicePeakCacheInfoService.getAllList();
    }

    /**
     * 根据用户名称获取用户所有有权限的ids
     *
     * @param userName
     * @throws UedmException
     * @return用户所有有权限的ids contains mo and md and userName
     * 有权限的节点ids ps:拥有全部权限时  list为空
     */
    public List<String> getAuthPositionsByUser(String userName) throws UedmException
    {
        List<String> result = new ArrayList<>();
        return result;
    }

    /**
     * 根据逻辑组ID查询所有的父节点id
     *
     * @param logicId
     * @return
     * @throws UedmException
     */
    public List<String> selectAllParentByLogicId(String logicId) throws UedmException
    {
        try
        {
            List<String> parentIds = new ArrayList<>();
            Response<ResponseBean> response = configurationRpcs.selectAllParentByLogicId(logicId).execute();
            if (response.isSuccessful())
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl selectAllParentByLogicId() failed, code is " + response.body().getCode());
                }

                log.info("ConfigurationManagerRpcImpl selectAllParentByLogicId success!");
                parentIds = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), List.class,
                        String.class);
                return parentIds;
            }
            log.info("ConfigurationManagerRpcImpl selectAllParentByLogicId failed!");
            return parentIds;
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectByLogic failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }





    public List<AdapterPointEntity.AdapterPointSfVo> selectAllSFMdId(MonitorDeviceSfBean monitorDeviceSfBean) throws UedmException
    {
        /* Started by AICoder, pid:a02bakd606f090114f4d08d510686a0917562d85 */
        List<AdapterPointEntity.AdapterPointSfVo> adapterPointSfVoList = adapterCacheManager.selectAll()
                .stream()
                .flatMap(adapterEntity -> adapterEntity.getAdapterPoints().stream())
                .map(AdapterPointEntity::getSfVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        /* Ended by AICoder, pid:a02bakd606f090114f4d08d510686a0917562d85 */
        return adapterPointSfVoList;
    }


    public PageInfo<ResourceCollectorRelationEntity> getRelationByMoIdList(List<String> moIdList, Integer pageNo, Integer pageSize) throws UedmException {
        if (CollectionUtils.isEmpty(moIdList)) {
            return new PageInfo<>();
        }
        List<ResourceCollectorRelationEntity> relationEntities = resourceCollectorRelationCacheManager.getAllEntity().stream()
                .filter(Objects::nonNull)
                .filter(e -> moIdList.contains(e.getResourceId()))
                .map(ResourceCollectorRelationEntity.class::cast)
                .collect(Collectors.toList());
        log.info("ConfigurationManagerRpcImpl getRelationByMoIdList ->resourceCollectorRelationCacheManager getAllEntity size:{}",relationEntities.size());
        PageInfo<ResourceCollectorRelationEntity> result = new PageInfo<>(relationEntities);
        return result;
    }

    public PageInfo<ResourceCollectorRelationEntity> getRelationByMdIdList(List<String> mdIdList, Integer pageNo, Integer pageSize) throws UedmException {
        if (CollectionUtils.isEmpty(mdIdList)) {
            return new PageInfo<>();
        }
        List<ResourceCollectorRelationEntity> relationEntities = resourceCollectorRelationCacheManager.getAllEntity().stream()
                .filter(Objects::nonNull)
                .filter(e -> mdIdList.contains(e.getCollectorId()))
                .map(ResourceCollectorRelationEntity.class::cast)
                .collect(Collectors.toList());
        log.info("ConfigurationManagerRpcImpl getRelationByMdIdList ->resourceCollectorRelationCacheManager getAllEntity size:{}",relationEntities.size());
        PageInfo<ResourceCollectorRelationEntity> result = new PageInfo<>(relationEntities);
        return result;
    }

    public PageInfo<MonitorObjectDsBean> getLogicGroupChildByQuery(MocIdsVO mocIdsVO, String languageOption) throws UedmException {
        if (null == mocIdsVO) {
            throw UedmErrorCodeParameterUtil.parameterIsMustInput("param is empty!");
        }
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        if (CollectionUtils.isEmpty(allResourceBase)){
            return new PageInfo<>();
        }
        Stream<ResourceBaseEntity> stream = allResourceBase.stream()
                .filter(entity -> mocIdsVO.getMoc().equals(entity.getMoc()));
        if (StringUtils.isNotEmpty(mocIdsVO.getName())){
            stream = stream.filter(entity -> entity.getName().contains(mocIdsVO.getName()));
        }
        if (CollectionUtils.isNotEmpty(mocIdsVO.getIdList())){
            stream = stream.filter(entity -> Arrays.stream(entity.getPathId())
                    .anyMatch(id -> mocIdsVO.getIdList().contains(id)));
        }
        List<ResourceBaseEntity> baseEntities = stream.collect(Collectors.toList());

        sortEntities(baseEntities, mocIdsVO.getSort(), mocIdsVO.getOrder());
        List<ResourceBaseEntity> pagedList = PageUtils.getPagedList(baseEntities, mocIdsVO.getPageNo(), mocIdsVO.getPageSize());
        List<MonitorObjectDsBean> monitorObjectDsBeans = pagedList.stream().map(entity -> {
            MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
            monitorObjectDsBean.setId(entity.getId());
            monitorObjectDsBean.setPathId(entity.toStringPathId());
            monitorObjectDsBean.setPath(entity.getPathName());
            monitorObjectDsBean.setName(entity.getName());
            return monitorObjectDsBean;
        }).collect(Collectors.toList());
        PageInfo<MonitorObjectDsBean> result= new PageInfo<>(monitorObjectDsBeans);
        result.setTotal(monitorObjectDsBeans.size());
        return result;
    }

    public void sortEntities(List<ResourceBaseEntity> list, String sort, String order) {
        if(StringUtils.isEmpty(sort)){
            return;
        }
        /* Started by AICoder, pid:3639424590p1091140a50a73c0cd552bd0f1d237 */
        Comparator<ResourceBaseEntity> comparator = null;
        switch (sort) {
            case "path_name":
                comparator = Comparator.comparing(ResourceBaseEntity::getPathName);
                break;
            case "name":
                comparator = Comparator.comparing(ResourceBaseEntity::getName);
                break;
            default:
                comparator = Comparator.comparing(ResourceBaseEntity::getId);
        }

        if ("desc".equalsIgnoreCase(order)) {
            comparator = comparator.reversed();
        }
        list.sort(comparator);
        /* Ended by AICoder, pid:3639424590p1091140a50a73c0cd552bd0f1d237 */
    }

    public  List<MonitorObjectBean> getMonitorObjectList(List<String> ids) {
        List<MonitorObjectBean> monitorObjectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids))
        {
            log.warn("getMonitorObjectList-init:ids is empty or null.");
            return monitorObjectList;
        }
        deviceCacheManager.selectDeviceById(new HashSet<>(ids)).forEach(entity -> {
            MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
            monitorObjectBean.setId(entity.getId());
            monitorObjectBean.setMoc(entity.getMoc());
            monitorObjectBean.setName(entity.getName());
            monitorObjectList.add(monitorObjectBean);
        });
        return monitorObjectList;
    }

    public List<MocEntity> selectByModuleIds(List<String> moduleIds, String languageOption) throws UedmException {
        List<MocEntity> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(moduleIds)) {
            return list;
        }

        try {
            list = mocCacheManager.selectAll().stream().filter(bean -> moduleIds.contains(bean.getId())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("selectByModuleIds in configuration is error!", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError("selectByModuleIds is error");
        }
        return list;
    }


    public void synPriceTableDataToConfig(String operation, List list) throws UedmException
    {
        try
        {
            Response<ResponseBean> response = configurationRpcs.synPriceTableDataToConfig(operation, list).execute();
            if (response.errorBody() != null)
            {
                log.info("ConfigurationManagerRpcImpl synPriceTableDataToConfig is {}", response.errorBody().string());
            }
            if (response.isSuccessful())
            {
                if (null != response.body())
                {
                    if (response.body().getCode() != 0)
                    {
                        throw new UedmException(-200, "ConfigurationManagerRpcImpl synPriceTableDataToConfig failed, code is "
                                + response.body().getCode());
                    }
                    log.info("ConfigurationManagerRpcImpl synPriceTableDataToConfig is success.");
                }
            }
            else
            {
                throw new UedmException(-200, "ConfigurationManagerRpcImpl synPriceTableDataToConfig failed");
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl synPriceTableDataToConfig failed", e);
            throw new UedmException(-1, "ConfigurationManagerRpcImpl synPriceTableDataToConfig failed");
        }
    }

    public boolean selectPriceStrategyDetail(String id) throws UedmException
    {
        try
        {
            Response<ResponseBean<Boolean>> response = configurationRpcs.selectPriceStrategyDetail(id).execute();
            if (response.isSuccessful())
            {
                if (null != response.body())
                {
                    if (response.body().getCode() != 0)
                    {
                        throw new UedmException(-200, "ConfigurationManagerRpcImpl selectPriceStrategyDetail failed, code is "
                                + response.body().getCode());
                    }
                    return response.body().getData();
                }
                else
                {
                    throw new UedmException(-200, "ConfigurationManagerRpcImpl selectPriceStrategyDetail empty response");
                }
            }
            else
            {
                throw new UedmException(-200, "ConfigurationManagerRpcImpl selectPriceStrategyDetail failed");
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl selectPriceStrategyDetail failed", e);
            throw new UedmException(-1, "ConfigurationManagerRpcImpl selectPriceStrategyDetail failed");
        }
    }

    public void deletePriceStrategyDetailDefaultData(String id) throws UedmException
    {
        try
        {
            Response<ResponseBean> response = configurationRpcs.deletePriceStrategyDetailDefaultData(id).execute();
            if (response.errorBody() != null)
            {
                log.info("ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData is {}", response.errorBody().string());
            }
            if (response.isSuccessful())
            {
                if (null != response.body())
                {
                    if (response.body().getCode() != 0)
                    {
                        throw new UedmException(-200, "ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData failed, code is "
                                + response.body().getCode());
                    }
                    log.info("ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData is success.");
                }
            }
            else
            {
                throw new UedmException(-200, "ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData failed");
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData failed", e);
            throw new UedmException(-1, "ConfigurationManagerRpcImpl deletePriceStrategyDetailDefaultData failed");
        }
    }



    /**
     * 根据moc查询所有moc类resource from resource_base表
     * resource-base/get-resource-by-moc
     * @param moc
     * @return
     */
    public List<ResourceBaseBean> getResourceBeanListByMoc(String moc) throws UedmException
    {
        log.info("[getResourceBeanListByMoc] get resourceBase by moc: {}", moc);
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        if(CollectionUtils.isNotEmpty(allResourceBase)) {
            List<ResourceBaseBean> resultCollect = allResourceBase.stream()
                    .filter(entity -> entity.getMoc().equals(moc))
                    .map(entity->{
                        ResourceBaseBean bean = new ResourceBaseBean();
                        bean.setId(entity.getId());
                        bean.setMoc(entity.getMoc());
                        return bean;
                    })
                    .collect(Collectors.toList());
            log.info("[getResourceBeanListByMoc] get resourceBase by moc end, size: {}", resultCollect.size());
            return resultCollect;
        }
        return new ArrayList<>();
    }


    /* Started by AICoder, pid:xe3ddab0bf191c6141e30b5c80e30416e63558b0 */
    public List<StandardPointEntity> getStandardPointByIds(List<String> ids, String moc) throws UedmException {
        log.info("Get StandardPointEntity by ids.");

        try {
            return standardPointCacheManager.getStandardListByIdMoc(new HashSet<>(ids), moc);
        } catch (Exception e) {
            log.error("getStandardListByIdMoc error: {}", e.getMessage());
            throw new UedmException(-1, "Query standardPoint error");
        }
    }
    /* Ended by AICoder, pid:xe3ddab0bf191c6141e30b5c80e30416e63558b0 */

    public List<RecordIndexBean> getRecordIndexByIds(String id) throws UedmException
    {
        log.info("get RecordIndexBean by ids.");
        List<RecordIndexBean> beanList = new ArrayList<>();
        if (StringUtils.isBlank(id))
        {
            return beanList;
        }

        log.debug("getRecordIndexByIds id  is {}", id);
        try
        {
            Call<ResponseBean> responseList = configurationRpcs.getRecordIndextByIds(id);
            Response<ResponseBean> responseBean = responseList.execute();
            log.debug("getRecordIndexByIds getStandardPointByIds={} ", jsonService.objectToJson(responseBean));
            log.debug("getRecordIndexByIds,body:={}", jsonService.objectToJson(responseBean.body()));
            if (responseBean.isSuccessful() && responseBean.body() != null)
            {
                if (responseBean.body().getCode() != 0)
                {
                    log.error("getRecordIndexByIds rpc code is:" + responseBean.body().getCode());
                    log.error(responseBean.body().getMessage());
                    throw UedmErrorCodeRpcUtil.rpcInterfaceCallFailed("configurationRpcs.getRecordIndexByIds rpc error");
                }
                log.info("ConfigurationManagerRpcImpl getRecordIndexByIds success!");
                beanList = jsonService.jsonToObject(jsonService.objectToJson(responseBean.body().getData()),List.class,RecordIndexBean.class);

                return beanList;
            }
            else
            {
                log.error("ConfigurationManagerRpcImpl getRecordIndexByIds failed!");
                throw new UedmException(-200, "ConfigurationManagerRpcImpl getRecordIndexByIds failed");
            }
        }
        catch (Exception e)
        {
            log.error("getStandardPointByIds error!", e);
            throw UedmErrorCodeRpcUtil.rpcInterfaceCallFailed("getRecordIndexByIds rpc failed");
        }
    }

    /**
     * 根据条件获取监控对象list
     * @param monitorObjectBean
     * @param pageNo
     * @param pageSize
     * @return
     * @throws UedmException
     */
/*    public PageInfo<MonitorObjectEntity> getAllMonitorObjectByBeanMoc(MonitorObjectBean monitorObjectBean, Integer pageNo, Integer pageSize)
            throws UedmException
    {
        try
        {
            Response<ResponseBean> response = configurationRpcs.getAllMonitorObjectByBeanMoc(monitorObjectBean, pageNo, pageSize).
                    execute();
            if (response.isSuccessful() && response.body() != null)
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl getAllMonitorObjectByBeanMoc() failed, code is " + response.body()
                                    .getCode());
                }
                log.info("ConfigurationManagerRpcImpl getAllMonitorObjectByBeanMoc success!");
                List<MonitorObjectEntity> monitorObjectBeans = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), List.class, MonitorObjectBean.class);
                monitorObjectBeans.forEach(bean -> bean.setResourceType(ResourceTypeOptional.RESOURCE_TYPE_MONITOR_OBJECT));
                log.info("get monitorObjectBeans by condition, monitorObjectBeans size: {}", monitorObjectBeans.size());
                PageInfo<MonitorObjectEntity> result = new PageInfo<>(monitorObjectBeans);
                result.setTotal(response.body().getTotal());
                return result;
            }
            else
            {
                log.error("ConfigurationManagerRpcImpl getAllMonitorObjectByBeanMoc failed!");
                throw new UedmException(-200, "ConfigurationManagerRpcImpl getAllMonitorObjectByBeanMoc failed");
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl getAllMonitorObjectByBeanMoc failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }*/

    /**
     * 根据moc查询对应的所有moc类型的监控对象（分页）
     * @param moc
     * @return
     */
   /* public List<MonitorObjectEntity> getAllObjectByMoc(String moc) throws UedmException
    {
        if (StringUtils.isBlank(moc))
        {
            return new ArrayList<>();
        }

        MonitorObjectBean monitorObjectBean=new MonitorObjectBean();
        monitorObjectBean.setMoc(moc);

        List<MonitorObjectEntity> allMonitorObject = new ArrayList<>();
        PageInfo<MonitorObjectEntity> pageInfo = getAllMonitorObjectByBeanMoc(monitorObjectBean, 1, PAGE_SIZE);

        if(pageInfo.getTotal() <= PAGE_SIZE)
        {
            allMonitorObject = pageInfo.getList();
        }
        else
        {
            int total  = (int) pageInfo.getTotal();
            int times = total/PAGE_SIZE + 1;
            allMonitorObject = pageInfo.getList();
            for(int i = 2 ; i <= times; i++)
            {
                allMonitorObject.addAll(getAllMonitorObjectByBeanMoc(monitorObjectBean, i, PAGE_SIZE).getList());
            }
        }
        log.info("RealGroupCacheManager allRealGroup size is {}",  allMonitorObject.size());
        log.debug("RealGroupCacheManager allRealGroup is {}", allMonitorObject);
        return allMonitorObject;
    }*/

    /**
     * 根据条件获取监控对象list
     * @param
     * @param moc
     * @return
     * @throws UedmException
     */
   /* public List<MonitorObjectEntity> getMonitorObjectByIdsAndMoc(List<String> ids, String moc)
            throws UedmException
    {
        if (StringUtils.isBlank(moc) || CollectionUtils.isEmpty(ids))
        {
            log.warn("getMonitorObjectByIdsAndMoc, moc or ids is empty!");
            return new ArrayList<>();
        }

        try
        {
            Response<ResponseBean> response = configurationRpcs.getMonitorObjectByIds(ids).
                    execute();
            if (response.isSuccessful() && response.body() != null)
            {
                if (response.body().getCode() != 0)
                {
                    log.error(response.body().getMessage());
                    throw new UedmException(-200,
                            "ConfigurationManagerRpcImpl getMonitorObjectByIdsAndMoc() failed, code is " + response.body()
                                    .getCode());
                }
                log.info("ConfigurationManagerRpcImpl getMonitorObjectByIdsAndMoc success!");
                List<MonitorObjectEntity> monitorObjectBeans = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), List.class, MonitorObjectBean.class);
                monitorObjectBeans.stream().filter(bean -> moc.equals(bean.getMoc()))
                        .forEach(bean -> bean.setResourceType(ResourceTypeOptional.RESOURCE_TYPE_MONITOR_OBJECT));
                log.info("get monitorObjectBeans by condition, monitorObjectBeans size: {}", monitorObjectBeans.size());

                return monitorObjectBeans;
            }
            else
            {
                log.error("ConfigurationManagerRpcImpl getMonitorObjectByIdsAndMoc failed!");
                throw new UedmException(-200, "ConfigurationManagerRpcImpl getMonitorObjectByIdsAndMoc failed");
            }
        }
        catch (Exception e)
        {
            log.error("ConfigurationManagerRpcImpl getMonitorObjectByIdsAndMoc failed:", e);
            throw new UedmException(-200, e.getMessage());
        }
    }*/



    /**
     * 查询站点补采能力
     * 查询失败/出错时返回为空
     * @param queryDto
     * @return
     */
    public List<SiteRecollectionCapabilityBean> queryRecollectionCapabilityWithSite(RecollectionCapabilityQueryDto queryDto){
        log.info("ConfigurationManagerRpcImpl queryDto{}", queryDto.toString());

        List<SiteRecollectionCapabilityBean> recollectionCapabilityBeanList = new ArrayList<>();
        try {
          /*  SELECT p.id,r.id as device_id,r.parent_id as site_id,p.name,
                    p.module_id,p.protocol_type,p.parent_id,r.name as device_name,p.recollection,p.recollect_enable
            from original_point_sf p
            LEFT JOIN monitor_device_base m ON p.module_id=m.module_id
            LEFT JOIN resource_base r ON m.id=r.id
                    <where>
                    r.resource_type='MonitorDeviceSf'
                    <if test="siteIdList != null and siteIdList.size()>0">
                    AND r.parent_id in
                <foreach collection="siteIdList" item="siteId" open="(" separator="," close=")">
                    #{siteId}
                </foreach>
            </if>
            <if test="pointIdList != null and pointIdList.size()>0">
                    AND p.id in
                <foreach collection="pointIdList" item="pointId" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
            <if test="name!= null">
                    AND p.name LIKE concat('%',#{name},'%')
            </if>
        </where>*/
            List<String> pointList = queryDto.getPointList();
            List<String> siteIdList = queryDto.getSiteIdList();
            //采集器（原monitor_device_base）
            List<CollectorEntity> collectorById = collectorCacheManager.getAllCollector().stream().collect(Collectors.toList());
            //站点过滤
            if(!CollectionUtils.isEmpty(siteIdList)){
                collectorById= collectorById.stream().filter(bean->siteIdList.contains(bean.getParentId())).collect(Collectors.toList());
            }
            List<CollectorDSEntity> collectorDSEntities = realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(collectorById);
            //获取adapterId(module_id)r.resource_type='MonitorDeviceSf'
            List<String> adapterId = collectorDSEntities.stream().filter(bean-> ProtocolTypeOptional.UNI_ZTE.getId().equals(bean.getProtocolTypes())).map(CollectorEntity::getAdapterId).collect(Collectors.toList());
            List<AdapterEntity> adapterEntities = adapterCacheManager.selectByIds(adapterId);
            List<AdapterPointEntity> adapterPointEntities = adapterEntities.stream().filter(bean -> CollectionUtils.isNotEmpty(bean.getAdapterPoints())).flatMap(bean -> bean.getAdapterPoints().stream()).collect(Collectors.toList());
            //测点过滤
            if(!CollectionUtils.isEmpty(pointList)){
                adapterPointEntities= adapterPointEntities.stream().filter(bean->pointList.contains(bean.getId())).collect(Collectors.toList());
            }
            recollectionCapabilityBeanList=  adapterPointEntities.stream().map(bean->{
                SiteRecollectionCapabilityBean siteRecollectionCapabilityBean = new SiteRecollectionCapabilityBean();
                siteRecollectionCapabilityBean.setRecollectionAble(null!=bean && null != bean.getRecollection()?bean.getRecollection():false);
                return siteRecollectionCapabilityBean;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("ConfigurationManagerRpcImpl queryRecollectionCapabilityWithSite error ", e);
        }
        return recollectionCapabilityBeanList;
    }



    /**
     * 获取license信息
     *
     * @return
     */
    /* Started by AICoder, pid:jbaabf9ce7yb47514e640b1fb045431a54918e08 */
    public Boolean queryLicenseInfo(String licenseId){
        if (StringUtils.isNotBlank(licenseId)) {
            try {
                Pair<Boolean, Set<String>> flag = licenseMgr.filterEnableBy(Collections.singleton(licenseId));
                return flag.getLeft();
            } catch ( LicenseException e) { // 假设这是您要捕获的特定异常类型
                log.error("queryLicenseInfo error!", e);
            }
        }
        return false;
    }
    /* Ended by AICoder, pid:jbaabf9ce7yb47514e640b1fb045431a54918e08 */


    public List<DetailHistoryResposeBean> detailHistoryExport(String id){
        List<DetailHistoryResposeBean> list = new ArrayList<>();
        try {
            List<DetailHistoryVo> detailHistoryVos = peakShiftFileService.detailHistoryExport(id, DEFAULT_USER, "en_US");
            log.debug("detailHistoryExport responseList {}",JSON.toJSONString(detailHistoryVos));
            if (CollectionUtils.isNotEmpty(detailHistoryVos)) {
                list = jsonService.jsonToObject(jsonService.objectToJson(detailHistoryVos), List.class, DetailHistoryResposeBean.class);
            }
        } catch (Exception e) {
            log.error("detailHistoryExport error!", e);
        }
        return list;
    }


    /**
     * 根据站点查询其关联设备
     * @return
     * @throws UedmException
     */
    public List<MonitorDeviceBaseBean> queryDeviceBySiteList(String siteId, String languageOption) throws UedmException {
        List<MonitorDeviceBaseBean> list = new ArrayList<>();
        /*   SELECT d.* FROM monitor_device_base d
        LEFT JOIN monitor_device_object_relation m ON d.id=m.monitor_device_id
        LEFT JOIN resource_base r ON r.id=m.monitor_object_id
        LEFT JOIN original_point_sf p ON p.module_id=d.module_id
        WHERE r.moc=#{moc} AND #{siteId}=any(r.path_id)
        AND p.recollection=true AND p.recollect_enable=true
        <if test="protocolType != null and protocolType !=''">
            AND p.protocol_type=#{protocolType}
        </if>
        */
        //monitor_object_id===>deviceid
        List<ResourceBaseEntity> allResourceBase = Optional.ofNullable(resourceBaseCacheManager.getAllResourceBase()).orElse(new ArrayList<>());
        List<String> collect = allResourceBase.stream().filter(bean -> MocOptional.PV.getId().equals(bean.getMoc()) && bean.toStringPathId().contains(siteId)).map(ResourceBaseEntity::getId).collect(Collectors.toList());
        log.info("queryDeviceBySiteList collect.size:{}",collect.size());
        //monitor_device_id===>collectorId
        List<String> collectorIds= resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collect);
        log.info("queryDeviceBySiteList collectorIds.size:{}",collect.size());
        //monitor_device_base===>CollectorEntity
        List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(collectorIds);
        List<CollectorDSEntity> collectorDSEntities = realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(collectorById);
        if(StringUtils.isNotBlank(SUPPLY_PROTOCOL_TYPE)){
            collectorDSEntities= collectorDSEntities.stream().filter(bean->SUPPLY_PROTOCOL_TYPE.equals(bean.getProtocolTypes())).collect(Collectors.toList());
        }
        log.info("queryDeviceBySiteList collectorById.size:{}",collectorById.size());
        //sf
        List<String> adapterIdSF = new ArrayList<>();
        List<AdapterPointEntity> adapterPointEntitys = adapterCacheManager.selectAll().stream().filter(bean -> CollectionUtils.isNotEmpty(bean.getAdapterPoints())).flatMap(bean -> bean.getAdapterPoints().stream()).collect(Collectors.toList());
        log.info("queryDeviceBySiteList adapterPointEntitys.size:{}",adapterPointEntitys.size());
        for (AdapterPointEntity adapterPointEntity : adapterPointEntitys) {
            if(null!=adapterPointEntity&&Boolean.TRUE.equals(adapterPointEntity.getRecollection())&&Boolean.TRUE.equals(adapterPointEntity.getRecollectEnable())){
                adapterIdSF.add(adapterPointEntity.getAdapterId());
            }
        }
        if(CollectionUtils.isNotEmpty(adapterIdSF)){
            collectorDSEntities= collectorDSEntities.stream().filter(bean->adapterIdSF.contains(bean.getAdapterId())).collect(Collectors.toList());
        }
        list= collectorDSEntities.stream().map(bean->{
            MonitorDeviceBaseBean monitorDeviceBaseBean = new MonitorDeviceBaseBean();
            monitorDeviceBaseBean.setId(bean.getId());
            monitorDeviceBaseBean.setProtocolType(bean.getProtocolTypes());
            return  monitorDeviceBaseBean;
        }).collect(Collectors.toList());

        return list;
    }

    /**
     * 获取用户完全权限id
     * @param userName
     * @param ids
     * @return
     * @throws UedmException
     */
    public Set<String> getAuthPositionsByUser(String userName ,Set<String> ids) throws UedmException
    {
        //获取用户角色
        List<String> userDetail = configurationDataDomain.getUserDetail(userName);
        // 判断角色是否预定义角色 若是则默认有所有权限
        if (configurationDataDomain.isReservedRole(userDetail)){
            return ids;
        }
        List<ResourceBaseEntity> entityByIds = resourceBaseCacheManager.getEntityByIds(ids);
        //获取用户有权限的id
        return entityByIds.parallelStream().filter(bean->configurationDataDomain.getAuthorizationStatus(userDetail,bean)).map(ResourceBaseEntity::getId).collect(Collectors.toSet());
    }

    /* Started by AICoder, pid:e76f9n0f535989e140560ab9b0bd7f229a76c6ea */
    public void forCi(Integer num) {
        if (1 == num) {
            log.info("0");
        }
        if (2 == num) {
            log.info("1");
        }
        if (3 == num) {
            log.info("2");
        }
        if (4 == num) {
            log.info("3");
        }
        if (5 == num) {
            log.info("4");
        }
        if (6 == num) {
            log.info("5");
        }
        if (7 == num) {
            log.info("6");
        }
        if (8 == num) {
            log.info("7");
        }
    }
    public static void forCi1(int flag)
    {
        if(flag == 4)
        {
            log.debug("444");
        }
        else if(flag == 3)
        {
            log.debug("333");
        }
        else if(flag == 2)
        {
            log.debug("222");
        }
        else if(flag == 1)
        {
            log.debug("111");
        }
        else
        {
            log.debug("000");
        }
    }

    public static void forCi2(int flag)
    {
        if(flag == 4)
        {
            log.debug("444");
        }
        else if(flag == 3)
        {
            log.debug("333");
        }
        else if(flag == 2)
        {
            log.debug("222");
        }
        else if(flag == 1)
        {
            log.debug("111");
        }
        else
        {
            log.debug("000");
        }
    }

    public static void forCi3(int flag)
    {
        if(flag == 4)
        {
            log.debug("444");
        }
        else if(flag == 3)
        {
            log.debug("333");
        }
        else if(flag == 2)
        {
            log.debug("222");
        }
        else if(flag == 1)
        {
            log.debug("111");
        }
        else
        {
            log.debug("000");
        }
    }

    public static void forCi4(int flag)
    {
        if(flag == 4)
        {
            log.debug("444");
        }
        else if(flag == 3)
        {
            log.debug("333");
        }
        else if(flag == 2)
        {
            log.debug("222");
        }
        else if(flag == 1)
        {
            log.debug("111");
        }
        else
        {
            log.debug("000");
        }
    }

    public static void forCi5(int flag)
    {
        if(flag == 4)
        {
            log.debug("444");
        }
        else if(flag == 3)
        {
            log.debug("333");
        }
        else if(flag == 2)
        {
            log.debug("222");
        }
        else if(flag == 1)
        {
            log.debug("111");
        }
        else
        {
            log.debug("000");
        }
    }

    /* Ended by AICoder, pid:e76f9n0f535989e140560ab9b0bd7f229a76c6ea */

}