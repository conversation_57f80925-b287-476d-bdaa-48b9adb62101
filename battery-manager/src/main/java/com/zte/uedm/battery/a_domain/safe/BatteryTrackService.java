package com.zte.uedm.battery.a_domain.safe;


import com.zte.uedm.battery.a_infrastructure.safe.bean.*;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BatteryTrackService
{
    /**
     * 获取电池历史轨迹数据
     * @param moId
     * @return
     * @throws UedmException
     */
    List<BatteryTrackBean> getHistoryTrack(String moId, String startTime, String endTime, Integer pointNumber, Integer ranuleNumber) throws UedmException;


    /**
     * 获取电池实时轨迹数据
     * @param moId
     * @return
     * @throws UedmException
     */
    BatteryTrackBean getRealtionTrack(String moId);

    /**
     * 根据batteryTrackSearchRequestBean条件查询电池轨迹消息
     * @param batteryTrackSearchRequestBean
     * @return
     */
    BatteryTrackSearchResponseBean select(BatteryTrackSearchRequestBean batteryTrackSearchRequestBean, ServiceBaseInfoBean serviceBaseInfoBean) throws Exception;

    /**
     * 判断电池是否具有轨迹功能
     * @param moId
     * @return
     * @throws UedmException
     */
    Integer enableTrackFunction(String moId) throws UedmException;

    /**
     * 电池轨迹状态维度更新
     * @param updateBeanList
     * @param userName
     * @return
     * @throws UedmException
     */
    Integer updateStatusConfig(List<BatteryTrackOverviewUpdateBean> updateBeanList, String userName) throws UedmException;

    /**
     * 电池轨迹状态维度查询
     * @param userName
     * @param languageOption
     * @return
     * @throws UedmException
     */
    List<BatteryTrackOverviewBean> selectStatusConfig(String userName, String languageOption) throws UedmException;

    /**
     * 电池轨迹概览维度更新
     * @param updateBeanList
     * @param userName
     * @return
     * @throws UedmException
     */
    Integer updateOverviewConfig(List<BatteryTrackOverviewUpdateBean> updateBeanList, String userName) throws UedmException;

    /**
     * 电池轨迹概览维度查询
     * @param userName
     * @param languageOption
     * @return
     */
    List<BatteryTrackOverviewBean> selectOverviewConfig(String userName, String languageOption) throws UedmException;

    List<BatteryHistoryLocationBean> getHistoryTrackNew(String moId, String startTime, String endTime) throws UedmException;

    List<BatteryLocationBean> getBatteryLocationInfo(List<String> ids, String userName) throws UedmException;
}
