package com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper;

import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyRecordBeanPo;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyRecordPo;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyStdRecordPo;
import com.zte.uedm.battery.a_interfaces.vpp.web.dto.FrequencyModulationDetailDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/* Started by AICoder, pid:6880f5c8a1s1e9014346092460a62979ae809ef7 */
@Mapper
public interface FrequencyRecordMapper {
    /**
     * 记录调频开始信息
     *
     * @param frequencyRecordPo
     */
    void insertFrequencyStartRecord(@Param("bean") FrequencyRecordBeanPo frequencyRecordPo);

    /**
     * 记录调频测点信息
     *
     * @param frequencyRecordBeanPo
     */
    void insertFrequencyStdRecord(@Param("bean") FrequencyStdRecordPo frequencyRecordBeanPo);

    /**
     * 记录调频结束信息
     *
     * @param frequencyRecordPo
     */
    void updateFrequencyEndRecord(@Param("bean") FrequencyRecordBeanPo frequencyRecordPo);

    /**
     * 查询调频目标功率信息
     *
     * @param moId
     */
    String selectTargetPowerList(@Param("moId") String moId);

    /**
     * 记录调频目标功率信息
     *
     * @param frequencyRecordPo
     */
    void updateFrequencyTargetPowerRecord(@Param("bean") FrequencyRecordBeanPo frequencyRecordPo);

    /**
     * 记录监听设备调频响应信息
     *
     * @param frequencyRecordPo
     */
    void updateSouthApplyRecord(@Param("bean") FrequencyRecordBeanPo frequencyRecordPo);

    /**
     * 查询调频开始时间
     *
     * @param deviceLogId
     */
    Date selectFrequencyStartTime(@Param("deviceLogId") String deviceLogId);

    String selectFrequencyMoId(@Param("deviceLogId") String deviceLogId);

    /**
     * 查询某个监控对象是否已开始调频
     *
     * @param moId
     */
    List<FrequencyRecordPo> selectByMoId(@Param("moId") String moId);

    List<FrequencyRecordPo> getDeviceFmRecord(@Param("detailDto") FrequencyModulationDetailDto detailDto);

    FrequencyRecordPo getDeviceFmOverview(@Param("deviceId") String deviceId);

    FrequencyRecordPo getDetailDiagram(@Param("detailDto") FrequencyModulationDetailDto detailDto);

    List<FrequencyStdRecordPo> selectFrequencyStartRecord(@Param("moId") String moId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<FrequencyRecordPo> selectFrequencyModulationMonitor();
}
/* Ended by AICoder, pid:6880f5c8a1s1e9014346092460a62979ae809ef7 */
