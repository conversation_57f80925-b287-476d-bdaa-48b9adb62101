package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.BattDischargeDepthCycleTimesBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BattDischargeDepthCycleTimesDomain
{

    /**
     * 电池配置数据批量入库，增加缓存
     *
     * @param beanList
     * @throws UedmException
     */
    void insert(List<BattDischargeDepthCycleTimesBean> beanList) throws UedmException;

    /**
     * 根据电池id从数据库中删除
     *
     * @param battIdList   电池id
     * @return
     * @throws UedmException
     */
    Integer deleteBybattIdList(List<String> battIdList) throws UedmException;

    /**
     * 批量更新数据库存储
     *
     * @param bean
     * @return
     * @throws UedmException
     */
    Integer update(BattDischargeDepthCycleTimesBean bean) throws UedmException;


    /**
     * 根据电池id从数据库中获取对象list
     * @return
     * @throws UedmException
     */
    List<BattDischargeDepthCycleTimesBean> selectBybattIdList(List<String> battIdList) throws UedmException;


    /**
     * 查询时为null，根据电池类型生成具体的配置数据（电池评估专用）,入库并返回
     * battType   电池类型
     * battIdList 电池ids
     * @return
     * @throws UedmException
     */
    List<BattDischargeDepthCycleTimesBean> selectByIdsAndType(List<String> battIdList,String battType) throws UedmException;


}
