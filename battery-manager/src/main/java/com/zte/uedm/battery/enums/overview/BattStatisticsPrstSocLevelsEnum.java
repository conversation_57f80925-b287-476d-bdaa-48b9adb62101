package com.zte.uedm.battery.enums.overview;


/**
 * @FileName : BattStatisticsTypesEnum
 * @FileDesc : TODO
 * <AUTHOR> wc 10263256
 * @date Date : 2022年05月16日 20:27
 * @Version : 1.0
 */

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池的统计维度
 * 使用场景为电池总览页面
 */
public enum BattStatisticsPrstSocLevelsEnum {

    TYPE_1("1","0~30%" ),
    TYPE_2("2","30%~60%" ),
    TYPE_3("3"," 60%~90%" ),
    TYPE_4("4"," 90%~100%" );



    private String id;
    private String name;

    private String getId()
    {
        return this.id;
    }
    private String getName()
    {
        return this.name;
    }

    BattStatisticsPrstSocLevelsEnum(String id, String name)
    {
        this.id = id;
        this.name = name;
    }

    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getAllBattStatisticsPrstSocLevelsIds()
    {
        List<String> allIds = new ArrayList<>();

        BattStatisticsPrstSocLevelsEnum[] values = BattStatisticsPrstSocLevelsEnum.values();
        for(BattStatisticsPrstSocLevelsEnum value : values)
        {
            if(StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }
    /**
     * 根据id获取name
     * @return
     */
    public static String getNameById(String id)
    {
        String name = null;
        BattStatisticsPrstSocLevelsEnum[] values = BattStatisticsPrstSocLevelsEnum.values();
        for(BattStatisticsPrstSocLevelsEnum value : values)
        {
            if(StringUtils.isNotBlank(value.getName()) && id == value.getId() ){
                name = value.getName();
            }

        }
        return name;
    }
}
