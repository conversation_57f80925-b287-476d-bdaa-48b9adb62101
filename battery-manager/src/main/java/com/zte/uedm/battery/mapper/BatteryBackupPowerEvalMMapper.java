package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BatteryBackupPowerEvalPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatteryBackupPowerEvalMMapper {
    /**
     * 根据id查询
     * @param id
     * @return
     */
    List<BatteryBackupPowerEvalPojo> selectById(@Param("moId") String id);


    /**
     * 根据id和当前日期查询最近一条数据
     * @param id
     * @return
     */
    BatteryBackupPowerEvalPojo selectByIdAndData(@Param("moId") String id, @Param("date") String date);

    /**
     * 根据id查询
     * @param batteryBackupPowerEvals
     * @return
     */
    Integer insertList(@Param("list") List<BatteryBackupPowerEvalPojo> batteryBackupPowerEvals);
}
