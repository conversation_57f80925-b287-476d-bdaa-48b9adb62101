package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

public interface BattAlarmService
{

    /**
     * 查询电池告警的级别列表
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> selectLevel(ServiceBaseInfoBean serviceBean) throws UedmException;

}
