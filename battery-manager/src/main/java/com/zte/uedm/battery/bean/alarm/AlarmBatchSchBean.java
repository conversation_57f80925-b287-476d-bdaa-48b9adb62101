package com.zte.uedm.battery.bean.alarm;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AlarmBatchSchBean
{
    /**
     * 告警关联对象的id
     */
    private List<String> ids;

    /**
     * 告警关联对象的moc类型
     */
    private List<String> mocs;


    /**
     * 告警码list
     */
    private List<AlarmCode> alarmCodes;

    /**
     * 告警级别 1-严重 2-主要，3-次要，4-警告
     */
    private List<Integer> severities;

    /**
     * 告警上报开始时间
     */
    private String alarmRaisedTimeStart;

    /**
     * 告警上报结束时间
     */
    private String alarmRaisedTimeEnd;
    /**
     * 告警清除开始时间
     */
    private String alarmClearedTimeStart;
    /**
     * 告警清除结束时间
     */
    private String alarmClearedTimeEnd;

    /**
     * 确认状态 1表示确认 2表示未确认
     */
    private List<Integer> ackState;

    /**
     * 告警状态,1-未恢复，2-已恢复
     */
    private List<Integer> alarmState;

    /**
     * 搜索框数据
     */
    private String schData;

    /**
     * 排序方向，0倒叙（默认）1正序
     */
    private int sortdirection;

    /**
     * 排序字段
     * alarmraisedtime
     */
    private String sortfield;
}
