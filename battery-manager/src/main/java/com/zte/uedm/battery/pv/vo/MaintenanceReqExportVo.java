package com.zte.uedm.battery.pv.vo;

import com.zte.uedm.battery.bean.ImageBean;
import com.zte.uedm.battery.pv.dto.MaintenanceDto;
import com.zte.uedm.common.bean.log.OperlogDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@ApiModel(description = "太阳能提醒概览")
public class MaintenanceReqExportVo extends MaintenanceDto {


    /**
     *  图片信息列表
     */
    @ApiModelProperty(value = "图片信息列表")
    private List<ImageBean> images;

    /**
     * export file name;
     */
    @ApiModelProperty(value = "导出文件名称")
    private String fileName;
    /**
     *  导出位置
     */
    @OperlogDetail(zhName = "导出位置",enName = "Export position")
    @ApiModelProperty(value = "导出位置")
    private String position;

}
