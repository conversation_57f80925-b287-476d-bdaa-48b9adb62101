package com.zte.uedm.battery.opti.domain.aggregate.model.vobj;

import com.zte.uedm.battery.opti.domain.service.bean.enums.CalculationFrequencyTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Setter
@Getter
@ToString
public class CalculationTimesDetail
{
    /**
     * 频次类型
     */
    private String type;

    /**
     * 频次
     */
    private Integer value;

    /**
     * 单位
     */
    private String unit;


    public boolean checkParameter()
    {
        boolean checkResult = false;
        if(StringUtils.isBlank(this.type))
        {
            checkResult = true;
        }
        else if(CalculationFrequencyTypeEnum.checkFrequencyType(this.type))
        {
            checkResult = null == this.value;
        }
        else if(CalculationFrequencyTypeEnum.checkTimeContinuous(this.type))
        {
            checkResult = (null == this.value) || StringUtils.isBlank(unit);
        }
        return checkResult;
    }
}
