package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

/**
 * 电池供应商 - 相关业务api
 */
public interface BattSupplierService
{

    /**
     * 模糊搜索 电池供应商信息
     * @param name 模糊匹配
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> search(String name, ServiceBaseInfoBean serviceBean) throws UedmException;

}
