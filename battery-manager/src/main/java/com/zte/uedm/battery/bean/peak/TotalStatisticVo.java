/* Started by AICoder, pid:i5ea592582zb82914ad2093150a1034c45c07a1e */
package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class TotalStatisticVo {
    /**
     * 累计收益
     */
    private String electricBenefit;

    /**
     * 累计充电量
     */
    private String totalCharge;

    /**
     * 各时段充电量
     */
    private Map<String, String> chargeDetail;

    /**
     * 累计放电量
     */
    private String totalDischarge;

    /**
     * 各时段放电量
     */
    private Map<String, String> dischargeDetail;
}
/* Ended by AICoder, pid:i5ea592582zb82914ad2093150a1034c45c07a1e */