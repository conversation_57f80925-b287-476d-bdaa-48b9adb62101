package com.zte.uedm.battery.pv.service.impl;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.controller.pv.dto.SolarRevenueOverviewDimUpdateDto;
import com.zte.uedm.battery.controller.pv.vo.SolarMonitorOverviewDimVo;
import com.zte.uedm.battery.pv.bean.SolarMonitorOverviewDimensionsBean;
import com.zte.uedm.battery.pv.enums.SolarMonitorOverviewDimEnums;
import com.zte.uedm.battery.pv.enums.SolarRevenuePriceEnums;
import com.zte.uedm.battery.pv.mapper.SolarMonitorOverviewDimensionsMapper;
import com.zte.uedm.battery.pv.service.SolarMonitorOverviewDimService;
import com.zte.uedm.battery.service.GridStrategyService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SolarMonitorOverviewDimServiceImpl implements SolarMonitorOverviewDimService {
    @Resource
    private DateTimeService dateTimeService;
    @Resource
    private I18nUtils i18nUtils;

    @Autowired
    private ConfigService configService;
    @Autowired
    private GridStrategyService strategyService;
    @Autowired
    private SolarMonitorOverviewDimensionsMapper solarMonitorOverviewDimensionsMapper;

    private static final String CFG_CENTER_CURRENCY_KEY = "configuration.show.currency.unit";

    @Override
    public List<SolarMonitorOverviewDimVo> selectSolarMonitorOverviewDim(ServiceBaseInfoBean baseInfoBean) throws UedmException {
        if (StringUtils.isBlank(baseInfoBean.getUserName())) {
            log.warn("SolarMonitorOverviewDimServiceImpl selectByUserName userName is empty");
            return new ArrayList<>();
        }
       try {
           List<SolarMonitorOverviewDimensionsBean> solarMonitorOverviewDimensionsBeanList = solarMonitorOverviewDimensionsMapper.selectByUserName(baseInfoBean.getUserName());
           log.info("SolarMonitorOverviewDimServiceImpl selectByUserName selectSolarMonitorOverviewDim size is {}.", solarMonitorOverviewDimensionsBeanList.size());

           //无数据时初始化
           initSolarMonitorOverviewDims(baseInfoBean.getUserName(), solarMonitorOverviewDimensionsBeanList);
           //获取单位
           String finalCurrency = configService.getGlobalProperty(CFG_CENTER_CURRENCY_KEY);

           List<SolarMonitorOverviewDimVo> collect = solarMonitorOverviewDimensionsBeanList.stream().map(item -> {
               SolarMonitorOverviewDimVo solarMonitorOverviewDimVo = new SolarMonitorOverviewDimVo();
               String name = i18nUtils.getMapFieldByLanguageOption(SolarMonitorOverviewDimEnums.getNameById(item.getId()), baseInfoBean.getLanguageOption());
               solarMonitorOverviewDimVo.setId(item.getId());
               solarMonitorOverviewDimVo.setName(name);
               solarMonitorOverviewDimVo.setUserName(item.getUserName());
               solarMonitorOverviewDimVo.setSequence(item.getSequence());
               solarMonitorOverviewDimVo.setEnable(item.isEnable());
               solarMonitorOverviewDimVo.setDefaultIndex(SolarMonitorOverviewDimEnums.getDefaultIndexById(item.getId()));
               solarMonitorOverviewDimVo.setDefaultEnable(SolarMonitorOverviewDimEnums.getDefaultEnableById(item.getId()));
               solarMonitorOverviewDimVo.setDefaultFixed(SolarMonitorOverviewDimEnums.getDefaultFixedById(item.getId()));
               if (SolarRevenuePriceEnums.getAllIds().contains(item.getId())) {
                   solarMonitorOverviewDimVo.setUnit(finalCurrency);
               } else {
                   solarMonitorOverviewDimVo.setUnit(SolarMonitorOverviewDimEnums.getUnitById(item.getId()));
               }
               solarMonitorOverviewDimVo.setSortable(SolarMonitorOverviewDimEnums.getSortableById(item.getId()));
               return solarMonitorOverviewDimVo;
           }).collect(Collectors.toList());

           //排序并返回
           return collect.stream()
                   .sorted(Comparator.comparing(SolarMonitorOverviewDimVo::getSequence))
                   .collect(Collectors.toList());
       }catch (Exception e){
           log.error("SolarMonitorOverviewDimDomainImpl selectByUserName error!", e);
           throw UedmErrorCodeDatabaseUtil.databaseSelectFailed("Data query error!");
       }
    }

    public void initSolarMonitorOverviewDims(String userName, List<SolarMonitorOverviewDimensionsBean> list) throws UedmException {
        if (CollectionUtils.isEmpty(list)) {
            List<String> allIds = SolarMonitorOverviewDimEnums.getAllIds();
            Date currentDateTime = dateTimeService.getCurrentDateTime();
            allIds.forEach(id -> {
                SolarMonitorOverviewDimensionsBean solarMonitorOverviewDimensionsBean  = new SolarMonitorOverviewDimensionsBean();
                solarMonitorOverviewDimensionsBean.setId(id);
                solarMonitorOverviewDimensionsBean.setEnable(SolarMonitorOverviewDimEnums.getDefaultEnableById(id));
                solarMonitorOverviewDimensionsBean.setSequence(SolarMonitorOverviewDimEnums.getDefaultIndexById(id));
                solarMonitorOverviewDimensionsBean.setCreator(userName);
                solarMonitorOverviewDimensionsBean.setName(SolarMonitorOverviewDimEnums.getNameById(id));
                solarMonitorOverviewDimensionsBean.setUserName(userName);
                solarMonitorOverviewDimensionsBean.setGmtCreate(currentDateTime);
                list.add(solarMonitorOverviewDimensionsBean);
            });
            try {
                solarMonitorOverviewDimensionsMapper.insertSolarMonitorOverviewDims(list);
            } catch (Exception e){
                log.error("SolarRevenueOverviewDimDomainImpl insertSolarRevenueOverviewDims error", e);
                throw UedmErrorCodeDatabaseUtil.databaseAddFailed("Data insert error!");
            }
        }
    }
    @Override
    public void updateUserSolarMonitorOverviewDim(List<SolarRevenueOverviewDimUpdateDto> dimUpdateDtoList, ServiceBaseInfoBean serviceBean) throws UedmException {
        if (CollectionUtils.isEmpty(dimUpdateDtoList)) {
            log.warn("SolarMonitorOverviewDimServiceImpl updateUserRecordIndexDim dimUpdateDtoList is empty!");
            return;
        }
        try {
            //是否满足要求
            checkUserSolarMonitorOverviewIndexDims(dimUpdateDtoList);
            log.info("SolarMonitorOverviewDimServiceImpl updateUserRecordIndexDim params is ok");
            List<SolarMonitorOverviewDimensionsBean> solarMonitorOverviewDimensionsBeanList = new ArrayList<>();
            for (SolarRevenueOverviewDimUpdateDto dto : dimUpdateDtoList) {
                SolarMonitorOverviewDimensionsBean solarMonitorOverviewDimensionsBean = new SolarMonitorOverviewDimensionsBean();
                solarMonitorOverviewDimensionsBean.setId(dto.getId());
                solarMonitorOverviewDimensionsBean.setUserName(serviceBean.getUserName());
                solarMonitorOverviewDimensionsBean.setEnable(dto.isEnable());
                solarMonitorOverviewDimensionsBean.setSequence(dto.getSequence());
                solarMonitorOverviewDimensionsBean.setGmtModified(dateTimeService.getCurrentDateTime());
                solarMonitorOverviewDimensionsBean.setUpdater(serviceBean.getUserName());
                solarMonitorOverviewDimensionsBeanList.add(solarMonitorOverviewDimensionsBean);
            }
            //查询旧数据
            List<SolarMonitorOverviewDimensionsBean> list = new ArrayList<>();
            try {
                list = solarMonitorOverviewDimensionsMapper.selectByUserName(serviceBean.getUserName());
            } catch (Exception e) {
                log.error("SolarMonitorOverviewDimServiceImpl [updateUserSolarMonitorOverviewDim] occur error message = {}", e.getMessage(), e);
                DatabaseExceptionEnum operatedb = DatabaseExceptionEnum.OPERATEDB;
                throw new UedmException(operatedb.getCode(), operatedb.getDesc());
            }
            Map<String, SolarMonitorOverviewDimensionsBean> map = list.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).collect(Collectors.toMap(SolarMonitorOverviewDimensionsBean::getId, a -> a));
            //固定元素是否变动
            for (SolarMonitorOverviewDimensionsBean dimensionsBean: list){
                String id = dimensionsBean.getId();
                boolean enable = dimensionsBean.isEnable();
                if (dimensionsBean.isDefaultFixed()){
                    List<SolarRevenueOverviewDimUpdateDto> filterList = dimUpdateDtoList.stream().filter(item -> item.getId().equals(id)).collect(Collectors.toList());
                    SolarRevenueOverviewDimUpdateDto updateDto = filterList.get(0);
                    if (!updateDto.isEnable() == enable){
                        log.error("SolarMonitorOverviewDimServiceImpl updateUserRecordIndexDim error: DefaultFixed is true!");
                        throw new UedmException(-208, "DefaultFixed is true");
                    }
                }
            }
            // 更新
            try {
                solarMonitorOverviewDimensionsMapper.updateSolarMonitorOverviewDims(solarMonitorOverviewDimensionsBeanList);
            } catch (Exception e) {
                log.error("SolarMonitorOverviewDimServiceImpl [updateUserSolarMonitorOverviewDim] occur error message = {}", e.getMessage(), e);
                DatabaseExceptionEnum operatedb = DatabaseExceptionEnum.OPERATEDB;
                throw new UedmException(operatedb.getCode(), operatedb.getDesc());
            }
        } catch (UedmException e) {
            log.error("SolarMonitorOverviewDimServiceImpl updateUserRecordIndexDim error: ", e);
            throw new UedmException(e.getErrorId(), e.getMessage());
        }
    }

    private void checkUserSolarMonitorOverviewIndexDims(List<SolarRevenueOverviewDimUpdateDto> list) throws UedmException {
        //id不存在
        List<String> ids = list.stream().map(SolarRevenueOverviewDimUpdateDto::getId).distinct().collect(Collectors.toList());
        SolarMonitorOverviewDimEnums.checkIdIsNeeded(ids);
        //顺序重复
        List<Integer> sequences = list.stream().map(SolarRevenueOverviewDimUpdateDto::getSequence).distinct().collect(Collectors.toList());
        if (sequences.size() < SolarMonitorOverviewDimEnums.getAllIds().size()) {
            log.error("SolarMonitorOverviewDimServiceImpl updateUserRecordIndexDim show sequence repeat!");
            throw new UedmException(-302, "Sequence repeat!");
        }
    }
}
