package com.zte.uedm.battery.enums.overview;

import com.zte.uedm.battery.service.impl.BatteryOverviewServiceImpl;
import com.zte.uedm.battery.util.DoubleUtils;
import org.apache.commons.lang3.StringUtils;

public enum RemainChargeColorEnum {

    DEEP_RED("#C45656", 0.0, 0.5),
    RED("#F56C6C", 0.5, 1.0),
    PEEK("#F89898", 1.0, 2.0),
    ORANGE("#E6A23C", 2.0, 4.0),
    GREEN("#67C23A", 4.0, 999999999.0);


    /**
     * 区间内显示的颜色[start,end)
     */
    private final String color;

    /**
     * 左区间
     */
    private final Double start;

    /**
     * 右区间
     */
    private final Double end;

     RemainChargeColorEnum(String color, Double start, Double end) {
        this.color = color;
        this.start = start;
        this.end = end;
    }

    public static String getColor(String remainChargeTimes) {
         // 保留一位小数
        Double value = DoubleUtils.transStringToDouble(remainChargeTimes, 1);
        if (value==null) {
            return "--";
        }
        for (RemainChargeColorEnum colorEnum : RemainChargeColorEnum.values()) {
            if (value >= colorEnum.start && value < colorEnum.end) {
                return colorEnum.color;
            }
        }
        return "--";
    }
}
