package com.zte.uedm.battery.a_application.peakshift.executor;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_interfaces.peakshift.inner.dto.QueryFileIdRpcDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.ScopeStrategyQueryDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateQueryDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.*;
import com.zte.uedm.battery.bean.ScopeStrategyQueryBean;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface StrategyTemplateService {

    /**
     * 新增设备模板策略
     *
     * @param strategyBaseDto
     * @param userCreate
     * @return
     * @throws UedmException
     */
    void addTemplateStrategy(TemplateStrategyAddDto strategyBaseDto, String userCreate, HttpServletRequest request) throws UedmException;

    /* Started by AICoder, pid:d371e96f4dfd482cb341d7fbc6489518 */
    void editTemplateStrategy(TemplateStrategyEditDto templateStrategyEditDto, String userName) throws UedmException;
    /* Ended by AICoder, pid:d371e96f4dfd482cb341d7fbc6489518 */

    /* Started by AICoder, pid:4ff9caf4357f45ceb3793ec260f8af40 */
    PageInfo<TemplateStrategyVo> selectStrategyByCondition(TemplateQueryDto queryBean, String languageOption) throws UedmException;
    /* Ended by AICoder, pid:4ff9caf4357f45ceb3793ec260f8af40 */

    /* Started by AICoder, pid:efd321640e7848318f61930892f3fbf2 */
    SeasonStrategyForTemplateVo queryOneSeasonStrategyFotTemplate(String seasonStrategyId, String languageOption) throws UedmException;
    /* Ended by AICoder, pid:efd321640e7848318f61930892f3fbf2 */

    /* Started by AICoder, pid:deb276f52cc94a68babb06b0f58836a4 */
    PageInfo<StrategyCombinationVo> queryAllSeasonStrategy(String sortBy, String order, Integer pageSize, Integer pageNo, String languageOption) throws UedmException;

    /* Ended by AICoder, pid:deb276f52cc94a68babb06b0f58836a4 */
    /* Started by AICoder, pid:ab9a6de9b6d7425b8240ab5b262bdfca */
    TemplateStrategyDetailVo detail(String id, String username, String languageOption) throws UedmException;

    /* Ended by AICoder, pid:ab9a6de9b6d7425b8240ab5b262bdfca */
    /* Started by AICoder, pid:389ea2854ef341e195c63bdae8137688 */
    Boolean checkName(ScopeStrategyQueryDto queryBean) throws UedmException;
    /* Ended by AICoder, pid:389ea2854ef341e195c63bdae8137688 */

    /* Started by AICoder, pid:f204e8908faf47e3b820996c8c823004 */
    int delete(List<String> ids, HttpServletRequest request, String languageOption) throws UedmException;
    /* Ended by AICoder, pid:f204e8908faf47e3b820996c8c823004 */

    /* Started by AICoder, pid:4f1bf461264d4272a69a5ce391dad6af */
    IssHistoryVo historyDetail(String taskId) throws UedmException;
    /* Ended by AICoder, pid:4f1bf461264d4272a69a5ce391dad6af */

    /* Started by AICoder, pid:791c0116433a42ef937460c1a61afb59 */
    List<String> getFileId(QueryFileIdRpcDto dto) throws UedmException;
    /* Ended by AICoder, pid:791c0116433a42ef937460c1a61afb59 */

}
