package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 电价政策新增、编辑参数Bean
 */
@Setter
@Getter
@ToString
@ApiModel(description = "范围策略")
public class ScopeStrategyRequestBean
{
    /**
     * 主键id
     */
    @ApiModelProperty(value = "任务标识")
    private String id;

    /**
     * 电网名称
     */
    @NotBlank(message = "parameter name is blank")
    @Size(max = 100,message = "name length cannot exceed 100")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 逻辑分组id
     */
    @NotEmpty(message = "parameter scope is blank")
    @ApiModelProperty(value = "范围")
    private List<String> scope;
    @ApiModelProperty(value = "逻辑分组")
    private String logicGroup;

    /**
     * 能源类型
     */
    @ApiModelProperty(value = "能源类型")
    private String energyType;

    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间")
    private String gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private String gmtModified;
}
