package com.zte.uedm.battery.bean.pojo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class AddPricePolicyPO {
    /**
     * 主键
     */
    private String id;

    /**
     * 范围策略ID
     */
    private String scopeStrategyId;

    /**
     * 生效时间
     */
    private String effectiveTime;

    /**
     * 失效时间
     */
    private String expirationTime;

    /**
     * 计费方式
     */
    private Integer rateType;

    /**
     * 状态
     */
    private String status;

    /**
     * 生成时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
