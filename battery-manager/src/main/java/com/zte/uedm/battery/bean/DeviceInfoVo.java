package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 错峰用电策略电池历史数据Bean
 */
@Setter
@Getter
@ToString
public class DeviceInfoVo
{
	/**
	 * 设备id
	 */
	private String deviceId;
    /**
     * 设备名称
	 */
	private String deviceName;
	/**
	 * 设备在线状态
	 */
	private Integer isOnline;
	/**
	 * 设备策略属性
	 */
	private String strategyType;

	/**
	 * 站点id
	 */
	private String siteId;

	/**
	 * 站点名称
	 */
	private String siteName;
	/**
	 * 路径串
	 */
	private String position;

    /**
	 * 下发状态
	 */
	private String status;

	/**
	 * 实际下发时间
	 */
	private String execTime;

	/**
	 * 设备是否还存在，true-存在，false-不存在
	 */
	private Boolean existsFlag;

	/**
	 * 失败原因
	 */
	private String failReason;

	/**
	 * 电价时段
	 */
	private String priceIntervalName;

	/**
	 * 生效状态  expired，nearToExpired ，normal
	 * 当前时间>失效时间？失效; 当前时间+7d>失效时间？即将失效; 其余：正常
	 */
	private String effectiveStatus;

	/**
	 * 生效时间
	 */
	private String effectiveDate;
	/**
	 * 失效时间
	 */
	private String expirationDate;
}
