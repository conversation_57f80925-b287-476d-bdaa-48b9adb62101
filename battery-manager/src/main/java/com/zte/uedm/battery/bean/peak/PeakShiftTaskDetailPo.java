package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 错峰任务详情表
 */
@Getter
@Setter
@ToString
public class PeakShiftTaskDetailPo
{
    /**
     * 唯一键
     */
    private String id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 站点id
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private String gmtModified;

    /**
     * 实际执行时间
     */
    private String execTime;

    /**
     * 是否超时
     */
    private Boolean failTimeOut;

    private String fileId;

    private String ftpPath;

    private String logId;
}
