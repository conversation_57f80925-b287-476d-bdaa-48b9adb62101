package com.zte.uedm.battery.a_domain.cache;


import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface StandardPointCacheMgr<K, V> {

    StandardPointEntity getStandardByIdMoc(String id, String moc) throws UedmException;

    List<StandardPointEntity> getStandardListByIdMoc(Set<String> ids, String moc) throws UedmException;

    List<StandardPointEntity> getStandardByMocs(Set<String> mocSet) throws UedmException;

   Map<String, List<StandardPointEntity>> getStandardByPointType(Set<String> pointTypeSet) throws UedmException;

    /**
     * 获取扩展测点或非扩展测点
     * @param idSet 测点id
     * @param isExpand true:扩展测点，false：非扩展测点
     * @return
     * @throws UedmException
     */
    List<StandardPointEntity> getStandardByIsExpand(Set<String> idSet, Boolean isExpand) throws UedmException;

    /**
     * 获取标准测点对应的扩展测点id
     * @param standardPointIdSet 标准测点id
     * @return key：标准测点id，value:扩展测点id
     */
    Map<String, Set<String>> getStandardPointExpandMap(Set<String> standardPointIdSet) throws UedmException;

    /**
     * 根据id获取 测点
     * @param ids
     * @return
     * @throws UedmException
     */
     List<StandardPointEntity> getStandardById(Set<String> ids )throws UedmException;

}
