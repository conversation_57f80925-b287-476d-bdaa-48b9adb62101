package com.zte.uedm.battery.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.log.filter.UserThreadLocal;
import com.zte.oes.dexcloud.ftpclient.service.FtpClientService;
import com.zte.uedm.battery.a_application.peakshift.executor.impl.PeakShiftMonitorServiceImpl;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.enums.DeviceStrategyPullStatusEnum;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.PeakShiftDeviceFileRepository;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.factory.PeakShiftFactory;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftSNMPServiceImpl;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftTemplateFileServiceImpl;
import com.zte.uedm.battery.a_domain.utils.CollectorUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.common.KafkaConst;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.PeakShiftDeviceIndexStatusBean;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.UpDownloadFileMapper;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.UpDownloadFileDataBasePO;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.UpDownloadFilePO;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.enums.peak.PeakShiftingStrategyEnum;
import com.zte.uedm.battery.mapper.PeakShiftDeviceFileMapper;
import com.zte.uedm.battery.mapper.PeakShiftDeviceIndexStatusMapper;
import com.zte.uedm.battery.mapper.PeakShiftDeviceStatusMapper;
import com.zte.uedm.battery.rpc.SouthFrameworkRpc;
import com.zte.uedm.battery.service.DevicePeakCacheInfoService;
import com.zte.uedm.battery.service.PeakShiftDeviceService;
import com.zte.uedm.battery.service.PeakShiftTaskService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.DeviceUtils;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperLogContantsBCUA;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceExecStatusEnum;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceIndexStatusEnum;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceStatusEnum;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.component.kafka.producer.bean.KafkaMessageBean;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import com.zte.ums.zenap.protocol.ftpclient.api.FtpClient;
import com.zte.ums.zenap.protocol.ftpclient.api.FtpClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.BufferedOutputStream;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftTemplateFileServiceImpl.*;

@Service
@Slf4j
public class PeakShiftDeviceServiceImpl implements PeakShiftDeviceService {
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private SouthFrameworkRpc southFrameworkRpc;
    @Autowired
    private CollectorCacheManager collectorCacheManager;
    @Autowired
    private PeakShiftFactory peakShiftFactory;
    @Autowired
    private MsgSenderService msgSenderService;
    @Autowired
    private PeakShiftMonitorServiceImpl peakShiftMonitorService;
    @Autowired
    private DevicePeakCacheInfoService devicePeakCacheInfoService;
    @Autowired
    private UpDownloadFileMapper upDownloadFileMapper;
    @Autowired
    private PeakShiftDeviceFileMapper peakShiftDeviceFileMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Autowired
    private StandardDataService standardDataService;
    @Autowired
    private PeakShiftTaskService peakShiftTaskService;

    @Autowired
    private PeakShiftSNMPServiceImpl peakShiftSNMPService;

    @Resource
    private AdapterPointDataService adapterPointDataService;

    @Autowired
    private FtpClientService ftpClientService;

    @Autowired
    private PeakShiftTemplateFileServiceImpl peakShiftTemplateFileService;

    @Autowired
    private PeakShiftDeviceFileRepository peakShiftDeviceFileRepository;
    private static final String BCUA_REDIS_NAME = "BCUAAdapterTimeOutRedisName";
    private static final String BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_ZH = "策略启用/停用命令下发"; //BCUA策略启停操作记录日志描述
    private static final String BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_EN = "Delivering a Policy Enabling or Disabling Command";
    private static final String ENABLE = "enable";
    private static final String DISABLE = "disable";

    //链路状态为0表示链路已连接
    private static String LINK_CONNECT_STATUS = "0";

    public static final Integer FOR_60=60;
    public static final Integer FOR_10=10;
    public static final Integer SLEEP_100=100;
    public static final Integer SLEEP_1000=1000;
    @Autowired
    private PeakShiftOperationRecordServiceImpl peakShiftOperationRecordService;
    @Autowired
    private PeakShiftDeviceIndexStatusMapper peakShiftDeviceIndexStatusMapper;
    @Autowired
    private PeakShiftDeviceStatusMapper peakShiftDeviceStatusMapper;


    public List<IdNameBean> getPeakShiftDeviceType(String language) throws UedmException
    {
        try
        {
            List<IdNameBean> result = new ArrayList<>();
            for (PeakDeviceTypeEnum value : PeakDeviceTypeEnum.values()) {
                IdNameBean nameBean = new IdNameBean();
                nameBean.setId(value.id);
                nameBean.setName(i18nUtils.getMapFieldByLanguageOption(value.showName, language));
                result.add(nameBean);
            }
            log.debug("all device type:{}",result);
            return result;
        }
        catch (Exception e)
        {
            log.error("query device type error!",e);
            throw new UedmException(-1,e.getMessage());
        }
    }

    /* Started by AICoder, pid:m5b22845bbl943f1421b0af0a0ab231b2279a88f */
    public Map<String, String> queryAllLinkMonitor() {
        Map<String, String> result = new HashMap<>();
        try {
            Response<ResponseBean> response = southFrameworkRpc.queryAllLinkMonitor().execute();
            if (response.isSuccessful()) {
                Map<String, Object> southFrameworkLinkBeanOriginMap = (Map<String, Object>) response.body().getData();
                log.debug("southFrameworkLinkBeanOriginMap info {}", jsonService.objectToJson(response.body()));
                for (Map.Entry<String, Object> entry : southFrameworkLinkBeanOriginMap.entrySet()) {
                    String linkId = entry.getKey();
                    Map<String, Object> linkStatusMap = (Map<String, Object>) entry.getValue();
                    String linkStatus = String.valueOf(linkStatusMap.get("connectStatus"));
                    result.put(linkId, linkStatus);
                }
            }
        } catch (Exception e) {
            log.error("southFrameworkRpc get link status is error", e);
        }
        return result;
    }
    /* Ended by AICoder, pid:m5b22845bbl943f1421b0af0a0ab231b2279a88f */

    /* Started by AICoder, pid:606d1a865ec56b8144fc0b5c40f90d712320a038 */
    @Override
    public List<String> setPeakShiftDeviceStrategies(List<PeakShiftDeviceEnableDto> deviceEnableDtoList, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        String userName = serviceBaseInfoBean.getUserName();
        OperlogBean oper = new OperlogBean(
                serviceBaseInfoBean.getUserName(),
                serviceBaseInfoBean.getRemoteHost(),
                connectMode,
                OperLogContantsBCUA.Staggered_Peak_Settings_zh,
                OperLogContantsBCUA.Staggered_Peak_Settings_en,
                OperlogBean.LogRank.operlog_rank_important,
                BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_ZH,
                BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_EN
        );
        StringBuilder detailZh = new StringBuilder();
        StringBuilder detailEn = new StringBuilder();
        List<String> failedList = new ArrayList<>();

        Set<String> collectIds = deviceEnableDtoList.stream()
                .map(PeakShiftDeviceEnableDto::getId)
                .collect(Collectors.toSet());
        Map<String, CollectorEntity> collectMap = new HashMap<>();
        try {
            collectMap = collectorCacheManager.selectCollectorByIds(collectIds).stream()
                    .collect(Collectors.toMap(CollectorEntity::getId, bean -> bean, (k1, k2) -> k2));
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("setPeakShiftDeviceStrategies get collect cache error", e);
            throw new UedmException(-1, "get collect cache error!");
        }

        // 链路状态
        Map<String, String> linkStatusMap = queryAllLinkMonitor();

        for (PeakShiftDeviceEnableDto peakShiftDeviceEnableDto : deviceEnableDtoList) {
            String deviceType = peakShiftDeviceEnableDto.getDeviceType();
            String collectorId = peakShiftDeviceEnableDto.getId();
            CollectorEntity collectorCacheBean = collectMap.get(collectorId);
            if (collectorCacheBean == null) {
                log.warn("setPeakShiftDeviceStrategies not found collector {}", collectorId);
                continue;
            }

            PeakShiftCommonService peakShiftService = peakShiftFactory.generateByDeviceType(deviceType);
            if (peakShiftService == null) {
                log.error("setPeakShiftDeviceStrategies device type not found {}", peakShiftDeviceEnableDto);
                continue;
            }

            String mainLink = DeviceUtils.parseMasterLink(collectorCacheBean.getLinkInfo());
            if (StringUtils.isBlank(mainLink)) {
                log.error("setPeakShiftDeviceStrategies main link is null");
                continue;
            }
            // 判断链路是否已连接
            String linkStatus = linkStatusMap.get(mainLink);
            if (!LINK_CONNECT_STATUS.equals(linkStatus)) {
                log.error("setPeakShiftDeviceStrategies link not connected");
                continue;
            }

            // 构造原始测点控制bean
            List<RemoteControlBean> remoteControlBeans = peakShiftService.buildDeviceEnablePeakBean(userName, peakShiftDeviceEnableDto, collectorCacheBean);
            // 发送修改原始测点值的kafka消息
            sendRemoteControlBean(remoteControlBeans, collectorCacheBean, peakShiftDeviceEnableDto.getEnable(), detailZh, detailEn, failedList);
        }

        setBUGDeviceStrategiesLog(oper, failedList.isEmpty(), detailZh.toString(), detailEn.toString());
        return failedList;
    }
    /* Ended by AICoder, pid:606d1a865ec56b8144fc0b5c40f90d712320a038 */

    /* Started by AICoder, pid:wea0cpc67c0d937146d00a1a40df703a0ce8a7bb */
    /**
     * 错峰策略启停操作记录日志发送
     *
     * @param oper 操作日志对象
     * @param operStatus 操作状态
     * @param detailZh 中文详细信息
     * @param detailEn 英文详细信息
     * @throws UedmException 异常信息
     */
    public void setBUGDeviceStrategiesLog(OperlogBean oper, boolean operStatus, String detailZh, String detailEn) throws UedmException {
        // 操作记录日志
        if (operStatus) {
            oper.refreshOperSuccess(detailZh, detailEn, new ArrayList<>());
        } else {
            oper.refreshOperFail(detailZh, detailEn, new ArrayList<>());
        }
        String operMsg = jsonService.objectToJson(oper);
        log.info("message {}", operMsg);
        msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
    }

    public void sendRemoteControlBean(List<RemoteControlBean> controlBeanList, CollectorEntity bean,
                                      boolean setEnable, StringBuilder detailZh, StringBuilder detailEn, List<String> failedList) {
        // 一次设置多个测点值总是失败，改为每次仅设置一个测点值
        for (RemoteControlBean remoteControlBean : controlBeanList) {
            try {
                msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                        jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
                detailZh.append("设备名称: ").append(bean.getName()).append(", ").append(setEnable ? "启用" : "停用").append(", 下发成功; ");
                detailEn.append("device name: ").append(bean.getName()).append(", ").append(setEnable ? ENABLE : DISABLE).append(", Delivered successfully; ");
            } catch (Exception e) {
                log.error("send BCUA device strategy failed: {}", bean, e);
                failedList.add(bean.getName());
                detailZh.append("设备名称: ").append(bean.getName()).append(", ").append(setEnable ? "启用" : "停用").append(", 下发失败; ");
                detailEn.append("device name: ").append(bean.getName()).append(", ").append(setEnable ? ENABLE : DISABLE).append(", Delivery failed; ");
            }
        }
    }
    /* Ended by AICoder, pid:wea0cpc67c0d937146d00a1a40df703a0ce8a7bb */

    /* Started by AICoder, pid:sf63ft6e842df36140db09dcb1cab21e7b449263 */
    /**
     * 批量启停BCUA错峰策略
     * @param allConfigDto 配置DTO
     * @param serviceBaseInfoBean 服务基本信息Bean
     * @return 失败的设备ID列表
     * @throws UedmException 异常信息
     */
    @Override
    public List<String> setPeakShiftDeviceStrategies(PeakShiftDeviceAllConfigDto allConfigDto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        // 操作记录日志信息
        String userName = serviceBaseInfoBean.getUserName();
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean oper = new OperlogBean(
                userName,
                serviceBaseInfoBean.getRemoteHost(),
                connectMode,
                OperLogContantsBCUA.Staggered_Peak_Settings_zh,
                OperLogContantsBCUA.Staggered_Peak_Settings_en,
                OperlogBean.LogRank.operlog_rank_important,
                BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_ZH,
                BCUA_PEAK_SHIFT_STRATEGY_COMMAND_SET_OPERATION_EN
        );
        StringBuilder detailZh = new StringBuilder();
        StringBuilder detailEn = new StringBuilder();
        List<String> failedList = new ArrayList<>();
        List<DevicePeakCacheInfoBean> resultSet = peakShiftMonitorService.getResultSet(allConfigDto, serviceBaseInfoBean);
        List<String> deviceIdList = resultSet.stream().map(DevicePeakCacheInfoBean::getDeviceId).collect(Collectors.toList());

        Map<String, CollectorEntity> collectMap = new HashMap<>();
        try {
            collectMap = collectorCacheManager.selectCollectorByIds(new HashSet<>(deviceIdList)).stream()
                    .collect(Collectors.toMap(CollectorEntity::getId, bean -> bean, (k1, k2) -> k2));
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("setPeakShiftDeviceStrategies get collect cache error", e);
            throw new UedmException(-1, "get collect cache error!");
        }
        // 链路状态
        Map<String, String> linkStatusMap = queryAllLinkMonitor();

        detailZh.append("逻辑组id: ").append(allConfigDto.getLogicGroupId()).append(";");
        detailEn.append("logic group id: ").append(allConfigDto.getLogicGroupId()).append(";");

        for (String deviceId : deviceIdList) {
            // 从缓存获取设备类型
            DevicePeakCacheInfoBean infoByDeviceId = devicePeakCacheInfoService.getInfoByDeviceId(deviceId);
            CollectorEntity collectorCacheBean = collectMap.get(deviceId);

            if (infoByDeviceId == null || collectorCacheBean == null) {
                log.error("setPeakShiftDeviceStrategies bean is null {} {} {}", deviceId, JSON.toJSONString(infoByDeviceId), JSON.toJSONString(collectorCacheBean));
                failedList.add(deviceId);
                continue;
            }

            String deviceType = infoByDeviceId.getDeviceType();
            PeakShiftCommonService peakShiftService = peakShiftFactory.generateByDeviceType(deviceType);
            if (peakShiftService == null) {
                log.error("setPeakShiftDeviceStrategies deviceType not support {} {}", deviceId, deviceType);
                failedList.add(deviceId);
                continue;
            }

            String mainLink = DeviceUtils.parseMasterLink(collectorCacheBean.getLinkInfo());
            if (StringUtils.isBlank(mainLink)) {
                log.error("setPeakShiftDeviceStrategies main link is null");
                continue;
            }
            // 判断链路是否已连接
            String linkStatus = linkStatusMap.get(mainLink);
            if (!LINK_CONNECT_STATUS.equals(linkStatus)) {
                log.error("setPeakShiftDeviceStrategies link not connected");
                continue;
            }

            PeakShiftDeviceEnableDto peakShiftDeviceEnableDto = new PeakShiftDeviceEnableDto();
            peakShiftDeviceEnableDto.setEnable(allConfigDto.isEnable());
            peakShiftDeviceEnableDto.setId(deviceId);
            peakShiftDeviceEnableDto.setDeviceType(deviceType);
            List<RemoteControlBean> remoteControlBeans = peakShiftService.buildDeviceEnablePeakBean(userName, peakShiftDeviceEnableDto, collectorCacheBean);
            // 发送修改原始测点值的kafka消息
            sendRemoteControlBean(remoteControlBeans, collectorCacheBean, peakShiftDeviceEnableDto.getEnable(), detailZh, detailEn, failedList);
        }
        setBUGDeviceStrategiesLog(oper, failedList.isEmpty(), detailZh.toString(), detailEn.toString());
        return failedList;
    }

    @Override
    public Pair<String,String> getCurrentStrategyAndExecStatus(String deviceId,
                                                               PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws UedmException {
        try {
            String execStatus = PeakShiftDeviceExecStatusEnum.getAbnormalId();
            String currPeakStrategy = PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId();

            DevicePeakCacheInfoBean devicePeakCacheInfoBean = devicePeakCacheInfoService.getInfoByDeviceId(deviceId);
            if (devicePeakCacheInfoBean == null) {
                // 缓存里面没有这个设备信息
                log.info("no this device in cache: {}", deviceId);
                return Pair.of(currPeakStrategy, execStatus);
            }

            String deviceType = devicePeakCacheInfoBean.getDeviceType();
            PeakShiftCommonService peakShiftService = peakShiftFactory.generateByDeviceType(deviceType);
            if (peakShiftService == null) {
                log.warn("getCurrentStrategyAndExecStatus not get peakShiftService");
                return Pair.of(currPeakStrategy, execStatus);
            }

            // 获取电池充放电状态
            String batteryStatus = peakShiftService.getBatteryStatus(deviceId, peakShiftMonitorBaseDataBean);

            return peakShiftService.findExecStatusAndCurrPeakStrategy(deviceId, batteryStatus, peakShiftMonitorBaseDataBean);
        } catch (Exception e) {
            log.error("getCurrentStrategyAndExecStatus", e);
            throw new UedmException(-1, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:sf63ft6e842df36140db09dcb1cab21e7b449263 */

    /* Started by AICoder, pid:d8512pf09b819ba1457c0b9b52d1de9fa9b0df3a */
    /**
     * 错峰设置的设备信息刷新
     * @param deviceId 设备ID
     * @param serviceBaseInfoBean 服务基本信息
     * @return PeakShiftDeviceChildBeanVo
     * @throws UedmException 自定义异常
     */
    @Override
    public PeakShiftDeviceChildBeanVo detail(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        // 参数校验
        PeakShiftDeviceChildBeanVo peakDevice = checkPeakDevice(deviceId);
        if (CommonConst.SNMP.equals(peakDevice.getResourceType())) {
            // 向南向发送请求并且等待结果(最多等2min)
            peakShiftSNMPService.pullDeviceStrategy(deviceId, serviceBaseInfoBean.getLanguageOption());
            // 获取设备最新策略文件信息
            return getLatestFileBean(deviceId);
        }
        try {
            // 返回结果
            PeakShiftDeviceChildBeanVo resultBean = new PeakShiftDeviceChildBeanVo();
            // 可以为空，但不允许为null
            if (serviceBaseInfoBean == null) {
                log.warn("the parameter is null -> serviceBaseInfoBean");
                return resultBean;
            }
            log.info("parameter information {}, {}", deviceId, serviceBaseInfoBean);
            // 存储文件信息Map(key为设备ID)
            Map<String, PeakShiftDeviceChildBeanVo> fileInfoMap = new HashMap<>();
            // 存储文件名称信息(key为文件ID, value为原文件名)
            Map<String, String> fileNameMap = new HashMap<>();
            // 存储用户有权限的设备节点
            List<String> authorizedDeviceIds = new ArrayList<>();

            resultBean = detailFilterPermissionNodes(deviceId, resultBean, authorizedDeviceIds);

            getDetailDeviceFileInfo(resultBean, fileInfoMap, fileNameMap, authorizedDeviceIds);

            return resultBean;
        } catch (Exception e) {
            log.error("PeakShiftDeviceServiceImpl detail occur exception {}, {}", e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    /**
     * 根据设备ID查询详细信息
     * @param deviceId 设备ID
     * @param lang 语言选项
     * @return PeakShiftMonitorDetailVo
     * @throws UedmException 自定义异常
     */
    public PeakShiftMonitorDetailVo peakShiftMonitorDetail(String deviceId, String lang) throws UedmException {
        try {
            // 取数据库数据
            PeakShiftMonitorDetailVo result = new PeakShiftMonitorDetailVo();
            PeakShiftDeviceChildBeanVo deviceFile = peakShiftDeviceFileMapper.selectDeviceFileIdAndTime(deviceId);
            if (deviceFile != null) {
                result.setDeviceStrategyTime(deviceFile.getGmtModified());
                result.setDeviceStrategyFileId(deviceFile.getFileId());
                UpDownloadFileBean deviceStrategyFile = upDownloadFileMapper.selectUpDownloadFileBeanById(deviceFile.getFileId());
                if (deviceStrategyFile != null) {
                    result.setDeviceStrategyFileName(deviceStrategyFile.getOriginalName());
                }
            }

            PeakShiftDeviceTaskBo peakShiftDeviceTaskBo = getTaskInfoByDeviceId(deviceId);
            if (peakShiftDeviceTaskBo != null) {
                result.setSettingStrategyFileId(peakShiftDeviceTaskBo.getFileId());
                result.setSettingStrategyTime(peakShiftDeviceTaskBo.getIssuedTime());
                UpDownloadFileBean settingStrategyFile = upDownloadFileMapper.selectUpDownloadFileBeanById(peakShiftDeviceTaskBo.getFileId());
                if (settingStrategyFile != null) {
                    result.setSettingStrategyFileName(settingStrategyFile.getOriginalName());
                }
            }

            // 取缓存数据
            getByRedis(deviceId, result, lang);

            log.debug("PeakShiftMonitorDetailVo:{}", result);
            return result;
        } catch (Exception e) {
            log.error("peakShiftMonitorDetail exception!", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    public PeakShiftDeviceTaskBo getTaskInfoByDeviceId(String deviceId) {
        PeakShiftDeviceTaskBo result = null;
        try {
            result = peakShiftTaskService.selectLatestTaskByDeviceId(deviceId);
        } catch (Exception e) {
            log.error("PeakShiftDeviceServiceImpl getTaskInfoByDeviceId failed", e);
        }
        return result;
    }

    public void getByRedis(String deviceId, PeakShiftMonitorDetailVo result, String lang) throws UedmException {
        DevicePeakCacheInfoBean redisBean = devicePeakCacheInfoService.getInfoByDeviceId(deviceId);
        if (redisBean != null) {
            result.setBattCapacity(redisBean.getBattCapacity());
            result.setEnablePeak(redisBean.getEnablePeak());
            result.setBattStatus(redisBean.getBattStatus());

            String peakStatusName = devicePeakCacheInfoService.getRunningStatusName(redisBean.getRunningStatus(), lang);
            result.setPeakStatus(peakStatusName);
            result.setPeakStatusDetail(redisBean.getRunningStatusMap());

            String expirationDate = redisBean.getExpirationDate();
            String now = dateTimeService.getCurrentTime();
            if (expirationDate != null && now != null) {
                result.setStrategyStatus(expirationDate.compareTo(now) <= 0 ? "1" : "0");
            }
            result.setEffectiveDate(redisBean.getEffectiveDate());
            result.setExpirationDate(redisBean.getExpirationDate());

            String deviceType = redisBean.getDeviceType();
            PeakShiftCommonService peakShiftCommonService = peakShiftFactory.generateByDeviceType(deviceType);
            if (peakShiftCommonService == null) {
                log.error("getByRedis peakShiftCommonService is null {} ", deviceType);
                return;
            }
            List<DevicePeakCacheInfoBean> devicePeakCacheInfoBeans = Collections.singletonList(redisBean);
            // 获取电池状态、最近下发策略等数据
            PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean = peakShiftCommonService.prepareBaseData(devicePeakCacheInfoBeans);
            peakShiftMonitorBaseDataBean.defaultValueIfNull();
            log.debug("getByRedis battpackList{}", JSON.toJSONString(peakShiftMonitorBaseDataBean));

            Pair<String, String> currentStrategyAndExecStatus = getCurrentStrategyAndExecStatus(deviceId, peakShiftMonitorBaseDataBean);
            result.setExecStatus(currentStrategyAndExecStatus.getRight());
            result.setCurrPeakStrategy(currentStrategyAndExecStatus.getLeft());
            result.setDeviceType(redisBean.getDeviceType());
            result.setDeviceTypeName(i18nUtils.getMapFieldByLanguageOption(PeakDeviceTypeEnum.getShowNameByRange(redisBean.getDeviceType()), lang));
        }
    }

    private void getDetailDeviceFileInfo(PeakShiftDeviceChildBeanVo resultBean, Map<String, PeakShiftDeviceChildBeanVo> fileInfoMap, Map<String, String> fileNameMap, List<String> authorizedDeviceIds) throws UedmException {
        if (!CollectionUtils.isEmpty(authorizedDeviceIds)) {
            // 通知设备上传文件前，先修改设备状态为status=false,若上传成功，status会变为true
            peakShiftDeviceFileMapper.updateStatusByDeviceId(authorizedDeviceIds, false);
            // 发送信息给南向
            sendMsgToDevice(authorizedDeviceIds, null, null);

            List<PeakShiftDeviceChildBeanVo> beanVos = getPeakShiftDeviceChildBeanVos(authorizedDeviceIds);

            // 获取文件IdList
            List<String> fileIdList = beanVos.stream().map(PeakShiftDeviceChildBeanVo::getFileId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(fileIdList)) {
                log.info("getDetailDeviceFileInfo file id list, fileIdList, {}", fileIdList.size());
                // 查询文件名称
                List<UpDownloadFilePO> upDownloadFileBeans = upDownloadFileMapper.selectByIds(fileIdList);
                for (UpDownloadFilePO bean : upDownloadFileBeans) {
                    fileNameMap.put(bean.getId(), bean.getOriginalName());
                }
            }
            // 遍历设置文件名称
            for (PeakShiftDeviceChildBeanVo bean : beanVos) {
                bean.setFileName(fileNameMap.get(bean.getFileId()));
                fileInfoMap.put(bean.getId(), bean);
            }
            // 设置文件信息
            PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = fileInfoMap.get(resultBean.getId());
            if (peakShiftDeviceBeanVo != null) {
                resultBean.setFileId(peakShiftDeviceBeanVo.getFileId());
                resultBean.setFileName(peakShiftDeviceBeanVo.getFileName());
                resultBean.setGmtModified(peakShiftDeviceBeanVo.getGmtModified());
            }
        }
    }

    @NotNull
    public List<PeakShiftDeviceChildBeanVo> getPeakShiftDeviceChildBeanVos(List<String> authorizedDeviceIds) throws UedmException {
        List<PeakShiftDeviceChildBeanVo> beanVos = new ArrayList<>();
        for (int i = 0; i < FOR_60; i++) {
            log.info("battery-manager wait for send finish {}", i);
            if (redisService.getCache(BCUA_REDIS_NAME, authorizedDeviceIds.get(0)) != null) {
                redisService.delete(BCUA_REDIS_NAME, authorizedDeviceIds.get(0));
                log.info("battery-manager send finish, now break");
                break;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(SLEEP_100);
            } catch (InterruptedException e) {
                log.error("PeakShiftDeviceServiceImpl getPeakShiftDeviceChildBeanVos occur InterruptedException {}, {}", e.getMessage(), e);
            }
        }
        for (int i = 0; i < FOR_10; i++) {
            log.info("battery-manager wait for upload {}", i);
            beanVos = peakShiftDeviceFileMapper.selectFileIdByDeviceId(authorizedDeviceIds, true);
            if (beanVos.size() == authorizedDeviceIds.size()) {
                break;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(SLEEP_1000);
            } catch (InterruptedException e) {
                log.error("PeakShiftDeviceServiceImpl getPeakShiftDeviceChildBeanVos occur InterruptedException {}, {}", e.getMessage(), e);
            }
        }
        return beanVos;
    }

    /**
     * 发送信息给南向
     * @param deviceIds 设备ID列表
     */
    public void sendMsgToDevice(List<String> deviceIds, Integer pageNo, Integer pageSize) throws UedmException {
        log.info("deviceIds:{}", deviceIds);
        try {
            List<CollectorEntity> collectorBeans = collectorCacheManager.selectCollectorByIds(new HashSet<>(deviceIds));
            List<MonitorDeviceBaseBean> deviceBaseBeans = collectorBeans.stream()
                    .map(collectorCacheBean -> {
                        MonitorDeviceBaseBean mo = new MonitorDeviceBaseBean();
                        mo.setId(collectorCacheBean.getId());
                        mo.setSignalCycle(DeviceUtils.parseSignalCycle(collectorCacheBean.getProtocolAttribute()));
                        mo.setMainLinkId(DeviceUtils.parseMasterLink(collectorCacheBean.getLinkInfo()));
                        return mo;
                    })
                    .collect(Collectors.toList());
            log.info("deviceBaseBeans:{}", deviceBaseBeans.size());
            if (!CollectionUtils.isEmpty(deviceBaseBeans)) {
                for (MonitorDeviceBaseBean bean : deviceBaseBeans) {
                    Map<String, Object> map = jsonService.jsonToObject(jsonService.objectToJson(bean), Map.class);
                    RemoteControlBean remoteControlBean = new RemoteControlBean();
                    remoteControlBean.setOmpId("04009B");
                    remoteControlBean.setValue("0");
                    remoteControlBean.setRelationPosition("1");
                    remoteControlBean.setPointIndex("1");
                    remoteControlBean.setDevice(map);
                    log.info("MonitorDeviceBaseBean:{}", map);
                    msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                            jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
                }
            }
        } catch (Exception e) {
            log.error("PeakShiftTaskServiceImpl -> sendMsgtoDevice is error", e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    private PeakShiftDeviceChildBeanVo detailFilterPermissionNodes(String deviceId, PeakShiftDeviceChildBeanVo resultBean, List<String> authorizedDeviceIds) {
        resultBean = getNodeInfoById(deviceId);
        authorizedDeviceIds.add(resultBean.getId());
        return resultBean;
    }

    private void detailParameterVerification(String deviceId) throws UedmException {
        if (!GlobalOptional.GLOBAL_ROOT.equalsIgnoreCase(deviceId)) {
            // 判断parentId是否在系统中存在
            PeakShiftDeviceChildBeanVo childBeanVo = getNodeInfoById(deviceId);
            if (childBeanVo == null) {
                log.info("does not exist in the system, deviceId: {}", deviceId);
                throw new UedmException(UedmErrorCodeConstants.OBJECT_NOT_EXIST, "deviceId does not exist in the system");
            }
            // 判断该设备是否为智能错峰支持的设备类型
            PeakShiftDeviceChildBeanVo beanVo = getBcuaNodeInfoById(deviceId);
            if (beanVo == null) {
                log.info("not a supported device type for smart peak staggering, deviceId: {}", deviceId);
                throw new UedmException(UedmErrorCodeConstants.OBJECT_OPERATION_EXCEED_RANGE_NOT_ALLOW, "deviceId not a supported device type for smart peak staggering");
            }
        }
    }
    /* Started by AICoder, pid:t9904ud4a3h7b83140520b4360981b2dc0d7e81b */
    public PeakShiftDeviceChildBeanVo checkPeakDevice(String deviceId) throws UedmException {
        List<CollectorEntity> collectors = collectorCacheManager.getCollectorById(Collections.singletonList(deviceId));
        if (collectors.size() < 1) {
            log.error("not found device: {}", deviceId);
            throw new UedmException(UedmErrorCodeConstants.OBJECT_NOT_EXIST, "not found device");
        }
        CollectorEntity collectorEntity = collectors.get(0);
        // 获取错峰设备类型
        String deviceType = CollectorUtils.parseDeviceType(collectorEntity.getMoc(), collectorEntity.getProtocolAttribute());
        log.info("checkPeakDevice deviceType:{} {}", deviceType, deviceId);

        // 仅电池直连类型需要从设备获取文件
        if (deviceType == null || !StringUtils.equalsAny(deviceType, CommonConst.SNMP, CommonConst.BCUA)) {
            log.error("not a supported get file from device  {} {}", deviceType, deviceId);
            throw new UedmException(UedmErrorCodeConstants.OBJECT_OPERATION_EXCEED_RANGE_NOT_ALLOW, "not a supported get file from device");
        }

        PeakShiftDeviceChildBeanVo result = new PeakShiftDeviceChildBeanVo();
        result.setId(deviceId);
        result.setName(collectorEntity.getName());
        // 临时存放设备类型
        result.setResourceType(deviceType);
        return result;
    }
    /* Ended by AICoder, pid:t9904ud4a3h7b83140520b4360981b2dc0d7e81b */

    public PeakShiftDeviceChildBeanVo getNodeInfoById(String deviceId) {
        PeakShiftDeviceChildBeanVo deviceChildBeanVo = new PeakShiftDeviceChildBeanVo();
        try {
            List<CollectorEntity> collectorCacheBeans = collectorCacheManager.selectCollectorByIds(Collections.singleton(deviceId));
            if (!CollectionUtils.isEmpty(collectorCacheBeans)) {
                CollectorEntity collectorCacheBean = collectorCacheBeans.get(0);
                deviceChildBeanVo.setId(collectorCacheBean.getId());
                deviceChildBeanVo.setName(collectorCacheBean.getName());
            }
        } catch (Exception e) {
            log.error("getNodeInfoById error={}", e);
        }
        return deviceChildBeanVo;
    }

    public PeakShiftDeviceChildBeanVo getBcuaNodeInfoById(String deviceId) {
        PeakShiftDeviceChildBeanVo deviceChildBeanVo = new PeakShiftDeviceChildBeanVo();
        try {
            CollectorEntity collectorCacheBean = (CollectorEntity) collectorCacheManager.selectByKey(deviceId);
            if (collectorCacheBean != null) {
                String[] parts = collectorCacheBean.getMoc().split("\\.");
                if (CommonConst.BCUA.equalsIgnoreCase(parts[parts.length - 1])) {
                    deviceChildBeanVo.setId(collectorCacheBean.getId());
                    deviceChildBeanVo.setName(collectorCacheBean.getName());
                }
            }
        } catch (Exception e) {
            log.error("getBcuaNodeInfoById error={}", e);
        }
        return deviceChildBeanVo;
    }
    /* Ended by AICoder, pid:d8512pf09b819ba1457c0b9b52d1de9fa9b0df3a */

    /* Started by AICoder, pid:38ac3x2005d4176144b90928b0793f32ae282b2f */
    /**
     * 根据逻辑组ID和设备类型查询其下对应类型设备ID(RPC接口)
     *
     * @param logicIds 逻辑组ID列表
     * @param serviceBaseInfoBean 服务基本信息
     * @param deviceType 设备类型
     * @return 分页信息
     * @throws UedmException 自定义异常
     */
    @Override
    public PageInfo<String> selectByLogicAndDeviceType(List<String> logicIds, ServiceBaseInfoBean serviceBaseInfoBean, String deviceType) throws UedmException {
        try {
            if (CollectionUtils.isEmpty(logicIds)) {
                log.warn("PeakShiftTaskServiceImpl selectByLogic -> logicIds is null");
                return new PageInfo<>(new ArrayList<>());
            }

            // 返回结果
            PageInfo<String> pageInfo = new PageInfo<>();

            // 查询操作
            List<String> applyRange = PeakDeviceTypeEnum.getApplyRangeById(deviceType);
            List<String> deviceIds = collectorCacheManager.getCollectorIdsByPathAndType(new HashSet<>(applyRange));
            log.info("PeakShiftTaskServiceImpl selectByLogic -> deviceIds size: {}", deviceIds.size());

            pageInfo.setTotal(deviceIds.size());

            if (serviceBaseInfoBean.isPage()) {
                deviceIds = PageUtil.getPageList(deviceIds, serviceBaseInfoBean.getPageNo(), serviceBaseInfoBean.getPageSize());
            }

            pageInfo.setList(deviceIds);
            return pageInfo;
        } catch (Exception e) {
            log.error("PeakShiftTaskServiceImpl -> selectByLogicAndDeviceType is error", e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    @Override
    public int setPeakShiftOperateRecord(PeakShiftOperationRecordPojo recordPojo, String userName) throws UedmException {
        int num = peakShiftOperationRecordService.setOperationRecord(recordPojo);
        return num;
    }

    /**
     * 新增BCUA策略状态
     * @param statusBeanList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setPeakShiftDeviceStatus(List<PeakShiftDeviceIndexStatusBean> statusBeanList) {
        log.debug("statusBean {}", statusBeanList);
        Date nowTime = new Date();
        PeakShiftDeviceStatusNewBean statusBean = new PeakShiftDeviceStatusNewBean();
        List<PeakShiftDeviceIndexStatusBean> disableList = new ArrayList<>();
        List<PeakShiftDeviceIndexStatusBean> enableList = new ArrayList<>();
        for (PeakShiftDeviceIndexStatusBean indexStatusBean : statusBeanList)
        {
            statusBean.setId(indexStatusBean.getDeviceId());
            indexStatusBean.setGmtCreate(nowTime);
            indexStatusBean.setGmtModified(nowTime);
            String status = indexStatusBean.getStatus();
            if ("0".equals(status)) {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getDisabledId());
                disableList.add(indexStatusBean);
            } else if ("1".equals(status)) {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getEnabledId());
                enableList.add(indexStatusBean);
            } else {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getUnknownId());
            }
        }
        String status = PeakShiftDeviceStatusEnum.getUnknownId();
        if (enableList.size() == statusBeanList.size()) {
            status = PeakShiftDeviceStatusEnum.getEnabledId();
        } else if (disableList.size() == statusBeanList.size()) {
            status = PeakShiftDeviceStatusEnum.getDisabledId();
        } else if (disableList.size() > 0 && enableList.size() > 0) {
            status = PeakShiftDeviceStatusEnum.getPartId();
        }
        statusBean.setStatus(status);
        statusBean.setGmtCreate(nowTime);
        statusBean.setGmtModified(nowTime);
        log.info("setPeakShiftDeviceStatus all size {}, enable size {}, disable size {}", statusBeanList.size(), enableList.size(), disableList.size());
        peakShiftDeviceIndexStatusMapper.upsertPeakShiftDeviceIndexStatus(statusBeanList);//CSU不需要存储索引位置
        int num = peakShiftDeviceStatusMapper.upsertPeakShiftDeviceStatus(statusBean);
        return num;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setCSUPeakShiftDeviceStatus(List<PeakShiftDeviceIndexStatusBean> statusBeanList) {
        log.debug("statusBean {}", statusBeanList);
        Date nowTime = new Date();
        PeakShiftDeviceStatusNewBean statusBean = new PeakShiftDeviceStatusNewBean();
        List<PeakShiftDeviceIndexStatusBean> disableList = new ArrayList<>();
        List<PeakShiftDeviceIndexStatusBean> enableList = new ArrayList<>();
        for (PeakShiftDeviceIndexStatusBean indexStatusBean : statusBeanList)
        {
            statusBean.setId(indexStatusBean.getDeviceId());
            indexStatusBean.setGmtCreate(nowTime);
            indexStatusBean.setGmtModified(nowTime);
            String status = indexStatusBean.getStatus();
            if ("0".equals(status)) {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getDisabledId());
                disableList.add(indexStatusBean);
            } else if ("2".equals(status)) {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getEnabledId());
                enableList.add(indexStatusBean);
            } else {
                indexStatusBean.setStatus(PeakShiftDeviceIndexStatusEnum.getUnknownId());
            }
        }
        String status = PeakShiftDeviceStatusEnum.getUnknownId();
        if (enableList.size() == statusBeanList.size()) {
            status = PeakShiftDeviceStatusEnum.getEnabledId();
        } else if (disableList.size() == statusBeanList.size()) {
            status = PeakShiftDeviceStatusEnum.getDisabledId();
        } else if (disableList.size() > 0 && enableList.size() > 0) {
            status = PeakShiftDeviceStatusEnum.getPartId();
        }
        log.info("status {}", status);
        statusBean.setStatus(status);
        statusBean.setGmtCreate(nowTime);
        statusBean.setGmtModified(nowTime);
        log.info("setPeakShiftDeviceStatus all size {}, enable size {}, disable size {}", statusBeanList.size(), enableList.size(), disableList.size());
        //CSU不需要存储索引位置,新增策略状态即可
        int num = peakShiftDeviceStatusMapper.upsertPeakShiftDeviceStatus(statusBean);
        return num;
    }
    /* Ended by AICoder, pid:38ac3x2005d4176144b90928b0793f32ae282b2f */

    @Override
    public boolean sendPortToAllBCUADevices(String port) throws UedmException
    {
        log.info("got port {}", port);
        List<CollectorEntity> deviceBaseBeans = collectorCacheManager.getCollectorsByType(new HashSet<>(Collections.singletonList("BCUA")));
        if (CollectionUtils.isEmpty(deviceBaseBeans))
        {
            return false;
        }
        for (CollectorEntity bean : deviceBaseBeans)
        {
            Map<String, Object> map = jsonService.jsonToObject(jsonService.objectToJson(bean), Map.class);
            RemoteControlBean remoteControlBean;
            remoteControlBean = new RemoteControlBean();
            remoteControlBean.setOmpId("030184");
            remoteControlBean.setValue(port);
            remoteControlBean.setRelationPosition("1");
            remoteControlBean.setPointIndex("1");
            remoteControlBean.setDevice(map);
            msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                    jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
        }
        return true;
    }
    /* Started by AICoder, pid:8bc3asbb94jd2451457309b1e11d777c004149bf */
    @Override
    public void dealDevicePeakStrategy(BatteryChargeDischargeHistoryResponeBean deviceStrategyBean) throws UedmException, com.zte.uedm.basis.exception.UedmException {
        String collectorId = deviceStrategyBean.getCollectorId();
        String status = deviceStrategyBean.getStatus();
        String logId = deviceStrategyBean.getLogId();
        // 拉取设备错峰策略失败
        if (!GlobalConstants.PULL_DEVICE_STRATEGY_STATUS_SUCCESS.equals(status)) {
            // 更新状态缓存为失败
            String failureCause = deviceStrategyBean.getFailureCause();
            failureCause = StringUtils.isEmpty(failureCause) ? DeviceStrategyPullStatusEnum.PULL_DEVICE_PEAK_STRATEGY_FAILURE.getCode() : failureCause;
            log.error("dealDevicePeakStrategy pull device strategy error {} {}", collectorId, failureCause);
            redisService.put(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId, failureCause);

            // 是否需要重试
            retryPullDeviceStrategy(collectorId, logId);
            throw new UedmException(-1, "pull device strategy failed");
        }

        // 获取文件的ftp路径
        Map<String, Object> functionRspParameter = deviceStrategyBean.getFunctionRspParameter();
        String filePath = (String) MapUtils.emptyIfNull(functionRspParameter).get(GlobalConstants.REMOTE_FILE_ACQUISITION_RSP_KEY_FTP);
        if (StringUtils.isEmpty(filePath)) {
            log.error("dealDevicePeakStrategy file path is empty {}", JSON.toJSONString(functionRspParameter));
            redisService.put(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId, DeviceStrategyPullStatusEnum.FILE_NOT_EXIST.getCode());
            retryPullDeviceStrategy(collectorId, logId);
            throw new UedmException(-1, "file path not found");
        }

        log.info("dealDevicePeakStrategy start download file");
        // 下载bin文件并解析
        PeakShiftDeviceStrategyDetailBean detailBean = downloadAndParseBinFile(collectorId, logId, filePath);
        //生成xlsx文件、上传xlsx文件、更新文件表
        generateAndSaveXlsx(detailBean, collectorId);
    }

    public void generateAndSaveXlsx(PeakShiftDeviceStrategyDetailBean detailBean, String collectorId) throws com.zte.uedm.basis.exception.UedmException, UedmException {

        // 更新设备实际错峰表与详情表
        peakShiftDeviceFileRepository.insertPeakShiftDeviceStrategyDetail(detailBean);
        PeakShiftDeviceStrategyBean strategyBean = new PeakShiftDeviceStrategyBean();
        peakShiftTemplateFileService.setPeakShiftDeviceStrategyBean(collectorId, detailBean.getId(), strategyBean);

        // 转为生成xlsx文件需要的bean
        TemplateStrategyDetailEntity latestStrategy = peakShiftSNMPService.getLatestStrategy(collectorId, null);
        log.info("dealDevicePeakStrategy convert xlsx bean success");

        UpDownloadFileDataBasePO upDownloadFileBean = new UpDownloadFileDataBasePO();
        // 生成xlsx文件，并上传至ftp
        generateXlsxAndUploadToFtp(latestStrategy, upDownloadFileBean);

        //文件信息存表
        String currentTime = dateTimeService.getCurrentTime();
        upDownloadFileBean.setFileType(FILE_TYPE_UPLOAD);
        upDownloadFileBean.setFileSuffix(CommonConst.SUFFIX_XLSX);
        upDownloadFileBean.setOriginalName(GlobalConstants.SNMP_BIN_XLSX_FILE_NAME);
        upDownloadFileBean.setName(GlobalConstants.SNMP_BIN_XLSX_FILE_NAME);
        upDownloadFileBean.setGmtCreate(currentTime);
        upDownloadFileBean.setGmtModified(currentTime);
        upDownloadFileBean.setModule(FILE_MODULE_TYPE_BCUA);
        upDownloadFileBean.setOperator(DEFAULT_USER);
        upDownloadFileMapper.insertFile(upDownloadFileBean);

        // 更新错峰设备最新文件表(peak_shift_device_file)
        peakShiftTemplateFileService.changeDeviceUploadResult(collectorId, upDownloadFileBean.getId(), DEFAULT_USER, true);
        // 删除拉取状态缓存
        redisService.delete(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId);

    }

    public PeakShiftDeviceStrategyDetailBean downloadAndParseBinFile(String collectorId, String logId, String filePath) throws UedmException {
        PeakShiftDeviceStrategyDetailBean detailBean;
        // 创建本地临时文件
        String tempFileId = UUID.randomUUID().toString();
        File tempFile = new File(tempFileId);
        try(FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            // 从ftp下载文件
            defaultFtpClient.getFile(tempFile.getAbsolutePath(), filePath);
            log.info("dealDevicePeakStrategy download ftp file success");

            // 解析bin文件
            detailBean = peakShiftSNMPService.parseStrategyBeanFormByte(FileUtil.readBytes(tempFile));
            log.info("dealDevicePeakStrategy parse bin file success");

        } catch (FtpClientException e) {
            log.error("dealDevicePeakStrategy file error", e);
            // 修改拉取结果为文件不存在
            redisService.put(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId, DeviceStrategyPullStatusEnum.FILE_NOT_EXIST.getCode());
            retryPullDeviceStrategy(collectorId, logId);
            throw new UedmException(-1, "get ftp file error");

        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("dealDevicePeakStrategy file parse error", e);
            // 修改拉取结果为文件解析错误
            redisService.put(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId, DeviceStrategyPullStatusEnum.FILE_PARSING_FAILED.getCode());
            retryPullDeviceStrategy(collectorId, logId);
            throw new UedmException(-1, "parse file error");
        } catch (Exception e) {
            log.error("dealDevicePeakStrategy file to ftp error", e);
            // 修改拉取结果为文件不存在
            redisService.put(GlobalConstants.REDIS_TOPIC_DEVICE_PEAK_STRATEGY_PULL_STATUS, collectorId, DeviceStrategyPullStatusEnum.FILE_NOT_EXIST.getCode());
            retryPullDeviceStrategy(collectorId, logId);
            throw new UedmException(-1, "get ftp file error");
        } finally {
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
        return detailBean;
    }

    public void generateXlsxAndUploadToFtp(TemplateStrategyDetailEntity latestStrategy, UpDownloadFileDataBasePO upDownloadFileBean) throws UedmException {
        // 生成xlsx的临时文件
        String tempXlsxFileId = UUID.randomUUID().toString();
        File tempXlsxFile = new File(tempXlsxFileId + CommonConst.SUFFIX_XLSX);
        String parentPath = peakShiftTemplateFileService.getRemotePath();
        String xlsxFilePath = parentPath + File.separator + tempXlsxFileId + CommonConst.SUFFIX_XLSX;
        try(BufferedOutputStream outputStream = FileUtil.getOutputStream(tempXlsxFile);
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient();) {
            // 生成xlsx文件
            peakShiftTemplateFileService.convertToExcel(latestStrategy, outputStream, xssfWorkbook);
            log.info("dealDevicePeakStrategy generate xlsx success");
            outputStream.flush();

            // 上传xlsx至ftp
            defaultFtpClient.mkdir(parentPath);
            defaultFtpClient.put(tempXlsxFile.getAbsolutePath(), xlsxFilePath, false);
            log.info("dealDevicePeakStrategy upload xlsx success");

        } catch (Exception e) {
            log.error("dealDevicePeakStrategy  generate xlsx error", e);
            throw new UedmException(-1, "generate xlsx error");
        }finally {
            if (tempXlsxFile.exists()) {
                tempXlsxFile.delete();
            }
        }

        upDownloadFileBean.setId(tempXlsxFileId);
        upDownloadFileBean.setFtpPath(xlsxFilePath);
        upDownloadFileBean.setFilePath(xlsxFilePath);
    }

    /**
     * 若在策略下发成功后 拉取设备策略失败，需要重新拉取设备策略，最多重试2次
     * @param collectorId
     * @param logId       策略下发成功后触发的格式为 TYPE_DISTRIBUTE_次数_collectorId_时间戳
     */
    public void retryPullDeviceStrategy(String collectorId, String logId) {
        // 包含DISTRIBUTE_表示是策略下发成功后触发的
        if (StringUtils.isBlank(logId) || !logId.contains(GlobalConstants.KAFKA_LOG_DISTRIBUTE)) {
            return;
        }
        // 解析logId,获取发送次数
        String[] split = logId.split("_");
        int times = Integer.parseInt(split[2]);
        if (split.length == 5 && times < 3) {
            times++;
            KafkaMessageBean kafkaMessageBean = peakShiftSNMPService.buildPullDeviceStrategyMsgBean(collectorId, times, true);
            String dataMessage = JSON.toJSONString(kafkaMessageBean);
            log.info("retry pull device strategy kafka message: {}", dataMessage);
            msgSenderService.sendMsgAsync(KafkaConst.KAFKA_BATTERY_CHARGE_DISCHARGE_HISTORY_DATA_TOPIC, dataMessage);
        }
    }

    public PeakShiftDeviceChildBeanVo getLatestFileBean(String collectorId){
        PeakShiftDeviceChildBeanVo latestFileBean = peakShiftDeviceFileMapper.selectDeviceFileIdAndTime(collectorId);
        if (latestFileBean != null && StringUtils.isNotBlank(latestFileBean.getFileId())) {
            // 获取文件名称
            UpDownloadFilePO upDownloadFilePO = upDownloadFileMapper.selectById(latestFileBean.getFileId());
            if (upDownloadFilePO != null) {
                latestFileBean.setFileName(upDownloadFilePO.getName());
                return latestFileBean;
            }
        }
        return new PeakShiftDeviceChildBeanVo();
    }
    /* Ended by AICoder, pid:8bc3asbb94jd2451457309b1e11d777c004149bf */
}
