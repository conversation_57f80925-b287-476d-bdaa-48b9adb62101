package com.zte.uedm.battery.domain;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

/**
 * 领域层
 * 电池告警 - 相关api
 */
public interface BattAlarmDomain
{
    /**
     * 查询电池的所有告警级别
     * 数据来源于 common的枚举值
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> selectAlarmLevels(ServiceBaseInfoBean serviceBean) throws UedmException;


    /**
     * 查询电池告警信息
     * @param moId 电池id
     * @return
     * @throws UedmException
     */
    List<Alarm> selectAlarmsByMoId(String moId, String languageOption) throws UedmException;

    /**
     * 查询多个电池的告警信息
     * @param moIds
     * @return
     * @throws UedmException
     */
    Map<String, List<AlarmDTO>> selectAlarmsByMoIds(List<String> moIds, String languageOption) throws UedmException;

    /**
     * 在告警列表中查询最高等级告警
     * @param alarms
     * @return
     * @throws UedmException
     */
    Integer selectMostAlarmLevelInList(List<AlarmDTO> alarms);

    /**
     * 查询电池健康告警
     * @param moIds
     * @return
     * @throws UedmException
     */
    Map<String, Boolean> selectHealthAlarmByMoIds(List<String> moIds, Map<String, BatteryEvalDTO> dtoMap, Map<String, Map<String, String>> nameMap) throws UedmException;

}
