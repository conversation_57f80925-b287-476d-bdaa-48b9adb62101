package com.zte.uedm.battery.a_interfaces.peakshift.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:s5f19mcb18c7ab2146fa087440dbc710bcf127d8 */
@Getter
@Setter
@ToString
@ApiModel(description = "错峰任务状态")
public class PeakShiftDistributionStatusFlipDto {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务标识")
    private String id;
    /**
     * 目标状态
     */
    @ApiModelProperty(value = "目标状态")
    private String status;
}
/* Ended by AICoder, pid:s5f19mcb18c7ab2146fa087440dbc710bcf127d8 */