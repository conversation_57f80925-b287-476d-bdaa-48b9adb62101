package com.zte.uedm.battery.controller.batttest.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zte.uedm.battery.controller.batttest.vo.BattRecordIndexDimVo;
import com.zte.uedm.battery.enums.batttest.BattTestStatusEnums;
import com.zte.uedm.battery.enums.batttest.BattTestTypeEnums;
import com.zte.uedm.battery.enums.record.BattTestIndexDefineEnums;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
@Slf4j
@ApiModel(description = "测试记录")
public class TestHistoryDto {

    /**
     * 开关电源id
     */
    @NotBlank(message = "id can not be blank")
    @ApiModelProperty("开关电源id")
    private String id;
    @ApiModelProperty("维度")
    @NotEmpty(message="historyDims can not be empty!")
    private List<BattRecordIndexDimVo> historyDims;
    /**
     * 设备类型
     */
    @ApiModelProperty("设备类型")
    private String scene;
    /**
     * 测试状态
     */
    @ApiModelProperty("测试状态")
    private List<String> testStatus;
    @ApiModelProperty("测试类型")
    private List<String> testTypes;
    /**
     * 测试开始时间
     */
    @ApiModelProperty("测试开始时间")
    private String testStartTimeBegin;
    @ApiModelProperty("位置")
    private String position;
    /**
     * 测试结束时间
     */
    @ApiModelProperty("测试结束时间")
    private String testStartTimeEnd;

    /**
     * 分页数量
     */
    @ApiModelProperty("分页数量")
    private Integer pageNo;
    /**
     * 	每页数量
     */
    @ApiModelProperty("每页数量")
    private Integer pageSize;


    /**
     * 非空校验
     * @return  校验结果--true -> 通过  false 未通过 ,  未通过校验的字段名称
     */
    public Pair<Boolean, Set<String>> checkNotEmpty()
    {
        Set<String> blankSets = new HashSet<>();
        boolean result = true;
        ValidationResult validationResult = ValidationUtils.validateForDefalut(this);
        if(validationResult.isHasErrors())
        {
            result=false;
            blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
        }
        return Pair.of(result,blankSets);
    }

    /**
     * 参数范围校验
     */
    public Pair<Boolean, Set<String>> checkParamsRange()
    {
        Set<String> blankSets = new HashSet<>();
        Boolean result = true;
        //测试状态
        result = checkRange(blankSets, result, testStatus, BattTestStatusEnums.getAllId(),"testStatus");
        //测试类型
        result = checkRange(blankSets, result, testTypes, Arrays.asList(BattTestTypeEnums.PERIODIC.getId(),BattTestTypeEnums.TEMPORARY.getId()),"testTypes");
        return Pair.of(result,blankSets);
    }

    private Boolean checkRange(Set<String> blankSets, Boolean result, List<String> dims, List<String> optionRangeList,String attrName) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dims)) {
            for (String dim : dims) {
                if (!optionRangeList.contains(dim)) {
                    result = false;
                    blankSets.add(attrName);
                }
            }
        }
        return result;
    }
    /**
     * 两者有值,校验时间
     */
    public boolean checkTime(){
        if (!StringUtils.isEmpty(testStartTimeBegin) && !StringUtils.isEmpty(testStartTimeEnd)) {
            //开始时间大于等于结束时间
            return  testStartTimeBegin.compareTo(testStartTimeEnd) < 0;
        }
        //开始时间小于结束时间
        return true;
    }

}
