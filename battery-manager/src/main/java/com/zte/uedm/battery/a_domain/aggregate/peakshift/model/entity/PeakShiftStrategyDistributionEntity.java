package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/* Started by AICoder, pid:xc3f1018751d96414dd20840f0b9fb1e25513c24 */
/**
 * 错峰用电策略电池历史数据Bean
 */
@Setter
@Getter
@ToString
public class PeakShiftStrategyDistributionEntity {
    /**
     * id
     */
    String id;
    /**
     * 任务名称
     */
    String name;
    /**
     * 设备id列表
     */
    List<String> deviceNames;
    /**
     * 创建
     */
    String creator;
    /**
     * 备注
     */
    String description;
    /**
     * 任务状态：
     * edit:拟制中，pending:待执行，execute:执行中，success:成功，partialSuccess：部分成功，fail：失败，invalid：失效
     */
    String status;
    /**
     * 创建时间
     */
    String gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private String gmtModified;
    /**
     * 生效日期
     */
    private String effectiveDate;
    /**
     * 失效日期
     */
    private String expirationDate;
    /**
     * 进度：任务下发成功或者失败的设备数量/总设备数量
     */
    private String progress;

    /**
     * 生效状态  expired，nearToExpired ，normal
     * 当前时间>失效时间？失效; 当前时间+7d>失效时间？即将失效; 其余：正常
     */
    private String effectiveStatus;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

}
/* Ended by AICoder, pid:xc3f1018751d96414dd20840f0b9fb1e25513c24 */