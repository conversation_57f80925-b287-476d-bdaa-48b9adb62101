package com.zte.uedm.battery.a_domain.common.vpp;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/* Started by AICoder, pid:t7885o5d65r980b1454a0a4e70243476ee028f1a */
public enum FMOverviewEnums {
    DEVICENAME("deviceName", "{\"en_US\":\"Collector Name\",\"zh_CN\":\"采集器名称\"}", 1, true, true, null, false),
    DEVICE_TYPE_NAME("deviceType", "{\"en_US\":\"Collector Type\",\"zh_CN\":\"采集器类型\"}", 2, true, true, null, false),
    SITE_NAME("siteName", "{\"en_US\":\"Site Name\",\"zh_CN\":\"站点名称\"}", 3, true, true, null, false),
    NAME_PATH("namePath", "{\"en_US\":\"Position\",\"zh_CN\":\"位置\"}", 4, true, true, null, false),
    WORK_MODEL("workModel", "{\"en_US\":\"Work Mode\",\"zh_CN\":\"工作模式\"}", 5, true, true, null, true),
    START_TIME("startTime", "{\"en_US\":\"Start Time\",\"zh_CN\":\"开始时间\"}", 6, true, false, null, false),
    END_TIME("endTime", "{\"en_US\":\"End Time\",\"zh_CN\":\"结束时间\"}", 7, true, false, null, false),
    Actual_Power("actualPower", "{\"en_US\":\"Actual Power\",\"zh_CN\":\"实际功率\"}", 8, true, false, "kW", true),
    Response_Time("responseTime", "{\"en_US\":\"Response Time\",\"zh_CN\":\"响应时间\"}", 9, true, false, "s", true);

    private String id;
    private String name;
    private Integer defaultIndex;
    private Boolean defaultEnable;
    private Boolean defaultFixed;
    private String unit;
    private Boolean sortable;

    FMOverviewEnums(String id, String name, Integer defaultIndex, Boolean defaultEnable, Boolean defaultFixed, String unit, Boolean sortable) {
        this.id = id;
        this.name = name;
        this.defaultIndex = defaultIndex;
        this.defaultEnable = defaultEnable;
        this.defaultFixed = defaultFixed;
        this.unit = unit;
        this.sortable = sortable;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public Integer getDefaultIndex() {
        return defaultIndex;
    }

    public Boolean getDefaultEnable() {
        return defaultEnable;
    }

    public Boolean getDefaultFixed() {
        return defaultFixed;
    }

    public String getUnit() {
        return unit;
    }

    public Boolean getSortable() {
        return sortable;
    }

    /**
     * 获取所有id
     * @return
     */
    public static List<String> getAllDimIds() {
        List<String> allIds = new ArrayList<>();
        FMOverviewEnums[] values = FMOverviewEnums.values();
        for (FMOverviewEnums value : values) {
            if (StringUtils.isNotBlank(value.getId())) {
                allIds.add(value.getId());
            }
        }
        return allIds;
    }
}
/* Ended by AICoder, pid:t7885o5d65r980b1454a0a4e70243476ee028f1a */
