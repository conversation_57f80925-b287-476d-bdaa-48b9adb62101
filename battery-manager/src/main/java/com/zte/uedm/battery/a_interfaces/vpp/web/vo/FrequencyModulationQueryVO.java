package com.zte.uedm.battery.a_interfaces.vpp.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:d8e82a1e3ae575214a430b3330f35b2cf4c93b8c */
@Setter
@Getter
@ToString
public class FrequencyModulationQueryVO {
    private String deviceId; // 监控设备id

    private String deviceName; // 监控设备名称

    private String deviceType; // 设备类型: BCUA 和 CUS5

    private String siteId; // 站点ID

    private String siteName; // 站点名称

    private String namePath; // 设备名字路径

    private String workModel; // 工作模式

    private String actualPower; // 实际功率

    @ApiModelProperty("调频开始时间")
    private String startTime;

    @ApiModelProperty("调频结束时间")
    private String endTime;

    @ApiModelProperty("设备响应时间")
    private String responseTime;
}
/* Ended by AICoder, pid:d8e82a1e3ae575214a430b3330f35b2cf4c93b8c */
