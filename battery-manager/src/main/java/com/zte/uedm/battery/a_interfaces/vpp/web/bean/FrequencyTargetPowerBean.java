package com.zte.uedm.battery.a_interfaces.vpp.web.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:sacfb7309fu0f2c1408a0b6330b1b51805b4c5d3 */
@Setter
@Getter
@ToString
@ApiModel(description = "调频功率信息")
public class FrequencyTargetPowerBean {
    @ApiModelProperty(value = "序号")
    private Integer sequence;
    @ApiModelProperty(value = "目标功率")
    private String targetPower;
    @ApiModelProperty(value = "调节时长")
    private String frequencyDuration;
    @ApiModelProperty(value = "调节时间")
    private String frequencyTime;
}
/* Ended by AICoder, pid:sacfb7309fu0f2c1408a0b6330b1b51805b4c5d3 */
