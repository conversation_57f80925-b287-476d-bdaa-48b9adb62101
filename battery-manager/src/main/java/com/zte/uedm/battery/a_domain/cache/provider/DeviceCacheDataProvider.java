package com.zte.uedm.battery.a_domain.cache.provider;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.cache.conveter.ResourceBaseConverter;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.service.config.api.configuraiton.DeviceService;
import com.zte.uedm.service.config.api.configuraiton.vo.DeviceVo;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@ToString
@Service
@Slf4j
public class DeviceCacheDataProvider implements CacheDataProvider<String, DeviceEntity> {

    @Autowired
    private DeviceService deviceService;

    @Override
    public List<DeviceEntity> getCacheDataForCacheProvider(Set<String> keys) throws UedmException {
        /* Started by AICoder, pid:q22b6k054cwcd1e141cf0b8c30c00c1d611786ce */
        Map<String, DeviceEntity> deviceEntityMap = new HashMap<>();
        if (CollectionUtils.isEmpty(keys)) {
            List<DeviceVo> deviceVoList = deviceService.queryAll();
            Set<String> ids = deviceVoList.stream().map(DeviceVo::getId).collect(Collectors.toSet());
            log.info("getCacheDataForCacheProvider deviceVos size:{} ids:{}", deviceVoList.size(), ids.size());
            deviceVoList.stream()
                    .map(ResourceBaseConverter.INSTANCE::deviceVoToRpcVo)
                    .forEach(entity -> deviceEntityMap.put(entity.getId(), entity));
        } else {
            List<DeviceVo> deviceVoList = deviceService.queryByIds(new ArrayList<>(keys));
            deviceVoList.stream()
                    .map(ResourceBaseConverter.INSTANCE::deviceVoToRpcVo)
                    .forEach(entity -> deviceEntityMap.put(entity.getId(), entity));
        }
        //只留下需要的设备moc
        List<DeviceEntity> deviceEntities = deviceEntityMap.values().parallelStream().filter(bean -> GlobalConstants.DEVICE_MOC_IDS.contains(bean.getMoc())).collect(Collectors.toList());
        return new ArrayList<>(deviceEntities);
        /* Ended by AICoder, pid:q22b6k054cwcd1e141cf0b8c30c00c1d611786ce */
    }

}
