package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Setter
@Getter
@ToString
@ApiModel(description = "收益趋势")
public class GrainTrendQueryBean
{

    @NotBlank(message = "objectId can not be blank")
    @ApiModelProperty("对象id")
    private String objectId;

    @NotBlank(message = "timeGran can not be blank")
    @ApiModelProperty("时间粒度")
    private String timeGran;

    @NotBlank(message = "startTime can not be blank")
    @ApiModelProperty("开始时间")
    private String startTime;

    @NotBlank(message = "endTime can not be blank")
    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("对比年份")
    private String year;

    @ApiModelProperty("站点id")
    private List<String> siteIds;

    @ApiModelProperty("策略类型 0: 低谷 1：平 2：峰 3：尖峰")
    private Integer strategyType;

    @NotBlank(message = "peakStrategy can not be blank")
    @ApiModelProperty("错峰策略启动与否 0-启用，1-未启用")
    private String peakStrategy;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("设备id列表")
    private List<String> deviceIds;
}
