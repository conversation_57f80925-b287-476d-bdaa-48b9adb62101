package com.zte.uedm.battery.service;

import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ Author     ：10260977
 * @ Date       ：16:21 2021/3/4
 * @ Description：太阳能产生负载消耗记录接口类
 * @ Modified By：
 * @ Version: 1.0
 */
public interface PvGenerConsumRecordService
{
    /**
     *
     */
    void pvGenerConsumRecord(String grain, String moc) throws UedmException;

    /**
     * key 太阳能id  value 站点场景(风光油电)阈值
     * @return
     * @throws UedmException
     */
    Map<String, Double> supplyMapSholdValue() throws UedmException;

    /**
     * spu天表汇聚
     * @param grain
     * @throws UedmException
     */
    void spuGenerationRecordDay(String grain, String moc, String smpId) throws UedmException;

    /**
     * spcu天表汇聚
     * @param grain
     * @throws UedmException
     */
    void spcuGenerationRecordDay(String grain, String moc) throws UedmException;
    /**
     * 生成spcu spu粒度表 月表，年表
     * @throws UedmException
     */
    void commonGenerationRecordMonAndYear(String grain, String moc) throws UedmException;
    /**
     * 汇聚太阳能
     * @throws UedmException
     */
    void pvDadaHandle() throws UedmException;

    /**
     * 汇聚方阵 极板
     * @throws UedmException
     */
    void spcuAndSpuDataHandle() throws UedmException;

    /**
     * 获取方阵和极板的关系
     * @param userName
     * @return
     * @throws UedmException
     */
    Map<String, List<String>> getSpuAndSpcuRelationship(String userName) throws UedmException;
}
