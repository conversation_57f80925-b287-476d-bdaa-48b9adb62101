package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 错峰用电策略电池历史数据Bean
 */
@Setter
@Getter
@ToString
public class PeakShiftDetailVo
{
	/**
	 * id
	 */
	private String id;
    /**
     * 任务名称
	 */
	private String name;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建时间
	 */
	private String gmtCreate;
	/**
	 * 文件id
	 */
	private String fileId;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 生效时间
	 */
	private String effectiveDate;
	/**
	 * 失效时间
	 */
	private String expirationDate;
	/**
	 * 任务状态
	 */
	private String status;
	/**
	 * 设备状态统计
	 */
	private DeviceStatusCountBean deviceStatusCount;

	/**
	 * 任务进度
	 */
	private String progress;
	/**
	 * 设备
	 */
	List<DeviceInfoVo> devices;
	/**
	 * 备注
	 */
	private String description;
	/**
	 * devices total
	 */
	private Integer total;

	/**
	 * 模板策略name
	 */
	private String templateStrategyName;

	/**
	 * 模板策略Id
	 */
	private String templateStrategyId;

	/**
	 * 模板策略版本
	 */
	private String version;

	/**
	 * 设备类型：BCUA，CSU5
	 */
	private String deviceType;

	/**
	 * 设备类型名称
	 */
	private String deviceTypeName;
}
