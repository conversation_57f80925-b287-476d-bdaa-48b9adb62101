package com.zte.uedm.battery.controller.batterytest;

import com.zte.uedm.battery.controller.batterytest.vo.BattTestCheckBeforeVo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.BatteryTemporaryTestVo;
import com.zte.uedm.battery.service.BatteryTemporaryTestService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 电池临时测试
 */
@Path("battery-test-temporary")
@Component
@Slf4j
//@Api(value = "battery-test-temporary")
@Api(value = "电池临时测试")
public class BatteryTemporaryTestController
{

    @Autowired
    private BatteryTemporaryTestService batteryTemporaryTestService;

    @POST
    @Path("/start-up")
    @Produces({ MediaType.APPLICATION_JSON })
//    @ApiOperation(value = "start battery temporary test", notes = "start battery temporary test", httpMethod = HttpMethod.POST)
    @ApiOperation(value = "开始临时测试", notes = "开始临时测试", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = 301,message = "参数为空")
    })
    public ResponseBean startBatteryTemporaryTest(List<String> ids, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        log.info("BatteryTemporaryTestController -> start Battery Temporary Test id :{}",ids);
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            List<BatteryTemporaryTestVo> data =  batteryTemporaryTestService.startBatteryTemporaryTest(ids, serviceBean);
            int total = data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestController-> Battery Temporary Test fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    @POST
    @Path("/check-soc")
    @Produces({ MediaType.APPLICATION_JSON })
//    @ApiOperation(value = "start battery temporary test", notes = "start battery temporary test", httpMethod = HttpMethod.POST)
    @ApiOperation(value = "校验soc", notes = "校验soc", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = 301,message = "参数为空")
    })
    public ResponseBean checkBatterySoc(List<String> ids, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        log.info("checkBatterySoc -> start Battery Temporary Test id :{}",ids);
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            List<BattTestCheckBeforeVo> data =  batteryTemporaryTestService.checkBatterySoc(ids, serviceBean);
            int total = data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestController-> Battery Temporary Test fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    @POST
    @Path("/battPack-start-up")
    @Produces({ MediaType.APPLICATION_JSON })
//    @ApiOperation(value = "start battery temporary test", notes = "start battery temporary test", httpMethod = HttpMethod.POST)
    @ApiOperation(value = "设置BCUA放电终止soc", notes = "设置BCUA放电终止soc", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = 301,message = "参数为空")
    })
    public ResponseBean startBatterySetSoc(List<String> ids, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        log.info("BatteryTemporaryTestController -> start Battery Temporary Test id :{}",ids);
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            List<BatteryTemporaryTestVo> data =  batteryTemporaryTestService.startBatterySetSoc(ids, serviceBean);
            int total = data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestController-> Battery Temporary Test fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    @GET
    @Path("/query")
    @Produces({ MediaType.APPLICATION_JSON })
//    @ApiOperation(value = "query battery temporary test command result", notes = "query battery temporary test command result", httpMethod = HttpMethod.GET)
    @ApiOperation(value = "查询电池临时测试结果", notes = "查询电池临时测试结果", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -200,message = "远程调用失败")
    })
    public ResponseBean queryTemporaryTestCommandResult(@QueryParam("id") String id, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        log.info("BatteryTemporaryTestController -> start query battery temporary test result");
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            List<BatteryTemporaryTestVo> data =  batteryTemporaryTestService.queryTemporaryTestCommandResult(serviceBean, id);
            int total = data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestController-> query battery temporary test result fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

}
