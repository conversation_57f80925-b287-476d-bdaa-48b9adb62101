package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BackupPowerConfigPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLException;
import java.util.List;

@Mapper
public interface BackupPowerConfigMapper
{
    /**
     * 电池备电展示项查询
     * @param userName
     * @return
     * @throws UedmException
     */
    List<BackupPowerConfigPo> selectBackupPowerConfig(@Param("userName") String userName) throws SQLException;

    void insertBackupPowerConfigByBeans(List<BackupPowerConfigPo> list) throws SQLException;

    Integer updateBackupPowerConfigByBeans(List<BackupPowerConfigPo> list) throws SQLException;

    Integer updateIdNameByBeans(List<BackupPowerConfigPo> list) throws SQLException;

    int deleteBackupPowerConfigByUserName(String userName) throws UedmException;
}
