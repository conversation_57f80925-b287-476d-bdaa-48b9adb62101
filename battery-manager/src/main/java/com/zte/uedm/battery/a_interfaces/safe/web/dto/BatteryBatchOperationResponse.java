package com.zte.uedm.battery.a_interfaces.safe.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class BatteryBatchOperationResponse {

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 成功的数量
     */
    private Integer successNum;

    /**
     * 失败的数量
     */
    private Integer failedNum;

    /**
     * 失败项详情
     */
    private List<BatteryBatchOperationItem> failedItemList;
}
