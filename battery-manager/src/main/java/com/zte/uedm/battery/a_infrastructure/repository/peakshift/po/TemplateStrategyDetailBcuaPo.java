package com.zte.uedm.battery.a_infrastructure.repository.peakshift.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Bcua设备模板详情PO，与数据库一一对应
 */

@Getter
@Setter
@ToString
public class TemplateStrategyDetailBcuaPo extends TemplateStrategyDetailCommonPo
{
    /**
     * 时间粒度(逗号分割的字符串)
     */
    private String timeGran;
    /**
     * 节假日日期间隔
     */
    private String holidayTimeGran;

}
