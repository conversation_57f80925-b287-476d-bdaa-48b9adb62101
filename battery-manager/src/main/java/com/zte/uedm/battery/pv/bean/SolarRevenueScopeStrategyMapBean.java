package com.zte.uedm.battery.pv.bean;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/16
 **/
@Setter
@Getter
@ToString
@ApiModel(description = "太阳能收益计算-太阳能和市电电价策略Map")
public class SolarRevenueScopeStrategyMapBean {
    /**
     * 太阳能策略Map
     */
    Map<String, SolarRevenueScopeStrategyBean> solarRevenueScopeStrategyBeanMap;

    /**
     * 市电策略Map
     */
    Map<String, SolarRevenueScopeStrategyBean> gridRevenueScopeStrategyBeanMap;
}
