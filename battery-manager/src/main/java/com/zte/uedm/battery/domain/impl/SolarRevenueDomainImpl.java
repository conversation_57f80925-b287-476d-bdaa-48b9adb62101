package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.domain.SolarRevenueDomain;
import com.zte.uedm.battery.pv.dto.SolarRevenueUpdateStrategyDto;
import com.zte.uedm.battery.pv.mapper.SolarRevenueMapper;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 升级任务获取策略实现类
 * @Date 12/10/23 9:05 PM
 * @Version 1.0
 */
@Service
@Slf4j
public class SolarRevenueDomainImpl implements SolarRevenueDomain {
    @Autowired
    private SolarRevenueMapper solarRevenueMapper;

    @Override
    public void updateRateAndTieredFor(List<String> pvList, Integer tiered, Integer rateType, String grain, String currentDate, String effectiveDate) throws UedmException {
        log.info("SolarRevenueDomainImpl updateRateAndTieredFor monthly or yearly table with pvList length is {}, grain is {}, effectiveTime is {}", pvList.size(), grain, effectiveDate);
        try {
            BatchUtils.doInBatch(3000, pvList, (list) -> {
                try {
                    log.info("SolarRevenueDomainImpl updateRateAndTieredFor monthly or yearly table with length is {}", list.size());
                    solarRevenueMapper.updateRateAndTieredFor(list, tiered, rateType, grain, currentDate, effectiveDate);
                } catch (Exception e) {
                    log.error(" SolarRevenueDomainImpl updateRateAndTieredFor monthly or yearly table error.",e);
                    throw new RuntimeException("SolarRevenueDomainImpl updateRateAndTieredFor monthly or yearly table error.");
                }
            });
        }catch (Exception e){
            log.error("Anomalies in updating the tier and billing mode of monthly or yearly tables.", e);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }

    @Override
    public void updateStrategyAndTieredFor(SolarRevenueUpdateStrategyDto dto, String grain, String pvId, String effectiveTime, String expirationTime) throws UedmException {
        try {
            solarRevenueMapper.updateStrategyAndTieredFor(dto, grain, pvId, effectiveTime, expirationTime);
        } catch (Exception e) {
            log.error("SolarRevenueDomainImpl updateStrategyAndTieredFor {}. Anomalies in updating the tiered and billing mode of hourly or daily tables.", grain);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }

    @Override
    public void updateStrategyAndTieredForList(List<SolarRevenueUpdateStrategyDto> dtoList, String grain, List<String> pvIdList, String effectiveTime, String expirationTime) throws UedmException {
        log.info("SolarRevenueDomainImpl updateStrategyAndTieredForList hourly or daily table with dtoList length is {},  grain is {}, effectiveTime is {}, expirationTime is {}", dtoList.size(), grain, effectiveTime, expirationTime);
        try {
            BatchUtils.doInBatch(3000, pvIdList, (list)->{
                try {
                    log.info("SolarRevenueDomainImpl updateStrategyAndTieredForList monthly or yearly table with pvId length is {}, ", list.size());
                    solarRevenueMapper.updateStrategyAndTieredForList(dtoList, grain, list, effectiveTime, expirationTime);
                } catch (Exception e) {
                    log.error(" SolarRevenueDomainImpl updateStrategyAndTieredForList hourly or daily table error.", e);
                    throw new RuntimeException(" SolarRevenueDomainImpl updateStrategyAndTieredForList hourly or daily table error.",e);
                }
            });
        }catch (Exception e){
            log.error("Anomalies in updating the strategy or tiered or billing mode of hourly or daily tables. ",e);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }

}
