package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/09/07
 */
@Data
@ApiModel(description = "氛围策略")
public class ScopeTreeQueryBean {
    /**
     * 范围策略ID，若编辑则传策略ID，若新增则不传
     */
    @ApiModelProperty(value = "范围策略标识")
    private String scopeStrategyId;

    /**
     * 当前节点ID
     */
    @ApiModelProperty(value = "当前节点标识")
    private String currentId;

    /**
     * 能源类型
     */
    @ApiModelProperty(value = "能源类型")
    private String energyType;

    /**
     * 节点名称，支持模糊查询
     */
    @ApiModelProperty(value = "节点名称")
    private String name;

}
