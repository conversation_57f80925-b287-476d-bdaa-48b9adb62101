package com.zte.uedm.battery.a_infrastructure.safe.enums;

public enum BatteryTrackOverviewEnums
{
    BATTERY_NAME("batteryName","{\"zh_CN\":\"电池\",\"en_US\":\"Battery\"}",1,true,true),
    REAL_GROUP_NAME("realGroupName","{\"zh_CN\":\"区域\",\"en_US\":\"Area\"}",2,true,true),
    SITE("site","{\"zh_CN\":\"站点\",\"en_US\":\"Site\"}",3,true,true),
    FORTIFICATION_STATE("fortificationState","{\"zh_CN\":\"设防状态\",\"en_US\":\"Fortification Status\"}",4,true,false),
    COMMUNICATION_STATUS("communicationStatus","{\"zh_CN\":\"通讯状态\",\"en_US\":\"Comm.Status\"}",5,true,false),
    LOCK_STATUS("lockStatus","{\"zh_CN\":\"锁死状态\",\"en_US\":\"Locked Status\"}",6,true,false),
    MOTION_STATE("motionState","{\"zh_CN\":\"运动状态\",\"en_US\":\"Motion Status\"}",7,true,false),
    THEFT_STATUS("theftStatus","{\"zh_CN\":\"位置状态\",\"en_US\":\"Location Status\"}",8,true,false),
    LATITUDE("latitude","{\"zh_CN\":\"纬度\",\"en_US\":\"Latitude\"}",9,true,false),
    LONGITUDE("longitude","{\"zh_CN\":\"经度\",\"en_US\":\"Longitude\"}",10,true,false),
    ARISE_TIME("ariseTime","{\"zh_CN\":\"位置更新时间\",\"en_US\":\"Re-location Time\"}",11,true,false),
    SITE_LEVEL("siteLevel","{\"zh_CN\":\"站点等级\",\"en_US\":\"Site Level\"}",12,true,false);
    private String id;
    private String name;
    private Boolean defaultEnable;
    private Integer defaultIndex;
    private Boolean defaultFixed;
    BatteryTrackOverviewEnums(String id, String name, Integer defaultIndex, Boolean defaultEnable, Boolean defaultFixed)
    {
        this.id = id;
        this.name = name;
        this.defaultIndex = defaultIndex;
        this.defaultEnable = defaultEnable;
        this.defaultFixed = defaultFixed;
    }

    public String getId() {
        return id;
    }
    public String getName() {
        return name;
    }
    public Integer getDefaultIndex() {
        return defaultIndex;
    }
    public Boolean getDefaultEnable() {
        return defaultEnable;
    }
    public Boolean getDefaultFixed() {
        return defaultFixed;
    }

}
