package com.zte.uedm.battery.opti.domain.utils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public  class MapUtils {
    //将map的大驼峰key转成下划线key
    public static Map<String, String> transforMapKey(Map<String, String> map) {
        Map<String,String> newMap  = new HashMap<String,String>();
        Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
        Map.Entry<String, String> entry;
        while(iterator.hasNext()){
            entry  = iterator.next();
            if(null != entry.getKey() && entry.getKey() instanceof String){
                newMap.put(humpToUnderline(entry.getKey()), entry.getValue());
            }
            iterator.remove();
        }
        return newMap;
    }

    public static String humpToUnderline(String camelString){
        if(null == camelString || "".equals(camelString)) return camelString;
        camelString = String.valueOf(camelString.charAt(0)).toUpperCase().concat(camelString.substring(1));

        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("[A-Z]([a-z\\d]+)?");
        Matcher matcher = pattern.matcher(camelString);
        while (matcher.find()) {
            String word = matcher.group();
            sb.append(word.toLowerCase());
            sb.append(matcher.end() == camelString.length() ? "" : "_");
        }
        String string = sb.toString();
        //释放
        sb.delete(0,sb.length());
        return string;
    }
}
