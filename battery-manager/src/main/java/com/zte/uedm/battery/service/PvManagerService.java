package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.pv.*;
import com.zte.uedm.common.exception.UedmException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PvManagerService {

    /**
     * 太阳能供电系统异常情况展示
     *
     * @param positions
     * @param siteLevels
     * @param supplyScenes
     * @param startTime
     * @param endTime
     * @return
     * @throws UedmException
     */
    PageInfo<PvExceptionRecordShowBean> getExceptionHistory(List<String> positions, List<String> siteLevels, List<String> supplyScenes,
                                                            String userName, String startTime, String endTime, Integer pageNo, Integer pageSize, String languageOption) throws UedmException;

    /**
     * 查询pv异常历史数据-根据pvId和时间范围
     *
     * @param pvId
     * @param beginTime
     * @param endTime
     * @param languageOption
     * @return
     * @throws UedmException
     */
    PvDetailResponseBean getPvGenerConsumRecordBeansByPvIdandTimeRange(String pvId, String beginTime, String endTime, String id,
                                                                       Integer pageNo, Integer pageSize, String languageOption) throws UedmException;

    /**
     * 查询趋势数据-根据pvId和时间范围
     *
     * @param pvId
     * @param beginTime
     * @param endTime
     * @param languageOption
     * @return
     * @throws UedmException
     */
    List<PvGenerConsumRecordBean> getPvAnalysisDetailByCondition(String pvId, String beginTime, String endTime, String languageOption) throws UedmException;

    /**
     * 清除告警
     *
     * @param pvAlarmCleanBean
     */
    void alarmClean(PvAlarmCleanBean pvAlarmCleanBean) throws UedmException;

    /**
     * 概览pv待维护列表
     *
     * @param positions
     * @param siteLevels
     * @param supplyScenes
     * @param pageNo
     * @param pageSize
     * @param languageOption
     * @return
     * @throws UedmException
     */
    PvOverviewShowBean getPvListStateNotEnd(List<String> positions, List<String> siteLevels, List<String> supplyScenes,
                                            String userName, Integer pageNo, Integer pageSize, String languageOption) throws UedmException;
}
