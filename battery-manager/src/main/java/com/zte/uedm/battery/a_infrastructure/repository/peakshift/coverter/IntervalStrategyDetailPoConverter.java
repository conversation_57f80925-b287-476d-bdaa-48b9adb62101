package com.zte.uedm.battery.a_infrastructure.repository.peakshift.coverter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.IntervalStrategyDetailEntity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.IntervalStrategyDetailPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:50ba6898fee74ac9b2afecea6ffea215 */
@Mapper
public interface IntervalStrategyDetailPoConverter {
    IntervalStrategyDetailPoConverter INSTANCE = Mappers.getMapper(IntervalStrategyDetailPoConverter.class);

    @Mappings({})
    IntervalStrategyDetailEntity intervalStrategyDetailPoToIntervalStrategyDetailEntity(IntervalStrategyDetailPo intervalStrategyDetailPo);

    List<IntervalStrategyDetailEntity> listIntervalStrategyDetailPoToIntervalStrategyDetailEntity(List<IntervalStrategyDetailPo> poList);

    @Mappings({})
    IntervalStrategyDetailPo intervalStrategyDetailEntityToIntervalStrategyDetailPo(IntervalStrategyDetailEntity intervalStrategyDetailEntity);

    @Mappings({})
    List<IntervalStrategyDetailPo> listIntervalStrategyDetailEntityToIntervalStrategyDetailPo(List<IntervalStrategyDetailEntity> intervalStrategyDetailEntities);
}
/* Ended by AICoder, pid:50ba6898fee74ac9b2afecea6ffea215 */
