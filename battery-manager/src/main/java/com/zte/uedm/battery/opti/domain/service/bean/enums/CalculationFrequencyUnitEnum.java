package com.zte.uedm.battery.opti.domain.service.bean.enums;

public enum CalculationFrequencyUnitEnum
{
    /**
     * 分钟
     */
    FREQUENCY_UNIT_MIN("min"),

    /**
     * 小时
     */
    FREQUENCY_UNIT_H("h"),

    /**
     * 天
     */
    FREQUENCY_UNIT_D("d");
    private String id;

    public String getId()
    {
        return id;
    }

    CalculationFrequencyUnitEnum(String id)
    {
        this.id = id;
    }
}
