package com.zte.uedm.battery.util;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;

/**
 * 返回response bean的工具类
 * 
 * <AUTHOR>
 *
 */
public class ResponseBeanUtils
{
    private ResponseBeanUtils()
    {

    }

    public static ResponseBean getParameterBlankResponseBean()
    {
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(ParameterExceptionEnum.BLANK.getCode());
        responseBean.setMessage(ParameterExceptionEnum.BLANK.getDesc());
        return responseBean;
    }

    public static ResponseBean getResponseBeanByUedmException(UedmException e)
    {
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(e.getErrorId());
        responseBean.setMessage(e.getMessage());
        return responseBean;
    }

    public static ResponseBean getResponseBeanByUedmException(ParseException e)
    {
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(e.getErrorOffset());
        responseBean.setMessage(e.getMessage());
        return responseBean;
    }

    public static ResponseBean getNormalResponseBean(Integer code, Object data, Integer total)
    {
        ResponseBean responseBean = new ResponseBean();
        if (code != null)
        {
            responseBean.setCode(code);
        }
        if (data != null)
        {
            responseBean.setData(data);
        }
        if (total != null)
        {
            responseBean.setTotal(total);
        }
        return responseBean;
    }

    public static ResponseBean getRemoteResponseBean(Integer code, String message)
    {
        ResponseBean responseBean = new ResponseBean();
        if (code != null)
        {
            responseBean.setCode(code);
        }
        if (message != null)
        {
            responseBean.setMessage(message);
        }
        return responseBean;
    }

    public static ResponseBean getRemoteResponseBean(Integer code, String error, String message, String data, Integer total) {
        ResponseBean responseBean = new ResponseBean();
        if (code != null) {
            responseBean.setCode(code);
        }
        if (error != null) {
            responseBean.setError(error);
        }
        if (message != null) {
            responseBean.setMessage(message);
        }
        if (data != null) {
            responseBean.setData(data);
        }
        if (total != null) {
            responseBean.setTotal(total);
        }
        return responseBean;
    }

    public static ResponseBean getResponseBeanByException(Exception e)
    {
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(-1);
        responseBean.setMessage(e.getMessage());
        return responseBean;
    }

    public static ResponseBean getNormalResponseBean(Integer code, String error, String message, Object data, Integer total)
    {
        ResponseBean responseBean = new ResponseBean();
        if (code != null)
        {
            responseBean.setCode(code);
        }
        if (StringUtils.isNotEmpty(error))
        {
            responseBean.setError(error);
        }
        if (StringUtils.isNotEmpty(message))
        {
            responseBean.setMessage(message);
        }
        if (data != null)
        {
            responseBean.setData(data);
        }
        if (total != null)
        {
            responseBean.setTotal(total);
        }
        return responseBean;
    }

    public static ResponseBean getCheckParamResponseBean(Integer code, String error, String message)
    {
        ResponseBean responseBean = new ResponseBean();
        if (code != null)
        {
            responseBean.setCode(code);
        }
        if (StringUtils.isNotEmpty(error))
        {
            responseBean.setError(error);
        }
        if (StringUtils.isNotEmpty(message))
        {
            responseBean.setMessage(message);
        }
        return responseBean;
    }


    public static ResponseBean getResponseBeanCheckParamByUedmException(UedmException e)
    {
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(e.getErrorId());
        responseBean.setMessage(e.getErrorDesc());
        responseBean.setError(e.getErrorData());
        return responseBean;
    }

}