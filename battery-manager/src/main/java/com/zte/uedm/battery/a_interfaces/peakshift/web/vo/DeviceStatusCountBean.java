package com.zte.uedm.battery.a_interfaces.peakshift.web.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:t7444s3cf946c6e1409909670019d71f93819246 */
/**
 * 下发任务设备状态统计
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceStatusCountBean
{
    /**
     * 未下发
     */
    private Integer pending = 0;

    /**
     * 下发中
     */
    @JsonProperty("in_progress")
    private Integer progress = 0;

    /**
     * 成功
     */
    private Integer success = 0;

    /**
     * 失败
     */
    private Integer fail = 0;
}
/* Ended by AICoder, pid:t7444s3cf946c6e1409909670019d71f93819246 */