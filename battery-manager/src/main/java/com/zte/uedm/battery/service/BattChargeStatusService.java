package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

public interface BattChargeStatusService
{
    /**
     * 查询电池充放电状态的可选值
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> selectLevels(ServiceBaseInfoBean serviceBean) throws UedmException;

}
