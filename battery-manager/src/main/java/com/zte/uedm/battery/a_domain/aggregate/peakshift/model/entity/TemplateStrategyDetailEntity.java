package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import com.zte.uedm.battery.bean.peak.TemplateDetailBaseDto;
import com.zte.uedm.battery.bean.peak.TemplateHolidayDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 设备模板策略详情Bo，对应详情所需数据格式
 */

@Getter
@Setter
@ToString
public class TemplateStrategyDetailEntity
{
    /**
     * 主键
     */
    private String id;
    /**
     * 策略模板名称
     */
    private String name;
    /**
     * BCUA，CSU5
     */
    private String deviceType;
    /**
     * web, excel
     */
    private String source;
    /**
     * 季节策略ID
     */
    private String seasonStrategyId;
    /**
     * 季节策略名称（web配置时候专用），Excel导入的时候为null
     */
    private String seasonStrategyName;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 周期模式 （0：日模式 1：周模式 2：月模式 3：无周期模式）
     */
    private String mode;
    /**
     * 是否周末错峰
     */
    private Boolean weekendFlag;
    /**
     * 版本
     */
    private String version;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 创建用户
     */
    private String userCreate;
    /**
     * 更新时间
     */
    private String gmtModified;
    /**
     * 修改用户
     */
    private String userModified;
    /**
     * 周期详情
     */
    private List<TemplateDetailBaseDto> detail;
    /**
     * 节假日模式详情
     */
    private List<TemplateHolidayDto> holiday;

    public String getVersion()
    {
        return StringUtils.isNotBlank(version) && version.startsWith("V") ? version : "V" + version;
    }
}
