/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BackupPowerOverviewStatisticsDimEnums
 * 文件作者：00248587
 * 开发时间：2023/3/7
 */
package com.zte.uedm.battery.enums.backuppower;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public enum BackupPowerOverviewStatisticsDimEnums 
{
    TOTAL("total", "{\"en_US\":\"Total\",\"zh_CN\":\"总数\"}", 1),
    NORMAL("normal", "{\"en_US\":\"Normal\",\"zh_CN\":\"正常\"}", 2),
    DEFICIENCY("deficiency", "{\"en_US\":\"Insufficient\",\"zh_CN\":\"不足\"}", 3),
    UNEVALUATE("unEvaluate", "{\"en_US\":\"UnEvaluate\",\"zh_CN\":\"无法评估\"}", 4);

    private String id;
    private String name;
    private Integer sequence;

    public String getId() {
        return id;
    }
    public String getName() {
        return name;
    }
    public Integer getSequence() {
        return sequence;
    }

    BackupPowerOverviewStatisticsDimEnums(String id, String name, Integer sequence)
    {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
    }

    /**
     * 根据id获取名称
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BackupPowerOverviewStatisticsDimEnums[] arr = BackupPowerOverviewStatisticsDimEnums.values();
        for(BackupPowerOverviewStatisticsDimEnums enu : arr)
        {
            if (enu.getId().equals(id))
            {
                return enu.getName();
            }
        }
        return "";
    }

    public static List<IdNameBean> getAllIdNameBySequence()
    {
        List<IdNameBean> statusLevels = new ArrayList<>();

        BackupPowerOverviewStatisticsDimEnums[] arr = BackupPowerOverviewStatisticsDimEnums.values();
        List<BackupPowerOverviewStatisticsDimEnums> list = Arrays.asList(arr);
        list.stream().sorted(Comparator.comparing(BackupPowerOverviewStatisticsDimEnums::getSequence));

        for(BackupPowerOverviewStatisticsDimEnums status: list)
        {
            statusLevels.add(new IdNameBean(status.getId(), status.getName()));
        }
        return statusLevels;
    }


    /**
     * 获取全部id
     * @return
     */
    public static List<String> getAllId()
    {
        List<String> result =new ArrayList<>();
        BackupPowerOverviewStatisticsDimEnums[] values = BackupPowerOverviewStatisticsDimEnums.values();
        for(BackupPowerOverviewStatisticsDimEnums enu : values)
        {
            result.add(enu.getId());
        }
        return result;
    }
}
