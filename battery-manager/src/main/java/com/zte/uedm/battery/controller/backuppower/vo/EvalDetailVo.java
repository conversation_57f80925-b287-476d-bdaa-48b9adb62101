package com.zte.uedm.battery.controller.backuppower.vo;

import com.zte.uedm.battery.bean.BattBackupPowerEvalPojo;
import com.zte.uedm.battery.enums.backuppower.BackupEvalExportColumnEnum;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.consts.TimeFormatConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;

@Getter
@Setter
@ToString
public class EvalDetailVo
{
    private String id;
    private String name;
    private String pathNames;
    private IdNameBean status;
    private IdNameBean preStatus;
    private String statusDetail;
    private String preStatusDetail;
    private SurplusDurationVo surplusDischargeDuration;
    private DurationVo backupPowerDuration;
    private DurationVo thresholdDuration;
    private String applicationScene;
    private String gmtModified;
    private String statusChangeTime;
    private String evalTime;
    private String manufacture;
    private String brand;
    private String series;
    private String model;

    public EvalDetailVo(){}

    /**
     * pojo转vo
     * @param battBackupPowerEvalPojo
     */
    public EvalDetailVo(BattBackupPowerEvalPojo battBackupPowerEvalPojo)
    {
        this.id = battBackupPowerEvalPojo.getId();
        this.name = battBackupPowerEvalPojo.getName();
        this.pathNames = battBackupPowerEvalPojo.getPathNames();
        this.gmtModified =
                new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_DAY).format(battBackupPowerEvalPojo.getGmtModified());
        this.evalTime = battBackupPowerEvalPojo.getEvalTime();
        this.statusChangeTime = battBackupPowerEvalPojo.getStatusChangeTime();
    }

}
