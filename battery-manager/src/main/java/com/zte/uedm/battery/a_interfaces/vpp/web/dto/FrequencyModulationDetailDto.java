package com.zte.uedm.battery.a_interfaces.vpp.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:v1d603d49fq895b14591089f40f03e28fdf35db1 */
/**
 * 调频监控详情
 */
@Setter
@Getter
@ToString
public class FrequencyModulationDetailDto {

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;
}
/* Ended by AICoder, pid:v1d603d49fq895b14591089f40f03e28fdf35db1 */
