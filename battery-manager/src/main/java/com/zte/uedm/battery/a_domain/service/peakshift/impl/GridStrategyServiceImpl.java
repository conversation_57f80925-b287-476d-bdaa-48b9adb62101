package com.zte.uedm.battery.a_domain.service.peakshift.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.*;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.GridStrategyRepository;
import com.zte.uedm.battery.a_domain.service.peakshift.GridStrategyService;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.common.configuration.enums.EnergyTypeEnum;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ComparatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("gridStrategyServiceImplNew")
@Slf4j
public class GridStrategyServiceImpl implements GridStrategyService {


    @Autowired
    private GridStrategyRepository gridStrategyRepository;

    @Autowired
    private JsonService jsonService;

    private static final Integer DEL_STATUS = 0;

    private static final Map<String, String> columnMap = new HashMap<>();

    private static final String YEAR = "2000-";

    //节假日模式
    private static final Integer MODE_HOLIDAY = 3;

    //周模式
    private static final Integer MODE_WEEK = 1;

    //月模式
    private static final Integer MODE_MONTH = 2;

    private static final String LANGUAGE_CN = "zh-CN";

    private static final String LANGUAGE_US = "en-US";

    private static final String STATUS_SUCCESS = "success";

    private static final String STATUS_PENDING = "pending";

    private static final String STATUS_IN_PROGRESS = "in_progress";

    private static final String STATUS_FAIL = "fail";

    static
    {
        columnMap.put("effectiveTime","effective_time");
        columnMap.put("expirationTime","expiration_time");
        columnMap.put("status","status");
    }



    /**
     * 获取多个策略融合之后的信息Bean，并排序分页
     *
     * @param sortBy 排序的字段
     * @param order  顺序，desc、asc
     * @return 分页之后的结果
     * @throws UedmException
     */
    @Override
    public List<StrategyCombinationEntity> getStrategyCombination(String sortBy, String order, String languageOption)
            throws UedmException
    {
        try
        {
            //查询的season_strategy需要不为已失效的策略，并且interval_strategy不查节假日策略
            String energyType = EnergyTypeEnum.GRID.getEnergyType();
            List<StrategyCombinationEntity> allBeans = gridStrategyRepository.getStrategyCombination(energyType);

            log.info("getStrategyCombination: allBeans = {}", jsonService.objectToJson(allBeans));

            for (StrategyCombinationEntity bean : allBeans)
            {
                String seasonStrategyName = getSeasonStrategyName(bean.getSeasonStrategyId(), languageOption);
                bean.setSeasonStrategyName(seasonStrategyName);
            }

            log.info("all strategy effective or to be effective :{}", allBeans.size());

            //1:升序，0:降序
            //默认排序，范围策略名称和生效时间
            ComparatorUtil cmp = new ComparatorUtil(new String[] { "scopeStrategyName", "effectiveTime" }, 1);
            if (StringUtils.isNotBlank(sortBy))
            {
                if (SortEnum.getDescSortID().equals(order))
                {
                    cmp = new ComparatorUtil(new String[] { sortBy }, 0);
                }
                else
                {
                    cmp = new ComparatorUtil(new String[] { sortBy }, 1);
                }
            }
            allBeans.sort(cmp);

            return allBeans;
        }
        catch (Exception e)
        {
            log.error("getStrategyCombination failed!", e);
            throw new UedmException(-1, e.getMessage());
        }

    }

    //组装策略名称，电网名称+生效日期+开始时间+结束时间
    @Override
    public String getSeasonStrategyName(String seasonStrategyId, String languageOption)
    {
        try
        {
            SeasonStrategyEntity seasonStrategyBean = gridStrategyRepository.queryById(seasonStrategyId);
            StringBuilder sb = new StringBuilder();
            ScopeStrategyEntity scopeStrategyResponseBean = gridStrategyRepository.selectById(
                    seasonStrategyBean.getScopeStrategyId());
            sb.append(scopeStrategyResponseBean.getName()).append(" > ");
            if (StringUtils.contains(languageOption, "en"))
            {
                //en-US
                sb.append("Effective Date ").append(seasonStrategyBean.getEffectiveTime());
                if (seasonStrategyBean.getExpirationTime() != null)
                {
                    sb.append(" - ").append(seasonStrategyBean.getExpirationTime());
                }
            }
            else
            {
                //zh-CN
                sb.append("生效日期 ").append(seasonStrategyBean.getEffectiveTime());
                if (seasonStrategyBean.getExpirationTime() != null)
                {
                    sb.append(" - ").append(seasonStrategyBean.getExpirationTime());
                }
            }

            log.debug("Season Strategy Name:{}", sb);
            return sb.toString();
        }
        catch (Exception e)
        {
            log.error("getSeasonStrategyName failed!", e);
            return null;
        }

    }

    @Override
    public SeasonStrategyForTemplateEntity queryOneSeasonStrategyFotTemplate(String seasonStrategyId, String languageOption)
            throws UedmException
    {
        try
        {
            SeasonStrategyForTemplateEntity bean = new SeasonStrategyForTemplateEntity();
            String seasonStrategyName = getSeasonStrategyName(seasonStrategyId, languageOption);

            bean.setSeasonStrategyId(seasonStrategyId);
            bean.setSeasonStrategyName(seasonStrategyName);

            //获取合适的seasonStrategyId对应的intervalStrategy
            List<IntervalStrategyEntity> intervalStrategyBeans = gridStrategyRepository.selectIntervalStrategyBySeasonId(
                    seasonStrategyId);
            //holidaySameUuid为空
            List<IntervalStrategyEntity> details = intervalStrategyBeans.stream()
                    .filter(x -> x.getHolidaySameUuid() == null).collect(Collectors.toList());

            //holidaySameUuid不为空
            Map<String, List<IntervalStrategyEntity>> holidayMap = intervalStrategyBeans.stream()
                    .filter(x -> x.getHolidaySameUuid() != null)
                    .collect(Collectors.groupingBy(IntervalStrategyEntity::getHolidaySameUuid));

            //获取合适的intervalStrategyId对应的intervalStrategyDetail
            List<String> intervalStrategyIds = intervalStrategyBeans.stream().map(x -> x.getId())
                    .collect(Collectors.toList());
            List<IntervalStrategyDetailEntity> detailBeans = gridStrategyRepository.selectIntervalStrategyDetailByIntervalStrategyIds(
                    intervalStrategyIds);
            Map<String, List<IntervalStrategyDetailEntity>> detailMap = detailBeans.stream()
                    .collect(Collectors.groupingBy(IntervalStrategyDetailEntity::getIntervalStrategyId));

            handle(details, detailMap, holidayMap, bean);

            log.debug("query one season strategy bean: {}", bean);
            return bean;
        }
        catch (Exception e)
        {
            log.error("queryOneSeasonStrategyFotTemplate failed!", e);
            throw new UedmException(-1, e.getMessage());
        }

    }

    public void handle(List<IntervalStrategyEntity> details, Map<String, List<IntervalStrategyDetailEntity>> detailMap,
                        Map<String, List<IntervalStrategyEntity>> holidayMap, SeasonStrategyForTemplateEntity bean)
    {
        String mode = "";
        List<TemplateDetailVo> detail = new ArrayList<>();
        List<TemplateHolidayVo> holiday = new ArrayList<>();
        for (IntervalStrategyEntity intervalStrategyBean : details)
        {
            Integer modeInt = intervalStrategyBean.getMode();
            String intervalStrategyId = intervalStrategyBean.getId();

            log.debug(" mode :{},intervalStrategyId :{}", modeInt, intervalStrategyId);
            if (MODE_HOLIDAY.equals(modeInt))
            {
                //节假日模式:3
                holidayMode(detailMap, holiday, intervalStrategyBean, intervalStrategyId);
            }
            else if (MODE_MONTH.equals(modeInt))
            {
                //月模式:2
                mode = String.valueOf(modeInt);

                monthMode(detailMap, detail, intervalStrategyBean);
            }
            else if (MODE_WEEK.equals(modeInt))
            {
                //周模式:1
                mode = String.valueOf(modeInt);

                weekMode(detailMap, detail, intervalStrategyBean);
            }
            else
            {
                //日模式:0
                mode = String.valueOf(modeInt);

                dayMode(detailMap, detail, intervalStrategyBean);
            }

        }
        log.info("holiday size() =========={}", holiday.size());
        log.info("holiday =={}", JSON.toJSONString(holiday));
        updateHoliday(detailMap, holiday, holidayMap);
        if (holiday.size() == 0)
        {
            for (TemplateDetailVo vo : detail)
            {
                TemplateHolidayVo holidayVo = new TemplateHolidayVo();
                holidayVo.setDetail(vo.getDetail());
                holiday.add(holidayVo);
            }
        }
        bean.setMode(mode);
        bean.setHoliday(holiday);
        bean.setDetail(detail);
    }

    private void holidayMode(Map<String, List<IntervalStrategyDetailEntity>> detailMap, List<TemplateHolidayVo> holiday,
                             IntervalStrategyEntity intervalStrategyBean, String intervalStrategyId)
    {
        TemplateHolidayVo templateHolidayVo = new TemplateHolidayVo();

        //组装timeGran属性
        List<TemplateTimeGranVo> timeGran = new ArrayList<>();
        TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
        templateTimeGranVo.setRemark(intervalStrategyBean.getRemark());
        templateTimeGranVo.setBegin(intervalStrategyBean.getStartDate());
        templateTimeGranVo.setEnd(intervalStrategyBean.getEndDate());
        timeGran.add(templateTimeGranVo);
        templateHolidayVo.setTimeGran(timeGran);

        //组装detail属性
        List<TemplateTimeGranDetailVo> templateTimeGranDetailVos = new ArrayList<>();
        List<IntervalStrategyDetailEntity> detailBeanList = detailMap.get(intervalStrategyId);
        detailBeanList = (detailBeanList == null) ? new ArrayList<>() : detailBeanList;
        for (IntervalStrategyDetailEntity intervalStrategyDetailBean : detailBeanList)
        {
            //一般节假日模式，只有一个详情
            templateTimeGranDetailVos = JSON.parseArray(intervalStrategyDetailBean.getDetail(),
                    TemplateTimeGranDetailVo.class);
        }

        templateHolidayVo.setDetail(templateTimeGranDetailVos);
        holiday.add(templateHolidayVo);
    }

    public void monthMode(Map<String, List<IntervalStrategyDetailEntity>> detailMap, List<TemplateDetailVo> detail,
                           IntervalStrategyEntity intervalStrategyBean)
    {
        List<IntervalStrategyDetailEntity> detailBeanList = detailMap.get(intervalStrategyBean.getId());

        detailBeanList = (detailBeanList == null) ? new ArrayList<>() : detailBeanList;
        //遍历各个阶段的detail，组装成模板的格式
        for (IntervalStrategyDetailEntity intervalStrategyDetailBean : detailBeanList)
        {
            List<TemplateTimeGranDetailVo> templateTimeGranDetailVos = JSON.parseArray(
                    intervalStrategyDetailBean.getDetail(), TemplateTimeGranDetailVo.class);

            TemplateDetailVo templateDetailVo = new TemplateDetailVo();

            List<Integer> timeGran = new ArrayList<>();

            //月模式的list需要从开始日期遍历到结束日期
            Integer periodStart = intervalStrategyDetailBean.getPeriodStart();
            Integer periodEnd = intervalStrategyDetailBean.getPeriodEnd();
            for (Integer i = periodStart; i <= periodEnd; i++)
            {
                timeGran.add(i);
            }

            templateDetailVo.setDetail(templateTimeGranDetailVos);
            templateDetailVo.setTimeGran(timeGran);
            String startDate = intervalStrategyBean.getStartDate();
            String endDate = intervalStrategyBean.getEndDate();
            templateDetailVo.setDateRange(Arrays.asList(startDate,endDate));
            detail.add(templateDetailVo);
        }
    }

    public void weekMode(Map<String, List<IntervalStrategyDetailEntity>> detailMap, List<TemplateDetailVo> detail,
                          IntervalStrategyEntity intervalStrategyBean)
    {
        List<IntervalStrategyDetailEntity> detailBeanList = detailMap.get(intervalStrategyBean.getId());

        detailBeanList = (detailBeanList == null) ? new ArrayList<>() : detailBeanList;
        //遍历各个阶段的detail，组装成模板的格式
        for (IntervalStrategyDetailEntity intervalStrategyDetailBean : detailBeanList)
        {
            List<TemplateTimeGranDetailVo> templateTimeGranDetailVos = JSON.parseArray(
                    intervalStrategyDetailBean.getDetail(), TemplateTimeGranDetailVo.class);

            TemplateDetailVo templateDetailVo = new TemplateDetailVo();

            //周模式的list已经存为json
            List<Integer> timeGran = JSON.parseArray(intervalStrategyDetailBean.getWeekStr(), Integer.class);

            templateDetailVo.setDetail(templateTimeGranDetailVos);
            templateDetailVo.setTimeGran(timeGran);
            String startDate = intervalStrategyBean.getStartDate();
            String endDate = intervalStrategyBean.getEndDate();
            templateDetailVo.setDateRange(Arrays.asList(startDate,endDate));
            detail.add(templateDetailVo);
        }
    }

    public void dayMode(Map<String, List<IntervalStrategyDetailEntity>> detailMap, List<TemplateDetailVo> detail,
                         IntervalStrategyEntity intervalStrategyBean)
    {
        List<IntervalStrategyDetailEntity> detailBeanList = detailMap.get(intervalStrategyBean.getId());

        detailBeanList = (detailBeanList == null) ? new ArrayList<>() : detailBeanList;
        //遍历各个阶段的detail，组装成模板的格式
        for (IntervalStrategyDetailEntity intervalStrategyDetailBean : detailBeanList)
        {
            List<TemplateTimeGranDetailVo> templateTimeGranDetailVos = JSON.parseArray(
                    intervalStrategyDetailBean.getDetail(), TemplateTimeGranDetailVo.class);

            TemplateDetailVo templateDetailVo = new TemplateDetailVo();
            templateDetailVo.setDetail(templateTimeGranDetailVos);
            String startDate = intervalStrategyBean.getStartDate();
            String endDate = intervalStrategyBean.getEndDate();
            templateDetailVo.setDateRange(Arrays.asList(startDate,endDate));
            detail.add(templateDetailVo);
        }
    }

    public void updateHoliday(Map<String, List<IntervalStrategyDetailEntity>> detailMap, List<TemplateHolidayVo> holiday,
                               Map<String, List<IntervalStrategyEntity>> holidayMap)
    {
        if (holidayMap != null && holidayMap.size() > 0)
        {
            log.info("holidayMap.size() ========={}", holidayMap.size());
            for (String holidaySameUuid : holidayMap.keySet())
            {
                TemplateHolidayVo holidayVo = new TemplateHolidayVo();
                List<IntervalStrategyEntity> beans = holidayMap.get(holidaySameUuid);
                List<TemplateTimeGranVo> timeGran = new ArrayList<>();
                List<TemplateTimeGranDetailVo> detailList = new ArrayList<>();
                for (IntervalStrategyEntity bean : beans)
                {
                    TemplateTimeGranVo vo = new TemplateTimeGranVo();
                    vo.setBegin(bean.getStartDate());
                    vo.setEnd(bean.getEndDate());
                    vo.setRemark(bean.getRemark());
                    timeGran.add(vo);
                    List<IntervalStrategyDetailEntity> intervalStrategyDetailBeans = detailMap.get(bean.getId());
                    if (null != intervalStrategyDetailBeans)
                    {
                        for (IntervalStrategyDetailEntity detailBean : intervalStrategyDetailBeans)
                        {
                            detailList.addAll(JSON.parseArray(detailBean.getDetail(), TemplateTimeGranDetailVo.class));
                        }
                    }
                }
                detailList = detailList.stream().distinct().collect(Collectors.toList());
                holidayVo.setTimeGran(timeGran);
                holidayVo.setDetail(detailList);
                holiday.add(holidayVo);
            }
            log.debug("holiday =={}", JSON.toJSONString(holiday));
        }
    }

}
