package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.PeakShiftDeviceIndexStatusBean;
import com.zte.uedm.battery.bean.BatteryChargeDischargeHistoryResponeBean;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * BCUA智能错峰策略接口
 */
public interface PeakShiftDeviceService {
    /**
     * 查询支持错峰的设备类型
     * @param language 语言
     * @return 设备类型
     * @throws UedmException
     */
    List<IdNameBean> getPeakShiftDeviceType(String language) throws UedmException;

    /**
     * 启用或停用BCUA错峰策略
     * @param deviceEnableDtoList
     * @param serviceBaseInfoBean
     * @throws UedmException
     * @return
     */
    List<String> setPeakShiftDeviceStrategies(List<PeakShiftDeviceEnableDto> deviceEnableDtoList, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 启用或停用全部错峰策略
     * @param allConfigDto
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    List<String> setPeakShiftDeviceStrategies(PeakShiftDeviceAllConfigDto allConfigDto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 查找当前时刻，该设备的当前策略和执行状态
     * @param deviceId 设备ID
     * @return
     * @throws UedmException
     */
    Pair<String,String> getCurrentStrategyAndExecStatus(String deviceId, PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws UedmException;

    /**
     * 错峰设置的设备信息刷新
     * @param deviceId
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PeakShiftDeviceChildBeanVo detail(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 根据设备ID查询详细信息
     * @param deviceId 设备ID
     * @return
     * @throws UedmException
     */
    PeakShiftMonitorDetailVo peakShiftMonitorDetail(String deviceId,String lang) throws UedmException;

    /**
     * 根据逻辑组ID和设备类型查询其下设备ID(RPC接口)
     * @param logicIds
     * @param serviceBaseInfoBean
     * @param deviceType
     * @return
     * @throws UedmException
     */
    PageInfo<String> selectByLogicAndDeviceType(List<String> logicIds, ServiceBaseInfoBean serviceBaseInfoBean,String deviceType) throws UedmException;

    /**
     * 生成错峰履历
     * @param recordPojo
     * @param userName
     * @return
     * @throws UedmException
     */
    int setPeakShiftOperateRecord(PeakShiftOperationRecordPojo recordPojo, String userName) throws UedmException;

    int setPeakShiftDeviceStatus(List<PeakShiftDeviceIndexStatusBean> statusBeanList);

    int setCSUPeakShiftDeviceStatus(List<PeakShiftDeviceIndexStatusBean> statusBeanList);

    /**
     * 发送BCUA端口信息到南向
     * @param port
     * @return
     * @throws UedmException
     */
    boolean sendPortToAllBCUADevices(String port) throws UedmException;


    void dealDevicePeakStrategy(BatteryChargeDischargeHistoryResponeBean deviceStrategyBean ) throws UedmException, com.zte.uedm.basis.exception.UedmException;
}
