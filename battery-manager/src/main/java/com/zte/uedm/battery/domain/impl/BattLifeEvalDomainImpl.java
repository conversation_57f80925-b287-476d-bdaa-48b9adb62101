package com.zte.uedm.battery.domain.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.util.concurrent.AtomicDouble;
import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.api.bean.RcdResponseBean;
import com.zte.uedm.battery.api.service.DataService;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.bean.overview.OverviewLifeInfoBean;
import com.zte.uedm.battery.consts.BattEvalFailedReasonEnum;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.bo.SelectSingleBattEvalHistoryBo;
import com.zte.uedm.battery.domain.BattCfgInfoDomain;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BattOverviewDomain;
import com.zte.uedm.battery.mapper.BattLifeEvalDMapper;
import com.zte.uedm.battery.mapper.BattLifeEvalMMapper;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.consts.GlobalBaseConstants;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BattLifeEvalDomainImpl implements BattLifeEvalDomain {

    @Resource
    private BattLifeEvalDMapper battLifeEvalDMapper;

    @Resource
    private BattLifeEvalMMapper battLifeEvalMMapper;

    @Resource
    private AssetRpcImpl assetRpcImpl;

    @Resource
    private DateTimeService dateTimeService;

    @Resource
    private DataService dataService;

    @Resource
    private BattCfgInfoDomain battCfgInfoDomain;

    @Resource
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private BattOverviewDomain battOverviewDomain;

    @Override
    public void recordEvalByDay(BattLifeEvalDto battLifeEvalDto) {
        BattLifeEvalDBean battLifeEvalDBean = new BattLifeEvalDBean();
        BeanUtils.copyProperties(battLifeEvalDto, battLifeEvalDBean);
        battLifeEvalDBean.setStartDate(battLifeEvalDto.getOpeningDate());
        battLifeEvalDBean.setProductionDate(battLifeEvalDto.getBirthday());
        String currentTime = dateTimeService.getCurrentTime();
        Date formatDate = DateUtils.getDate(currentTime, DateTypeConst.DATE_FORMAT_1);
        String date = Optional.ofNullable(formatDate)
                .map(item -> DateUtils.getStrDateByDateType(item, DateTypeConst.DATE_FORMAT_2))
                .orElse(null);
        battLifeEvalDBean.setEvalTime(date);
        battLifeEvalDBean.setGmtCreate(formatDate);
        battLifeEvalDBean.setGmtModified(formatDate);
        battLifeEvalDBean.setUpdater("admin");
        battLifeEvalDBean.setSource(battLifeEvalDto.getSource());
        battLifeEvalDBean.setLeftCycleTimes(battLifeEvalDto.getLeftCycleTimes());
        battLifeEvalDBean.setSoh(battLifeEvalDto.getSoh());
        battLifeEvalDBean.setRatedUsefulLife(battLifeEvalDto.getRatedUsefulLife());

        QueryWrapper<BattLifeEvalDBean> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", battLifeEvalDto.getId());
        queryWrapper.eq("eval_time", date);

        try {
            BattLifeEvalDBean lifeEvalDBean = battLifeEvalDMapper.selectOne(queryWrapper);

            //更新总览life
            String lifeInfo = jsonService.objectToJson(OverviewLifeInfoBean.buildOverviewBean(battLifeEvalDBean));
            BatteryBaseInfoBean batteryBaseInfoBean = BatteryBaseInfoBean.buildOverviewLifeBean(battLifeEvalDto.getId(), lifeInfo);
            if (Objects.nonNull(lifeEvalDBean)) {
                battLifeEvalDMapper.update(battLifeEvalDBean, queryWrapper);
                battOverviewDomain.updateOverviewEvalResult(Arrays.asList(batteryBaseInfoBean));
                return;
            }
            battLifeEvalDMapper.insert(battLifeEvalDBean);
            battOverviewDomain.updateOverviewEvalResult(Arrays.asList(batteryBaseInfoBean));
        }catch (Exception e){
            log.error("BattLifeEvalDomainImpl recordEvalByDay {} error {}", battLifeEvalDto.getId(), e);
        }
        
    }

    @Override
    public Integer recordEvalByMonth(BattLifeEvalDto battLifeEvalDto) {
        BattLifeEvalMBean battLifeEvalMBean = new BattLifeEvalMBean();
        BeanUtils.copyProperties(battLifeEvalDto, battLifeEvalMBean);
        battLifeEvalMBean.setStartDate(battLifeEvalDto.getOpeningDate());
        battLifeEvalMBean.setProductionDate(battLifeEvalDto.getBirthday());
        String currentTime = dateTimeService.getCurrentTime();
        Date formatDate = DateUtils.getDate(currentTime, DateTypeConst.DATE_FORMAT_1);
        String date = Optional.ofNullable(formatDate)
                .map(item -> DateUtils.getStrDateByDateType(item, DateTypeConst.DATE_FORMAT_3))
                .orElse(null);
        battLifeEvalMBean.setEvalTime(date);
        battLifeEvalMBean.setGmtCreate(formatDate);
        battLifeEvalMBean.setGmtModified(formatDate);
        battLifeEvalMBean.setUpdater("admin");
        battLifeEvalMBean.setSource(battLifeEvalDto.getSource());
        battLifeEvalMBean.setLeftCycleTimes(battLifeEvalDto.getLeftCycleTimes());
        battLifeEvalMBean.setSoh(battLifeEvalDto.getSoh());
        battLifeEvalMBean.setRatedUsefulLife(battLifeEvalDto.getRatedUsefulLife());

        QueryWrapper<BattLifeEvalMBean> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", battLifeEvalDto.getId());
        queryWrapper.eq("eval_time", date);

        BattLifeEvalMBean lifeEvalMBean = battLifeEvalMMapper.selectOne(queryWrapper);
        if (Objects.nonNull(lifeEvalMBean)) {
            return battLifeEvalMMapper.update(battLifeEvalMBean, queryWrapper);
        }
        return battLifeEvalMMapper.insert(battLifeEvalMBean);
    }

    @Override
    public Double calcBattDischargeCap(String moId, String startDate, String endDate, BattLifeEvalDto battLifeEvalDto) throws UedmException {
        List<RcdResponseBean> list = dataService.getRcds(moId, BattConst.RCD_BATT_DISCHARGE, null, endDate,
                new String[]{BattConst.RCD_BATT_DISCHARGE_POINT_CHANGED_SOC, BattConst.RCD_BATT_DISCHARGE_POINT_DURATION, BattConst.RCD_BATT_DISCHARGE_CAP});
        AtomicDouble changedSocTotal = new AtomicDouble(0);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            // 算出总放电容量
            list.forEach(item -> {
                Map<String, Object> valuesMap = item.getValues();
                double dischargeCap = 0d;
                if (org.apache.commons.collections4.MapUtils.isNotEmpty(valuesMap)) {
                    Object changedSOCObj = valuesMap.get(BattConst.RCD_BATT_DISCHARGE_POINT_CHANGED_SOC);
                    Double changedSoc = Optional.ofNullable(changedSOCObj)
                            .map(socObj -> (Double) socObj)
                            .orElse(0D);
                    Object durationObj = valuesMap.get(BattConst.RCD_BATT_DISCHARGE_POINT_DURATION);
                    Double duration = Optional.ofNullable(durationObj)
                            .map(disChgDur -> (Double) disChgDur)
                            .orElse(0D);
                    if (changedSoc > 2 && duration > 10) {
                        Object dischargeCapObj = valuesMap.get(BattConst.RCD_BATT_DISCHARGE_CAP);
                        dischargeCap = Optional.ofNullable(dischargeCapObj)
                                .map(capObj -> (Double) capObj)
                                .orElse(0D);
                        changedSocTotal.addAndGet(dischargeCap);
                    }
                }
            });
            Date firstRecordDate = getFirstRecordDate(list);
            battLifeEvalDto.setFirstRecordDate(firstRecordDate);
        }
        log.info("accumBattDischargeCap: [{}]", changedSocTotal.get());
        return changedSocTotal.get();
    }

    public Date getFirstRecordDate(List<RcdResponseBean> list) {
        return list.stream()
                .map(RcdResponseBean::getStartTime)
                .filter(StringUtils::isNotEmpty)
                .sorted()
                .findFirst()
                .map(item -> DateUtils.getDate(item, DateTypeConst.DATE_FORMAT_1))
                .orElse(null);
    }

    @Override
    public Integer getBattRunDays(String moId, BattLifeEvalDto battLifeEvalDto) throws UedmException {
        Integer result = null;
        // 查询电池启用日期（资产）
        Pair<Date, Date> dateDatePair = getOpeningDate(moId);
        Date openingDate = dateDatePair.getKey();
        Date productionDate = dateDatePair.getValue();
        battLifeEvalDto.setOpeningDate(openingDate);
        battLifeEvalDto.setBirthday(productionDate);

        Date runDays = getRunDays(battLifeEvalDto, moId);
        String date = Optional.ofNullable(runDays)
                .map(DateUtils::getStrDate)
                .orElseThrow(() -> {
                    battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_RUN_DAYS_FAILED.getCode());
                    return new UedmException(-1, "get battery run days error.");
                });
        battLifeEvalDto.setOperatingDaysFrom(date);
        AtomicInteger battRunDays = new AtomicInteger();
        try {
            Integer integer = TimeUtils.differentDays(date, dateTimeService.getCurrentTime());
            battRunDays.set(integer);
        } catch (Exception e) {
            log.error("time parse error", e);
        }
        log.info("end to query battery run days info by moId, response: [{}]", battRunDays.get());
        result = battRunDays.get();
        return result;
    }

    public Pair<Date, Date> getOpeningDate(String moId) {
        Date openingDate = null;
        Date productionDate = null;
        try {
            BatteryAssetBean batteryAssetBean = assetRpcImpl.getAssetByMoId(moId, GlobalBaseConstants.LANGUAGE_OPTION2_CHINESE);
            log.info("end to query asset info by moId, response: [{}]", batteryAssetBean);
            openingDate = Optional.ofNullable(batteryAssetBean)
                    .map(BatteryAssetBean::getStartDate)
                    .map(item -> DateUtils.getDate(item, DateTypeConst.DATE_FORMAT_2))
                    .orElse(null);
            productionDate = Optional.ofNullable(batteryAssetBean)
                    .map(BatteryAssetBean::getProductionDate)
                    .map(item -> DateUtils.getDate(item, DateTypeConst.DATE_FORMAT_2))
                    .orElse(null);
            log.info("get site create date, moId: [{}], openingDate: [{}]", moId, openingDate);
        } catch (Exception e) {
            log.error("get opending date error, moId: [{}]", moId);
        }
        return Pair.of(openingDate,productionDate);
    }

    public String getSiteCreateDate(String moId) {
        String siteCreateDate = null;
        try {
            SiteBean siteBean = configurationManagerRpcImpl.getSiteInfoByMoId(moId);
            siteCreateDate = Optional.ofNullable(siteBean)
                    .map(ResourceBaseBean::getGmtCreate)
                    .orElse(null);
            log.info("get site create date, moId: [{}], siteCreateDate: [{}]", moId, siteCreateDate);
        }
        catch (Exception e){
            log.error("get site create date error, moId: [{}]", moId);
        }
        return siteCreateDate;
    }

    public Date getRunDays(BattLifeEvalDto battLifeEvalDto, String moId) throws UedmException {
        String siteCreateDate = getSiteCreateDate(moId);
        Date runDays = null;
        if (Objects.nonNull(battLifeEvalDto.getOpeningDate())) {
            runDays = battLifeEvalDto.getOpeningDate();
        } else if (Objects.nonNull(battLifeEvalDto.getFirstRecordDate())) {
            runDays = battLifeEvalDto.getFirstRecordDate();
        } else if (StringUtils.isNotEmpty(siteCreateDate)) {
            try {
                runDays = DateUtils.getStringDateStr(siteCreateDate);
                battLifeEvalDto.setSiteCreateDate(runDays);
            } catch (ParseException e) {
                log.error("parse error.", e);
            }
        }
        return runDays;
    }

    @Override
    public Map<String, Integer> getBattLifeMap(List<String> moIds) throws UedmException {

        if (CollectionUtils.isEmpty(moIds)) {
            log.warn("getBattLifeMap: param is empty.");
            return new HashMap<>();
        }

        try {
            List<BattLifeEvalDBean> pojos = new ArrayList<>();
            BatchUtils.doInBatch(10000, moIds, (item) -> {
                List<BattLifeEvalDBean> battLifeEvalDBeans = new ArrayList<>();
                try
                {
                    battLifeEvalDBeans = battLifeEvalDMapper.selectLastEvalsByMoIds(item);
                } catch (Exception e) {
                    log.warn("selectLastEvalsByMoIds failed!", e);
                    throw new RuntimeException();
                }
                pojos.addAll(battLifeEvalDBeans);
            });
            if (CollectionUtils.isNotEmpty(pojos)) {
                Map<String, Integer> moReLifeMap = pojos.stream().filter(bean -> (StringUtils.isNotBlank(bean.getId()) && bean.getLife() != null)).collect(Collectors.toMap(BattLifeEvalDBean::getId, BattLifeEvalDBean::getLife, (old, newValue)->old));
                return moReLifeMap;
            }
        } catch (Exception e) {
            log.error("getBattLifeMap is error", e);
            throw new UedmException(-1, "getBattLifeMap is error.");
        }

        return new HashMap<>();
    }

    @Override
    public Map<String, BattLifeEvalDBean> getBattLifeEvalDayMap(List<String> moIds) {
        if (CollectionUtils.isNotEmpty(moIds)) {

            List<BattLifeEvalDBean> evalDayBeans = new ArrayList<>();
            BatchUtils.doInBatch(10000, moIds, (item) -> {
                try {
                    List<BattLifeEvalDBean> battLifeEvalDBeans = battLifeEvalDMapper.selectLastEvalsByMoIds(item);
                    if (CollectionUtils.isNotEmpty(battLifeEvalDBeans)) {
                        evalDayBeans.addAll(battLifeEvalDBeans);
                    }
                } catch (UedmException e) {
                    log.error("getBattLifeEvalDayMap lambda BatchUtils.doInBatch item={} error:", item, e);
                }
            });
            if (CollectionUtils.isNotEmpty(evalDayBeans)) {
                return evalDayBeans.stream().filter(bean -> (StringUtils.isNotBlank(bean.getId()) && bean.getLife() != null))
                        .collect(Collectors.toMap(BattLifeEvalDBean::getId, bean -> bean, (old, newValue) -> old));
            }
        }
        return new HashMap<>();
    }

    @Override
    public PageInfo<BattLifeEvalDBean> selectSingleEvalByMoId(SelectSingleBattEvalHistoryBo bo, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException
    {
        if (null==bo || StringUtils.isBlank(bo.getBatteryId()))
        {
            log.info("BattLifeEvalDomainImpl selectSingleEvalByMoId param is empty");
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,"params is empty.");
        }
        log.info("BattLifeEvalDomainImpl selectSingleEvalByMoId bo:{},serviceBaseInfoBean:{}",
                bo,serviceBaseInfoBean);
        try {
            if(serviceBaseInfoBean.isPage())
            {
                PageHelper.startPage(serviceBaseInfoBean.getPageNo(),serviceBaseInfoBean.getPageSize());
            }
            List<BattLifeEvalDBean> battLifeEvalDBeans = battLifeEvalDMapper.selectSingleEvalByMoId(bo);
            return new PageInfo<>(battLifeEvalDBeans);
        }
        catch (Exception e)
        {
            log.error("ResourceBaseDomain selectGroupAndMoByCondition is error ",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }
}
