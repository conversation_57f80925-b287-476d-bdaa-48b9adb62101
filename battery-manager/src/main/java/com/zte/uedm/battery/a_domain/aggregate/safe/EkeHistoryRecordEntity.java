package com.zte.uedm.battery.a_domain.aggregate.safe;

import com.zte.uedm.common.bean.log.OperlogDetail;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EkeHistoryRecordEntity {

    /**
     * 主键id
     */
    @OperlogDetail(zhName = "电子钥匙ID", enName = "ekey id")
    private String id;
    /**
     * 电子钥匙（密文）
     */
    private String ekey;
    /**
     * 是否当前秘钥
     */
    @OperlogDetail(zhName = "是否当前秘钥", enName = "is curr")
    private Boolean isCurr;
    /**
     * 创建时间
     */
    @OperlogDetail(zhName = "创建时间", enName = "create time")
    private String gmtCreate;
    /**
     * 创建人
     */
    @OperlogDetail(zhName = "创建人", enName = "create man")
    private String creator;
    /**
     * ekey的sha256格式
     */
    private String ekeySha256;
}
