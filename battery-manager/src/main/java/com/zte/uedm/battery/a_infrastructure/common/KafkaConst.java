package com.zte.uedm.battery.a_infrastructure.common;

public class KafkaConst
{
    private KafkaConst()
    {

    }



    public static final String KAFKA_TREE_CHANGE = "tree_change";

    /**
     * 告警管理接收通用业务告警主题
     */
    public static final String KAFKA_TOPIC_UEDM_COMMON_ALARM = "uedm_common_alarm";

    /**
     * 南向文件传输，SNMP错峰下发发送此主题
     */
    public static final String KAFKA_TOPIC_REMOTE_FILE_TRANSFER = "remote-file-transfer";

    /**
     * 南向文件传输响应
     */
    public static final String KAFKA_TOPIC_REMOTE_FILE_TRANSFER_RSP = "remote-file-transfer-rsp";

    /**
     * 拉取设备历史充放电记录/设备实际错峰策略文件
     */
    public static final String KAFKA_BATTERY_CHARGE_DISCHARGE_HISTORY_DATA_TOPIC = "remote-file-acquisition";

    /**
     * 返回 设备历史充电记录/设备实际错峰策略文件
     */
    public static final String KAFKA_TOPIC_REMOTE_FILE_ACQUISITION_RSP = "remote-file-acquisition-rsp";


}
