package com.zte.uedm.battery.controller.battchargestatus;


import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.service.BattChargeStatusService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 电池充放电状态相关业务 api
 *
 * <AUTHOR>
 */
@Path("batt-charge-status")
@Component
@Slf4j
//@Api(value = "batt-charge-status")
@Api(value = "饭吃充电放电")
public class BattChargeStatusController
{

    @Autowired
    private BattChargeStatusService battChargeStatusService;


    @GET
    @Path("/level/select")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询电池的充放电状态可选值", notes = "查询电池的充放电状态可选值", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -200,message = "远程调用失败")
    })
    public ResponseBean selectLevels(@QueryParam("pageNo")Integer pageNo, @QueryParam("pageSize")Integer pageSize,
                               @Context HttpServletRequest request,
                               @HeaderParam("language-option") String languageOption)
    {
        log.info("BattChargeStatusController->start to select batt charge status level.");
        int total = 0;
        List<IdNameBean> data = null;
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);

            total = 0;
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption, pageNo, pageSize);

            PageInfo<IdNameBean> page =  battChargeStatusService.selectLevels(serviceBean);
            total = (int) page.getTotal();
            data = page.getList();

        } catch (UedmException e) {
            log.error("BattSohController-> select batt charge status level fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }

        return ResponseBeanUtils.getNormalResponseBean(0, data, total);
    }
}
