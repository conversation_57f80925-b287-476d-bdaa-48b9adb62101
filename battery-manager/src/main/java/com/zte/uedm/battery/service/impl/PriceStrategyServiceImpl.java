package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.utils.CollectorUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pojo.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.enums.peak.PeakGainsNameEnum;
import com.zte.uedm.battery.enums.peak.TimeGranularityTypeEnum;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.mapper.PeakShiftMapper;
import com.zte.uedm.battery.mapper.PriceStrategyMapper;
import com.zte.uedm.battery.service.PriceStrategyService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.common.bean.log.OperLogContants;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.enums.EnergyTypeEnum;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ComparatorUtil;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PriceStrategyServiceImpl implements PriceStrategyService
{
    @Autowired
    private PriceStrategyMapper priceStrategyMapper;

    @Autowired
    private GridStrategyMapper gridStrategyMapper;

    @Autowired
    private PeakShiftMapper peakShiftMapper;

    @Autowired
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LogUtils logUtils;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CollectorCacheManager collectorCacheManager;

    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Autowired
    private FieldCacheManager fieldCacheManager;


    private static final String DEL_STATUS = "pending";
    private static final String PRICE = ",电价: {";
    private static final String GRID_NAME = "电网名称：";
    private static final String EFFECTIVE_TIME = ",生效时间：";
    private static final String PEAK_EN = ",peak:";
    private static final String GRID_NAMEN = "Grid name：";
    private static final String VALLEY_EN = "Valley:";
    private static final String FLAT_EN = ",flat:";
    private static final String CFG_CENTER_CURRENCY_KEY = "configuration.show.currency.unit";

    @Override
    public GrainTrendResponseBean queryGrainTrend(GrainTrendQueryBean bean, HttpServletRequest request, String languageOption)
            throws UedmException
    {
        log.info("[IN] queryGrainTrend request = {}",bean);
        checkQueryParam(bean);
        List<String> setIds = peakShiftConfigBaseServiceImpl.getChildrenIds(bean.getObjectId());
        bean.setSiteIds(setIds);
        GrainTrendResponseBean grainTrendResponseBean = new GrainTrendResponseBean();
        List<String> xAxis = new ArrayList<>();
        List<String> names = new ArrayList<>();
        List<ResultBean> data = new ArrayList<>();
        List<String> deviceIdList = getDeviceIdList(bean.getDeviceType());

        bean.setDeviceIds(deviceIdList);
        if (TimeGranularityTypeEnum.DAY.getStr().equals(bean.getTimeGran()))
        {
            queryDayModeData(bean,xAxis,names,data,grainTrendResponseBean,languageOption);
        }
        if (TimeGranularityTypeEnum.MONTH.getStr().equals(bean.getTimeGran()))
        {
            queryMonthModeData(bean,xAxis,names,data,grainTrendResponseBean,languageOption);
        }
        if (TimeGranularityTypeEnum.YEAR.getStr().equals(bean.getTimeGran()))
        {
            queryYearModeData(bean,xAxis,names,data,grainTrendResponseBean,languageOption);
        }
        setDataFlag(grainTrendResponseBean);
        log.info("[OUT] queryGrainTrend response = {}",grainTrendResponseBean);
        return grainTrendResponseBean;
    }

    private void checkQueryParam(GrainTrendQueryBean bean) throws UedmException
    {
        if (bean == null)
        {
            log.error("requestBean con't be null");
            throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ParameterExceptionEnum.BLANK.getDesc());
        }
        if (StringUtils.isBlank(bean.getStartTime()))
        {
            log.error("startTime is blank {}",bean.getStartTime());
            throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ParameterExceptionEnum.BLANK.getDesc());
        }
        if (StringUtils.isBlank(bean.getEndTime()))
        {
            log.error("startTime is blank {}",bean.getStartTime());
            throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ParameterExceptionEnum.BLANK.getDesc());
        }
    }

    /**
     * 日模式统计
     * @param bean
     * @param xAxis
     * @param names
     * @param data
     * @param grainTrendResponseBean
     * @param language
     */
    public void queryDayModeData(GrainTrendQueryBean bean,List<String> xAxis,List<String> names,List<ResultBean> data,GrainTrendResponseBean grainTrendResponseBean,String language)
    {
        String year = bean.getStartTime().substring(0, 4);
        if (StringUtils.isBlank(bean.getYear()))
        {
            List<GainTrendVo> dayModeVos = priceStrategyMapper.queryDayModeList(bean);
            log.debug("queryDayModeData, year is null, dayModeVos = {}", JSON.toJSONString(dayModeVos));
            List<GainTrendVo> list = updateDayModeData(bean.getStartTime(), bean.getEndTime(), dayModeVos);
            // 非同比不需要展示年份
            getResult(list,data,xAxis,names,"",language);
            grainTrendResponseBean.setXAxis(xAxis);
            grainTrendResponseBean.setName(names);
            grainTrendResponseBean.setData(data);
        }
        else
        {
            List<GainTrendVo> dayModeVos = priceStrategyMapper.queryDayModeList(bean);
            log.debug("queryDayModeData dayModeVos = {}", JSON.toJSONString(dayModeVos));
            List<GainTrendVo> list = updateDayModeData(bean.getStartTime(), bean.getEndTime(), dayModeVos);
            getResult(list,data,xAxis,names,year,language);
            //查询对比年份数据
            bean.setStartTime(bean.getStartTime().replace(year, bean.getYear()));
            bean.setEndTime(bean.getEndTime().replace(year,bean.getYear()));
            List<GainTrendVo> dayModeVos1 = priceStrategyMapper.queryDayModeList(bean);
            log.debug("queryDayModeData dayModeVos1 = {}", JSON.toJSONString(dayModeVos1));
            List<GainTrendVo> voList = updateDayModeData(bean.getStartTime(), bean.getEndTime(), dayModeVos1);
            getResult(voList,data,new ArrayList<>(),names,bean.getYear(),language);
            grainTrendResponseBean.setXAxis(xAxis);
            grainTrendResponseBean.setName(names);
            grainTrendResponseBean.setData(data);
        }
    }

    /**
     * 月模式统计
     * @param bean
     * @param xAxis
     * @param names
     * @param data
     * @param grainTrendResponseBean
     * @param language
     */
    public void queryMonthModeData(GrainTrendQueryBean bean,List<String> xAxis,List<String> names,List<ResultBean> data,GrainTrendResponseBean grainTrendResponseBean,String language)
            throws UedmException
    {
        String year = bean.getStartTime().substring(0, 4);
        if (StringUtils.isBlank(bean.getYear()))
        {
            List<GainTrendVo> monthModeVos = priceStrategyMapper.queryMonthModeList(bean);
            log.debug("queryMonthModeData monthModeVos = {}", JSON.toJSONString(monthModeVos));
            List<GainTrendVo> voList = updateMonthModeData(bean.getStartTime(), bean.getEndTime(), monthModeVos);
            // 非同比不需要展示年份
            getResult(voList,data,xAxis,names,"",language);
            grainTrendResponseBean.setXAxis(xAxis);
            grainTrendResponseBean.setName(names);
            grainTrendResponseBean.setData(data);
        }
        else
        {
            List<GainTrendVo> monthModeVos = priceStrategyMapper.queryMonthModeList(bean);
            log.debug("queryMonthModeData monthModeVos = {}", JSON.toJSONString(monthModeVos));
            List<GainTrendVo> voList = updateMonthModeData(bean.getStartTime(), bean.getEndTime(), monthModeVos);
            getResult(voList,data,xAxis,names,year,language);
            //查询对比年份数据
            bean.setStartTime(bean.getStartTime().replace(year, bean.getYear()));
            bean.setEndTime(bean.getEndTime().replace(year,bean.getYear()));
            List<GainTrendVo> monthModeVos1 = priceStrategyMapper.queryMonthModeList(bean);
            log.debug("queryMonthModeData monthModeVos1 = {}", JSON.toJSONString(monthModeVos1));
            List<GainTrendVo> voList1 = updateMonthModeData(bean.getStartTime(), bean.getEndTime(), monthModeVos1);
            getResult(voList1,data,new ArrayList<>(),names,bean.getYear(),language);
            grainTrendResponseBean.setXAxis(xAxis);
            grainTrendResponseBean.setName(names);
            grainTrendResponseBean.setData(data);
        }
    }

    /**
     * 年模式统计
     * @param bean
     * @param xAxis
     * @param names
     * @param data
     * @param grainTrendResponseBean
     * @param language
     */
    public void queryYearModeData(GrainTrendQueryBean bean,List<String> xAxis,List<String> names,List<ResultBean> data,GrainTrendResponseBean grainTrendResponseBean,String language)
    {
        List<GainTrendVo> yearModeVos = priceStrategyMapper.queryYearModeList(bean);
        log.debug("queryYearModeData yearModeVos = {}", JSON.toJSONString(yearModeVos));
        List<GainTrendVo> voList = updateYearModeData(bean.getStartTime(), bean.getEndTime(), yearModeVos);
        getResult(voList,data,xAxis,names,"",language);
        grainTrendResponseBean.setXAxis(xAxis);
        grainTrendResponseBean.setName(names);
        grainTrendResponseBean.setData(data);
    }

    /**
     * 封装统计结果
     * @param voList
     * @param data
     * @param xAxis
     * @param names
     * @param year
     * @param language
     */
    private void getResult(List<GainTrendVo> voList,List<ResultBean> data,List<String> xAxis,List<String> names,String year,String language)
    {
        if(!CollectionUtils.isEmpty(voList))
        {
            voList = voList.stream().sorted(Comparator.comparing(GainTrendVo::getTime)).collect(Collectors.toList());
            List<String> chargePrice = new ArrayList<>();
            List<String> disChargePrice = new ArrayList<>();
            List<String> gain = new ArrayList<>();
            ResultBean chargePriceBean = new ResultBean();
            ResultBean disChargePriceBean = new ResultBean();
            ResultBean gainBean = new ResultBean();
            for (int i = 0; i < voList.size(); i++)
            {
                xAxis.add(voList.get(i).getTime());
                chargePrice.add(voList.get(i).getChargePrice());
                disChargePrice.add(voList.get(i).getDisChargePrice());
                gain.add(voList.get(i).getGain());
            }
            year = StringUtils.isBlank(year) ? year : year + " ";
            chargePriceBean.setName(year + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.CHARGECOST.getName(),language));
            chargePriceBean.setData(chargePrice);
            data.add(chargePriceBean);
            disChargePriceBean.setName(year + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.DISCHARGECOST.getName(),language));
            disChargePriceBean.setData(disChargePrice);
            data.add(disChargePriceBean);
            gainBean.setName(year +  i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.PROFIT.getName(), language));
            gainBean.setData(gain);
            data.add(gainBean);
            names.add(chargePriceBean.getName());
            names.add(disChargePriceBean.getName());
            names.add(gainBean.getName());
        }
    }

    @Override
    public List<DeviceAccumulatedKwhGainBo> queryByDeviceTotal(String beginTime,String endTime,
            List<String> deviceId) throws UedmException
    {
        try
        {
            //查询出每个设备这段时间内的累积量
            List<DeviceAccumulatedKwhGainBo> total = priceStrategyMapper.queryDeviceAccumulatedKwhGain(beginTime,
                    endTime, deviceId);
            //查询出每个设备这段时间内的各个错峰时段的累积量
            List<DeviceAccumulatedKwhGainBo> totalStrategyType = priceStrategyMapper.queryDeviceStrategyTypeKwhGain(
                    beginTime, endTime, deviceId);

            log.info("total size:{};totalStrategyType size:{}",total.size(),totalStrategyType.size());
            //找到每个设备的各个错峰时段累积量，并set
            for (DeviceAccumulatedKwhGainBo totalBean : total)
            {
                String deviceIdStr = totalBean.getMonitorDeviceId();
                //寻找到设备ID匹配的各个错峰时段的累积量
                List<DeviceAccumulatedKwhGainBo> partStrategyType = totalStrategyType.stream()
                        .filter(x -> StringUtils.equals(x.getMonitorDeviceId(), deviceIdStr)).collect(Collectors.toList());
                Map<String, String> totalChargeDetail = new HashMap<>();
                Map<String, String> totalDischargeDetail = new HashMap<>();

                for (DeviceAccumulatedKwhGainBo typeBean : partStrategyType)
                {
                    String strategyType = typeBean.getStrategyType();
                    totalChargeDetail.put(strategyType, typeBean.getTotalCharge());
                    totalDischargeDetail.put(strategyType, typeBean.getTotalDischarge());
                }
                totalBean.setTotalChargeDetail(totalChargeDetail);
                totalBean.setTotalDischargeDetail(totalDischargeDetail);
            }

            return total;
        }
        catch(Exception e)
        {
            log.error("queryByDeviceTotal failed!",e);
            throw new UedmException(-1,"queryByDeviceTotal failed!");
        }
    }

    @Override
    public List<DeviceAccumulatedKwhGainBo> queryByDeviceDay(String beginTime,String endTime,
            List<String> deviceId) throws UedmException
    {
        try
        {
            //查询出每个设备每天的累积量
            List<DeviceAccumulatedKwhGainBo> total = priceStrategyMapper.queryDeviceDailyKwhGain(beginTime,
                    endTime,deviceId);
            //查询出每个设备每天的各个错峰时段的累积量
            List<DeviceAccumulatedKwhGainBo> totalStrategyType =
                    priceStrategyMapper.queryDeviceDailyStrategyTypeKwhGain(beginTime,
                            endTime,deviceId);

            log.info("total size:{};totalStrategyType size:{}",total.size(),totalStrategyType.size());
            //找到每个设备每天的各个错峰时段累积量，并set
            for(DeviceAccumulatedKwhGainBo totalBean:total)
            {
                String deviceIdStr = totalBean.getMonitorDeviceId();
                String dateStr = totalBean.getDateStr();

                //匹配这个设备这一天的各个错峰时段的累积量
                List<DeviceAccumulatedKwhGainBo> partStrategyType =
                        totalStrategyType.stream().filter(
                                x -> StringUtils.equals(x.getMonitorDeviceId(),deviceIdStr) && StringUtils.equals(x.getDateStr(),dateStr))
                                .collect(Collectors.toList());
                Map<String,String> totalChargeDetail = new HashMap<>();
                Map<String,String> totalDischargeDetail = new HashMap<>();

                for(DeviceAccumulatedKwhGainBo typeBean:partStrategyType)
                {
                    String strategyType = typeBean.getStrategyType();
                    totalChargeDetail.put(strategyType,typeBean.getTotalCharge());
                    totalDischargeDetail.put(strategyType,typeBean.getTotalDischarge());
                }
                totalBean.setTotalChargeDetail(totalChargeDetail);
                totalBean.setTotalDischargeDetail(totalDischargeDetail);
            }

            return total;

        }
        catch(Exception e)
        {
            log.error("queryByDeviceDay failed!",e);
            throw new UedmException(-1,"queryByDeviceDay failed!");
        }
    }

    @Override
    public PageInfo<PriceStrategyVo> selectByCondition(QueryPriceStrategyBean queryBean)
            throws UedmException
    {
        log.info("[IN] selectByCondition requestBean={}",queryBean);
        if (queryBean == null || StringUtils.isBlank(queryBean.getScopeStrategyId()))
        {
            throw new UedmException(ParameterExceptionEnum.BLANK.getCode(),ParameterExceptionEnum.BLANK.getDesc());
        }
        PageInfo<PriceStrategyVo> pageInfo = new PageInfo<>();
        List<PriceStrategyVo> list = priceStrategyMapper.selectByCondition(queryBean);
        if (CollectionUtils.isEmpty(list))
        {
            pageInfo.setList(list);
            return pageInfo;
        }
        for (PriceStrategyVo vo : list)
        {
            Map<String, String> map = new HashMap<>();
            List<PriceDetailBean> priceDetailBeans = priceStrategyMapper.selectPriceDetail(vo.getId());
            if (!CollectionUtils.isEmpty(priceDetailBeans))
            {
                for (PriceDetailBean bean : priceDetailBeans)
                {
                    map.put(bean.getStrategyType().toString(),bean.getPrice());
                }
            }
            vo.setPrices(map);
        }
        return sortAndPageing(pageInfo,list,queryBean);
    }

    @Override
    public PageInfo<QueryDetailVo> queryDetail(QueryDetailDto dto, HttpServletRequest request, String languageOption)
            throws UedmException
    {
        List<String> siteIds = peakShiftConfigBaseServiceImpl.getChildrenIds(dto.getId());
        dto.setSiteIds(siteIds);
        if (dto.getPageNo() != null && dto.getPageSize() != null)
        {
            PageHelper.startPage(dto.getPageNo(),dto.getPageSize());
        }
        if(TimeGranularityTypeEnum.MONTH.getStr().equals(dto.getTimeGran()))
        {
            dto.setStartDate(dto.getStartDate()+"-01");
            dto.setEndDate(dto.getEndDate()+"-31");
        }
        else if(TimeGranularityTypeEnum.YEAR.getStr().equals(dto.getTimeGran()))
        {
            dto.setStartDate(dto.getStartDate()+"01-01");
            dto.setEndDate(dto.getEndDate()+"12-31");
        }
        List<String> deviceIdList = getDeviceIdList(dto.getDeviceType());
        dto.setDeviceIds(deviceIdList);
        List<QueryDetailVo> queryDetailVos = priceStrategyMapper.queryDetail(dto);
        //从缓存中获取采集器站点关系
        Map<String, ResourceBaseEntity> sites = fieldCacheManager.selectByMocs(Collections.singleton(MocOptional.SITE.getId()))
                .stream().collect(Collectors.toMap(FieldEntity::getId, entity -> entity, (k1, k2) -> k2));
        Set<String> collectorIds = new HashSet<>(collectorCacheManager.getAllCollectorId());
        Map<String, ResourceBaseEntity> deviceSiteRelationDtoMap = collectorCacheManager.getResourceBeansInPath(collectorIds, sites);

        log.debug("=============== new cache deviceSiteRelationDtoMap:{}",deviceSiteRelationDtoMap);
        log.debug("=============== new cache deviceSiteRelationDtoMap size:{}",deviceSiteRelationDtoMap.size());
        fillInPowerSystemInfo(queryDetailVos,deviceSiteRelationDtoMap);
        return new PageInfo<>(queryDetailVos);
    }

    @Override
    public QueryPowerTrendVo queryPowerTrend(QueryPowerTrendDto dto, HttpServletRequest request,
            String languageOption) throws UedmException
    {
        log.info("[IN] queryPowerTrend request = {}",dto);
        GrainTrendQueryBean bean = new GrainTrendQueryBean();
        BeanUtils.copyProperties(dto,bean);
        checkQueryParam(bean);
        List<String> setIds = peakShiftConfigBaseServiceImpl.getChildrenIds(bean.getObjectId());
        bean.setSiteIds(setIds);
        List<String> deviceIdList = getDeviceIdList(bean.getDeviceType());
        bean.setDeviceIds(deviceIdList);
        QueryPowerTrendVo vo = new QueryPowerTrendVo();
        if (TimeGranularityTypeEnum.DAY.getStr().equals(bean.getTimeGran()))
        {
            vo = queryDayMode(bean, languageOption);
        }
        if (TimeGranularityTypeEnum.MONTH.getStr().equals(bean.getTimeGran()))
        {
            vo = queryMonthMode(bean, languageOption);
        }
        if (TimeGranularityTypeEnum.YEAR.getStr().equals(bean.getTimeGran()))
        {
            vo = queryYearMode(bean,languageOption);
        }
        setDataFlag(vo.getGain());
        setDataFlag(vo.getKwh());
        log.info("[OUT] queryGrainTrend response = {}",vo);
        return vo;
    }

    @Override
    public boolean synDataStrategy(List<?> list, String operation) throws UedmException
    {
        if (CommonConst.PRICE_STRATEGY_ADD.equals(operation))
        {
            List<AddPricePolicyPO> policyPOList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, AddPricePolicyPO.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy policyPOList is {}", policyPOList);
            priceStrategyMapper.insertListPricePolicy(policyPOList);
        }
        else if (CommonConst.PRICE_STRATEGY_DETAIL_ADD.equals(operation))
        {
            List<AddPricePolicyDetailPO> policyDetailList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, AddPricePolicyDetailPO.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy policyDetailList is {}", policyDetailList);
            priceStrategyMapper.insertListPricePolicyDetail(policyDetailList);
        }
        else if (CommonConst.PRICE_STRATEGY_UPDATE.equals(operation))
        {
            List<EditPricePolicyPO> editPricePolicyList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, EditPricePolicyPO.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy editPricePolicyList is {}", editPricePolicyList);
            priceStrategyMapper.updateListPricePolicy(editPricePolicyList);
        }
        else if (CommonConst.PRICE_STRATEGY_DETAIL_UPDATE.equals(operation))
        {
            List<EditPricePolicyDetailPO> editPricePolicyDetailList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, EditPricePolicyDetailPO.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy editPricePolicyDetailList is {}", editPricePolicyDetailList);
            priceStrategyMapper.updateListPricePolicyDetail(editPricePolicyDetailList);
        }
        else if (CommonConst.PRICE_STRATEGY_DELETE.equals(operation))
        {
            List<String> idList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, String.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy delete idList is {}", idList);
            for (String id : idList)
            {
                priceStrategyMapper.deletePriceStrategy(id);
            }
        }
        else if (CommonConst.PRICE_STRATEGY_DETAIL_DELETE.equals(operation))
        {
            List<String> idList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, String.class);
            log.debug("PriceStrategyServiceImpl synDataStrategy detail idList is {}", idList);
            for (String id : idList)
            {
                priceStrategyMapper.deletePriceStrategyDetail(id);
            }
        }
        intervalStrategy(list, operation);
        scheduledChangeStatus(operation);
        return true;
    }

    @Override
    public List<PriceStatisticsVo> queryPriceStatistics() {
        String currency = configService.getGlobalProperty(CFG_CENTER_CURRENCY_KEY);
        List<PriceStatisticsQueryDetailBean> priceStatisticsQueryDetailBeanList = priceStrategyMapper.selectPriceStrategy(6);

        List<PriceStatisticsVo> priceStatisticsVoList = new ArrayList<>();
        PriceStatisticsVo priceGridStatisticsVo = new PriceStatisticsVo();
        priceGridStatisticsVo.setPriceType(EnergyTypeEnum.GRID.getEnergyType());
        priceGridStatisticsVo.setPriceUnit(currency);
        PriceStatisticsVo priceSolarStatisticsVo = new PriceStatisticsVo();
        priceSolarStatisticsVo.setPriceType(EnergyTypeEnum.SOlAR.getEnergyType());
        priceSolarStatisticsVo.setPriceUnit(currency);
        List<PriceStatisticsDetailBean> priceGridStatisticsDetail = new ArrayList<>();
        List<PriceStatisticsDetailBean> priceSolarStatisticsDetail = new ArrayList<>();

        for (PriceStatisticsQueryDetailBean priceStatisticsQueryDetailBean : priceStatisticsQueryDetailBeanList) {
            if ((EnergyTypeEnum.GRID.getEnergyType()).equals(priceStatisticsQueryDetailBean.getPriceType())) {
                PriceStatisticsDetailBean priceStatisticsDetailBean = new PriceStatisticsDetailBean();
                BeanUtils.copyProperties(priceStatisticsQueryDetailBean, priceStatisticsDetailBean);
                priceGridStatisticsDetail.add(priceStatisticsDetailBean);
            } else if ((EnergyTypeEnum.SOlAR.getEnergyType()).equals(priceStatisticsQueryDetailBean.getPriceType())) {
                PriceStatisticsDetailBean priceStatisticsDetailBean = new PriceStatisticsDetailBean();
                BeanUtils.copyProperties(priceStatisticsQueryDetailBean, priceStatisticsDetailBean);
                priceSolarStatisticsDetail.add(priceStatisticsDetailBean);
            }
        }
        priceGridStatisticsVo.setPriceStatisticsDetail(priceGridStatisticsDetail);
        priceSolarStatisticsVo.setPriceStatisticsDetail(priceSolarStatisticsDetail);
        priceStatisticsVoList.add(priceSolarStatisticsVo);
        priceStatisticsVoList.add(priceGridStatisticsVo);
        return priceStatisticsVoList;
    }

    @Override
    public Boolean existPriceStatistics() {
        ScopeStrategyResponseBean scopeStrategyResponseBean = priceStrategyMapper.selectOne();
        if (scopeStrategyResponseBean != null && StringUtils.isNotBlank(scopeStrategyResponseBean.getId())){
            return true;
        }
        return false;
    }


    @Override
    public List<PriceStatisticsQueryDetailBean> queryPriceStatisticsForExport() throws UedmException {
        return priceStrategyMapper.selectPriceStrategy(5);
    }


    private void scheduledChangeStatus(String operation)
    {
        if (CommonConst.PRICE_STRATEGY_STATUS_INVALID.equals(operation))
        {
            log.debug("PriceStrategyServiceImpl scheduledChangeStatus price_strategy_status_invalid");
            priceStrategyMapper.changePriceStrategyStatusToInvalid();
        }
        else if (CommonConst.PRICE_STRATEGY_STATUS_EFFECTIVE.equals(operation))
        {
            log.debug("PriceStrategyServiceImpl scheduledChangeStatus price_strategy_status_effective");
            priceStrategyMapper.changePriceStrategyStatusToEffective();
        }
        else if (CommonConst.INTERVAL_STRATEGY_STATUS_TWO.equals(operation))
        {
            log.debug("PriceStrategyServiceImpl scheduledChangeStatus interval_strategy_status_two");
            peakShiftMapper.changeSeasonStrategyStatusInto2();
        }
        else if (CommonConst.INTERVAL_STRATEGY_STATUS_ONE.equals(operation))
        {
            log.debug("PriceStrategyServiceImpl scheduledChangeStatus interval_strategy_status_one");
            peakShiftMapper.changeSeasonStrategyStatusInto1();
        }
    }

    private void intervalStrategy(List<?> list, String operation) throws UedmException
    {
        if (CommonConst.INTERVAL_STRATEGY_ADD.equals(operation))
        {
            List<IntervalStrategyBean> beanList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, IntervalStrategyBean.class);
            log.debug("PriceStrategyServiceImpl intervalStrategy beanList is {}", beanList);
            gridStrategyMapper.addIntervalStrategy(beanList);
        }
        else if (CommonConst.INTERVAL_STRATEGY_DETAIL_ADD.equals(operation))
        {
            List<IntervalStrategyDetailBean> beanList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, IntervalStrategyDetailBean.class);
            log.debug("PriceStrategyServiceImpl intervalStrategy detail beanList is {}", beanList);
            gridStrategyMapper.addIntervalStrategyDetail(beanList);
        }
        else if (CommonConst.SEASON_STRATEGY_ADD.equals(operation))
        {
            List<SeasonStrategyBean> beanList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, SeasonStrategyBean.class);
            log.debug("PriceStrategyServiceImpl seasonStrategy beanList is {}", beanList);
            for (SeasonStrategyBean seasonStrategyBean : beanList)
            {
                gridStrategyMapper.addSeasonStrategy(seasonStrategyBean);
            }
        }
        else if (CommonConst.INTERVAL_STRATEGY_EDIT.equals(operation))
        {
            List<SeasonStrategyBean> beanList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, SeasonStrategyBean.class);
            log.debug("PriceStrategyServiceImpl intervalStrategy edit beanList is {}", beanList);
            for (SeasonStrategyBean seasonStrategyBean : beanList)
            {
                gridStrategyMapper.editSeasonStrategy(seasonStrategyBean);
            }
        }
        seasonIntervalAndDetailDelete(list, operation);
    }

    /**
     * seasonStrategy/intervalStrategy/intervalDetail删除相关
     *
     * @param list
     * @param operation
     * @throws UedmException
     */
    private void seasonIntervalAndDetailDelete(List<?> list, String operation) throws UedmException {
        if (CommonConst.SEASON_STRATEGY_DELETE.equals(operation)) {
            List<SeasonStrategyBean> beanList = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, SeasonStrategyBean.class);
            log.debug("PriceStrategyServiceImpl seasonStrategy delete beanList is {}", beanList);
            for (SeasonStrategyBean seasonStrategyBean : beanList) {
                gridStrategyMapper.deleteseasonStrategyById(seasonStrategyBean.getId());
            }
        } else if (CommonConst.INTERVAL_STRATEGY_DELETE.equals(operation)) {
            List<String> seasonStrategyIds = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, String.class);
            log.info("PriceStrategyServiceImpl intervalStrategy delete seasonIds is {}", seasonStrategyIds);
            for (String seasonStrategyId : seasonStrategyIds) {
                gridStrategyMapper.deleteIntervalStrategyBySeasonId(seasonStrategyId);
            }
        } else if (CommonConst.INTERVAL_STRATEGY_DETAIL_DELETE.equals(operation)) {
            List<String> intervalStrategyIds = jsonService.jsonToObject(jsonService.objectToJson(list),
                    List.class, String.class);
            log.info("PriceStrategyServiceImpl intervalStrategyDetail delete intervalStrategyIds is {}", intervalStrategyIds);
            gridStrategyMapper.deleteIntervalStrategyDetailByIntervalIds(intervalStrategyIds);
        }
    }

    public void fillInPowerSystemInfo(List<QueryDetailVo> queryDetailVos,Map<String, ResourceBaseEntity> deviceSiteRelationDtoMap)
    {
        // 获取采集器及其路径
        Map<String, String> pathNameMap = collectorCacheManager.getAllCollector().stream()
                .filter(collector -> StringUtils.isNotBlank(collector.getPathName()))
                .collect(Collectors.toMap(CollectorEntity::getId, CollectorEntity::getPathName, (k1, k2) -> k2));

        for(QueryDetailVo detail: queryDetailVos)
        {
            DeviceEntity bean = deviceCacheManager.selectDeviceById(Collections.singleton(detail.getPowerSystemId())).stream()
                    .findFirst().orElse(new DeviceEntity());
            detail.setPowerSystemName(bean.getName());
            detail.setPosition(pathNameMap.get(detail.getDeviceId()));

            ResourceBaseEntity site = deviceSiteRelationDtoMap.get(detail.getDeviceId());
            if(site != null)
            {
                detail.setSiteId(site.getId());
                detail.setSiteName(site.getName());
            }
        }
    }
    public List<GainTrendVo> updateDayModeData(String begin,String end,List<GainTrendVo> dayModeVos)
    {
        if (StringUtils.isBlank(begin) || StringUtils.isBlank(end))
        {
            if (dayModeVos.size() == 0)
            {
                return dayModeVos;
            }
            begin = dayModeVos.get(0).getTime();
            end = dayModeVos.get(dayModeVos.size() - 1).getTime();
        }
        DateTimeFormatter date = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startTime = LocalDate.parse(begin, date);
        LocalDate endTime = LocalDate.parse(end, date);
        if (startTime == null)
        {
            return dayModeVos;
        }
        List<GainTrendVo> voList = new ArrayList<>();
        while(!startTime.isAfter(endTime))
        {
            String freeDate = startTime.format(date);
            List<GainTrendVo> list = dayModeVos.stream().filter(s -> freeDate.equals(s.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list))
            {
                GainTrendVo gainTrendVo = new GainTrendVo();
                gainTrendVo.setTime(freeDate);
                gainTrendVo.setGain("--");
                gainTrendVo.setChargePrice("--");
                gainTrendVo.setDisChargePrice("--");
                gainTrendVo.setCharge("--");
                gainTrendVo.setDisCharge("--");
                voList.add(gainTrendVo);
            }
            startTime = startTime.plusDays(1);
        }
        voList.addAll(dayModeVos);
        return voList;
    }

    public List<GainTrendVo> updateMonthModeData(String begin,String end,List<GainTrendVo> monthModeVos) throws UedmException
    {
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date startDate = sdf.parse(begin);
            Date endDate = sdf.parse(end);
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            min.setTime(startDate);
            min.set(min.get(Calendar.YEAR),min.get(Calendar.MONTH),1);
            max.setTime(endDate);
            max.set(max.get(Calendar.YEAR),max.get(Calendar.MONTH),2);
            Calendar curr = min;
            List<GainTrendVo> voList = new ArrayList<>();
            while (curr.before(max))
            {
                String time = sdf.format(curr.getTime());
                List<GainTrendVo> list = monthModeVos.stream().filter(s -> time.equals(s.getTime())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list))
                {
                    GainTrendVo gainTrendVo = new GainTrendVo();
                    gainTrendVo.setTime(time);
                    gainTrendVo.setGain("--");
                    gainTrendVo.setChargePrice("--");
                    gainTrendVo.setDisChargePrice("--");
                    gainTrendVo.setCharge("--");
                    gainTrendVo.setDisCharge("--");
                    voList.add(gainTrendVo);
                }
                curr.add(Calendar.MONTH, 1);
            }
            voList.addAll(monthModeVos);
            return voList;
        }
        catch (Exception e)
        {
            log.error("dateStr format failed", e);
            throw new UedmException(-1,e.getMessage());
        }
    }

    public List<GainTrendVo> updateYearModeData(String start,String end,List<GainTrendVo> yearModeVos)
    {
        List<GainTrendVo> voList = new ArrayList<>();
        while (start.compareTo(end)<=0)
        {
            String finalStart = start;
            List<GainTrendVo> list = yearModeVos.stream().filter(s -> finalStart.equals(s.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list))
            {
                GainTrendVo gainTrendVo = new GainTrendVo();
                gainTrendVo.setTime(start);
                gainTrendVo.setGain("--");
                gainTrendVo.setChargePrice("--");
                gainTrendVo.setDisChargePrice("--");
                gainTrendVo.setCharge("--");
                gainTrendVo.setDisCharge("--");
                voList.add(gainTrendVo);
            }
            start = String.valueOf(Integer.valueOf(start)+1);
        }
        voList.addAll(yearModeVos);
        return voList;
    }

    /**
     * 排序分页
     * @param pageInfo
     * @param list
     * @param queryBean
     * @return
     */
    private PageInfo<PriceStrategyVo> sortAndPageing(PageInfo<PriceStrategyVo> pageInfo,List<PriceStrategyVo> list,QueryPriceStrategyBean queryBean)
    {
        ComparatorUtil cmp = new ComparatorUtil(new String[]{"effectiveTime"},1);
        if (StringUtils.isNotBlank(queryBean.getSortBy()))
        {
            if (SortEnum.getDescSortID().equals(queryBean.getOrder()))
            {
                cmp = new ComparatorUtil(new String[]{queryBean.getSortBy()},0);
            }
            else
            {
                cmp = new ComparatorUtil(new String[]{queryBean.getSortBy()},1);
            }
        }
        list.sort(cmp);
        pageInfo.setTotal(list.size());
        log.info("[OUT] selectByCondition result size = {}",list.size());
        if (queryBean.getPageSize() != null && queryBean.getPageNo() != null)
        {
            list = PageUtil.getPageList(list, queryBean.getPageNo(), queryBean.getPageSize());
        }
        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     *新增和删除的价格政策日志
     * @param bean
     * @param request
     * @param operationEn
     * @param operationZh
     * @throws UedmException
     */
    public void addAndDeletePriceStrategyLogSender(PriceStrategyVo bean,HttpServletRequest request,String operationEn,String operationZh) throws UedmException
    {
        ScopeStrategyResponseBean scopeStrategyBean = gridStrategyMapper.selectById(bean.getScopeStrategyId());
        String name = scopeStrategyBean.getName();
        String effectiveTime = bean.getEffectiveTime();
        Map<String,String> map = bean.getPrices();
        StringBuilder sbZh = new StringBuilder();
        StringBuilder sbEn = new StringBuilder();
        sbZh.append("操作详情：{")
                .append(GRID_NAME).append(name).append(EFFECTIVE_TIME).append(effectiveTime).append(PRICE).append("低谷:").append(map.get("0")).append(",平:").append(map.get("1")).append(",峰:").append(map.get("2")).append(",尖峰:").append(map.get("3")).append("}").append("}");
        sbEn.append("Operation details:{")
                .append(GRID_NAMEN).append(name).append(",effectiveTime：").append(effectiveTime).append(",electricity Price: {").append(VALLEY_EN).append(map.get("0")).append(FLAT_EN).append(map.get("1")).append(PEAK_EN).append(map.get("2")).append(",spike:").append(map.get("3")).append("}").append("}");
        LogInputBean logInputBean = new LogInputBean();
        logInputBean.setHost(Tools.getRemoteHost(request));
        logInputBean.setUser(Tools.getUserName(request));
        logInputBean.setRank(OperlogBean.LogRank.operlog_rank_important);
        logInputBean.setOperateResult(OperLogContants.OperateResultSuccess);
        logInputBean.setDetailEn(sbEn.toString());
        logInputBean.setDetailZh(sbZh.toString());
        logInputBean.setModuleNameZh("智能错峰");
        logInputBean.setModuleNameEn("Peak Shifting");
        logInputBean.setOperationEn(operationEn);
        logInputBean.setOperationZh(operationZh);
        logInputBean.setOperateResource(new String[]{name+" - " +bean.getEffectiveTime()});
        logUtils.generalLogSender(logInputBean);
    }

    /**
     * 编辑的价格政策日志
     * @param beanBefore
     * @param beanAfter
     * @param request
     * @param operationEn
     * @param operationZh
     * @throws UedmException
     */
    public void editPriceStrategyLogSender(PriceStrategyVo beanBefore,PriceStrategyVo beanAfter,HttpServletRequest request,String operationEn,String operationZh) throws UedmException
    {
        ScopeStrategyResponseBean scopeStrategyBeanAfter = gridStrategyMapper.selectById(beanAfter.getScopeStrategyId());
        String nameAfter = scopeStrategyBeanAfter.getName();
        String effectiveTimeAfter = beanAfter.getEffectiveTime();
        Map<String,String> mapAfter = beanAfter.getPrices();
        ScopeStrategyResponseBean scopeStrategyBeanBefore = gridStrategyMapper.selectById(beanBefore.getScopeStrategyId());
        String nameBefore = scopeStrategyBeanBefore.getName();
        String effectiveTimeBefore = beanBefore.getEffectiveTime();
        Map<String,String> mapBefore = beanBefore.getPrices();
        StringBuilder sbZh = new StringBuilder();
        StringBuilder sbEn = new StringBuilder();
        sbZh.append("操作详情：{")
                .append("编辑前{").append(GRID_NAME).append(nameBefore).append(EFFECTIVE_TIME).append(effectiveTimeBefore).append(PRICE).append("低谷:").append(mapBefore.get("0")).append(",平:").append(mapBefore.get("1")).append(",峰:").append(mapBefore.get("2")).append(",尖峰:").append(mapBefore.get("3")).append("}")
                .append("编辑后{").append(GRID_NAME).append(nameAfter).append(EFFECTIVE_TIME).append(effectiveTimeAfter).append(PRICE).append("低谷:").append(mapAfter.get("0")).append(",平:").append(mapAfter.get("1")).append(",峰:").append(mapAfter.get("2")).append(",尖峰:").append(mapAfter.get("3")).append("}").append("}");
        sbEn.append("Operation details:{")
                .append("Before editing{").append(GRID_NAMEN).append(nameBefore).append(",Effective time：").append(effectiveTimeBefore).append(",Electricity price: {").append(VALLEY_EN).append(mapBefore.get("0")).append(FLAT_EN).append(mapBefore.get("1")).append(PEAK_EN).append(mapBefore.get("2")).append(PEAK_EN).append(mapBefore.get("3")).append("}")
                .append("After editing{").append(GRID_NAMEN).append(nameAfter).append(",Effective time：").append(effectiveTimeAfter).append(",Electricity price: {").append(VALLEY_EN).append(mapAfter.get("0")).append(FLAT_EN).append(mapAfter.get("1")).append(PEAK_EN).append(mapAfter.get("2")).append(PEAK_EN).append(mapAfter.get("3")).append("}").append("}");
        LogInputBean logInputBean = new LogInputBean();
        logInputBean.setHost(Tools.getRemoteHost(request));
        logInputBean.setUser(Tools.getUserName(request));
        logInputBean.setRank(OperlogBean.LogRank.operlog_rank_important);
        logInputBean.setOperateResult(OperLogContants.OperateResultSuccess);
        logInputBean.setDetailEn(sbEn.toString());
        logInputBean.setDetailZh(sbZh.toString());
        logInputBean.setModuleNameZh("智能错峰");
        logInputBean.setModuleNameEn("Peak Shifting");
        logInputBean.setOperationEn(operationEn);
        logInputBean.setOperationZh(operationZh);
        logInputBean.setOperateResource(new String[]{nameAfter+" - " +beanAfter.getEffectiveTime()});
        logUtils.generalLogSender(logInputBean);
    }

    public void editPricePolicyBeanConvertToEditPricePolicyPO(EditPricePolicyBean editPricePolicyBean, EditPricePolicyPO editPricePolicyPO) throws UedmException
    {
        editPricePolicyPO.setId(editPricePolicyBean.getId());
        editPricePolicyPO.setScopeStrategyId(editPricePolicyBean.getScopeStrategyId());
        editPricePolicyPO.setEffectiveTime(editPricePolicyBean.getEffectiveTime());
    }

    public void editPricePolicyBeanConvertToPriceStrategyVo(EditPricePolicyBean editPricePolicyBean, PriceStrategyVo priceStrategyVo) throws UedmException
    {
        priceStrategyVo.setId(editPricePolicyBean.getId());
        priceStrategyVo.setPrices(editPricePolicyBean.getPrices());
        priceStrategyVo.setScopeStrategyId(editPricePolicyBean.getScopeStrategyId());
        priceStrategyVo.setEffectiveTime(editPricePolicyBean.getEffectiveTime());
    }

    public QueryPowerTrendVo queryDayMode(GrainTrendQueryBean bean,String language)
    {
        String year = bean.getStartTime().substring(0, 4);
        List<GainTrendVo> dayModeVos = priceStrategyMapper.queryDayModeList(bean);
        log.debug("queryDayMode, dayModeVos = {}", JSON.toJSONString(dayModeVos));
        List<GainTrendVo> voList = updateDayModeData(bean.getStartTime(), bean.getEndTime(), dayModeVos);
        if (StringUtils.isBlank(bean.getYear()))
        {
            // 非同比柱状图不需要显示年份
            return getResult(voList, language, "");
        }
        else
        {
            QueryPowerTrendVo result = getResult(voList, language, year);
            //对比年份数据
            bean.setStartTime(bean.getStartTime().replace(year, bean.getYear()));
            bean.setEndTime(bean.getEndTime().replace(year,bean.getYear()));
            List<GainTrendVo> dayModeVos1 = priceStrategyMapper.queryDayModeList(bean);
            log.info("queryDayModeData dayModeVos1 size {}",dayModeVos1.size());
            List<GainTrendVo> list = updateDayModeData(bean.getStartTime(), bean.getEndTime(), dayModeVos1);
            QueryPowerTrendVo result2 = getResult(list, language, bean.getYear());
            return merge(result,result2);
        }
    }

    public QueryPowerTrendVo queryMonthMode(GrainTrendQueryBean bean,String language) throws UedmException
    {
        String year = bean.getStartTime().substring(0, 4);
        List<GainTrendVo> monthModeList = priceStrategyMapper.queryMonthModeList(bean);
        log.info("queryMonthMode, monthModeList = {}", JSON.toJSONString(monthModeList));
        List<GainTrendVo> voList = updateMonthModeData(bean.getStartTime(), bean.getEndTime(), monthModeList);
        if (StringUtils.isBlank(bean.getYear()))
        {
            return getResult(voList, language, "");
        }
        else
        {
            QueryPowerTrendVo result = getResult(voList, language, year);
            //对比年份数据
            bean.setStartTime(bean.getStartTime().replace(year, bean.getYear()));
            bean.setEndTime(bean.getEndTime().replace(year,bean.getYear()));
            List<GainTrendVo> monthModeList1 = priceStrategyMapper.queryMonthModeList(bean);
            log.info("queryDayModeData monthModeList1 = {}", JSON.toJSONString(monthModeList1));
            List<GainTrendVo> list = updateMonthModeData(bean.getStartTime(), bean.getEndTime(), monthModeList1);
            QueryPowerTrendVo result1 = getResult(list, language, bean.getYear());
            return merge(result,result1);
        }
    }

    public QueryPowerTrendVo queryYearMode(GrainTrendQueryBean bean,String language)
    {
        List<GainTrendVo> yearModeList = priceStrategyMapper.queryYearModeList(bean);
        log.info("queryYearMode, yearModeList = {}", JSON.toJSONString(yearModeList));
        List<GainTrendVo> voList = updateYearModeData(bean.getStartTime(), bean.getEndTime(), yearModeList);
        return getResult(voList, language, "");
    }

    public QueryPowerTrendVo merge(QueryPowerTrendVo vo1,QueryPowerTrendVo vo2)
    {
        GrainTrendResponseBean kwh = vo1.getKwh();
        GrainTrendResponseBean gain = vo1.getGain();

        List<String> gainName = gain.getName();
        gainName.addAll(vo2.getGain().getName());
        gain.setName(gainName);
        List<ResultBean> gainData = gain.getData();
        gainData.addAll(vo2.getGain().getData());
        gain.setData(gainData);
        List<String> kwhName = kwh.getName();
        kwhName.addAll(vo2.getKwh().getName());
        kwh.setName(kwhName);
        List<ResultBean> data = kwh.getData();
        data.addAll(vo2.getKwh().getData());
        kwh.setData(data);

        vo1.setGain(gain);
        vo1.setKwh(kwh);
        return vo1;
    }
    public QueryPowerTrendVo getResult(List<GainTrendVo> voList,String language,String year)
    {
        voList = voList.stream().sorted(Comparator.comparing(GainTrendVo::getTime)).collect(Collectors.toList());
        List<String> xAxis = voList.stream().map(GainTrendVo::getTime).collect(Collectors.toList());

        List<String> chargePrice = voList.stream().map(GainTrendVo::getChargePrice).collect(Collectors.toList());
        List<String> disChargePrice = voList.stream().map(GainTrendVo::getDisChargePrice).collect(Collectors.toList());
        List<String> charge = voList.stream().map(GainTrendVo::getCharge).collect(Collectors.toList());
        List<String> disCharge = voList.stream().map(GainTrendVo::getDisCharge).collect(Collectors.toList());

        ResultBean chargePriceBean = new ResultBean();
        ResultBean disChargePriceBean = new ResultBean();
        ResultBean chargeBean = new ResultBean();
        ResultBean disChargeBean = new ResultBean();

        QueryPowerTrendVo vo = new QueryPowerTrendVo();
        GrainTrendResponseBean gain = new GrainTrendResponseBean();
        GrainTrendResponseBean kwh = new GrainTrendResponseBean();

        List<String> gainNames = new ArrayList<>();
        List<String> kwhNames = new ArrayList<>();
        List<ResultBean> gainData = new ArrayList<>();
        List<ResultBean> kwhData = new ArrayList<>();

        year = StringUtils.isBlank(year) ? year : year + " ";
        chargePriceBean.setName(year + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.CHARGECOST.getName(),language));
        chargePriceBean.setData(chargePrice);
        gainData.add(chargePriceBean);
        disChargePriceBean.setName(year + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.DISCHARGECOST.getName(),language));
        disChargePriceBean.setData(disChargePrice);
        gainData.add(disChargePriceBean);
        gainNames.add(chargePriceBean.getName());
        gainNames.add(disChargePriceBean.getName());

        chargeBean.setName(year  + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.CHARGE.getName(),language));
        chargeBean.setData(charge);
        kwhData.add(chargeBean);
        disChargeBean.setName(year  + i18nUtils.getMapFieldByLanguageOption(PeakGainsNameEnum.DISCHARGE.getName(), language));
        disChargeBean.setData(disCharge);
        kwhData.add(disChargeBean);
        kwhNames.add(chargeBean.getName());
        kwhNames.add(disChargeBean.getName());

        gain.setXAxis(xAxis);
        kwh.setXAxis(xAxis);

        gain.setName(gainNames);
        gain.setData(gainData);
        kwh.setName(kwhNames);
        kwh.setData(kwhData);
        vo.setGain(gain);
        vo.setKwh(kwh);
        return vo;
    }

    public List<String> getDeviceIdList(String deviceType) throws UedmException
    {
        if (StringUtils.isBlank(deviceType))
        {
            return null;
        }
        List<String> collectorIds = new ArrayList<>();
        collectorCacheManager.getAllCollector()
                .stream().filter(collectorEntity -> StringUtils.isNotBlank(collectorEntity.getMoc()))
                .forEach(collectorEntity -> {
                    String moc = collectorEntity.getMoc();
                    Object protocolAttribute = collectorEntity.getProtocolAttribute();
                    String type = CollectorUtils.parseDeviceType(moc, protocolAttribute);
                    List<String> applyRange = PeakDeviceTypeEnum.getApplyRangeById(deviceType);
                    if (applyRange.contains(type)) {
                        collectorIds.add(collectorEntity.getId());
                    }
                });
        return collectorIds;
    }

    public void setDataFlag(GrainTrendResponseBean bean)
    {
        List<String> list = new ArrayList<>();
        List<ResultBean> data = bean.getData();
        if (!CollectionUtils.isEmpty(data))
        {
            for (ResultBean resultBean : data)
            {
                list.addAll(resultBean.getData());
                List<String> beanData = resultBean.getData();
            }
        }
        List<String> collect = list.stream().filter(s -> !"--".equals(s)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect))
        {
            bean.setFlag(true);
        }
        else
        {
            bean.setFlag(false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int synDeletePriceStrategy(DeletePriceStrategyInfo deletePriceStrategyInfo) throws UedmException {
        log.info("synDeletePriceStrategy: sync delete price strategy end");
        String priceStrategyId = deletePriceStrategyInfo.getPriceStrategyId();
        List<String> priceIntervalStrategyList = deletePriceStrategyInfo.getPriceIntervalStrategyList();
        try {
            priceStrategyMapper.deletePriceStrategy(priceStrategyId);
            priceStrategyMapper.deletePriceIntervalStrategy(priceStrategyId);
            priceStrategyMapper.deleteTieredPriceStrategyByPriceIntervalStrategyIds(priceIntervalStrategyList);
        } catch (Exception e) {
            log.error("synDeletePriceStrategy: delete exception: ", e);
            throw new UedmException(-1, "delete price strategy failed");
        }
        log.info("synDeletePriceStrategy: sync delete price strategy end");
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean synAddPriceStrategy(AddPriceStrategyInfo addPriceStrategyInfo) throws UedmException {
        log.info("synAddPriceStrategy: sync add price strategy start");
        AddPricePolicyPO addPricePolicyPO = addPriceStrategyInfo.getAddPricePolicyPO();
        List<PriceIntervalStrategyPO> priceIntervalStrategyPOList = addPriceStrategyInfo.getPriceIntervalStrategyPOList();
        List<TieredPriceStrategyPO> tieredPriceStrategyPOList = addPriceStrategyInfo.getTieredPriceStrategyPOList();

        try {
            priceStrategyMapper.insertPricePolicy(addPricePolicyPO);
            priceStrategyMapper.batchAddPriceIntervalStrategy(priceIntervalStrategyPOList);
            priceStrategyMapper.batchAddTieredPriceStrategy(tieredPriceStrategyPOList);
        } catch (Exception e) {
            log.error("synAddPriceStrategy: add exception: ", e);
            throw new UedmException(-1, "add price strategy failed");
        }
        log.info("synAddPriceStrategy: sync add price strategy end");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean synEditPriceStrategy(EditPriceStrategyInfo editPriceStrategyInfo) throws UedmException {
        log.info("synEditPriceStrategy: sync edit price strategy start");
        AddPricePolicyPO updatePricePolicyPO = editPriceStrategyInfo.getUpdatePricePolicyPO();
        List<PriceIntervalStrategyPO> priceIntervalStrategyPOList = editPriceStrategyInfo.getPriceIntervalStrategyPOList();
        List<TieredPriceStrategyPO> tieredPriceStrategyPOList = editPriceStrategyInfo.getTieredPriceStrategyPOList();
        List<String> priceIntervalStrategyIdList = editPriceStrategyInfo.getPriceIntervalStrategyIdList();

        //先删除再添加
        try {
            priceStrategyMapper.deletePriceIntervalStrategy(updatePricePolicyPO.getId());
            priceStrategyMapper.deleteTieredPriceStrategyByPriceIntervalStrategyIds(priceIntervalStrategyIdList);
            priceStrategyMapper.batchAddPriceIntervalStrategy(priceIntervalStrategyPOList);
            priceStrategyMapper.batchAddTieredPriceStrategy(tieredPriceStrategyPOList);

            priceStrategyMapper.updatePricePolicy(updatePricePolicyPO);
        } catch (Exception e) {
            log.error("synEditPriceStrategy: edit exception: ", e);
            throw new UedmException(-1, "edit price strategy failed");
        }
        log.info("synEditPriceStrategy: sync edit price strategy end");
        return true;
    }
}
