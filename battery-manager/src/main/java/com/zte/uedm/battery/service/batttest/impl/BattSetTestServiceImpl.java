package com.zte.uedm.battery.service.batttest.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.SouthApplySetStatusDataBean;
import com.zte.uedm.battery.domain.BattTestTaskDeviceDomain;
import com.zte.uedm.battery.domain.RemoteControlDomain;
import com.zte.uedm.battery.domain.impl.BatteryTestDomainImpl;
import com.zte.uedm.battery.enums.batttest.BattTestCommandResultEnums;
import com.zte.uedm.battery.enums.batttest.BattTestExceptionEnum;
import com.zte.uedm.battery.enums.batttest.BattTestTypeEnums;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.service.batttest.BattSetTestService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_BATTERY_TEST_SET;


@Service
@Slf4j
public class BattSetTestServiceImpl implements BattSetTestService {
    @Autowired
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpc;
    @Autowired
    private RedisService redisService;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private BatteryTestDomainImpl batteryTestDomain;
    @Autowired
    private RemoteControlDomain remoteControlDomain;
    @Autowired
    private BattTestTaskDeviceDomain battTestTaskDeviceDomain;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private JsonService jsonService;

    private final static String BATTPACK_TEST_VALUE = "2";

    @Override
    public void handleRealtimeData(SouthApplySetStatusDataBean southDataBean) {
        String logId = southDataBean.getLogId();
        if (StringUtils.isBlank(logId) || !logId.contains(BatteryConstant.BCUA_SOC + "|")) {
            return;
        }

        boolean status = southDataBean.getStatus() == 0;
        if (!status) {
            log.error("BCUA BatterySet batt Test change soc fail :{}", JSON.toJSONString(southDataBean));
        }
        String[] split = logId.split("\\|");

        // 电池组id
        String battSetId = split[1];
        // 本次手动测试用户
        String userName = split[2];
        // 测试类型
        String testType = split[3];
        // 测试taskId
        String taskId = split[4];
        startBatterySetTemporaryTest(battSetId, userName, testType, taskId);

    }

    @Override
    public void startBatterySetTemporaryTest(String id, String userName, String testType, String taskId)  {

        // 防止设备响应时间过快，把入库的下发时间往前推迟2s
        String nowTime = TimeUtils.beforeSecondTime(2);

        Map<Integer, String> result = startBatterySetTest(id);
        log.info("BatteryTemporaryTestServiceImpl ->startBatterySetTemporaryTest result :{}",result);
        for(Map.Entry<Integer, String> entry : result.entrySet())
        {
            if(!BattTestExceptionEnum.NORMAL.getCode().equals(entry.getKey()))
            {
                log.info("BatteryTemporaryTestServiceImpl ->startBatterySetTemporaryTest fail id :{}",id);
            }
        }
        //根据监控对象id查询名称
        List<MonitorObjectBean> monitorObjectList = siteSpBatteryRelatedRpc.getMonitorObjectList(Collections.singletonList(id));
        Map<String, String> idNameMap = monitorObjectList.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).filter(bean-> StringUtils.isNotBlank(bean.getName()))
                .collect(Collectors.toMap(MonitorObjectBean::getId, MonitorObjectBean::getName, (key1, key2)->key2));


        //存入redis
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME, nowTime);
        valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, BattTestCommandResultEnums.COMMAND_ISSUING.getId());
        valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME, userName);
        try {
            if (BattTestTypeEnums.PERIODIC.getId().equals(testType)) {
                valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID, taskId);
                redisService.put(BatteryConstant.PERIOD.concat(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE), id, valueMap);
                return;
            }
            redisService.put(BatteryConstant.TEMPORARY.concat(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE), id, valueMap);
        } catch (UedmException e) {
            log.info("BatteryTemporaryTestServiceImpl ->startBatterySetTemporaryTest redisService put fail, id :{}",id);
        }

    }

    @Override
    public Map<Integer, String> startBatterySetTest(String id) {
        log.debug("BatterySet id is {}", id);
        Map<Integer, String> result = new HashMap<>();
        result.put(BattTestExceptionEnum.NORMAL.getCode(), BattTestExceptionEnum.NORMAL.getDesc());
//        Set<String> idSet = new HashSet<>();
//        idSet.add(id);
        //查询电池组测试状态
//        try {
//            batteryTestDomain.getNotTestSpId(Collections.singletonList(id), idSet);
//        } catch (UedmException e) {
//            log.error("startBatterySetTest is fail!", e);
//        }
//        if (!CollectionUtils.isEmpty(idSet)) {
//            log.warn("BatterySet can't start up new test activity.");
//            String errorMessage = JSONObject.toJSONString(idSet);
//            result.put(BattTestExceptionEnum.BATT_SET_CANNOT_TEST.getCode(), errorMessage);
//            return result;
//        }
        try {
            //下发命令
            remoteControlDomain.remoteControl(id,BATTERYSET_SMPID_BATTERY_TEST_SET, BATTPACK_TEST_VALUE,UUID.randomUUID().toString());

        } catch (Exception e) {
            log.error("startBatteryTemporaryTest is fail!", e);
            result.put(BattTestExceptionEnum.REMOTE_CONTROL_FAIL.getCode(), BattTestExceptionEnum.REMOTE_CONTROL_FAIL.getDesc());
            return result;
        }
        return result;
    }


}
