package com.zte.uedm.battery.a_domain.safe.task;/* Started by AICoder, pid:h14d4ec669q789514c95090f506b3308d5316137 */

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.aggregate.safe.BatteryEkeyRelationEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.CollectorDeliveryBean;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.EleKeyDeliveryBean;
import com.zte.uedm.battery.a_infrastructure.kafka.util.KafkaUtils;
import com.zte.uedm.battery.a_infrastructure.redis.utils.RedisUtils;
import com.zte.uedm.battery.a_infrastructure.safe.enums.EleKeyTypeEnums;
import com.zte.uedm.battery.a_infrastructure.safe.persistence.BatteryEkeyRelationRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.safe.persistence.EkeHistoryRecordRepositoryImpl;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_FORTIFICATION_STATUSE;
import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_SERIAL_NUMBER;
import static com.zte.uedm.battery.a_infrastructure.kafka.util.KafkaUtils.partitionList;


/**
 * 电池设防下发电子钥匙定时任务
 */
@Component
@Slf4j
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
public class BatteryProtectStateEkeyTask extends QuartzJobBean {

    @Resource
    private MessageSenderService messageSenderService;
    @Resource
    private BatteryEkeyRelationRepositoryImpl batteryEkeyRelationRepositoryImpl;
    @Resource
    private JsonService jsonService;
    @Resource
    private StandardDataService standardDataService;
    @Resource
    private DeviceCacheManager deviceCacheManager;
    @Resource
    private EkeHistoryRecordRepositoryImpl ekeHistoryRecordRepositoryImpl;
    @Resource
    private KafkaUtils kafkaUtils;
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    /* Started by AICoder, pid:f7ef5jb796bdffd1419a093b6058520c5651a3a1 */
    @Override
    public void executeInternal(JobExecutionContext context) {
        try
        {
            JobKey jobKey = context.getJobDetail().getKey();
            if (jobKey == null){
                log.warn("jobKey is null");
                return;
            }
            log.info("BatteryProtectStateEkeyTask execute job =======start======= and jobKey={}",jobKey);
            //查询所有电池
            List<String> batteryIds = deviceCacheManager.getDevicesByMoc(MocOptional.BATTERY.getId()).stream()
                    .filter(Objects::nonNull)
                    .map(ResourceBaseEntity::getId)
                    .collect(Collectors.toList());
            List<String> collectorIds = resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(batteryIds);

            if (CollectionUtils.isEmpty(batteryIds) || CollectionUtils.isEmpty(collectorIds))
            {
                log.warn("BatteryProtectStateEkeyTask executeInternal ids empty");
                return;
            }
            log.info("BatteryProtectStateEkeyTask executeInternal battery ids={},collector ids={}",batteryIds.size(),collectorIds.size());
            Map<String, Map<String, Object>> redisDataMap = standardDataService.batchQueryByResourceIdAndStandPointId(batteryIds,
                    Arrays.asList(BATTERY_SMPID_FORTIFICATION_STATUSE,BATTERY_SMPID_SERIAL_NUMBER));
            log.debug("BatteryProtectStateEkeyTask executeInternal ids={}", JSON.toJSONString(redisDataMap));
            List<BatteryEkeyRelationEntity> batteryEkeyEntityList = batteryEkeyRelationRepositoryImpl.selectBatteryList(batteryIds);
            Map<String, List<String>> collectorRelationBattMap = resourceCollectorRelationCacheManager.queryCollectorIdWithResMapByCollectAndRes(collectorIds, batteryIds);
            log.debug("BatteryProtectStateEkeyTask collectorRelationBattMap ids={}", JSON.toJSONString(collectorRelationBattMap));

            if (CollectionUtils.isNotEmpty(batteryEkeyEntityList))
            {
                Map<String, String> batteryEkeyMap = batteryEkeyEntityList.stream()
                        .collect(Collectors.toMap(BatteryEkeyRelationEntity::getMoId, BatteryEkeyRelationEntity::getEkey));
                log.debug("BatteryProtectStateEkeyTask batteryEkeyMap ids={}", JSON.toJSONString(batteryEkeyMap));

                List<CollectorDeliveryBean> collectorDeliveryBeanList = new ArrayList<>();//最大数量20
                for (String collectorId : collectorIds) {
                    List<EleKeyDeliveryBean> ekeyDeliveryList = addEkeyDelivery(collectorId, collectorRelationBattMap, redisDataMap, batteryEkeyMap);

                    if (CollectionUtils.isNotEmpty(ekeyDeliveryList))
                    {
                        addCollectorDeliveryList(collectorId, ekeyDeliveryList, collectorDeliveryBeanList);
                    }
                    log.debug("BatteryProtectStateEkeyTask collectorDeliveryBeanList={}",collectorDeliveryBeanList);

                    if (CollectionUtils.isNotEmpty(collectorDeliveryBeanList))
                    {
                        kafkaUtils.sendKafkaToSouth(KafkaTopicOptional.KAFKA_TOPIC_UEDM_SOUTH_EVENT, collectorDeliveryBeanList);
                    }
                }
            }
            log.info("BatteryProtectStateEkeyTask execute job =======end=======");
        }
        catch (Exception e)
        {
            log.error("BatteryProtectStateEkeyTask execute error={}", e);
        }
    }
    /* Ended by AICoder, pid:f7ef5jb796bdffd1419a093b6058520c5651a3a1 */

    private void addCollectorDeliveryList(String collectorId,List<EleKeyDeliveryBean> ekeyDeliveryList, List<CollectorDeliveryBean> collectorDeliveryBeanList) {
        List<List<EleKeyDeliveryBean>> batches = partitionList(ekeyDeliveryList, 200);
        for (List<EleKeyDeliveryBean> batch : batches) {
            CollectorDeliveryBean collectorBean = new CollectorDeliveryBean();
            collectorBean.setCollectorId(collectorId);
            collectorBean.setFunctionType(EleKeyTypeEnums.HEART.getCode());
            collectorBean.setDeviceType(GlobalConstants.SOUTH_DEVICE_TYPE);
            collectorBean.setLogId(UUID.randomUUID() + GlobalConstants.BOTTOM_LINE + collectorId);
            collectorBean.setEleKeyDelData(batch);
            collectorDeliveryBeanList.add(collectorBean);
        }
    }

    /* Started by AICoder, pid:9ecfdf36b0zb3ce141b70bdce0be8b0b75b12425 */
    private List<EleKeyDeliveryBean> addEkeyDelivery(String collectorId, Map<String, List<String>> collectorRelationBattMap,
                                                     Map<String, Map<String, Object>> redisDataMap, Map<String, String> batteryEkeyMap)
    {
        List<String> batteryRelationList = collectorRelationBattMap.getOrDefault(collectorId, new ArrayList<>());
        List<EleKeyDeliveryBean> ekeyDeliveryList = new ArrayList<>();//最大数量20
        for (String batteryId : batteryRelationList) {
            Map<String, Object> battProtectMap = redisDataMap.getOrDefault(batteryId, new HashMap<>());
            String protectValue = RedisUtils.getSmpValueByKey(batteryId, battProtectMap, BATTERY_SMPID_FORTIFICATION_STATUSE);
            String snNumber = RedisUtils.getSmpValueByKey(batteryId, battProtectMap, BATTERY_SMPID_SERIAL_NUMBER);
            String battEkey = batteryEkeyMap.getOrDefault(batteryId, GlobalConstants.EMPTY_STR);
            if (protectValue.equals(GlobalConstants.ARM_STANDARD_VALUE) && StringUtils.isNotEmpty(battEkey)) {
                EleKeyDeliveryBean deliveryBean = new EleKeyDeliveryBean();
                deliveryBean.setSn(snNumber);
                deliveryBean.setNewKey(battEkey);
                ekeyDeliveryList.add(deliveryBean);
            }
        }
        log.debug("BatteryProtectStateEkeyTask addEkeyDelivery={}",ekeyDeliveryList);

        return ekeyDeliveryList;
    }
    /* Ended by AICoder, pid:9ecfdf36b0zb3ce141b70bdce0be8b0b75b12425 */

}
/* Ended by AICoder, pid:h14d4ec669q789514c95090f506b3308d5316137 */
