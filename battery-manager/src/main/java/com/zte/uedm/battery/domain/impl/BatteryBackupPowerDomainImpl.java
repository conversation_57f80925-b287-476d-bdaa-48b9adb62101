package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.BatteryBackupPowerEvalPojo;
import com.zte.uedm.battery.domain.BatteryBackupPowerDomain;
import com.zte.uedm.battery.mapper.BatteryBackupPowerEvalDMapper;
import com.zte.uedm.battery.mapper.BatteryBackupPowerEvalMMapper;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class BatteryBackupPowerDomainImpl implements BatteryBackupPowerDomain
{
    @Autowired
    private BatteryBackupPowerEvalDMapper batteryBackupPowerEvalDMapper;

    @Autowired
    private BatteryBackupPowerEvalMMapper batteryBackupPowerEvalMMapper;

    @Override
    public Integer addBackupPowerEvalDResult(List<BatteryBackupPowerEvalPojo> batteryBackupPowerEvalDPojoList) throws UedmException {
        try
        {
            Integer[] batchNum = { 0 };
            BatchUtils.doInBatch(500, batteryBackupPowerEvalDPojoList, (beans -> {
                try
                {
                    Integer n = batteryBackupPowerEvalDMapper.insertList(beans);
                    batchNum[0] += n;
                }
                catch (Exception e)
                {
                    log.error("BatteryBackupPowerDomainImpl addBackupPowerEvalDResult error", e);
                    throw new RuntimeException();
                }
            }));
            return batchNum[0];
        }
        catch (Exception e)
        {
            log.error("BatteryBackupPowerDomainImpl addBackupPowerEvalDResult error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed(e.getMessage());
        }
    }

    @Override
    public Integer addBackupPowerEvalMResult(List<BatteryBackupPowerEvalPojo> batteryBackupPowerEvalMPojoList) throws UedmException {
        try
        {
            Integer[] batchNum = { 0 };
            BatchUtils.doInBatch(500, batteryBackupPowerEvalMPojoList, (beans -> {
                try
                {
                    Integer n = batteryBackupPowerEvalMMapper.insertList(beans);
                    batchNum[0] += n;
                }
                catch (Exception e)
                {
                    log.error("BatteryBackupPowerDomainImpl addBackupPowerEvalMResult error", e);
                    throw new RuntimeException();
                }
            }));
            return batchNum[0];
        }
        catch (Exception e)
        {
            log.error("BatteryBackupPowerDomainImpl addBackupPowerEvalMResult error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed(e.getMessage());
        }
    }
}
