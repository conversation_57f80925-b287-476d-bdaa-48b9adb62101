package com.zte.uedm.battery.pv.bean;

import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class StrategyWithPvBean {
    /**
     * 太阳能id
     */
    private String id;
    /**
     * 策略对应的分组
     */
    private String scopeLogicGroup;
    /**
     * 策略id
     */
    private String scopeId;
    /**
     * 能源类型
     */
    private String energyType;
    /**
     * 季节策略id
     */
    private String seasonId;
    /**
     * 策略id
     */
    private String scopeStrategyId;
    /**
     * 季节策略状态
     */
    private Integer seasonStatus;
    /**
     * 季节策略生效时间
     */
    private String seasonEffectiveTime;
    /**
     * 季节策略失效时间
     */
    private String seasonExpirationTime;
    /**
     * 价格策略id
     */
    private String priceId;
    /**
     * 价格策略状态
     */
    private String priceStatus;
    /**
     * 价格策略生效时间
     */
    private String priceEffectiveTime;
    /**
     * 价格策略失效时间
     */
    private String priceExpirationTime;
    /**
     * 最晚生效时间
     */
    private String greatestEffectiveTime;
    /**
     * 最早失效时间
     */
    private String leastExpirationTime;

}

