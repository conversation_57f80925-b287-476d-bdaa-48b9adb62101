package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @FileDesc : 错峰用电收益统计查询DTO
 * <AUTHOR> TZZ 10286469
 * @date Date : 2022年07月27日 下午19:28
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
public class PeakShiftGrainQueryResultBean
{
    /**
     * 表头信息
     */
    private PeakShiftGrainQueryTitlesBean titles;
    /**
     * 表值信息
     */
    private List<Map<String,Object>> values;

    /**
     * 总条数
     */
    private Long total;
}
