package com.zte.uedm.battery.controller.batterytest.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class SelectBattTestConfigVo
{
    /**
     * 测试概览维度key
     */
    private String id;
    /**
     * 资产维度名称  -中英文国际化
     */
    private String name;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 顺序
     */
    private Integer sequence;
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     *  默认顺序
     */
    private Integer defaultIndex;
    /**
     *  默认是否启动
     */
    private Boolean defaultEnable;
    /**
     * 是否固定（指true时不可能变动，包括顺序和是否勾选）
     */
    private Boolean defaultFixed;
    /**
     * 支持排序
     */
    private Boolean sortable;
    /**
     * 是否隐藏
     */
    private Boolean assetAttributeShow = true;
    /**
     * 单位
     */
    private String unit;

}
