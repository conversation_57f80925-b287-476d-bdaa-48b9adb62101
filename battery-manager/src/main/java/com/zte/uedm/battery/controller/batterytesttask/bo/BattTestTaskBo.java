package com.zte.uedm.battery.controller.batterytesttask.bo;

import com.zte.uedm.battery.bean.pojo.BattTestTaskDevicesPo;
import com.zte.uedm.battery.bean.pojo.BattTestTaskPo;
import com.zte.uedm.battery.controller.batterytesttask.dto.TaskEditRequestDto;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import com.zte.uedm.common.util.validat.groups.ValidatorInAdd;
import com.zte.uedm.common.util.validat.groups.ValidatorInEdit;
import com.zte.uedm.battery.bean.pojo.BattTestTaskPo;
import com.zte.uedm.battery.enums.batttest.BattTestStatusEnums;
import com.zte.uedm.battery.soh.util.I18nUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电池测试任务 - Bo
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@Slf4j
public class BattTestTaskBo
{
    /**
     * 任务id
     */
    @NotBlank
    private String id;
    /**
     * 任务名称
     */
    @NotBlank
    @Length(max = 50, message = "name length limit 50", groups = {ValidatorInEdit.class})
    private String name;
    /**
     * 执行频度
     */
    @NotNull
    @Range(min = 1, max = 730, message = "period limit in range(1,730)", groups = {ValidatorInAdd.class})
    private Integer period;
    /**
     * 状态
     */
    private String status;
    /**
     * 开始时间
     */
    @NotBlank
    private String startTime;
    /**
     * 备注
     */
    @Length(max = 500, message = "name length limit 500", groups = {ValidatorInEdit.class})
    private String remark;
    /**
     * 是否内置
     */
    private Boolean internal;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date gmtModified;
    /**
     * 设备列表
     */
    private List<BattTestTaskDeviceBo> devices;
    /**
     * 临时设备列表
     */
    private List<BattTestTaskDeviceBo> temDevices;

    public BattTestTaskBo(){}

    public BattTestTaskBo(TaskEditRequestDto dto)
    {
        Optional.ofNullable(dto).ifPresent(item->{
            this.setId(item.getId())
                    .setName(item.getName())
                    .setPeriod(item.getPeriod())
                    .setStartTime(item.getStartTime())
                    .setRemark(item.getRemark());
        });
    }

    /**
     * 根据任务Po信息和其关联设备的po信息列表 生成实例
     * @param po
     * @param devicesPos
     */
    public BattTestTaskBo(BattTestTaskPo po, List<BattTestTaskDevicesPo> devicesPos)
    {
        Optional.ofNullable(po).ifPresent(item->{
            this.setId(item.getId())
                            .setName(item.getName())
                    .setPeriod(item.getPeriod())
                    .setStatus(item.getStatus())
                    .setRemark(item.getRemark())
                    .setInternal(item.getInternal())
                    .setCreator(item.getCreator())
                    .setGmtCreate(item.getGmtCreate())
                    .setUpdater(item.getUpdater())
                    .setGmtModified(item.getGmtModified());


            try {
                this.setStartTime(DateTimeService.convertDateToString(item.getStartTime()));
            } catch (UedmException e) {
                log.error("DateTimeService->convertDateToString is error.", e);
            }

        });
        if(CollectionUtils.isNotEmpty(devicesPos))
        {
            this.devices = devicesPos.stream().map(item->{
                BattTestTaskDeviceBo bo = new BattTestTaskDeviceBo(item);
                return bo;
            }).collect(Collectors.toList());
        }
    }


    /**
     * 检验非空字段
     * @return <Boolean, List<String>>  left：（true:校验通过，false:校验未通过），List<String> 不满足的字段id
     */
    public Pair<Boolean, List<String>> checkBlank()
    {
        Boolean checkRes = true;
        List<String> resKeys = new ArrayList<>();

        ValidationResult validationResult = ValidationUtils.validateForDefalut(this);
        if (validationResult.isHasErrors())
        {
            checkRes = false;
            Map<String, String> errMsg = validationResult.getPropertyErrMsg();
            for (Map.Entry<String, String> entry : errMsg.entrySet())
            {
                resKeys.add(entry.getKey());
            }
        }
        return Pair.of(checkRes, resKeys);
    }

    /**
     * 校验字段长度
     * @return <Boolean, List<String>>  left：（true:校验通过，false:校验未通过），List<String> 不满足的字段id
     */
    public Pair<Boolean, List<String>> checkLengthLimit()
    {
        Boolean checkRes = true;
        List<String> resKeys = new ArrayList<>();

        ValidationResult validationResult = ValidationUtils.validateForEdit(this);
        if (validationResult.isHasErrors())
        {
            checkRes = false;
            Map<String, String> errMsg = validationResult.getPropertyErrMsg();
            for (Map.Entry<String, String> entry : errMsg.entrySet())
            {
                resKeys.add(entry.getKey());
            }
        }
        return Pair.of(checkRes, resKeys);
    }


    /**
     * 校验执行频度的范围
     * @return
     */
    public boolean checkPeriodRange()
    {

        ValidationResult validationResult = ValidationUtils.validateForAdd(this);
        if (validationResult.isHasErrors())
        {
            return false;
        }
        return true;
    }
}
