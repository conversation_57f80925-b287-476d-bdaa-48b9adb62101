package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.factory.PeakShiftFactory;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_domain.utils.CollectorUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.PeakShiftStrategyDetailBean;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.controller.peakshifttask.PeakShiftTaskOperationMgr;
import com.zte.uedm.battery.domain.PeakShiftIssuedTaskDomain;
import com.zte.uedm.battery.enums.peak.PeakShiftDeviceStatusEnum;
import com.zte.uedm.battery.enums.peak.PeakShiftTaskStatusEnum;
import com.zte.uedm.battery.mapper.PeakShiftOperationRecordMapper;
import com.zte.uedm.battery.mapper.PeakShiftTaskDetailMapper;
import com.zte.uedm.battery.mapper.PeakShiftTaskMapper;
import com.zte.uedm.battery.mapper.TemplateStrategyMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SouthFrameworkRpcImpl;
import com.zte.uedm.battery.service.PeakShiftTaskService;
import com.zte.uedm.battery.service.TemplateStrategyService;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.enums.peak.CycleModelEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.ComparatorUtil;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PeakShiftIssuedTaskServiceImpl implements PeakShiftTaskService
{
    //默认的策略模板version
    private static Double DEFAULT_VERSION = 1.00;
    private static String LANGUAGE_OPTION_CN = "zh-CN";

    private static String LANGUAGE_OPTION_US = "en-US";

    private static String ONLINE_STATUS_CN = "在线";

    private static String OFFLINE_STATUS_CN = "离线";

    private static String ONLINE_STATUS_US = "online";

    private static String OFFLINE_STATUS_US = "offline";

    //链路状态为0表示链路已连接
    private static String LINK_CONNECT_STATUS = "0";

    private static int BATCH_NUMBER = 500;

    private static final String OMPID = "04009B";

    private static final String PARAM_NOT_NULL = "params serviceBaseInfoBean is not null";

    @Autowired
    private PeakShiftTaskMapper peakShiftTaskMapper;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Autowired
    private PeakShiftTaskDetailMapper peakShiftTaskDetailMapper;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MessageSenderService msgSenderService;

    @Autowired
    private PeakShiftTaskSchduleJobServiceImpl peakShiftTaskSchduleJobService;

    @Autowired
    private TemplateStrategyMapper templateStrategyMapper;

    @Autowired
    private SouthFrameworkRpcImpl southFrameworkRpc;

    @Autowired
    private PeakShiftOperationRecordMapper peakShiftOperationRecordMapper;

    @Autowired
    private PeakShiftIssuedTaskDomain peakShiftIssuedTaskDomain;

    @Autowired
    private CollectorCacheManager collectorCacheManager;

    @Autowired
    private FieldCacheManager fieldCacheManager;

    @Autowired
    private PeakShiftFactory peakShiftFactory;

    @Override
    public List<PeakShiftTaskPo> checkTaskByname(String name) throws UedmException
    {
        return peakShiftTaskMapper.checkTaskByname(name);
    }

    @Override
    public List<PeakShiftDeviceChildBeanVo> getFileInfo(List<String> ids) throws UedmException
    {
        List<PeakShiftDeviceChildBeanVo> deviceBeanVos = new ArrayList<>();
        try
        {
            if (CollectionUtils.isEmpty(ids))
            {
                log.warn("peakShiftTaskServiceImpl getFileInfo -> parameter ids is empty");
                return deviceBeanVos;
            }

            //查询设备最新的、状态为success的文件ID与更新时间
            List<PeakShiftDeviceChildBeanVo> beanVos = peakShiftTaskDetailMapper.selectFileInfoList(ids,
                    PeakShiftDeviceStatusEnum.getSuccessId());
            if (!CollectionUtils.isEmpty(beanVos))
            {
                deviceBeanVos.addAll(beanVos);
            }

            log.info("peakShiftTaskServiceImpl getFileInfo -> deviceBeanVos", deviceBeanVos.size());
            return deviceBeanVos;
        }
        catch (Exception e)
        {
            log.error("PeakShiftTaskServiceImpl getFileInfo occur exception {}, {}", e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(),
                    DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    @Override
    public Map<String, Boolean> getOptionalDeviceStatus(List<String> ids) throws UedmException
    {
        Map<String, Boolean> deviceStatusMap = new HashMap<>();
        try
        {
            if (CollectionUtils.isEmpty(ids))
            {
                log.warn("peakShiftTaskServiceImpl getOptionalDeviceStatus -> parameter ids is empty");
                return deviceStatusMap;
            }

            //查询ids中状态为InProgress的
            List<String> idList = peakShiftTaskDetailMapper.selectOptionalDeviceStatus(ids,
                    PeakShiftDeviceStatusEnum.getInProgressId());
            log.info("peakShiftTaskServiceImpl getOptionalDeviceStatus -> idList, {}", idList.size());
            Set<String> idSet = new HashSet<>(idList);

            for (String id : ids)
            {
                //存在，则表示设备存在一个任务内的状态为待下发，不可选择
                if (idSet.contains(id))
                {
                    deviceStatusMap.put(id, false);
                }
                else
                {
                    deviceStatusMap.put(id, true);
                }
            }

            log.info("peakShiftTaskServiceImpl getOptionalDeviceStatus -> map, {}", deviceStatusMap.size());
            return deviceStatusMap;
        }
        catch (Exception e)
        {
            log.error("PeakShiftTaskServiceImpl getOptionalDeviceStatus occur exception {}, {}", e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(),
                    DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    @Override
    public int setDeviceStatus(String detailId, String status) throws UedmException
    {
        int result = 0;
        if (StringUtils.isAnyBlank(detailId, status))
        {
            log.error("device id or status is blank");
            return 0;
        }
        result = peakShiftTaskDetailMapper.updateDeviceStatus(detailId, status);
        updateTaskStatus(detailId);
        return result;
    }

    @Override
    public PeakShiftDeviceChildBeanVo getFileIdByDeviceId(String deviceId, String status) throws UedmException
    {
        if (StringUtils.isAnyBlank(deviceId, status))
        {
            log.error("device id or status is blank");
            return null;
        }
        PeakShiftDeviceChildBeanVo beanVo = peakShiftTaskDetailMapper.selectFileInfo(deviceId, status);
        return beanVo;
    }

    @Override
    public int updateInProgressDevice()
    {
        int result = 0;
        try
        {
            result = peakShiftTaskDetailMapper.updateInProgressDevice(PeakShiftDeviceStatusEnum.getFailId(),
                    PeakShiftDeviceStatusEnum.getInProgressId());
        }
        catch (Exception e)
        {
            log.error("change in progress status failed");
        }
        return result;
    }

    @Override
    public PeakShiftDeviceTaskBo selectLatestTaskByDeviceId(String deviceId) throws UedmException
    {
        PeakShiftDeviceTaskBo result = peakShiftTaskDetailMapper.selectLatestTaskByDeviceId(deviceId);
        return result;
    }

   /* public String toUnderlineCase(String camelCaseStr)
    {
        if (camelCaseStr == null)
        {
            return null;
        }
        // 将驼峰字符串转换成数组
        char[] charArray = camelCaseStr.toCharArray();
        StringBuffer buffer = new StringBuffer();
        //处理字符串
        for (int i = 0, l = charArray.length; i < l; i++)
        {
            if (charArray[i] >= 65 && charArray[i] <= 90)
            {
                buffer.append("_").append(charArray[i] += 32);
            }
            else
            {
                buffer.append(charArray[i]);
            }
        }
        String string = buffer.toString();
        //释放
        buffer.delete(0,buffer.length());
        return string;
    }*/

    @Override
    public Boolean duplicateNameCheck(String id, String name) throws UedmException
    {
        List<String> duplicateTaskNames = peakShiftTaskMapper.selectDuplicateNames(id, name);

        return duplicateTaskNames.size() > 0;
    }

    /**
     * 分页排序
     */
    public List<DeviceInfoVo> pagingAndSorting(List<DeviceInfoVo> deviceInfoVos,
            ServiceBaseInfoBean serviceBaseInfoBean, PeakShiftTaskDetailRequestBean requestBean)
    {
        if (!CollectionUtils.isEmpty(deviceInfoVos))
        {
            ComparatorUtil cmp = new ComparatorUtil(new String[] { "siteName", "deviceName" }, 1);
            if (StringUtils.isNotBlank(requestBean.getSortBy()))
            {
                if (SortEnum.getDescSortID().equals(requestBean.getOrder()))
                {
                    cmp = new ComparatorUtil(new String[] { requestBean.getSortBy() }, 0);
                }
                else
                {
                    cmp = new ComparatorUtil(new String[] { requestBean.getSortBy() }, 1);
                }
            }
            deviceInfoVos.sort(cmp);
        }
        if (serviceBaseInfoBean.isPage())
        {
            deviceInfoVos = PageUtil.getPageList(deviceInfoVos, serviceBaseInfoBean.getPageNo(),
                    serviceBaseInfoBean.getPageSize());
        }
        log.info("result size = {}", deviceInfoVos.size());
        return deviceInfoVos;
    }

    @Override
    public Pair<Integer, String> doStatusFlip(String id, String status, ServiceBaseInfoBean serviceBean)
            throws UedmException
    {
        try
        {
            // 查询任务当前状态
            PeakShiftTaskPo taskPo = peakShiftTaskMapper.selectById(id);

            // 当前任务状态可以翻转到目标状态
            if (PeakShiftTaskStatusEnum.statusFlipCheck(taskPo.getStatus(), status))
            {
                // 状态更新入库
                peakShiftTaskMapper.updateStatusById(id, status);
                // 记录状态更新成功日志
                PeakShiftTaskOperationMgr.statusChangeOperSuccessDetail(serviceBean, taskPo, status);
                String currentTime = dateTimeService.getCurrentTime();
                //如果生效时间小于当前时间立即下发
                if (currentTime.compareTo(taskPo.getEffectiveDate()) >= 0)
                {
                    peakShiftTaskSchduleJobService.PeakShiftTaskExectue();
                }
                return new MutablePair<>(0, "");
            }
            else
            {
                return new MutablePair<>(-402,
                        "Current Status is " + taskPo.getStatus() + ", Can Not Flip Status to " + status);
            }
        }
        catch (UedmException e)
        {
            log.error("PeakShiftIssuedTaskServiceImpl doStatusFlip occur UedmException {}", e.getMessage(), e);
            // 记录状态更新失败日志
            PeakShiftTaskOperationMgr.statusChangeOperFailDetail(serviceBean, id, e);
            throw e;
        }
    }

    @Override
    public List<DeviceLatestPeakShiftTaskVo> getDeviceLatestStrategyTaskInfo() throws IOException
    {
        return peakShiftTaskMapper.selectDeviceLatestTask();
    }

    private void addDeviceOnlineInfo(List<PeakShiftDeviceTaskBean> resultBeans, PeakShiftDeviceTaskVo requestBean,
            ServiceBaseInfoBean serviceBaseInfoBean, List<PeakShiftDeviceTaskBean> finalBeans) throws UedmException
    {
        String languageOption = serviceBaseInfoBean.getLanguageOption() == null ?
                LANGUAGE_OPTION_CN :
                serviceBaseInfoBean.getLanguageOption();
        String OnlineStatus = requestBean.getDeviceOnlineId();
        //获取设备id
        List<String> deviceIds = new ArrayList<>();
        try
        {
            for (PeakShiftDeviceTaskBean bean : resultBeans)
            {
                deviceIds.add(bean.getId());
            }
            log.info("addDeviceOnlineInfo deviceIds size : {}", deviceIds.size());
            //查询所有设备所对应的链路id
            List<SearchLinkBean> list = new ArrayList<>();
            BatchUtils.doInBatch(BATCH_NUMBER, deviceIds, (item) -> {
                List<SearchLinkBean> batchList = new ArrayList<>();
                try
                {
                    /* Started by AICoder, pid:23a7123135266001441509d160e419164cb77b42 */
                    List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(item);
                    batchList = collectorById.stream()
                            .map(x -> {
                                SearchLinkBean searchLinkBean = new SearchLinkBean();
                                searchLinkBean.setDeviceId(x.getId());
                                List<Map<String, Object>> linkInfo = (List<Map<String, Object>>) x.getLinkInfo();
                                Optional<Map<String, Object>> master = linkInfo.stream().filter(y -> y.containsValue("master")).findFirst();
                                if (master.isPresent()) {
                                    Map<String, Object> stringObjectMap = master.get();
                                    String linkId = stringObjectMap.keySet().iterator().next();
                                    searchLinkBean.setLinkId(linkId);
                                }
                                return searchLinkBean;
                            }).collect(Collectors.toList());
                    /* Ended by AICoder, pid:23a7123135266001441509d160e419164cb77b42 */

                    //batchList = siteSpBatteryRelatedRpc.getLinkIdByDeviceId(item);
                }
                catch (Exception e)
                {
                    log.error("", e);
                    throw new RuntimeException();
                }
                list.addAll(batchList);
            });
            log.info("getLinkIdByDeviceId linkList size : {}", list.size());
            //根据链路状态判断在线离线状态
            getLinkStatusByLinkId(list, OnlineStatus, languageOption, resultBeans, finalBeans);

        }
        catch (Exception e)
        {
            log.error("addDeviceOnlineInfo error info :", e);
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    "PeakShiftIssuedTaskServiceImpl addDeviceOnlineInfo failed");
        }

    }

    private void getLinkStatusByLinkId(List<SearchLinkBean> list, String onlineStatus, String languageOption,
            List<PeakShiftDeviceTaskBean> resultBeans, List<PeakShiftDeviceTaskBean> finalBeans) throws UedmException
    {
        try
        {
            //获取设备的链路状态
            Map<String, String> linkMap = southFrameworkRpc.queryAllLinkMonitor();
            for (SearchLinkBean searchBean : list)
            {
                String connectStatus = linkMap.get(searchBean.getLinkId());
                if (connectStatus != null)
                {
                    searchBean.setConnectStatus(connectStatus);
                }
            }
            log.debug("getLinkStatusByLinkId list info {}", list);
            getFinalOnlineStatus(list, onlineStatus, languageOption, resultBeans, finalBeans);
        }
        catch (Exception e)
        {
            log.error("getLinkStatusByLinkId error info :", e);
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    "PeakShiftIssuedTaskServiceImpl getFinalStatusByOnlineStatusAndLinkId failed");
        }
    }

    private void getFinalOnlineStatus(List<SearchLinkBean> list, String onlineStatus, String languageOption,
            List<PeakShiftDeviceTaskBean> resultBeans, List<PeakShiftDeviceTaskBean> finalBeans)
    {
        if (ONLINE_STATUS_US.equals(onlineStatus))
        {
            List<SearchLinkBean> onlineList = list.stream()
                    .filter(item -> LINK_CONNECT_STATUS.equals(item.getConnectStatus())).collect(Collectors.toList());
            addOnlineInfo(languageOption, resultBeans, finalBeans, onlineList);
        }
        else if (OFFLINE_STATUS_US.equals(onlineStatus))
        {
            List<SearchLinkBean> offlineList = list.stream()
                    .filter(item -> !LINK_CONNECT_STATUS.equals(item.getConnectStatus())).collect(Collectors.toList());
            addOnlineInfo(languageOption, resultBeans, finalBeans, offlineList);
        }else
        {
            addOnlineInfo(languageOption, resultBeans, finalBeans, list);
        }
    }
    

    private void addOnlineInfo(String languageOption, List<PeakShiftDeviceTaskBean> resultBeans,
            List<PeakShiftDeviceTaskBean> finalBeans, List<SearchLinkBean> list)
    {
        for (SearchLinkBean searchLinkBean : list)
        {
            for (PeakShiftDeviceTaskBean resultBean : resultBeans)
            {
                if (searchLinkBean.getDeviceId().equals(resultBean.getId()))
                {
                    if (LINK_CONNECT_STATUS.equals(searchLinkBean.getConnectStatus()))
                    {
                        addOnlineInfoByLanguage(languageOption, resultBean);
                    }else
                    {
                        addOfflineInfoByLanguage(languageOption, resultBean);
                    }
                    finalBeans.add(resultBean);
                }
            }
        }
    }

    private void addOfflineInfoByLanguage(String languageOption, PeakShiftDeviceTaskBean resultBean)
    {
        resultBean.setDeviceOnlineId(OFFLINE_STATUS_US);
        if (languageOption.equals(LANGUAGE_OPTION_US))
        {
            resultBean.setDeviceOnlineName(OFFLINE_STATUS_US);
        }
        else
        {
            resultBean.setDeviceOnlineName(OFFLINE_STATUS_CN);
        }
    }

    private void addOnlineInfoByLanguage(String languageOption, PeakShiftDeviceTaskBean resultBean)
    {
        resultBean.setDeviceOnlineId(ONLINE_STATUS_US);
        if (languageOption.equals(LANGUAGE_OPTION_US))
        {
            resultBean.setDeviceOnlineName(ONLINE_STATUS_US);
        }
        else
        {
            resultBean.setDeviceOnlineName(ONLINE_STATUS_CN);
        }
    }

    public List<String> filterDeviceIdByPositionAndTemplate(List<String> ids, List<String> position,
            String templateStrategyId) throws UedmException
    {
        //idItem集合用来暂存过滤一次后的设备id
        List<String> idItem = new ArrayList<>();
        //idResult集合返回结果
        List<String> idResult = new ArrayList<>();
        try
        {
            //获取所有监控设备的idPath
            Map<String, List<String>> map = collectorCacheManager.queryAll().stream().filter(Objects::nonNull).map(CollectorEntity.class::cast)
                    .filter(it -> it.getPathId() != null)
                    .collect(Collectors.toMap(CollectorEntity::getId,
                            (collectorEntity -> Arrays.asList(collectorEntity.getPathId())), (oldValue, newValue) -> newValue));
            if (map.isEmpty())
            {
                throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "IdPath is not null");
            }
            filterByTemplateStrategyId(ids, templateStrategyId, idItem, map);
            if (!CollectionUtils.isEmpty(position))
            {
                filterByPosition(idResult, position, idItem, map);
                return idResult;
            }
            return idItem;
        }
        catch (Exception e)
        {
            log.error("filterDeviceIdByPositionAndTemplate error info :",e);
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    "PeakShiftIssuedTaskServiceImpl filterDeviceIdByPositionAndTemplate failed");
        }
    }

    private void filterByTemplateStrategyId(List<String> ids, String templateStrategyId, List<String> idItem,
            Map<String, List<String>> map)
    {
        String logicGroup = peakShiftTaskDetailMapper.getLogicGroupByTemplateStrategyId(templateStrategyId);
        List<String> logicGroupList = JSON.parseArray(logicGroup, String.class);
        log.debug("logicGroupList :{}", logicGroupList);
        if (CollectionUtils.isEmpty(logicGroupList) || logicGroupList.contains(GlobalOptional.GLOBAL_ROOT))
        {
            idItem.addAll(ids);
            return;
        }

        for (String group : logicGroupList)
        {
            for (String id : ids)
            {
                List<String> value = map.getOrDefault(id,new ArrayList<>());
                if ( value != null && value.contains(group))
                {
                    idItem.add(id);
                }
            }
        }
    }

    private void filterByPosition(List<String> idResult, List<String> position, List<String> idItem,
            Map<String, List<String>> map)
    {
        for (String po : position)
        {
            for (String id : idItem)
            {
                List<String> value = map.getOrDefault(id,new ArrayList<>());
                if (value != null && value.contains(po))
                {
                    idResult.add(id);
                }
            }
        }
    }

    public List<String> filterDeviceIdByEnablePeak(List<String> idList) throws UedmException
    {
        try
        {
            List<String> ids = new ArrayList<>();
            //获取缓存中的所有设备信息
            List<DevicePeakCacheInfoBean> list = configurationManagerRpc.queryAllList();
            //过滤其中支持错峰的设备
            List<DevicePeakCacheInfoBean> deviceList = list.stream().filter(item -> item.getEnablePeak())
                    .collect(Collectors.toList());
            //将权限下的设备ID集合放入支持错峰的设备中过滤
            for (String id : idList)
            {
                for (DevicePeakCacheInfoBean bean : deviceList)
                {
                    if (id.equals(bean.getDeviceId()))
                    {
                        ids.add(id);
                    }
                }
            }
            return ids;
        }
        catch (Exception e)
        {
            log.error("filterDeviceIdByEnablePeak error info :",e);
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    "PeakShiftIssuedTaskServiceImpl filterDeviceIdByEnablePeak failed");
        }
    }

    @Override
    public PageInfo<PeakShiftDeviceTaskBean> getDeviceTaskBeansByDeviceList(PeakShiftDeviceTaskVo requestBean,
                                                                            ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        if (null == serviceBaseInfoBean) {
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    PARAM_NOT_NULL);
        }
        //requestBean.setDeviceIds(idList);
        List<String> idList = requestBean.getDeviceIds() == null ? new ArrayList<>() : requestBean.getDeviceIds();

        // 采集器站点关联信息
        Map<String, DeviceSiteRelationDto> deviceSiteRelationDtoMap = getCollectorSiteRelation(idList);

        Map<String, PeakShiftDeviceTaskBean> deviceTaskBeanMap = new HashMap<>();
        List<PeakShiftDeviceTaskBean> deviceTaskBeans = new ArrayList<>();
        //条件查询
        List<PeakShiftDeviceTaskBean> deviceTaskBeansFromDB = peakShiftTaskDetailMapper.selectDeviceTaskInfoByCondition(
                requestBean);
        for (PeakShiftDeviceTaskBean bean : deviceTaskBeansFromDB) {
            TemplateStrategyDetailBo templateStrategyDetailBo = peakShiftIssuedTaskDomain.getHistoryStrategyByTaskId(bean.getTaskId());
            bean.setPriceIntervalName(templateStrategyDetailBo.getSeasonStrategyName());
        }

        //将详情Bean中的设备站点信息补全
        addSiteInfo(deviceSiteRelationDtoMap, deviceTaskBeanMap, deviceTaskBeansFromDB);
        log.debug("deviceTaskBeansFromDB:{}", deviceTaskBeansFromDB);

        //条件过滤,有这几个条件的则只查数据库里面的数据，否则就查全部设备
        int deviceStatusExist = CollectionUtils.isEmpty(requestBean.getDeviceStatus()) ? 0 : 1;
        int taskStatusExist = CollectionUtils.isEmpty(requestBean.getTaskStatus()) ? 0 : 1;
        int expirationDateStartExist = StringUtils.isEmpty(requestBean.getExpirationDateStart()) ? 0 : 1;
        int expirationDateEndExist = StringUtils.isEmpty(requestBean.getExpirationDateEnd()) ? 0 : 1;
        boolean conditionFlag =
                (deviceStatusExist + taskStatusExist + expirationDateStartExist + expirationDateEndExist) != 0;

        log.info("conditionFlag:{}", conditionFlag);
        getDeviceTaskBeanByCondition(idList, deviceSiteRelationDtoMap, deviceTaskBeanMap, deviceTaskBeans,
                deviceTaskBeansFromDB, conditionFlag);
        log.debug("deviceTaskBeans:{}", deviceTaskBeans);

        String siteName = requestBean.getSiteName();
        String deviceName = requestBean.getDeviceName();

        List<PeakShiftDeviceTaskBean> finalBeans = new ArrayList<>();
        addDeviceOnlineInfo(deviceTaskBeans, requestBean, serviceBaseInfoBean, finalBeans);

        //如果站点名称和设备名称有需要模糊查询，则需如下过滤
        List<PeakShiftDeviceTaskBean> result = getPeakShiftDeviceTaskBeans(finalBeans, siteName, deviceName);

        //按位置排序
        result = result.stream().filter(x -> x.getPosition() != null)
                .sorted(Comparator.comparing(PeakShiftDeviceTaskBean::getPosition)).collect(Collectors.toList());
        log.debug("result:{}", result);
        PageInfo<PeakShiftDeviceTaskBean> peakShiftTaskVoPageInfo = getPeakShiftDeviceTaskBeanPageInfo(
                serviceBaseInfoBean, result);
        return peakShiftTaskVoPageInfo;
    }

    public Map<String, DeviceSiteRelationDto> getCollectorSiteRelation(List<String> idList) throws UedmException {
        /* Started by AICoder, pid:26c71yecd4bb4f71445d0afff08a8f4db302548f */
        // 获取站点
        Map<String, ResourceBaseEntity> siteMap = null;
        try {
            siteMap = fieldCacheManager.queryAll().stream()
                    .filter(Objects::nonNull)
                    .map(ResourceBaseEntity.class::cast)
                    .collect(Collectors.toMap(ResourceBaseEntity::getId, Function.identity(), (k1, k2) -> k2));
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("getDeviceTaskBeansByDeviceList get field cache error", e);
            throw new UedmException(e.getErrorId(), "get field cache error!");
        }

        Map<String, DeviceSiteRelationDto> deviceSiteRelationDtoMap = new HashMap<>();
        List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(idList);
        for (CollectorEntity collectorEntity : collectorById) {
            String id = collectorEntity.getId();
            String[] pathId = collectorEntity.getPathId();
            // 设备类型
            String deviceType = CollectorUtils.parseDeviceType(collectorEntity.getMoc(), collectorEntity.getProtocolAttribute());

            DeviceSiteRelationDto siteRelationDto = new DeviceSiteRelationDto();
            siteRelationDto.setDeviceId(id);
            siteRelationDto.setDeviceName(collectorEntity.getName());
            siteRelationDto.setNamePath(collectorEntity.getPathName());
            siteRelationDto.setIdPath(collectorEntity.toStringPathId());
            siteRelationDto.setDeviceModule(collectorEntity.getAdapterId());
            siteRelationDto.setDeviceType(deviceType);

            // 采集器站点关联关系
            if (pathId != null) {
                for (String path : pathId) {
                    ResourceBaseEntity site = siteMap.getOrDefault(path, new ResourceBaseEntity());
                    siteRelationDto.setSiteId(site.getId());
                    siteRelationDto.setSiteName(site.getName());
                }
            }
            deviceSiteRelationDtoMap.put(id, siteRelationDto);
        }
        /* Ended by AICoder, pid:26c71yecd4bb4f71445d0afff08a8f4db302548f */

        log.debug("=============== new cache deviceSiteRelationDtoMap:{}", deviceSiteRelationDtoMap);
        log.debug("=============== new cache deviceSiteRelationDtoMap size:{}", deviceSiteRelationDtoMap.size());
        log.debug("deviceSiteRelationDtoMap:{}", deviceSiteRelationDtoMap);
        return deviceSiteRelationDtoMap;
    }

    @NotNull
    private PageInfo<PeakShiftDeviceTaskBean> getPeakShiftDeviceTaskBeanPageInfo(
            ServiceBaseInfoBean serviceBaseInfoBean, List<PeakShiftDeviceTaskBean> result)
    {
        //分页
        PageInfo<PeakShiftDeviceTaskBean> peakShiftTaskVoPageInfo = new PageInfo<>(result);
        peakShiftTaskVoPageInfo.setTotal(result.size());
        if (serviceBaseInfoBean.isPage())
        {
            result = PageUtil.getPageList(result, serviceBaseInfoBean.getPageNo(), serviceBaseInfoBean.getPageSize());
        }
        peakShiftTaskVoPageInfo.setList(result);
        return peakShiftTaskVoPageInfo;
    }

    private void addSiteInfo(Map<String, DeviceSiteRelationDto> deviceSiteRelationDtoMap,
            Map<String, PeakShiftDeviceTaskBean> deviceTaskBeanMap, List<PeakShiftDeviceTaskBean> deviceTaskBeansFromDB)
    {
        for (PeakShiftDeviceTaskBean deviceTaskBean : deviceTaskBeansFromDB) {
            String deviceId = deviceTaskBean.getId();
            DeviceSiteRelationDto dto = deviceSiteRelationDtoMap.getOrDefault(deviceId, new DeviceSiteRelationDto());
            deviceTaskBean.setName(dto.getDeviceName());
            deviceTaskBean.setPosition(dto.getNamePath());
            deviceTaskBean.setSiteName(dto.getSiteName());
            deviceTaskBean.setDeviceType(dto.getDeviceType());
            deviceTaskBean.setDeviceTypeName(dto.getDeviceType());
            deviceTaskBeanMap.put(deviceId, deviceTaskBean);
        }
    }

    private void getDeviceTaskBeanByCondition(List<String> idList,
            Map<String, DeviceSiteRelationDto> deviceSiteRelationDtoMap,
            Map<String, PeakShiftDeviceTaskBean> deviceTaskBeanMap, List<PeakShiftDeviceTaskBean> deviceTaskBeans,
            List<PeakShiftDeviceTaskBean> deviceTaskBeansFromDB, boolean conditionFlag)
    {
        if (conditionFlag) {
            deviceTaskBeans.addAll(deviceTaskBeansFromDB);
        } else {
            for (String deviceId : idList) {
                PeakShiftDeviceTaskBean deviceTaskBean = deviceTaskBeanMap.get(deviceId);
                if (deviceTaskBean != null) {
                    //如果找到了deviceID对应的bean，则直接加入
                    deviceTaskBeans.add(deviceTaskBean);
                } else {
                    //如果没有找到deviceID对应的bean，则找到前三项赋值后加入
                    PeakShiftDeviceTaskBean bean = new PeakShiftDeviceTaskBean();
                    bean.setId(deviceId);
                    DeviceSiteRelationDto dto = deviceSiteRelationDtoMap.getOrDefault(deviceId,
                            new DeviceSiteRelationDto());
                    bean.setName(dto.getDeviceName());
                    bean.setPosition(dto.getNamePath());
                    bean.setSiteName(dto.getSiteName());
                    bean.setDeviceType(dto.getDeviceType());
                    bean.setDeviceTypeName(dto.getDeviceType());
                    deviceTaskBeans.add(bean);
                }

            }

        }
    }

    @Override
    public void manualRetry(PeakShiftTaskRetryBean retryBean, ServiceBaseInfoBean serviceBaseInfoBean)
            throws UedmException
    {
        log.info("manualRetry retryBean = {}", retryBean);
        PeakShiftTaskPo taskPo = peakShiftTaskMapper.selectById(retryBean.getTaskId());
        //任务转为下发中
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(GlobalConstants.SYSTEM, GlobalConstants.SYSTEM_IP, "");
        PeakShiftTaskOperationMgr.initStatusChangeOperLogBean(serviceBean);
        peakShiftTaskMapper.updateStatusById(taskPo.getId(), PeakShiftTaskStatusEnum.EXECUTE.getId());
        // 记录状态更新成功日志
        PeakShiftTaskOperationMgr.statusChangeOperSuccessDetail(serviceBean, taskPo, PeakShiftTaskStatusEnum.EXECUTE.getId());
        //发送操作日志
        String operMsg = JSON.toJSONString(serviceBean.getOperlogBean());
        msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        //设备转为下发中
        String currentTime = dateTimeService.getCurrentTime();
        peakShiftTaskDetailMapper.updateStatusToProgress(taskPo.getId(), retryBean.getDeviceIds(), currentTime);
        //下发
        peakShiftTaskSchduleJobService.sendMsgtoDevice(retryBean.getDeviceIds(), taskPo,
                serviceBaseInfoBean.getLanguageOption());
    }

    @Override
    public void delete(String id, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        PeakShiftTaskPo peakShiftTaskPo = peakShiftTaskMapper.selectById(id);
        if (peakShiftTaskPo != null)
        {
            String status = peakShiftTaskPo.getStatus();
            if (!PeakShiftTaskStatusEnum.FAIL.getId().equals(status) && !PeakShiftTaskStatusEnum.EDIT.getId()
                    .equals(status))
            {
                // 记录删除失败日志
                UedmException e = new UedmException(-201, status + "task can not be deleted");
                PeakShiftTaskOperationMgr.deleteTaskOperFailDetail(serviceBean, id, e);

                throw e;
            }
            peakShiftTaskMapper.deleteById(id);

            IssuedTaskStrategyRelationPo relationPo = templateStrategyMapper.selectIssuedTaskStrategyRelation(id);

            //查询除本身任务使用快照之外，有没有其他任务使用快照
            IssuedTaskStrategyRelationPo queryTaskBean = new IssuedTaskStrategyRelationPo();
            queryTaskBean.setTaskId(id);
            queryTaskBean.setVersion(relationPo.getVersion());
            queryTaskBean.setTemplateStrategyId(relationPo.getTemplateStrategyId());
            List<IssuedTaskStrategyRelationPo> otherTask = templateStrategyMapper.selectRelationExceptSelf(
                    queryTaskBean);
            if (otherTask.size() == 0)
            {
                //如果没有其他模板要用，才能删除
                templateStrategyMapper.deleteIssuedHistoryDetail(relationPo.getTemplateStrategyId(),
                        relationPo.getVersion());
            }

            //删除taskId对应的relation
            templateStrategyMapper.deleteIssuedTaskStrategyRelationByTaskId(id);

            // 记录删除成功日志
            PeakShiftTaskOperationMgr.deleteTaskOperSuccessDetail(serviceBean, peakShiftTaskPo);
        }
    }

    private List<PeakShiftDeviceTaskBean> getPeakShiftDeviceTaskBeans(List<PeakShiftDeviceTaskBean> deviceTaskBeans,
            String siteName, String deviceName)
    {
        List<PeakShiftDeviceTaskBean> result = new ArrayList<>();
        if (StringUtils.isEmpty(siteName))
        {
            if (StringUtils.isEmpty(deviceName))
            {
                result = deviceTaskBeans;
            }
            //有设备名称过滤
            else
            {
                //获取任务idSet，经过设备名称过滤之后的task_id
                result = deviceTaskBeans.stream().filter(x -> StringUtils.containsIgnoreCase(x.getName(), deviceName))
                        .collect(Collectors.toList());

            }
        }
        //条件中有siteName，则需要过滤
        else
        {
            if (StringUtils.isEmpty(deviceName))
            {
                //获取任务idSet，经过站点名称过滤之后的task_id
                result = deviceTaskBeans.stream().filter(x -> StringUtils.contains(x.getSiteName(), siteName))
                        .collect(Collectors.toList());

            }
            else
            {
                //获取任务idSet，经过站点名称、设备名称过滤之后的task_id
                result = deviceTaskBeans.stream().filter(x -> StringUtils.contains(x.getName(), deviceName))
                        .filter(x -> StringUtils.contains(x.getSiteName(), siteName)).collect(Collectors.toList());

            }
        }
        return result;
    }

    public String divsion(int num1, int total)
    {
        NumberFormat instance = NumberFormat.getInstance();
        instance.setMaximumFractionDigits(2);
        DecimalFormat df = new DecimalFormat("##.##%");
        return df.format((float) num1 / (float) total);
    }

    private synchronized void updateTaskStatus(String detailId) throws UedmException
    {
        String taskId = peakShiftTaskDetailMapper.selectTaskIdByDetailId(detailId);
        DeviceStatusCountBean countBean = peakShiftTaskDetailMapper.selectCountGroupByStatus(taskId);
        Integer total = peakShiftTaskDetailMapper.selectTotalByTaskId(taskId);
        int incomplete = countBean.getPending() + countBean.getProgress();

        if (incomplete == 0)
        {
            List<String> failIds = peakShiftTaskDetailMapper.selectlDeviceByTaskIdAndStatus(taskId,
                    PeakShiftDeviceStatusEnum.getFailId());
            String status;
            //全部成功
            if (CollectionUtils.isEmpty(failIds))
            {
                status = PeakShiftTaskStatusEnum.SUCCESS.getId();
            }
            //全部失败
            else if (total == failIds.size())
            {
                status = PeakShiftTaskStatusEnum.FAIL.getId();
            }
            else //部分成功
            {
                status = PeakShiftTaskStatusEnum.PARTIAL_SUCCESS.getId();
            }
            peakShiftTaskMapper.updateStatusById(taskId, status);
            List<PeakShiftTaskDetailPo> peakShiftTaskDetailPos = peakShiftTaskDetailMapper.selectByTaskId(
                    taskId, new ArrayList<>());
            List<String> deviceIds = peakShiftTaskDetailPos.stream().map(PeakShiftTaskDetailPo::getDeviceId)
                    .collect(Collectors.toList());
            sendMsgToDevice(deviceIds);
            //更新履历实际执行时间
            updateExecTime(taskId);
        }
    }

    /**
     * 通知设备上传文件
     * @param deviceIds
     * @throws UedmException
     */
    public void sendMsgToDevice(List<String> deviceIds) throws UedmException {
        log.info("deviceIds size :{}", deviceIds.size());
        List<CollectorEntity> list = collectorCacheManager.getAllCollector();
        try {
            if (list != null && list.size()>0) {
                /* Started by AICoder, pid:q280caab22yf23b1432708e7c0ed5e10c862fe99 */
                for (CollectorEntity bean : list) {
                    CollectorDSEntity collectorDSEntity = CollectorDSEntity.convertEntityToBean(bean);
                    collectorDSEntity.parseMasterLink();
                    collectorDSEntity.buildProtocol();

                    Map<String, Object> map = null;
                    try {
                        map = JsonUtils.jsonToObject(JSON.toJSONString(collectorDSEntity), Map.class);
                    } catch (com.zte.uedm.basis.exception.UedmException e) {
                        log.error("setOmpValue json convert error", e);
                        throw new UedmException(-1, "json to object error!");
                    }
                    RemoteControlBean remoteControlBean;
                    remoteControlBean = new RemoteControlBean();
                    remoteControlBean.setOmpId(OMPID);
                    remoteControlBean.setValue("0");
                    remoteControlBean.setRelationPosition("1");
                    remoteControlBean.setPointIndex("1");
                    remoteControlBean.setDevice(map);
                    log.info("MonitorDeviceBaseBean:{}", map);
                    msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                            jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
                }
                /* Ended by AICoder, pid:q280caab22yf23b1432708e7c0ed5e10c862fe99 */
            }
        } catch (Exception e) {
            log.error("PeakShiftDeviceStateExecutor-sendMsgtoDevice:getMonitorDevicebyDeviceIds  is error!", e);
        }
    }

    public void updateExecTime(String taskId)
    {
        List<PeakShiftTaskDetailPo> detailPos = peakShiftTaskDetailMapper.selectSuccessDevices(taskId);
        if (!CollectionUtils.isEmpty(detailPos))
        {
            for (PeakShiftTaskDetailPo detailPo : detailPos)
            {
                peakShiftOperationRecordMapper.updateExecTime(detailPo.getDeviceId(),detailPo.getExecTime());
            }
        }
    }

    /* Started by AICoder, pid:ocff4gd927x3c27140f10b1e40e9612aa46467ef */
    @Override
    public TemplateStrategyDetailEntity queryCsuDeviceTransferParam(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        // 获取采集器类型
        Map<String, String> collectorsTypeMap = collectorCacheManager.getCollectorsType(Collections.singleton(deviceId));
        String collectorType = collectorsTypeMap.get(deviceId);
        PeakShiftCommonService peakShiftCommonService = peakShiftFactory.generateByDeviceType(collectorType);
        if (peakShiftCommonService != null) {
            return peakShiftCommonService.getLatestStrategy(deviceId, serviceBaseInfoBean);
        }
        return new TemplateStrategyDetailEntity();
    }
    /* Ended by AICoder, pid:ocff4gd927x3c27140f10b1e40e9612aa46467ef */
}