package com.zte.uedm.battery.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.domain.BattAssetAttributeDomain;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.mapper.BatteryWorkConditionDimensionsMapper;
import com.zte.uedm.battery.service.BatteryWorkConditionDimensionsDatesCache;
import com.zte.uedm.battery.service.BatteryWorkConditionDimensionsService;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.consts.asset.AssetModelAttributeIdConstants;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 电池管理配置
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = UedmException.class)
public class BatteryWorkConditionDimensionsServiceImpl implements BatteryWorkConditionDimensionsService
{

    @Autowired
    private BatteryWorkConditionDimensionsMapper batteryWorkConditionDimensionsMapper;
    @Autowired
    private BatteryWorkConditionDimensionsDatesCache batteryWorkConditionDimensionsDatesCache;
    @Autowired
    private MessageSenderService msgSenderService;
    @Autowired
    private BattAssetAttributeDomain battAssetAttributeDomain;
    @Autowired
    private BattAssetDomain battAssetDomain;

    @Override
    public int updateWorkConditionConfig(List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> beanList,String userName) throws UedmException
    {
        int total = 0;
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean = new BatteryOverviewWorkConditionDimensionsDatesBean() ;
        log.info("update begin " );
        for(BatteryWorkConditionDimensionsUpdateRequestBeanBean listBatteryOverviewBean : beanList){
            String id = listBatteryOverviewBean.getId();
            log.info(id);
            int sequence = listBatteryOverviewBean.getSequence();
            boolean enable = listBatteryOverviewBean.getEnable();
            batteryOverviewWorkConditionDimensionsDatesBean.setId(id);
            batteryOverviewWorkConditionDimensionsDatesBean.setUserName(userName);
            batteryOverviewWorkConditionDimensionsDatesBean.setEnable(enable);
            batteryOverviewWorkConditionDimensionsDatesBean.setSequence(sequence);
            Calendar ca = Calendar.getInstance();
            batteryOverviewWorkConditionDimensionsDatesBean.setGmtModified(ca.getTime() );
            batteryWorkConditionDimensionsMapper.updateWorkConditionConfig(batteryOverviewWorkConditionDimensionsDatesBean);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC ,
                    JSON.toJSONString(batteryOverviewWorkConditionDimensionsDatesBean));
            total ++;
            log.info("update  "+total+" data");
        }

        log.info("update finished " );
        return total;
    }

    @Override
    public List<BatteryOverviewWorkConditionDimensionsDatesBean> selectWorkConditionConfig(BatteryOverviewBeanVo batteryOverviewBeanVo,String languageOption) throws UedmException
    {
        List<BatteryOverviewWorkConditionDimensionsDatesBean> list = new ArrayList<>();
        try {
            list = batteryWorkConditionDimensionsMapper.selectWorkConditionConfig(batteryOverviewBeanVo);
            List<String> ids = list.stream().map(BatteryOverviewWorkConditionDimensionsDatesBean::getId).collect(Collectors.toList());
            Map<String, BatteryWorkConditionDimensionsDatesBean> allBatteryWorkConditionDimensionsDates = batteryWorkConditionDimensionsDatesCache.getAllBatteryWorkConditionDimensionsDates();
            if (allBatteryWorkConditionDimensionsDates.size() != list.size() || !checkDimIdExist(ids, allBatteryWorkConditionDimensionsDates.keySet())){
                //数据库中保存的id与实际id数据不一致，清空数据
                int deleteNum = batteryWorkConditionDimensionsMapper.deleteWorkConditionConfigByUserName(batteryOverviewBeanVo.getUserName());
                log.info("selectWorkConditionConfig deleteOverviewConfigByUserName -> deleteNum:{}",deleteNum);
                list = new ArrayList<>();
            }
            for (int i = 0; i < list.size(); i++) {
                BatteryOverviewWorkConditionDimensionsDatesBean listBatteryOverviewWorkConditionDimensionsDatesBean = list.get(i);
                String id = listBatteryOverviewWorkConditionDimensionsDatesBean.getId();
                String name = listBatteryOverviewWorkConditionDimensionsDatesBean.getName();
                String[] ceName = name.split(",");
                if (StringUtils.isBlank(languageOption)){
                    languageOption = "";
                }
                for (String names : ceName) {
                        if (names.contains(languageOption)) {
                            if (names.contains("en-US")) {
                                String[] returnNames = names.split(":");
                                String returnName = returnNames[1].replace("}", "");
                                returnName = returnName.replace("{", "");
                                returnName=returnName.replace("\"","");
                                name = returnName;
                            } else {
                                String[] returnNames = names.split(":");
                                String returnName = returnNames[1].replace("}", "");
                                returnName = returnName.replace("{", "");
                                returnName=returnName.replace("\"","");
                                name = returnName;
                            }
                        }
                    }
                    int defaultIndex = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).getDefaultIndex();
                    boolean defaultEnable = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultEnable();
                    boolean defaultFixed = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultFixed();
                    listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultIndex(defaultIndex);
                    listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultEnable(defaultEnable);
                    listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultFixed(defaultFixed);
                    listBatteryOverviewWorkConditionDimensionsDatesBean.setName(name);

                    // 处理额定容量-资产属性未开启
                    dealAssetAttributeShow(listBatteryOverviewWorkConditionDimensionsDatesBean, id);

                    list.set(i, listBatteryOverviewWorkConditionDimensionsDatesBean);
                }
                return list;
            }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB
                    .getDesc());
        }
    }

    private boolean checkDimIdExist(List<String> poIds, Set<String> allDimIds) {
        for (String poId: poIds)
        {
            if (!allDimIds.contains(poId))
            {
                log.info("selectWorkConditionConfig checkDimIdExist -> Data is wrong.");
                return false;
            }
        }
        return true;
    }

    @Override
    public BatteryOverviewWorkConditionDimensionsDatesBean selectWorkConditionConfigById(BatteryOverviewBeanVo batteryOverviewBeanVo) throws UedmException
    {
        try
        {
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean = batteryWorkConditionDimensionsMapper.selectWorkConditionConfigById(batteryOverviewBeanVo);
            String id = batteryOverviewWorkConditionDimensionsDatesBean.getId();
            int defaultIndex = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).getDefaultIndex();
            boolean defaultEnable = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultEnable();
            boolean defaultFixed = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultFixed();
            batteryOverviewWorkConditionDimensionsDatesBean.setDefaultIndex(defaultIndex);
            batteryOverviewWorkConditionDimensionsDatesBean.setDefaultEnable(defaultEnable);
            batteryOverviewWorkConditionDimensionsDatesBean.setDefaultFixed(defaultFixed);
            return batteryOverviewWorkConditionDimensionsDatesBean;
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB
                    .getDesc());
        }
    }

    @Override
    public List<BatteryOverviewWorkConditionDimensionsDatesBean> searchWorkConditionConfig(BatteryOverviewBeanVo batteryOverviewBeanVo,String languageOption) throws UedmException
    {
        try
        {
            List<BatteryOverviewWorkConditionDimensionsDatesBean> list = batteryWorkConditionDimensionsMapper.searchWorkConditionConfig(batteryOverviewBeanVo);
            for (int i = 0; i < list.size(); i++) {
                BatteryOverviewWorkConditionDimensionsDatesBean listBatteryOverviewWorkConditionDimensionsDatesBean = list.get(i);
                String id = listBatteryOverviewWorkConditionDimensionsDatesBean.getId();
                String name = listBatteryOverviewWorkConditionDimensionsDatesBean.getName();
                String []ceName = name.split(",");
                for (String names :
                        ceName) {
                    if (names.contains(languageOption)) {
                            if (names.contains("en-US")){
                                String []returnNames = names.split(":");
                                String returnName=returnNames[1].replace("}","");
                                returnName=returnName.replace("{","");
                                returnName=returnName.replace("\"","");
                                name = returnName;
                            }else {
                                String []returnNames = names.split(":");
                                String returnName=returnNames[1].replace("}","");
                                returnName=returnName.replace("{","");
                                returnName=returnName.replace("\"","");
                                name = returnName;
                            }
                    }
                }
                int defaultIndex = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).getDefaultIndex();
                boolean defaultEnable = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultEnable();
                boolean defaultFixed = batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(id).isDefaultFixed();
                listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultIndex(defaultIndex);
                listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultEnable(defaultEnable);
                listBatteryOverviewWorkConditionDimensionsDatesBean.setDefaultFixed(defaultFixed);
                listBatteryOverviewWorkConditionDimensionsDatesBean.setName(name);
                // 处理额定容量-资产属性未开启
                dealAssetAttributeShow(listBatteryOverviewWorkConditionDimensionsDatesBean, id);
            }
            return list;
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB
                    .getDesc());
        }
    }

    public void dealAssetAttributeShow(BatteryOverviewWorkConditionDimensionsDatesBean listBatteryOverviewWorkConditionDimensionsDatesBean, String id)
    {
        if(batteryWorkConditionDimensionsDatesCache.matchRatedCapKey(id))
        {
            // 资产功能关闭时，该维度返回为false
            if(!battAssetDomain.selectAssetEnable())
            {
                listBatteryOverviewWorkConditionDimensionsDatesBean.setAssetAttributeShow(false);
            }

            String ratedCapAttributeId = AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BATT_RATED_CAP;
            Map<String, Boolean> attributeMap = battAssetAttributeDomain.selectAttributeEnable(Arrays.asList(ratedCapAttributeId),new ServiceBaseInfoBean(HeaderUtils.ROOT_USER,""));
            log.info("attributeMap.size={}", attributeMap.size());
            if(attributeMap.get(ratedCapAttributeId) != null && !attributeMap.get(ratedCapAttributeId))
            {
                listBatteryOverviewWorkConditionDimensionsDatesBean.setAssetAttributeShow(false);
            }
        }
    }


    @Override
    public void insertDefaultData(String useraName)  {
        batteryWorkConditionDimensionsDatesCache.getAllBatteryWorkConditionDimensionsDates().forEach((k, v) ->{
            int defaultIndex = v.getDefaultIndex();
            boolean defaultEnable = v.isDefaultEnable();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean.setId(k);
            batteryOverviewWorkConditionDimensionsDatesBean.setSequence(defaultIndex);
            batteryOverviewWorkConditionDimensionsDatesBean.setEnable(defaultEnable);
            batteryOverviewWorkConditionDimensionsDatesBean.setName(v.getName());
            batteryOverviewWorkConditionDimensionsDatesBean.setCreator(useraName);
            batteryOverviewWorkConditionDimensionsDatesBean.setUserName(useraName);
            Calendar ca = Calendar.getInstance();
            log.info("gmt  create time  = "+ JSON.toJSONString(ca.getTime()));
            batteryOverviewWorkConditionDimensionsDatesBean.setGmtCreate(ca.getTime());
            batteryWorkConditionDimensionsMapper.insertDefaultData(batteryOverviewWorkConditionDimensionsDatesBean);

        });
    }
}
