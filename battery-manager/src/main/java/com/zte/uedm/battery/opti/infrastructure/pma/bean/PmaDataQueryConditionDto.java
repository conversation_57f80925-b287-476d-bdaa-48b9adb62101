package com.zte.uedm.battery.opti.infrastructure.pma.bean;

import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import lombok.Getter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * pma历史数据查询sql条件
 */
@Getter
@ToString
public class PmaDataQueryConditionDto
{
    /**
     * mtId
     */
    @NotBlank(message = "mtId can not be empty")
    private String mtId;

    /**
     * 表名
     */
    @NotBlank(message = "tableName can not be empty")
    private String tableName;

    /**
     * 监控对象id
     */
    @NotBlank(message = "moId can not be empty")
    private String moId;

    /**
     * 开始时间
     */
    @NotBlank(message = "beginTime can not be empty")
    private String beginTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "endTime can not be empty")
    private String endTime;

    /**
     * 查询数据范围粒度
     */
    @NotBlank(message = "queryRangeGr can not be empty")
    String queryRangeGr;

    /**
     * 列名
     */
    @NotEmpty(message = "columnNames can not be empty")
    private Map<String, String> columnNameSmpMap;

    public PmaDataQueryConditionDto(){}

    public PmaDataQueryConditionDto(String mtId, String tableName, String moId, String beginTime, String endTime, Map<String, String> columnNameSmpMap, String queryRangeGr)
    {
        this.mtId = mtId;
        this.tableName = tableName;
        this.moId = moId;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.columnNameSmpMap = columnNameSmpMap;
        this.queryRangeGr = queryRangeGr;
    }

    /**
     * 校验查询有效性
     * @return
     */
    public List<String> checkInvalid()
    {
        ValidationResult validationResult = ValidationUtils.validateForDefalut(this);
        Map<String, String> resultMap = validationResult.getPropertyErrMsg();
        return new ArrayList<>(resultMap.keySet());
    }
}
