package com.zte.uedm.battery.opti.infrastructure.repository.persistence;


import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.infrastructure.repository.converter.BattRiskRuleSourcePoConverter;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskRuleSourceMapper;
import com.zte.uedm.battery.opti.infrastructure.repository.po.BattRiskRuleSourcePo;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.BatchUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class BattRiskRuleSourceRepositoryImpl implements BattRiskRuleSourceRepository
{

    @Autowired
    private BattRiskRuleSourceMapper battRiskRuleSourceMapper;

    @Override
    public List<BattRiskRuleSourceEntity> selectByIds(List<String> ids) throws UedmException
    {
        List<BattRiskRuleSourceEntity> entities = new ArrayList<>();

        if(CollectionUtils.isEmpty(ids))
        {
            return entities;
        }

        List<BattRiskRuleSourcePo> pos = new ArrayList<>();

        try
        {
            BatchUtils.doInBatch(30000, ids, (items)->
            {
                try
                {
                    pos.addAll(battRiskRuleSourceMapper.selectByIds(items));
                }
                catch (Exception e)
                {
                    log.error("[BattRiskRuleSourceRepository]_selectByIds, occur exception.", e);
                    throw new RuntimeException("[BattRiskRuleSourceRepository]_selectByIds, occur exception.");
                }
            });
        }
        catch (Exception e)
        {
            log.error("[BattRiskRuleSourceRepository]_selectByIds occur exception. e.getmessage={}", e.getMessage(), e);
            throw new UedmException(-1, "[BattRiskRuleSourceRepository]_selectByIds, occur exception.");
        }

        log.info("[BattRiskRuleSourceRepository]_selectByIds, pos.size={}", pos.size());

        // 转化成Entity
        return BattRiskRuleSourcePoConverter.converterFromPo(pos);
    }

    @Override
    public List<BattRiskRuleSourceEntity> selectAll() throws UedmException
    {
        List<BattRiskRuleSourcePo> pos;

        try
        {
            pos = battRiskRuleSourceMapper.selectAllResouce();
        }
        catch (Exception e)
        {
            log.error("[BattRiskRuleSourceRepository]_selectAll occur exception. e.getmessage={}", e.getMessage(), e);
            throw new UedmException(-1, "[BattRiskRuleSourceRepository]_selectAlls, occur exception.");
        }

        log.info("[BattRiskRuleSourceRepository]_selectAll, pos.size={}", pos.size());

        // 转化成Entity
        return BattRiskRuleSourcePoConverter.converterFromPo(pos);
    }
}
