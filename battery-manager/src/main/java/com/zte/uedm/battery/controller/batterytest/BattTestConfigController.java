/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattTestConfigController
 * 文件作者：00248587
 * 开发时间：2023/3/14
 */
package com.zte.uedm.battery.controller.batterytest;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.log.filter.UserThreadLocal;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.controller.batterytest.dto.BattImpactTrendDimsUpdateDto;
import com.zte.uedm.battery.controller.batterytest.vo.BattImpactTrendVo;
import com.zte.uedm.battery.controller.backuppowerconfig.dto.BackupPowerConfigUpdateDto;
import com.zte.uedm.battery.controller.batterytest.dto.BattTestProportionUpdateDto;
import com.zte.uedm.battery.controller.batterytest.vo.BattImpactTrendVo;
import com.zte.uedm.battery.controller.batterytest.vo.SelectBattTestConfigVo;
import com.zte.uedm.battery.controller.battlife.vo.BattLifeConfigVo;
import com.zte.uedm.battery.enums.battlife.BatteryTestProportionEnums;
import com.zte.uedm.battery.service.batttest.BattTestConfigService;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池寿命列表表头
 */
@Path("batt-test-config")
@Component
@Slf4j
@Api(value = "batt-test-config", tags = "放电测试/维度")
public class BattTestConfigController
{

    @Autowired
    private BattTestConfigService battTestConfigService;


    private static final String PARAM_IS_BLANK = "param is blank.";

    /**
     *  影响趋势维度查询
     *
     * @return
     */
    @GET
    @Path("/impact-trend/select")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "影响趋势维度查询",
            notes = "影响趋势维度查询",
            response = ResponseBean.class,
            httpMethod = HttpMethod.GET,
            tags = {"放电测试/维度/影响趋势维度查询"})
    @ApiResponses({
            @ApiResponse(code = -1, message = "失败"),
            @ApiResponse(code = -301, message = "参数为空")
    })
    public ResponseBean selectImpactTrendConfig(@QueryParam("pageSize") Integer pageSize,
                                                 @QueryParam("pageNo") Integer pageNo,
                                                 @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {

        try {
            String userName = Tools.getUserName(request);
            log.info("===== BattTestConfigController selectImpactTrendConfig :=====\n userName: {}", userName);
            if (StringUtils.isBlank(userName)) {
                log.warn("BattTestConfigController selectImpactTrendConfig username null");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, null, PARAM_IS_BLANK, null, null);
            }
            List<BattImpactTrendVo> battImpactTrendVos = battTestConfigService.selectImpactTrendConfig(userName, languageOption);

            // 分页
            if(pageNo != null && pageSize != null){
                battImpactTrendVos = PageUtil.getPageList(battImpactTrendVos, pageNo, pageSize);
            }
            int total = battImpactTrendVos.size();
            PageInfo<BattImpactTrendVo> pageInfoList = new PageInfo<>();
            pageInfoList.setList(battImpactTrendVos);
            pageInfoList.setTotal(total);
            Object data = pageInfoList.getList();
            log.debug("BattTestConfigController selectImpactTrendConfig list :{}", data);
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        } catch (UedmException e) {
            log.error("BattTestConfigController selectImpactTrendConfig select fail", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    /**
     * 电池放电-影响趋势维度更新
     *
     * @param
     * @return
     */
    @PUT
    @Path("/impact-trend/update")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "影响趋势维度更新",
            notes = "电池放电-影响趋势维度更新",
            httpMethod = HttpMethod.PUT,
            tags = {"放电测试/维度/影响趋势维度更新"})
    @ApiResponses({
            @ApiResponse(code = -1, message = "失败"),
            @ApiResponse(code = -208, message = "参数中固定元素变动"),
            @ApiResponse(code = -301, message = "List参数为空"),
            @ApiResponse(code = -302, message = "参数中元素sequence不唯一"),
            @ApiResponse(code = -305, message = "参数中元素不在可选值范围")
    })
    public ResponseBean updateImpactTrendConfig(List<BattImpactTrendDimsUpdateDto> updateBeanList,
                                                @Context HttpServletRequest request,
                                                @HeaderParam("language-option") String languageOption) throws UedmException {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        try
        {
            log.info("===== BattTestConfigController updateImpactTrendConfig :=====\n userName: {}", userName);
            if (StringUtils.isBlank(userName)) {
                log.warn("BattTestConfigController updateImpactTrendConfig username is null");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, null, PARAM_IS_BLANK, null, null);
            }
            if (CollectionUtils.isEmpty(updateBeanList))
            {
                log.warn("BattTestConfigController updateImpactTrendConfig param updateBeanList is empty");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, null, PARAM_IS_BLANK, null, null);
            }
            log.info("===== BattTestConfigController updateImpactTrendConfig  :=====\n updateBeanList: {}", updateBeanList);

            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            Integer total = battTestConfigService.updateImpactTrendConfig(updateBeanList, serviceBean);

            return ResponseBeanUtils.getNormalResponseBean(0, null, total);
        }
        catch (UedmException e)
        {
            log.error("BattTestConfigController updateImpactTrendConfig select fail", e);
            return ResponseBeanUtils.getResponseBeanCheckParamByUedmException(e);
        }
    }

    @GET
    @Path("/proportion/select")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "占比维度查询",
            notes = "放电测试-占比维度查询",
            response = ResponseBean.class,
            httpMethod = HttpMethod.GET,
            tags = {"放电测试/占比维度/展示项查询"})
    @ApiResponses({
            @ApiResponse(code = -1, message = "失败"),
            @ApiResponse(code = -301, message = "参数为空")
    })
    public ResponseBean selectBattTestProportion(@QueryParam("pageSize") Integer pageSize,
                                                 @QueryParam("pageNo") Integer pageNo,
                                                 @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {

        try {
            String userName = Tools.getUserName(request);
            log.info("===== BattTestConfigController selectBattTestConfig :=====\n userName: {}", userName);
            if (StringUtils.isBlank(userName))
            {
                log.warn("BattTestConfigController selectBattTestConfig username is blank");
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,PARAM_IS_BLANK);
            }
            List<SelectBattTestConfigVo> selectBattTestConfigVos = battTestConfigService.selectBattTestProportion(userName, languageOption);

            // 分页
            if(pageNo != null && pageSize != null){
                selectBattTestConfigVos = PageUtil.getPageList(selectBattTestConfigVos, pageNo, pageSize);
            }
            int total = selectBattTestConfigVos.size();
            PageInfo<SelectBattTestConfigVo> pageInfoList = new PageInfo<>();
            pageInfoList.setList(selectBattTestConfigVos);
            pageInfoList.setTotal(total);
            Object data = pageInfoList.getList();
            log.debug("BattTestConfigController selectBattTestConfig list :{}", data);
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        } catch (UedmException e) {
            log.error("BattTestConfigController selectBattTestConfig select fail", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @PUT
    @Path("/proportion/update")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "占比维度更新",
            notes = "放电测试-占比维度更新",
            httpMethod = HttpMethod.PUT,
            tags = {"放电测试/占比维度/展示项更新"})
    @ApiResponses({
            @ApiResponse(code = -1, message = "失败"),
            @ApiResponse(code = -208, message = "参数中固定元素变动"),
            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -302, message = "参数中元素sequence不唯一"),
            @ApiResponse(code = -305, message = "参数中元素不在可选值范围")
    })
    public ResponseBean updateBattTestProportion(List<BattTestProportionUpdateDto> updateDtoList,
                                                 @Context HttpServletRequest request,
                                                 @HeaderParam("language-option") String languageOption)
    {
        try
        {
            String userName = Tools.getUserName(request);
            log.info("===== BattTestConfigController updateBattTestProportion :=====\n userName: {}", userName);
            if (StringUtils.isBlank(userName) || CollectionUtils.isEmpty(updateDtoList)) {
                log.warn("BattTestConfigController updateBattTestProportion username or updateDtoList is empty");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, PARAM_IS_BLANK, PARAM_IS_BLANK, null, null);
            }
            //获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
            if (checkSeqUnique(updateDtoList))
            {
                log.warn("BattTestConfigController updateBattTestProportion -> sequence is not unique");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, "sequence is not unique","sequence is not unique",null,null);
            }
            //可选值范围校验
            List<String> dimIds = updateDtoList.stream().map(BattTestProportionUpdateDto::getDimId).distinct()
                    .collect(Collectors.toList());
            Pair<Boolean, List<String>> booleanListPair = BatteryTestProportionEnums.checkIdInRange(dimIds);
            if (!booleanListPair.getLeft())
            {
                log.warn("BattTestConfigController updateBattTestProportion -> dimIds is not in range.");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, JSON.toJSONString(booleanListPair.getRight()),"dimIds is not in range.",null,null);
            }
            //是否可变动
            if (!BatteryTestProportionEnums.checkIdImmutable(updateDtoList))
            {
                log.warn("BattTestConfigController checkIdImmutable -> param cannot be changed");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.OBJECT_OPERATION_EXCEED_RANGE_NOT_ALLOW, "param cannot be changed","param cannot be changed",null,null);
            }

            log.info("===== BattTestConfigController updateBattTestProportion  :=====\n updateDtoList: {}", updateDtoList);

            Integer total = battTestConfigService.updateBattTestProportion(updateDtoList, userName, languageOption);

            return ResponseBeanUtils.getNormalResponseBean(0, null, total);
        }
        catch (UedmException e)
        {
            log.error("BattTestConfigController updateBattTestProportion select fail", e);
            return ResponseBeanUtils.getResponseBeanCheckParamByUedmException(e);
        }
    }

    /**
     * 校验顺序是否唯一
     * @param updateDtoList
     * @return
     */
    private boolean checkSeqUnique(List<BattTestProportionUpdateDto> updateDtoList) {
        List<Integer> reqSeqList = updateDtoList.stream().map(BattTestProportionUpdateDto::getSequence).distinct()
                .collect(Collectors.toList());
        if(reqSeqList.size() != updateDtoList.size()){
            log.warn("BattTestProportionUpdateDto checkReqSeq -> sequence is not unique");
            return true;
        }
        return false;
    }

}
