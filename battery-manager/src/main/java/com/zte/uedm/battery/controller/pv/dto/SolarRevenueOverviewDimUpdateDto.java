package com.zte.uedm.battery.controller.pv.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@ApiModel(description = "太阳能收益总览更新")
public class SolarRevenueOverviewDimUpdateDto {
    /**
     * 记录属性id
     */
    @ApiModelProperty("维度id")
    private String id;

    /**
     * 是否勾选
     */
    @ApiModelProperty("是否启用")
    private boolean enable;

    /**
     * 展示顺序
     */
    @ApiModelProperty("展示顺序")
    private Integer sequence;

}
