package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BatteryChargeDischargeHistoryBean;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BatteryChargeDischargeHistoryR321Mapper {

    List<BatteryChargeDischargeHistoryBean> selectAll(String deviceId, String batterySeq);

    void batchInsertBatteryChargeDischargeHistory(List<BatteryChargeDischargeHistoryBean> insertList);
}

