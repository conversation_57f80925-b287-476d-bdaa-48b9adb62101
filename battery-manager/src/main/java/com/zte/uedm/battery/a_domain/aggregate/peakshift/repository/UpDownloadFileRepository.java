package com.zte.uedm.battery.a_domain.aggregate.peakshift.repository;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.UpDownloadFileEntity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.UpDownloadFileDataBasePO;

import java.util.List;

public interface UpDownloadFileRepository {

    List<UpDownloadFileEntity> selectByIds(List<String> ids) throws UedmException;

    UpDownloadFileEntity selectById(String id) throws UedmException;

    void update(UpDownloadFileDataBasePO upDownloadFileBean) throws UedmException;

    Object insertFile(UpDownloadFileDataBasePO upDataBaseBean) throws UedmException;

    int deleteByIds(List<String> ids) throws UedmException;
}
