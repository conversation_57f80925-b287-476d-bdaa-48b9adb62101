package com.zte.uedm.battery.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.uedm.function.license.consts.LicenseIdConstant;
import com.zte.uedm.function.license.exception.LicenseException;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.domain.BattSohDomain;
import com.zte.uedm.battery.enums.overview.*;
import com.zte.uedm.battery.service.BatteryOverviewConfigService;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.enums.AlarmLevelEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.bean.license.LicenseBean;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.kafka.common.protocol.types.Field;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 电池总览controller
 *
 * <AUTHOR>
 */

@Path("battery-overview")
@Component
//@Api(value = "battery-overview")
@Api(value = "电池配置总览")
@Slf4j
public class BatteryOverviewConfigController
{

    @Autowired
    private BatteryOverviewConfigService battOverviewConfigService;
    @Autowired
    private BattSohDomain battSohDomain;


    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;


    @GET
    @Path("/select-asset-manager")
    @Consumes({ MediaType.APPLICATION_JSON })
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "select asset manager license info", notes = "select asset manager license info", httpMethod = "GET")
    public ResponseBean selectAssetManger(@HeaderParam("language-option") String languageOption)
    {
        Boolean ret = configurationManagerRpc.queryLicenseInfo(LicenseIdConstant.ASSET_MODEL);
        LicenseBean result = new LicenseBean();
        result.setValue(String.valueOf(ret));
        log.info("license Service select Asset manager success.");
        return ResponseBeanUtils.getNormalResponseBean(0, result, 1);

    }


    /**
     *  电池概览维度查询
     *
     * @return
     */
    @GET
    @Path("/overview-config/select")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询电池配置", notes = "查询电池配置", response = ResponseBean.class, httpMethod = HttpMethod.GET)
    public ResponseBean selectOverviewConfig(@QueryParam("pageSize") Integer pageSize,
                                             @QueryParam("pageNo") Integer pageNo,
                                             @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption) {

        Object data = null;
        int total = 0;
        try {
            String userName = Tools.getUserName(request);
            log.info("===== select OverviewConfig  :=====\n userName: {}", userName);
            List<BatteryOverviewBean> queryOverviewList =
                    battOverviewConfigService.selectOverviewConfig(userName, languageOption);

            // 分页
            total = queryOverviewList.size();
            if(pageNo != null && pageSize != null){
                queryOverviewList = PageUtil.getPageList(queryOverviewList, pageNo, pageSize);
            }
            PageInfo<BatteryOverviewBean> pageInfoList = new PageInfo<>();
            pageInfoList.setList(queryOverviewList);
            pageInfoList.setTotal(total);
            data = pageInfoList.getList();
        } catch (UedmException e) {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        log.debug("list :{}", data);
        return ResponseBeanUtils.getNormalResponseBean(0, data, total);
    }

    /**
     * 电池概览维度更新
     *
     * @param
     * @return
     */
    @POST
    @Path("/overview-config/update")
    @Produces({MediaType.APPLICATION_JSON})
//    @ApiOperation(value = "update overviewConfig", notes = "update overviewConfig", httpMethod = HttpMethod.POST)
    @ApiOperation(value = "电池概览信息更新", notes = "电池概览信息更新", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常")
    })
    public ResponseBean updateBatteryOverviewConfig(List<BatteryOverviewUpdateBean> updateBeanList,
                                             @Context HttpServletRequest request,
                                                  @HeaderParam("language-option") String languageOption) {

        log.info("===== update overviewConfig  :=====\n updateBeanList: {}", JSON.toJSONString(updateBeanList));
        Integer total;
        ResponseBean response = new ResponseBean();
        List<String> errorList = new ArrayList<>();
        String userName = Tools.getUserName(request);

        try {
            response = checkArgs(updateBeanList);
            if (response != null) return response;

            //获取用户概览维度列表
            List<BatteryOverviewBean> battOverviewBeanList =
                    battOverviewConfigService.selectOverviewConfig(userName, languageOption);
            log.info("database batteryOverviewBeanList:{}",JSON.toJSONString(battOverviewBeanList, SerializerFeature.WriteMapNullValue));
            //根据概览维度列表，获取 id集合
            List<String> queryIdList =
                    battOverviewBeanList.stream().map(BatteryOverviewBean::getId).collect(Collectors.toList());
            //根据概览维度列表，获取用户序列集合
            List<Integer> querySeqList =
                    battOverviewBeanList.stream().map(BatteryOverviewBean::getSequence).collect(Collectors.toList());

            response = checkIdAndSeq(updateBeanList, queryIdList, querySeqList, errorList);
            if (response != null) return response;

            //遍历 概览维度列表 校验不可变动的设定 <4>
            if (checkEnable(updateBeanList, errorList, battOverviewBeanList))
                return ResponseBeanUtils.getNormalResponseBean(-208, JSON.toJSONString(errorList), null, null, null);
            total = battOverviewConfigService.updateOverviewConfig(updateBeanList, userName);

        } catch (UedmException e) {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0, null, total);
    }

    private boolean checkEnable(List<BatteryOverviewUpdateBean> updateBeanList, List<String> errorList, List<BatteryOverviewBean> battOverviewBeanList) {
        for (BatteryOverviewBean queryBean : battOverviewBeanList) {
            String id = queryBean.getId();
            Boolean enable = queryBean.getEnable();
            if(queryBean.getDefaultFixed()){
                List<BatteryOverviewUpdateBean> filterList =
                        updateBeanList.stream().filter(item -> item.getDimId().equals(id)).collect(Collectors.toList());
                //添加非空判断
                if (!CollectionUtils.isEmpty(filterList)){
                    BatteryOverviewUpdateBean updateBean = filterList.get(0);
                    if(!updateBean.getEnable().equals(enable)){
                        log.error("BatteryOverviewConfigController updateBatteryOverviewConfig -> DefaultFixed is true");
                        errorList.add(updateBean.getDimId());
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * 电池概览维度搜索
     *
     * @return
     */
    @POST
    @Path("/overview-config/search")
    @Produces({MediaType.APPLICATION_JSON})
//    @ApiOperation(value = "search overview config", notes = "search overview config", response = ResponseBean.class, httpMethod = HttpMethod.POST)
    @ApiOperation(value = "电池概览维度搜索", notes = "电池概览维度搜索", response = ResponseBean.class, httpMethod = HttpMethod.POST)
    public ResponseBean searchOverviewConfig(BatteryConfigSearchBean searchBean,
                                             @Context HttpServletRequest request,
                                             @HeaderParam("language-option") String languageOption) {


        Object data;
        int total = 0;
        try {
            log.info("param queryBean is {}", searchBean);

            String userName = Tools.getUserName(request);
            PageInfo<BatteryOverviewBean> list = battOverviewConfigService.searchOverviewConfig(searchBean, userName, languageOption);

            data = list.getList();
            total = (int) list.getTotal();
        } catch (UedmException e) {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0, data, total);
    }


    @Nullable
    private ResponseBean checkArgs(List<BatteryOverviewUpdateBean> updateBeanList) {
        //非空判断 <1>
        if (ObjectUtils.isEmpty(updateBeanList)) {
            log.error("update BatteryOverviewConfig -> parameter is blank");
            return ResponseBeanUtils.getNormalResponseBean(-301, null, null, null, null);
        }

        //获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
        if (checkReqSeq(updateBeanList)) return ResponseBeanUtils.getNormalResponseBean(-302, null, null, null, null);
        return null;
    }

    private boolean checkReqSeq(List<BatteryOverviewUpdateBean> updateBeanList) {
        List<Integer> reqSeqList = updateBeanList.stream().map(BatteryOverviewUpdateBean::getSequence).distinct()
                .collect(Collectors.toList());
        if(reqSeqList.size() != updateBeanList.size()){
            log.error("BatteryOverviewConfigController update BatteryOverviewConfig -> sequence is not unique");
            return true;
        }
        return false;
    }

    @Nullable
    private ResponseBean checkIdAndSeq(List<BatteryOverviewUpdateBean> updateBeanList,
                                       List<String> queryIdList
            , List<Integer> querySeqList, List<String> errorList) {
        for (BatteryOverviewUpdateBean updateBean : updateBeanList) {
            //入参对象id
            if (checkId(queryIdList, errorList, updateBean))
                return ResponseBeanUtils.getNormalResponseBean(-305, JSON.toJSONString(errorList), null, null, null);
            //sequence 每个元素的int唯一 <2>
            if (checkSequence(querySeqList, updateBean))
                return ResponseBeanUtils.getNormalResponseBean(-302, null, null, null, null);
        }
        return null;
    }

    private boolean checkSequence(List<Integer> querySeqList, BatteryOverviewUpdateBean updateBean) {
        Integer sequence = updateBean.getSequence();
        if (!querySeqList.contains(sequence)) {
            log.error("BatteryOverviewConfigController updateBatteryOverviewConfig -> sequence is not a given data");
            return true;
        }
        return false;
    }

    private boolean checkId(List<String> queryIdList, List<String> errorList, BatteryOverviewUpdateBean updateBean) {
        String id = updateBean.getDimId();
        //id 在可选值范围内判断 <3>
        if (!queryIdList.contains(id)) {
            log.error("BatteryOverviewConfigController updateBatteryOverviewConfig -> id is not a given data");
            errorList.add(id);
            return true;
        }
        return false;
    }

//    /**
//     * 根据条件导出电池概览维度信息
//     *
//     * @return
//     */
//    @POST
//    @Path("/statistics/export")
//    @Produces({MediaType.APPLICATION_JSON})
//    @ApiOperation(value = "export battery overview", notes = "export battery overview", response =
//            ResponseBean.class, httpMethod = HttpMethod.POST)
//    public ResponseBean exportOverview(BatteryOverviewExportBean exportBean,
//                                                  @Context HttpServletRequest request,
//                                       @Context HttpServletResponse response,
//                                                  @HeaderParam("language-option") String languageOption) {
//
//            String userName = Tools.getUserName(request);
//            String ip = Tools.getRemoteHost(request);
//            try {
//
//                if (null == exportBean) {
//                    return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "batteryHisDataQueryBean is null", null, null, null);
//                }
//
//                ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption, null, null);
//                ResponseBean responseBean = battOverviewConfigService.parameterCheck(exportBean, serviceBean);
//                if(responseBean != null && responseBean.getCode() != 0) return responseBean;
//
//                battOverviewConfigService.exportOverview(exportBean, request, response, serviceBean);
//                return ResponseBeanUtils.getNormalResponseBean(0, null, null, "ok", 1);
//
//            } catch (UedmException e) {
//
//                log.error("batteryHisDataExport", e);
//                return ResponseBeanUtils.getResponseBeanByException(e);
//            }
//        }
}
