/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BackupPowerAssetDomainImpl
 * 文件作者：00248587
 * 开发时间：2023/3/8
 */
package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.controller.backuppower.dto.BackupPowerFilterDto;
import com.zte.uedm.battery.domain.BackupPowerAssetDomain;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.vo.MoAssetInstanceVo;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BackupPowerAssetDomainImpl implements BackupPowerAssetDomain
{
    @Autowired
    private AssetRpcImpl assetRpcImpl;


    private static  final  String UNKNOWN_LEFT = "unknown";
    private static  final  String UNKNOWN_RIGHT = "{\"en_US\":\"Unknown\",\"zh_CN\":\"未知\"}";

    @Override
    public List<String> selectMoIdsFromAsset(BackupPowerFilterDto dto, List<String> spMoIds, String lang) throws UedmException
    {
        if(dto.noExistFilter())
        {
            //若供应商等筛选条件为空，则不无需通过资产过滤
            log.info("selectMoIdsFromAsset filter asset condition is empty");
            return spMoIds;
        }
        List<String> moIds = new ArrayList<>();
        List<MoAssetInstanceVo> assetInstanceVoList = assetRpcImpl.selectByMoIds(spMoIds, lang);
        Set<String> collect = assetInstanceVoList.stream().map(MoAssetInstanceVo::getId).collect(Collectors.toSet());
        for(String spId:spMoIds)
        {
            if(!collect.contains(spId))
            {
                MoAssetInstanceVo moAssetInstanceVo = new MoAssetInstanceVo();
                moAssetInstanceVo.setId(spId);
                moAssetInstanceVo.setManufacture(UNKNOWN_LEFT);
                moAssetInstanceVo.setSeries(UNKNOWN_LEFT);
                moAssetInstanceVo.setModel(UNKNOWN_LEFT);
                moAssetInstanceVo.setBrand(UNKNOWN_LEFT);
                assetInstanceVoList.add(moAssetInstanceVo);
            }
        }
        //过滤厂商等信息
        List<String> manufactures = dto.getManufacturers();
        assetInstanceVoList = filterMoAssetInstanceVosByManu(assetInstanceVoList, manufactures);
        List<String> brands = dto.getBrands();
        assetInstanceVoList = filterMoAssetInstanceVosByBrands(assetInstanceVoList, brands);
        List<String> models = dto.getModels();
        assetInstanceVoList = filterMoAssetInstanceVosByModels(assetInstanceVoList, models);
        List<String> series = dto.getSeries();
        assetInstanceVoList = filterMoAssetInstanceVosBySeries(assetInstanceVoList, series);

        moIds = Optional.ofNullable(assetInstanceVoList.stream().filter(bean -> StringUtils.isNotBlank(bean.getId()))
                .map(MoAssetInstanceVo::getId).collect(Collectors.toList())).orElse(new ArrayList<>());
        log.info("selectMoIdsFromAsset filter end assetInstanceVoList size ={}", assetInstanceVoList.size());
        return moIds;
    }

    private List<MoAssetInstanceVo> filterMoAssetInstanceVosByBrands(List<MoAssetInstanceVo> assetInstanceVoList, List<String> brands)
    {
        if (CollectionUtils.isNotEmpty(brands) && brands.contains(UNKNOWN_LEFT))
        {
            assetInstanceVoList = assetInstanceVoList.stream()
                    .filter(bean-> brands.contains(bean.getBrand()) || StringUtils.isBlank(bean.getBrand()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(brands))
        {
            assetInstanceVoList = assetInstanceVoList.stream().filter(bean-> brands.contains(bean.getBrand()))
                    .collect(Collectors.toList());
        }
        log.info("selectMoIdsFromAsset filter brands assetInstanceVoList size={}",assetInstanceVoList.size());
        return assetInstanceVoList;
    }

    private List<MoAssetInstanceVo> filterMoAssetInstanceVosByManu(List<MoAssetInstanceVo> assetInstanceVoList, List<String> manufactures)
    {
        if (CollectionUtils.isNotEmpty(manufactures) && manufactures.contains(UNKNOWN_LEFT))
        {
            assetInstanceVoList = assetInstanceVoList.stream()
                    .filter(bean-> manufactures.contains(bean.getManufacture()) || StringUtils.isBlank(bean.getManufacture()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(manufactures))
        {
            assetInstanceVoList = assetInstanceVoList.stream().filter(bean-> manufactures.contains(bean.getManufacture()))
                    .collect(Collectors.toList());
        }
        log.info("selectMoIdsFromAsset filter manufactures assetInstanceVoList size={}",assetInstanceVoList.size());
        return assetInstanceVoList;
    }


    private List<MoAssetInstanceVo> filterMoAssetInstanceVosByModels(List<MoAssetInstanceVo> assetInstanceVoList, List<String> models)
    {
        if (CollectionUtils.isNotEmpty(models) && models.contains(UNKNOWN_LEFT))
        {
            assetInstanceVoList = assetInstanceVoList.stream()
                    .filter(bean-> models.contains(bean.getModel()) || StringUtils.isBlank(bean.getModel()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(models))
        {
            assetInstanceVoList = assetInstanceVoList.stream().filter(bean-> models.contains(bean.getModel()))
                    .collect(Collectors.toList());
        }
        log.info("selectMoIdsFromAsset filter models assetInstanceVoList size={}",assetInstanceVoList.size());
        return assetInstanceVoList;
    }


    private List<MoAssetInstanceVo> filterMoAssetInstanceVosBySeries(List<MoAssetInstanceVo> assetInstanceVoList, List<String> series)
    {
        if (CollectionUtils.isNotEmpty(series) && series.contains(UNKNOWN_LEFT))
        {
            assetInstanceVoList = Optional.ofNullable(assetInstanceVoList.stream()
                    .filter(bean-> series.contains(bean.getSeries()) || StringUtils.isBlank(bean.getSeries()))
                    .collect(Collectors.toList())).orElse(new ArrayList<>());
        }
        else if (CollectionUtils.isNotEmpty(series))
        {
            assetInstanceVoList = Optional.ofNullable(assetInstanceVoList.stream().filter(bean-> series.contains(bean.getSeries()))
                    .collect(Collectors.toList())).orElse(new ArrayList<>());
        }
        log.info("selectMoIdsFromAsset filter series assetInstanceVoList size={}",assetInstanceVoList.size());
        return assetInstanceVoList;
    }
}
