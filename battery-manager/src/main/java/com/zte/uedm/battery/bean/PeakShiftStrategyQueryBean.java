package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 错峰用电策略查询、编辑参数Bean
 */
@Setter
@Getter
@ToString
@ApiModel(description = "错峰策略")
public class PeakShiftStrategyQueryBean
{
    /**
     * 时段策略名称
     */
    @ApiModelProperty(value = "时段策略名称")
    private String name;
    /**
     * 作用范围
     */
    @ApiModelProperty(value = "作用范围")
    private List<String> scopeIds;

    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 作用范围开始日期
     */
    @ApiModelProperty(value = "作用范围开始日期")
    private String startTime;
    /**
     * 作用范围结束日期
     */
    @ApiModelProperty(value = "作用范围结束日期")
    private String endTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}