package com.zte.uedm.battery.a_infrastructure.kafka.bean;/* Started by AICoder, pid:jee7f85f672198814f090840b0028d3b3b421240 */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Pattern;

/**
 * 电子钥匙下发Bean
 */
@Getter
@Setter
@ToString
public class EleKeyDeliveryBean {

    /**
     * 序列号
     */
    private String sn;

    /**
     * 旧钥匙
     */
    private String oldKey;

    /**
     * 新钥匙
     */
    private String newKey;
}

/* Ended by AICoder, pid:jee7f85f672198814f090840b0028d3b3b421240 */