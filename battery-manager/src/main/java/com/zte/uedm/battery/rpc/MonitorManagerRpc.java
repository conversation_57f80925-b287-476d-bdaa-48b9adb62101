package com.zte.uedm.battery.rpc;

import com.zte.uedm.battery.bean.PointsDataSrchCondition;
import com.zte.uedm.battery.bean.pv.HistoryDataRequestConditionBean;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.List;
import java.util.Map;

@ServiceHttpEndPoint(serviceName = "monitor-manager", serviceVersion = "v1")
public interface MonitorManagerRpc {


    @POST("history-data/points-data")
    Call<ResponseBean> queryHistoryDataBySmpIdList(@Body
            PointsDataSrchCondition pointsDataSrchCondition,
            @Query("gr") String gr, @Query("startTime") String startTime, @Query("endTime") String endTime,
            @Query("dataType") String dataType, @Header("language-option") String languageOption);

}
