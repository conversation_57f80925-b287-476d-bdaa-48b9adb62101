package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 报表实时统计查询Bean
 */
@Setter
@Getter
@ToString
public class BatteryRealtimeReportBean
{

    /**
     * 电池id
     */
    private String id;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 位置
     */
    private String position;

    /**
     * 位置ids
     */
    private String pathIds;

    /**
     * 电池名称
     */
    private String battName;

    /**
     * 额定循环次数
     */
    private String ratedCycleTimes;

    /**
     * 剩余循环次数
     */
    private String leftCycleTimes;

    /**
     * 剩余容量
     */
    private String soc;

    /**
     * 电池健康程度
     */
    private String soh;

    /**
     * 电池id - 寿命评估表
     */
    private String lifeId;

    /**
     * 电池id - 健康状态评估表
     */
    private String healthId;
}
