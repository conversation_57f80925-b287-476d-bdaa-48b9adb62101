package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
public class UpDownloadFileBean {
    /**
     * 主键
     */
    @NotNull
    private String id;
    /**
     * 文件名
     */
    @NotNull
    private String name;
    /**
     * 原始文件名
     */
    @NotNull
    private String originalName;
    /**
     * 文件路径
     */
    @NotNull
    private String filePath;
    /**
     * 文件后缀
     */
    @NotNull
    private String fileSuffix;
    /**
     * 文件类型
     */
    @NotNull
    private Integer fileType;
}
