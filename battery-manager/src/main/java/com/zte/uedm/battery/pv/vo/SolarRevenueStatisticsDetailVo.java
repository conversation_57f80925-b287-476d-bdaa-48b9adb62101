package com.zte.uedm.battery.pv.vo;

import com.zte.uedm.battery.pv.bean.RateTypeRevenueDetailBean;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 太阳能收益统计返回前端Vo
 * @date 2023/9/4
 **/
@Data
public class SolarRevenueStatisticsDetailVo {
    /**
     * 站点总数
     */
    private Integer siteTotal;
    /**
     * 站点ID
     */
    private String siteId;
    /**
     * 查询时间段
     */
    private String recordDate;
    /**
     * 站点路径
     */
    private String pathName;
    /**
     * 站点名称
     */
    private String name;
    /**
     * 数据起止时间
     */
    private String time;
    /**
     * 位置，站点所在分组全路径
     */
    private String location;
    /**
     * 总电量
     */
    private String totalEnergyGeneration;
    /**
     * 减碳量
     */
    private String carbonReduction;
    /**
     * 太阳能发电总收益
     */
    private String totalSolarRevenue;
    /**
     * 市电总电费
     */
    private String totalGridFee;
    /**
     * 总节省收益
     */
    private String totalSavings;
    /**
     * 计费类型收益详情
     */
    private List<RateTypeRevenueDetailBean> rateTypeRevenueDetail;
    /**
     * 查询所用的站点历史策略id列表开始时间
     */
    private String beginTime;
    /**
     * 查询所用的站点历史策略id列表结束时间
     */
    private String endTime;
}
