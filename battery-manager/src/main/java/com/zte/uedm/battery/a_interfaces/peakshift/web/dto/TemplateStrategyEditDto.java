package com.zte.uedm.battery.a_interfaces.peakshift.web.dto;

import com.zte.uedm.battery.bean.peak.TemplateDetailBaseDto;
import com.zte.uedm.battery.bean.peak.TemplateHolidayDto;
import com.zte.uedm.common.bean.log.OperlogDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备模板策略Dto，对应编辑所需数据格式
 */

@Getter
@Setter
@ToString
@ApiModel(description = "设备模板策略更新")
public class TemplateStrategyEditDto
{
    @NotBlank(message = "id Can not be empty")
    @OperlogDetail(zhName = "策略模板id", enName = "id")
    @ApiModelProperty("策略模板id")
    private String id;

    @Length(min = 1, max = 50, message = "name length must be greater than 1 and less than 50 characters")
    @OperlogDetail(zhName = "策略模板名称", enName = "name")
    @ApiModelProperty("策略模板名称")
    private String name;

    @NotBlank(message = "seasonStrategyId Can not be empty")
    @OperlogDetail(zhName = "季节策略ID", enName = "seasonStrategyId")
    @ApiModelProperty("季节策略ID（web配置时候专用），Excel导入的时候为null")
    private String seasonStrategyId;

    @NotBlank(message = "mode Can not be empty")
    @OperlogDetail(zhName = "模式", enName = "mode", zhTansfer = "0:日模式 1:周模式 2：月模式 3:非周期模式", enTansfer = "0:day 1:week 2：month 3:Not_Cycle_Mode")
    @ApiModelProperty("模式")
    private String mode;

    @OperlogDetail(zhName = "备注", enName = "remark")
    @Length(min = 0, max = 2000, message = "remark length must be less than 2000 characters")
    @ApiModelProperty("备注")
    private String remark;

    @OperlogDetail(zhName = "导入文件ID", enName = "fileId")
    @ApiModelProperty("文件ID（Excel导入的时候专用），web配置的时候为null")
    private String fileId;

    @OperlogDetail(zhName = "是否允许周末错峰", enName = "allow peakshift on weekends", zhTansfer = "true:是 false:否", enTansfer = "true:true false:false")
    @ApiModelProperty("是否允许周末错峰")
    private Boolean weekendFlag;


    @OperlogDetail(zhName = "周期详情", enName = "detail")
    @NotNull(message = "detail Can not be null")
    @ApiModelProperty("周期详情")
    private List<TemplateDetailBaseDto> detail;

    @OperlogDetail(zhName = "节假日模式详情", enName = "holiday")
    @NotNull(message = "holiday Can not be null")
    @ApiModelProperty("节假日模式详情")
    private List<TemplateHolidayDto> holiday;

    /**
     * 组装设备模板PO
     *
     * @return
     */
    public TemplateStrategyDto packageTemplateStrategyPo()
    {
        //设备模板策略PO
        TemplateStrategyDto bean = new TemplateStrategyDto();
        bean.setId(this.getId());
        bean.setName(this.getName());
        bean.setSeasonStrategyId(this.getSeasonStrategyId());
        bean.setMode(this.getMode());
        bean.setRemark(this.getRemark());
        return bean;
    }
}
