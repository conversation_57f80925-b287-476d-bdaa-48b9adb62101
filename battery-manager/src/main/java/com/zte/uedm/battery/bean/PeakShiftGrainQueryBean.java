package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @FileDesc : 错峰用电收益查询DTO
 * <AUTHOR> TZZ 10286469
 * @date Date : 2022年07月27日 下午19:28
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
@ApiModel(description = "错峰用电收益查询")
public class PeakShiftGrainQueryBean
{
    @NotBlank(message = "startDate is Blank")
    @ApiModelProperty("开始时间")
    private String startTime;

    @NotBlank(message = "endDate is Blank")
    @ApiModelProperty("结束时间")
    private String endTime;

    @NotNull(message = "position is null")
    @ApiModelProperty("逻辑分组IDPath")
    private List<String> position;

    @ApiModelProperty("位置粒度")
    private Integer positionGran;

    @ApiModelProperty("时间粒度：日/月/年/全部，day/month/year/all")
    private String timeGran;

    @ApiModelProperty("错峰策略启动与否：0-启用，1-未启用")
    private String peakStrategy;

    @ApiModelProperty("设备类型")
    private String deviceType;

}
