package com.zte.uedm.battery.controller.battsupplier.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@ApiModel(description = "电池供应商")
public class BattSupplierSearchDto
{
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}
