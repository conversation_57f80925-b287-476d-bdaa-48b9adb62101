package com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper;

import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/* Started by AICoder, pid:z29f5v5bc825725142e40af3d069c614c4c0a643 */
@Mapper
public interface FMOverviewMapper {
    List<FMOverviewPo> selectFmOverviewBean(@Param("userName") String userName) throws UedmException;

    int deleteFmOverviewBean(@Param("userName") String userName) throws UedmException;

    void insertFmOverviewBean(List<FMOverviewPo> list);

    Integer updateFmOverviewBean(@Param("list") List<FMOverviewPo> beanList);
}
/* Ended by AICoder, pid:z29f5v5bc825725142e40af3d069c614c4c0a643 */
