package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.service.PvGenerConsumRecordService;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ Author     ：10260977
 * @ Date       ：9:22 2021/3/12
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
@Component
@Slf4j
public class PvGenerConsumHistoryScheduleJob
{
    @Autowired
    private PvGenerConsumRecordService pvGenerConsumRecordService;

    @Scheduled(cron = "0 0 4 * * ?") //每天的凌晨4点触发
    public void execute()
    {
        try
        {
            log.info("PvGenerConsumHistoryScheduleJob execute job ******************begin.********************");
            pvGenerConsumRecordService.pvDadaHandle();
            pvGenerConsumRecordService.spcuAndSpuDataHandle();
            log.info("PvGenerConsumHistoryScheduleJob execute job ******************end.********************");
        }
        catch (Exception e)
        {
            log.error("StorageJob execute occur exception:"+e.getMessage(), e);
        }
    }
}
