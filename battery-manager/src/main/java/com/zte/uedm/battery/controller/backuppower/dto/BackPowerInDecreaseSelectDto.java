package com.zte.uedm.battery.controller.backuppower.dto;

import com.zte.uedm.battery.enums.backuppower.BackupPowerApplicationSceneEnum;
import com.zte.uedm.battery.enums.backuppower.BackupPowerStateEnum;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.consts.asset.AssetModelAttributeIdConstants;
import com.zte.uedm.common.enums.SortEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

@Getter
@Setter
@ToString
@Slf4j
public class BackPowerInDecreaseSelectDto
{
    /**
     * 逻辑组id
     * */
    @ApiModelProperty("逻辑组id")
    private String logicGroupId;
    /**
     * 数量
     * */
    @ApiModelProperty("数量")
    private Integer number;
    /**
     * 当前备电状态
     * */
    @ApiModelProperty("当前备电状态")
    private List<String> status;
    /**
     * 使用场景
     * */
    @ApiModelProperty("使用场景")
    private List<String> applicationScene;
    /**
     * 制造商列表
     * */
    @ApiModelProperty("制造商列表")
    private List<String> manufacturers;
    /**
     * 品牌列表
     * */
    @ApiModelProperty("品牌列表")
    private List<String> brands;
    /**
     * 系列列表
     * */
    @ApiModelProperty("系列列表")
    private List<String> series;
    /**
     * 型号列表
     * */
    @ApiModelProperty("型号列表")
    private List<String> models;
    /**
     * 排序字段
     * */
    @ApiModelProperty("排序字段")
    private String order;
    /**
     * 顺序
     * */
    @ApiModelProperty("顺序")
    private String sort;
    /**
     * 页码
     * */
    @ApiModelProperty("页码")
    private Integer pageSize;
    /**
     * 每页数量
     * */
    @ApiModelProperty("每页数量")
    private Integer pageNo;

    public Boolean checkBlank()
    {
        return StringUtils.isBlank(logicGroupId);
    }

    public Pair<Boolean, Map<String, List<String>>> checkParam()
    {
        Boolean flag = true;
        Map<String, List<String>> errorMsg = new HashMap<>();
        List<String> allIdSet = BackupPowerStateEnum.getAllackupPowerStateIds();
        if (!CollectionUtils.isEmpty(this.status)) {
            for (String s : status)
            {
                if (!allIdSet.contains(s)) {
                    log.error("status is Not in the range of optional values");
                    flag = false;
                    errorMsg.put("status", new ArrayList<>(allIdSet));
                }
            }
        }

        List<String> allApplicationScene = BackupPowerApplicationSceneEnum.getAllIds();
        if (!CollectionUtils.isEmpty(this.applicationScene)) {
            for (String a : applicationScene)
            {
                if (!allApplicationScene.contains(a)) {
                    log.error("applicationScene is Not in the range of optional values");
                    flag = false;
                    errorMsg.put("applicationScene", new ArrayList<>(allIdSet));
                }
            }
        }

        return checkOrSetDefaltOrder(flag, errorMsg);
    }

    public Pair<Boolean, Map<String, List<String>>> checkOrSetDefaltOrder(Boolean flag, Map<String, List<String>> errorMsg)
    {
        List<String> allIds = BackupPowerStateEnum.getAllackupPowerStateIds();
        log.info("BackPowerInDecreaseSelectDto checkOrSetDefaltOrder allIdSet:{}", allIds);
        Set<String> sortIds = SortEnum.getSortIds();
        if (StringUtils.isNotBlank(this.order)) {
            if (!allIds.contains(this.order)) {
                log.error("order is Not in the range of optional values");
                flag = false;
                errorMsg.put("order", new ArrayList<>(allIds));
            }
        } else {
            this.order = BackupPowerStateEnum.DEFICIENCY.getId();
        }
        if (StringUtils.isNotBlank(this.sort)) {
            if (!sortIds.contains(this.sort)) {
                log.error("sort is Not in the range of optional values");
                flag = false;
                errorMsg.put("order", new ArrayList<>(allIds));
            }
        } else {
            this.sort = SortEnum.getDescSortID();
        }
        log.info("BackPowerInDecreaseSelectDto checkOrSetDefaltOrder  order:{},sort:{}", order, sort);

        return Pair.of(flag, errorMsg);
    }

    /**
     * 校验未开启的资产属性是否传值
     * @return true: 通过校验， false： 未通过校验
     */
    public Pair<Boolean, List<String>>  checkAssetAttribute(Map<String, Boolean> attributeEnableMap)
    {
        Boolean check = true;
        List<String> attributeId = new ArrayList<>();
        if(attributeEnableMap == null)
        {
            return Pair.of(check, new ArrayList<>());
        }

        //电池制造商
        check = checkManAttribute(attributeEnableMap, check, attributeId);
        //电池品牌
        check = checkBrandAttribute(attributeEnableMap, check, attributeId);
        //电池系列
        check = checkSeriesAttribute(attributeEnableMap, check, attributeId);
        //电池型号
        check = checkModelAttribute(attributeEnableMap, check, attributeId);

        return Pair.of(check, attributeId);

    }

    private Boolean checkModelAttribute(Map<String, Boolean> attributeEnableMap, Boolean check, List<String> attributeId) {
        if(CollectionUtils.isNotEmpty(models) && (attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MODELS) == null || !attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MODELS)))
        {
            check = false;
            attributeId.add(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MODELS);
        }
        return check;
    }

    private Boolean checkSeriesAttribute(Map<String, Boolean> attributeEnableMap, Boolean check, List<String> attributeId) {
        if(CollectionUtils.isNotEmpty(series) && (attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_SERIES) == null || !attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_SERIES)))
        {
            check = false;
            attributeId.add(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_SERIES);
        }
        return check;
    }

    private Boolean checkBrandAttribute(Map<String, Boolean> attributeEnableMap, Boolean check, List<String> attributeId) {
        if(CollectionUtils.isNotEmpty(brands) && (attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BRAND) ==null || !attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BRAND)))
        {
            check = false;
            attributeId.add(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BRAND);
        }
        return check;
    }

    private Boolean checkManAttribute(Map<String, Boolean> attributeEnableMap, Boolean check, List<String> attributeId) {
        if(CollectionUtils.isNotEmpty(manufacturers) && (attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MANUFACTURER) == null ||!attributeEnableMap.get(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MANUFACTURER)))
        {
            check = false;
            attributeId.add(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MANUFACTURER);
        }
        return check;
    }
}
