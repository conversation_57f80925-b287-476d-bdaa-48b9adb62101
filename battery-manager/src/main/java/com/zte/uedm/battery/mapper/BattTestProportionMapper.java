package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BackupPowerConfigPo;
import com.zte.uedm.battery.bean.BattTestProportionDimPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;

import java.sql.SQLException;
import java.util.List;

@Mapper
public interface BattTestProportionMapper
{
    /**
     * 占比纬度展示项查询
     * @param userName
     * @return
     * @throws UedmException
     */
    List<BattTestProportionDimPo> selectBattTestProportion(String userName) throws UedmException;

    void insertBattTestProportionByBeans(List<BattTestProportionDimPo> battTestProportionDimPos) throws UedmException;

    int deleteBattTestProportionByUserName(String userName) throws UedmException;

    Integer updateBattTestProportionByBeans(List<BattTestProportionDimPo> list) throws SQLException;
}
