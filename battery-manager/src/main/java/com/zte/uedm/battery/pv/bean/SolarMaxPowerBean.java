package com.zte.uedm.battery.pv.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @date Create on 2023/11/24 下午5:09
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SolarMaxPowerBean {
    /**
     * 太阳能pvId
     */
    private String pvId;
    /**
     * 记录时间
     */
    private String recordDate;
    /**
     * 站点id
     */
    private String siteId;
    /**
     * 最大功率
     */
    private String maxOutTotalPower;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
}
