package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.common.bean.log.OperlogDetail;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class TemplateHolidayVo
{
    /**
     * 节假日列表
     */
    @OperlogDetail(zhName = "时间周期",enName = "timeGran")
    private List<TemplateTimeGranVo> timeGran;
    /**
     * 节假日模式详情
     */
    @OperlogDetail(zhName = "错峰详情",enName = "detail")
    private List<TemplateTimeGranDetailVo> detail;
}
