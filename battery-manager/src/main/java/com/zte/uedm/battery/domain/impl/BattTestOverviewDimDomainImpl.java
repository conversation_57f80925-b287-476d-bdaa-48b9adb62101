package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.pojo.BattTestOverviewDimPo;
import com.zte.uedm.battery.domain.BattTestOverviewDimDomain;
import com.zte.uedm.battery.mapper.BattTestOverviewDimensionsMapper;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class BattTestOverviewDimDomainImpl implements BattTestOverviewDimDomain {

    @Resource
    private BattTestOverviewDimensionsMapper battTestOverviewDimensionsMapper;

    @Override
    public List<BattTestOverviewDimPo> selectByUserName(String scene,String userName) throws UedmException {
        if (StringUtils.isBlank(userName)) {
            log.warn("BattTestOverviewDimDomainImpl selectByUserName userName is empty!");
            return Collections.emptyList();
        }
        try {
            return battTestOverviewDimensionsMapper.selectByUserName(scene,userName);
        } catch (Exception e) {
            log.error("BattTestOverviewDimDomainImpl selectByUserName error", e);
            throw UedmErrorCodeDatabaseUtil.databaseSelectFailed("Data query error!");
        }
    }

    @Override
    public Integer insertBattTestOverviewDims(List<BattTestOverviewDimPo> battTestOverviewDimPoList) throws UedmException {
        if (CollectionUtils.isEmpty(battTestOverviewDimPoList)) {
            log.warn("BattTestOverviewDimDomainImpl insertBattTestOverviewDims data is empty!");
            return 0;
        }
        try {
            return battTestOverviewDimensionsMapper.insertBattTestOverviewDims(battTestOverviewDimPoList);
        } catch (Exception e) {
            log.error("BattTestOverviewDimDomainImpl insertBattTestOverviewDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed("Data insert error!");
        }
    }

    @Override
    public Integer updateBattTestOverviewDims(List<BattTestOverviewDimPo> battTestOverviewDimPoList) throws UedmException {
        if (CollectionUtils.isEmpty(battTestOverviewDimPoList)) {
            log.warn("BattTestOverviewDimDomainImpl updateBattTestOverviewDims data is empty!");
            return 0;
        }
        try {
            return battTestOverviewDimensionsMapper.updateBattTestOverviewDims(battTestOverviewDimPoList);
        } catch (Exception e) {
            log.error("BattTestOverviewDimDomainImpl updateBattTestOverviewDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }
}
