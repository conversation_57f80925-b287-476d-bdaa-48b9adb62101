package com.zte.uedm.battery.pv.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 阶梯计费--档位
 */
public enum TieredEnum {
    FIRST_TIERED("TIER_1", 1),
    SECOND_TIERED("TIER_2", 2),
    THIRD_TIERED("TIER_3", 3),
    FOURTH_TIERED("TIER_4", 4);
    private final String code;
    private final Integer value;
    TieredEnum(String code, Integer value){
        this.code = code;
        this.value = value;
    };
    public String getCode() {
        return this.code;
    }

    public Integer getValue() {
        return this.value;
    }

    public static Integer getValueByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        TieredEnum[] enums = TieredEnum.values();
        for (TieredEnum grainEnum : enums) {
            if (grainEnum.getCode().equals(code)) {
                return grainEnum.getValue();
            }
        }
        return null;
    }
}
