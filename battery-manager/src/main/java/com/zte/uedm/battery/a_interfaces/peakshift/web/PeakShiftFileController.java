package com.zte.uedm.battery.a_interfaces.peakshift.web;

import com.alibaba.fastjson.JSON;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.basis.util.base.response.ResponseBeanUtils;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftFileService;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftFileDeviceDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftFileDeviceStatusDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.DetailHistoryVo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.ImportFileResponseVo;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.peak.DetailHistoryResposeBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.CsrfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Path("peak-shift-file/device")
@Component
@Api(value = "peak-shift-file")
@Slf4j
public class PeakShiftFileController {

    @Autowired
    private PeakShiftFileService peakShiftFileService;

    @Autowired
    private CsrfService csrfService;

    /**
     * 设备侧下载接口
     * @return*/

    /* Started by AICoder, pid:m8616r3dd5y98ce14f32093740b13d203ac5ca62 */
    @POST
    @Path("/down")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "get file for devices", notes = "get file for devices", response = ResponseBean.class, httpMethod = "POST")
    public ResponseBean deviceGetPeakShiftStrategyFile(@NotNull PeakShiftFileDeviceDto deviceBean,
                                                       @HeaderParam("language-option") String languageOption,
                                                       @Context HttpServletRequest request,
                                                       @Context HttpServletResponse response) {

        log.info("device down start {}", JSON.toJSONString(deviceBean));
        // 调用服务层方法获取结果
        Map<Integer, String> resultMap = peakShiftFileService.deviceGetPeakShiftStrategyFile(deviceBean, languageOption, request, response);

        // 检查结果是否为空
        if (resultMap == null || resultMap.isEmpty()) {
            return ResponseBeanUtils.getNormalResponseBean(-1, null, "result is empty", null, null);
        }

        // 获取第一个键值对
        Map.Entry<Integer, String> entry = resultMap.entrySet().iterator().next();
        Integer key = entry.getKey();
        String value = entry.getValue();

        log.info("device down end {}", JSON.toJSONString(entry));
        // 返回响应
        return ResponseBeanUtils.getNormalResponseBean(key, null, value, value, null);
    }
    /* Ended by AICoder, pid:m8616r3dd5y98ce14f32093740b13d203ac5ca62 */


    /**
     * 设备侧上传错峰策略文件接口
     */
    /* Started by AICoder, pid:u5d35n8ba0m222114233084ae051e6467030e838 */
    @POST
    @Path("/upload")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "device upload strategy file", notes = "device upload strategy file", httpMethod = "POST")
    public ResponseBean deviceUploadPeakShiftStrategyFile(
            @QueryParam("Method") String method,
            @QueryParam("Date") String date,
            @QueryParam("DeviceId") String deviceId,
            @FormDataParam("file") InputStream fis,
            @FormDataParam("file") FormDataContentDisposition disp,
            @HeaderParam("language-option") String languageOption,
            @Context HttpServletRequest request
    ) {
        PeakShiftFileDeviceDto deviceBean = new PeakShiftFileDeviceDto();
        deviceBean.setMethod(method);
        deviceBean.setDate(date);
        deviceBean.setDeviceId(deviceId);
        log.info("device upload start {}", JSON.toJSONString(deviceBean));

        Map<Integer, String> resultMap = peakShiftFileService.deviceUploadPeakShiftStrategyFile(
                deviceBean, fis, disp, languageOption, request
        );
        log.info("device upload end {}", JSON.toJSONString(resultMap));

        if (resultMap.containsKey(0)) {
            return ResponseBeanUtils.getNormalResponseBean(0, null, resultMap.get(0), null, null);
        }

        Integer key = resultMap.keySet().stream()
                .findFirst()
                .orElse(null);
        String value = key != null ? resultMap.get(key) : null;

        if (key != null && value != null) {
            return ResponseBeanUtils.getNormalResponseBean(key, "error", value, null, null);
        } else {
            return ResponseBeanUtils.getNormalResponseBean(-1, "error", "Unknown error occurred", null, null);
        }
    }
    /* Ended by AICoder, pid:u5d35n8ba0m222114233084ae051e6467030e838 */

    /**
     * 设备上传状态接口
     */
    /* Started by AICoder, pid:a5aa111cbajf54d14cba0b5d60d6bd2e25f8630f */
    @POST
    @Path("/upload-status")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "get status for devices", notes = "get status for devices", response = ResponseBean.class, httpMethod = "POST")
    public ResponseBean deviceGetPeakShiftStatus(
            @NotNull PeakShiftFileDeviceStatusDto peakShiftFileDeviceStatusBean,
            @HeaderParam("language-option") String languageOption,
            @Context HttpServletRequest request) {
        log.info("device upload status start {}", JSON.toJSONString(peakShiftFileDeviceStatusBean));
        Map<Integer, String> resultMap = peakShiftFileService.deviceGetPeakShiftStatus(peakShiftFileDeviceStatusBean, languageOption, request);
        log.info("device upload status end {}", JSON.toJSONString(resultMap));

        if (resultMap.containsKey(0)) {
            return ResponseBeanUtils.getNormalResponseBean(0, null, resultMap.get(0), null, null);
        }

        Integer firstKey = resultMap.keySet().stream()
                .findFirst()
                .orElse(null);

        if (firstKey != null) {
            String firstValue = resultMap.get(firstKey);
            return ResponseBeanUtils.getNormalResponseBean(firstKey, null, firstValue, null, null);
        }

        return ResponseBeanUtils.getNormalResponseBean(-1, null, "Unknown error", null, null);
    }
    /* Ended by AICoder, pid:a5aa111cbajf54d14cba0b5d60d6bd2e25f8630f */

    @GET
    @Path("/universal-download")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "universal-download", notes = "universal-download", httpMethod = "GET")
    public void universalDownLoad(@Context HttpServletResponse response,@QueryParam("fileId") String fileId)
    {
        try
        {
            try
            {
                csrfService.checkCsrfToken();
            }
            catch (UedmException e)
            {
                log.error("universalDownLoad uportal check fail");
            }
            List<String> fileIds = new ArrayList<>();
            fileIds.add(fileId);
            peakShiftFileService.downFileByIds(fileIds, response);
        }
        catch(Exception e)
        {
            log.error("universalDownLoad error", e);
        }
    }

    /* Started by AICoder, pid:r05e1m433d07b5914d3b0b1e2074b1103fa995e7 */
    @GET
    @Path("/export-template-to-xls")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "export template to xls", notes = "export template to xls", httpMethod = "GET")
    public ResponseBean exportFile(
            @QueryParam("id") String id,
            @HeaderParam("language-option") String languageOption,
            @Context HttpServletRequest request,
            @Context HttpServletResponse response
    ) {
        try {
            peakShiftFileService.exportFile(id, request, response, languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, null, 1);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("PeakShiftFileController exportFile error", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    /* Ended by AICoder, pid:r05e1m433d07b5914d3b0b1e2074b1103fa995e7 */


    /* Started by AICoder, pid:t04faqd921a8f8e146330b22d0cd0b11ed96184c */
    @POST
    @Path("/import-template-by-xls")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "import template by xls", notes = "import template by xls", httpMethod = "POST")
    public ResponseBean importFile(FormDataMultiPart fileData,
                                   @HeaderParam("language-option") String languageOption,
                                   @Context HttpServletRequest request) {
        try {
            ImportFileResponseVo responseBean = peakShiftFileService.importFile(fileData, request, languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, responseBean, 1);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("PeakShiftFileController importFile error", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    /* Ended by AICoder, pid:t04faqd921a8f8e146330b22d0cd0b11ed96184c */

    /* Started by AICoder, pid:k826b3d6e11ad58145e20830e06baf1143a99740 */
    @GET
    @Path("/detail-history-export")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "detail history export", notes = "detail history export", httpMethod = "GET")
    public ResponseBean detailHistoryExport(
            @QueryParam("id") String id,
            @HeaderParam("language-option") String languageOption,
            @Context HttpServletRequest request,
            @Context HttpServletResponse response) {

        try {
            List<DetailHistoryVo> result = peakShiftFileService.detailHistoryExport(id, Tools.getUserName(request), languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("PeakShiftFileController detailHistoryExport error", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }
    /* Ended by AICoder, pid:k826b3d6e11ad58145e20830e06baf1143a99740 */
}
