package com.zte.uedm.battery.pv.enums;

import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public enum SolarRevenuePriceEnums {
    SOLAR_FEE("totalSolarRevenue", "{\"zh_CN\":\"太阳能\",\"en_US\":\"Solar\"}", "$"),
    SAVINGS("totalSavings", "{\"zh_CN\":\"节省电费\",\"en_US\":\"Savings\"}", "$"),
    GRID("totalGridFee","{\"zh_CN\":\"市电\",\"en_US\":\"Grid\"}",  "$"),
    SOLAR_TIP_REVENUE("peakestSolarRevenue", "{\"zh_CN\":\"太阳能尖时段\",\"en_US\":\"Solar Tip\"}",  "$"),
    SOLAR_TIP_SAVINGS("peakestSavings","{\"zh_CN\":\"尖时段节省\",\"en_US\":\"Savings Tip\"}", "$"),
    GRID_TIP_FEE("peakestGridFee", "{\"zh_CN\":\"市电尖时段\",\"en_US\":\"Grid Tip\"}",  "$"),
    SOLAR_PEAK_REVENUE("peakSolarRevenue","{\"zh_CN\":\"太阳能峰时段\",\"en_US\":\"Solar Peak\"}",  "$"),
    SOLAR_PEAK_SAVINGS("peakSavings","{\"zh_CN\":\"峰时段节省\",\"en_US\":\"Savings Peak\"}",  "$"),
    GRID_PEAK_FEE("peakGridFee", "{\"zh_CN\":\"市电峰值时段\",\"en_US\":\"Grid Peak\"}",  "$"),
    SOLAR_PLAT_REVENUE("normalSolarRevenue", "{\"zh_CN\":\"太阳能平时段\",\"en_US\":\"Solar Flat\"}",  "$"),
    SOLAR_PLAT_SAVINGS("normalSavings", "{\"zh_CN\":\"平时段节省\",\"en_US\":\"Savings Flat\"}",  "$"),
    GRID_PLAT_FEE("normalGridFee","{\"zh_CN\":\"市电平时段\",\"en_US\":\"Grid Flat\"}",  "$"),
    SOLAR_VALLEY_REVENUE("lowSolarRevenue", "{\"zh_CN\":\"太阳能谷时段\",\"en_US\":\"Solar Valley\"}",  "$"),
    SOLAR_VALLEY_SAVINGS("lowSavings", "{\"zh_CN\":\"谷时段节省\",\"en_US\":\"Savings Valley\"}", "$"),
    GRID_VALLEY_FEE("lowGridFee", "{\"zh_CN\":\"市电谷时段\",\"en_US\":\"Grid Valley\"}", null),

    TODAY_REVENUE("todayRevenue", "{\"zh_CN\":\"今日发电收益\",\"en_US\":\"Today\"}",  null),
    This_Month_Revenue("monthRevenue","{\"zh_CN\":\"当月发电收益\",\"en_US\":\"This Month\"}",  null),
    This_Year_Revenue("yearRevenue", "{\"zh_CN\":\"当年发电收益\",\"en_US\":\"This Year\"}", null),
    Accum_revenue("accumulatedRevenue", "{\"zh_CN\":\"累计发电收益\",\"en_US\":\"Accum.\"}",  null),
    Today_Savings("todaySavings","{\"zh_CN\":\"今日节省电费\",\"en_US\":\"Today Savings\"}", null),
    This_Month_Savings("monthSavings", "{\"zh_CN\":\"当月节省电费\",\"en_US\":\"This Month Savings\"}", null),
    This_Year_Savings("yearSavings", "{\"zh_CN\":\"当年节省电费\",\"en_US\":\"This Year Savings\"}", null),
    Accum_Savings("accumulatedSavings", "{\"zh_CN\":\"累计节省电费\",\"en_US\":\"Accum. Savings\"}",  null);

    /**
     * id
     */
    private String id;
    /**
     * 中英文名称
     */
    private String name;
    /**
     * 单位
     */
    private String unit;

    public String getId() { return this.id; }
    public String getName() { return this.name; }

    public String getUnit() {
        return this.unit;
    }

    SolarRevenuePriceEnums(String id, String name,  String unit)
    {
        this.id = id;
        this.name = name;
        this.unit = unit;
    }

    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getIdsWithIdName() {
        List<String> ids = new ArrayList<>();
        ids.add(SolarRevenuePriceEnums.SOLAR_FEE.getId());
        ids.add(SolarRevenuePriceEnums.SAVINGS.getId());
        ids.add(SolarRevenuePriceEnums.GRID.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_PEAK_REVENUE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_PEAK_SAVINGS.getId());
        ids.add(SolarRevenuePriceEnums.GRID_PEAK_FEE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_PLAT_REVENUE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_PLAT_SAVINGS.getId());
        ids.add(SolarRevenuePriceEnums.GRID_PLAT_FEE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_TIP_REVENUE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_TIP_SAVINGS.getId());
        ids.add(SolarRevenuePriceEnums.GRID_TIP_FEE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_VALLEY_REVENUE.getId());
        ids.add(SolarRevenuePriceEnums.SOLAR_VALLEY_SAVINGS.getId());
        ids.add(SolarRevenuePriceEnums.GRID_VALLEY_FEE.getId());
        ids.add(SolarRevenuePriceEnums.TODAY_REVENUE.getId());
        ids.add(SolarRevenuePriceEnums.This_Month_Revenue.getId());
        ids.add(SolarRevenuePriceEnums.This_Year_Revenue.getId());
        ids.add(SolarRevenuePriceEnums.Accum_revenue.getId());
        ids.add(SolarRevenuePriceEnums.Today_Savings.getId());
        ids.add(SolarRevenuePriceEnums.This_Month_Savings.getId());
        ids.add(SolarRevenuePriceEnums.This_Year_Savings.getId());
        ids.add(SolarRevenuePriceEnums.Accum_Savings.getId());
        return ids;
    }
    private static Map<String, SolarRevenuePriceEnums> map;

    static {
        map = Arrays.stream(SolarRevenuePriceEnums.values())
                .collect(Collectors.toMap(SolarRevenuePriceEnums::getId, a -> a));
    }

    /**
     * 根据id获取名称
     */
    public static String getNameById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarRevenuePriceEnums::getName)
                .orElse(null);
    }


    /**
     * 获取所有id
     */
    public static List<String> getAllIds() {
        return Arrays.stream(SolarRevenuePriceEnums.values())
                .map(SolarRevenuePriceEnums::getId)
                .collect(Collectors.toList());
    }

    /**
     * id校验
     */
    public static void checkIdIsNeeded(List<String> ids) throws UedmException {
        if (CollectionUtils.isEmpty(ids)) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim ids is empty!");
            throw new UedmException(-200, "Dim ids is empty!");
        }
        ids.removeAll(SolarRevenuePriceEnums.getAllIds());
        if (ids.size() > 0) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim dim id beyond limit!");
            throw new UedmException(-302, "Dim id beyond limit");
        }
    }

    /**
     * 根据id获取单位
     * @param id
     * @return
     */
    public static String getUnitById(String id)
    {
        if(StringUtils.isBlank(id))
        {
            return null;
        }

        SolarRevenuePriceEnums[] values = SolarRevenuePriceEnums.values();
        for(SolarRevenuePriceEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.getUnit();
            }
        }
        return null;
    }
}
