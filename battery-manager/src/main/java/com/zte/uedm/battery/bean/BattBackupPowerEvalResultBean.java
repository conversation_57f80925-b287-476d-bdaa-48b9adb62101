package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
@Setter
@Getter
@ToString
public class BattBackupPowerEvalResultBean {
    //备电时长
    BigDecimal result;
    //备电时长(告警)
    BigDecimal alarmResult;
    //备电时长(恒流)
    BigDecimal currResult;
    //备电时长（铅酸铁锂混用场景，分开显示）
    String liAndLeadAlarmResult;
    //备电时长（铅酸铁锂混用场景，分开显示）
    String liAndLeadCurrResult;
    //满充判断(1,满充，0，非满充或无法判断)
    Integer max;
}
