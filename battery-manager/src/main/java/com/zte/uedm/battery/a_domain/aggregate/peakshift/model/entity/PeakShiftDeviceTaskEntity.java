package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:tef7e5606bz042c148e10b97f0c1b7110ff132ad */
/**
 * 错峰用电设备任务信息Bean
 */
@Setter
@Getter
@ToString
public class PeakShiftDeviceTaskEntity {
    /**
     * 设备id
     */
    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;
    /**
     * 设备策略类型 0--自动 1--人工
     */
    private String strategyType;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 设备状态
     */
    private String deviceStatus;


    /**
     * 设备位置
     */
    private String position;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 生效时间
     */
    private String effectiveDate;

    /**
     * 失效时间
     */
    private String expirationDate;

    /**
     * 生效状态  expired，nearToExpired ，normal
     * 当前时间>失效时间？失效; 当前时间+7d>失效时间？即将失效; 其余：正常
     */
    private String effectiveStatus;

    /**
     * 电价时段
     */
    private String priceIntervalName;

    /**
     * 在线状态ID
     * online/offline
     * 在线/离线
     */
    private String deviceOnlineId;

    /**
     * 在线状态名称(国际化)
     */
    private String deviceOnlineName;

    /**
     * 季节策略id
     */
    private String seasonStrategyId;
}
/* Ended by AICoder, pid:tef7e5606bz042c148e10b97f0c1b7110ff132ad */