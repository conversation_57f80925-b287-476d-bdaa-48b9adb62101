package com.zte.uedm.battery.a_domain.safe;


import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryHistoryLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryOriginalLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryRealTimeLocationBean;

import java.util.List;


public interface BatteryLocationService
{
    /**
     * 根据ID查询电池位置
     * @param moId
     * @return
     */
    BatteryOriginalLocationBean selectByMoId(String moId);

    /**
     * 设置电池位置
     * @param bean
     */
    void addOrUpdateLocation(BatteryOriginalLocationBean bean);

    /**
     * 根据对象id集合查询
     * @param moIdList
     * @return
     */
    List<BatteryOriginalLocationBean> selectByMoIds(List<String> moIdList);

    /**
     * 根据id查询电池位置
     */
    BatteryOriginalLocationBean selectByMoIdForCache(String id);

    /**
     * 根据id查询电池实时位置
     */
    BatteryRealTimeLocationBean selectRealByMoIdForCache(String id);

    /**
     * 批量插入或更新电池位置
     */
    void addOrUpdateLocationBatch(List<BatteryOriginalLocationBean> beanList);

    /**
     * 批量插入或更新电池位置
     */
    void addOrUpdateHistoryLocationBatch(List<BatteryRealTimeLocationBean> beanList);
}
