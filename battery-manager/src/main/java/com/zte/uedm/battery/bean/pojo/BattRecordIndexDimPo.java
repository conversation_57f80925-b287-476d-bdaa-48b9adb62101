package com.zte.uedm.battery.bean.pojo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ Author     ：10260977
 * @ Date       ：11:29 2022/8/10
 * @ Description：测试记录维度表数据库
 * @ Modified By：
 * @ Version: 1.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class BattRecordIndexDimPo
{
    /**
     * 记录属性id
     */
    private String id;

    /**
     * 记录属性名称-国际化
     */
    private String userName;
    /**
     * 设备类型
     */
    private String scene;
    /**
     * 是否被勾选
     */
    private boolean selected;

    /**
     * 展示顺序
     */
    private Integer showSequence;

    /**
     * 创建者
     */
    private String creator;
    /**
     * 更新者
     */
    private String updater;

    private Date gmtCreate;

    private Date gmtModified;
}
