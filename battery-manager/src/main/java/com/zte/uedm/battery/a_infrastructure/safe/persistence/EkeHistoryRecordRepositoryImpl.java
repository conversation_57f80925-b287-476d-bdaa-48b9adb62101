package com.zte.uedm.battery.a_infrastructure.safe.persistence;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zte.uedm.battery.a_domain.aggregate.safe.EkeHistoryRecordEntity;
import com.zte.uedm.battery.a_domain.safe.repository.EkeHistoryRecordRepository;
import com.zte.uedm.battery.a_infrastructure.safe.converter.EkeHistoryRecordConverter;
import com.zte.uedm.battery.a_infrastructure.safe.po.EkeHistoryRecordPo;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.EkeHistoryRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EkeHistoryRecordRepositoryImpl implements EkeHistoryRecordRepository {

    @Resource
    private EkeHistoryRecordMapper ekeHistoryRecordMapper;
    private static final String IS_CURR_FIELD = "is_curr";
    private static final String EKEY_FIELD = "ekey";

    @Override
    @Transactional
    public Integer insert(EkeHistoryRecordPo ekeHistoryRecordPo) {
        return ekeHistoryRecordMapper.insert(ekeHistoryRecordPo);
    }

    @Transactional
    public Integer update(EkeHistoryRecordPo ekeHistoryRecordPo){
        QueryWrapper<EkeHistoryRecordPo> queryWrapper = buildCondition();
        return ekeHistoryRecordMapper.update(ekeHistoryRecordPo,queryWrapper);
    }

    @Override
    public EkeHistoryRecordEntity select(EkeHistoryRecordPo queryPO) {
        QueryWrapper<EkeHistoryRecordPo> queryWrapper = buildCondition();
        EkeHistoryRecordPo ekeHistoryRecordPo = ekeHistoryRecordMapper.selectOne(queryWrapper);
        return EkeHistoryRecordConverter.converterPoToEn(ekeHistoryRecordPo);
    }

    public QueryWrapper<EkeHistoryRecordPo> buildCondition(){
        QueryWrapper<EkeHistoryRecordPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(IS_CURR_FIELD, true);
        return queryWrapper;
    }

    @Override
    @Transactional
    public boolean existEKey() {
        QueryWrapper<EkeHistoryRecordPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(IS_CURR_FIELD, true);
        return ekeHistoryRecordMapper.exists(queryWrapper);
    }

    @Override
    public EkeHistoryRecordEntity selectEkeyHistoryOne(EkeHistoryRecordPo queryPo) {
        QueryWrapper<EkeHistoryRecordPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(EKEY_FIELD,queryPo.getEkey());
        EkeHistoryRecordPo ekeHistoryRecordPo = ekeHistoryRecordMapper.selectOne(queryWrapper);
        return EkeHistoryRecordConverter.converterPoToEn(ekeHistoryRecordPo);
    }

    @Override
    public List<EkeHistoryRecordEntity> selectList(EkeHistoryRecordPo queryPo) {
        List<EkeHistoryRecordPo> ekeHistoryRecordPoList = ekeHistoryRecordMapper.selectList(new QueryWrapper<>());
        return EkeHistoryRecordConverter.converterPoToEnList(ekeHistoryRecordPoList);
    }
}
