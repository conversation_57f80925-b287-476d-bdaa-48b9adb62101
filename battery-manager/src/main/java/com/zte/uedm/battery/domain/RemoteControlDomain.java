package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.RemoteControlInfoBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.lang.reflect.InvocationTargetException;

/**
 * 远程命令
 */
public interface RemoteControlDomain
{
    /**
     * 远程控制命令下发
     * @param controlInfoBean
     * @param serviceBean
     */
    void remoteControl(RemoteControlInfoBean controlInfoBean, ServiceBaseInfoBean serviceBean)
            throws UedmException, InvocationTargetException, IllegalAccessException;

    void remoteControl(String moId, String smpId, String value,String logId) throws UedmException;
}
