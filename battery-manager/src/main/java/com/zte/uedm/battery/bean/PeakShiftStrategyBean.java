package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 错峰用电策略Bean
 */
@Setter
@Getter
@ToString
public class PeakShiftStrategyBean
{

	private String id;

	private String name;

	/**
	 * 作用范围JSON
	 */
	private String effectiveScope;

	/**
	 * 作用范围Bean
	 */
	private List<PeakShiftStrategyScopeBean> scopeBeanList;

	//0：日模式 1：周模式 2：月模式
	private Integer mode;

	//0：拟制中 1：启用 2：停用
	private Integer status;

	private String effectiveTime;

	private String expirationTime;

	private String remark;

	private String startDate;

	private String endDate;

	private String gmtCreate;

	private String gmtModified;
}