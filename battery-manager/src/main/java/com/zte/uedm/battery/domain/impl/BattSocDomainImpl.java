package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.domain.BattSocDomain;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.enums.batt.BattPrstSocLevelEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class BattSocDomainImpl implements BattSocDomain
{

    @Autowired
    private I18nUtils i18nUtils;

    @Override
    public PageInfo<IdNameBean> selectSocLevels(ServiceBaseInfoBean serviceBean) throws UedmException
    {
        if(serviceBean == null)
        {
            log.warn("BattSocDomainImpl->selectSocLevels, serviceBean is null. return");
            return new PageInfo<>(new ArrayList<>());
        }
        log.info("BattSocDomainImpl->selectSocLevels, serviceBean={}", serviceBean);

        List<IdNameBean> socLevels = new ArrayList<>();

        List<Pair<String, String>> socLevelEnums = BattPrstSocLevelEnum.getAllBattPrstSocLevels();
        log.info("get batt soc level enums 'size={}, socLevelEnums={}", socLevelEnums.size(), socLevelEnums);
        for(Pair<String, String> pair : socLevelEnums)
        {
            String name = i18nUtils.getMapFieldByLanguageOption(pair.getRight(), serviceBean.getLanguageOption());
            IdNameBean idNameBean = new IdNameBean(pair.getLeft(), name);
            socLevels.add(idNameBean);
        }
        List<IdNameBean> list = PageUtils.getPageList(socLevels, serviceBean.getPageNo(), serviceBean.getPageSize());
        log.info("BattSocDomainImpl->selectSocLevels success., result' size={},total={}", list.size(), socLevels.size());

        PageInfo<IdNameBean> page = new PageInfo<>(list);
        page.setTotal(socLevels.size());
        return page;
    }

    @Override
    public Double transSmpValueIntoValue(String smpValue)
    {
        if(StringUtils.isBlank(smpValue))
        {
            return null;
        }

        try {
            return Double.valueOf(smpValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
