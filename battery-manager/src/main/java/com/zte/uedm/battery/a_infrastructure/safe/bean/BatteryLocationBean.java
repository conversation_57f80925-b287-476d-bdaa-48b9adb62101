package com.zte.uedm.battery.a_infrastructure.safe.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @create 2025/03/24
 * @description
 */
@Setter
@Getter
@ToString
public class BatteryLocationBean {
    /**
     * 监控对象Id
     */
    private String batteryId;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 更新时间
     */
    private String recordTime;
    /**
     * 电池运动状态 0静止 1运动 空未知
     */
    private String status;

    private String batteryName;
}
