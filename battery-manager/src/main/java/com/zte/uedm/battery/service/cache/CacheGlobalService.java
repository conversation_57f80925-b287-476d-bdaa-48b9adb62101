package com.zte.uedm.battery.service.cache;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import java.util.List;

public interface CacheGlobalService {
    Object getAllCache(String cacheName);

    Object getAllCacheByKey(String cacheName, String key);

    List<BaseCacheBean> getBatteryCache(String cacheName) throws UedmException;

    BaseCacheBean getBatteryCacheByKey(String cacheName, String key) throws UedmException;
}
