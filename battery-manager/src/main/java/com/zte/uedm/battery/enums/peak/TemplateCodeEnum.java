/* Started by AICoder, pid:v7acbs024al727314ed00859a0f0742d58063ee5 */
package com.zte.uedm.battery.enums.peak;

public enum TemplateCodeEnum {
    /**
     * 获取模板相应的数字
     */
    DAILYTEMPLATE("0", "{\"zh_CN\":\"日\",\"en_US\":\"daily\"}"),
    WEEKTEMPLATE("1", "{\"zh_CN\":\"周\",\"en_US\":\"week\"}"),
    MONTHTEMPLATE("2", "{\"zh_CN\":\"月\",\"en_US\":\"month\"}");

    private String id;
    private String name;

    TemplateCodeEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getId() {
        return this.id;
    }
}
/* Ended by AICoder, pid:v7acbs024al727314ed00859a0f0742d58063ee5 */