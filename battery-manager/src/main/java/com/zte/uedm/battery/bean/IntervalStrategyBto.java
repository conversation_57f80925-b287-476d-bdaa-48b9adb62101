package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 时段策略新增、编辑参数Bean
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@ApiModel(description = "时段策略")
public class IntervalStrategyBto
{
    /**
     * 季节策略id
     */
    @ApiModelProperty(value = "季节策略标识")
    private String seasonId;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private String effectiveTime;

    /**
     * 范围策略id
     */
    @ApiModelProperty(value = "范围策略标识")
    private String scopeStrategyId;

    /**
     * 季节策略
     */
    @ApiModelProperty(value = "季节策略")
    private List<IntervalStrategyBean> season;

    /**
     * 节假日模式
     */
    @ApiModelProperty(value = "节假日模式")
    private List<HolidayDetailBean> holiday;
}
