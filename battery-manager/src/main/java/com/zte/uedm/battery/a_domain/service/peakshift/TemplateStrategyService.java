package com.zte.uedm.battery.a_domain.service.peakshift;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.IssHistoryEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyEntity;
import com.zte.uedm.battery.a_interfaces.peakshift.inner.dto.QueryFileIdRpcDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateQueryDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto;
import com.zte.uedm.battery.bean.peak.IssuedHistoryBo;
import com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBo;
import com.zte.uedm.common.bean.IsKafkaBean;
import com.zte.uedm.common.bean.log.OperlogBean;

import java.util.List;

public interface TemplateStrategyService {

    /**
     * 新增设备模板策略
     *
     * @param strategyBaseDto
     * @param userCreate
     * @return
     * @throws UedmException
     */
    void addTemplateStrategy(TemplateStrategyAddDto strategyBaseDto, String userCreate) throws UedmException;

    /**
     * 编辑设备模板策略
     *
     * @param templateStrategyEditDto
     * @param user_modified
     * @return
     * @throws UedmException
     */
    void editTemplateStrategy(TemplateStrategyEditDto templateStrategyEditDto, String user_modified) throws UedmException;

    /**
     * 条件查询设备模板策略
     *
     * @param queryBean
     * @return
     * @throws UedmException
     */
    PageInfo<TemplateStrategyEntity> searchByConditions(TemplateQueryDto queryBean, String languageOption) throws UedmException;

    /**
     * 详情查询设备模板策略
     *
     * @param id
     * @param languageOption
     * @return
     * @throws UedmException
     */
    TemplateStrategyDetailEntity selectDetailByTemplateId(String id, String userName, String languageOption) throws  UedmException;

    /**
     * 检查用户输入的名称是否唯一，或者超长
     *
     * @param id   template_strategy 的id
     * @param name 用户输入的name
     * @return 是否唯一
     * @throws UedmException
     */
    Boolean checkNameUnique(String id, String name) throws UedmException;

    /**
     * 删除错峰模板策略
     *
     * @param ids
     * @throws UedmException
     */
    int deleteTemplateStrategy(List<String> ids, OperlogBean operlogBean, IsKafkaBean isKafkaBean) throws UedmException;

    /**
     * 通过taskId查询历史快照
     *
     * @param taskId
     * @return
     * @throws UedmException
     */
    IssHistoryEntity selectHistory(String taskId) throws UedmException;

    List<String> getFileId(QueryFileIdRpcDto dto) throws UedmException;

    String getTaskIdByTemplateId(QueryFileIdRpcDto dto) throws UedmException;

   }
