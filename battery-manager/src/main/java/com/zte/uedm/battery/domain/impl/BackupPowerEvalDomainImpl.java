package com.zte.uedm.battery.domain.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.controller.backuppower.dto.DecreaseStatisticsNewDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalDetailDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalTrendDto;
import com.zte.uedm.battery.controller.backuppower.dto.TableDetailSelectDto;
import com.zte.uedm.battery.domain.BackupPowerEvalDomain;
import com.zte.uedm.battery.mapper.BattBackupPowerEvalMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.consts.CommonConst.BATT_BACK_POWER;
import static com.zte.uedm.battery.consts.CommonConst.CACHE_NAME_BATT_BACK_POWER_EVAL;

@Service
@Slf4j
public class BackupPowerEvalDomainImpl implements BackupPowerEvalDomain
{
    @Autowired
    private BattBackupPowerEvalMapper backupPowerMapper;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Autowired
    private CommonCacheService cacheService;

    private static final String  EN_US = "en-US";
    private static final String  ZH_CN = "zh-CN";

    private static final String SP = MocOptional.SP.getId();
    private static final String BP = MocOptional.BATTERY_SET.getId();

    private static final String ABSENT = "no.absent";
    private static final String Power_Supply_Equipment = "Power Supply Equipment";
    private static final String BATTERY_PACK = "Battery Pack(Independent Monitoring)";

    private static final String Power_Supply_Equipment_ZH = "电源设备";
    private static final String BATTERY_PACK_ZH = "电池组(独立监控)";

    @Override
    public void initBackPowerUp(){
        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = backupPowerMapper.selectEvalDByMoIds(null);
        List<BatteryBackupPowerEvalCachePojo> battBackupPowerEvalPojoss = new ArrayList<>();
        for (BattBackupPowerEvalPojo batteryBackupPowerEvalPojo1 : battBackupPowerEvalPojos) {
            BatteryBackupPowerEvalCachePojo batteryBackupPowerEvalPojo = new BatteryBackupPowerEvalCachePojo();
            BeanUtils.copyProperties(batteryBackupPowerEvalPojo1, batteryBackupPowerEvalPojo);
            battBackupPowerEvalPojoss.add(batteryBackupPowerEvalPojo);
        }
        cacheService.put(CACHE_NAME_BATT_BACK_POWER_EVAL, BATT_BACK_POWER, battBackupPowerEvalPojoss);
    }


    /**
     * 备电评估结果详情-条件查询
     * @param evalDetailDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public PageInfo<BattBackupPowerEvalPojo> selectByCondition(EvalDetailDto evalDetailDto,
                                                               ServiceBaseInfoBean serviceBean) throws UedmException
    {
        if (null == evalDetailDto || null == serviceBean) {
            log.error("BackupPowerDomainImpl selectByCondition evalDetailDto or serviceBean is empty");
            throw new UedmException(-100, "params evalDetailDto or serviceBean is empty");
        }
        log.info("BackupPowerDomainImpl selectByCondition evalDetailDto:{},serviceBean:{}",evalDetailDto,serviceBean);
        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos;

        try {
            battBackupPowerEvalPojos = backupPowerMapper.selectByCondition(evalDetailDto);

            battBackupPowerEvalPojos = filterDuplicateDataByIdAndEvalTime(battBackupPowerEvalPojos);

            //根据节点id和权限过滤
            battBackupPowerEvalPojos = getResultList(battBackupPowerEvalPojos, evalDetailDto.getLogicGroupId(),serviceBean.getUserName());
            log.info("BackupPowerDomainImpl selectByCondition battBackupPowerEvalPojos.size:{}",battBackupPowerEvalPojos.size());
            battBackupPowerEvalPojos = filterDeletedSPDetail(battBackupPowerEvalPojos);

            List<BattBackupPowerEvalPojo> pageList = PageUtil.getPageList(battBackupPowerEvalPojos,
                    serviceBean.getPageNo(),serviceBean.getPageSize());
            PageInfo<BattBackupPowerEvalPojo> res = new PageInfo<>(pageList);
            res.setTotal(battBackupPowerEvalPojos.size());
            return res;

        } catch (Exception e) {
            log.error("BackupPowerDomainImpl selectByCondition  select DB is error :{}", e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(),
                    DatabaseExceptionEnum.OPERATEDB.getDesc());
        }

    }

    private List<BattBackupPowerEvalPojo> filterDuplicateDataByIdAndEvalTime(List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos) {
        LinkedHashMap<String, BattBackupPowerEvalPojo> resultMap= new LinkedHashMap<>();
        for (BattBackupPowerEvalPojo item : battBackupPowerEvalPojos) {
            if(resultMap.containsKey(item.getId())){
                BattBackupPowerEvalPojo battBackupPowerEvalPojo = resultMap.get(item.getId());
                if(null == battBackupPowerEvalPojo || battBackupPowerEvalPojo.getEvalTime().compareTo(item.getEvalTime()) < 0)
                {
                    resultMap.remove(item.getId());
                    resultMap.put(item.getId(), item);
                }
            }
            else
            {
                resultMap.put(item.getId(), item);
            }
        }
        battBackupPowerEvalPojos = resultMap.values().stream().collect(Collectors.toList());
        return battBackupPowerEvalPojos;
    }


    /**
     * 过滤调已经删除的开关电源监控对象详情
     * @param battBackupPowerEvalPojos
     * @return
     * @throws UedmException
     */
    public List<BattBackupPowerEvalPojo> filterDeletedSPDetail(List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos) throws UedmException {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojoList = new ArrayList<>();
        List<String> mocTypes = new ArrayList<>();
        mocTypes.add(MocOptional.SP.getId());
        mocTypes.add(MocOptional.BATTERY_SET.getId());
        Map<String, String> idAndNameResMap = new HashMap<>();
        for(String mocType:mocTypes) {
            List<ResourceBaseBean> resourceBeanListByMoc = configurationManagerRpcImpl.getResourceBeanListByMoc(mocType);
            if (CollectionUtils.isNotEmpty(resourceBeanListByMoc)) {
                Map<String, String> idAndNameMap = resourceBeanListByMoc.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).filter(bean -> StringUtils.isNotBlank(bean.getMoc()))
                        .collect(Collectors.toMap(ResourceBaseBean::getId, ResourceBaseBean::getMoc, (key1, key2) -> key2));
                idAndNameResMap.putAll(idAndNameMap);
            }
        }
        for (BattBackupPowerEvalPojo battBackupPowerEvalPojo : battBackupPowerEvalPojos) {
            if (idAndNameResMap.containsKey(battBackupPowerEvalPojo.getId())) {
                battBackupPowerEvalPojoList.add(battBackupPowerEvalPojo);
            }
        }
        return battBackupPowerEvalPojoList;
    }

    public List<BattBackupPowerEvalPojo> getResultList(List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos, String logicGroupId,String userName) throws UedmException
    {
        List<BattBackupPowerEvalPojo> resultList = new ArrayList<>();
        List<String> authIds = configurationManagerRpcImpl.getAuthPositionsByUser(userName);
        Set<String> authIdSets = new HashSet<>(authIds);
        //根据逻辑组判断是不是在idpath中 再看开关电源的id是否在有权限的ids中
        for (BattBackupPowerEvalPojo item : battBackupPowerEvalPojos)
        {
            String[] split = item.getPathIds().split("/");
            List<String> pathIdList = Arrays.asList(split);
            //判断是不是root节点 root就不根据逻辑组id过滤
            judgeAuth(resultList, authIdSets, item);
        }
        return resultList;
    }

    private void judgeAuth(List<BattBackupPowerEvalPojo> resultList, Set<String> authIdSets, BattBackupPowerEvalPojo item)
    {
        if (CollectionUtils.isNotEmpty(authIdSets))
        {
            if (authIdSets.contains(item.getId()))
            {
                resultList.add(item);
            }
        }
        else
        {
            resultList.add(item);
        }
    }


    @Override
    public List<BattBackupPowerOverviewPojo> selectByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException {
        if(null==logicGroupId || null==serviceBean)
        {
            log.error("BackupPowerDomainImpl selectByLogicGroupId logicGroupId or serviceBean is empty");
            throw new UedmException(-100,"params logicGroupId or serviceBean is empty");
        }
        log.info("BackupPowerDomainImpl selectByLogicGroupId logicGroupId:{},serviceBean:{}",logicGroupId,serviceBean);

        String langConversion = i18nUtils.getLangConversion(serviceBean.getLanguageOption());
        try
        {
            List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = backupPowerMapper.selectByLogicGroupId(logicGroupId,langConversion);
            if (!StringUtils.equalsIgnoreCase(GlobalOptional.GLOBAL_ROOT, logicGroupId)) {
                battBackupPowerOverviewPojos = getPoJoResultListByLogicGroupId(battBackupPowerOverviewPojos, logicGroupId);
            }

            battBackupPowerOverviewPojos = filterDeletedSP(battBackupPowerOverviewPojos);

            return battBackupPowerOverviewPojos;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectByLogicGroupId  select DB is error :{}",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    /**
     * 过滤调已经删除的开关电源监控对象
     * @param battBackupPowerOverviewPojos
     * @return
     * @throws UedmException
     */
    private List<BattBackupPowerOverviewPojo> filterDeletedSP(List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos) throws UedmException {
        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojoList = new ArrayList<>();
        List<ResourceBaseBean> resourceBeanListByMoc = configurationManagerRpcImpl.getResourceBeanListByMoc(MocOptional.SP.getId());
        if(CollectionUtils.isNotEmpty(resourceBeanListByMoc)) {
            Map<String, String> idAndNameMap = resourceBeanListByMoc.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).filter(bean -> StringUtils.isNotBlank(bean.getMoc()))
                    .collect(Collectors.toMap(ResourceBaseBean::getId, ResourceBaseBean::getMoc, (key1,key2)->key2));
            for (BattBackupPowerOverviewPojo battBackupPowerOverviewPojo : battBackupPowerOverviewPojos) {
                if (idAndNameMap.containsKey(battBackupPowerOverviewPojo.getId()))
                {
                    battBackupPowerOverviewPojoList.add(battBackupPowerOverviewPojo);
                }
            }
        }
        return battBackupPowerOverviewPojoList;
    }

    /**
     * 备电状态增减情况 - 查询
     * @param logicGroupId
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public List<BattBackupPowerOverviewPojo> selectInDesByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException {
        if(null==logicGroupId || null==serviceBean)
        {
            log.error("BackupPowerDomainImpl selectByLogicGroupId logicGroupId or serviceBean is empty");
            throw new UedmException(-100,"params logicGroupId or serviceBean is empty");
        }
        log.info("BackupPowerDomainImpl selectByLogicGroupId logicGroupId:{},serviceBean:{}",logicGroupId,serviceBean);

        String langConversion = i18nUtils.getLangConversion(serviceBean.getLanguageOption());
        try
        {
            List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = backupPowerMapper.selectInDesByLogicGroupId(logicGroupId,langConversion);
            if (!StringUtils.equalsIgnoreCase(GlobalOptional.GLOBAL_ROOT, logicGroupId)) {
                battBackupPowerOverviewPojos = getPoJoResultListByLogicGroupId(battBackupPowerOverviewPojos, logicGroupId);
            }

            battBackupPowerOverviewPojos = filterDeletedSP(battBackupPowerOverviewPojos);

            return battBackupPowerOverviewPojos;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectByLogicGroupId  select DB is error :{}",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }


    /**
     * 备电详情 - 查询
     * @param logicGroupId
     * @return
     * @throws UedmException
     */
    @Override
    public List<BattBackupPowerOverviewPojo> selectInDecreaseDetailByLogicGroupId(String logicGroupId) throws UedmException {
        if(null==logicGroupId)
        {
            log.error("BackupPowerDomainImpl selectInDecreaseDetailByLogicGroupId logicGroupId is empty");
            throw new UedmException(-100,"params logicGroupId is empty");
        }
        log.debug("BackupPowerDomainImpl selectInDecreaseDetailByLogicGroupId logicGroupId:{}",JSONObject.toJSONString(logicGroupId));
//        if(serviceBean.isPage())
//        {
//            log.info("BackupPowerDomainImpl selectByCondition start page");
//            PageHelper.startPage(serviceBean.getPageNo(),serviceBean.getPageSize());
//        }
        try
        {
            List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = backupPowerMapper.selectInDecreaseDetailByLogicGroupId(logicGroupId);

            if (!StringUtils.equalsIgnoreCase(GlobalOptional.GLOBAL_ROOT, logicGroupId)) {
                battBackupPowerOverviewPojos = getPoJoResultList(battBackupPowerOverviewPojos, logicGroupId);
            }

            battBackupPowerOverviewPojos = filterDeletedSP(battBackupPowerOverviewPojos);

//            PageInfo<BattBackupPowerOverviewPojo> pageInfo = new PageInfo<>(battBackupPowerOverviewPojos);
            return battBackupPowerOverviewPojos;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectInDecreaseDetailByLogicGroupId  select DB is error :{}",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    private List<BattBackupPowerOverviewPojo> getPoJoResultList(List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos, String logicGroupId) {
        List<BattBackupPowerOverviewPojo> resultList = new ArrayList<>();
        return getBattBackupPojos(battBackupPowerOverviewPojos, logicGroupId, resultList);
    }

    private List<BattBackupPowerOverviewPojo> getPoJoResultListByLogicGroupId(List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos, String logicGroupId) {
        List<BattBackupPowerOverviewPojo> resultList = new ArrayList<>();
        return getBattBackupPojos(battBackupPowerOverviewPojos, logicGroupId, resultList);
    }

    private List<BattBackupPowerOverviewPojo> getBattBackupPojos(List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos, String logicGroupId, List<BattBackupPowerOverviewPojo> resultList) {
        for (BattBackupPowerOverviewPojo pojo : battBackupPowerOverviewPojos) {
            String[] split = pojo.getPathIds().split("/");
            List<String> pathIdList = Arrays.asList(split);
            if(pathIdList.contains(logicGroupId)){
                resultList.add(pojo);
            }
        }
        return resultList;
    }


    /**
     * 备电评估结果历史趋势 - 查询
     * @param evalTrendDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public PageInfo<BattBackupPowerEvalTrendPojo> selectTrendByCondition(EvalTrendDto evalTrendDto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        if (null == evalTrendDto || null == serviceBean) {
            log.error("BackupPowerDomainImpl selectTrendByCondition evalTrendDto or serviceBean is empty");
            throw new UedmException(-100, "params evalTrendDto or serviceBean is empty");
        }
        log.info("BackupPowerDomainImpl selectTrendByCondition evalTrendDto:{},serviceBean:{}", evalTrendDto, serviceBean);

        if (serviceBean.isPage()) {
            log.info("BackupPowerDomainImpl selectTrendByCondition start page");
            PageHelper.startPage(serviceBean.getPageNo(),serviceBean.getPageSize());
        }

        try {
            List<BattBackupPowerEvalTrendPojo> battBackupPowerEvalTrendPojo =
                    backupPowerMapper.selectTrendByCondition(evalTrendDto);


            log.info("selectTrendByCondition: battBackupPowerEvalPojos:{}", JSONObject.toJSONString(battBackupPowerEvalTrendPojo));
            PageInfo<BattBackupPowerEvalTrendPojo> pageInfo = new PageInfo<>(battBackupPowerEvalTrendPojo);
            return pageInfo;

        } catch (Exception e) {
            log.error("BackupPowerDomainImpl selectTrendByCondition  select DB is error",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    /**
     * 根据监控对象ids查询备电状态
     * @param moIds
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    @Override
    public PageInfo<BattBackupPowerEvalPojo> selectEvalDByMoIds(List<String> moIds,ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException
    {
        if(null==serviceBaseInfoBean)
        {
            log.error("BackupPowerDomainImpl selectEvalMByMoIds  serviceBean is empty");
            throw new UedmException(-100, "params serviceBean is empty");
        }
        int total = 0;
        try
        {
            List<BattBackupPowerEvalPojo> battBackupPowerEvalPojoss;
            List<BatteryBackupPowerEvalCachePojo> cache = cacheService.getCache(CACHE_NAME_BATT_BACK_POWER_EVAL, BATT_BACK_POWER, List.class);
            if (cache == null || cache.isEmpty()){
                List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = backupPowerMapper.selectEvalDByMoIds(null);
                List<BatteryBackupPowerEvalCachePojo> battBackupPowerEvalPojosss = new ArrayList<>();
                for (BattBackupPowerEvalPojo batteryBackupPowerEvalPojo1 : battBackupPowerEvalPojos) {
                    BatteryBackupPowerEvalCachePojo batteryBackupPowerEvalPojo = new BatteryBackupPowerEvalCachePojo();
                    BeanUtils.copyProperties(batteryBackupPowerEvalPojo1, batteryBackupPowerEvalPojo);
                    battBackupPowerEvalPojosss.add(batteryBackupPowerEvalPojo);
                }
                cacheService.put(CACHE_NAME_BATT_BACK_POWER_EVAL, BATT_BACK_POWER, battBackupPowerEvalPojosss);
                battBackupPowerEvalPojoss = battBackupPowerEvalPojos.parallelStream().filter(item -> moIds.contains(item.getId())).collect(Collectors.toList());
            }else {
                battBackupPowerEvalPojoss = cache.parallelStream()
                        .map(batteryBackupPowerEvalPojo -> {
                            BattBackupPowerEvalPojo battBackupPowerEvalPojo = new BattBackupPowerEvalPojo();
                            BeanUtils.copyProperties(batteryBackupPowerEvalPojo, battBackupPowerEvalPojo);
                            return battBackupPowerEvalPojo;
                        })
                        .filter(battBackupPowerEvalPojo -> moIds.contains(battBackupPowerEvalPojo.getId()))
                        .collect(Collectors.toList());
            }


            // 分页
            List<BattBackupPowerEvalPojo> backupPowerEvalPojos = new ArrayList<>(); //分页后
            PageInfo<BattBackupPowerEvalPojo> pageInfoList = new PageInfo<>();
            total = battBackupPowerEvalPojoss.size();
            if(serviceBaseInfoBean.getPageNo() != null && serviceBaseInfoBean.getPageSize() != null){
                backupPowerEvalPojos = PageUtil.getPageList(battBackupPowerEvalPojoss, serviceBaseInfoBean.getPageNo(), serviceBaseInfoBean.getPageSize());
                pageInfoList.setList(backupPowerEvalPojos);
            }else {
                pageInfoList.setList(battBackupPowerEvalPojoss);
            }
            pageInfoList.setTotal(total);

            return pageInfoList;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectEvalMByMoIds  select DB is error",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }


    @Override
    public PageInfo<BattBackupPowerEvalPojo> selectEvalDetailByCondition(TableDetailSelectDto evalDetailSelectDto, Map<String, SiteBean> siteMap, Map<String, String> siteLevelMap, Map<String, String> powerSupplySceneMap, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException
    {
        if(null==serviceBaseInfoBean)
        {
            log.error("BackupPowerDomainImpl selectEvalDetailByCondition  serviceBean is empty");
            throw new UedmException(-100, "params serviceBean is empty");
        }
        //设备类型模糊查询(字段映射)
        // evalDetailSelectDto = getDeviceTypeByName(evalDetailSelectDto, serviceBaseInfoBean);

        try
        {
            List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = backupPowerMapper.selectEvalDetailByCondition(evalDetailSelectDto);

            battBackupPowerEvalPojos = filterDuplicateDataByIdAndEvalTime(battBackupPowerEvalPojos);

            //根据节点id和权限过滤
            battBackupPowerEvalPojos = getResultList(battBackupPowerEvalPojos, evalDetailSelectDto.getLogicGroupId(),serviceBaseInfoBean.getUserName());
            log.info("BackupPowerDomainImpl selectByCondition battBackupPowerEvalPojos.size:{}",battBackupPowerEvalPojos.size());
            battBackupPowerEvalPojos = filterDeletedSPDetail(battBackupPowerEvalPojos);

            // 根据站点等级和供应场景过滤
            battBackupPowerEvalPojos = filterBySiteLevAndScene(evalDetailSelectDto,battBackupPowerEvalPojos,siteMap);
            battBackupPowerEvalPojos = generateSelectVos(battBackupPowerEvalPojos,siteLevelMap,powerSupplySceneMap);


            PageInfo<BattBackupPowerEvalPojo> res = new PageInfo<>(battBackupPowerEvalPojos);
            res.setTotal(battBackupPowerEvalPojos.size());
            if(serviceBaseInfoBean.isPage())
            {
                List<BattBackupPowerEvalPojo> pageList = PageUtil.getPageList(battBackupPowerEvalPojos,serviceBaseInfoBean.getPageNo(),serviceBaseInfoBean.getPageSize());
                res.setList(pageList);
            }
            return res;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectEvalDetailByCondition  select DB is error:{}",e.getMessage());
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }

    @Override
    public PageInfo<BattBackupPowerEvalPojo> filterDeletedSPDetailAndPage(TableDetailSelectDto evalDetailSelectDto, List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos, Map<String, SiteBean> siteMap, Map<String, String> siteLevelMap, Map<String, String> powerSupplySceneMap,ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        try
        {
            // 根据站点等级和供应场景过滤
            battBackupPowerEvalPojos = filterBySiteLevAndScene(evalDetailSelectDto,battBackupPowerEvalPojos,siteMap);
            battBackupPowerEvalPojos = generateSelectVos(battBackupPowerEvalPojos,siteLevelMap,powerSupplySceneMap);

            PageInfo<BattBackupPowerEvalPojo> res = new PageInfo<>(battBackupPowerEvalPojos);
            res.setTotal(battBackupPowerEvalPojos.size());
            if(serviceBaseInfoBean.isPage())
            {
                List<BattBackupPowerEvalPojo> pageList = PageUtil.getPageList(battBackupPowerEvalPojos,serviceBaseInfoBean.getPageNo(),serviceBaseInfoBean.getPageSize());
                res.setList(pageList);
            }
            return res;
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectEvalDetailByCondition  select DB is error:{}",e.getMessage());
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }
    private List<BattBackupPowerEvalPojo> generateSelectVos(List<BattBackupPowerEvalPojo> list, Map<String, String> siteLevIdNameMap, Map<String, String> powerSupplyIdNameMap) {
        for (BattBackupPowerEvalPojo battBackupPowerEvalPojo : list) {
            String siteLevel = battBackupPowerEvalPojo.getSiteLevel();
            battBackupPowerEvalPojo.setSiteLevel(siteLevIdNameMap.get(siteLevel));
            String powerSupplyScene = battBackupPowerEvalPojo.getPowerSupplyScene();
            String[] split = powerSupplyScene.split("-");
            List<String> collect = Arrays.stream(split).map(powerSupplyIdNameMap::get).collect(Collectors.toList());
            if (StringUtils.isBlank(powerSupplyScene)){
                battBackupPowerEvalPojo.setPowerSupplyScene("--");
            }else {
                battBackupPowerEvalPojo.setPowerSupplyScene(String.join("-", collect));
            }
        }
        return list;
    }

    @NotNull
    private List<BattBackupPowerEvalPojo> filterBySiteLevAndScene(TableDetailSelectDto detailSelectDto,List<BattBackupPowerEvalPojo> list,Map<String, SiteBean> siteMap){
        // 获取前端入参
        List<String> siteLevels = detailSelectDto.getSiteLevel();
        List<String> powerSupplyScenes = detailSelectDto.getPowerSupply();
        String powerSupplySceneString = null;
        if (CollectionUtils.isNotEmpty(powerSupplyScenes)){
            Collections.sort(powerSupplyScenes);
            powerSupplySceneString = String.join("-", powerSupplyScenes);
        }
        log.info("filterBySiteLevAndScene siteLevels={},powerSupplySceneString={}", siteLevels, powerSupplySceneString);

        List<BattBackupPowerEvalPojo> result = new ArrayList<>();
        for (BattBackupPowerEvalPojo pojo : list) {
            String[] split = pojo.getPathIds().split("/");
            for (String pathId : split) {
                if (siteMap.containsKey(pathId)){
                    SiteBean siteBean = siteMap.get(pathId);
                    String siteLevel = siteBean.getSiteLevel();
                    String powerSupplyScene = siteBean.getPowerSupplyScene();
                    if ((CollectionUtils.isNotEmpty(siteLevels) && !siteLevels.contains(siteLevel))
                            // kw 误报
                            || (null != powerSupplySceneString && StringUtils.isNotEmpty(powerSupplySceneString) && !powerSupplySceneString.equals(powerSupplyScene))) {
                        continue;
                    }
                    pojo.setSiteLevel(siteLevel);
                    pojo.setPowerSupplyScene(powerSupplyScene);
                    result.add(pojo);
                }
            }
        }
        return result;
    }

    public  TableDetailSelectDto  getDeviceTypeByName(TableDetailSelectDto evalDetailSelectDto, ServiceBaseInfoBean serviceBaseInfoBean){
        List<String> type = new ArrayList<>();
        if(null != evalDetailSelectDto.getName()){
            if(null!= serviceBaseInfoBean.getLanguageOption()&&serviceBaseInfoBean.getLanguageOption().equals(EN_US)){
                getEnDeviceType(evalDetailSelectDto,  type);
            }
            else{
                getZhDeviceType( evalDetailSelectDto,  type);
            }
            if(0 == type.size()){
                type.add(ABSENT);
            }
        }
        evalDetailSelectDto.setType(type);
        return evalDetailSelectDto;
    }

    public void getEnDeviceType(TableDetailSelectDto evalDetailSelectDto, List<String> type){
        if(Power_Supply_Equipment.toLowerCase().contains(evalDetailSelectDto.getName().toLowerCase())){
            type.add(SP);
        }
        if(BATTERY_PACK.toLowerCase().contains(evalDetailSelectDto.getName().toLowerCase())){
            type.add(BP);
        }
    }
    public void getZhDeviceType(TableDetailSelectDto evalDetailSelectDto, List<String> type){
        if(Power_Supply_Equipment_ZH.contains(evalDetailSelectDto.getName())){
            type.add(SP);
        }
        if(BATTERY_PACK_ZH.contains(evalDetailSelectDto.getName())){
            type.add(BP);
        }
    }


    @Override
    public PageInfo<BattBackupPowerEvalPojo> selectBackupPowerEvalMByCondition(DecreaseStatisticsNewDto dto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException
    {
        if(null==serviceBaseInfoBean || null==dto || StringUtils.isAnyBlank(dto.getEvalTime(),dto.getLogicGroupId()))
        {
            log.error("BackupPowerDomainImpl selectBackupPowerEvalMByCondition params is empty,serviceBaseInfoBean:{},dto:{}",serviceBaseInfoBean,dto);
            throw new UedmException(-100, "params is empty");
        }
        if (serviceBaseInfoBean.isPage())
        {
            log.info("BackupPowerDomainImpl selectBackupPowerEvalMByCondition start page");
            PageHelper.startPage(serviceBaseInfoBean.getPageNo(),serviceBaseInfoBean.getPageSize());
        }
        try
        {
            List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = backupPowerMapper.selectBackupPowerEvalMByCondition(dto);
            return new PageInfo<>(battBackupPowerEvalPojos);
        }
        catch (Exception e)
        {
            log.error("BackupPowerDomainImpl selectBackupPowerEvalMByCondition  select DB is error",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }
}
