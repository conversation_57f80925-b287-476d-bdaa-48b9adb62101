package com.zte.uedm.battery.controller.batttest.bean;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DetailLoopDataBean {

    /**
     * 回路id
     */
    private String id;
    /**
     * 回路名称
     */
    private String name;
    /**
     * 回路类型
     */
    private String type;
    /**
     * 测试结果
     */
    private IdNameBean testResult;
    /**
     * 测试开始时间
     */
    private String testStartTime;
    /**
     * 测试结束时间
     */
    private String testEndTime;
    /**
     * 起始SOC
     */
    private String testInitSoc;
    /**
     * 终止SOC
     */
    private String testFinalSoc;
    /**
     * 变化SOC
     */
    private String testChangedSoc;
    /**
     * 持续时长
     */
    private DurationBean testDuration;
    /**
     * 起始电压
     */
    private String testInitVolt;
    /**
     * 终止电压
     */
    private String testFinalVolt;
    /**
     * 测试进入原因
     */
    private IdNameBean testStartCause;
    /**
     * 测试停止原因
     */
    private IdNameBean testStopCause;
    /**
     * 平均放电电流
     */
    private String testAvgCurr;
    /**
     * 电池温度
     */
    private String testTemp;
    /**
     * 起始放电电量
     */
    private String testInitPower;
    /**
     * 终止放电电量
     */
    private String testFinalPower;
    /**
     * 记录开始电池状态
     */
    private IdNameBean testInitStatus;
    /**
     * 记录结束电池状态
     */
    private IdNameBean testFinalStatus;
    /**
     * 测试类型
     */
    private IdNameBean testType;

    /**
     * 电池类型为铁锂的电池数量
     */
    private int battNum;

}
