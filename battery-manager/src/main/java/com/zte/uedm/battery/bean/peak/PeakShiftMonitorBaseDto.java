package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.battery.enums.peak.PeakShiftMonitorDimEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PeakShiftMonitorBaseDto {
    @ApiModelProperty("逻辑位置id")
    private String logicGroupId;

    @ApiModelProperty("执行状态（正常，异常），设备侧提供测点")
    private String execStatus;

    @ApiModelProperty("运行状态（启动，停止），原始测点")
    private List<String> runningStatus;

    @ApiModelProperty("是否支持错峰，默认true")
    private Boolean enablePeak;

    @ApiModelProperty("站点名称")
    private String siteName;

    @ApiModelProperty("执行策略（尖峰平谷）")
    private List<String> currStrategy;

    @ApiModelProperty("策略生效日期开始")
    private String effectiveDateBegin;

    @ApiModelProperty("策略生效日期结束")
    private String effectiveDateEnd;

    @ApiModelProperty("策略失效日期开始")
    private String expirationDateBegin;

    @ApiModelProperty("策略失效日期结束")
    private String expirationDateEnd;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("自定义列表")
    private List<PeakShiftMonitorDimBean> peakShiftMonitorDims;

    /* Started by AICoder, pid:s9691e31a5nd922146630bc810b8c29902229ea2 */
    public Pair<Boolean, List<String>> checkDimAvailable() {
        List<String> errorList = new ArrayList<>();
        boolean checkResult = checkArgs(errorList, true);
        if (!checkResult) {
            return Pair.of(false, errorList);
        }

        // 根据概览维度列表，获取 id 集合
        List<PeakShiftMonitorDimEnums> dimsDefault = Arrays.asList(PeakShiftMonitorDimEnums.values());
        List<String> queryIdList = dimsDefault.stream()
                .map(PeakShiftMonitorDimEnums::getId)
                .collect(Collectors.toList());

        // 根据概览维度列表，获取用户序列集合
        List<Integer> querySeqList = dimsDefault.stream()
                .map(PeakShiftMonitorDimEnums::getDefaultIndex)
                .collect(Collectors.toList());

        checkResult = checkIdAndSeq(queryIdList, querySeqList, errorList, true);
        if (!checkResult) {
            return Pair.of(false, errorList);
        }

        Map<String, PeakShiftMonitorDimBean> collect = peakShiftMonitorDims.stream()
                .collect(Collectors.toMap(PeakShiftMonitorDimBean::getId, item -> item));

        // 遍历概览维度列表，校验不可变动的设定
        checkResult = checkEnable(dimsDefault, errorList, collect, true);
        return Pair.of(checkResult, errorList);
    }

    public boolean checkArgs(List<String> errorList, boolean checkResult) {
        // 非空判断
        if (CollectionUtils.isEmpty(peakShiftMonitorDims)) {
            errorList.add("params is blank");
            return false;
        }

        // 获取入参的 sequence 列表，做去重并进行合理性校验，不一致则报错
        if (checkReqSeq()) {
            checkResult = false;
            errorList.add("params is invalid");
        }
        return checkResult;
    }

    private boolean checkReqSeq() {
        List<Integer> reqSeqList = peakShiftMonitorDims.stream()
                .map(PeakShiftMonitorDimBean::getSequence)
                .distinct()
                .collect(Collectors.toList());
        return reqSeqList.size() != peakShiftMonitorDims.size();
    }

    private boolean checkIdAndSeq(List<String> queryIdList, List<Integer> querySeqList, List<String> errorList, boolean checkResult) {
        List<String> updateIds = peakShiftMonitorDims.stream()
                .map(PeakShiftMonitorDimBean::getId)
                .collect(Collectors.toList());
        List<Integer> updateSeqs = peakShiftMonitorDims.stream()
                .map(PeakShiftMonitorDimBean::getSequence)
                .collect(Collectors.toList());

        updateIds.removeAll(queryIdList);
        if (CollectionUtils.isNotEmpty(updateIds)) {
            checkResult = false;
            errorList.add("The parameter id is not within the optional range");
        }

        updateSeqs.removeAll(querySeqList);
        if (CollectionUtils.isNotEmpty(updateSeqs)) {
            checkResult = false;
            errorList.add("The parameter sequence is not within the optional range");
        }
        return checkResult;
    }

    private boolean checkEnable(List<PeakShiftMonitorDimEnums> dimsDefault, List<String> errorList, Map<String, PeakShiftMonitorDimBean> collect, boolean checkResult) {
        for (PeakShiftMonitorDimEnums enums : dimsDefault) {
            String id = enums.getId();
            Boolean defaultFixed = enums.getDefaultFixed();
            Boolean enable = enums.getDefaultEnable();
            if (defaultFixed != null && defaultFixed && enable != null) {
                PeakShiftMonitorDimBean dim = collect.get(id);
                if (dim != null && !Objects.equals(enable, dim.getEnable())) {
                    errorList.add(dim.getId() + " Default immutable setting verification failed");
                    checkResult = false;
                    break;
                }
            }
        }
        return checkResult;
    }
    /* Ended by AICoder, pid:s9691e31a5nd922146630bc810b8c29902229ea2 */
}
