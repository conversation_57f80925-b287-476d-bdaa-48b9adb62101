package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.bean.PeakShiftCsu5StrategyDetailBean;
import com.zte.uedm.battery.bean.PeakShiftCsuAllStrategyBean;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class PeakShiftMonitorBaseDataBean {

    // 电池组状态标准测点值（除SNMP外需要）
    private Map<String, Map<String, Object>> battPackStatusMap;

    // 设备与电池组的关联关系
    private Map<String,List<ResourceCollectorRelationEntity>> battpackMap;

    // 电池组状态原始测点值（SNMP需要）
    private Map<String, Map<String, Map<String, AdapterPointDataVo>>> adapterPointMap;

    // 电池最近一次下发的策略（电池直连类型）
    private Map<String, PeakShiftDeviceStrategyDetailBean> directStrategyDetailMap;

    // 电池最近一次下发的策略（电池CSU类型）
    private Map<String, PeakShiftCsuAllStrategyBean> csuStrategyDetailMap;

    public void defaultValueIfNull() {
        if (battPackStatusMap == null) {
            battPackStatusMap = new HashMap<>();
        }
        if (battpackMap == null) {
            battpackMap = new HashMap<>();
        }
        if (adapterPointMap == null) {
            adapterPointMap = new HashMap<>();
        }
        if (directStrategyDetailMap == null) {
            directStrategyDetailMap = new HashMap<>();
        }
        if (csuStrategyDetailMap == null) {
            csuStrategyDetailMap = new HashMap<>();
        }
    }

}
