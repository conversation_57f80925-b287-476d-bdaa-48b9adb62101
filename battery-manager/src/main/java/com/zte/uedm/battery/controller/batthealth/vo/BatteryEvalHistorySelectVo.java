package com.zte.uedm.battery.controller.batthealth.vo;

import com.zte.uedm.common.bean.log.OperlogDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2023年03月09日 上午11:45
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
public class BatteryEvalHistorySelectVo
{
    @ApiModelProperty(value = "评估时间")
    @OperlogDetail(zhName = "评估时间", enName = "Evaluate Time")
    private String evaluateTime;
    @ApiModelProperty(value = "状态id")
    @OperlogDetail(zhName = "状态id", enName = "Status Id")
    private String statusId;
    @ApiModelProperty(value = "状态名")
    @OperlogDetail(zhName = "状态名", enName = "Status Name")
    private String statusName;
    @ApiModelProperty(value = "健康度")
    @OperlogDetail(zhName = "健康度", enName = "SOH")
    private String soh;
    @ApiModelProperty(value = "健康度来源")
    @OperlogDetail(zhName = "健康度来源", enName = "sohSource")
    private String source;
}
