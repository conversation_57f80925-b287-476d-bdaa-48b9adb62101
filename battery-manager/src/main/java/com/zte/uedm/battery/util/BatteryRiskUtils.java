package com.zte.uedm.battery.util;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zte.uedm.battery.bean.BattMaintenancePeriodBean;
import com.zte.uedm.battery.bean.BattRiskEvalBean;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.enums.batteryrisk.BatteryRiskLevelEnums;
import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleRepository;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BatteryRiskUtils {
    @Autowired
    private BattRiskRuleRepository battRiskRuleRepository;
    @Autowired
    private AssetRpcImpl assetRpcimpl;

    /**
     * 过滤隐藏
     * @param riskEvalBeans
     * @throws UedmException
     */
    public  void   dealBlank(List<BattRiskEvalBean> riskEvalBeans) throws UedmException {
        log.info("BatteryRiskUtils -->before dealBlank riskEvalBeans.size:{}",riskEvalBeans.size());
        log.debug("BatteryRiskUtils -->before riskEvalBeans :{}", JSON.toJSONString(riskEvalBeans));
        //风险规则
        List<BattRiskRuleEntity> battRiskRuleEntities = battRiskRuleRepository.selectAllRiskRule();
        log.info("BatteryRiskUtils -->dealBlank battRiskRuleEntities.size:{}",battRiskRuleEntities.size());
        //需要隐藏的风险id
         List<String> riskIds = Optional.ofNullable(battRiskRuleEntities.stream().filter(s-> !s.isDisplay()).map(BattRiskRuleEntity::getId).collect(Collectors.toList())).orElse(new ArrayList<>());
        log.info("BatteryRiskUtils -->dealBlank riskIds.size:{}",riskIds.size());
        log.debug("BatteryRiskUtils -->dealBlank riskIds:{}", riskIds);
        // 有隐藏风险，需要判断隐藏的电池id
        List<String> collectBattid =Optional.ofNullable(riskEvalBeans.stream().filter(bean -> riskIds.contains(bean.getRiskId())).map(BattRiskEvalBean::getBattId).collect(Collectors.toList())).orElse(new ArrayList<>());
        log.info("BatteryRiskUtils -->dealBlank collectBattid.size:{}",collectBattid.size());
        log.debug("BatteryRiskUtils -->dealBlank collectBattid:{}", collectBattid);
        List<String> blankBattIds =new ArrayList<>();
        /* Started by AICoder, pid:ed5a63bfc7a64434b12c0cc115aba461 */
        Optional.ofNullable(collectBattid)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(battIds -> {
                    List<BattMaintenancePeriodBean> battBeans = new ArrayList<>();
                    //预防 资产返回空
                    blankBattIds.addAll(battIds);
                    try {
                        battBeans =Optional.ofNullable(assetRpcimpl.getMaintenancePeriodById(battIds)).orElse(battBeans) ;
                    } catch (UedmException e) {
                        throw new RuntimeException(e);
                    }
                    log.info("BatteryRiskUtils -->dealBlank battBeans.size:{}", battBeans.size());
                    log.debug("BatteryRiskUtils -->dealBlank battBeans:{}", JSON.toJSONString(battBeans));
                    String cruDateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_1));
                    if (!CollectionUtils.isEmpty(battBeans)) {
                        //存在维保数据且需要隐藏的电池id
                         List<String> collect= battBeans.stream()
                                .filter(s -> StringUtils.isBlank(s.getMaintenancePeriod()) || dateComparison(s.getMaintenancePeriod(), cruDateStr))
                                .map(BattMaintenancePeriodBean::getId).distinct()
                                .collect(Collectors.toList());
                         //维保期外
                         List<String> collect1 = battBeans.stream().map(BattMaintenancePeriodBean::getId).distinct().collect(Collectors.toList());
                         collect1.removeAll(collect);
                         // 差集 -->需要隐藏的id
                        blankBattIds.removeAll(collect1);
                    }
                });
        /* Ended by AICoder, pid:ed5a63bfc7a64434b12c0cc115aba461 */
        Optional.ofNullable(blankBattIds)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(battIds -> {
                    riskEvalBeans.stream()
                            .filter(bean -> battIds.contains(bean.getBattId()) && riskIds.contains(bean.getRiskId()))
                            .forEach(bean -> bean.setRiskLevel(BatteryRiskLevelEnums.LEVEL_4.getId()));
                });

        log.info("BatteryRiskUtils -->after dealBlank riskEvalBeans.size:{}",riskEvalBeans.size());
        log.debug("BatteryRiskUtils -->after dealBlank riskEvalBeans:{}",JSON.toJSONString(riskEvalBeans));

    }






    /**
     * 校验维保期
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    /* Started by AICoder, pid:a0c5fe5a2296434fb505fd0c13b7f488 */
    public boolean dateComparison(String startDateStr, String endDateStr){
        boolean result=false;
        String start = Optional.ofNullable(startDateStr).orElse("");
        if (!"".equals(start)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_1);
            String[] split = start.split("\\+");
            LocalDateTime startDate = LocalDateTime.parse(split[0], formatter);
            LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);
            if (null != startDate && endDate != null) {
                result = startDate.isAfter(endDate);
            }
        }
        return  result;
    }
    /* Ended by AICoder, pid:a0c5fe5a2296434fb505fd0c13b7f488 */


}
