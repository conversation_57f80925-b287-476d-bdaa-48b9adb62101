package com.zte.uedm.battery.opti.domain.service.bean.enums;

/**
 * 频次类型
 */
public enum CalculationFrequencyTypeEnum
{
    /**
     * 连续
     */
    CONTINUOUS("continuous"),

    /**
     * 不连续
     */
    DISCONTINUOUS("discontinuous"),

    /**
     * 时间连续
     */
    TIME_CONTINUOUS("time_continuous"),

    /**
     * 频次连续
     */
    FREQUENCY_CONTINUOUS("frequency_continuous"),
    /**
     * 满足时间连续的次数
     */
    TIME_FREQUENCY_CONTINUOUS("time_frequency_continuous");
    private String id;

    public String getId()
    {
        return id;
    }

    CalculationFrequencyTypeEnum(String id)
    {
        this.id = id;
    }

    public static boolean checkFrequencyType(String type)
    {
        return DISCONTINUOUS.getId().equals(type) || TIME_CONTINUOUS.getId().equals(type);
    }

    public static boolean checkContinuous(String type)
    {
        return CONTINUOUS.getId().equals(type);
    }

    public static boolean checkDiscontinuous(String type)
    {
        return DISCONTINUOUS.getId().equals(type);
    }

    public static boolean checkTimeContinuous(String type)
    {
        return TIME_CONTINUOUS.getId().equals(type);
    }

    public static boolean checkFrequencyContinuous(String type)
    {
        return FREQUENCY_CONTINUOUS.getId().equals(type);
    }

    public static boolean checkTimeFrequencyContinuous(String type){return TIME_FREQUENCY_CONTINUOUS.getId().equals(type);}

}
