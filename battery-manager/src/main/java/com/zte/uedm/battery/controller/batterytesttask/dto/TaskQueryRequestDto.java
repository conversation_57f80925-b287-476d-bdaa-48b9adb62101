package com.zte.uedm.battery.controller.batterytesttask.dto;

import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * @ Author     ：10260977
 * @ Date       ：9:27 2022/8/15
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
@Setter
@Getter
@ToString
@ApiModel(description = "任务查询")
public class TaskQueryRequestDto
{
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private List<String> status;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String name;
    /**
     * 设备id列表
     */
    @ApiModelProperty(value = "设备标识")
    private List<String> deviceIds;
    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String order;
    /**
     * 排序顺序
     */
    @ApiModelProperty(value = "排序顺序")
    private String sort;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;

    public void checkNameLimit() throws UedmException
    {
        if(StringUtils.isNotBlank(this.name)&& this.name.length()>50)
        {
            throw new UedmException(-3041, "Parameter exceeds length limit.", "name");
        }
        return;
    }

    /**
     * 校验order是否在允许范围内(为空的话设置默认值)
     * @return
     */
    public Pair<Boolean, List<String>> checkOrderAndSetDefault()
    {
        List<String> orderIds = Arrays.asList("name", "siteNumber", "switchPowerNumber", "period", "status", "startTime", "creator", "gmtCreate", "updater", "gmtModified");
        if (StringUtils.isBlank(this.order))
        {
            if (SortEnum.isDesc(this.sort))
            {
                this.order = CommonConst.START_TIME;
            }
            else
            {
                this.order = "name";
            }
        }
        if(orderIds.contains(this.order))
        {
            return Pair.of(true, new ArrayList<>());
        }
        return Pair.of(false, orderIds);
    }
    /**
     * 校验sort是否在允许范围内(为空的话设置默认值)
     * @return
     */
    public Pair<Boolean, List<String>> checkSortAndSetDefault()
    {
        List<String> sortIds = Arrays.asList("asc", "desc");
        if (StringUtils.isBlank(this.sort))
        {
            this.sort= SortEnum.getAscSortID();
        }
        if(sortIds.contains(this.sort))
        {
            return Pair.of(true, new ArrayList<>());
        }
        return Pair.of(false, sortIds);
    }
    /**
     * 排序字段转换
     * @return
     */
    public void transOrderFieldVoToPo()
    {
        Map<String, String> map = new HashMap<>();
        map.put("startTime", "start_time");
        map.put("gmtCreate", "gmt_create");
        map.put("gmtModified", "gmt_modified");
        String orderPo = map.get(this.order);
        if(null != orderPo)
        {
            this.order = orderPo;
        }
    }


}
