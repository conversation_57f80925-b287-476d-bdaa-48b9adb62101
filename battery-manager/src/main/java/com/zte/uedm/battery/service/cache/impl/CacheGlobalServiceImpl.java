package com.zte.uedm.battery.service.cache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.service.cache.CacheGlobalService;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.caffeine.service.impl.CacheBaseManager;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class CacheGlobalServiceImpl implements CacheGlobalService {

    @Resource
    private CommonCacheService cacheService;

    @Setter
    @Autowired()
    private Map<String, CacheBaseManager> cacheBaseManagerMap;

    @Override
    public Object getAllCache(String cacheName) {
        Cache<Object, Object> cache = (Cache<Object, Object>) cacheService.getAllCache(cacheName);
        Map<String, Object> result = new HashMap<>();
        for (Object key : cache.asMap().keySet()) {
            Object value = cache.getIfPresent(key);
            result.put(key.toString(), value);
        }
        return result;
    }

    @Override
    public Object getAllCacheByKey(String cacheName, String key) {
        Object cache = cacheService.getCache(cacheName, key, Object.class);
        return cache;
    }

    @Override
    public List<BaseCacheBean> getBatteryCache(String cacheName) throws UedmException {
        CacheBaseManager cacheBaseManager = cacheBaseManagerMap.get(cacheName);
        return cacheBaseManager == null ? new ArrayList<>() : cacheBaseManager.queryAll();
    }

    @Override
    public BaseCacheBean getBatteryCacheByKey(String cacheName, String key) throws UedmException {
        CacheBaseManager cacheBaseManager = cacheBaseManagerMap.get(cacheName);
        return cacheBaseManager == null ? new BaseCacheBean() : (BaseCacheBean) cacheBaseManager.queryByKey(key);
    }
}
