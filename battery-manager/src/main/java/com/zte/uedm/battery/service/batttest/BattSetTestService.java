package com.zte.uedm.battery.service.batttest;

import com.zte.uedm.battery.a_infrastructure.kafka.bean.Item;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.SouthApplySetStatusDataBean;
import com.zte.uedm.battery.bean.BatteryTemporaryTestVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

public interface BattSetTestService {
    /**
     * 监听下发终止soc结果 成功则进行下发电池测试
     * @param southApplySetStatusDataBean 南向消息体
     */
    public void handleRealtimeData(SouthApplySetStatusDataBean southApplySetStatusDataBean);
    /**
     * 开启BCUA电池组临时性测试
     * @param id
     * @return
     * @throws UedmException
     */
    void startBatterySetTemporaryTest(String id, String userName, String testType, String taskId);
    /**
     * BCUA电池组电池测试命令下发
     * @param id
     * @return
     * @throws UedmException
     */
    public Map<Integer, String> startBatterySetTest(String id) throws UedmException;
}
