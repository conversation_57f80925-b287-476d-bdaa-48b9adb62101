package com.zte.uedm.battery.bean.alarm;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AlarmAckTime
{
    /**
     * 0表示开始结束时间模式，1表示relative模式
     */
    private Integer mode;
    /**
     * 当mode为1，此字段必须填写
     */
    private Integer relative;
    /**
     * 开始确认时间
     */
    private Long start;
    /**
     * 结束确认时间
     */
    private Long end;
}
