package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.BattHealthThreshold;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 电池配置
 */
@Service
@Setter
@Getter
@RefreshScope
public class SystemConfigService
{
    /**
     * 电池健康阈值配置
     */
    @Value("${battery.health.status.healthy.name_zh}")
    private String healthNameZh;
    @Value("${battery.health.status.healthy.name_en}")
    private String healthNameEn;
    @Value("${battery.health.status.healthy.threshold.max}")
    private Double healthMax;
    @Value("${battery.health.status.healthy.threshold.containMax}")
    private boolean healthContainMax;
    @Value("${battery.health.status.healthy.threshold.min}")
    private Double healthMin;
    @Value("${battery.health.status.healthy.threshold.containMin}")
    private boolean healthContainMin;
    @Value("${battery.health.status.subhealth.name_zh}")
    private String subHealthNameZh;
    @Value("${battery.health.status.subhealth.name_en}")
    private String subHealthNameEn;
    @Value("${battery.health.status.subhealth.threshold.max}")
    private Double subHealthMax;
    @Value("${battery.health.status.subhealth.threshold.containMax}")
    private boolean subHealthContainMax;
    @Value("${battery.health.status.subhealth.threshold.min}")
    private Double subHealthMin;
    @Value("${battery.health.status.subhealth.threshold.containMin}")
    private boolean subHealthContainMin;
    @Value("${battery.health.status.exception.name_zh}")
    private String exceptionNameZh;
    @Value("${battery.health.status.exception.name_en}")
    private String exceptionNameEn;
    @Value("${battery.health.status.exception.threshold.max}")
    private Double exceptionMax;
    @Value("${battery.health.status.exception.threshold.containMax}")
    private boolean exceptionContainMax;
    @Value("${battery.health.status.exception.threshold.min}")
    private Double exceptionMin;
    @Value("${battery.health.status.exception.threshold.containMin}")
    private boolean exceptionContainMin;
    @Value("${battery.health.status.unEvaluate.name_zh}")
    private String unEvaluateNameZh;
    @Value("${battery.health.status.unEvaluate.name_en}")
    private String unEvaluateNameEn;
    @Value("${battery.health.status.unEvaluate.threshold}")
    private Double unEvaluateThreshold;

    @Value("${uedm.schedule.deviceState_timeout}")
    private Integer timeOut;

    @Value("${battery.backupPowerAlarm.llvdTimeRange}")
    private Integer llvdTimeRange;

    @Value("${battery.backupPowerAlarm.acPowerOffTimeRange}")
    private Integer acPowerOffTimeRange;

    @Value("${uedm.scheduler.poolsize}")
    private Integer schedulerPoolSize;

    public BattHealthThreshold getAllValue()
    {
        BattHealthThreshold battHealthThreshold = new BattHealthThreshold();
        battHealthThreshold.setHealthNameZh(this.healthNameZh);
        battHealthThreshold.setHealthNameEn(this.healthNameEn);
        battHealthThreshold.setHealthMin(this.healthMin);
        battHealthThreshold.setHealthMax(this.healthMax);
        battHealthThreshold.setHealthContainMin(this.healthContainMin);
        battHealthThreshold.setHealthContainMax(this.healthContainMax);
        battHealthThreshold.setSubHealthNameZh(this.subHealthNameZh);
        battHealthThreshold.setSubHealthNameEn(this.subHealthNameEn);
        battHealthThreshold.setSubHealthMin(this.subHealthMin);
        battHealthThreshold.setSubHealthMax(this.subHealthMax);
        battHealthThreshold.setSubHealthContainMin(this.subHealthContainMin);
        battHealthThreshold.setSubHealthContainMax(this.subHealthContainMax);
        battHealthThreshold.setExceptionNameZh(this.exceptionNameZh);
        battHealthThreshold.setExceptionNameEn(this.exceptionNameEn);
        battHealthThreshold.setExceptionMin(this.exceptionMin);
        battHealthThreshold.setExceptionMax(this.exceptionMax);
        battHealthThreshold.setExceptionContainMin(this.exceptionContainMin);
        battHealthThreshold.setExceptionContainMax(this.exceptionContainMax);
        return battHealthThreshold;
    }
}
