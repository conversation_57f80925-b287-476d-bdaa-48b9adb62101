package com.zte.uedm.battery.controller.schedule;

import com.zte.uedm.battery.schedule.*;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

@Path("schedule")
@Component
@Api(tags = {"battery/定时任务管理"})
@Slf4j
public class ScheduleInitController {

    @Autowired
    private BatteryCurrentSchduleJob batteryCurrentSchduleJob;

    @Autowired
    private BattHealthStatusEvalSchduleJob battHealthStatusEvalSchduleJob;

    @Autowired
    private BatteryRiskEvalScheduleJob batteryRiskEvalScheduleJob;

    @Autowired
    private BattTestResultScheduleJob battTestResultScheduleJob;

    @Autowired
    private BatteryBackupPowerEvalScheduleJob batteryBackupPowerEvalScheduleJob;

    @Autowired
    private BattLifeEvalScheduleJob battLifeEvalScheduleJob;

    @Autowired
    private BatteryBaseInfoScheduleJob batteryBaseInfoScheduleJob;

    @Autowired
    private PeakShiftR321GainSchdeuleJob peakShiftR321GainSchdeuleJob;

    @Resource
    @Qualifier("taskScheduler")
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    @GET
    @Path("/init-job")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "重新触发定时任务", notes = "重新触发定时任务", httpMethod = "GET")
    public ResponseBean<String> init(@QueryParam("jobName") String jobName) {

        log.info("jobName:{}", jobName);
        switch (jobName) {
            case "health":
                threadPoolTaskScheduler.execute(() -> battHealthStatusEvalSchduleJob.execute());
                break;
            case "risk":
                threadPoolTaskScheduler.execute(() -> batteryRiskEvalScheduleJob.execute());
                break;
            case "life":
                threadPoolTaskScheduler.execute(() -> battLifeEvalScheduleJob.execute());
                break;
            case "current":
                threadPoolTaskScheduler.execute(() -> batteryCurrentSchduleJob.execute());
                break;
            case "testResult":
                threadPoolTaskScheduler.execute(() -> battTestResultScheduleJob.execute());
                break;
            case "backupPower":
                threadPoolTaskScheduler.execute(() -> batteryBackupPowerEvalScheduleJob.execute());
                break;
            case "baseInfoClear":
                threadPoolTaskScheduler.execute(() -> batteryBaseInfoScheduleJob.executeClear());
                break;
            case "peakShiftR321Gain":
                threadPoolTaskScheduler.execute(() -> peakShiftR321GainSchdeuleJob.execute());
                break;
            default:
                break;
        }

        return ResponseBeanUtils.getNormalResponseBean(0, "success", 0);
    }

}
