package com.zte.uedm.battery.controller.battoverview.dto;

import com.zte.uedm.battery.bean.BatteryOverviewExportBean;
import com.zte.uedm.battery.enums.overview.BattStatisticsDimEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 电池总览统计维度筛选条件
 */
@Setter
@Getter
@ToString
public class BatteryOverviewFilterStatisticsRequestDto extends BatteryOverviewFilterRequestDto
{
    public Pair<Boolean, List<String>> checkDimRange()
    {
        Boolean check = true;
        List<String> errMs = new ArrayList<>();

        if(CollectionUtils.isEmpty(super.getDims()))
        {
            return Pair.of(check, errMs);
        }

        Set<String> dimsList = BattStatisticsDimEnum.getAllBattStatisticsDimKeySets();
        for(String dim : super.getDims())
        {
            if(!dimsList.contains(dim))
            {
                check = false;
                continue;
            }
        }

        return Pair.of(check, new ArrayList<>(dimsList));
    }
    
    public BatteryOverviewFilterStatisticsRequestDto(){}
    
    public BatteryOverviewFilterStatisticsRequestDto(BatteryOverviewExportBean bean){
        {
            super.setLogicId(bean.getLogicId());
            super.setTypes(bean.getTypes());
            super.setSoh(bean.getSoh());
            super.setPrstSocLevels(bean.getPrstSocLevels());
            super.setAlarmLevels(bean.getAlarmLevels());
//            super.setLifeRangeLower(bean.getLifeRangeLower());
//            super.setLifeRangeUpper(bean.getLifeRangeUpper());
            super.setLifeLevels(bean.getLifeLevels());
            super.setChargeStatus(bean.getChargeStatus());
            super.setManufacturers(bean.getManufacturers());
            super.setBrands(bean.getBrands());
            super.setSeries(bean.getSeries());
            super.setModels(bean.getModels());
            super.setRatedCapLevels(bean.getRatedCapLevels());
            super.setStartDateBegin(bean.getStartDateBegin());
            super.setStartDateEnd(bean.getStartDateEnd());
        }
    }
}
