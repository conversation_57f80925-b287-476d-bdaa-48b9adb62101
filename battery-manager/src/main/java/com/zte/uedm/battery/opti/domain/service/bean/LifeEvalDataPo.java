package com.zte.uedm.battery.opti.domain.service.bean;

import com.zte.uedm.battery.controller.BattLifeEvalStatistics.bean.LifeBean;
import com.zte.uedm.battery.opti.domain.service.util.AssetAttributeIdMapping;
import lombok.Data;
import lombok.Getter;

@Data
public class LifeEvalDataPo {
    /**
     * 电池id
     */
    @AssetAttributeIdMapping(attributeId = "id")
    private String id;

    /**
     * 评估时间
     */
    @AssetAttributeIdMapping(attributeId = "eval_time")
    private String evalTime;
    /**
     * 电芯类型
     */
    @AssetAttributeIdMapping(attributeId = "batt_type")
    private String battType;
    /**
     * 电池名称
     */
    @AssetAttributeIdMapping(attributeId = "name")
    private String name;
    /**
     * 路径id
     */
    @AssetAttributeIdMapping(attributeId = "path_ids")
    private String pathIds;

    /**
     * 路径名称串
     */
    @AssetAttributeIdMapping(attributeId = "path_names")
    private String pathNames;
    /**
     * 位置
     */
    @AssetAttributeIdMapping(attributeId = "position")
    private String position;
    /**
     * 寿命等级，寿命名称，寿命单位
     */
    @AssetAttributeIdMapping(attributeId = "life")
    private Integer life;
    /**
     * 额定循环次数
     */
    @AssetAttributeIdMapping(attributeId = "rated_cycle_times")
    private Integer ratedCycleTimes;
    /**
     * 累计循环次数
     */
    @AssetAttributeIdMapping(attributeId = "accum_cycle_times")
    private Integer accumCycleTimes;
    /**
     * 累计放电容量
     */
    @AssetAttributeIdMapping(attributeId = "accum_discharge_cap")
    private Float accumDischargeCap;
    /**
     * 额定放电容量
     */
    @AssetAttributeIdMapping(attributeId = "rated_cap")
    private Float ratedCap;
    /**
     * 平均放电深度
     */
    @AssetAttributeIdMapping(attributeId = "avg_discharge_depth")
    private Float avgDischargeDepth;
    /**
     * 累计放电次数
     */
    @AssetAttributeIdMapping(attributeId = "accum_discharge_times")
    private Integer accumDischargeTimes;
    /**
     * 循环次数
     */
    @AssetAttributeIdMapping(attributeId = "cycle_times")
    private Integer cycleTimes;
    /**
     * 运行总天数
     */
    @AssetAttributeIdMapping(attributeId = "operating_days")
    private Integer operatingDays;
    /**
     * 运行总天数来源
     */
    @AssetAttributeIdMapping(attributeId = "operating_days_from")
    private String operatingDaysFrom;
    /**
     * 更新时间
     */
    @AssetAttributeIdMapping(attributeId = "gmt_modified")
    private String gmtModified;
    /**
     * 开启日期
     */
    @AssetAttributeIdMapping(attributeId = "start_date")
    private String startDate;
    /**
     * 生产日期
     */
    @AssetAttributeIdMapping(attributeId = "production_Date")
    private String productionDate;
}
