package com.zte.uedm.battery.bean.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Data
public class PriceIntervalStrategyPO {
    /**
     * 单价周期策略id
     */
    private String id;

    /**
     * 单价策略id
     */
    private String priceStrategyId;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 累计周期
     */
    private Integer mode;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
