package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalCachePo;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.controller.batthealth.dto.SelectEvalDetailDto;
import com.zte.uedm.battery.domain.BattHealthStatusEvalDomain;
import com.zte.uedm.battery.mapper.BattHealthStatusEvalMapper;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.consts.CommonConst.CACHE_NAME_BATT_HEALTH_STATUS_EVAL;

/**
 * @ Author     ：10260977
 * @ Date       ：19:29 2022/6/24
 * @ Description：实现类
 * @ Modified By：
 * @ Version: 1.0
 */
@Service
@Slf4j
public class BattHealthStatusEvalDomainImpl implements BattHealthStatusEvalDomain
{
    @Autowired
    private BattHealthStatusEvalMapper battHealthStatusEvalMapper;

    @Autowired
    private CommonCacheService cacheService;

    private static final Integer TIME_GRAIN = 5;

    @Value("${overview.thread.data:5}")
    private Integer threadNum;

    /**
     * 通过对象ids查询最新状态
     * @param moIds
     * @return
     * @throws UedmException
     */
    @Override
    public List<BattHealthStatusEvalPo> selectPreHealthStatusByMoIds(List<String> moIds) throws UedmException
    {
        return battHealthStatusEvalMapper.selectPreHealthStatusByMoIds(moIds);
    }

    @Override
    public Integer addHealthStatusEvalResult(List<BattHealthStatusEvalPo> battHealthStatusEvalPos)
            throws UedmException
    {
        try
        {
            log.info("54---start insert DB--battHealthStatusEvalPos.size:{}",battHealthStatusEvalPos.size());
            battHealthStatusEvalPos.parallelStream()
                    .forEach(battHealthStatusEvalPo -> {
                        BattHealthStatusEvalCachePo  batteryBackupPowerEvalCache = new BattHealthStatusEvalCachePo(battHealthStatusEvalPo.getId(),battHealthStatusEvalPo.getStatusId());
                        cacheService.put(CACHE_NAME_BATT_HEALTH_STATUS_EVAL, battHealthStatusEvalPo.getId(), batteryBackupPowerEvalCache);
                    });
            Integer[] batchNum = { 0 };
            BatchUtils.doInBatch(500, battHealthStatusEvalPos, (beans -> {
                try
                {
                    Integer n = battHealthStatusEvalMapper.insertBeans(beans);
                    batchNum[0] += n;
                }
                catch (Exception e)
                {
                    log.error("BattHealthStatusEvalDomainImpl addHealthStatusEvalResult error", e);
                    throw new RuntimeException();
                }
            }));
            return batchNum[0];
        }
        catch (Exception e)
        {
            log.error("BattHealthStatusEvalDomainImpl addHealthStatusEvalResult error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed(e.getMessage());
        }
    }

    @Override
    public List<BattHealthStatusEvalPo> selectHealthEvalByDate(String batteryId, String evaluateTimeStart, String evaluateTimeEnd) throws UedmException {
        return battHealthStatusEvalMapper.selectHealthEvalByDate(batteryId, evaluateTimeStart, evaluateTimeEnd);
    }

    @Override
    public List<BattHealthStatusEvalPo> selectHealthEvalStatistics(String logicGroupId, String evaluateTimeStart, String evaluateTimeEnd) throws UedmException {
        String groupId = GlobalOptional.GLOBAL_ROOT.equals(logicGroupId)?"":logicGroupId;
        List<BattHealthStatusEvalPo> result = Collections.synchronizedList(new ArrayList<>());
        ExecutorService executor = new ForkJoinPool(threadNum);
        long time1 = System.currentTimeMillis();
        List<Pair<String, String>> segmentTime = DateUtils.getSegmentTime(evaluateTimeStart, evaluateTimeEnd, TIME_GRAIN);
        List<Callable<Object>> tasks = new ArrayList<>();
        segmentTime.forEach(pair -> {
            Callable<Object> task = () -> result.addAll(battHealthStatusEvalMapper.selectHealthEvalStatistics(groupId, pair.getLeft(), pair.getRight()));
            tasks.add(task);
        });
        long time2 = System.currentTimeMillis();
        log.info("time2-time1 :{}", time2-time1);
        try {
            // 执行所有的任务
            executor.invokeAll(tasks);
        } catch (InterruptedException e) {
            log.error("selectHealthEvalStatistics: execute all task error", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }
        long time3 = System.currentTimeMillis();
        log.info("time3-time2 :{}", time3-time2);
        return result;
    }

    @Override
    public List<BattHealthStatusEvalPo> selectLastByMoIds(List<String> moIds) throws UedmException
    {
        List<BattHealthStatusEvalPo> pojos = new ArrayList<>();

        if(CollectionUtils.isEmpty(moIds))
        {
            return pojos;
        }

        pojos = battHealthStatusEvalMapper.selectLastHealthEvalByMoIds(moIds);
        HashSet<String> hashSet = new HashSet<>(moIds);
        pojos = pojos.stream().filter(bean -> hashSet.contains(bean.getId())).collect(Collectors.toList());
//        BatchUtils.doInBatch(1000, moIds, (temIds -> {
//            try
//            {
//                pojos.addAll(battHealthStatusEvalMapper.selectLastHealthEvalByMoIds(temIds));
//            }
//            catch (Exception e)
//            {
//                log.error("battHealthStatusEvalMapper selectLastHealthEvalByMoIds error", e);
//                throw new RuntimeException();
//            }
//        }));

        return pojos;
    }

    @Override
    public PageInfo<BattHealthStatusEvalPo> selectEvalDetail(SelectEvalDetailDto selectEvalDetailDto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException
    {
        if(null==selectEvalDetailDto || null==serviceBaseInfoBean)
        {
            log.error("BattHealthStatusEvalDomainImpl -> selectEvalDetail params is null");
            throw new UedmException(-100,"params is blank");
        }
        if(serviceBaseInfoBean.isPage() && StringUtils.isNotEmpty(selectEvalDetailDto.getOrder()))
        {
            log.info("BattHealthStatusEvalDomainImpl -> selectEvalDetail start page");
            PageHelper.startPage(serviceBaseInfoBean.getPageNo(),serviceBaseInfoBean.getPageSize());
        }
        try
        {
            if (StringUtils.isBlank(selectEvalDetailDto.getEvaluateTime()))
            {
                return new PageInfo<>(new ArrayList<>());
            }
            String logroupId = GlobalOptional.GLOBAL_ROOT.equals(selectEvalDetailDto.getLogicGroupId()) ? "" : selectEvalDetailDto.getLogicGroupId();
            selectEvalDetailDto.setLogicGroupId(logroupId);
            List<BattHealthStatusEvalPo> battHealthStatusEvalPos = battHealthStatusEvalMapper.selectEvalDetail(selectEvalDetailDto);
            PageInfo<BattHealthStatusEvalPo> battHealthStatusEvalPoPageInfo = new PageInfo<>(battHealthStatusEvalPos);
            return battHealthStatusEvalPoPageInfo;
        }
         catch (Exception e)
        {
            log.error("BattHealthStatusEvalDomainImpl selectEvalDetail  DB is error :{}",e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
    }
}
