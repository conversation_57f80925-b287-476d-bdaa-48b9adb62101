package com.zte.uedm.battery.enums.overview;


/**
 * @FileName : BattStatisticsTypesEnum
 * @FileDesc : TODO
 * <AUTHOR> wc 10263256
 * @date Date : 2022年05月16日 20:27
 * @Version : 1.0
 */

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池的统计维度
 * 使用场景为电池总览页面
 */
public enum BattStatisticsSohEnum {

    SOH_0("health","{\"en-US\":\"health\",\"zh-CN\":\"健康\"}" ),
    SOH_1("sub_health","{\"en-US\":\"sub_health\",\"zh-CN\":\"亚健康\"}" ),
    SOH_2("fault","{\"en-US\":\"fault\",\"zh-CN\":\"异常\"}" );




    private String id;
    private String name;

    private String getId()
    {
        return this.id;
    }
    private String getName()
    {
        return this.name;
    }

    BattStatisticsSohEnum(String id, String name)
    {
        this.id = id;
        this.name = name;
    }

    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getAllBattStatisticsSohIds()
    {
        List<String> allIds = new ArrayList<>();

        BattStatisticsSohEnum[] values = BattStatisticsSohEnum.values();
        for(BattStatisticsSohEnum value : values)
        {
            if(StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }

    /**
     * 根据id获取name
     * @return
     */
    public static String getNameById(String id)
    {
        String name = null;
        BattStatisticsSohEnum[] values = BattStatisticsSohEnum.values();
        for(BattStatisticsSohEnum value : values)
        {
            if(StringUtils.isNotBlank(value.getName()) && value.getId().equals(id) ){
                name = value.getName();
            }

        }
        return name;
    }
}
