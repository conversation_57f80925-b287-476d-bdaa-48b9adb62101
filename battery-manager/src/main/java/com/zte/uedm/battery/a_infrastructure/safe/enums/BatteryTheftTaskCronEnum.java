package com.zte.uedm.battery.a_infrastructure.safe.enums;

/* Started by AICoder, pid:706ba77c0ah157214c0c0822d0cf1a3d15e28ead */
public enum BatteryTheftTaskCronEnum {
    HOURLY("1", "0 0 0/1 * * ?"),
    TWO_HOURLY("2", "0 0 0/2 * * ?"),
    SIX_HOURLY_DEFAULT("6", "0 0 0/6 * * ?"),
    TWELVE_HOURLY("12", "0 0 0/12 * * ?"),
    TWENTY_FOUR("24", "0 0 0 * * ?"),
    FORTY_EIGHT("48", "0 0 0 1/2 * ?");

    private final String hours;
    private final String cronExpression;

    BatteryTheftTaskCronEnum(String hours, String cronExpression) {
        this.hours = hours;
        this.cronExpression = cronExpression;
    }

    public String getHours() {
        return hours;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public static String generateCronExpression(String hours) {
        for (BatteryTheftTaskCronEnum cronExpressionEnum : values()) {
            if (cronExpressionEnum.getHours().equals(hours)) {
                return cronExpressionEnum.getCronExpression();
            }
        }
        return SIX_HOURLY_DEFAULT.getCronExpression(); // 默认值
    }
}

/* Ended by AICoder, pid:706ba77c0ah157214c0c0822d0cf1a3d15e28ead */