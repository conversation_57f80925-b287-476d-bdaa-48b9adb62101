package com.zte.uedm.battery.controller.batthealth.dto;

import com.zte.uedm.battery.bean.ImageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.LinkedHashMap;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(description = "评估统计")
public class EvalStatisticsExportDto extends SelectEvalDetailDto
{
    @ApiModelProperty(value = "图片")
    private List<ImageBean> image;


    public LinkedHashMap<String,Object> initHeader()
    {
        LinkedHashMap<String, Object> header = new LinkedHashMap<>();
        header.put("name","{\"en_US\":\"batt\",\"zh_CN\":\"电池\"}");
        header.put("position","{\"en_US\":\"position\",\"zh_CN\":\"位置\"}");
        header.put("status","{\"en_US\":\"current Status\",\"zh_CN\":\"当前状态\"}");
        header.put("preStatus","{\"en_US\":\"State Before Change\",\"zh_CN\":\"变化前状态\"}");
        header.put("statusModifiedTime","{\"en_US\":\"State Change Time\",\"zh_CN\":\"状态变化时间\"}");
        header.put("startDate","{\"en_US\":\"startDate\",\"zh_CN\":\" 启用时间\"}");
        header.put("productionDate","{\"en_US\":\"productionDate\",\"zh_CN\":\"生产时间\"}");
        return  header;
    }
}
