/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：ConfigurationRpcServiceImpl
 * 文件作者：00248587
 * 开发时间：2023/11/8
 */
package com.zte.uedm.battery.opti.infrastructure.gateway.persistence;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.opti.domain.gateway.ConfigurationServiceInterface;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.opt.logic.group.entity.RealGroupEntity;
import com.zte.uedm.common.configuration.opt.monitordevice.entity.MonitorDeviceModuleEntity;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.configuration.opt.real.group.site.entity.SiteEntity;
import com.zte.uedm.common.configuration.opt.relation.objectdevice.entity.MonitorDeviceObjectRelationEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConfigurationServiceInterImpl implements ConfigurationServiceInterface
{

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DeviceCacheManager deviceCacheManager;

    private static final Integer PAGE_SIZE = 5000;
    private final static Integer SIGLE_SEARCH_NUM = 5000;

    /**
     * 监控对象     工程配置适配后不分页，从缓存获取全部
     * 根据ids查询该moc类型的监控对象实体
     * @param ids
     * @return
     * @throws UedmException
     */
    @Override
    public List<MonitorObjectEntity> batchGetAllObjectByMoc(List<String> ids, String moc) throws UedmException
    {
        List<MonitorObjectEntity> allMonitorObject = new ArrayList<>();
        if (StringUtils.isEmpty(moc) || CollectionUtils.isEmpty(ids)){
            return allMonitorObject;
        }
        deviceCacheManager.selectAllDevice().stream()
                .filter(entity -> moc.equals(entity.getMoc()) && ids.contains(entity.getId()))
                .forEach(entity -> {
                    MonitorObjectEntity monitorObjectEntity = new MonitorObjectEntity();
                    monitorObjectEntity.setId(entity.getId());
                    monitorObjectEntity.setParentId(entity.getParentId());
                    monitorObjectEntity.setMoc(entity.getMoc());
                    monitorObjectEntity.setName(entity.getName());
                    monitorObjectEntity.setDisplayname(entity.getName());
                    monitorObjectEntity.setPathName(entity.getPathName());
                    monitorObjectEntity.setNamePath(entity.getPathName());
                    monitorObjectEntity.setIdPath(entity.toStringPathId());
                    allMonitorObject.add(monitorObjectEntity);
                });
        log.info("ConfigureServiceInterface getPageObjectByIdsAndMoc {}, size is {}",  moc, allMonitorObject.size());
        log.debug("ConfigureServiceInterface getPageObjectByIdsAndMoc is {}", allMonitorObject);
        return allMonitorObject;
    }


}
