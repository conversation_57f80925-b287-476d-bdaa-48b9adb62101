package com.zte.uedm.battery.pv.enums;

import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public enum SolarMonitorOverviewDimEnums {
    NAME("name","{\"zh_CN\":\"名称\",\"en_US\":\"Name\"}", 1, true, true, null, false),
    ON_OFF_LINE("onLine","{\"zh_CN\":\"在线状态\",\"en_US\":\"On-Off Line\"}",2,true, true,null,true),
    ALARM_STATE("alarmState","{\"zh_CN\":\"告警状态\",\"en_US\":\"Alarm State\"}", 3, true, true, null, true),
    GENERATION_POWER("generationPower", "{\"zh_CN\":\"发电功率\",\"en_US\":\"Generation Power\"}", 4, true, true, "kW", true),
    INSTALLATION_CAPACITY("installationCapacity", "{\"zh_CN\":\"装机容量\",\"en_US\":\"Installation Capacity\"}", 5, true, false, "kW", true),
    EFFICIENCY("efficiency","{\"zh_CN\":\"转换效率\",\"en_US\":\"Efficiency\"}", 6,true, false, "%", true),

    TODAY_POWER("todayPower","{\"zh_CN\":\"今日发电量\",\"en_US\":\"Today\"}",7, false, false, "kWh", false),
    TODAY_CO2("todayCO2", "{\"zh_CN\":\"今日CO2\",\"en_US\":\"Today CO2\"}", 8, false,false, "kg", false),
    THIS_MONTH_POWER("monthPower", "{\"zh_CN\":\"当月发电量\",\"en_US\":\"This Month\"}", 9, false, false, "kWh", false),
    THIS_MONTH_CO2("monthCO2","{\"zh_CN\":\"当月CO2\",\"en_US\":\"This Month CO2\"}", 10, false, false, "kg", false),
    THIS_YEAR_POWER("yearPower","{\"zh_CN\":\"当年发电量\",\"en_US\":\"This Year\"}",11, false, false, "kWh", false),
    THIS_YEAR_CO2("yearCO2", "{\"zh_CN\":\"当年CO2\",\"en_US\":\"This Year CO2\"}", 12, false, false, "kg",false),
    ACCUM_POWER("accumulatedPower","{\"zh_CN\":\"累计发电量\",\"en_US\":\"Accum.\"}", 13, true, true, "kWh", false),
    ACCUM_CO2("accumulatedCO2","{\"zh_CN\":\"累计CO2\",\"en_US\":\"Accum. CO2\"}", 14, true, false, "kg", false),

    TODAY_REVENUE("todayRevenue", "{\"zh_CN\":\"今日发电收益\",\"en_US\":\"Today\"}", 15,false, false, null, false),
    THIS_MONTH_REVENUE("monthRevenue","{\"zh_CN\":\"当月发电收益\",\"en_US\":\"This Month\"}", 16, false, false, null, false),
    THIS_YEAR_REVENUE("yearRevenue", "{\"zh_CN\":\"当年发电收益\",\"en_US\":\"This Year\"}", 17, false, false, null, false),
    ACCUM_REVENUE("accumulatedRevenue", "{\"zh_CN\":\"累计发电收益\",\"en_US\":\"Accum.\"}", 18, true, false, null, false),
    TODAY_SAVINGS("todaySavings","{\"zh_CN\":\"今日节省电费\",\"en_US\":\"Today Savings\"}", 19, false, false, null, false),
    THIS_MONTH_SAVINGS("monthSavings", "{\"zh_CN\":\"当月节省电费\",\"en_US\":\"This Month Savings\"}", 20, false, false,null, false),
    THIS_YEAR_SAVINGS("yearSavings", "{\"zh_CN\":\"当年节省电费\",\"en_US\":\"This Year Savings\"}", 21, false, false, null, false),
    ACCUM_SAVINGS("accumulatedSavings", "{\"zh_CN\":\"累计节省电费\",\"en_US\":\"Accum. Savings\"}", 22, true, false, null, false),

    ON_SERVICE_DATE("onServiceDate", "{\"zh_CN\":\"启用日期\",\"en_US\":\"On Service Date\"}", 23, true, false, null, false),
    EXPIRATION_DATE("expirationDate", "{\"zh_CN\":\"过保日期\",\"en_US\":\"End Warantee Date\"}", 24, true, false, null, false),
    LOCATION("location", "{\"zh_CN\":\"位置\",\"en_US\":\"Location\"}", 25, true, true,null, false),
    RATE_TYPE("rateType", "{\"zh_CN\":\"计费方式\",\"en_US\":\"Rate Structure\"}", 26, true, false,null, false),
    TODAY_MAX_POWER("todayMaxPower", "{\"zh_CN\":\"当日最大功率\",\"en_US\":\"Today Max. Power\"}", 27, false, false,"kW", false),
    TODAY_MAX_EFFICIENCY("todayMaxEfficiency", "{\"zh_CN\":\"当日最大效率\",\"en_US\":\"Today Max. Efficiency\"}", 28, false, false,"%", false),
    MONTH_MAX_POWER("monthMaxPower", "{\"zh_CN\":\"当月最大功率\",\"en_US\":\"This Month Max. Power\"}", 29, false, false,"kW", false),
    MONTH_MAX_EFFICIENCY("monthMaxEfficiency", "{\"zh_CN\":\"当月最大效率\",\"en_US\":\"This Month Max. Efficiency\"}", 30, false, false,"%", false),
    YEAR_MAX_POWER("yearMaxPower", "{\"zh_CN\":\"当年最大功率\",\"en_US\":\"This Year Max. Power\"}", 31, false, false,"kW", false),
    YEAR_MAX_EFFICIENCY("yearMaxEfficiency", "{\"zh_CN\":\"当年最大效率\",\"en_US\":\"This Year Max. Efficiency\"}", 32, false, false,"%", false),
    LONGITUDE("longitude", "{\"zh_CN\":\"经度\",\"en_US\":\"Longitude\"}", 33, false, false,null, false),
    LATITUDE("latitude", "{\"zh_CN\":\"纬度\",\"en_US\":\"Latitude\"}", 34, false, false,null, false);

    /**
     * id
     */
    private String id;
    /**
     * 中英文名称
     */
    private String name;
    /**
     * 默认顺序
     */
    private Integer defaultIndex;
    /**
     * 默认是否启用
     */
    private Boolean defaultEnable;
    /**
     * 是否固定
     */
    private Boolean defaultFixed;
    /**
     * 单位
     */
    private String unit;
    /**
     * 字段内容是否排序
     */
    private Boolean sortable;



    public String getId() { return this.id; }
    public String getName() { return this.name; }
    public Integer getDefaultIndex(){ return this.defaultIndex; }
    public Boolean getDefaultEnable() { return this.defaultEnable; }

    public Boolean getDefaultFixed() {
        return this.defaultFixed;
    }

    public String getUnit() {
        return this.unit;
    }

    public Boolean getSortable() {
        return sortable;
    }
    SolarMonitorOverviewDimEnums(String id, String name, Integer defaultIndex, Boolean defaultEnable, Boolean defaultFixed,String unit, Boolean sortable)
    {
        this.id = id;
        this.name = name;
        this.defaultIndex = defaultIndex;
        this.defaultEnable = defaultEnable;
        this.defaultFixed = defaultFixed;
        this.unit = unit;
        this.sortable = sortable;
    }

    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getIdsWithIdName() {
        List<String> ids = new ArrayList<>();
        ids.add(SolarMonitorOverviewDimEnums.NAME.getId());
        ids.add(SolarMonitorOverviewDimEnums.ON_OFF_LINE.getId());
        ids.add(SolarMonitorOverviewDimEnums.ALARM_STATE.getId());
        ids.add(SolarMonitorOverviewDimEnums.GENERATION_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.INSTALLATION_CAPACITY.getId());
        ids.add(SolarMonitorOverviewDimEnums.EFFICIENCY.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_CO2.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_MONTH_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_MONTH_CO2.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_YEAR_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_YEAR_CO2.getId());
        ids.add(SolarMonitorOverviewDimEnums.ACCUM_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.ACCUM_CO2.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_REVENUE.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_MONTH_REVENUE.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_YEAR_REVENUE.getId());
        ids.add(SolarMonitorOverviewDimEnums.ACCUM_REVENUE.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_SAVINGS.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_MONTH_SAVINGS.getId());
        ids.add(SolarMonitorOverviewDimEnums.THIS_YEAR_SAVINGS.getId());
        ids.add(SolarMonitorOverviewDimEnums.ACCUM_SAVINGS.getId());
        ids.add(SolarMonitorOverviewDimEnums.ON_SERVICE_DATE.getId());
        ids.add(SolarMonitorOverviewDimEnums.EXPIRATION_DATE.getId());
        ids.add(SolarMonitorOverviewDimEnums.LOCATION.getId());
        ids.add(SolarMonitorOverviewDimEnums.RATE_TYPE.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_MAX_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.TODAY_MAX_EFFICIENCY.getId());
        ids.add(SolarMonitorOverviewDimEnums.MONTH_MAX_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.MONTH_MAX_EFFICIENCY.getId());
        ids.add(SolarMonitorOverviewDimEnums.YEAR_MAX_POWER.getId());
        ids.add(SolarMonitorOverviewDimEnums.YEAR_MAX_EFFICIENCY.getId());
        ids.add(SolarMonitorOverviewDimEnums.LONGITUDE.getId());
        ids.add(SolarMonitorOverviewDimEnums.LATITUDE.getId());
        return ids;
    }
    private static Map<String, SolarMonitorOverviewDimEnums> map;

    static {
        map = Arrays.stream(SolarMonitorOverviewDimEnums.values())
                .collect(Collectors.toMap(SolarMonitorOverviewDimEnums::getId, a -> a));
    }

    /**
     * 根据id获取名称
     */
    public static String getNameById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarMonitorOverviewDimEnums::getName)
                .orElse(null);
    }

    /**
     * 根据id获取默认顺序
     */
    public static Integer getDefaultIndexById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarMonitorOverviewDimEnums::getDefaultIndex)
                .orElse(0);
    }

    /**
     * 根据id获取是否启用默认值
     */
    public static boolean getDefaultEnableById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarMonitorOverviewDimEnums::getDefaultEnable)
                .orElse(Boolean.FALSE);
    }

    /**
     * 根据id获取是否固定值
     */
    public static boolean getDefaultFixedById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarMonitorOverviewDimEnums::getDefaultFixed)
                .orElse(Boolean.FALSE);
    }

    /**
     * 根据id获取是否分类
     */
    public static boolean getSortableById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(SolarMonitorOverviewDimEnums::getSortable)
                .orElse(Boolean.FALSE);
    }

    /**
     * 获取所有id
     */
    public static List<String> getAllIds() {
        return Arrays.stream(SolarMonitorOverviewDimEnums.values())
                .map(SolarMonitorOverviewDimEnums::getId)
                .collect(Collectors.toList());
    }

    /**
     * id校验
     */
    public static void checkIdIsNeeded(List<String> ids) throws UedmException {
        if (CollectionUtils.isEmpty(ids)) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim ids is empty!");
            throw new UedmException(-301, "Dim ids is empty!");
        }
        ids.removeAll(SolarMonitorOverviewDimEnums.getAllIds());
        if (ids.size() > 0) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim dim id beyond limit!");
            throw new UedmException(-305, "Dim id beyond limit");
        }
    }

    public static Map<String, SolarMonitorOverviewDimEnums> getAllCannotModifyEnums() {
        SolarMonitorOverviewDimEnums[] values = SolarMonitorOverviewDimEnums.values();
        return Arrays.stream(values)
                .filter(bean -> !bean.getDefaultFixed()).filter(bean -> StringUtils.isNotBlank(bean.getId())).
                collect(Collectors.toMap(SolarMonitorOverviewDimEnums::getId, a -> a));
    }

    /**
     * 根据id获取单位
     * @param id
     * @return
     */
    public static String getUnitById(String id)
    {
        log.info("传入的id：{}", id);
        if(StringUtils.isBlank(id))
        {
            return null;
        }

        SolarMonitorOverviewDimEnums[] values = SolarMonitorOverviewDimEnums.values();
        for(SolarMonitorOverviewDimEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.getUnit();
            }
        }
        return null;
    }
}
