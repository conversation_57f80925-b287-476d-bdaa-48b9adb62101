package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.pv.PriceSettingBean;
import com.zte.uedm.battery.bean.pv.PvRevenueOverviewResponseBean;
import com.zte.uedm.battery.bean.pv.PvRevenueQueryBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;
import com.zte.uedm.battery.controller.pv.dto.PvPowerAnalysisDetailDto;
import com.zte.uedm.battery.controller.pv.dto.PvPowerAnalysisExportDto;
import com.zte.uedm.battery.controller.pv.vo.PvPowerAnalysisDetailVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface PvRevenueAnalysisService {

    /**
     * 设置单价
     * @param priceSettingBean
     * @throws UedmException
     */
    void priceSetting(PriceSettingBean priceSettingBean) throws UedmException;

    PvRevenueOverviewResponseBean overview(PvRevenueQueryBean pvRevenueQueryBean,String userName, String languageOption) throws UedmException;

    Map<String, Double> queryPrice() throws UedmException;

    List<PvPowerAnalysisDetailVo> pvPowerAnalysisDetail(PvPowerAnalysisDetailDto pvPowerAnalysisDetailDto, String languageOption, HttpServletRequest request) throws UedmException;


    String pvPowerAnalysisExport(PvPowerAnalysisExportDto pvPowerAnalysisDetailDto, ServiceBaseInfoBean serviceBean,
                                 HttpServletRequest request, HttpServletResponse response) throws UedmException;
}
