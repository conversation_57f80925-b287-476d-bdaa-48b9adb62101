package com.zte.uedm.battery.controller;

import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.ScopeStrategyRequestBean;
import com.zte.uedm.battery.bean.SynCfgDataToBatteryBean;
import com.zte.uedm.battery.bean.TemplatePriceIntervalBean;
import com.zte.uedm.battery.bean.peak.TemplateStrategyBo;
import com.zte.uedm.battery.pv.vo.IntervalStrategyTypeVo;
import com.zte.uedm.battery.service.GridStrategyService;
import com.zte.uedm.battery.service.impl.PeakShiftConfigBaseServiceImpl;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import retrofit2.http.Body;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 电网策略controller
 */
@Path("grid-strategy")
@Component
//@Api(value = "grid-strategy")
@Api(value = "电网策略")
@Slf4j
public class GridStrategyController
{
    @Autowired
    private GridStrategyService strategyService;

    @Autowired
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Autowired
    private JsonService jsonService;

    @POST
    @Path("/scope/sync-scope-data")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "范围策略数据同步rpc（新增、修改、删除）", notes = "范围策略数据同步rpc（新增、修改、删除）", httpMethod = HttpMethod.POST
            , response = ResponseBean.class, tags = {"范围策略"})
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空")
    })
    public ResponseBean syncScopeData(ScopeStrategyRequestBean bean, @QueryParam("operation") String operation)
    {
        try
        {
            log.info("syncScopeData requestBean :{},operation:{}", bean,operation);
            Integer total = strategyService.syncScopeDataRpc(bean,operation);
            if (total > 0)
            {
                return ResponseBeanUtils.getNormalResponseBean(0, total, 1);
            }
            return ResponseBeanUtils.getRemoteResponseBean(-200, "syncScopeData fail");
        }
        catch (Exception e)
        {
            log.error("/grid-strategy/scope/syncScopeData exception", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1,"/grid-strategy/scope/syncScopeData exception");
        }
    }


    @GET
    @Path("/interval-strategy/template-info")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "interval-strategy-detail", notes = "interval-strategy-detail", httpMethod = HttpMethod.GET,
            response = ResponseBean.class, tags = {"时段策略"})
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空")
    })
    public ResponseBean queryTemplateDetails(@QueryParam("seasonStrategyId") String seasonStrategyId)
    {
        if (StringUtils.isBlank(seasonStrategyId))
        {
            log.error("queryTemplateDetails error, seasonStrategyId is blank");
            return ResponseBeanUtils.getParameterBlankResponseBean();
        }
        try
        {
            log.debug("queryTemplateDetails seasonStrategyId is:{}",seasonStrategyId);
            List<TemplateStrategyBo> res = strategyService.selectTemplateDetailsBySeasonStrategyId(seasonStrategyId);
            int total = res.size();
            return ResponseBeanUtils.getNormalResponseBean(0, res, total);
        }
        catch (Exception e)
        {
            log.error("queryStrategyDetail Exception", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "queryStrategyDetail Exception");
        }
    }

    /**
     * 配置电价策略拆分调整同步至电源
     * 范围策略、价格策略及详情、季节时段策略及详情
     *
     * @param synCfgDataToBatteryBean 同步数据，包含表更新与新增策略信息
     * @return Call<ResponseBean>
     */
    @POST
    @Path("scope-strategy/synData")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "电价策略调整数据同步Rpc接口", notes = "电价策略调整数据同步Rpc接口", httpMethod = HttpMethod.POST, response = ResponseBean.class)
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = -200, message = "同步数据失败"),
            @ApiResponse(code = -305, message = "同步新增或更新失败")
    })
    public ResponseBean synCfgPricePolicyModifyRpc(@Body SynCfgDataToBatteryBean synCfgDataToBatteryBean) {
        log.info("Syn config price policy modify Rpc starts. ###");
        if (ObjectUtils.isEmpty(synCfgDataToBatteryBean))
        {
            log.error("synCfgPricePolicyModifyRpc error, synCfgDataToBatteryBean is empty");
            return ResponseBeanUtils.getParameterBlankResponseBean();
        }
        try
        {
            boolean successFlag = strategyService.synCfgPricePolicyModifyRpc(synCfgDataToBatteryBean);
            log.info("Syn config price policy modify Rpc is END. ###");
            if (successFlag)
            {
                log.info(ResponseBeanUtils.getNormalResponseBean(0, "Syn success", 1).toString());
                return ResponseBeanUtils.getNormalResponseBean(0, "Syn success", 1);
            }
            return ResponseBeanUtils.getRemoteResponseBean(-200, "syncScopeData fail, please check body.");
        }
        catch (UedmException e)
        {
            log.error("/grid-strategy/scope-strategy/synData exception", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @GET
    @Path("/interval-strategy/userSet")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "获取用户已设置过的策略包含的峰谷类型", notes = "interval-strategy-userSet", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空")
    })
    public ResponseBean getIntervalStrategyType(){
        try {
            List<IntervalStrategyTypeVo> list = strategyService.getIntervalStrategyType();
            return ResponseBeanUtils.getNormalResponseBean(0,list,list.size());
        } catch (Exception e) {
            log.error("queryStrategyDetail Exception", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "queryStrategyDetail Exception");
        }
    }

    @GET
    @Path("/template-price-interval")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "根据模板Id查询电价策略", notes = "根据模板Id查询电价策略", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = -1, message = "查询数据失败")
    })
    public ResponseBean getTemplatePriceInterval(@QueryParam("templateStrategyId") String templateStrategyId,@QueryParam("deviceType") String deviceType,
                                                 @HeaderParam("language-option") String languageOption,@Context HttpServletRequest request)
    {
        if (StringUtils.isBlank(templateStrategyId))
        {
            log.error("getTemplatePriceInterval error, templateStrategyId is blank");
            return ResponseBeanUtils.getParameterBlankResponseBean();
        }
        try
        {
            String userName = Tools.getUserName(request);
            String host = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption, false,
                    false);
            List<TemplatePriceIntervalBean> list = strategyService.getTemplatePriceInterval(templateStrategyId,deviceType,
                    serviceBaseInfoBean);
            int total = list.size();
            return ResponseBeanUtils.getNormalResponseBean(0, list, total);
        }
        catch (Exception e)
        {
            log.error("getTemplatePriceInterval Exception", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

}
