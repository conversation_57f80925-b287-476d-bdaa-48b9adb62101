package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.peak.PeakShiftOperationRecordPojo;
import com.zte.uedm.battery.bean.peak.PeakShiftOperationRecordSelectDto;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PeakShiftOperationRecordMapper
{
    /**
     * 查询错峰设置操作记录
     * @param peakShiftOperationRecordSelectDto
     * @return
     * @throws UedmException
     */
    List<PeakShiftOperationRecordPojo> selectPeakShiftOperationRecord(PeakShiftOperationRecordSelectDto peakShiftOperationRecordSelectDto) throws UedmException;

    /**
     * 插入BCUA操作履历
     * @param recordPojo
     * @return
     */
    int setOperationRecord(@Param("recordPojo") PeakShiftOperationRecordPojo recordPojo);

    /**
     * 更新实际执行实际
     */
    int updateExecTime(@Param("id") String id,@Param("execTime") String execTime);
}