package com.zte.uedm.battery.a_domain.safe.task.scheduler;

import com.zte.oes.dexcloud.configcenter.commons.event.ConfigChangeEvent;
import com.zte.oes.dexcloud.configcenter.commons.event.ConfigChangeItem;
import com.zte.uedm.battery.a_domain.safe.task.BatteryProtectStateEkeyTask;
import com.zte.uedm.battery.a_domain.safe.task.BatteryTheftStatusTask;
import com.zte.uedm.battery.a_domain.safe.task.config.CronTimeConfig;
import com.zte.uedm.battery.a_infrastructure.safe.enums.BatteryTheftTaskCronEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class BatteryTheftCronConfigListener {

    @Resource
    private CronTimeConfig timeConfig;
    @Resource
    private BatteryTheftQuartzJob quartzJob;
    public static final String BATTERY_THEFT_TASK_KEY = "battery.theft.status.schedule";
    public static final String BATTERY_THEFT_TASK_NAME = "battery-theft-status-schedule";
    //心跳下发
    public static final String BATTERY_PROTECT_STATE_KEY = "battery.protect.state.schedule";
    //电子钥匙匹配
    public static final String BATTERY_PROTECT_STATE_TASK_NAME = "battery-protect-state-schedule";

    @PostConstruct
    public void initCreateBatteryTheftStatusCronJob(){
        String cron = timeConfig.getSynchronize();
        String protectCron = timeConfig.getBatteryProtect();
        quartzJob.createScheduleJobCron(cron, BATTERY_THEFT_TASK_NAME, BatteryTheftStatusTask.class);
        quartzJob.createScheduleJobCron(protectCron, BATTERY_PROTECT_STATE_TASK_NAME, BatteryProtectStateEkeyTask.class);
    }

    /* Started by AICoder, pid:o8681q8dd7ad26a1426b090f30b96b046381f65c */
    @EventListener
    public void onConfigChange(ConfigChangeEvent event) {
        Map<String, ConfigChangeItem> changes = event.getChanges();
        handleCronChange(BATTERY_THEFT_TASK_KEY, BATTERY_THEFT_TASK_NAME, BatteryTheftStatusTask.class, changes);
        handleCronChange(BATTERY_PROTECT_STATE_KEY, BATTERY_PROTECT_STATE_TASK_NAME, BatteryProtectStateEkeyTask.class, changes);
    }

    private void handleCronChange(String configKey, String jobName, Class<? extends Job> jobClass,
                                  Map<String, ConfigChangeItem> changes) {
        if (changes.containsKey(configKey)) {
            String newCron = String.valueOf(changes.get(configKey).getNewValue());
            log.info("handleCronChange cron change for {}: newCron: {}", configKey, newCron);

            if (!quartzJob.checkExistsScheduleJob(jobName)) {
                log.info("handleCronChange Creating job: {}", jobName);
                quartzJob.createScheduleJobCron(newCron, jobName, jobClass);
            } else {
                log.info("handleCronChange Updating job: {}", jobName);
                updateScheduleJob(jobName, newCron);
            }
        }
    }
    /* Ended by AICoder, pid:o8681q8dd7ad26a1426b090f30b96b046381f65c */

    /* Started by AICoder, pid:e1a53y13fdy739b143b30bf6f049090c2a18436f */
    private void updateScheduleJob(String jobName, String newCron) {
        if (StringUtils.isNotEmpty(newCron)) {
            newCron = BatteryTheftTaskCronEnum.generateCronExpression(newCron);
            quartzJob.updateScheduleJobCron(newCron, jobName);
        }
    }
    /* Ended by AICoder, pid:e1a53y13fdy739b143b30bf6f049090c2a18436f */
}
