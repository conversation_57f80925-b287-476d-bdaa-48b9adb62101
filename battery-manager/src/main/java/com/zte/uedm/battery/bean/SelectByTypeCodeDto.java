package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class SelectByTypeCodeDto
{
    //排序字段-告警码
    public static final String ORDER_ALARM_CODE = "alarmCode";
    //排序字段-告警码名称
    public static final String ORDER_ALARM_CODE_NAME = "alarmCodeName";

    @ApiModelProperty(value = "监控对象id")
    private String moId;
    @ApiModelProperty(value = "模糊搜索条件-关联告警码")
    private List<String> smpId;
    @ApiModelProperty(value = "模糊搜索条件-告警码")
    private String alarmCode;
    @ApiModelProperty(value = "模糊搜索条件-告警码名称")
    private String alarmCodeName;
    @ApiModelProperty(value = "顺序")
    private String order;
    @ApiModelProperty(value = "排序字段")
    private String sort;

    /**
     * 校验模糊搜索条件是否为null，null-false
     * @return
     */
    public Pair<Boolean, List<String>>  checkSearchCondition()
    {
        boolean checkRes = true;
        List<String> err = new ArrayList<>();
        if(null==this.getSmpId())
        {
            err.add("smpId is null.");
            checkRes = false;
        }
        if(null==this.getAlarmCode())
        {
            err.add("alarmCode is null.");
            checkRes = false;
        }
        if(null==this.getAlarmCodeName())
        {
            err.add("alarmCodeName is null.");
            checkRes = false;
        }

        return Pair.of(checkRes, err);
    }

}
