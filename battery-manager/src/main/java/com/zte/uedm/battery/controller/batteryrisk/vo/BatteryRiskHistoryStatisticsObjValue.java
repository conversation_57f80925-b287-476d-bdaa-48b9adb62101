package com.zte.uedm.battery.controller.batteryrisk.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@Getter
@Setter
@ToString
public class BatteryRiskHistoryStatisticsObjValue {
    /**
     *  风险等级
     */
    private RiskLevelObjValue riskLevel;
    /**
     *  数量
     */
    private Integer number;
    /**
     *  详情
     */
    private List<RiskDetailObjValue> details;
}
