package com.zte.uedm.battery.service;


import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.BattLifeEvalExportDto;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.BattLifeEvalResultDto;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.SelectSingleBattEvalHistoryDto;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.vo.BattLifeEvalResultVo;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.vo.SelectSingleBattEvalHistoryVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BattLifeEvalResultService {

    /**
     * 电池寿命评估结果概览查询service层
     * @param battLifeEvalResultDto 前段传入dto
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattLifeEvalResultVo> selectByCondition(BattLifeEvalResultDto battLifeEvalResultDto,
                                                     ServiceBaseInfoBean serviceBaseInfoBean)throws UedmException;

    /**
     * 电池寿命评估结果导出 service层
     * @param exportDto
     * @param request
     * @param response
     * @param serviceBean
     * @throws UedmException
     */
    void exportOverview(BattLifeEvalExportDto exportDto, HttpServletRequest request,
                        HttpServletResponse response, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 查询电池寿命等级id&name信息
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<IdNameBean> selectAllIdsAndNames(ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 单个电池寿命评估历史查询
     * @param dto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<SelectSingleBattEvalHistoryVo> selectSingleBattEvalHistory(SelectSingleBattEvalHistoryDto dto, ServiceBaseInfoBean serviceBean) throws UedmException;

}
