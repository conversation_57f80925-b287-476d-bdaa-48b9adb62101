package com.zte.uedm.battery.controller.batttest.vo;

import com.zte.uedm.battery.controller.batttest.bean.DurationBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TestOverviewVo {

    /**
     * 开关电源id
     */
    private String id;
    /**
     *  开关电源名称
     */
    private String name;
    /**
     *  站点id
     */
    private String siteId;
    /**
     *  站点名称
     */
    private String siteName;
    /**
     * 站点等级
     */
    private IdNameBean siteLevel;
    private String siteLevelId;
    /**
     * 站点供电场景
     */
    private IdNameBean powerSupplyScene;
    private String powerSupplySceneId;
    /**
     * 位置
     */
    private String position;
    /**
     * 测试前备电状态
     */
    private IdNameBean preBackupPowerStatus;
    /**
     * 测试前备电状态id
     */
    private String preBackupPowerStatusId;
    /**
     * 备电状态
     */
    private IdNameBean backupPowerStatus;
    private String backupPowerStatusId;
    /**
     * 测试前健康状态
     */
    private IdNameBean preHealthStatus;
    private String preHealthStatusId;
    /**
     * 健康状态
     */
    private IdNameBean healthStatus;
    private String healthStatusId;
    /**
     *  测试开始时间
     */
    private String testStartTime;
    /**
     * 测试状态
     */
    private IdNameBean testStatus;
    private String testStatusId;
    /**
     * 持续时长
     */
    private DurationBean duration;
    private int durationTotalMin;
    /**
     * 交流输入状态
     */
    private IdNameBean acInputStatus;
    private String acInputStatusId;

    /**
     *  下发时间
     */
    private String deliveryTime;
    /**
     *  测试结果
     */
    private IdNameBean testResult;
    /**
     *  测试类型
     */
    private IdNameBean testType;
}
