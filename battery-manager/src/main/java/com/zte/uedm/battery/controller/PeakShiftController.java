package com.zte.uedm.battery.controller;

import com.zte.uedm.battery.bean.PeakShiftCsuAllStrategyBean;
import com.zte.uedm.battery.bean.PeakShiftStatisticsQueryBean;
import com.zte.uedm.battery.bean.StrategyDetailQueryDto;
import com.zte.uedm.battery.service.PeakShiftDetailRptService;
import com.zte.uedm.battery.service.PeakShiftService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.HttpMethod;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("peak-shift")
@Component
//@Api(value = "peak-shift")
@Api(value = "错峰")
@Slf4j
public class PeakShiftController
{

    @Autowired
    private PeakShiftService peakShiftService;

    @Autowired
    private PeakShiftDetailRptService peakShiftDetailRptService;

    @POST
    @Path("/Strategy-all-detail/query")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询设备时间范围内的错峰策略（rpc）", notes = "查询设备时间范围内的错峰策略(Rpc)", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -1, message = "查询策略详情失败")
    })
    public ResponseBean queryStrategyDetail(StrategyDetailQueryDto strategyDetailQueryDto)
    {

        List<PeakShiftCsuAllStrategyBean> res;
        int total;
        try {
            res = peakShiftService.selectStrategyDetailByDeviceAndTimeRange(strategyDetailQueryDto.getDeviceId(), strategyDetailQueryDto.getStartTime(),
                    strategyDetailQueryDto.getEndTime());
            total = res.size();
        } catch (Exception e) {
            log.error("queryStrategyDetail is error!",e);
            return ResponseBeanUtils.getRemoteResponseBean(-1,"queryStrategyDetail is error!");
        }
        return ResponseBeanUtils.getNormalResponseBean(0,res, total);
    }

    /* Started by AICoder, pid:34772m47f6lfcb0142ca09b6509bd52d7b21ee35 */
    @POST
    @Path("/statistics/csu6")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "触发统计任务", notes = "触发统计任务", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = -601, message = "JSON转换异常"),
            @ApiResponse(code = -1, message = "获取关联失败"),
            @ApiResponse(code = -701, message = "一次计算最多30天数据")
    })
    public ResponseBean statisticsCSU6(@RequestBody PeakShiftStatisticsQueryBean queryBean) {
        try {
            peakShiftService.statisticsCSU6(queryBean.getStartDate(), queryBean.getEndDate(), queryBean.getPosition(), true);
        } catch (UedmException | com.zte.uedm.basis.exception.UedmException e) {
            log.error("statisticsCSU6 error", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        } catch (Exception e) {
            log.error("statisticsCSU6 parse error", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1,"statisticsCSU6 error");
        }
        return ResponseBeanUtils.getNormalResponseBean(0, null, 0);
    }
    /* Ended by AICoder, pid:34772m47f6lfcb0142ca09b6509bd52d7b21ee35 */
}