package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Setter
@Getter
@ToString
public class BattBackupPowerThresholdDetailPojo
{
    /**
     * 备电系统id
     */
    String id;
    /**
     * 阈值
     */
    Integer threshold;
    /**
     * 配置数据来源分类
     */
    String type;
    /**
     *分类id
     */
    String categoryId;

    Integer backupPowerScenario;

    /**
     *创建者
     */
    String creator;
    /**
     * 创建时间
     */
    Date gmtCreate;
    /**
     *更新者
     */
    String updater;
    /**
     *更新时间
     */
    Date gmtModified;


    public  BattBackupPowerThresholdDetailPojo(){}

    /**
     *新增-更新时构造
     * @param threshold
     * @param id
     * @param userName
     */
    public  BattBackupPowerThresholdDetailPojo(Integer threshold,String id,String userName)
    {
        this.threshold=threshold;
        this.id=id;
        this.type=ConfigSourceClassify.SPECIAL.getType();
        this.updater=userName;
        this.gmtModified=new Date();
    }

    /**
     * 删除特殊设置时--恢复默认设置
     * @param threshold
     * @param id
     * @param userName
     * @param date
     */
    public BattBackupPowerThresholdDetailPojo(Integer threshold,String id,String userName,Date date)
    {
        if(null==threshold)
        {
            this.threshold=4;
        }
        else
        {
            this.threshold=threshold;
        }
        this.updater = userName;
        this.id = id;
        this.type=ConfigSourceClassify.DEFAULTCLASSIFY.getType();
        this.gmtModified = date;
    }

    /**
     * 备电分类设置-新增和编辑
     * @param userName
     * @param id
     * @param threshold
     * @param categoryId
     */
    public BattBackupPowerThresholdDetailPojo(String userName, String id, Integer threshold, String categoryId)
    {
        this.creator = userName;
        this.updater = userName;
        this.id = id;
        this.threshold=threshold;
        this.type=ConfigSourceClassify.CATEGORY.getType();
        this.categoryId = categoryId;
        this.gmtCreate = new Date();
        this.gmtModified = this.gmtCreate;
    }

    /**
     * 备电分类设置-删除
     * @param userName
     * @param id
     * @param threshold
     */
    public BattBackupPowerThresholdDetailPojo(String userName, String id, Integer threshold)
    {
        this.creator = userName;
        this.updater = userName;
        this.id = id;
        this.threshold = threshold;
        this.type = ConfigSourceClassify.DEFAULTCLASSIFY.getType();
        this.categoryId = null;
        this.gmtCreate = new Date();
        this.gmtModified = this.gmtCreate;
    }

    public BattBackupPowerThresholdDetailPojo(String userName, String id, Integer threshold, String categoryId,String type)
    {
        this.creator = userName;
        this.updater = userName;
        this.id = id;
        this.threshold=threshold;
        this.type=ConfigSourceClassify.DEFAULTCLASSIFY.getType();
        this.categoryId = categoryId;
        this.gmtCreate = new Date();
        this.gmtModified = this.gmtCreate;
    }

}


/**
 *配置数据来源分类
 */
@Slf4j
enum ConfigSourceClassify
{
    CATEGORY("category","{\"en_US\":\"category\",\"zh_CN\":\"分类设置\"}"),
    SPECIAL("special","{\"en_US\":\"special\",\"zh_CN\":\"特殊设置\"}"),
    DEFAULTCLASSIFY("default","{\"en_US\":\"default\",\"zh_CN\":\"默认\"}");

    private final String type;
    private final String typeZhEn;

    ConfigSourceClassify(String type, String typeZhEn)
    {
        this.type = type;
        this.typeZhEn = typeZhEn;
    }

    public String getType() {
        return type;
    }
    public String getTypeZhEn() {
        return typeZhEn;
    }
}

