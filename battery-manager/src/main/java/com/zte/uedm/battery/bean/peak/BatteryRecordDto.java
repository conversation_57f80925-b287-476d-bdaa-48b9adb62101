/* Started by AICoder, pid:0060bd09e35ac96142390beba0c6a724ffc6252c */
package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * 错峰监控查询充放电记录条件Dto
 */
@Getter
@Setter
@ToString
public class BatteryRecordDto {
    @NotEmpty(message = "device is empty")
    private String deviceId;

    @NotEmpty(message = "begin time is empty")
    private String beginTime;

    @NotEmpty(message = "end time is empty")
    private String endTime;

    private String status;
}
/* Ended by AICoder, pid:0060bd09e35ac96142390beba0c6a724ffc6252c */