package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskWithDevicePo;
import com.zte.uedm.battery.bean.PeakShiftTaskRequestBean;
import com.zte.uedm.battery.bean.peak.DeviceLatestPeakShiftTaskVo;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskPo;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskTemplateDetailPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.util.List;

/**
 * 电池管理配置mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PeakShiftTaskMapper {

    List<PeakShiftTaskPo>  selectByCondition(PeakShiftTaskRequestBean requestBean);

    PeakShiftTaskPo selectById(String id);

    /**
     * 新增 错峰任务表
     * @param bean
     * @return
     * @throws UedmException
     */
    Integer insertPeakShiftTask(@Param("bean") PeakShiftTaskPo bean) throws UedmException;

    /**
     * 更新 错峰任务表
     * @param bean
     * @return
     * @throws UedmException
     */
    Integer updatePeakShiftTask(@Param("bean") PeakShiftTaskPo bean) throws UedmException;

    List<PeakShiftTaskPo> checkTaskByname(String name) throws UedmException;

    /**
     * 错峰策略任务重名校验，新增（id不为空）和编辑（id为空）任务时，查询和输入任务名相同的名称
     * @param id
     * @param name
     * @return
     * @throws UedmException
     */
    List<String> selectDuplicateNames(String id, String name) throws UedmException;

    void updatePeakShiftTaskExpirationDate(String name, String expirationDate) throws UedmException;

    /**
     * 根据任务id更新其状态
     * @param id
     * @param status
     * @return
     * @throws UedmException
     */
    Integer updateStatusById(String id, String status) throws UedmException;

    /**
     * 查询各设备的最近一次策略任务
     * @return
     */
    List<DeviceLatestPeakShiftTaskVo> selectDeviceLatestTask() throws IOException;

    /**
     * 查询各设备的最近一次策略任务
     * @return
     */
    List<PeakShiftTaskWithDevicePo> selectDeviceLatestTaskWithDevice() throws IOException;

    /**
     * 查询已到生效但还未执行的任务
     * @return
     */
    List<PeakShiftTaskPo> selectEffectivePendingTask();

    void deleteById(String id);

    /**
     * 查询失效任务
     * @return
     */
    List<PeakShiftTaskPo> selectExpirationTask();

    /**
     * 根据任务ID联表查询得到策略名称和版本
     * @param idList
     * @return
     */
    List<PeakShiftTaskTemplateDetailPo> selectByIdList(@Param("idList") List<String> idList);

    List<String> selectTaskIds(@Param("timeout")String timeout ,@Param("status")String status);

    List<String> selectExecuteTaskIds();
}