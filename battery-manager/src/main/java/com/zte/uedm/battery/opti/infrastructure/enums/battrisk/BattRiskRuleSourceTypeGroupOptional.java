package com.zte.uedm.battery.opti.infrastructure.enums.battrisk;

import java.util.ArrayList;
import java.util.List;

public class BattRiskRuleSourceTypeGroupOptional
{
    public static final List<BattRiskRuleSourceTypeGroupOptional> instances = new ArrayList<>();

    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_ATTRIBUTE_ASSET = new BattRiskRuleSourceTypeGroupOptional("attribute_asset","{\"en_US\":\"Attribute_asset\",\"zh_CN\":\"属性-资产\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ATTRIBUTE);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_ATTRIBUTE_MONITOR_OBJECT = new BattRiskRuleSourceTypeGroupOptional("attribute_object","{\"en_US\":\"Attribute_object\",\"zh_CN\":\"属性-监控对象\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ATTRIBUTE);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_ALARM_CODE = new BattRiskRuleSourceTypeGroupOptional("alarm_code","{\"en_US\":\"Alarm Code\",\"zh_CN\":\"告警码\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_ALARM_FREQUENCY = new BattRiskRuleSourceTypeGroupOptional("alarm_frequency","{\"en_US\":\"Alarm Frequency\",\"zh_CN\":\"告警频次\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_ALARM_TIME = new BattRiskRuleSourceTypeGroupOptional("alarm_time","{\"en_US\":\"Alarm Time\",\"zh_CN\":\"告警时间\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_HEALTH= new BattRiskRuleSourceTypeGroupOptional("health_batt_health_status_eval","{\"en_US\":\"Health Status\",\"zh_CN\":\"健康状态\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_HEALTH);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_LIFF_DAY= new BattRiskRuleSourceTypeGroupOptional("life_batt_life_eval_d","{\"en_US\":\"Life\",\"zh_CN\":\"寿命\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_LIFE);
    public static final BattRiskRuleSourceTypeGroupOptional SOURCE_TYPE_GROUP_STD_POINT= new BattRiskRuleSourceTypeGroupOptional("stdpoint_cache_data","{\"en_US\":\"Standard Point Data\",\"zh_CN\":\"标准化数据\"}",BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);


    private String id;
    private String name;

    private BattRiskRuleSourceTypeOptional sourceType;

    public BattRiskRuleSourceTypeGroupOptional(){}
    private BattRiskRuleSourceTypeGroupOptional(String id, String name, BattRiskRuleSourceTypeOptional sourceType)
    {
        this.id = id;
        this.name = name;
        this.sourceType = sourceType;

        instances.add(this);
    }


    public static BattRiskRuleSourceTypeGroupOptional valueOf(String id)
    {
        for(BattRiskRuleSourceTypeGroupOptional sourceTypeGroup : instances)
        {
            if(sourceTypeGroup.id.equalsIgnoreCase(id))
            {
                return sourceTypeGroup;
            }
        }
        return null;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(obj != null && obj instanceof BattRiskRuleSourceTypeGroupOptional)
        {
            BattRiskRuleSourceTypeGroupOptional sourceTypeGroup = (BattRiskRuleSourceTypeGroupOptional) obj;
            return this.id.equalsIgnoreCase(sourceTypeGroup.id);
        }
        return false;
    }
    /* Started by AICoder, pid:3872380ec2k014814c28082d30d5d80316c44560 */
    @Override
    public int hashCode() {
        return this.id.hashCode();
    }
    /* Ended by AICoder, pid:3872380ec2k014814c28082d30d5d80316c44560 */
    @Override
    public String toString()
    {
        return "{ id: " + this.id + ", name: " +this.name + ", sourceType: " + sourceType.toString() + " }";
    }


}
