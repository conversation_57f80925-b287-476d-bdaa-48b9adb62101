package com.zte.uedm.battery.a_infrastructure.repository.peakshift.coverter;

/* Started by AICoder, pid:468dcd98882b455e9f3474f044323287 */
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailCsu5Entity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.TemplateStrategyDetailCsu5Po;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TemplateStrategyDetailCsu5PoConverter {
    TemplateStrategyDetailCsu5PoConverter INSTANCE = Mappers.getMapper(TemplateStrategyDetailCsu5PoConverter.class);

    @Mappings({})
    TemplateStrategyDetailCsu5Entity templateStrategyDetailCsu5PoToTemplateStrategyDetailCsu5Entity(TemplateStrategyDetailCsu5Po templateStrategyDetailCsu5Po);

    List<TemplateStrategyDetailCsu5Entity> listTemplateStrategyDetailCsu5PoToTemplateStrategyDetailCsu5Entity(List<TemplateStrategyDetailCsu5Po> poList);

    @Mappings({})
    TemplateStrategyDetailCsu5Po templateStrategyDetailCsu5EntityToTemplateStrategyDetailCsu5Po(TemplateStrategyDetailCsu5Entity templateStrategyDetailCsu5Entity);

    @Mappings({})
    List<TemplateStrategyDetailCsu5Po> listTemplateStrategyDetailCsu5EntityToTemplateStrategyDetailCsu5Po(List<TemplateStrategyDetailCsu5Entity> templateStrategyDetailCsu5Entities);
}
/* Ended by AICoder, pid:468dcd98882b455e9f3474f044323287 */
