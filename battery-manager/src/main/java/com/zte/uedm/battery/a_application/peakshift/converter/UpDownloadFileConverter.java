package com.zte.uedm.battery.a_application.peakshift.converter;

/* Started by AICoder, pid:x4516y510b1af8c140aa0aef1041e52b46541ebc */

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.UpDownloadFileEntity;
import com.zte.uedm.battery.bean.UpDownloadFileBean;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:unique_pid_for_this_converter */
@Mapper
public interface UpDownloadFileConverter {
    UpDownloadFileConverter INSTANCE = Mappers.getMapper(UpDownloadFileConverter.class);

    @Mappings({})
    UpDownloadFileBean upDownloadFileEntityToUpDownloadFileBean(UpDownloadFileEntity upDownloadFileEntity);

    List<UpDownloadFileBean> listUpDownloadFileEntityToUpDownloadFileBean(List<UpDownloadFileEntity> entityList);

    @Mappings({})
    UpDownloadFileEntity upDownloadFileBeanToUpDownloadFileEntity(UpDownloadFileBean upDownloadFileBean);

    List<UpDownloadFileEntity> listUpDownloadFileBeanToUpDownloadFileEntity(List<UpDownloadFileBean> beanList);
}
/* Ended by AICoder, pid:unique_pid_for_this_converter */
/* Ended by AICoder, pid:x4516y510b1af8c140aa0aef1041e52b46541ebc */
