package com.zte.uedm.battery.opti.domain.service.impl;

import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.opti.domain.service.BattRiskClassifyService;
import com.zte.uedm.battery.opti.domain.service.bean.enums.BattRiskRuleApplicationTypeEnum;
import com.zte.uedm.battery.opti.domain.service.bean.enums.OutSourceBattTypeEnum;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_MODEL;

@Component
@Slf4j
public class BattRiskClassifyServiceImpl implements BattRiskClassifyService
{
    @Autowired
    private BattAssetDomain battAssetDomain;

    @Autowired
    private BattTypeDomain battTypeDomain;

    @Autowired
    private DataRedis dataRedis;

    @Override
    public Map<String, List<String>> battClassifyByRisk(List<String> batts)
            throws UedmException
    {
        if(CollectionUtils.isEmpty(batts))
        {
            return new HashMap<>();
        }
        log.info("battClassifyByRisk : batts size is {}", batts.size());
        Map<String, List<String>> battRiskTypeMap = new HashMap<>();
        //获取所有的电池类型
        boolean assetEnable = battAssetDomain.selectAssetEnable();
        log.info("battClassifyByRisk : assetEnable is {}", assetEnable);
        List<BattTypeBean> batteryTypes = battTypeDomain.getBatteryTypeByMoIds(batts, assetEnable);
        log.info("battClassifyByRisk : batteryTypes size is {}", batteryTypes.size());
        log.debug("battClassifyByRisk : batteryTypes is {}", batteryTypes);
        Map<BattTypeEnum, Set<String>> battTypeMap = batteryTypes.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).distinct().collect(Collectors
                .groupingBy(BattTypeBean::getBattType, Collectors.mapping(BattTypeBean::getId, Collectors.toSet())));
        log.debug("battClassifyByRisk : battTypeMap is {}", battTypeMap);
        Set<String> pbacBattIds = battTypeMap.get(BattTypeEnum.PBAC);
        Set<String> liBattIds = battTypeMap.get(BattTypeEnum.LFP);
        if(CollectionUtils.isNotEmpty(pbacBattIds))
        {
            battRiskTypeMap.put(BattRiskRuleApplicationTypeEnum.PBAC_COMMON_RISK_RULE.getId(), new ArrayList<>(pbacBattIds));
        }
        if(CollectionUtils.isNotEmpty(liBattIds))
        {
            classifyLiBatt(liBattIds, battRiskTypeMap);
        }
        log.info("battClassifyByRisk : battRiskTypeMap key set is {}", battRiskTypeMap.keySet());
        log.debug("battClassifyByRisk : battRiskTypeMap  is {}", battRiskTypeMap);
        return battRiskTypeMap;
    }

    private void classifyLiBatt(Set<String> battIds, Map<String, List<String>> battRiskTypeMap) throws UedmException {
        Map<String, Map<String, Object>> batteryStdRealData = dataRedis.batchSelectRealData(new ArrayList<>(battIds), Collections.singletonList(BATTERY_SMPID_MODEL));
        log.debug("battClassifyByRisk : batteryStdRealData is {}", batteryStdRealData);
        List<String> selfBattIds = new ArrayList<>();
        List<String> outResourceBattIds = new ArrayList<>();
        List<String> commonBattIds = new ArrayList<>();
        Set<String> outSourceBattTypes = OutSourceBattTypeEnum.getAllIds();
        battIds.forEach(item -> {
            Map<String, Object> batteryTypeMap = batteryStdRealData.get(item);
            log.debug("{} batteryTypeMap is {}", item, batteryTypeMap);
            if(batteryTypeMap == null || batteryTypeMap.isEmpty())
            {
                commonBattIds.add(item);
            }
            else
            {
                Object valueObj = batteryTypeMap.get(BATTERY_SMPID_MODEL);
                if(valueObj == null)
                {
                    commonBattIds.add(item);
                }
                else
                {
                    Map<String, String> valueMap = (Map<String, String>) valueObj;
                    String value = valueMap.get("value");
                    if(StringUtils.isBlank(value))
                    {
                        commonBattIds.add(item);
                    }
                    else if(outSourceBattTypes.contains(value))
                    {
                        outResourceBattIds.add(item);
                    }
                    else
                    {
                        selfBattIds.add(item);
                    }
                }
            }
        });

        saveToMap(BattRiskRuleApplicationTypeEnum.LITHIUM_COMMON_RISK_RULE.getId(), commonBattIds, battRiskTypeMap);
        saveToMap(BattRiskRuleApplicationTypeEnum.LITHIUM_SELF_RISK_RULE.getId(), selfBattIds, battRiskTypeMap);
        saveToMap(BattRiskRuleApplicationTypeEnum.LITHIUM_OUT_RISK_RULE.getId(), outResourceBattIds, battRiskTypeMap);
    }

    private void saveToMap(String riskTypeId, List<String> battIds, Map<String, List<String>> battRiskTypeMap)
    {
        if(CollectionUtils.isNotEmpty(battIds))
        {
            battRiskTypeMap.put(riskTypeId, battIds);
        }
    }
}
