package com.zte.uedm.battery.bean;

import java.util.Map;

import com.zte.uedm.battery.enums.FormulaSourceEnum;
import com.zte.uedm.common.configuration.point.bean.DataType;
import com.zte.uedm.common.configuration.point.bean.PointType;

import lombok.Data;

@Data
public class StandardPointViewBean
{
    /**
     * id 测点id
     */
    private String id;

    /**
     * pointType 测点类型（AI\DI\AO\DO）
     */
    private PointType pointType;

    /**
     * dataType 数据类型
     */
    private DataType dataType;

    /**
     * name 测点名
     */
    private String name;

    /**
     * unit 单位
     */
    private String unit;

    /**
     * scale 小数位数，数值型才有，为0表示整数
     */
    private Integer scale;

    /**
     * precision 精度
     */
    private Integer precision;

    /**
     * len 长度
     */
    private Integer len;

    /**
     * function 映射函数
     */
    private String function;

    /**
     * funcDesc 映射函数描述
     */
    private String funcDesc;

    /**
     * parameter 参数
     */
    private String parameter;

    /**
     * customPointThreshold 阈值
     */
    private CustomPointThresholdViewBean customPointThreshold;

    /**
     * valueDefineI18n 值定义国际化
     */
    private Map<Integer, Map<String, String>> valueDefineI18n;

    /**
     * desc 标准测点描述
     */
    private String desc;

    /**
     * tag 测点标签，业务分类用
     */
    private String tag;

    /**
     * expandTimes 测点拓展次数
     */
    private Integer expandTimes;

    /**
     * moc 测点moc
     */
    private String moc;

    /**
     * isVisual 测点是否可见
     */
    private Boolean isVisual;

    /**
     * counterId 计数器Id
     */
    private String counterId;

    /**
     * protocolType,(just deal shoonis protocolType)
     */
    private String protocolType;

    /**
     * dataSource 数据来源
     */
    private String dataSource;

    /**
     * 数据源为对象的映射函数
     */
    private String functionObject;

    /**
     * 数据源为对象参数
     */
    private String parameterObject;

    /**
     * 公式来源模板级还是实例级，可选值：Custom, Template
     */
    private String mappingSource = FormulaSourceEnum.TEMPLATE.getId();

    /**
     * 自定义测点还是内置
     */
    private String sourceType;
}

