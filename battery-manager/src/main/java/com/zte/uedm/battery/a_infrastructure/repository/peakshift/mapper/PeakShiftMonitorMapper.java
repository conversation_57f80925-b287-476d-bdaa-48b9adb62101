package com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper;

import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.DeviceAccumulatedKwhGainPo;
import com.zte.uedm.battery.bean.DeviceAccumulatedKwhGainBo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PeakShiftMonitorMapper {

    /**
     * 查询一段时间内的累计充放电和收益情况，total版
     *
     * @param beginTime
     * @param endTime
     * @param deviceId
     * @return
     */
    List<DeviceAccumulatedKwhGainPo> getDeviceAccumulatedKwhGain(String beginTime, String endTime,
                                                                 List<String> deviceId);

    /**
     * 查询一段时间内的累计充放电和收益情况,错峰时段粒度版
     *
     * @param beginTime
     * @param endTime
     * @param deviceId
     * @return
     */
    List<DeviceAccumulatedKwhGainPo> getDeviceStrategyTypeKwhGain(String beginTime, String endTime,
                                                                  List<String> deviceId);

    /**
     * 查询一段时间内天粒度的充放电和收益情况,total版
     *
     * @param deviceId 设备ID list
     * @return
     */
    List<DeviceAccumulatedKwhGainPo> getDeviceDailyKwhGain(String beginTime, String endTime,
                                                           List<String> deviceId);

    /**
     * 查询一段时间内的每天的充放电和收益情况,错峰时段粒度版
     *
     * @param deviceId 设备ID list
     * @return
     */
    List<DeviceAccumulatedKwhGainPo> getDeviceDailyStrategyTypeKwhGain(String beginTime, String endTime,
                                                                       List<String> deviceId);
}
