package com.zte.uedm.battery.service;

import java.util.List;

import com.zte.uedm.battery.bean.BattSupplyPowerBean;
import com.zte.uedm.common.exception.UedmException;

/**
 * 获取备电列表
 * <AUTHOR>
 */
public interface SpStandbyListService
{
    /**
     *
     * @param lgOids
     * @return
     * @throws UedmException
     */
    BattSupplyPowerBean getBatterySupplyPower(List<String> lgOids) throws UedmException;
    
}
