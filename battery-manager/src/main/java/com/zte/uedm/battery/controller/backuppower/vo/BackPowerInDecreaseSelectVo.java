package com.zte.uedm.battery.controller.backuppower.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BackPowerInDecreaseSelectVo
{
    /**
     * 位置
     * */
    @ApiModelProperty("位置")
    private String position;
    /**
     * 总数
     * */
    @ApiModelProperty("总数")
    private int total;
    /**
     * 正常数量
     * */
    @ApiModelProperty("正常数量")
    private int normal;
    /**
     * 不足数量
     * */
    @ApiModelProperty("不足数量")
    private int deficiency;
    /**
     * 无法评估数量
     * */
    @ApiModelProperty("无法评估数量")
    private int unEvaluate;
}
