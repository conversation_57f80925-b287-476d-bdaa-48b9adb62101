package com.zte.uedm.battery.service.impl;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.mapper.PriceStrategyMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/7/18 下午3:23
 */
@Service
@Slf4j
public class SynPriceTableServiceImpl
{
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Autowired
    private PriceStrategyMapper priceStrategyMapper;

    private static final Integer SINGLE_OPERATION_NUM = 1000;

    private static final String PRICE_STRATEGY_ID = "default_data";

    @Transactional(rollbackFor = UedmException.class)
    public void synPriceTableData() throws UedmException
    {
        try
        {
            // 通过调用rpc查询价格策略详情是否含有默认的那一条数据，判断是否同步电池管理的数据到工程配置
            boolean flag = configurationManagerRpc.selectPriceStrategyDetail(PRICE_STRATEGY_ID);
            log.info("SynPriceTableServiceImpl synPriceTableData flag is {}", flag);
            if (!flag)
            {
                // 调用Rpc查询电池管理的价格策略数据
                synPriceStrategy();
                // 调用Rpc查询电池管理的价格策略详情数据
                synPriceStrategyDetail();
                // 调用Rpc查询电池管理的范围策略数据
                synScopeStrategy();
                // 调用Rpc查询电池管理的季节策略数据
                synSeasonStrategy();
                // 调用Rpc查询电池管理的时段策略数据
                synIntervalStrategy();
                // 调用Rpc查询电池管理的时段策略数据
                synIntervalStrategyDetail();
                // 删除价格策略详情里面的默认数据
                configurationManagerRpc.deletePriceStrategyDetailDefaultData(PRICE_STRATEGY_ID);
                log.info("synchronize battery-manager price-table to configuration success");
            }
            else
            {
                log.info("synchronize battery-manager price-table to configuration is unnecessary");
            }
        }
        catch (Exception e)
        {
            log.error("SynPriceTableData:synPriceTableData occur exception", e);
            throw new UedmException(-1, "SynPriceTableData:synPriceTableData occur exception");
        }
    }

    /**
     *调用Rpc查询电池管理的时段策略详情数据,保存到工程配置
     */
    private void synIntervalStrategyDetail() throws UedmException
    {
        List<IntervalStrategyDetailBean> intervalStrategyDetailList = priceStrategyMapper.selectIntervalStrategyDetailAll();
        log.debug("SynPriceTableData synIntervalStrategyDetail intervalStrategyDetailList size is {}", intervalStrategyDetailList.size());
        if (CollectionUtils.isNotEmpty(intervalStrategyDetailList))
        {
            // 分为1000一批插入数据库
            List<List<IntervalStrategyDetailBean>> partition = Lists.partition(intervalStrategyDetailList, SINGLE_OPERATION_NUM);
            for (List<IntervalStrategyDetailBean> intervalStrategyDetailBeans : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.INTERVAL_STRATEGY_DETAIL_SYN, intervalStrategyDetailBeans);
            }
        }
    }

    /**
     *调用Rpc查询电池管理的时段策略数据,保存到工程配置
     */
    private void synIntervalStrategy() throws UedmException
    {
        // 获取电池管理时段策略数据
        List<IntervalStrategyBean> intervalStrategyBeanList = priceStrategyMapper.selectIntervalStrategyAll();
        log.debug("SynPriceTableData synIntervalStrategy intervalStrategyBeanList size is {}", intervalStrategyBeanList.size());
        if (CollectionUtils.isNotEmpty(intervalStrategyBeanList))
        {
            // 分为1000一批插入数据库
            List<List<IntervalStrategyBean>> partition = Lists.partition(intervalStrategyBeanList, SINGLE_OPERATION_NUM);
            for (List<IntervalStrategyBean> beanList : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.INTERVAL_STRATEGY_SYN, beanList);
            }
        }
    }

    /**
     *调用Rpc查询电池管理的季节策略数据,保存到工程配置
     */
    private void synSeasonStrategy() throws UedmException
    {
        // 获取电池管理范围策略详情数据
        List<SeasonStrategyBean> seasonStrategyList = priceStrategyMapper.selectSeasonStrategyAll();
        log.debug("SynPriceTableData synSeasonStrategy seasonStrategyList size is {}", seasonStrategyList.size());
        if (CollectionUtils.isNotEmpty(seasonStrategyList))
        {
            // 分为1000一批插入数据库
            List<List<SeasonStrategyBean>> partition = Lists.partition(seasonStrategyList, SINGLE_OPERATION_NUM);
            for (List<SeasonStrategyBean> seasonStrategyBeans : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.SEASON_STRATEGY_SYN, seasonStrategyBeans);
            }
        }
    }

    /**
     *调用Rpc查询电池管理的范围策略数据,保存到工程配置
     */
    private void synScopeStrategy() throws UedmException
    {
        // 获取电池管理范围策略数据
        List<ScopeStrategyResponseBean> scopeBeanList = priceStrategyMapper.selectScopeStrategyAll();
        log.debug("SynPriceTableData synScopeStrategy scopeBeanList size is {}", scopeBeanList.size());
        if (CollectionUtils.isNotEmpty(scopeBeanList))
        {
            // 分为1000一批插入数据库
            List<List<ScopeStrategyResponseBean>> partition = Lists.partition(scopeBeanList, SINGLE_OPERATION_NUM);
            for (List<ScopeStrategyResponseBean> scopeStrategyResponseBeans : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.SCOPE_STRATEGY_SYN, scopeStrategyResponseBeans);
            }
        }
    }

    /**
     *调用Rpc查询电池管理的价格策略详情数据,保存到工程配置
     */
    private void synPriceStrategyDetail() throws UedmException
    {
        // 获取电池管理价格策略详情数据
        List<PriceDetailBean> priceDetailBeanList = priceStrategyMapper.selectPriceStrategyDetailAll();
        log.debug("SynPriceTableData synPriceStrategyDetail priceDetailBeanList size is {}", priceDetailBeanList.size());
        if (CollectionUtils.isNotEmpty(priceDetailBeanList))
        {
            // 分为1000一批插入数据库
            List<List<PriceDetailBean>> partition = Lists.partition(priceDetailBeanList, SINGLE_OPERATION_NUM);
            for (List<PriceDetailBean> priceDetailBeans : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.PRICE_STRATEGY_DETAIL_SYN, priceDetailBeans);
            }
        }

    }

    /**
     *调用Rpc查询电池管理的价格策略数据,保存到工程配置
     */
    private void synPriceStrategy() throws UedmException
    {
        // 获取电池管理价格策略数据
        List<PriceStrategyVo> priceStrategyList = priceStrategyMapper.selectPriceStrategyAll();
        log.debug("SynPriceTableData synPriceStrategy priceStrategyList size is {}", priceStrategyList.size());
        if (CollectionUtils.isNotEmpty(priceStrategyList))
        {
            // 分为1000一批插入数据库
            List<List<PriceStrategyVo>> partition = Lists.partition(priceStrategyList, SINGLE_OPERATION_NUM);
            for (List<PriceStrategyVo> priceStrategyVos : partition)
            {
                configurationManagerRpc.synPriceTableDataToConfig(CommonConst.PRICE_STRATEGY_SYN, priceStrategyVos);
            }
        }
    }

}
