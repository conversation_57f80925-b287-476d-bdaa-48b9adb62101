package com.zte.uedm.battery.opti.domain.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @FileName : CheckSqlparamsUtil
 * @FileDesc : CheckSqlparamsUtil
 */
public class CheckSqlparamsUtil
{
    private static class SingletonInner
    {
        private static final CheckSqlparamsUtil CheckSqlparamsUtil = new CheckSqlparamsUtil();
    }

    public static CheckSqlparamsUtil getInstance()
    {
        return SingletonInner.CheckSqlparamsUtil;
    }

    public final String verify(String input)
    {
        if (null == input)
        {
            return StringUtils.EMPTY;
        }

        char[] originalChars = input.toCharArray();
        char[] chars = new char[originalChars.length];
        for (int i = 0; i < originalChars.length; i++)
        {
            chars[i] = Character.valueOf(originalChars[i]).charValue();
        }
        return new String(chars);
    }
}
