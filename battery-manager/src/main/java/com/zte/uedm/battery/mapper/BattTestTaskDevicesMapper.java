package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BattTestTaskDeviceBean;
import com.zte.uedm.battery.bean.pojo.BattTestTaskDevicesPo;
import com.zte.uedm.battery.controller.batterytesttask.bo.BattTestTaskBo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电池测试任务设备 - Mapper
 */
@Mapper
public interface BattTestTaskDevicesMapper
{

    /**
     * 批量入库任务关联设备
     * @return
     */
    Integer insert(@Param("list") List<BattTestTaskDevicesPo> list);

    /**
     * 查询任务关联的设备列表
     * @param taskId
     * @return
     */
    List<BattTestTaskDevicesPo> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 查询任务关联的设备数量
     * @param taskId
     * @return
     */
    Integer selectDevicesNumByTaskId(@Param("taskId") String taskId);

    /**
     * 查询任务关联的设备id列表
     * @param taskId
     * @return
     */
    List<String> selectIdsByTaskId(@Param("taskId") String taskId);

    /**
     * 删除任务关联设备
     * @param ids
     * @return
     */
    Integer deleteByIds(@Param("ids") List<String> ids);

    /**
     * 根据任务id删除设备
     * @param taskIds
     * @return
     */
    Integer deleteByTaskIds(@Param("taskIds") List<String> taskIds);
    /**
     * 选择任务id关联的所有设备id
     * @param taskId
     * @return
     */
    List<String> selectIdByTaskId(@Param("taskId")String taskId);

    List<String> selectIdByTaskIds(@Param("taskIds") List<String> taskIds) throws UedmException;

    /**
     * 查询测试任务设备
     * @return
     */
    List<BattTestTaskDeviceBean> selectTaskByIds(@Param("list") List<String> ids);

    /**
     * 更新任务设备信息
     * @param list
     * @return
     */
    Integer updateTestDeviceInfo(@Param("list") List<BattTestTaskDeviceBean> list);

    /**
     * 获取所有设备id列表
     * @return
     */
    List<String> selectAllDevicesIds();
    List<BattTestTaskDevicesPo> selectAll();

    /**
     * 获取存在设备id列表
     * @return
     */
    List<String> selectExistDevicesIds(@Param("list") List<String> ids);

    List<String> selectDeviceIdsByTaskIds(@Param("list") List<String> ids);

    BattTestTaskBo selectById(@Param("id") String id);
}
