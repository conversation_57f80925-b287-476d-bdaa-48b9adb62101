package com.zte.uedm.battery.bean.pojo;

import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.util.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.UUID;

/**
 * 电池测试关联数据记录
 */
@Setter
@Getter
@ToString
public class BattTestRelationDataRecordPojo
{
    private String id;

    private String loopId;

    private String recordId;

    private String data;

    private String creator;

    private Date gmtCreate;

    private String updater;

    private Date gmtModified;

    /**
     * 电池测试初始化记录数据
     * @param loopId
     * @param recordId
     * @param userName
     */
    public BattTestRelationDataRecordPojo(String loopId, String recordId, String userName, String time)
    {
        this.id = UUID.randomUUID().toString();
        this.loopId = loopId;
        this.recordId = recordId;
        this.data = "";
        this.creator = userName;
        this.gmtCreate = DateUtils.getDate(time, DateTypeConst.DATE_FORMAT_1);
    }

    public BattTestRelationDataRecordPojo() {}
}
