/* Started by AICoder, pid:9e716ja0b6c3ca31419b0a4270763e6483621283 */
package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class PeakShiftDeviceStrategyDetailBean {
    /**
     * 策略id
     */
    private String id;

    /**
     * 模板数量
     */
    private Integer templateNum;

    /**
     * 模式 0:day,1:week,2:month
     */
    private Integer mode;

    /**
     * 假期采用的模板页
     */
    private Integer holidayMode;

    /**
     * 配置模板版本
     */
    private String version;

    /**
     * 假期的日期(存放格式["01-01","02-02"])
     */
    private String holidayDateStr;

    private List<String> holidays;

    /**
     * 采用模板的Str(存放格式[{"num":1,"templateNum":2},{"num":2,"templateNum":2}])
     */
    private String datesAndTemp;

    private List<ModeTemplateDto> modeTemplateDtos;

    /**
     * 模板的详情(存放格式[{"num":1,"startTime":"10:00","endTime":"12:00","mode":0},{"num":1,"startTime":"12:00","endTime":"14:00","mode":2}])
     */
    private String templateDetailStr;

    private List<PeakShiftDeviceTemplateBean> templateBeanList;
}
/* Ended by AICoder, pid:9e716ja0b6c3ca31419b0a4270763e6483621283 */