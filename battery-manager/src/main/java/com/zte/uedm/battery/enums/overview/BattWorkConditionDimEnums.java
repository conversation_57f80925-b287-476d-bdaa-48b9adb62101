package com.zte.uedm.battery.enums.overview;


/**
 * @FileName : BattStatisticsTypesEnum
 * @FileDesc : TODO
 * <AUTHOR> wc 10263256
 * @date Date : 2022年05月16日 20:27
 * @Version : 1.0
 */

import com.zte.uedm.battery.bean.BatteryWorkConditionDimensionsDatesBean;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池的统计维度
 * 使用场景为电池总览页面
 */
public enum BattWorkConditionDimEnums {
    ALARM("alarm","{\"en_US\":\"Alarm\",\"zh_CN\":\"告警\"}" ),
    SOH("soh","{\"en_US\":\"Health\",\"zh_CN\":\"健康状态\"}" ),
    LIFE("life","{\"en_US\":\"Life\",\"zh_CN\":\"寿命\"}"),
    PRST_SOC("prstSoc","{\"en_US\":\"Battery SOC\",\"zh_CN\":\"电池剩余容量\"}"),
    RATED_CAP("ratedCap","{\"en_US\":\"Battery Rated Capacity\",\"zh_CN\":\"电池额定容量\"}"),
    RISK_LEVEL("riskLevel","{\"en_US\":\"Risk\",\"zh_CN\":\"风险\"}"),
    REMAIN_DISCHARGE_DURATION("remainDischargeDuration","{\"en_US\":\"Remain Discharge Duration\",\"zh_CN\":\"剩余放电时长\"}");


    private String id;
    private String name;

    public String getId()
    {
        return this.id;
    }
    public String getName()
    {
        return this.name;
    }

    BattWorkConditionDimEnums(String id, String name)
    {
        this.id = id;
        this.name = name;
    }

    /**
     * 获取所有ids
     * @return
     */
    public static List<String> getAllBattWorkConditionDimIds()
    {
        List<String> allIds = new ArrayList<>();

        BattWorkConditionDimEnums[] values = BattWorkConditionDimEnums.values();
        for(BattWorkConditionDimEnums value : values)
        {
            if(StringUtils.isNotBlank(value.getId()))
                allIds.add(value.getId());
        }
        return allIds;
    }

    /**
     * 根据id查询名称
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BattWorkConditionDimEnums[] values = BattWorkConditionDimEnums.values();
        for(BattWorkConditionDimEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.name;
            }
        }
        return "";
    }

}
