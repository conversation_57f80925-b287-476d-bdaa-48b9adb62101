package com.zte.uedm.battery.controller.backuppower.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BackPowerInDecreaseResponStatDto
{

    /**
     * 状态id
     * */
    private String  id;
    /**
     * 状态名称
     * */
    private String  name;
    /**
     * 总数
     * */
    private int totalNumber;
    /**
     * 增加数量
     * */
    private int inNumber;
    /**
     * 减少数量
     * */
    private int deNumber;

}
