package com.zte.uedm.battery.util;

import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.util.constant.PvConstant;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.NetPermission;
import java.net.URLEncoder;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import sun.misc.BASE64Encoder;

import static com.zte.uedm.basis.util.base.file.FileUtil.checkFile;
import static com.zte.uedm.common.util.SecurityCheckUtils.checkPerm;

/**
 * 文件下载操作类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUtils
{
    public final static String BROWSER_IE ="MSIE";
    public final static String BROWSER_FIREFOX ="FIREFOX";
    private final static String USER_AGENT = "User-Agent";
    private final static String UTF = "utf-8";
    public static void downloadFile(String path, HttpServletResponse response, HttpServletRequest request)
    {
        File file = new File(pathManipulation(path));
        File parentFile = file.getParentFile();

        String agent = request.getHeader(USER_AGENT)==null? PvConstant.BROWSER_IE:request.getHeader(USER_AGENT).toUpperCase();
        String fileName = file.getName();
        log.info("download file name:" + fileName);
        // fileName = getFileName(fileName, agent);

        log.info("download file name look:" + fileName);

        try {
            //清空reponse
            response.reset();
            //设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename*=" + URLEncoder.encode(fileName,
                    UTF));
            response.addHeader("Content-Length", "" + file.length());
            if (path.contains(GlobalConstants.FILE_ZIP_EXTENSION))
            {
                response.setContentType("application/x-zip-compressed;charset=UTF-8");
            }
            else
            {
                response.setContentType("application/octet-stream;charset=UTF-8");
            }

            //以流的形式下载文件
            try (FileInputStream fistream = new FileInputStream(pathManipulation(path));
                 InputStream bis = new BufferedInputStream(fistream);
                 OutputStream bos = new BufferedOutputStream(response.getOutputStream()))
            {

                byte[] buff = new byte[2048];
                int buffLen;
                checkParms();
                while ((buffLen = bis.read(buff, 0, buff.length)) != -1) {
                    bos.write(buff, 0, buffLen);
                }
                bos.flush();
            } catch (Exception e) {
                log.error("", e);
            } finally {
                if(parentFile!=null) {
                    delFile(parentFile);
                }
            }
        }
        catch (UnsupportedEncodingException e)
        {
            log.error("encode is error!", e);
        }

    }

    protected static void close(Closeable c) throws IOException {
        if (c != null) {
            c.close();
        }
    }

//    /**
//     * 获取文件名
//     * @param fileName
//     * @param agent
//     * @return
//     */
//    protected static String getFileName(String fileName,String agent)throws UnsupportedEncodingException{
//        if (agent.contains(ReportConstants.BROWSER_IE)) {
//            // IE浏览器
//            fileName = URLEncoder.encode(fileName, "utf-8");
//            fileName = fileName.replace("+", " ");
//        } else if (agent.contains(ReportConstants.BROWSER_FIREFOX)) {
//            // 火狐浏览器
//            BASE64Encoder base64Encoder = new BASE64Encoder();
//            fileName = "=?utf-8?B?" + base64Encoder.encode(fileName.getBytes("utf-8")) + "?=";
//        } else {
//            // 其它浏览器
//            log.info("into other search");
//            fileName = URLEncoder.encode(fileName, "utf-8");
//        }
//        return fileName ;
//    }

   public static boolean delFile(File file) {
        if (!file.exists()) {
            return false;
        }

        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    delFile(f);
                }
            }
        }
        checkParms();
        return file.delete();
    }

    public static void checkParms()
    {
        SecurityManager sm = System.getSecurityManager();
        log.info("System.getSecurityManager():{}", sm);
        if (sm != null)
        {
            sm.checkPermission(new NetPermission("setResponseCache"));
        }
    }

    /**
     * 解决coverity扫描pathManipulation 问题
     *
     * @param path
     * @return
     */
    public static String pathManipulation(String path) {
        if (path.contains("..")) {
            log.error("path is error path=" + path);
            path = "";
        }
        return path;
    }

    public static void downloadPictureFile(String path, HttpServletResponse response, HttpServletRequest request)
    {
        File file = new File(pathManipulation(path));
        File parentFile = file.getParentFile();

        try {
            String agent = request.getHeader(USER_AGENT)==null?BROWSER_IE:request.getHeader(USER_AGENT).toUpperCase();
            String fileName = file.getName();
            fileName = getFileName(fileName,agent);
            //清空reponse
            response.reset();
            //设置response的Header
            byte[] bytes = fileName.getBytes("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(bytes, "UTF-8"));
            response.addHeader("Content-Length", "" + file.length());
            response.setContentType("application/octet-stream;charset=UTF-8");

            //以流的形式下载文件
            try (FileInputStream fistream = new FileInputStream(pathManipulation(path));
                 InputStream bis = new BufferedInputStream(fistream);
                 OutputStream bos = new BufferedOutputStream(response.getOutputStream()))
            {

                byte[] buff = new byte[2048];
                int buffLen;
                SecurityCheckUtils.checkPerm();
                while ((buffLen = bis.read(buff, 0, buff.length)) != -1) {
                    bos.write(buff, 0, buffLen);
                }
                bos.flush();
            } catch (Exception e) {
                log.error("", e);
            } finally {
                if(parentFile!=null) {
                    delFile(parentFile);
                }
            }
        }
        catch (UnsupportedEncodingException e)
        {
            log.error("encode is error!", e);
        }

    }

    /**
     * 获取文件名
     * @param fileName
     * @param agent
     * @return
     */
    protected static String getFileName(String fileName,String agent)throws UnsupportedEncodingException {
        if (agent.contains(BROWSER_IE)) {
            // IE浏览器
            fileName = URLEncoder.encode(fileName, UTF);
            fileName = fileName.replace("+", " ");
        } else if (agent.contains(BROWSER_FIREFOX)) {
            // 火狐浏览器

            BASE64Encoder base64Encoder = new BASE64Encoder();
            fileName = "=?utf-8?B?" + base64Encoder.encode(fileName.getBytes(UTF)) + "?=";
        } else {
            // 其它浏览器
            log.info("into other search");
            fileName = URLEncoder.encode(fileName, UTF);
        }
        return fileName ;
    }

    /**
     * 将文件名与相应二进制字节的映射压缩为文件
     *
     * @param file
     * @param fileName
     * @param passwd
     * @return
     * @throws IOException
     */
    /* Started by AICoder, pid:22238jeab2797d614e8e0b4010a94f0ef5039a5a */
    public static File zipProtected(File file, String fileName, String passwd) throws IOException
    {
        checkPerm();
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setEncryptFiles(true);
        zipParameters.setCompressionLevel(CompressionLevel.HIGHER);
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
        try(ZipFile zipFile = new ZipFile(fileName, passwd.toCharArray());)
        {
            zipFile.addFile(file, zipParameters);
        }
        catch (Exception e)
        {
            log.error("zipFile error", e);
        }
        return new File(fileName);
    }
    /* Ended by AICoder, pid:22238jeab2797d614e8e0b4010a94f0ef5039a5a */

}
