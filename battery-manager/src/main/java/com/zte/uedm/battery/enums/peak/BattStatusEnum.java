/* Started by AICoder, pid:oa5ef12809hcf3e14bac0ab930a04e2e49d97adf */
package com.zte.uedm.battery.enums.peak;

public enum BattStatusEnum {
    /**
     * 获取电池状态
     */
    BATTFLOAT("0", "{\"zh_CN\":\"浮充\",\"en_US\":\"float\"}"),
    BATTEQUAL("1", "{\"zh_CN\":\"均充\",\"en_US\":\"equal\"}"),
    BATTTEST("2", "{\"zh_CN\":\"放电测试\",\"en_US\":\"test\"}"),
    BATTDISCHARGE("3", "{\"zh_CN\":\"放电\",\"en_US\":\"discharge\"}"),
    BATTCHARGE("6", "{\"zh_CN\":\"充电\",\"en_US\":\"charge\"}"),
    BATTSTANDBY("7", "{\"zh_CN\":\"待机\",\"en_US\":\"standby\"}");

    private String id;
    private String name;

    BattStatusEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getId() {
        return this.id;
    }
}
/* Ended by AICoder, pid:oa5ef12809hcf3e14bac0ab930a04e2e49d97adf */