package com.zte.uedm.battery.opti.domain.aggregate.model;

import com.zte.uedm.battery.opti.domain.aggregate.model.vobj.CalculationTimesDetail;
import com.zte.uedm.battery.opti.domain.utils.function.bean.FuncParameterBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 电池风险规则
 */
/* Started by AICoder, pid:1ab2336ab6dc4507832125d37c122640 */
@Getter
@Setter
@ToString
public class BattRiskRuleEntity
{
    /**
     * 风险id
     */
    private String id;
    /**
     * 电池类型
     */
    private String battType;
    /**
     * 应用范围
     */
    private String applicationScope;
    /**
     * 风险原因（中英文）
     */
    private String name;
    /**
     * 风险等级id
     */
    private String level;
    /**
     * 风险规则
     */
    private String rule;
    /**
     * 风险原因（中英文）
     */
    private String cause;
    /**
     * 风险建议（中英文）
     */
    private String suggestion;
    /**
     * 内置
     */
    private Boolean internal;
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 计算类型
     */
    private Integer calculationType;
    /**
     * 公式类型
     */
    private String functionType;
    /**
     * 实时计算内容
     */
    private String realTimeCalFunc;
    /**
     * 实时计算参数
     */
    private List<FuncParameterBean> realTimeCalPara;

    /**
     * 计算时间范围
     */
    private Integer timeRange;
    /**
     * 计算次数
     */
    private CalculationTimesDetail timeRangeCalTimes;

    /**
     * 计算内容
     */
    private String timeRangeCalFunc;
    /**
     * 计算参数
     */
    private List<FuncParameterBean> timeRangeCalPara;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date gmtModified;
    /**
     * 隐藏判断
     */
    private boolean display;
}
/* Ended by AICoder, pid:1ab2336ab6dc4507832125d37c122640 */