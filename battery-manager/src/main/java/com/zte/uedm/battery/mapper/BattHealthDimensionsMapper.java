package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BatteryHealthDimensionsBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileDesc : 健康列表维度
 * <AUTHOR> 00253634
 * @date Date : 2023年03月01日 上午10:52
 * @Version : 1.0
 */
@Mapper
public interface BattHealthDimensionsMapper
{
    List<BatteryHealthDimensionsBean> selectHealthListConfig(String userName) throws UedmException;

    void insertBatch(List<BatteryHealthDimensionsBean> list) throws UedmException;

    int updateBatch(List<BatteryHealthDimensionsBean> updateDBList) throws UedmException;

    List<BatteryHealthDimensionsBean> searchHealthConfig(@Param("name") String name, @Param("userName") String userName) throws UedmException;

    int deleteByName(String userName) throws UedmException;
}
