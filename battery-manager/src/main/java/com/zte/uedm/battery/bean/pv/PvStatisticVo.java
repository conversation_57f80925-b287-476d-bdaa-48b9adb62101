package com.zte.uedm.battery.bean.pv;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PvStatisticVo {

    private String pvId;

    private String lowPowerSupply = "0.00";

    /**
     * 谷时段太阳发电总收益
     */
    private String lowSolarRevenue = "0.00";

    /**
     * 谷时段市电总电费
     */
    private String lowGridFee = "0.00";

    private String normalPowerSupply = "0.00";

    /**
     * 谷时段太阳发电总收益
     */
    private String normalSolarRevenue = "0.00";

    /**
     * 谷时段市电总电费
     */
    private String normalGridFee = "0.00";

    private String peakPowerSupply = "0.00";

    /**
     * 谷时段太阳发电总收益
     */
    private String peakSolarRevenue = "0.00";

    /**
     * 谷时段市电总电费
     */
    private String peakGridFee = "0.00";

    private String peakestPowerSupply = "0.00";

    /**
     * 谷时段太阳发电总收益
     */
    private String peakestSolarRevenue = "0.00";

    /**
     * 谷时段市电总电费
     */
    private String peakestGridFee = "0.00";


    /**
     * 总电量
     */
    private String totalEnergyGeneration = "0.00";

    /**
     * 太阳发电总收益
     */
    private String totalSolarRevenue = "0.00";

    /**
     * 市电总电费
     */
    private String totalGridFee = "0.00";

}
