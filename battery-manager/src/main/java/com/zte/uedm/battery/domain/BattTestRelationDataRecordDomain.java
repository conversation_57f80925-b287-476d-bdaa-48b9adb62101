package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPojo;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BattTestRelationDataRecordDomain
{
    /**
     * 根据记录id查询关联记录详情
     * @param recordIds
     * @return
     * @throws UedmException
     */
    List<BattTestRelationDataRecordPojo> selectDataRecords(List<String> recordIds) throws UedmException;

    /**
     * 更新电池关联测试记录详情
     * @param relationDataRecordPos
     * @return
     * @throws UedmException
     */
    Integer updateDataRecords(List<BattTestRelationDataRecordPojo> relationDataRecordPos) throws UedmException;
}
