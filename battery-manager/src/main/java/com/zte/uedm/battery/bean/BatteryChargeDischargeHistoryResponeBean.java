package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
public class BatteryChargeDischargeHistoryResponeBean {

    /**
     * 采集器id
     */
    private String collectorId;

    /**
     * 标志id
     */
    private String logId;

    /**
     * 文件获取状态
     */
    private String status;

    /**
     * 文件获取失败原因
     */
    private String failureCause;

    /**
     * 解析后的文件内容。
     */
    private List<BatteryChargeDischargeHistorySouthBean> fileData;

    /**
     * 功能响应参数
     */
    private Map<String, Object> functionRspParameter;
}
