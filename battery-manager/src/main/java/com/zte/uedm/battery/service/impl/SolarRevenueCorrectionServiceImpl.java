package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.zte.log.filter.UserThreadLocal;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.bean.ScopeStrategyResponseBean;
import com.zte.uedm.battery.bean.SolarRevenueCorrectRecordDetailCommand;
import com.zte.uedm.battery.bean.SolarRevenueCorrectTaskCommand;
import com.zte.uedm.battery.domain.impl.PriceStrategyDomainImpl;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.opti.domain.service.AuthorizationService;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectRecordDetailBO;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectRecordDetailVO;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectionTaskPO;
import com.zte.uedm.battery.pv.dto.SolarCorrectTaskQueryDto;
import com.zte.uedm.battery.pv.enums.SolarRevenueCorrectionTaskStatusEnum;
import com.zte.uedm.battery.pv.mapper.SolarRevenueCorrectionTaskDayDetailMapper;
import com.zte.uedm.battery.pv.mapper.SolarRevenueCorrectionTaskMapper;
import com.zte.uedm.battery.pv.service.SolarRevenueStatisticsService;
import com.zte.uedm.battery.pv.service.impl.SolarRevenueStatisticsServiceImpl;
import com.zte.uedm.battery.service.SolarRevenueCorrectionService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.pv.consts.SolarRevenueConst.*;
import static com.zte.uedm.battery.pv.enums.SolarRevenueCorrectionTaskStatusEnum.TO_BE_EXECUTED;
import static com.zte.uedm.battery.util.constant.PvConstant.*;

/**
 * 太阳能收益矫正
 *
 * <AUTHOR>
 * @version 1.0, 2023/11/22 11:24
 */
@Service
@Slf4j
public class SolarRevenueCorrectionServiceImpl implements SolarRevenueCorrectionService {

    @Autowired
    private ConfigService configService;

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private SolarRevenueCorrectionTaskMapper solarRevenueCorrectionTaskMapper;

    @Autowired
    private SolarRevenueCorrectionTaskDayDetailMapper solarRevenueCorrectionTaskDayDetailMapper;

    @Autowired
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;

    @Autowired
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Autowired
    private SolarRevenueStatisticsService solarRevenueStatisticsService;

    private ExecutorService solarRevenueCorrectionThreadPool;

    @Autowired
    private SolarRevenueStatisticsServiceImpl solarRevenueStatisticsServiceImpl;

    @Autowired
    private LogUtils logUtils;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private PriceStrategyDomainImpl priceStrategyDomain;

    @Autowired
    GridStrategyMapper gridStrategyMapper;

    @Autowired
    private FieldCacheManager fieldCacheManager;

    @Autowired
    private AuthorizationService authorizationService;



    @PostConstruct
    public void init() {
        if (solarRevenueCorrectionThreadPool != null) {
            return;
        }
        // 如果出现线程任务被拒绝，则等待30s后，再次创建并提交到线程池中
        solarRevenueCorrectionThreadPool = new ThreadPoolExecutor(20, 100, 0L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
            new ThreadFactoryBuilder().setNameFormat("SolarRevenueCorrectionCompletableProcessor-%d").build(), this::rejectedExecution);
    }

    private void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        executor.execute(r);
    }

    @Override
    public String solarRevenueCorrectTaskCreate(SolarRevenueCorrectTaskCommand param, String languageOption)
            throws UedmException {
        //分域
        List<String> fullPermissionByResIds = authorizationService.getFullPermissionByResIds(param.getSiteIdList());
        if (CollectionUtils.isEmpty(fullPermissionByResIds)) {
            log.error("No access permission");
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }
        param.setSiteIdList(fullPermissionByResIds);
        Integer siteNum = fullPermissionByResIds.size();
        //1. 矫正任务校验
        log.info("begin verify task!!!");
        verifyTask(siteNum, languageOption);
        log.info("finished verify task!!!");
        //2. 电价策略校验
        log.info("begin verify strategy!!!");
        priceStrategyDomain.verifyPriceStrategy(param.getSolarScopeStrategyId(), param.getGridScopeStrategyId(),
            param.getMonth(), param.getConfirmCreate(), languageOption);
        log.info("finished verify strategy!!!");
        //3. 创建任务
        if (param.getConfirmCreate()) {
            log.info("verify task and strategy success, now start create task!!!");
            String result = createSolarRevenueCorrectTask(param);
            //发送日志
            log.info("create task success, now start send log!!!");
            logUtils.sendCreateCorrectionTaskLog(true, dateTimeService.getCurrentTime(), siteNum.toString(),
                param.getStartTimeOfMonth() + WAVY_LINE_EN + param.getLastTimeOfMonth(), param.getGridScopeStrategyName(),
                param.getSolarScopeStrategyName());
            //调用任务执行方法
            log.info("send log success, now start call exeCorrectiveTasks!!!");
            CompletableFuture.runAsync(() -> solarRevenueStatisticsService.exeCorrectiveTasks());
            return result;
        }
        return null;
    }

    @Override
    public List<SolarRevenueCorrectRecordDetailVO> getSolarRevenueCorrectRecordDetail(
        SolarRevenueCorrectRecordDetailCommand param)
        throws UedmException {
        log.info("begin get all sites by taskId {}",param.getId());
        Map<String ,SolarRevenueCorrectRecordDetailVO> sitesBaseInfo = getAllSiteBaseInfoByTaskId(param.getId());
        List<SolarRevenueCorrectRecordDetailVO> solarRevenueCorrectRecordDetailVOs = new ArrayList<>();
        //根据任务ID获取所有数据
        List<SolarRevenueCorrectRecordDetailBO> solarRevenueCorrectRecordDetailBOs =
                null;
        try {
            solarRevenueCorrectRecordDetailBOs = solarRevenueCorrectionTaskDayDetailMapper.getSolarRevenueCorrectRecordDetailByTaskId(param.getId());
        } catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [getSolarRevenueCorrectRecordDetail] occur error message = {}", e.getMessage(), e);
            DatabaseExceptionEnum operatedb = DatabaseExceptionEnum.OPERATEDB;
            throw new UedmException(operatedb.getCode(), operatedb.getDesc());
        }
        log.info("solar revenue correct record detail data:{}", solarRevenueCorrectRecordDetailBOs);
        if (CollectionUtils.isEmpty(solarRevenueCorrectRecordDetailBOs)) {
            return sitesBaseInfo.values().stream().sorted(Comparator.comparing(SolarRevenueCorrectRecordDetailVO::getSiteName)).collect(
                    Collectors.toList());
        }
        //根据pvId找到所属站点,把查到的数据按站点分组
        //该map  key为站点  value为该站点下的所有的太阳能收益日表数据
        Map<String, List<SolarRevenueCorrectRecordDetailBO>> solarRevenueCorrectRecordDetailMap =
            groutSolarRevenueCorrectionTaskDayDetailBySite(solarRevenueCorrectRecordDetailBOs);
        log.info("solar revenue correct record detail map:{}", solarRevenueCorrectRecordDetailMap);
        if (MapUtils.isNotEmpty(solarRevenueCorrectRecordDetailMap)) {
            //统计每个分组內数据
            groupStatisticsSolarRevenueCorrectRecordDetail(solarRevenueCorrectRecordDetailVOs, solarRevenueCorrectRecordDetailMap);
        }

        //对于没有详情的数据，需要把站点基本信息塞进去
        List<String> sites = solarRevenueCorrectRecordDetailVOs.stream().map(SolarRevenueCorrectRecordDetailVO::getSiteId).collect(Collectors.toList());
        List<SolarRevenueCorrectRecordDetailVO> noDataSitesBaseInfo = sitesBaseInfo.keySet().stream().filter(site -> !sites.contains(site)).map(
                sitesBaseInfo::get).collect(Collectors.toList());
        solarRevenueCorrectRecordDetailVOs.addAll(noDataSitesBaseInfo);

        solarRevenueCorrectRecordDetailVOs = solarRevenueCorrectRecordDetailVOs.stream()
            .sorted(Comparator.comparing(SolarRevenueCorrectRecordDetailVO::getSiteName)).collect(
                Collectors.toList());

        return solarRevenueCorrectRecordDetailVOs;
    }

    public Map<String ,SolarRevenueCorrectRecordDetailVO> getAllSiteBaseInfoByTaskId(String taskId) throws UedmException {
        Map<String ,SolarRevenueCorrectRecordDetailVO> solarRevenueCorrectRecordDetailVOs = new HashMap<>();
        //为空取任务列表中的有效站点 通过站点id,在缓存中找到站点的名称和位置
        SolarCorrectTaskQueryDto solarCorrectTaskQueryDto = new SolarCorrectTaskQueryDto();
        solarCorrectTaskQueryDto.setId(taskId);
        /* Started by AICoder, pid:df4fem6b15z4ed4143270b48c001f7044478ca4d */
        List<SolarRevenueCorrectionTaskPO> revenueCorrectionTaskPOS = null;
        try {
            revenueCorrectionTaskPOS = solarRevenueCorrectionTaskMapper.selectSolarRevenueCorrectionTaskByPage(
                    solarCorrectTaskQueryDto);
        }catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [getAllSiteBaseInfoByTaskId] occur error message = {}", e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
        /* Ended by AICoder, pid:df4fem6b15z4ed4143270b48c001f7044478ca4d */
        //在矫正任务站点列表不为空的情况下,在缓存根据站点id,找到站点名称和位置,组装成详情bean返回给前端
        if (CollectionUtils.isNotEmpty(revenueCorrectionTaskPOS)){
//            log.info("taskId {} has record: {}",taskId,revenueCorrectionTaskPOS);
            SolarRevenueCorrectionTaskPO solarRevenueCorrectionTaskPO = revenueCorrectionTaskPOS.get(0);
            String[] sites = new String[]{};
            if (solarRevenueCorrectionTaskPO != null){
                sites = solarRevenueCorrectionTaskPO.getSites();
            }

             Map<String, FieldEntity> siteBeanMap = realGroupRelationSiteUtils.getAllSiteDsBeanMap();
            solarRevenueCorrectRecordDetailVOs = Arrays.stream(sites).parallel().map(siteId ->{
                FieldEntity siteBean = siteBeanMap.get(siteId);
//                log.info("siteId {} detail info: {}",siteId,siteBean);
                if (siteBean == null){
                    return null;
                }
                SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO = new SolarRevenueCorrectRecordDetailVO();
                solarRevenueCorrectRecordDetailVO.setSiteId(siteId);
                solarRevenueCorrectRecordDetailVO.setPosition(siteBean.getPathName());
                solarRevenueCorrectRecordDetailVO.setSiteName(siteBean.getName());
                return solarRevenueCorrectRecordDetailVO;
            }).filter(Objects::nonNull).collect(Collectors.toMap(SolarRevenueCorrectRecordDetailVO::getSiteId, Function.identity(),(v1,v2)->v1));
        }
        return solarRevenueCorrectRecordDetailVOs;
    }

    private void groupStatisticsSolarRevenueCorrectRecordDetail(
        List<SolarRevenueCorrectRecordDetailVO> solarRevenueCorrectRecordDetailVOs,
        Map<String, List<SolarRevenueCorrectRecordDetailBO>> solarRevenueCorrectRecordDetailMap) {
        Map<String, FieldEntity> fieldMap = fieldCacheManager.getFieldMapBeans();
        for (String siteId : solarRevenueCorrectRecordDetailMap.keySet()) {
            SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO = new SolarRevenueCorrectRecordDetailVO();
            List<SolarRevenueCorrectRecordDetailBO> list = solarRevenueCorrectRecordDetailMap.get(
                siteId);
            List<SolarRevenueCorrectRecordDetailBO> oldList =list.stream().filter(solarRevenueCorrectRecordDetailBO -> DATE_SOURCE_OLD.equals(solarRevenueCorrectRecordDetailBO.getSource())).collect(Collectors.toList());
            List<SolarRevenueCorrectRecordDetailBO> newList =list.stream().filter(solarRevenueCorrectRecordDetailBO -> DATE_SOURCE_NEW.equals(solarRevenueCorrectRecordDetailBO.getSource())).collect(Collectors.toList());
            setSolarRevenue(solarRevenueCorrectRecordDetailVO,oldList,DATE_SOURCE_OLD);
            setSolarRevenue(solarRevenueCorrectRecordDetailVO,newList,DATE_SOURCE_NEW);

            //根据站点id获取站点信息
            FieldEntity fieldEntity = fieldMap.get(siteId);
            solarRevenueCorrectRecordDetailVO.setSiteId(siteId);
            if (null != fieldEntity) {
                solarRevenueCorrectRecordDetailVO.setPosition(fieldEntity.getPathName());
                solarRevenueCorrectRecordDetailVO.setSiteName(fieldEntity.getName());
            }
            solarRevenueCorrectRecordDetailVOs.add(solarRevenueCorrectRecordDetailVO);
        }
    }

    private void setSolarRevenue(SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO,List<SolarRevenueCorrectRecordDetailBO> detailList,String source){
        BigDecimal solarRevenueTotal = new BigDecimal(0);
        BigDecimal gridRevenueTotal = new BigDecimal(0);
        BigDecimal saveElectricChargeTotal = new BigDecimal(0);

        int solarRevenueNullCount = 0;
        int gridRevenueNullCount = 0;
        int saveElectricChargeNullCount = 0;


        for (SolarRevenueCorrectRecordDetailBO solarRevenueCorrectRecordDetailBO : detailList) {
            if (StringUtils.isBlank(solarRevenueCorrectRecordDetailBO.getTotalSolarRevenue())){
                solarRevenueNullCount++;
            }
            if (StringUtils.isBlank(solarRevenueCorrectRecordDetailBO.getTotalGridFee())) {
                gridRevenueNullCount++;
            }
            if (StringUtils.isBlank(solarRevenueCorrectRecordDetailBO.getTotalSavings())){
                saveElectricChargeNullCount++;
            }

            BigDecimal solarRevenue = strToBigDecimal(solarRevenueCorrectRecordDetailBO.getTotalSolarRevenue());
            solarRevenueTotal = solarRevenueTotal.add(solarRevenue);

            BigDecimal gridRevenue = strToBigDecimal(solarRevenueCorrectRecordDetailBO.getTotalGridFee());
            gridRevenueTotal = gridRevenueTotal.add(gridRevenue);

            BigDecimal saveElectricCharge = strToBigDecimal(solarRevenueCorrectRecordDetailBO.getTotalSavings());
            saveElectricChargeTotal = saveElectricChargeTotal.add(saveElectricCharge);

        }
        String solarRevenueResult = parsedRevenueResult(detailList.size(),solarRevenueNullCount,solarRevenueTotal);
        String gridRevenueResult = parsedRevenueResult(detailList.size(),gridRevenueNullCount,gridRevenueTotal);
        String saveElectricChargeResult = parsedRevenueResult(detailList.size(),saveElectricChargeNullCount,saveElectricChargeTotal);

        if (DATE_SOURCE_OLD.equals(source)) {
            solarRevenueCorrectRecordDetailVO.setSolarRevenueOld(solarRevenueResult);
            solarRevenueCorrectRecordDetailVO.setGridRevenueOld(gridRevenueResult);
            solarRevenueCorrectRecordDetailVO.setSaveElectricChargeOld(saveElectricChargeResult);
        }else if (DATE_SOURCE_NEW.equals(source)) {
            solarRevenueCorrectRecordDetailVO.setSolarRevenueNew(solarRevenueResult);
            solarRevenueCorrectRecordDetailVO.setGridRevenueNew(gridRevenueResult);
            solarRevenueCorrectRecordDetailVO.setSaveElectricChargeNew(saveElectricChargeResult);
        }
    }



    private String parsedRevenueResult(int detailListSize, int revenueNullCount, BigDecimal revenueTotal){
        return  revenueNullCount == detailListSize && revenueTotal.intValue() == 0 ?
                null : revenueTotal.setScale(2, RoundingMode.HALF_UP).toString();
    }

    private BigDecimal strToBigDecimal(String revenue) {
        if (StringUtils.isBlank(revenue)) {
            return new BigDecimal(0);
        }
        return new BigDecimal(revenue);
    }

    private Map<String, List<SolarRevenueCorrectRecordDetailBO>> groutSolarRevenueCorrectionTaskDayDetailBySite(
        List<SolarRevenueCorrectRecordDetailBO> solarRevenueCorrectRecordDetailBOs)
        throws UedmException {
        Map<String, String> allPvToSite = allPvToSite();
        if (MapUtils.isEmpty(allPvToSite)) {
            return new HashMap<>();
        }
        solarRevenueCorrectRecordDetailBOs.forEach(solarRevenueCorrectRecordDetailBO -> {
            String pvId = solarRevenueCorrectRecordDetailBO.getPvId();
            if (StringUtils.isNotBlank(pvId) && StringUtils.isNotEmpty(allPvToSite.get(pvId))) {
                solarRevenueCorrectRecordDetailBO.setSiteId(allPvToSite.get(pvId));
            }
        });
        return solarRevenueCorrectRecordDetailBOs
            .stream().collect(Collectors.groupingBy(SolarRevenueCorrectRecordDetailBO::getSiteId));
    }

    private Map<String, String> allPvToSite() throws UedmException {
        Map<String, String> allPvToSite = new HashMap<>();
       // Map<String, List<MonitorObjectDsBean>> siteRelatedPvMap = realGroupRelationSiteUtils.getsiteRelatedPvMap();
        Map<String, List<DeviceDSEntity>>  siteRelatedPvMap=  realGroupRelationSiteUtils.getsiteRelatedPvMap_new();
        for (String siteId : siteRelatedPvMap.keySet()) {
            List<DeviceDSEntity> siteRelatedPvMapOrDefault = siteRelatedPvMap.getOrDefault(siteId, new ArrayList<>());
            List<String> pvIds = siteRelatedPvMapOrDefault.stream().map(DeviceEntity::getId)
                .collect(Collectors.toList());
            for (String pvId : pvIds) {
                allPvToSite.put(pvId, siteId);
            }
        }
        return allPvToSite;
    }


    public String createSolarRevenueCorrectTask(SolarRevenueCorrectTaskCommand param) throws UedmException {
        SolarRevenueCorrectionTaskPO solarRevenueCorrectionTaskPO = new SolarRevenueCorrectionTaskPO();
        solarRevenueCorrectionTaskPO.setId(UUID.randomUUID().toString());
        solarRevenueCorrectionTaskPO.setCorrectionStartDate(param.getStartTimeOfMonth());
        solarRevenueCorrectionTaskPO.setCorrectionEndDate(param.getLastTimeOfMonth());
        solarRevenueCorrectionTaskPO.setSiteCount(param.getSiteIdList().size());
        solarRevenueCorrectionTaskPO.setSites(param.getSiteIdList().toArray(new String[0]));
        solarRevenueCorrectionTaskPO.setGmtCreate(new Date());
        solarRevenueCorrectionTaskPO.setGmtModified(new Date());
        solarRevenueCorrectionTaskPO.setTaskStatus(TO_BE_EXECUTED.getCode());
        String userName = UserThreadLocal.getuserName();
        solarRevenueCorrectionTaskPO.setOperator(userName);
        solarRevenueCorrectionTaskPO.setTaskExecTimes(0);
        solarRevenueCorrectionTaskPO.setSolarScopeStrategyId(param.getSolarScopeStrategyId());
        solarRevenueCorrectionTaskPO.setSolarScopeStrategyName(param.getSolarScopeStrategyName());
        solarRevenueCorrectionTaskPO.setGridScopeStrategyId(param.getGridScopeStrategyId());
        solarRevenueCorrectionTaskPO.setGridScopeStrategyName(param.getGridScopeStrategyName());
        log.info("solarRevenueCorrectionTaskPO:{}", solarRevenueCorrectionTaskPO);
        /* Started by AICoder, pid:b2586ef5b9329f81401a08112088c50e7ea71459 */
        try {
            return solarRevenueCorrectionTaskMapper.insert(solarRevenueCorrectionTaskPO).toString();
        } catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [createSolarRevenueCorrectTask] occur error message = {}", e.getMessage(), e);
            DatabaseExceptionEnum operatedb = DatabaseExceptionEnum.OPERATEDB;
            throw new UedmException(operatedb.getCode(), operatedb.getDesc());
        }
        /* Ended by AICoder, pid:b2586ef5b9329f81401a08112088c50e7ea71459 */
    }

    public void verifyTask(Integer numberOfSites, String languageOption) throws UedmException {
        //分组站点数是否超上限
        String numberOfSingleCorrectionSitesLimit = getCfgCenterDataByKey(NUMBER_OF_SINGLE_CORRECTION_SITES_KEY);
        verifyConfiguration(numberOfSites, numberOfSingleCorrectionSitesLimit, NUMBER_OF_SINGLE_CORRECTION_SITES_OVERSIZE,
                languageOption);

        // 当天总站点数是否超上限
        String numberOfSingleCorrectionSitesEveryDayLimit = getCfgCenterDataByKey(
                NUMBER_OF_SINGLE_CORRECTION_SITES_EVERY_DAY_KEY);
        /* Started by AICoder, pid:y14e7d1d66b3bae1405d0804c033831d2bb52911 */
        int numberOfConcurrentTasks = 0;
        String numberOfConcurrentTasksLimit = null;
        try {
            int numberOfSitesToday = solarRevenueCorrectionTaskMapper.getNumberOfSitesToday();
            verifyConfiguration(numberOfSitesToday + numberOfSites, numberOfSingleCorrectionSitesEveryDayLimit,
                    NUMBER_OF_SINGLE_CORRECTION_SITES_EVERY_ADY_OVERSIZE, languageOption);
            // 并发的待执行任务数是否超上限
            numberOfConcurrentTasksLimit = getCfgCenterDataByKey(NUMBER_OF_CONCURRENT_TASKS_KEY);

            numberOfConcurrentTasks = solarRevenueCorrectionTaskMapper.getNumberOfTaskByTaskStatus(
                    TO_BE_EXECUTED.getCode());
        } catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [verifyTask] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
        /* Ended by AICoder, pid:y14e7d1d66b3bae1405d0804c033831d2bb52911 */
        verifyConfiguration(numberOfConcurrentTasks + 1, numberOfConcurrentTasksLimit, NUMBER_OF_CONCURRENT_TASKS_OVERSIZE,
                languageOption);
    }

    /**
     * 根据key获取配置中心数据
     *
     * @param key :
     * @return java.lang.String
     * <AUTHOR> 2023/11/24 - 下午2:05
     **/
    private String getCfgCenterDataByKey(String key) {
        String result = configService.getGlobalProperty(key);
        log.info(" key {}, cfgCenter data is {}.", key, result);
        return result;
    }

    private void verifyConfiguration(int numberOfReality, String numberOfLimit, String prompt, String languageOption)
            throws UedmException {
        log.info("numberOfReality, numberOfLimit, prompt:{},{},{}.", numberOfReality, numberOfLimit, prompt);
        try {
            if (numberOfReality > Integer.parseInt(numberOfLimit)) {
                throw new UedmException(STRONG_VERIFY_CODE,
                        i18nUtils.getMapFieldByLanguageOption(String.format(prompt, numberOfLimit, numberOfLimit), languageOption));
            }
        } catch (NumberFormatException e) {
            log.error("verify configuration parse int error", e);
            throw new UedmException(STRONG_VERIFY_CODE,
                    i18nUtils.getMapFieldByLanguageOption(NUMBER_FORMAT_EXCEPTION, languageOption));
        }
    }

    public PageInfo<SolarRevenueCorrectionTaskPO> getCorrectionTaskRecords(SolarCorrectTaskQueryDto solarCorrectTaskQueryDto) {
        Integer pageNo = solarCorrectTaskQueryDto.getPageNo();
        Integer pageSize = solarCorrectTaskQueryDto.getPageSize();

        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        List<SolarRevenueCorrectionTaskPO> revenueCorrectionTaskPOS = null;
        try {
            revenueCorrectionTaskPOS = solarRevenueCorrectionTaskMapper.selectSolarRevenueCorrectionTaskByPage(
                    solarCorrectTaskQueryDto);
        }catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [getCorrectionTaskRecords] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(DatabaseExceptionEnum.OPERATEDB.getDesc());
        }

        return new PageInfo<> (revenueCorrectionTaskPOS);
    }

    public List<JSONObject> parseSolarRevenueCorrectionTaskPO2Json(List<SolarRevenueCorrectionTaskPO> solarRevenueCorrectionTaskPOS){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<JSONObject> result = solarRevenueCorrectionTaskPOS.stream().map(solarRevenueCorrectionTaskPO -> {
            JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(solarRevenueCorrectionTaskPO));
            jsonObject.put("gmtCreate",simpleDateFormat.format(solarRevenueCorrectionTaskPO.getGmtCreate()));
            jsonObject.put("gmtModified",simpleDateFormat.format(solarRevenueCorrectionTaskPO.getGmtModified()));
            ScopeStrategyResponseBean gridScopeStrategyBean = gridStrategyMapper.selectById(solarRevenueCorrectionTaskPO.getGridScopeStrategyId());
            ScopeStrategyResponseBean solarScopeStrategyBean = gridStrategyMapper.selectById(solarRevenueCorrectionTaskPO.getSolarScopeStrategyId());
            jsonObject.put("gridScopeStrategyName",ObjectUtils.isEmpty(gridScopeStrategyBean)?Strings.EMPTY:gridScopeStrategyBean.getName());
            jsonObject.put("solarScopeStrategyName",ObjectUtils.isEmpty(solarScopeStrategyBean)?Strings.EMPTY:solarScopeStrategyBean.getName());
            return jsonObject;
        }).collect(Collectors.toList());
        return result;
    }

    public void retryCorrect(String correctionTaskId){
        SolarRevenueCorrectionTaskPO solarRevenueCorrectionTaskPO = new SolarRevenueCorrectionTaskPO();
        solarRevenueCorrectionTaskPO.setTaskStatus(SolarRevenueCorrectionTaskStatusEnum.TO_BE_EXECUTED.getCode());
        solarRevenueCorrectionTaskPO.setGmtModified(new Date());
        solarRevenueCorrectionTaskPO.setId(correctionTaskId);
        try {
            solarRevenueCorrectionTaskMapper.updateSolarRevenueCorrectionTask(solarRevenueCorrectionTaskPO);
            solarRevenueCorrectionTaskDayDetailMapper.deleteSolarRevenueCorrectRecordDetailByTaskId(correctionTaskId);
        } catch (Exception e) {
            log.error("SolarRevenueCorrectionServiceImpl [retryCorrect] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
        //触发任务执行
        CompletableFuture.runAsync(() -> solarRevenueStatisticsService.exeCorrectiveTasks());
    }
}
