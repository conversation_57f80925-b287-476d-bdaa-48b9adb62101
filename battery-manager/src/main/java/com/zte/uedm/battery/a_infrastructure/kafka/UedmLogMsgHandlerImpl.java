package com.zte.uedm.battery.a_infrastructure.kafka;
import com.zte.uedm.component.kafka.consumer.service.KafkaMsgHandler;
import com.zte.uedm.battery.a_domain.safe.OperationLogService;
import com.zte.uedm.common.bean.KafkaBean;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @FileDesc :
 * <AUTHOR> LDQ 10260975
 * @date Date : 2021年01月15日 16:29
 * @Version : 1.0
 */
/* Started by AICoder, pid:2fa3e465cew53d414b7c0bf6b01b0d334c081c3c */
@Component(KafkaTopicConstants.KAFKA_TOPIC_UEDM_LOG)
@Slf4j
public class UedmLogMsgHandlerImpl implements KafkaMsgHandler {
    private static final String CREATE_ACTION = "create";
    private static final String UPDATE_ACTION = "update";

    @Autowired
    private JsonService jsonService;

    @Autowired
    private OperationLogService operationLogService;

    @Override
    public void onMsg(String msg) {
        try {
            log.info("[Kafka-Receive] ====receive topic={}, start to dealKafka==========", KafkaTopicConstants.KAFKA_TOPIC_UEDM_LOG);
            processMessage(msg);
            log.info("[Kafka-Receive]  topic ={}, dealKafka end.", KafkaTopicConstants.KAFKA_TOPIC_UEDM_LOG);
        } catch (Exception e) {
            log.error("uedm_log deal error:{}", e.getMessage());
        }
    }

    private void processMessage(String msg) throws UedmException {
        KafkaBean kafkaBean = jsonService.jsonToObject(msg, KafkaBean.class);
        String action = kafkaBean.getAction();
        Object data =  kafkaBean.getData();
        OperationLogBean operationLogBean = convertToOperationLogBean(data);

        if (UPDATE_ACTION.equals(action)) {
            operationLogService.updateLog(operationLogBean);
        } else if (CREATE_ACTION.equals(action)) {
            operationLogService.addLog(operationLogBean);
        }
    }

    private OperationLogBean convertToOperationLogBean(Object data) throws UedmException {
        return jsonService.jsonToObject(jsonService.objectToJson(data), OperationLogBean.class);
    }
}

/* Ended by AICoder, pid:2fa3e465cew53d414b7c0bf6b01b0d334c081c3c */
