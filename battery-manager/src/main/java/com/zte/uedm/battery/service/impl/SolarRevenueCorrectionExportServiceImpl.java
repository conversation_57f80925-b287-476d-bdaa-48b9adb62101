package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.bean.SolarRevenueCorrectRecordDetailCommand;
import com.zte.uedm.battery.bean.pv.ExportDataDto;
import com.zte.uedm.battery.bean.pv.ExportDataMiddleBean;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.export.manage.entity.ExportReportBO;
import com.zte.uedm.battery.export.manage.entity.ExportType;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectRecordDetailVO;
import com.zte.uedm.battery.pv.bean.SolarRevenueCorrectionTaskPO;
import com.zte.uedm.battery.pv.consts.SolarRevenueConst;
import com.zte.uedm.battery.pv.dto.SolarCorrectTaskQueryDto;
import com.zte.uedm.battery.pv.enums.SolarRevenueCorrectionTaskStatusEnum;
import com.zte.uedm.battery.pv.mapper.SolarRevenueCorrectionTaskMapper;
import com.zte.uedm.battery.service.SolarRevenueCorrectionExportService;
import com.zte.uedm.battery.service.SolarRevenueCorrectionService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.battery.util.constant.PvConstant;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zte.uedm.battery.consts.CommonConst.MASTER_DIRECTORY;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SolarRevenueCorrectionExportServiceImpl implements SolarRevenueCorrectionExportService {

    @Autowired
    private SolarRevenueCorrectionService solarRevenueCorrectionService;

    @Autowired
    private I18nUtils i18nUtils;

    private static String EN_DASH = " - ";

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private WriterExportFactory wf;


    @Autowired
    private SolarRevenueCorrectionTaskMapper solarRevenueCorrectionTaskMapper;

    private static String SOLAR_CORRECTION = "_SolarCorrection_";

    private static String CORRECT_RECORDS_EXPORT_FILENAME = "SolarCorrection";

    @Autowired
    private ConfigService configService;

    @Override
    public List<ExportDataDto> getCorrectRecords(SolarCorrectTaskQueryDto solarCorrectTaskQueryDto, String lang) throws UedmException {
        try {
            List<SolarRevenueCorrectionTaskPO> revenueCorrectionTaskPOS =  Optional.ofNullable(solarRevenueCorrectionTaskMapper.selectSolarRevenueCorrectionTaskByPage(solarCorrectTaskQueryDto)).orElse(new ArrayList<>());
            List<JSONObject> result = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(revenueCorrectionTaskPOS)){
                result = solarRevenueCorrectionService.parseSolarRevenueCorrectionTaskPO2Json(revenueCorrectionTaskPOS);
            }
            ExportDataMiddleBean exportDataMiddleBean = transferCorrectRecordsToExportData(result,lang);
            String fileName = CORRECT_RECORDS_EXPORT_FILENAME;
            List<ExportDataDto> exportDataDtosList = transferExportDataDto(exportDataMiddleBean,fileName);
            return exportDataDtosList;
        }
        catch (Exception e)
        {
            log.error("SolarRevenueCorrectionExportServiceImpl getCorrectRecords error", e);
            throw new UedmException(-200, "SolarRevenueCorrectionExportServiceImpl getCorrectRecords error");
        }
    }

    private ExportDataMiddleBean transferCorrectRecordsToExportData( List<JSONObject> result,String languageOption ){
        ExportDataMiddleBean resultBean = new ExportDataMiddleBean();
        if(CollectionUtils.isEmpty(result)){
            //为空保护
            String[] columns = buildCorrectRecordsTableColumns(languageOption);
            resultBean.setColumns(columns);
            resultBean.setRows(new String[1][columns.length]);
            resultBean.setTotalCount(1);
            log.info("SolarRevenueCorrectionExportServiceImpl correctRecordsList list is empty");
            return resultBean;
        }
        String[] columns = buildCorrectRecordsTableColumns(languageOption);
        resultBean.setColumns(columns);
        String[][] data = new String[result.size()][columns.length];
        int row = 0;
        for(JSONObject correctionTask:result){
            int column = 0;
            if(null!=correctionTask.get("gmtCreate")) {
                data[row][column++] = Objects.toString(correctionTask.get("gmtCreate"));
            }
            data[row][column++] = Objects.toString(correctionTask.get("correctionStartDate"),"")+EN_DASH +Objects.toString(correctionTask.get("correctionEndDate"),"");
            data[row][column++] = SolarRevenueCorrectionTaskStatusEnum.getStatusByCode(Objects.toString(correctionTask.get("taskStatus"),""),languageOption);
            if(null!=correctionTask.get("siteCount")){
                data[row][column++] = Objects.toString(correctionTask.get("siteCount"));
            }
            data[row][column++] = Objects.toString(correctionTask.get("gridScopeStrategyName"),"");
            data[row][column++] = Objects.toString(correctionTask.get("solarScopeStrategyName"),"");
            data[row][column++] = Objects.toString(correctionTask.get("operator"),"");
            row++;
        }
        resultBean.setRows(data);
        resultBean.setTotalCount(result.size());
        return resultBean;
    }

    private String[] buildCorrectRecordsTableColumns(String languageOption){
        ArrayList<String> list = new ArrayList<>();
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.CREATE_DATE,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.TIME_FRAME,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.TASK_STATUS,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.SITE_COUNT,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.GRID_ELECTRICITY_PRICE_STRATEGY,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.SOLAR_ELECTRICITY_PRICE_STRATEGY,languageOption));
        list.add(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.OPERATOR,languageOption));
        String[] tableTitle = list.toArray(new String[list.size()]);
        return tableTitle;
    }

    public List<ExportDataDto> transferExportDataDto(ExportDataMiddleBean monitorExportDataDto, String fileName)
    {
        if (null == monitorExportDataDto)
        {
            return new ArrayList<>();
        }
        List<ExportDataDto> result = new ArrayList<>();
        ExportDataDto exportDataDto = new ExportDataDto();
        String[] columnArray = monitorExportDataDto.getColumns();
        String[][] data = monitorExportDataDto.getRows();
        exportDataDto.setData(data);
        exportDataDto.setHeader(columnArray);
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        //文件名称命名默认"SolarCorrection-"+time
        String taskName = format.format(date);
        String name = fileName + "_" + taskName;
        exportDataDto.setFileName(name);
        exportDataDto.setTitle(name);
        result.add(exportDataDto);
        return result;
    }


    @Override
    public boolean dataExport(HttpServletResponse response, HttpServletRequest request, List<ExportDataDto> beanList) throws UedmException {
        List<String[]> headerList = new ArrayList<>();
        List<String[][]> dataList = new ArrayList<>();
        List<String> titles = new ArrayList<>();
        if (CollectionUtils.isEmpty(beanList) || beanList.get(0) == null)
        {
            log.warn("beanList is empty!");
            return false;
        }
        headerList.add(beanList.get(0).getHeader());
        dataList.add(beanList.get(0).getData());
        titles.add(beanList.get(0).getTitle());
        log.info("SolarRevenueCorrectionExportServiceImpl dataExport sheet name:{}", beanList.get(0).getTitle());

        FileExportWriter fw = wf.getWriter(ExportType.EXCEL);
        if (fw == null)
        {
            return false;
        }

        ExportReportBO erf = new ExportReportBO();
        log.info("SolarRevenueCorrectionExportServiceImpl dataExport begin time is {}", dateTimeService.getCurrentTime());
        String mills = System.currentTimeMillis()+"";
        String filePathStr =
                PvConstant.MASTER_DIRECTORY + mills + File.separator + beanList.get(0).getFileName() + fw
                        .getExtension();
        try
        {
            log.info("SolarRevenueCorrectionExportServiceImpl dataExport before time is {} filePathStr {}", dateTimeService.getCurrentTime(), filePathStr);
            File filedir = new File(
                    PvConstant.MASTER_DIRECTORY + mills + File.separator + beanList.get(0).getFileName());
            log.info("SolarRevenueCorrectionExportServiceImpl dataExport second time is {}", dateTimeService.getCurrentTime());
            if (!filedir.exists())
            {
                boolean isMkdir = filedir.mkdirs();
                log.info("SolarRevenueCorrectionExportServiceImpl dataExport is mkdired:" + isMkdir);
            }
            erf.setOutputFile(filePathStr);
            erf.setFileName(mills);
            erf.setTitles(titles);
            erf.setMills(mills);
            erf.setColHeaderList(headerList);
            erf.setDataMessList(dataList);
            fw.setFormat(erf);
            fw.writeData();
        }
        catch (Exception e)
        {
            log.info("SolarRevenueCorrectionExportServiceImpl dataExport error:", e);
            throw new UedmException(-200, e.getMessage());
        }
        finally {
            closeFile(fw);
        }

        File file = new File(FileUtils.pathManipulation(filePathStr));

        if (file.exists())
        {
            // 压缩文件
            String srcStr =
                    PvConstant.MASTER_DIRECTORY + mills + File.separator + beanList.get(0).getFileName() + ".xlsx";
            FileUtils.downloadFile(srcStr, response, request);
        }
        log.info("SolarRevenueCorrectionExportServiceImpl dataExport end time is {}", dateTimeService.getCurrentTime());

        return true;
    }

    public static void closeFile(FileExportWriter wb) {
        try {
            if (wb!= null)
            {
                wb.closeFile();
            }
        } catch (IOException e) {
            log.error("ExcelFileExportUtils close error!", e);
        }
    }

    @Override
    public String exportCorrectRecordsDetails(SolarRevenueCorrectRecordDetailCommand param, HttpServletResponse response, HttpServletRequest request, String lang) throws UedmException {
        try{
            List<SolarRevenueCorrectRecordDetailVO> correctRecordsDetailList = solarRevenueCorrectionService.getSolarRevenueCorrectRecordDetail(param);
            return transferCorrectRecordsDetailsToExportData(correctRecordsDetailList,response,request,lang);
        }
        catch (Exception e)
        {
            log.error("SolarRevenueCorrectionExportServiceImpl exportRecordsDetails error", e);
            throw new UedmException(-200, e.getMessage());
        }
    }



    private String transferCorrectRecordsDetailsToExportData(List<SolarRevenueCorrectRecordDetailVO>correctRecordDetail, HttpServletResponse response, HttpServletRequest request,String languageOption ) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        String fileName = "";
        String downloadFileName = "";
        if(CollectionUtils.isEmpty(correctRecordDetail)){
            Sheet sheet = workbook.createSheet("SolarCorrection_" + 0);
            //创建表头数据
            createCorrectRecordsDetailsExcelHeader(workbook, sheet, languageOption);
            downloadFileName = downloadRecordsDetail(workbook,response,request,fileName);
            return downloadFileName;
        }
        int total = correctRecordDetail.size();
        //根据数据量生成文件名
        fileName = generateExportFileName(correctRecordDetail);
        int num = total/SolarRevenueConst.MAX_SHEET_ROW;
        for(int i =0;i<num+1;i++) {
            Sheet sheet = workbook.createSheet("SolarCorrection_" + i);
            //创建表头数据
            createCorrectRecordsDetailsExcelHeader(workbook, sheet, languageOption);
            int currentRow = i * SolarRevenueConst.MAX_SHEET_ROW;
            int index = 0;
            int beginRow = 2;
            for (int m = currentRow; m < correctRecordDetail.size(); m++) {
                if (index == SolarRevenueConst.MAX_SHEET_ROW) {
                    break;
                }
                SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO = correctRecordDetail.get(m);
                Row row = sheet.createRow(beginRow++);
                row.createCell(0).setCellValue(solarRevenueCorrectRecordDetailVO.getSiteName());
                row.createCell(1).setCellValue(solarRevenueCorrectRecordDetailVO.getPosition());
                row.createCell(2).setCellValue(solarRevenueCorrectRecordDetailVO.getSolarRevenueNew());
                row.createCell(3).setCellValue(solarRevenueCorrectRecordDetailVO.getSolarRevenueOld());
                row.createCell(4).setCellValue(solarRevenueCorrectRecordDetailVO.getGridRevenueNew());
                row.createCell(5).setCellValue(solarRevenueCorrectRecordDetailVO.getGridRevenueOld());
                row.createCell(6).setCellValue(solarRevenueCorrectRecordDetailVO.getSaveElectricChargeNew());
                row.createCell(7).setCellValue(solarRevenueCorrectRecordDetailVO.getSaveElectricChargeOld());
                index++;
            }
        }
        downloadFileName = downloadRecordsDetail(workbook,response,request,fileName);
        return downloadFileName;
    }

    /* Started by AICoder, pid:j790cn640fcc3231462e087fe0c8e07a8bb025b0 */
    public void createCorrectRecordsDetailsExcelHeader(Workbook workbook, Sheet sheet, String languageOption){
        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建第一行
        Row row0 = sheet.createRow(0);
        Cell cell0 = row0.createCell(0);
        cell0.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.SITE,languageOption));
        cell0.setCellStyle(cellStyle);

        Cell cell1 = row0.createCell(1);
        cell1.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.POSITION,languageOption));
        cell1.setCellStyle(cellStyle);

        Cell cell2 = row0.createCell(2);
        String solarRevenue = i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.SOLAR_REVENUE,languageOption);
        String solarRevenueValue = solarRevenue + "(" + getCurrency() + ")";
        cell2.setCellValue(solarRevenueValue);
        cell2.setCellStyle(cellStyle);

        Cell cell4 = row0.createCell(4);
        String gridElectricityCosts = i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.GRID_ELECTRICITY_COSTS,languageOption);
        String gridElectricityCostsValue = gridElectricityCosts + "(" + getCurrency() + ")";
        cell4.setCellValue(gridElectricityCostsValue);
        cell4.setCellStyle(cellStyle);

        Cell cell6 = row0.createCell(6);
        String saveElectricityCosts = i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.SAVE_ELECTRICITY_COSTS,languageOption);
        String saveElectricityCostsValue = saveElectricityCosts + "(" + getCurrency() + ")";
        cell6.setCellValue(saveElectricityCostsValue);
        cell6.setCellStyle(cellStyle);

        // 创建第二行（下方线条增粗）
        Row row1 = sheet.createRow(1);
        Cell cell12 = row1.createCell(2);
        cell12.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.NEW,languageOption));
        cell12.setCellStyle(cellStyle);
        Cell cell13 = row1.createCell(3);
        cell13.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.OLD,languageOption));
        cell13.setCellStyle(cellStyle);

        Cell cell14 = row1.createCell(4);
        cell14.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.NEW,languageOption));
        cell14.setCellStyle(cellStyle);
        Cell cell15 = row1.createCell(5);
        cell15.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.OLD,languageOption));
        cell15.setCellStyle(cellStyle);

        Cell cell16 = row1.createCell(6);
        cell16.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.NEW,languageOption));
        cell16.setCellStyle(cellStyle);
        Cell cell17 = row1.createCell(7);
        cell17.setCellValue(i18nUtils.getMapFieldByLanguageOption(SolarRevenueConst.OLD,languageOption));
        cell17.setCellStyle(cellStyle);

        // 合并单元格
        CellRangeAddress range1 = new CellRangeAddress(0, 1, 0, 0);
        CellRangeAddress range2 = new CellRangeAddress(0, 1, 1, 1);
        CellRangeAddress range3 = new CellRangeAddress(0, 0, 2, 3);
        CellRangeAddress range4 = new CellRangeAddress(0, 0, 4, 5);
        CellRangeAddress range5 = new CellRangeAddress(0, 0, 6, 7);
        sheet.addMergedRegion(range1);
        sheet.addMergedRegion(range2);
        sheet.addMergedRegion(range3);
        sheet.addMergedRegion(range4);
        sheet.addMergedRegion(range5);
    }
    /* Ended by AICoder, pid:j790cn640fcc3231462e087fe0c8e07a8bb025b0 */
    private String generateExportFileName(List<SolarRevenueCorrectRecordDetailVO>correctRecordDetail){
        String fileName = "";
        int total = correctRecordDetail.size();
        if(total == 1) {
            //单站点
            SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO = correctRecordDetail.get(0);
            if(StringUtils.isNotEmpty(solarRevenueCorrectRecordDetailVO.getPosition())) {
                fileName = solarRevenueCorrectRecordDetailVO.getPosition().replace("/", "-");
            }

        }else {
            //多站点
            SolarRevenueCorrectRecordDetailVO solarRevenueCorrectRecordDetailVO = correctRecordDetail.get(0);
            if (StringUtils.isNotEmpty(solarRevenueCorrectRecordDetailVO.getPosition())) {
                String[] position = solarRevenueCorrectRecordDetailVO.getPosition().split("/");
                fileName = position[0];
            }
        }
        return fileName;
    }

    private String downloadRecordsDetail(Workbook workbook,HttpServletResponse response, HttpServletRequest request,String filName) throws IOException {

        String downloadFileName = "";
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        //文件名称命名默认"Pv_power_Analysis-"+time
        String taskName = format.format(date);
        String name = filName + SOLAR_CORRECTION + taskName;
        String mills = System.currentTimeMillis()+"";

        String filePathStr =
                MASTER_DIRECTORY + mills ;
        String fullPathName = MASTER_DIRECTORY + mills + File.separator + name + ".xlsx";

        FileOutputStream fos = null;
        try {
            File filedir = new File(filePathStr);
            if(!filedir.exists()){
                boolean isMkdir = filedir.mkdirs();
                log.info("SolarRevenueCorrectionExportServiceImpl dataExport is mkdired:" + isMkdir);
            }
            //写入
            File file = new File(FileUtils.pathManipulation(fullPathName));
            fos = new FileOutputStream(file);
            workbook.write(fos);
            fos.flush();
            //下载文件
            if (file.exists())
            {
                log.info("down excel File");
                FileUtils.downloadFile(fullPathName, response, request);
            }
            downloadFileName = name+".xlsx";
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
        finally {
            try{
                if(fos!=null){
                fos.close();
            }
            }catch (IOException e){
                log.error("ExcelFileExport close error!", e);
            }

        }
        return downloadFileName;
    }

    private String getCurrency() {
        String currency = configService.getGlobalProperty(SolarRevenueConst.CFG_CENTER_CURRENCY_KEY);
        return currency;
    }


}
