package com.zte.uedm.battery.controller.batttest.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zte.uedm.battery.enums.backuppower.BackupPowerStateEnum;
import com.zte.uedm.battery.enums.batttest.*;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.*;

@Getter
@Setter
@ToString
public class TestImpactTrendSelectDto
{
    @ApiModelProperty("逻辑组id")
    @NotBlank(message = "logicGroupId can not be blank")
    private String logicGroupId;

    @ApiModelProperty("影响趋势维度列表")
    @NotEmpty(message = "impactDims can not be blank")
    private List<String> impactDims;

    @ApiModelProperty("测试状态列表")
    private List<String> testStatus;

    @ApiModelProperty("站点等级列表")
    private List<String> siteLevels;

    @ApiModelProperty("供电场景列表")
    private List<String> powerSupplyScene;

    @ApiModelProperty("备电状态列表")
    private List<String> backupPowerStatus;

    @ApiModelProperty("健康状态列表")
    private List<String> healthStatus;

    @ApiModelProperty("测试类型列表")
    private List<String> testTypes;

    @ApiModelProperty("测试结果列表")
    private List<String> testResult;

    @ApiModelProperty("交流输入状态列表")
    private List<String> acInputStatus;

    @ApiModelProperty("下发时间起")
    private String deliveryStartTime;

    @ApiModelProperty("下发时间止")
    private String deliveryEndTime;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;


    /**
     * 非空校验
     * @return  校验结果--true -> 通过  false 未通过 ,  未通过校验的字段名称
     */
    public Pair<Boolean, Set<String>> checkNotEmpty()
    {
        Set<String> blankSets = new HashSet<>();
        boolean result = true;
        ValidationResult validationResult = ValidationUtils.validateForDefalut(this);
        if(validationResult.isHasErrors())
        {
            result=false;
            blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
        }
        return Pair.of(result,blankSets);
    }

    /**
     * 参数范围校验
     */
    public Pair<Boolean, Set<String>> checkParamsRange()
    {
        Set<String> blankSets = new HashSet<>();
        Boolean result = true;
        //维度列表
        result = checkRange(blankSets, result, impactDims, new HashSet<>(BatteryTestImpactEnums.getAllID()), "impactDims");
        //测试状态
        result = checkRange(blankSets, result, testStatus, new HashSet<>(BattTestStatusEnums.getAllId()), "testStatus");
        //备电状态
        result = checkRange(blankSets, result, backupPowerStatus, new HashSet<>(BackupPowerStateEnum.getAllackupPowerStateIds()), "backupPowerStatus");
        //测试类型
        result = checkRange(blankSets, result, testTypes, new HashSet<>(Arrays.asList(BattTestTypeEnums.PERIODIC.getId(),BattTestTypeEnums.TEMPORARY.getId())), "testTypes");
        //测试结果
        result = checkRange(blankSets, result, testResult, new HashSet<>(BatteryTestResultEnums.getAllIds()), "testResult");
        //交流输入状态列表
        result = checkRange(blankSets, result, acInputStatus, new HashSet<>(AcInputStatusEnums.getAllId()), "acInputStatus");
        return Pair.of(result,blankSets);
    }

    public Boolean checkRange(Set<String> blankSets, Boolean result, List<String> checkList, Set<String> optionalRangeSet, String attrName) {
        if (CollectionUtils.isNotEmpty(checkList)) {
            for (String item : checkList) {
                if (!optionalRangeSet.contains(item)) {
                    result = false;
                    blankSets.add(attrName);
                }
            }
        }
        return result;
    }


    public Pair<Boolean, Set<String>> checkParmasRangeByOptionaRange(Set<String> siteLevelOptionalRangeSet, Set<String> sceneOptionalRangeSet, Set<String> healthStatusOptionalRangeSet)
    {
        Set<String> checkFailAttrNameSet = new HashSet<>();
        //根据站点等级，供电场景可选范围判断
        Boolean result=true;
        if(CollectionUtils.isNotEmpty(siteLevels))
        {
            result=checkRange(checkFailAttrNameSet,result,siteLevels,siteLevelOptionalRangeSet,"siteLevels");
        }
        if(CollectionUtils.isNotEmpty(powerSupplyScene))
        {
            result=checkRange(checkFailAttrNameSet,result,powerSupplyScene,sceneOptionalRangeSet,"powerSupplyScene");
        }
        if(CollectionUtils.isNotEmpty(healthStatus))
        {
            result=checkRange(checkFailAttrNameSet,result,healthStatus,healthStatusOptionalRangeSet,"healthStatus");
        }
        return Pair.of(result,checkFailAttrNameSet);
    }

    public boolean checkTime(){
        boolean result=true;
        if (!StringUtils.isEmpty(deliveryStartTime) && !StringUtils.isEmpty(deliveryEndTime))
        {
            //开始时间大于等于结束时间
            result=  deliveryStartTime.compareTo(deliveryEndTime) < 0;
        }
        if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime))
        {
            //开始时间大于等于结束时间
            if(result)
            {
                result=  startTime.compareTo(endTime) < 0;
            }
        }
        return result;
    }
}
