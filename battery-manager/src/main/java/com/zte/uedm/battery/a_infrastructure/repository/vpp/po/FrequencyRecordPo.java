package com.zte.uedm.battery.a_infrastructure.repository.vpp.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Date;

/* Started by AICoder, pid:jae7bw01d0026d4146200b7bd0f4143accb65b4d */
@Setter
@Getter
@ToString
@ApiModel(description = "调频记录信息")
public class FrequencyRecordPo {

    @ApiModelProperty(value = "南北向标识")
    private String deviceLogId;

    @ApiModelProperty(value = "监控对象id")
    private String moId;

    @ApiModelProperty(value = "监控设备id")
    private String deviceId;

    @ApiModelProperty(value = "调频开始时间")
    private Timestamp startTime;

    @ApiModelProperty(value = "调频结束时间")
    private Timestamp endTime;

    @ApiModelProperty(value = "工作模式")
    private String workModel;

    @ApiModelProperty(value = "设备响应时间")
    private String responseTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "调节功率")
    private String frequency;
}
/* Ended by AICoder, pid:jae7bw01d0026d4146200b7bd0f4143accb65b4d */
