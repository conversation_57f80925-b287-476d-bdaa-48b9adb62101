package com.zte.uedm.battery.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.zte.uedm.battery.bean.BattStandbyPowerRcdHisBean;

/**
 * 电池备电配置历史记录mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BattStandbyPowerRcdHisMapper
{

    void insert(List<BattStandbyPowerRcdHisBean> beanList);

    Integer update(BattStandbyPowerRcdHisBean bean);

    List<BattStandbyPowerRcdHisBean> select(BattStandbyPowerRcdHisBean bean);

    BattStandbyPowerRcdHisBean selectLastestRcdByTime(BattStandbyPowerRcdHisBean bean);
}
