package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pojo.ScopeIntervalStrategyPo;
import com.zte.uedm.battery.bean.pojo.ScopePriceStrategyPo;
import com.zte.uedm.battery.bean.scopeStrategy.TieredPriceStrategyBean;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.enums.PriceStrategyStatusEnum;
import com.zte.uedm.battery.mapper.BatteryChargeDischargeHistoryR321Mapper;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.mapper.PeakShiftMapper;
import com.zte.uedm.battery.mapper.PriceStrategyMapper;
import com.zte.uedm.battery.service.BatteryChargeDischargeHistoryService;
import com.zte.uedm.common.configuration.enums.EnergyTypeEnum;
import com.zte.uedm.common.consts.TimeFormatConstants;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.service.config.api.configuraiton.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BatteryChargeDischargeHistoryServiceImpl implements BatteryChargeDischargeHistoryService {

    @Resource
    private BatteryChargeDischargeHistoryR321Mapper batteryChargeDischargeHistoryR321Mapper;

    @Resource
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Resource
    private JsonService jsonService;

    @Resource
    private GridStrategyMapper gridStrategyMapper;

    @Resource
    private PriceStrategyMapper priceStrategyMapper;
    @Resource
    private DeviceService deviceService;

    @Resource
    private ResourceBaseCacheManager resourceBaseCacheManager;

    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Resource
    private DateTimeService dateTimeService;

    @Resource
    private PeakShiftMapper peakShiftMapper;

    public static final String DATE_FORMAT_0 = "yyyy-MM-dd 00:00:00";
    public static final String ZERO_TIME = " 00:00:00";

    //节假日模式
    public static final Integer MODE_HOLIDAY = 3;

    //周模式
    public static final Integer MODE_WEEK = 1;

    //月模式
    public static final Integer MODE_MONTH = 2;

    //日模式
    public static final Integer MODE_DAY = 0;

    public static final int batchSize = 500;

    /* Started by AICoder, pid:64b29qb83es099b141310b5091db3d0686f93724 */
    @Override
    public void updateOrInsertBatteryChargeDischargeHistory(List<BatteryChargeDischargeHistoryBean> dataBeans) throws UedmException, com.zte.uedm.common.exception.UedmException, ParseException {
        // 首先检查入参是否为空
        if (dataBeans.isEmpty()) {
            log.warn("Input dataBeans is empty, skip processing ");
            return;
        }
        //将databeans根据deviceId+battertSeq进行分组
        Map<String, List<BatteryChargeDischargeHistoryBean>> batterySeqMap = dataBeans.stream()
               .collect(Collectors.groupingBy(bean -> bean.getDeviceId() + "_" + bean.getBatterySeq()));

        List<BatteryChargeDischargeHistoryBean> insertList = new ArrayList<>();

        //遍历batterySeqMap
        for (String batterySeq : batterySeqMap.keySet()) {
            List<BatteryChargeDischargeHistoryBean> batteryData = batterySeqMap.get(batterySeq);
            //检查是否有历史数据
            List<BatteryChargeDischargeHistoryBean> dbBeanList = batteryChargeDischargeHistoryR321Mapper.selectAll(batterySeq.split("_")[0], batterySeq.split("_")[1]);
            if (dbBeanList != null && !dbBeanList.isEmpty()) {
                Optional<BatteryChargeDischargeHistoryBean> optionalDbBean = dbBeanList.stream()
                        .filter(dbBean -> batterySeq.split("_")[1].equals(dbBean.getBatterySeq()))
                        .max(Comparator.comparingLong(a -> Long.parseLong(a.getRecordTime())));
                if (optionalDbBean.isPresent()) {
                    BatteryChargeDischargeHistoryBean batteryChargeDischargeHistoryBean = optionalDbBean.get();

                    // 获取库中记录的最大recordTime值
                    long maxRecordTime = Long.parseLong(batteryChargeDischargeHistoryBean.getRecordTime());

                    // 移除batteryData中recordTime小于等于maxRecordTime的bean
                    batteryData = batteryData.stream()
                            .filter(dataBean -> Long.parseLong(dataBean.getRecordTime()) > maxRecordTime)
                            .collect(Collectors.toList());

                    if (!batteryData.isEmpty()) {
                        // 库中有数据，走正常的计算逻辑
                        processWithHistoryData(batteryData, batteryChargeDischargeHistoryBean, insertList);
                    }
                }

            } else {
                // 库中没有数据，按设备ID和电池序列号分组计算
                processWithoutHistoryData(batteryData, insertList);
            }

            log.debug("insertList = {}", JSON.toJSONString(insertList));
            if (insertList.isEmpty()) {continue;}
            List<BatteryChargeDischargeHistoryBean> batteryChargeDischargeHistoryBeans = new ArrayList<>(insertList);

            insertPeakShiftBatteryChargeDischarge(batteryChargeDischargeHistoryBeans);

            // 批量插入数据
            if (!insertList.isEmpty()) {
                for (int i = 0; i < insertList.size(); i += batchSize) {
                    int end = Math.min(i + batchSize, insertList.size());
                    List<BatteryChargeDischargeHistoryBean> batchList = insertList.subList(i, end);
                    batteryChargeDischargeHistoryR321Mapper.batchInsertBatteryChargeDischargeHistory(batchList);
                }
            }
        }
    }

    /**
     * 处理有历史数据的情况
     */
    private void processWithHistoryData(List<BatteryChargeDischargeHistoryBean> dataBeans,
                                        BatteryChargeDischargeHistoryBean dbBean,
                                        List<BatteryChargeDischargeHistoryBean> insertList) {
        // 对每个设备ID和电池序列号组合的数据分别处理
        if (!dataBeans.isEmpty()) {
            // 按记录时间排序
            dataBeans.sort(Comparator.comparingLong(a -> Long.parseLong(a.getRecordTime())));

            // 第一条数据的增量根据库中最后一条数据计算
            BatteryChargeDischargeHistoryBean firstRecord = dataBeans.get(0);
            calculateIncrements(firstRecord, dbBean);
            insertList.add(firstRecord);

            // 从第一条数据开始逐条计算增量
            for (int i = 1; i < dataBeans.size(); i++) {
                BatteryChargeDischargeHistoryBean currentRecord = dataBeans.get(i);
                BatteryChargeDischargeHistoryBean previousRecord = dataBeans.get(i - 1);
                calculateIncrements(currentRecord, previousRecord);
                insertList.add(currentRecord);
            }

        }
    }

    /**
     * 处理无历史数据的情况
     */
    private void processWithoutHistoryData(List<BatteryChargeDischargeHistoryBean> dataBeans,
                                           List<BatteryChargeDischargeHistoryBean> insertList) {
        Map<String, List<BatteryChargeDischargeHistoryBean>> batterySeqMap = dataBeans.stream()
                .collect(Collectors.groupingBy(bean -> bean.getDeviceId() + "_" + bean.getBatterySeq()));


        // 对每个设备ID和电池序列号组合的数据分别处理
        for (List<BatteryChargeDischargeHistoryBean> batteryData : batterySeqMap.values()) {
            if (!batteryData.isEmpty()) {
                // 按记录时间排序
                batteryData.sort(Comparator.comparingLong(a -> Long.parseLong(a.getRecordTime())));

                // 第一条数据的增量设置为0
                BatteryChargeDischargeHistoryBean firstRecord = batteryData.get(0);
                firstRecord.setIncChargeValue("0.00");
                firstRecord.setIncDischargeValue("0.00");
                insertList.add(firstRecord);

                // 从第一条数据开始逐条计算增量
                for (int i = 1; i < batteryData.size(); i++) {
                    BatteryChargeDischargeHistoryBean currentRecord = batteryData.get(i);
                    BatteryChargeDischargeHistoryBean previousRecord = batteryData.get(i - 1);
                    calculateIncrements(currentRecord, previousRecord);
                    insertList.add(currentRecord);
                }
            }
        }
    }

    public void insertPeakShiftBatteryChargeDischarge(List<BatteryChargeDischargeHistoryBean> dataBeans) throws UedmException, com.zte.uedm.common.exception.UedmException, ParseException {
        List<ResourceCollectorRelationEntity> allEntity = resourceCollectorRelationCacheManager.getAllEntity();

        //将dataBeans根据deviceId在allEntity中查找对应的resouceId
        Set<String> collectorIds = dataBeans.stream().map(BatteryChargeDischargeHistoryBean::getDeviceId).collect(Collectors.toSet());
        log.debug("insertPeakShiftBatteryChargeDischarge  collectorIds= {}", JSON.toJSONString(collectorIds));


        //根据collectorIds从allEntity中过滤出对应的resourceIds map
        Map<String, String> resourceIdsMap = allEntity.stream()
                .filter(entity -> collectorIds.contains(entity.getCollectorId()))
                .collect(Collectors.toMap(ResourceCollectorRelationEntity::getResourceId, ResourceCollectorRelationEntity::getCollectorId));

        log.debug("insertPeakShiftBatteryChargeDischarge  resourceIdsMap={}", JSON.toJSONString(resourceIdsMap));


        long timestamp1 = Long.parseLong(dataBeans.get(0).getRecordTime());


        LocalDateTime currentRecordTime = Instant.ofEpochMilli(timestamp1)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        String day = currentRecordTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("insertPeakShiftBatteryChargeDischarge  day={} ", day);



        // 获取电池父id所在的电价应用范围 key:电池父id（一般是电池组），value：scopeId

        Map<String, String> scopeMap = getBattParentIdScopeId(resourceIdsMap.keySet());
        List<String> scopeIdList = new ArrayList<>(scopeMap.values());

        // 获取当天的电价 key:scopeId,value:当日电价
        Map<String, TieredPriceStrategyBean> priceMap = getPriceByScopeIdAndDate(scopeIdList, day);
        // 获取当天的电价时段策略 key:scopeId,value:当日时段策略
        Map<String, List<IntervalStrategyDetailModeBean>> intervalStrategyMap = getIntervalStrategyByScopeId(scopeIdList, day);


        log.debug("scopeMap = {}", JSON.toJSONString(scopeMap));
        log.debug("priceMap = {}", JSON.toJSONString(priceMap));
        log.debug("intervalStrategyMap = {}", JSON.toJSONString(intervalStrategyMap));


        List<PeakShiftStatisticsBean> beans = calculateKwhGain(dataBeans, resourceIdsMap, priceMap, intervalStrategyMap, scopeMap, day);
        log.debug("beans = {}", JSON.toJSONString(beans));


        if (!CollectionUtils.isEmpty(beans)) {
            for (PeakShiftStatisticsBean bean : beans) {
                peakShiftMapper.insertIntoStatisticsData(bean);
            }
        }
    }

    /**
     * 计算充放电增量
     */
    private void calculateIncrements(BatteryChargeDischargeHistoryBean currentRecord,
                                     BatteryChargeDischargeHistoryBean previousRecord) {

        BigDecimal chargeValueCurrent = new BigDecimal(currentRecord.getChargeValue()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal dischargeValueCurrent = new BigDecimal(currentRecord.getDischargeValue()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal chargeValuePrevious = new BigDecimal(previousRecord.getChargeValue()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal dischargeValuePrevious = new BigDecimal(previousRecord.getDischargeValue()).setScale(2, RoundingMode.HALF_UP);


        BigDecimal incChargeValue = chargeValueCurrent.subtract(chargeValuePrevious).setScale(2, RoundingMode.HALF_UP);
        BigDecimal incDischargeValue = dischargeValueCurrent.subtract(dischargeValuePrevious).setScale(2, RoundingMode.HALF_UP);


        currentRecord.setIncChargeValue(incChargeValue.toString());
        currentRecord.setIncDischargeValue(incDischargeValue.toString());
    }
    /* Ended by AICoder, pid:64b29qb83es099b141310b5091db3d0686f93724 */

    /* Started by AICoder, pid:26e1b7a741v696b14ab20861d36c85746eb526c7 */


    private Map<String, String> getBattParentIdScopeId(Set<String> batteryPackIdSet) throws com.zte.uedm.common.exception.UedmException, UedmException {
        ScopeStrategyQueryBean queryBean = new ScopeStrategyQueryBean();
        queryBean.setEnergyType(Arrays.asList(EnergyTypeEnum.GRID.getEnergyType()));

        log.info("statistics:  queryBean = {}", jsonService.objectToJson(queryBean));
        List<ScopeStrategyResponseBean> strategyResponseBeans = gridStrategyMapper.queryList(queryBean);
        log.info("statistics:  strategyResponseBeans,{}", jsonService.objectToJson(strategyResponseBeans));

        boolean flag = strategyResponseBeans.stream()
                .anyMatch(bean -> bean.getLogicGroup().contains(CommonConst.LOGIC_GROUP_ROOT));
        Map<String, String> spWithStratygyMap = flag ? peakShiftConfigBaseServiceImpl.logicGroupIsRoot(strategyResponseBeans, true) :
                peakShiftConfigBaseServiceImpl.getMapRelation(strategyResponseBeans, false);
        log.debug("getBattParentIdScopeId  spWithStratygyMap:{}", JSON.toJSONString(spWithStratygyMap));
        Map<String, String> batteryPackScopeMap = spWithStratygyMap.entrySet().stream()
                .filter(stringStringEntry -> stringStringEntry.getValue() != null)
                .filter(stringStringEntry -> batteryPackIdSet.contains(stringStringEntry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        log.info("getBattParentIdScopeId  batteryPackIdSet size:{} res size:{}", batteryPackIdSet.size(), batteryPackScopeMap.size());
        log.debug("getBattParentIdScopeId  res:{}", JSON.toJSONString(batteryPackScopeMap));
        return batteryPackScopeMap;
    }

    public Map<String, TieredPriceStrategyBean> getPriceByScopeIdAndDate(List<String> scopeIdList, String day) {
        Map<String, TieredPriceStrategyBean> result = new HashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(scopeIdList)) {
            return result;
        }

        // 根据范围id和日期，获取单价日期范围表id
        List<ScopePriceStrategyPo> scopePriceStrategyPos = priceStrategyMapper.selectPriceStrategyIdByScopeId(scopeIdList, day, PriceStrategyStatusEnum.PENDING.getId());
        log.debug("getPriceByScopeIdAndDate scopePriceStrategyPos:{}", JSON.toJSONString(scopePriceStrategyPos));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(scopePriceStrategyPos)) {
            return result;
        }
        Map<String, String> priceIntervalScopeMap = scopePriceStrategyPos.stream()
                .collect(Collectors.toMap(ScopePriceStrategyPo::getPriceIntervalId, ScopePriceStrategyPo::getScopeStrategyId, (k1, k2) -> k2));
        List<String> priceIntervalIds = new ArrayList<>(priceIntervalScopeMap.keySet());

        // 根据单价日期范围id获取单价
        List<TieredPriceStrategyBean> tieredPriceStrategyBeans = priceStrategyMapper.selectPriceByIntervalIds(priceIntervalIds);
        log.debug("getPriceByScopeIdAndDate tieredPriceStrategyBeans:{}", JSON.toJSONString(tieredPriceStrategyBeans));
        Map<String, TieredPriceStrategyBean> priceStrategyBeanMap = Optional.ofNullable(tieredPriceStrategyBeans).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(TieredPriceStrategyBean::getPriceIntervalStrategyId, Function.identity(), (k1, k2) -> k1));

        // 关联范围id和电价
        priceStrategyBeanMap.forEach((priceIntervalId, priceBean) -> {
            String scopeId = priceIntervalScopeMap.get(priceIntervalId);
            // 一个范围id在一天内只会有一条价格
            if (org.apache.commons.lang3.StringUtils.isNotBlank(scopeId)) {
                result.put(scopeId, priceBean);
            }
        });
        log.info("getPriceByScopeIdAndDate req size:{} res size:{}", scopeIdList.size(), result.size());
        log.debug("getPriceByScopeIdAndDate result:{}", JSON.toJSONString(result));
        return result;
    }

    public Map<String, List<IntervalStrategyDetailModeBean>> getIntervalStrategyByScopeId(List<String> scopeIds, String day) {
        Map<String, List<IntervalStrategyDetailModeBean>> result = new HashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(scopeIds)) {
            return result;
        }

        // 根据范围id查询interval strategy id
        List<ScopeIntervalStrategyPo> scopeIntervalStrategyPos = priceStrategyMapper.selectIntervalStrategyByScopeIds(scopeIds, day);
        log.debug("getIntervalStrategyByScopeId scopeIntervalStrategyPos:{}", JSON.toJSONString(scopeIntervalStrategyPos));
        Map<String, ScopeIntervalStrategyPo> intervalStrategyPoMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(scopeIntervalStrategyPos).stream()
                .filter(bean -> org.apache.commons.lang3.StringUtils.isNotEmpty(bean.getIntervalStrategyId()))
                .collect(Collectors.toMap(ScopeIntervalStrategyPo::getIntervalStrategyId, Function.identity(), (k1, k2) -> k2));
        if (intervalStrategyPoMap.isEmpty()) {
            return result;
        }

        // 根据时段id获取时段策略详情
        List<IntervalStrategyDetailBean> strategyDetailBeans = gridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(new ArrayList<>(intervalStrategyPoMap.keySet()));
        org.apache.commons.collections4.CollectionUtils.emptyIfNull(strategyDetailBeans)
                .forEach(strategyBean -> {
                    String intervalStrategyId = strategyBean.getIntervalStrategyId();

                    // 获取时段策略关联的范围id
                    ScopeIntervalStrategyPo intervalStrategyPo = intervalStrategyPoMap.get(intervalStrategyId);
                    if (intervalStrategyPo != null && intervalStrategyPo.getMode() != null) {
                        IntervalStrategyDetailModeBean detailModeBean = new IntervalStrategyDetailModeBean();
                        BeanUtils.copyProperties(strategyBean, detailModeBean);

                        String scopeStrategyId = intervalStrategyPo.getScopeStrategyId();
                        detailModeBean.setScopeStrategyId(scopeStrategyId);
                        detailModeBean.setMode(intervalStrategyPo.getMode());

                        result.computeIfAbsent(scopeStrategyId, k -> new ArrayList<>()).add(detailModeBean);
                    } else {
                        log.warn("getIntervalStrategyByScopeId scope not find：{}", JSON.toJSONString(strategyBean));
                    }
                });

        log.debug("getIntervalStrategyByScopeId result:{}", JSON.toJSONString(result));
        log.info("getIntervalStrategyByScopeId req size:{} res size:{}", scopeIds.size(), result.size());
        return result;
    }

    public List<PeakShiftStatisticsBean> calculateKwhGain(List<BatteryChargeDischargeHistoryBean> dataBeans, Map<String, String> resourceIds,
                                                          Map<String, TieredPriceStrategyBean> priceMap, Map<String, List<IntervalStrategyDetailModeBean>> intervalStrategyMap,
                                                          Map<String, String> scopeMap, String day) throws ParseException {
        List<KwhGainBean> result = new ArrayList<>();
        List<ResourceBaseEntity> allResourceBase = resourceBaseCacheManager.getAllResourceBase();
        if (null == allResourceBase){
            return new ArrayList<>();
        }
        for (Map.Entry<String, String> resourceId : resourceIds.entrySet()) {
            String batteryParentId = resourceId.getKey();
            // 电价策略范围id
            String scopeId = scopeMap.get(batteryParentId);
            if (org.apache.commons.lang3.StringUtils.isBlank(scopeId)) {
                log.warn("calculateKwhGain scopeId not found batteryParentId:{}", batteryParentId);
                continue;
            }

            // 电价
            TieredPriceStrategyBean priceStrategyBean = priceMap.get(scopeId);
            // 时段策略
            List<IntervalStrategyDetailModeBean> intervalStrategyBeans = intervalStrategyMap.get(scopeId);
            if (priceStrategyBean == null || CollectionUtils.isEmpty(intervalStrategyBeans)) {
                log.warn("calculateKwhGain price or interval strategy not found, batter pack:{},{},{}",
                        batteryParentId, JSON.toJSONString(priceStrategyBean), JSON.toJSONString(intervalStrategyBeans));
                continue;
            }

            log.debug("batteryChargeDischargeHistoryBeans======{}",dataBeans);
            log.info("resourceIds==={}",resourceIds);

            // 遍历batteryChargeDischargeHistoryBeans，获取bean的充电增量和放电增量，将其放入chargeList和dischargeList中
            List<HistoryAiBean> chargeList = new ArrayList<>();
            List<HistoryAiBean> dischargeList = new ArrayList<>();
            // 先按recordTime排序
            List<BatteryChargeDischargeHistoryBean> sortedBeans = dataBeans.stream()
                    .sorted(Comparator.comparingLong(bean -> Long.parseLong(bean.getRecordTime())))
                    .collect(Collectors.toList());

            // 从第二条记录开始遍历，这样可以获取到前一条记录
            for (int i = 1; i < sortedBeans.size(); i++) {
                BatteryChargeDischargeHistoryBean currentBean = sortedBeans.get(i);
                BatteryChargeDischargeHistoryBean previousBean = sortedBeans.get(i - 1);

                HistoryAiBean charge = new HistoryAiBean();
                HistoryAiBean discharge = new HistoryAiBean();

                // 获取当前记录时间
                long currentTimestamp = Long.parseLong(currentBean.getRecordTime());
                LocalDateTime currentRecordTime = Instant.ofEpochMilli(currentTimestamp)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

                // 获取前一条记录时间
                long previousTimestamp = Long.parseLong(previousBean.getRecordTime());
                LocalDateTime previousRecordTime = Instant.ofEpochMilli(previousTimestamp)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

                String currentTimeStr = currentRecordTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String previousTimeStr = previousRecordTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                charge.setTimeRange(previousTimeStr + " - " + currentTimeStr);
                charge.setIncValue(currentBean.getIncChargeValue());

                discharge.setTimeRange(previousTimeStr + " - " + currentTimeStr);
                discharge.setIncValue(currentBean.getIncDischargeValue());

                chargeList.add(charge);
                dischargeList.add(discharge);
            }

            log.info("calculateKwhGain batteryParentId:{},chargeList:{},dischargeList:{}", batteryParentId, JSON.toJSONString(chargeList), JSON.toJSONString(dischargeList));

            // 计算当日每个时段的电池组充放电差值
            List<PeakShiftStatisticsBean> peakShiftStatisticsBeans = calculateKwhGainByTimePeriodDetail(allResourceBase, batteryParentId, intervalStrategyBeans, day, dischargeList, chargeList);
            return peakShiftStatisticsBeans;

        }
        log.info("calculateKwhGain battery pack size:{} res size:{}", resourceIds.size(), result.size());
        log.debug("calculateKwhGain battery pack:{} res:{}", JSON.toJSONString(resourceIds), JSON.toJSONString(result));
        return new ArrayList<>();
    }

    public List<PeakShiftStatisticsBean> calculateKwhGainByTimePeriodDetail(List<ResourceBaseEntity> allResourceBase, String batteryParentId, List<IntervalStrategyDetailModeBean> intervalStrategyBeans, String day,
                                                                            List<com.zte.uedm.pma.bean.HistoryAiBean> dischargeByBattery, List<com.zte.uedm.pma.bean.HistoryAiBean> chargeByBattery) throws ParseException {
        List<PeakShiftStatisticsBean> peakShiftStatisticsBeans = new ArrayList<>();
        Calendar cal = Calendar.getInstance();

        // 用batteryParentId在allResourceBase中获取资源信息并获取资源信息中的parentId
        ResourceBaseEntity resourceBaseEntity = allResourceBase.stream()
                .filter(bean -> batteryParentId.equals(bean.getId()))
                .findFirst().orElse(null);
        if (resourceBaseEntity == null) {
            log.error("calculateKwhGainByTimePeriodDetail resource not found, batteryParentId:{}", batteryParentId);
            return peakShiftStatisticsBeans;
        }
        String parentId = resourceBaseEntity.getParentId();

        log.info("calculateKwhGainByTimePeriodDetail batteryParentId:{},parentId:{},day:{},dischargeByBattery:{},chargeByBattery:{}",
                batteryParentId, parentId, day, JSON.toJSONString(dischargeByBattery), JSON.toJSONString(chargeByBattery));
        LocalDate localDate = LocalDate.parse(day + ZERO_TIME, DateTimeFormatter.ofPattern(DATE_FORMAT_0));
        Map<Integer, Function<IntervalStrategyDetailModeBean, List<PeakShiftStrategyTimePeriodDetailsBean>>> modeHandlers = new HashMap<>();
        modeHandlers.put(MODE_HOLIDAY, this::handleHolidayMode);
        modeHandlers.put(MODE_DAY, this::handleDayMode);
        modeHandlers.put(MODE_WEEK, bean -> handleWeekMode(bean, localDate));
        modeHandlers.put(MODE_MONTH, bean -> handleMonthMode(bean, localDate));
        Map<String, PeakShiftStatisticsBean> mergedStatisticsMap = new HashMap<>(); // 用于合并的 Map

        for (IntervalStrategyDetailModeBean intervalStrategyBean : intervalStrategyBeans) {
            log.info("calculateKwhGainByTimePeriodDetail intervalStrategyBean:{}", JSON.toJSONString(intervalStrategyBean));
            Integer mode = intervalStrategyBean.getMode();
            String detail = intervalStrategyBean.getDetail();
            String scopeStrategyId = intervalStrategyBean.getScopeStrategyId();
            Function<IntervalStrategyDetailModeBean, List<PeakShiftStrategyTimePeriodDetailsBean>> function = modeHandlers.get(mode);
            if (mode == null || org.apache.commons.lang3.StringUtils.isBlank(detail) || function == null) {
                log.warn("filterPeriodDetailFromStrategy mode or detail is empty {}", JSON.toJSONString(intervalStrategyBean));
                continue;
            }
            Optional<List<PeakShiftStrategyTimePeriodDetailsBean>> result = Optional.ofNullable(function.apply(intervalStrategyBean));
            if (result.isPresent()) {
                for (PeakShiftStrategyTimePeriodDetailsBean timePeriodDetail : result.get()) {
                    // 时段开始与结束,格式：HH:mm
                    String strategyBegintime = timePeriodDetail.getBeginTime();
                    String strategyEndtime = timePeriodDetail.getEndTime();

                    // 将时段开始与结束时间格式化为yyyy-MM-dd HH:mm:ss
                    cal.setTime(new SimpleDateFormat(PeakShiftServiceImpl.DATE_FORMAT_5).parse(day + " " + strategyBegintime));
                    String startInterval = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_BASE).format(cal.getTime());
                    cal.setTime(new SimpleDateFormat(PeakShiftServiceImpl.DATE_FORMAT_5).parse(day + " " + strategyEndtime));
                    String endInterval = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_BASE).format(cal.getTime());

                    log.debug("startInterval:{},endInterval:{}", startInterval, endInterval);

                    // 此时段电池的累计充放电量
                    double dischargeSum = calculateDataByInterval(dischargeByBattery, startInterval, endInterval);
                    double chargeSum = calculateDataByInterval(chargeByBattery, startInterval, endInterval);

                    int strategyType = timePeriodDetail.getStrategyType();
                    String batteryId = batteryParentId; // 假设 batteryParentId 是 bean 的 id
                    String mapKey = batteryId + "-" + strategyType; // 组合 key，包括 id 和 strategyType

                    // 检查是否已存在该 key 的统计数据
                    PeakShiftStatisticsBean peakShiftStatisticsBean = mergedStatisticsMap.get(mapKey);
                    if (peakShiftStatisticsBean == null) {
                        // 如果不存在，创建新 bean
                        peakShiftStatisticsBean = new PeakShiftStatisticsBean();
                        peakShiftStatisticsBean.setSiteId(parentId);
                        peakShiftStatisticsBean.setId(batteryId);
                        peakShiftStatisticsBean.setStrategyType(strategyType);
                        peakShiftStatisticsBean.setChargeValue(chargeSum); // 初始化充电值
                        peakShiftStatisticsBean.setDischargeValue(dischargeSum); // 初始化放电值
                        peakShiftStatisticsBean.setStrategyPeriodStart(startInterval);
                        peakShiftStatisticsBean.setStrategyPeriodEnd(endInterval);
                        peakShiftStatisticsBean.setStrategyId(scopeStrategyId);
                        peakShiftStatisticsBean.setDatestr(day);
                        peakShiftStatisticsBean.setStrategyMode(mode);
                        peakShiftStatisticsBean.setGmtCreate(dateTimeService.getCurrentTime());
                        // 初始化其他字段
                        peakShiftStatisticsBean.setDischargeTimes(0);
                        peakShiftStatisticsBean.setDischargeDuration(0.0);
                        peakShiftStatisticsBean.setDischargeDurationMax(0.0);
                        peakShiftStatisticsBean.setChargeTimes(0);
                        peakShiftStatisticsBean.setChargeDuration(0.0);
                        peakShiftStatisticsBean.setFreeDuration(0.0);
                        peakShiftStatisticsBean.setLlvd1Times(0);
                        mergedStatisticsMap.put(mapKey, peakShiftStatisticsBean);
                    } else {
                        // 如果已存在，则将当前的充放电量相加
                        peakShiftStatisticsBean.setChargeValue(peakShiftStatisticsBean.getChargeValue() + chargeSum);
                        peakShiftStatisticsBean.setDischargeValue(peakShiftStatisticsBean.getDischargeValue() + dischargeSum);
                    }
                }
            }
        }

        peakShiftStatisticsBeans.addAll(mergedStatisticsMap.values());

        return peakShiftStatisticsBeans;
    }

    public double calculateDataByInterval(List<com.zte.uedm.pma.bean.HistoryAiBean> dataList, String start, String end) {
        double result = 0.0;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(TimeFormatConstants.TIME_FORMAT_BASE);
        LocalDateTime startInterval = LocalDateTime.parse(start, dateTimeFormatter);
        LocalDateTime endInterval = LocalDateTime.parse(end, dateTimeFormatter);
        log.info("dealDataByInterval startInterval:{},endInterval:{}", startInterval, endInterval);
        for (com.zte.uedm.pma.bean.HistoryAiBean historyAiBean : dataList) {
            try {
                String incValue = historyAiBean.getIncValue();
                String timeRange = historyAiBean.getTimeRange();
                if (org.apache.commons.lang3.StringUtils.isBlank(timeRange) || org.apache.commons.lang3.StringUtils.isBlank(incValue)) {
                    log.warn("dealDataByInterval time range is blank {}", JSON.toJSONString(historyAiBean));
                    continue;
                }
                String[] times = timeRange.split(" - ");
                if (times.length != 2) {
                    log.warn("dealDataByInterval time range size is not 2 {}", JSON.toJSONString(historyAiBean));
                    continue;
                }
                LocalDateTime startTime = LocalDateTime.parse(times[0], dateTimeFormatter);
                LocalDateTime endTime = LocalDateTime.parse(times[1], dateTimeFormatter);
                log.info("dealDataByInterval startTime:{},endTime:{},incValue:{}", startTime, endTime, incValue);
                if (startTime.isAfter(endInterval) || endTime.isBefore(startInterval)) {
                    continue;
                }
                double value = calculateOneByTime(startInterval, endInterval, startTime, endTime, incValue);
                result = result + value;
            } catch (Exception e) {
                log.error("Error processing data: ", e);
            }
        }
        log.info("cutDataByInterval: startInterval:{},endInterval:{},res:{}", startInterval, endInterval, result);
        return result;
    }

    public double calculateOneByTime(LocalDateTime startInterval, LocalDateTime endInterval,
                                     LocalDateTime startTime, LocalDateTime endTime, String incValue) {
        double value = Double.parseDouble(incValue);
        // 时段开始时间 <= 数据开始时间
        if (!startInterval.isAfter(startTime)) {
            // 时段结束时间 < 数据结束时间
            if (endInterval.isBefore(endTime)) {
                BigDecimal duration = new BigDecimal((endInterval.toEpochSecond(ZoneOffset.UTC) - startTime.toEpochSecond(ZoneOffset.UTC)) * 1000);
                BigDecimal totalDuration = new BigDecimal((endTime.toEpochSecond(ZoneOffset.UTC) - startTime.toEpochSecond(ZoneOffset.UTC)) * 1000);
                value = duration.multiply(new BigDecimal(value)).divide(totalDuration, 8, RoundingMode.HALF_UP).doubleValue();
                log.debug("startInterval <= startTime and endInterval < endTime {}-{},{}-{},{}-{}", startInterval, endInterval, startTime, endTime, incValue, value);
            }
        } else {
            // 时段开始时间 > 数据开始时间 && 时段结束时间 >= 数据结束时间
            if (!endInterval.isBefore(endTime)) {
                BigDecimal duration = new BigDecimal((endTime.toEpochSecond(ZoneOffset.UTC) - startInterval.toEpochSecond(ZoneOffset.UTC)) * 1000);
                BigDecimal totalDuration = new BigDecimal((endTime.toEpochSecond(ZoneOffset.UTC) - startTime.toEpochSecond(ZoneOffset.UTC)) * 1000);
                value = duration.multiply(new BigDecimal(value)).divide(totalDuration, 8, RoundingMode.HALF_UP).doubleValue();
                log.debug("startInterval > startTime and endInterval >= endTime {}-{},{}-{},{}-{}", startInterval, endInterval, startTime, endTime, incValue, value);
            } else {
                // 时段开始时间 > 数据开始时间 && 时段结束时间 < 数据结束时间
                BigDecimal duration = new BigDecimal((endInterval.toEpochSecond(ZoneOffset.UTC) - startInterval.toEpochSecond(ZoneOffset.UTC)) * 1000);
                BigDecimal totalDuration = new BigDecimal((endTime.toEpochSecond(ZoneOffset.UTC) - startTime.toEpochSecond(ZoneOffset.UTC)) * 1000);
                value = duration.multiply(new BigDecimal(value)).divide(totalDuration, 8, RoundingMode.HALF_UP).doubleValue();
                log.debug("startInterval > startTime and endInterval < endTime {}-{},{}-{},{}-{}", startInterval, endInterval, startTime, endTime, incValue, value);
            }
        }
        return value;
    }

    public List<PeakShiftStrategyTimePeriodDetailsBean> handleHolidayMode(IntervalStrategyDetailModeBean bean) {
        return JSON.parseArray(bean.getDetail(), PeakShiftStrategyTimePeriodDetailsBean.class);
    }

    public List<PeakShiftStrategyTimePeriodDetailsBean> handleDayMode(IntervalStrategyDetailModeBean bean) {
        return JSON.parseArray(bean.getDetail(), PeakShiftStrategyTimePeriodDetailsBean.class);
    }

    public List<PeakShiftStrategyTimePeriodDetailsBean> handleWeekMode(IntervalStrategyDetailModeBean bean, LocalDate localDate) {
        String weekStr = bean.getWeekStr();
        int week = localDate.getDayOfWeek().getValue();
        if (org.apache.commons.lang3.StringUtils.contains(weekStr, String.valueOf(week))) {
            return JSON.parseArray(bean.getDetail(), PeakShiftStrategyTimePeriodDetailsBean.class);
        }
        return null;
    }

    public List<PeakShiftStrategyTimePeriodDetailsBean> handleMonthMode(IntervalStrategyDetailModeBean bean, LocalDate localDate) {
        int month = localDate.getMonthValue();
        if (month >= bean.getPeriodStart() && month <= bean.getPeriodEnd()) {
            return JSON.parseArray(bean.getDetail(), PeakShiftStrategyTimePeriodDetailsBean.class);
        }
        return null;
    }
    /* Ended by AICoder, pid:26e1b7a741v696b14ab20861d36c85746eb526c7 */

}
