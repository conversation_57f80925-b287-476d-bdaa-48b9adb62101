package com.zte.uedm.battery.pv.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "状态统计汇总查询-入参")
public class SolarMonitorStatisticsQueryBean {
    @ApiModelProperty(value = "位置")
    private String logicGroupId;
    @ApiModelProperty(value = "查询名称")
    private String name;
}
