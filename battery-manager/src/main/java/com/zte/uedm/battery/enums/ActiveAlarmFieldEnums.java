package com.zte.uedm.battery.enums;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum ActiveAlarmFieldEnums {
    ME("me"),
    ALARM_CODE("alarmcode"),
    ALARM_RAISED_TIME("alarmraisedtime"),
    ALARM_ACKSTATES("ackstate"),
    ALARM_SERVERITIES("perceivedseverity"),
    ALARM_VISIBLES("visible");

    private String field;

    ActiveAlarmFieldEnums(String field){
        this.field = field;
    }
}
