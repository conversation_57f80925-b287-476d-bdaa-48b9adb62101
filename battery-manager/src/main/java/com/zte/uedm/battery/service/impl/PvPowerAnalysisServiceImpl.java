package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.opti.domain.service.AuthorizationService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.LogicIdAndMocQueryBean;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.bean.pv.*;
import com.zte.uedm.battery.dao.PvGenerConsumRecordDao;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.PvGenerConsumRecordService;
import com.zte.uedm.battery.service.PvPowerAnalysisService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.battery.util.constant.PvConstant;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.enums.TimeGrainEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ Author     ：10260977
 * @ Date       ：14:50 2021/3/16
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
@Service
@Slf4j
public class PvPowerAnalysisServiceImpl implements PvPowerAnalysisService
{
    @Autowired
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;

    @Autowired
    private I18nUtils i18nUtilsService;

    @Autowired
    private PvGenerConsumRecordMapper pvGenerConsumRecordMapper;

    @Autowired
    private PvGenerConsumRecordService pvGenerConsumRecordService;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private PvGenerConsumRecordDao pvGenerConsumRecordDao;
    @Autowired
    private DeviceCacheManager deviceCacheManager;
    @Autowired
    private AuthorizationService authorizationService;

    @Override
    public List<PvPowerAnalysisResponseBean> pvPowerAnalysisOverview(PvCommonQueryRequestBean pvCommonQueryRequestBean,
            String userName,Integer pageNo, Integer pageSize, String languageOption) throws UedmException
    {
        Map<String, String> pvTypeMocMap = PvConstant.getPvTypeMocMap();
        //获取对应moc
        String moc = pvTypeMocMap.get(pvCommonQueryRequestBean.getPvTypes().get(0));
        Map<String, String> headers = HeaderUtils.buildUserNameHeaders(Tools.escape(userName));
        //结果集
        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans;
        List<String> positions = pvCommonQueryRequestBean.getPositions();
        //分域，过滤无权限的站点
        positions = authorizationService.getFullPermissionByResIds(positions);
        if (CollectionUtils.isEmpty(positions)) {
            log.error("No access permission");
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = configurationManagerRpcImpl
                    .selectMoByMocAndPosition(new LogicIdAndMocQueryBean(positions, moc),
                            headers);
            if(CollectionUtils.isEmpty(monitorObjectDsBeans))
            {
                log.error("PvPowerAnalysisServiceImpl pvPowerAnalysisOverview user {} no authority pv.", userName);
                return new ArrayList<>();
            }
            List<String> moIds = monitorObjectDsBeans.stream().map(MonitorObjectDsBean::getId).distinct()
                    .collect(Collectors.toList());
            //id-path-map
            Map<String, MonitorObjectDsBean> idBeanMap = monitorObjectDsBeans.stream().filter(bean -> StringUtils.isNotBlank(bean.getId()))
                    .collect(Collectors.toMap(MonitorObjectDsBean::getId, Function.identity(), (key1, key2) -> key2));

            PvCommonQueryMapperBean pvCommonQueryMapperBean = new PvCommonQueryMapperBean();
            BeanUtils.copyProperties(pvCommonQueryRequestBean, pvCommonQueryMapperBean);
            pvCommonQueryMapperBean.setPvIds(moIds);

            //是否设定粒度 d m y 无粒度要求  默认天
            if(StringUtils.isBlank(pvCommonQueryRequestBean.getGrain()))
            {
                pvCommonQueryMapperBean.setGrain(TimeGrainEnum.d.getId());
                pvPowerAnalysisResponseBeans = pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(moIds, pvCommonQueryMapperBean);
            }
            else
            {
                pvPowerAnalysisResponseBeans = pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(moIds, pvCommonQueryMapperBean);
            }
            if(CollectionUtils.isEmpty(pvPowerAnalysisResponseBeans))
            {
                log.error("PvPowerAnalysisServiceImpl pvPowerAnalysisOverview no statisfy pv from db!.");
                return new ArrayList<>();
            }
            //pv关联站点信息
            //todo:替换
         /*   Map<String, List<MonitorObjectDsBean>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                    .getsiteRelatedPvMap(); */
            Map<String, List<DeviceDSEntity>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                    .getsiteRelatedPvMap_new();
           // Map<String, SiteDsBean> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeans();
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeanMap();
            if(allSiteRelatedPvMaps.size() < 1 || siteIdRelationSiteDsBeansMap.size() < 1)
            {
                log.error("PvPowerAnalysisServiceImpl pvPowerAnalysisOverview site pv relationship dont exists.");
                return new ArrayList<>();
            }

            //pvid和站点名称 map
            Map<String, String> pvIdSiteNameMap = getPvIdSiteNameMap(allSiteRelatedPvMaps, siteIdRelationSiteDsBeansMap);
            log.info("PvPowerAnalysisServiceImpl pvPowerAnalysisOverview pvIdSiteNameMap size is {}, pvPowerAnalysisResponseBeans"
                    + " is {}.", pvIdSiteNameMap.size(), pvPowerAnalysisResponseBeans.size());
            //补充太阳能信息
            pvPowerAnalysisResponseBeans = buildPvInfo(moc, pvCommonQueryRequestBean, idBeanMap, pvPowerAnalysisResponseBeans, pvIdSiteNameMap, languageOption);
            //补充方阵信息
            pvPowerAnalysisResponseBeans = buildSpcuInfo(moc, pvCommonQueryRequestBean.getName(), idBeanMap, pvPowerAnalysisResponseBeans, pvIdSiteNameMap, languageOption);
            //补充极板信息
            pvPowerAnalysisResponseBeans = buildSpuInfo(moc, pvCommonQueryRequestBean.getName(), idBeanMap, pvPowerAnalysisResponseBeans, pvIdSiteNameMap, languageOption);
            //排序
            sortByCondition(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
        }
        catch (Exception e)
        {
            log.error("PvPowerAnalysisServiceImpl pvPowerAnalysisOverview error", e);
            throw new UedmException(-200, "PvPowerAnalysisServiceImpl pvPowerAnalysisOverview error");
        }

        return pvPowerAnalysisResponseBeans;
    }

    @Override
    public PvTrendResponseBean pvPowerAnalysisTrend(PvTrendQueryRequestBean pvTrendQueryRequestBean,
            String userName, Integer pageNo, Integer pageSize, String languageOption) throws UedmException
    {
        //分域
        List<String> authList = authorizationService.getFullPermissionByResIds(Collections.singletonList(pvTrendQueryRequestBean.getPvId()));
        if (CollectionUtils.isEmpty(authList)) {
            log.error("No access permission : {}", pvTrendQueryRequestBean.getPvId());
            throw new UedmException(-635, "{\"zh_CN\":\"无访问权限\",\"en_US\":\"No access permission\"}");
        }
        MonitorObjectBean monitorObject = configurationService.getMonitorObjectById(pvTrendQueryRequestBean.getPvId());
        PvTrendResponseBean pvTrendResponseBean = new PvTrendResponseBean();
        pvTrendResponseBean.setPvId(pvTrendQueryRequestBean.getPvId());
        //名称
        pvTrendResponseBean.setName(monitorObject.getName());
        pvTrendResponseBean.setCompareYear(pvTrendQueryRequestBean.getCompareYear());
        //太阳能发用电阈值
        setTrendThreShold(pvTrendQueryRequestBean, monitorObject, pvTrendResponseBean);
        //选择当年的数据
        List<PvTrendDataBean> pvTrendDataCurrentBeans = pvGenerConsumRecordMapper.selectTrendRecordByCondition(pvTrendQueryRequestBean);
        if(CollectionUtils.isEmpty(pvTrendDataCurrentBeans))
        {
            log.error("PvPowerAnalysisServiceImpl pvPowerAnalysisTrend current year data is empty");
            return pvTrendResponseBean;
        }
        //同比年和日月粒度
        pvTrendDataCurrentBeans = getTrendDataWithDayOrMonth(pvTrendQueryRequestBean, pvTrendDataCurrentBeans);
        //同比年和年粒度
        getTrendDataWithYear(pvTrendQueryRequestBean, pvTrendDataCurrentBeans);
        if(MocOptional.PV.getId().equals(monitorObject.getMoc()))
        {
            pvTrendDataCurrentBeans.stream().forEach(bean->bean.setThreshold(pvTrendResponseBean.getThreshold()));
        }
        //升序
        pvTrendDataCurrentBeans.sort(Comparator.comparing(PvTrendDataBean::getRecordDate));
        pvTrendResponseBean.setData(pvTrendDataCurrentBeans);
        return pvTrendResponseBean;
    }

    /**
     * 日月粒度和同比年
     * @param pvTrendQueryRequestBean
     * @param pvTrendDataCurrentBeans
     * @return
     */
    @NotNull
    private List<PvTrendDataBean> getTrendDataWithDayOrMonth(PvTrendQueryRequestBean pvTrendQueryRequestBean,
            List<PvTrendDataBean> pvTrendDataCurrentBeans)
    {
        if(StringUtils.isNotBlank(pvTrendQueryRequestBean.getCompareYear())&&!TimeGrainEnum.isYear(pvTrendQueryRequestBean.getGrain()))
        {
            //替换 年
            String startTime = pvTrendQueryRequestBean.getStartTime();
            startTime = pvTrendQueryRequestBean.getCompareYear() + startTime.substring(4);
            String endTime = pvTrendQueryRequestBean.getEndTime();
            endTime = pvTrendQueryRequestBean.getCompareYear() + endTime.substring(4);
            pvTrendQueryRequestBean.setStartTime(startTime.compareTo(endTime) >= 0 ? endTime : startTime);
            pvTrendQueryRequestBean.setEndTime(startTime.compareTo(endTime) < 0 ? endTime : startTime);
            List<PvTrendDataBean> pvTrendDataCompareBeans = pvGenerConsumRecordMapper.selectTrendRecordByCondition(pvTrendQueryRequestBean);
            log.info("PvPowerAnalysisServiceImpl pvPowerAnalysisTrend compare year data size is {}",
                    pvTrendDataCompareBeans.size());
            Map<String, PvTrendDataBean> recordCurrentMap = pvTrendDataCurrentBeans.stream().collect(Collectors.toMap(PvTrendDataBean::getRecordDate, Function.identity()));
            Map<String, PvTrendDataBean> recordCompareMap = pvTrendDataCompareBeans.stream().collect(Collectors.toMap(bean -> bean.getRecordDate().substring(5), Function.identity()));
            for (Entry<String, PvTrendDataBean> map : recordCurrentMap.entrySet())
            {
                PvTrendDataBean compareBean = recordCompareMap.get(map.getKey().substring(5));
                PvTrendDataBean currentBean = map.getValue();
                if (null != compareBean)
                {
                    currentBean.setComparePvGeneration(compareBean.getPvGeneration());
                    currentBean.setCompareDcConsumtion(compareBean.getDcloadConsumption());
                    currentBean.setCompareGenerConsumRatio(compareBean.getGenerConsumRatio());
                }
                else
                {
                    currentBean.setComparePvGeneration(BatteryConstant.DEFAULT_DOUBLE_VALUE_STR);
                    currentBean.setCompareDcConsumtion(BatteryConstant.DEFAULT_DOUBLE_VALUE_STR);
                    currentBean.setCompareGenerConsumRatio(BatteryConstant.DEFAULT_DOUBLE_VALUE_STR);
                }
            }
            pvTrendDataCurrentBeans = new ArrayList<>(recordCurrentMap.values());
        }
        return pvTrendDataCurrentBeans;
    }

    /**
     * 同比年和年粒度
     * @param pvTrendQueryRequestBean
     * @param pvTrendDataCurrentBeans
     * @return
     */
    private void getTrendDataWithYear(PvTrendQueryRequestBean pvTrendQueryRequestBean, List<PvTrendDataBean> pvTrendDataCurrentBeans)
    {
        if(StringUtils.isNotBlank(pvTrendQueryRequestBean.getCompareYear())&&TimeGrainEnum.isYear(pvTrendQueryRequestBean.getGrain()))
        {
            //替换 年
            String startTime = pvTrendQueryRequestBean.getCompareYear();
            String endTime = pvTrendQueryRequestBean.getCompareYear();
            pvTrendQueryRequestBean.setStartTime(startTime);
            pvTrendQueryRequestBean.setEndTime(endTime);
            List<PvTrendDataBean> pvTrendDataCompareBeans = pvGenerConsumRecordMapper.selectTrendRecordByCondition(pvTrendQueryRequestBean);
            log.info("PvPowerAnalysisServiceImpl getTrendDataWithYear compare year data size is {}",
                    pvTrendDataCompareBeans.size());
            //同比年一条数据
            if(CollectionUtils.isEmpty(pvTrendDataCompareBeans))
            {
                log.info("PvPowerAnalysisServiceImpl getTrendDataWithYear compare year data  is empty!");
                return;
            }
            List<String> recordDates = pvTrendDataCurrentBeans.stream().map(PvTrendDataBean::getRecordDate).collect(
                    Collectors.toList());
            pvTrendDataCurrentBeans.stream().forEach(bean -> {
                bean.setCompareDcConsumtion(pvTrendDataCompareBeans.get(0).getDcloadConsumption());
                bean.setComparePvGeneration(pvTrendDataCompareBeans.get(0).getPvGeneration());
                bean.setCompareGenerConsumRatio(pvTrendDataCompareBeans.get(0).getGenerConsumRatio());
            });
            if(!recordDates.contains(pvTrendDataCompareBeans.get(0).getRecordDate()))
            {
                //选择年跨度不包含同比年，加入结果集
                PvTrendDataBean pvTrendDataBean = pvTrendDataCompareBeans.get(0);
                pvTrendDataBean.setCompareDcConsumtion(pvTrendDataBean.getDcloadConsumption());
                pvTrendDataBean.setComparePvGeneration(pvTrendDataBean.getPvGeneration());
                pvTrendDataBean.setCompareGenerConsumRatio(pvTrendDataBean.getGenerConsumRatio());
                pvTrendDataCurrentBeans.add(pvTrendDataCompareBeans.get(0));
            }
            //年升序
            pvTrendDataCurrentBeans.sort(Comparator.comparing(PvTrendDataBean::getRecordDate));
        }
    }

    private void setTrendThreShold(PvTrendQueryRequestBean pvTrendQueryRequestBean, MonitorObjectBean monitorObject,
            PvTrendResponseBean pvTrendResponseBean) throws UedmException
    {
        if(MocOptional.PV.getId().equals(monitorObject.getMoc()))
        {
            //pv关联站点信息
            //todo：替换
           /* Map<String, List<MonitorObjectDsBean>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                    .getsiteRelatedPvMap();    */
            Map<String, List<DeviceDSEntity>> allSiteRelatedPvMaps = realGroupRelationSiteUtils
                    .getsiteRelatedPvMap_new();
           // Map<String, SiteDsBean> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeans();
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = realGroupRelationSiteUtils.getAllSiteDsBeanMap();
            if(allSiteRelatedPvMaps.size() > 0 || siteIdRelationSiteDsBeansMap.size() > 0)
            {
                //pvid 阈值
                Map<String, Double> thresholdMap = pvGenerConsumRecordService.supplyMapSholdValue();
                Double threshold = thresholdMap.get(pvTrendQueryRequestBean.getPvId());
                pvTrendResponseBean.setThreshold(threshold == null ? "":threshold.toString());
            }
        }
    }

    @NotNull
    private Map<String, String> getPvIdSiteNameMap(Map<String, List<DeviceDSEntity>> allSiteRelatedPvMaps,
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap)
    {
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        for(Entry<String, List<DeviceDSEntity>> map : allSiteRelatedPvMaps.entrySet())
        {
            List<DeviceDSEntity> monitorBeans = map.getValue();
            for(DeviceEntity bean: monitorBeans)
            {
                FieldEntity siteDsBean = siteIdRelationSiteDsBeansMap.get(map.getKey());
                if(null != siteDsBean)
                {
                    pvIdSiteNameMap.put(bean.getId(), siteDsBean.getName());
                }
            }
        }
        return pvIdSiteNameMap;
    }


    private void sortByCondition(PvCommonQueryRequestBean pvCommonQueryRequestBean,
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans) throws Exception
    {
        if(StringUtils.isBlank(pvCommonQueryRequestBean.getOrder())&&StringUtils.isBlank(pvCommonQueryRequestBean.getGrain()))
        {
            log.info("PvPowerAnalysisServiceImpl sortByCondition both para is empty!");
            /* Started by AICoder, pid:o5c7bt54968ec881418b0902e0a8b8076bf6aece */
            pvPowerAnalysisResponseBeans.sort(Comparator.comparing(PvPowerAnalysisResponseBean::getPosition,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getSiteName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvarrayName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvmoduleName,
                    Comparator.nullsFirst(String::compareTo)));
            /* Ended by AICoder, pid:o5c7bt54968ec881418b0902e0a8b8076bf6aece */
        }
        if(StringUtils.isBlank(pvCommonQueryRequestBean.getOrder())&&StringUtils.isNotBlank(pvCommonQueryRequestBean.getGrain()))
        {
            log.info("PvPowerAnalysisServiceImpl sortByCondition order is empty grain is {}!", pvCommonQueryRequestBean.getGrain());
            /* Started by AICoder, pid:b3ca0gd0d6x1d971433f0bbe609cbc037c87f855 */
            pvPowerAnalysisResponseBeans.sort(Comparator.comparing(PvPowerAnalysisResponseBean::getRecordDate,
                    Comparator.nullsFirst(String::compareTo)).reversed().thenComparing(PvPowerAnalysisResponseBean::getPosition,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getSiteName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvarrayName,
                    Comparator.nullsFirst(String::compareTo)).thenComparing(PvPowerAnalysisResponseBean::getPvmoduleName,
                    Comparator.nullsFirst(String::compareTo)));
            /* Ended by AICoder, pid:b3ca0gd0d6x1d971433f0bbe609cbc037c87f855 */
        }
        sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
    }

    /**
     * 通过发用电比率排序
     * @param pvCommonQueryRequestBean
     * @param pvPowerAnalysisResponseBeans
     */
    /* Started by AICoder, pid:038319fa9d697eb14b5b0ba6f0c0673c30043764 */
    public void sortByGenerConsumRatio(PvCommonQueryRequestBean pvCommonQueryRequestBean,
                                       List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans) {
        if (StringUtils.isNotBlank(pvCommonQueryRequestBean.getOrder()) && PvConstant.GENER_CONSUM_RATIO.equals(
                pvCommonQueryRequestBean.getOrder())) {
            sortByGenerConsum(pvCommonQueryRequestBean,pvPowerAnalysisResponseBeans);
        }
        if (StringUtils.isNotBlank(pvCommonQueryRequestBean.getOrder()) && PvConstant.SOLAR_GENERATION.equals(
                pvCommonQueryRequestBean.getOrder())) {
            sortByPvGeneration(pvCommonQueryRequestBean,pvPowerAnalysisResponseBeans);
        }
        if (StringUtils.isNotBlank(pvCommonQueryRequestBean.getOrder()) && PvConstant.DCLOAD_CONSUMPTION.equals(
                pvCommonQueryRequestBean.getOrder())) {
            sortByDcloadConsumption(pvCommonQueryRequestBean,pvPowerAnalysisResponseBeans);
        }
    }

    public void sortByGenerConsum(PvCommonQueryRequestBean pvCommonQueryRequestBean,
                                  List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans) {

        if (SortEnum.asc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, Comparator.comparingDouble(o -> Double.parseDouble(o.getGenerConsumRatio())));
        }
        if (SortEnum.desc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, (o1, o2) -> {
                double v1 = Double.parseDouble(o1.getGenerConsumRatio());
                double v2 = Double.parseDouble(o2.getGenerConsumRatio());
                return Double.compare(v2, v1); // 降序排列
            });
        }
    }

    public void sortByPvGeneration(PvCommonQueryRequestBean pvCommonQueryRequestBean,
                                   List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans) {
        if (SortEnum.asc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, Comparator.comparingDouble(o -> Double.parseDouble(o.getPvGeneration())));
        }
        if (SortEnum.desc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, (o1, o2) -> {
                double v1 = Double.parseDouble(o1.getPvGeneration());
                double v2 = Double.parseDouble(o2.getPvGeneration());
                return Double.compare(v2, v1); // 降序排列
            });
        }

    }

    public void sortByDcloadConsumption(PvCommonQueryRequestBean pvCommonQueryRequestBean,
                                        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans) {
        if (SortEnum.asc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, Comparator.comparingDouble(o -> Double.parseDouble(o.getDcloadConsumption())));
        }
        if (SortEnum.desc.getId().equals(pvCommonQueryRequestBean.getSort())) {
            Collections.sort(pvPowerAnalysisResponseBeans, (o1, o2) -> {
                double v1 = Double.parseDouble(o1.getDcloadConsumption());
                double v2 = Double.parseDouble(o2.getDcloadConsumption());
                return Double.compare(v2, v1); // 降序排列
            });
        }

    }
    /**
     * 通过moc和path获取位置
     * @param monitorObjectDsBean
     * @return
     */
    public String getMoPositionByPath(MonitorObjectDsBean monitorObjectDsBean, String siteName)
    {
        String position = "";
        if(StringUtils.isEmpty(monitorObjectDsBean.getPath()) || null== siteName)
        {
            return position;
        }
        //通用处理
        String path = monitorObjectDsBean.getPath();
        int index = path.indexOf(siteName);
        position = path.substring(0, index)+siteName;
        return position;
    }

    public List<PvPowerAnalysisResponseBean> buildSpuInfo(String moc, String name, Map<String, MonitorObjectDsBean> idBeanMap,
                                                          List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans, Map<String, String> pvIdSiteNameMap, String languageOption) {
        //spu
        if (!StringUtils.equals(MocOptional.SPU.getId(), moc)) {
            return pvPowerAnalysisResponseBeans;
        }

        List<DeviceEntity> deviceByMoc = deviceCacheManager.getDeviceByMoc(new HashSet<>(Arrays.asList(MocOptional.SPCU.getId(), MocOptional.SPU.getId(), MocOptional.PV.getId())));
        Map<String, DeviceEntity> deviceMap = deviceByMoc.stream()
                .collect(Collectors.toMap(DeviceEntity::getId, entity -> entity));

        /* Started by AICoder, pid:ea019la61emd7fe1466a090af017ec6b76a2f18a */
        List<PvPowerAnalysisResponseBean> spuFilterResults = pvPowerAnalysisResponseBeans.parallelStream()
                .filter(bean -> {
                    MonitorObjectDsBean monitorObjectDsBean = idBeanMap.get(bean.getPvId());
                    return monitorObjectDsBean != null;
                })
                .map(bean -> {
                    MonitorObjectDsBean monitorObjectDsBean = idBeanMap.get(bean.getPvId());
                    if (monitorObjectDsBean != null) {
                        String[] topo = monitorObjectDsBean.getTopo();
                        List<String> positions;
                        String siteName;
                        if (topo != null && topo.length > 0) {

                            List<DeviceEntity> topoEntity = Arrays.asList(topo).stream()
                                    .map(topoId -> deviceMap.get(topoId))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());

                            List<DeviceEntity> spcuEntities = topoEntity.stream()
                                    .filter(device -> device.getMoc().equals(MocOptional.SPCU.getId()))
                                    .collect(Collectors.toList());

                            if (spcuEntities.size() > 0) {
                                String[] topoPv = spcuEntities.get(0).getTopo();

                                List<DeviceEntity> topoPvEntity = Arrays.asList(topoPv).stream()
                                        .map(topoId -> deviceMap.get(topoId))
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList());

                                List<DeviceEntity> pVEntities = topoPvEntity.stream()
                                        .filter(device -> device.getMoc().equals(MocOptional.PV.getId()))
                                        .collect(Collectors.toList());

                                bean.setPvarrayName(spcuEntities.get(0).getName());
                                if (pVEntities != null && pVEntities.size() > 0) {
                                    bean.setPvName(pVEntities.get(0).getName());
                                }
                            }

                            String path = monitorObjectDsBean.getPath();
                            String[] split = path.split(PvConstant.PATH_SPILIT_FLAG);
                            positions = Arrays.asList(split);
                            bean.setPvmoduleName(monitorObjectDsBean.getName());
                            siteName = positions.get(positions.size() - 2);
                        } else {
                            log.error("monitorObjectDsBean.getTopo() is null or empty.");
                            String path = monitorObjectDsBean.getPath();
                            String[] split = path.split(PvConstant.PATH_SPILIT_FLAG);
                            positions = Arrays.asList(split);
                            bean.setPvmoduleName(monitorObjectDsBean.getName());
                            siteName = positions.get(positions.size() - 2);
                        }

                        bean.setSiteName(siteName);
                        bean.setPosition(getMoPositionByPath(monitorObjectDsBean, siteName));
                    }
                    return bean;
                })
                .collect(Collectors.toList());

        if (StringUtils.isNotBlank(name)) {
            spuFilterResults = spuFilterResults.parallelStream()
                    .filter(bean -> bean.getPvmoduleName().contains(name))
                    .collect(Collectors.toList());
        }

        /* Ended by AICoder, pid:ea019la61emd7fe1466a090af017ec6b76a2f18a */
        return spuFilterResults;
    }

    public List<PvPowerAnalysisResponseBean>
    buildSpcuInfo(String moc, String name, Map<String, MonitorObjectDsBean> idBeanMap,
                                                           List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans, Map<String, String> pvIdSiteNameMap, String languageOption) {
        //spcu
        if (!StringUtils.equals(MocOptional.SPCU.getId(), moc)){
            return pvPowerAnalysisResponseBeans;
        }
        /* Started by AICoder, pid:f4fd1o43afm30ea142df0a2d20db365e13147cc9 */
        List<DeviceEntity> deviceByMoc = deviceCacheManager.getDeviceByMoc(new HashSet<>(Arrays.asList(MocOptional.SPCU.getId(), MocOptional.SPU.getId(), MocOptional.PV.getId())));
        Map<String, DeviceEntity> deviceMap = deviceByMoc.stream()
                .collect(Collectors.toMap(DeviceEntity::getId, entity -> entity));

        List<PvPowerAnalysisResponseBean> spcuFilterResults = pvPowerAnalysisResponseBeans.parallelStream()
                .filter(bean -> {
                    MonitorObjectDsBean monitorObjectDsBean = idBeanMap.get(bean.getPvId());
                    return monitorObjectDsBean != null;
                })
                .map(bean -> {
                    MonitorObjectDsBean monitorObjectDsBean = idBeanMap.get(bean.getPvId());
                    if (monitorObjectDsBean != null) {
                        String[] topo = monitorObjectDsBean.getTopo();
                        List<String> positions;
                        String siteName;

                        if (topo != null && topo.length > 0) {

                            List<DeviceEntity> topoEntity = Arrays.asList(topo).stream()
                                    .map(topoId -> deviceMap.get(topoId))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());

                            String path = monitorObjectDsBean.getPath();
                            String[] splitPath = path.split(PvConstant.PATH_SPILIT_FLAG);
                            positions = Arrays.asList(splitPath);

                            List<DeviceEntity> pvEntities = topoEntity.stream()
                                    .filter(device -> device.getMoc().equals(MocOptional.PV.getId()))
                                    .collect(Collectors.toList());

                            String pvName = Optional.ofNullable(pvEntities)
                                    .filter(entities -> !entities.isEmpty())
                                    .map(entities -> entities.get(0).getName())
                                    .orElse(null);

                            bean.setPvName(pvName);
                            siteName = positions.get(positions.size() - 2);
                        } else {
                            String path = monitorObjectDsBean.getPath();
                            String[] splitPath = path.split(PvConstant.PATH_SPILIT_FLAG);
                            positions = Arrays.asList(splitPath);
                            siteName = positions.get(positions.size() - 2);
                        }

                        bean.setSiteName(siteName);
                        bean.setPvarrayName(monitorObjectDsBean.getName());
                        bean.setPvmoduleName("");
                        bean.setPosition(getMoPositionByPath(monitorObjectDsBean, siteName));
                    }
                    return bean;
                })
                .collect(Collectors.toList());

        if (StringUtils.isNotBlank(name)) {
            spcuFilterResults = spcuFilterResults.parallelStream()
                    .filter(bean1 -> bean1.getPvarrayName().contains(name))
                    .collect(Collectors.toList());
        }

        /* Ended by AICoder, pid:f4fd1o43afm30ea142df0a2d20db365e13147cc9 */
        return spcuFilterResults;
    }

    public List<PvPowerAnalysisResponseBean> buildPvInfo(String moc, PvCommonQueryRequestBean pvCommonQueryRequestBean,
            Map<String, MonitorObjectDsBean> idBeanMap, List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans,
            Map<String, String> pvIdSiteNameMap, String languageOption) throws UedmException
    {
        List<PvPowerAnalysisResponseBean> pvFilterResults = new ArrayList<>();
        if(MocOptional.PV.getId().equals(moc))
        {
            for(PvPowerAnalysisResponseBean bean: pvPowerAnalysisResponseBeans)
            {
                //计算发用电比 不满足要求过滤
                if(!computeGenerConsumRatio(bean, pvCommonQueryRequestBean))
                {
                    continue;
                }
                else
                {
                    MonitorObjectDsBean monitorObjectDsBean = idBeanMap.get(bean.getPvId());
                    if(null != monitorObjectDsBean)
                    {
                        log.debug("PvPowerAnalysisServiceImpl buildPvInfo monitorObjectDsBean is {}", monitorObjectDsBean.toString());
                        bean.setPvName(monitorObjectDsBean.getName());
                        String siteName = pvIdSiteNameMap.get(bean.getPvId());
                        bean.setPosition(getMoPositionByPath(monitorObjectDsBean, siteName));
                        bean.setSiteName(siteName);
                        bean.setPvmoduleName("");
                        bean.setPvarrayName("");
                    }
                    pvFilterResults.add(bean);
                }

            }
            pvPowerAnalysisResponseBeans = pvFilterResults;
            log.info("PvPowerAnalysisServiceImpl buildPvInfo pvFilterResults is {}", pvFilterResults.size());
            if(StringUtils.isNotBlank(pvCommonQueryRequestBean.getName()))
            {
                pvPowerAnalysisResponseBeans = pvPowerAnalysisResponseBeans.stream().filter(bean->bean.getPvName().
                        contains(pvCommonQueryRequestBean.getName())).collect(Collectors.toList());
            }
        }
        return pvPowerAnalysisResponseBeans;
    }

    //计算发用电比
    public boolean computeGenerConsumRatio(PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean, PvCommonQueryRequestBean pvCommonQueryRequestBean)
    {
        Double ratioBegin = pvCommonQueryRequestBean.getRatioBegin();
        Double ratioEnd = pvCommonQueryRequestBean.getRatioEnd();

        String dcConsumtion = pvPowerAnalysisResponseBean.getDcloadConsumption();
        Double ratio = BatteryConstant.DEFAULT_DOUBLE_VALUE;
        //天粒度太阳能 比率已经计算 *100
        if(StringUtils.isNotEmpty(pvPowerAnalysisResponseBean.getGenerConsumRatio()))
        {
            ratio = Double.parseDouble(pvPowerAnalysisResponseBean.getGenerConsumRatio());
        }
        else
        {
            //实时计算
            if (!BatteryConstant.DEFAULT_VALUE.equals(pvPowerAnalysisResponseBean.getDcloadConsumption()))
            {
                BigDecimal one = new BigDecimal(pvPowerAnalysisResponseBean.getPvGeneration());
                BigDecimal two = new BigDecimal(dcConsumtion);
                BigDecimal divideResult = one.divide(two, 2, BigDecimal.ROUND_HALF_UP);
                ratio = divideResult.doubleValue();
            }
            ratio = ratio * 100;
        }
        pvPowerAnalysisResponseBean.setGenerConsumRatio(String.format(BatteryConstant.DEFAULT_STRING_FORMAT, ratio));
        if(null == ratioBegin && null == ratioEnd)
        {
            return true;
        }
        log.info("PvPowerAnalysisServiceImpl computeGenerConsumRatio ratio is {}", ratio);
        if (fliterByBeainAndEndRatio(ratioBegin, ratioEnd, ratio))
        {
            return true;
        }
        return false;

    }

    private boolean fliterByBeainAndEndRatio(Double ratioBegin, Double ratioEnd, Double ratio)
    {
        if(ratioBegin !=null && ratioEnd !=null)
        {
            if(ratioBegin <= ratio && ratioEnd >= ratio)
            {
                return true;
            }
        }
        if (filterBySigleGrain(ratioBegin, ratioEnd, ratio))
        {
            return true;
        }
        return false;
    }

    private boolean filterBySigleGrain(Double ratioBegin,
            Double ratioEnd, Double ratio)
    {
        if(ratioBegin !=null)
        {
            if(ratioBegin <= ratio)
            {
                log.info("PvPowerAnalysisServiceImpl computeGenerConsumRatio ratioBegin {}, ratio {}",ratioBegin, ratio);
                return true;
            }
        }
        if(ratioEnd !=null)
        {
            if(ratioEnd >= ratio)
            {
                log.info("PvPowerAnalysisServiceImpl computeGenerConsumRatio ratioEnd {}, ratio {}",ratioEnd, ratio);
                return true;
            }
        }
        return false;
    }
}
