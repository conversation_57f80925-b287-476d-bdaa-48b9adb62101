package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @ Author     ：10260977
 * @ Date       ：14:18 2022/4/21
 * @ Description：文件上传前端传入参数
 * @ Modified By：
 * @ Version: 1.0
 */
@Getter
@Setter
@ToString
public class UpDownloadFileHttpEntity
{
    /**
     * 文件同步是否替换
     */
    private Boolean isReplace;
    /**
     * 文件大写 单位M
     */
    private Long maxSize;
    /**
     * 文件展示的大小 kb M
     */
    @NotNull
    private String fileSize;
}
