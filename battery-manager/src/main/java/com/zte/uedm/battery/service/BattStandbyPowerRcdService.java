package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.BattStandbyPowerRcdBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;
import java.util.Map;

/**
 * 电池备电时长mapper
 *
 * <AUTHOR>
 */
public interface BattStandbyPowerRcdService {

    void insert(List<BattStandbyPowerRcdBean> beanList)throws UedmException;

    Integer update(BattStandbyPowerRcdBean bean)throws UedmException;

    List<BattStandbyPowerRcdBean> select(BattStandbyPowerRcdBean bean)throws UedmException;

    List<BattStandbyPowerRcdBean> selectStandbyList(List<String> spOids)throws UedmException;

    List<Map> countEvaluateNums(List<String> oids)throws UedmException;
}
