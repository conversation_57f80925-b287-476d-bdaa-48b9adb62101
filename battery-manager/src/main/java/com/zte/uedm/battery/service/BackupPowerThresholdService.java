package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryConfigAddDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryConfigUpdateDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryDetailDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.CategoryConfigAddVo;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.CategoryDetailVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BackupPowerThresholdService {

    /**
     * 查询分类设置-按条件查询
     * @param categoryDetailDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    PageInfo<CategoryDetailVo> selectByCondition(CategoryDetailDto categoryDetailDto, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 备电分类设置-新增
     * @param configAddDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    CategoryConfigAddVo battAddCategoryConfig(CategoryConfigAddDto configAddDto, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 备电分类设置-编辑
     * @param updateDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    List<String> battUpdateCategoryConfig(CategoryConfigUpdateDto updateDto, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 备电分类设置-删除
     * @param id
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    List<String> battDeleteCategoryConfig(String id, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 电池备电初始化阈值
     */
    void initBackupPowerThresholdValue();
}
