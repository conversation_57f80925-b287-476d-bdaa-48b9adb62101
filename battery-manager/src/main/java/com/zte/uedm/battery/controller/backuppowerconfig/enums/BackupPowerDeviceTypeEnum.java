package com.zte.uedm.battery.controller.backuppowerconfig.enums;

import com.zte.uedm.battery.enums.backuppower.BackupPowerApplicationSceneEnum;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */

/**
 * 备电--设备类型--枚举
 * <AUTHOR>
 */
public enum BackupPowerDeviceTypeEnum {

    Power_Supply_Equipment("powerSupplyEquipment", "{\"en_US\":\"Power Supply Equipment\",\"zh_CN\":\"电源设备\"}", 1),
    BATTERY_PACK("batteryPack", "{\"en_US\":\"Battery Pack(Independent Monitoring)\",\"zh_CN\":\"电池组(独立监控)\"}", 2);

    private String id;
    private String name;
    private Integer sequence;

    public String getId() {
        return id;
    }
    public String getName() {
        return name;
    }
    public Integer getSequence() {
        return sequence;
    }

    BackupPowerDeviceTypeEnum(String id, String name, Integer sequence)
    {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
    }

    /**
     * 根据id获取名称
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BackupPowerDeviceTypeEnum[] arr = BackupPowerDeviceTypeEnum.values();
        for(BackupPowerDeviceTypeEnum enu : arr)
        {
            if (enu.getId().equals(id))
            {
                return enu.getName();
            }
        }
        return "";
    }

    public static List<IdNameBean> getAllIdNameBySequence()
    {
        List<IdNameBean> statusLevels = new ArrayList<>();

        BackupPowerDeviceTypeEnum[] arr = BackupPowerDeviceTypeEnum.values();
        List<BackupPowerDeviceTypeEnum> list = Arrays.asList(arr);
        list.stream().sorted(Comparator.comparing(BackupPowerDeviceTypeEnum::getSequence));

        for(BackupPowerDeviceTypeEnum status: list)
        {
            statusLevels.add(new IdNameBean(status.getId(), status.getName()));
        }
        return statusLevels;
    }

    /**
     *获取所有备电设备类型ids
     * @return
     */
    public static List<String> getAllIds() {
        List<String> allIds = new ArrayList<>();
        BackupPowerDeviceTypeEnum[] arr = BackupPowerDeviceTypeEnum.values();
        for (BackupPowerDeviceTypeEnum enu : arr) {
            if (StringUtils.isNotBlank(enu.getId())) {
                allIds.add(enu.getId());
            }
        }
        return allIds;
    }
}
