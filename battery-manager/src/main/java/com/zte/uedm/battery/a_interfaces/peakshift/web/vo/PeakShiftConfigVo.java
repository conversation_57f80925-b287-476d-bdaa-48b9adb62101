package com.zte.uedm.battery.a_interfaces.peakshift.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 错峰属性配置Bean
 */
@Setter
@Getter
@ToString
public class PeakShiftConfigVo {
    /**
     * 设备id
     */
    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 错峰场景
     */
    private String strategyCase;
    /**
     * 设备类型
     */
    private String deviceType;


    /**
     * 站点id
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 设备位置
     */
    private String devicePosition;
    /**
     * 设备策略类型 0--自动 1--人工
     */
    private String strategyType;
}
