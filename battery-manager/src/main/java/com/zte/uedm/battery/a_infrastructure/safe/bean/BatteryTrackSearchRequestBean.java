package com.zte.uedm.battery.a_infrastructure.safe.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/* Started by AICoder, pid:f9ebegc542e615214afc088d7068b508ee55594d */
@Setter
@Getter
@ToString
public class BatteryTrackSearchRequestBean
{
    private  int Length = 100;
    
    /**
     * 区域id列表
     */
    private List<String> realGroupIds;
    
    /**
     * 站点名称
     * 模糊查询
     */
    private String siteName;
    
    /**
     * 通讯状态
     */
    private String communicationStatus;
    
    /**
     * 运动状态
     */
    private String motionState;
    
    /**
     * 设防状态
     */
    private String fortificationState;

    /**
     * 电池位置状态
     */
    private String theftStatus;

    /**
     * 电池锁死状态
     */
    private String lockStatus;
    
    /**
     * 站点等级列表
     */
    private List<String> siteLevel;

    /**
     * 排序字段
     */
    private String order;
    /**
     * 排序顺序
     */
    private String sort;
    /**
     * 开始时间
     */
    private String ariseTimeBegin;

    /**
     * 结束时间
     */
    private String ariseTimeEnd;
    
    /**
     * 页码
     */
    private Integer pageNo;
    
    /**
     * 单页数量
     */
    private Integer pageSize;
    /* Ended by AICoder, pid:f9ebegc542e615214afc088d7068b508ee55594d */

    public Boolean checkParameter(){
        return StringUtils.isNotBlank(this.siteName) && this.siteName.length() > Length;
    }
}
