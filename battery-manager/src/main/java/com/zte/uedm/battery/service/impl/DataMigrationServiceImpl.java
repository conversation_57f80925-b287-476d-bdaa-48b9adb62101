package com.zte.uedm.battery.service.impl;

import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.LogInputBean;
import com.zte.uedm.battery.bean.MigrationDataDTO;
import com.zte.uedm.battery.mapper.DataMigrationMapper;
import com.zte.uedm.battery.service.DataMigrationService;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.common.bean.log.OperLogContants;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.service.config.api.configuraiton.DeviceService;
import com.zte.uedm.service.config.api.configuraiton.vo.DeviceVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataMigrationServiceImpl implements DataMigrationService {

    @Resource
    DataMigrationMapper dataMigrationMapper;

    @Resource
    DeviceService deviceService;

    @Autowired
    LogUtils logUtils;

    private static final String[] mocs = {"r32.uedm.device.batteryset","r32.uedm.device.dcpower","r32.uedm.device.battery"};

    private static final String BATTERY_MODULE_ZH = "电池模块";
    private static final String BATTERY_MODULE_EN = "Battery Module";

    private static final String BATTERY_MIGRAEDATA_OPERATION_ZH = "数据迁移";
    private static final String BATTERY_MIGRAEDATA_OPERATION_EN = "Data migration";

    private static final String resultFailedEn = "Failed to migrate data.";
    private static final String resultFailedZh = "数据迁移失败。";

    private static final String resultSuccessEn = "Data migration succeeded.";
    private static final String resultSuccessZh = "数据迁移成功。";

    /* Started by AICoder, pid:5e98bze7e29474d146f10b39e03ac17ea1968213 */
    @Transactional
    @Override
    public void migrateData(HttpServletRequest request){
        try {
            // 开始时间
            long startTime = System.currentTimeMillis();
            log.info("=====data migration start=====");

            List<DeviceVo> deviceVos = deviceService.queryAll();
            if (null == deviceVos) {
                log.error("No data found.");
                return;
            }
            log.info("Total device count: " + deviceVos.size());

            List<DeviceVo> filteredBeans = deviceVos.stream()
                    .filter(bean -> Arrays.stream(mocs).anyMatch(moc -> moc.equals(bean.getMoc())))
                    .collect(Collectors.toList());

            log.info("Total filtered beans count: " + filteredBeans.size());

            // 批次大小
            int batchSize = 500;
            int totalSize = filteredBeans.size();
            // 计算总批次数
            int totalBatches = (int) Math.ceil((double) totalSize / batchSize);

            // 构建批量数据列表
            List<MigrationDataDTO> migrationDataList = filteredBeans.stream().map(bean -> {
                String pathIdsStr = String.join("/", bean.getPathId());
                return new MigrationDataDTO(bean.getId(), pathIdsStr, bean.getPathName());
            }).collect(Collectors.toList());

            // 分批处理
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<MigrationDataDTO> batchList = migrationDataList.subList(i, endIndex);

                // 计算当前批次
                int currentBatch = (i / batchSize) + 1;
                log.info("Processing batch " + currentBatch + " of " + totalBatches + ". Batch size: " + batchList.size());

                if (!batchList.isEmpty()) {
                    int successAmount = dataMigrationMapper.batchUpdateAllTables(batchList); // 批量更新
                    log.info("Batch " + currentBatch + " successfully updated " + successAmount + " records.");
                }
            }

            // 发送成功日志
            intervalLogSender(resultSuccessZh, resultSuccessEn, request, OperLogContants.OperateResultSuccess);

            // 结束时间
            long endTime = System.currentTimeMillis();
            log.info("=====data migration end, time elapsed: " + (endTime - startTime) + " ms =====");
        } catch (Exception e) {
            log.error("Data migration failed.", e);
            // 发送失败日志
            intervalLogSender(resultFailedZh, resultFailedEn, request, OperLogContants.OperateResultFail);
        }
    }

    public void intervalLogSender(String resultZh, String resultEn, HttpServletRequest request, String operateResult){
        try{
            LogInputBean logInputBean = new LogInputBean();
            logInputBean.setHost(Tools.getRemoteHost(request));
            logInputBean.setUser(Tools.getUserName(request));
            logInputBean.setRank(OperlogBean.LogRank.operlog_rank_important);
            logInputBean.setOperateResult(operateResult);
            logInputBean.setDetailEn(resultEn);
            logInputBean.setDetailZh(resultZh);
            logInputBean.setModuleNameZh(BATTERY_MODULE_ZH);
            logInputBean.setModuleNameEn(BATTERY_MODULE_EN);
            logInputBean.setOperationEn(BATTERY_MIGRAEDATA_OPERATION_EN);
            logInputBean.setOperationZh(BATTERY_MIGRAEDATA_OPERATION_ZH);
            logInputBean.setOperateResource(new String[]{"Battery Migrated Data"});
            logUtils.generalLogSender(logInputBean);
        } catch (Exception e) {
            log.error("Interval log sender failed.", e);
        }
    }
    /* Ended by AICoder, pid:5e98bze7e29474d146f10b39e03ac17ea1968213 */
}
