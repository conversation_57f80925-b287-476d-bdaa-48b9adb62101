package com.zte.uedm.battery.opti.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.domain.gateway.AssetServiceInterface;
import com.zte.uedm.battery.opti.domain.service.BattRiskCalculateDataProvider;
import com.zte.uedm.battery.opti.domain.service.bean.RiskCalulateData;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeGroupOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.battery.opti.infrastructure.repository.po.BattRiskRuleSourcePo;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("attribute")
@Slf4j
public class BattRiskCalculateAttributeDataProvider implements BattRiskCalculateDataProvider
{

    @Autowired
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository;
    @Autowired
    private AssetServiceInterface assetServiceInterface;
    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Override
    public Map<String, Map<String, String>> getDataRealTime(List<String> moIds, List<String> sourceIds) throws UedmException
    {
        // 校验参数
        if(CollectionUtils.isEmpty(moIds) || CollectionUtils.isEmpty(sourceIds))
        {
            log.info("[RiskDataProvider-Attribute] moIds or sourceIds is empty");
            return new HashMap<>();
        }

        // 查询电池风险规则参数信息，并根据"类型分组" 分组
        List<BattRiskRuleSourceEntity> riskRuleSourceEntities = battRiskRuleSourceRepository.selectByIds(sourceIds);
        log.info("[RiskDataProvider-Attribute][getDataRealTime] select ruleSource from DB success, riskRuleSourceEntities.size={}", riskRuleSourceEntities.size());
        List<BattRiskRuleSourceEntity> riskRuleSourceEntitiesAfterFilter = riskRuleSourceEntities.stream()
                .filter(b-> BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ATTRIBUTE.equals(b.getSourceType()))
                .collect(Collectors.toList());
        log.info("[RiskDataProvider-Attribute][getDataRealTime] filter riskRuleSourceEntities success, riskRuleSourceEntitiesAfterFilter.size={}", riskRuleSourceEntitiesAfterFilter.size());
        Map<BattRiskRuleSourceTypeGroupOptional, List<BattRiskRuleSourceEntity>> riskRuleSourceEntitiesBySourceTypeGroup = riskRuleSourceEntitiesAfterFilter.stream()
                .filter(b->b.getSourceTypeGroup() != null)
                .collect(Collectors.groupingBy(BattRiskRuleSourceEntity::getSourceTypeGroup));
        log.info("[RiskDataProvider-Attribute][getDataRealTime] riskRuleSourceEntities groupBy SourceTypeGroup success, riskRuleSourceEntitiesBySourceTypeGroup.size={}", riskRuleSourceEntitiesBySourceTypeGroup.size());


        // 查询电池的资产信息
        Map<String, List<Map<String, String>>> objectToAssetsAttributeMap = assetServiceInterface.getAssetAttributeDataByMoIds(moIds, HeaderUtils.ROOT_USER);
        log.info("[RiskDataProvider-Attribute][getDataRealTime] get asset attribute data by moIds from assetServiceInterface success, objectToAssetsAttributeMap.size={}",objectToAssetsAttributeMap.size());
        log.debug("[RiskDataProvider-Attribute][getDataRealTime] get asset attribute data by moIds from assetServiceInterface success, objectToAssetsAttributeMap={}",objectToAssetsAttributeMap);
        Map<String, Map<String, String>> battToAssetAttributeMap = transferToBattToAssetAttributeMap(objectToAssetsAttributeMap);
        log.info("[RiskDataProvider-Attribute][getDataRealTime] transferToBattToAssetAttributeMap end, battToAssetAttributeMap.size={}", battToAssetAttributeMap.size());
        log.debug("[RiskDataProvider-Attribute][getDataRealTime] get asset attribute data by moIds from assetServiceInterface success, battToAssetAttributeMap={}",battToAssetAttributeMap);

        // 查询电池的监控对象信息
        Map<String, Map<String, String>> battToObjectAttributeMap = getBattToObjectAttributeMap(moIds);
        log.info("[RiskDataProvider-Attribute][getDataRealTime] get object attribute data by moIds from cacheManager success, battToObjectAttributeMap.size={}", battToObjectAttributeMap.size());


        Map<String, Map<String, String>> result = buildDataRealTime(moIds, riskRuleSourceEntitiesBySourceTypeGroup, battToAssetAttributeMap, battToObjectAttributeMap);
        log.debug("[RiskDataProvider-Attribute][getDataRealTime] get asset attribute data by moIds from assetServiceInterface success, result={}",result);
        return result;
    }

    /**
     * 转换对象关联资产属性 为电池关联资产属性
     * 电池和资产关联关系为1对1
     * @param objectToAssetsAttributeMap
     * @return
     */
    private Map<String, Map<String, String>> transferToBattToAssetAttributeMap(Map<String, List<Map<String, String>>> objectToAssetsAttributeMap)
    {
        Map<String, Map<String, String>> battToAssetAttributeMap = new HashMap<>();
        for(Map.Entry<String, List<Map<String, String>>> entry : objectToAssetsAttributeMap.entrySet())
        {
            List<Map<String, String>> list = entry.getValue();
            if(list != null && list.size() > 0)
            {
                battToAssetAttributeMap.put(entry.getKey(), list.get(0));
            }
        }
        return battToAssetAttributeMap;
    }

    @NotNull
    private Map<String, Map<String, String>> buildDataRealTime(List<String> moIds, Map<BattRiskRuleSourceTypeGroupOptional,
            List<BattRiskRuleSourceEntity>> riskRuleSourceEntitiesBySourceTypeGroup, Map<String, Map<String, String>> battToAssetAttributeMap,
                                                               Map<String, Map<String, String>> battToObjectAttributeMap)
    {
        Map<String, Map<String, String>> result = new HashMap<>();
        Map<String, Map<String, String>> battToAttributeMapFromAsset = new HashMap<>();
        Map<String, Map<String, String>> battToAttributeMapFromObject = new HashMap<>();

        // 源自 属性-监控对象组的参数值从监控对象信息取
        for(Map.Entry<BattRiskRuleSourceTypeGroupOptional, List<BattRiskRuleSourceEntity>> entry : riskRuleSourceEntitiesBySourceTypeGroup.entrySet())
        {
            BattRiskRuleSourceTypeGroupOptional sourceTypeGroup = entry.getKey();

            List<BattRiskRuleSourceEntity> battRiskRuleSourceEntities = entry.getValue();
            // 资产来源情况
            if(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ATTRIBUTE_ASSET.equals(sourceTypeGroup))
            {
                log.debug("[RiskDataProvider-Attribute][buildDataRealTime] sourceTypeGroup={}，battRiskRuleSourceEntities.size={},battRiskRuleSourceEntities={}",
                        sourceTypeGroup, battRiskRuleSourceEntities, battRiskRuleSourceEntities);
                battToAttributeMapFromAsset = buildAttributeData(moIds, battToAssetAttributeMap, battRiskRuleSourceEntities);
                log.debug("[RiskDataProvider-Attribute][buildDataRealTime] battToAttributeMapFromAsset={}", battToAttributeMapFromAsset);

                result.putAll(battToAttributeMapFromAsset);
            }
            // 监控对象情况
            if(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ATTRIBUTE_MONITOR_OBJECT.equals(sourceTypeGroup))
            {
                log.debug("[RiskDataProvider-Attribute][buildDataRealTime] sourceTypeGroup={}，battRiskRuleSourceEntities.size={},battRiskRuleSourceEntities={}",
                        sourceTypeGroup,battRiskRuleSourceEntities.size(),battRiskRuleSourceEntities);
                battToAttributeMapFromObject = buildAttributeData(moIds, battToObjectAttributeMap, battRiskRuleSourceEntities);
                log.debug("[RiskDataProvider-Attribute][buildDataRealTime] battToAttributeMapFromObject={}", battToAttributeMapFromObject);
                result.putAll(battToAttributeMapFromObject);
            }
        }
        //不同来源数据合并
        dealDifferentSource(battToAttributeMapFromAsset, battToAttributeMapFromObject, result);
        return result;
    }

    private void dealDifferentSource(Map<String, Map<String, String>> battToAttributeMapFromAsset, Map<String, Map<String, String>> battToAttributeMapFromObject, Map<String, Map<String, String>> result)
    {

        if(!battToAttributeMapFromAsset.isEmpty() && battToAttributeMapFromObject.isEmpty())
        {
            result.putAll(battToAttributeMapFromAsset);
        }
        else if(battToAttributeMapFromAsset.isEmpty() && !battToAttributeMapFromObject.isEmpty())
        {
            result.putAll(battToAttributeMapFromObject);
        }
        else if(!battToAttributeMapFromAsset.isEmpty())
        {
            Set<String> allBattIds = new HashSet<>(battToAttributeMapFromAsset.keySet());
            allBattIds.addAll(battToAttributeMapFromObject.keySet());
            for(String id : allBattIds)
            {
                Map<String, String> map = mergeDifferentSource(battToAttributeMapFromAsset.get(id), battToAttributeMapFromObject.get(id));
                result.put(id, map);
            }
        }
    }

    private Map<String, String> mergeDifferentSource(Map<String, String> assetMap, Map<String, String> objectMap)
    {
        Map<String, String> result = new HashMap<>();
        if(null != assetMap)
        {
            result.putAll(assetMap);
        }
        if(null != objectMap)
        {
            result.putAll(objectMap);
        }
        return result;
    }

    @NotNull
    private Map<String, Map<String, String>> getBattToObjectAttributeMap(List<String> moIds) throws UedmException
    {
        Map<String, Map<String, String>> battToObjectAttributeMap = new HashMap<>();
        List<DeviceEntity> batteryEntities = deviceCacheManager.getDeviceByIdsAndMoc(moIds, MocOptional.BATTERY.getId());
        log.info("[RiskDataProvider-Attribute][getDataRealTime] get object attribute data by moIds from cacheManager success, monitorObjectEntities.size={}", batteryEntities.size());
        for(DeviceEntity entity : batteryEntities)
        {
            Map<String, String> objectMap = JSON.parseObject(JSON.toJSONString(entity), new TypeReference<Map<String, String>>() {});
            battToObjectAttributeMap.put(entity.getId(), objectMap);
        }
        log.info("[RiskDataProvider-Attribute][getDataRealTime] transfer end, battToObjectAttributeMap.size={}", battToObjectAttributeMap.size());
        return battToObjectAttributeMap;
    }

    private Map<String, Map<String, String>> buildAttributeData(List<String> moIds, Map<String, Map<String, String>> battToAttributeMap,  List<BattRiskRuleSourceEntity> battRiskRuleSourceEntities)
    {
        Map<String, Map<String, String>> result = new HashMap<>();

        for(String moId : moIds)
        {
            log.debug("[RiskDataProvider-Attribute][buildAttributeData] moId={}", moId);
            Map<String, String> attributeToValueMap = battToAttributeMap.get(moId);
            log.info("{} [RiskDataProvider-Attribute][buildAttributeData] attributeToValueMap={}", moId, attributeToValueMap);

            if(attributeToValueMap!= null)
            {
                Map<String, String> sourceIdToValue = new HashMap<>();
                for(BattRiskRuleSourceEntity battRiskRuleSourceEntity : battRiskRuleSourceEntities)
                {
                    log.debug("[RiskDataProvider-Attribute][buildAttributeData] battRiskRuleSourceEntity={}", battRiskRuleSourceEntity);
                    try
                    {
                        log.info("{} [RiskDataProvider-Attribute][buildAttributeData] {} ={}", moId, battRiskRuleSourceEntity.getId(), battRiskRuleSourceEntity.getMappingId());
                        String value = attributeToValueMap.get(battRiskRuleSourceEntity.getMappingId());
                        log.info("[RiskDataProvider-Attribute][buildAttributeData] value={}", value);
                        // 值转换
                        String mappingValue = battRiskRuleSourceEntity.transferValue(value);
                        log.debug("[RiskDataProvider-Attribute][buildAttributeData] mappingValue={}", mappingValue);
                        sourceIdToValue.put(battRiskRuleSourceEntity.getId(), mappingValue);
                    }
                    catch (UedmException e)
                    {
                        log.error("transferValue occur exception.{}",e.getMessage(), e);
                    }
                }
                log.debug("[RiskDataProvider-Attribute][buildAttributeData] sourceIdToValue={}", sourceIdToValue);
                result.put(moId, sourceIdToValue);
            }
        }

        return result;
    }


    @Override
    public Map<String, Map<String, List<RiskCalulateData>>> getDataRangeTime(List<String> moIds, List<String> parameterIds, Pair<Date, Date> timeRange) throws UedmException {
        return new HashMap<>();
    }
}
