package com.zte.uedm.battery.controller;

import com.zte.log.filter.UserThreadLocal;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.controller.battexport.dto.ExportTaskDto;
import com.zte.uedm.battery.controller.battexport.vo.ExportTaskVO;
import com.zte.uedm.battery.service.ExportTaskService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;

@Path("/export-tasks")
@Component
@Slf4j
public class ExportTaskController {
    @Autowired

    private ExportTaskService exportTaskService;
    @Autowired
    private MessageSenderService msgSenderService;
    @Autowired
    private JsonService jsonService;
    //导出模块-中文
    private static final String BATTERY_OVERVIEW_ZH="电池总览";
    //导出模块-英文
    private static final String BATTERY_OVERVIEW_EN="Battery Overview";
    //操作-电池备电概览导出-中文
    private static final String BATTERY_OVERVIEW_EXPORT_ZH="电池总览导出";
    //操作-电池备电概览导出-英文
    private static final String BATTERY_OVERVIEW_EXPORT_EN="Battery Overview Export";
    /* Started by AICoder, pid:va7d1v25137c2f7140d20a1820b36a930158756d */
    @POST
    @Path("/list")
    @ApiResponses({@ApiResponse(code = -1, message = "失败")})
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "导出离线任务查询", notes = "导出离线任务查询", response = ResponseBean.class, httpMethod = HttpMethod.GET)
    public ResponseBean getExportTasksByCondition(ExportTaskDto exportTaskDto, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption) {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);

        try {
            exportTaskDto.setCreateUser(userName);
            List<ExportTaskVO> exportTaskVO = exportTaskService.selectByCondition(exportTaskDto);

            return ResponseBeanUtils.getNormalResponseBean(0, exportTaskVO, exportTaskVO.size());
        } catch (Exception e) {
            return ResponseBeanUtils.getRemoteResponseBean(-1,"getExportTasksByCondition fail.");
        }
    }

    @POST
    @Path("/statistics/export")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "电池概览导出", notes = "电池概览导出", response = ResponseBean.class, httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -304, message = "参数超出范围")
    })
    public ResponseBean exportOverview(ExportTaskDto exportBean,
                                       @Context HttpServletRequest request,
                                       @Context HttpServletResponse response,
                                       @HeaderParam("language-option") String languageOption) throws UedmException {

        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        boolean operStatus = true;
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(userName, ip, connectMode, BATTERY_OVERVIEW_ZH, BATTERY_OVERVIEW_EN,
                OperlogBean.LogRank.operlog_rank_notice, BATTERY_OVERVIEW_EXPORT_ZH, BATTERY_OVERVIEW_EXPORT_EN);

        String fileName = "";
        if (null == exportBean) {
            return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "batteryHisDataQueryBean is null", null, null, null);
        }

        try {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            fileName = exportTaskService.taskFileDownload(exportBean.getId(), response, request);
            return ResponseBeanUtils.getNormalResponseBean(0, null, null, "ok", 1);
        } catch (Exception e) {
            operStatus = false;
            log.error("batteryHisDataExport", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "batteryHisDataExport fail");
        } finally {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();

            detailZh.append("用户名称 ：").append(operlogBean.getUserName()).append(" , 导出文件名 ：")
                    .append(fileName).append(" , 导出位置 ：").append("");
            detailEn.append("userName ：").append(operlogBean.getUserName()).append(" , Export file name ：")
                    .append(fileName).append(" , Export position ：").append("");
            log.info("exportOverview -> detailZh:{}, detailEn:{}", detailZh, detailEn);

            if (operStatus) {
                operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            } else {
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }

            String operMsg = jsonService.objectToJson(operlogBean);
            log.info("exportOverview -> operMsg:{}", operMsg);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    @DELETE
    @Path("/task-manage")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "批量删除导出任务", notes = "批量删除导出任务", httpMethod = "DELETE", tags = {"批量删除导出任务"})
    public ResponseBean taskBatchDelete(ExportTaskDto exportDeleteDto, @Context HttpServletRequest request,
                                        @Context HttpServletResponse response) {
        String userName = Tools.getUserName(request);

        try {
            if (null == exportDeleteDto || !StringUtils.isNotBlank(exportDeleteDto.getExportKey())) {
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "exportDeleteDto is null", null, null, null);
            }
            exportTaskService.taskBatchDelete(exportDeleteDto.getExportKey(), exportDeleteDto.getTaskIds(), userName);
            return ResponseBeanUtils.getNormalResponseBean(0, null, "ok", null, null);
        } catch (Exception e) {
            log.error("Error deleting task with taskId: {}", exportDeleteDto.getTaskIds().size(), e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "Error deleting task!");
        }
    }
    /* Ended by AICoder, pid:va7d1v25137c2f7140d20a1820b36a930158756d */
}
