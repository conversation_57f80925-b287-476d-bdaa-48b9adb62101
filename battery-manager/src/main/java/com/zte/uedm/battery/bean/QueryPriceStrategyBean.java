package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(description = "价格策略")
public class QueryPriceStrategyBean
{
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startTime;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endTime;
    /**
     * 策略范围ID
     */
    @ApiModelProperty(value = "策略范围标识")
    private String scopeStrategyId;
    /**
     * 排序字段名称
     */
    @ApiModelProperty(value = "排序字段名称")
    private String sortBy;
    /**
     * 按名称排序顺序 desc/asc  默认desc
     */
    @ApiModelProperty(value = "顺序")
    private String order;
    /**
     *状态
     */
    @ApiModelProperty(value = "状态")
    private List<String> status;

    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}
