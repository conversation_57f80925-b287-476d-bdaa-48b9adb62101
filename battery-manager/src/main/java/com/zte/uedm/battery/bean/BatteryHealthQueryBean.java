package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@ApiModel(description = "电池健康评估查询")
public class BatteryHealthQueryBean {

    /**
     * 逻辑分组id
     */
    @ApiModelProperty(value = "逻辑分组标识")
    private String logicGroupId;

    /**
     * 评估时间起 "yyyy-mm-dd hh:mm:ss"
     */
    @ApiModelProperty(value = "评估开始时间")
    private String evaluateTimeStart;

    /**
     * 评估时间止
     */
    @ApiModelProperty(value = "评估结束时间时间")
    private String evaluateTimeEnd;
    @ApiModelProperty(value = "页码")

    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}
