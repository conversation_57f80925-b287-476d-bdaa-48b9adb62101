package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pv.PowerSupplySceneBean;
import com.zte.uedm.battery.controller.backuppower.bo.BattBackupPowerEvalBo;
import com.zte.uedm.battery.controller.backuppower.dto.*;
import com.zte.uedm.battery.controller.backuppower.vo.*;
import com.zte.uedm.battery.controller.backuppowerconfig.dto.ExportBackupPowerDistributionDto;
import com.zte.uedm.battery.controller.backuppowerconfig.enums.BackupPowerDeviceTypeEnum;
import com.zte.uedm.battery.controller.backuppowerconfig.enums.BackupPowerDeviceTypeExportEnum;
import com.zte.uedm.battery.controller.backuppowerconfig.enums.BackupPowerOverviewDimEnums;
import com.zte.uedm.battery.controller.backuppowerconfig.enums.BackupPowerStatisticsDimEnums;
import com.zte.uedm.battery.controller.backuppowerconfig.service.BackupPowerConfigService;
import com.zte.uedm.battery.controller.backuppowerconfig.vo.BackupPowerConfigVo;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterRequestDto;
import com.zte.uedm.battery.dao.BackupPowerOverviewDao;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.impl.BackupPowerEvalDomainImpl;
import com.zte.uedm.battery.enums.backuppower.*;
import com.zte.uedm.battery.enums.overview.BattExportParamEnums;
import com.zte.uedm.battery.enums.overview.BattOverViewDimEnums;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.export.manage.entity.ExportReportBO;
import com.zte.uedm.battery.export.manage.entity.ExportType;
import com.zte.uedm.battery.mapper.BattBackupPowerEvalMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.service.BackupPowerOverviewService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.battery.util.PowerSupplySceneUtils;
import com.zte.uedm.battery.util.SiteLevelUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteLevelBean;
import com.zte.uedm.common.consts.TimeFormatConstants;
import com.zte.uedm.common.consts.asset.AssetModelAttributeIdConstants;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ComparatorUtil;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeOtherUtil;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.common.util.PageUtils;
import com.zte.uedm.common.util.RandomUtils;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.uedm.battery.consts.CommonConst.MASTER_DIRECTORY;
import static com.zte.uedm.battery.service.impl.PvDataExportServiceImpl.randomString;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BackupPowerOverviewServiceImpl implements BackupPowerOverviewService {
    @Autowired
    private BackupPowerEvalDomain backupPowerDomain;
    @Autowired
    private BattPackStatusOverviewDomain battPackStatusOverviewDomain;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private WriterExportFactory wf;
    @Autowired
    private BackupPowerOverviewService backupPowerOverviewService;
    @Autowired
    private BattAssetAttributeDomain battAssetAttributeDomain;
    @Autowired
    private BackupPowerOverviewDao backupPowerOverviewDao;
    @Autowired
    private BackupPowerAssetDomain backupPowerAssetDomain;
    @Autowired
    private BattAssetDomain battAssetDomain;
    @Autowired
    private ConfigurationDataDomain configurationDataDomain;
    @Autowired
    private BatteryBackupPowerEvalDomain batteryBackupPowerEvalDomain;
    @Autowired
    private BackupPowerConfigService backupPowerConfigService;
    @Autowired
    private BattBackupPowerEvalMapper backupPowerMapper;
    @Autowired
    private BattBackupPowerEvalMapper battBackupPowerEvalMapper;
    @Autowired
    private BackupPowerEvalDomainImpl backupPowerEvalDomain;
    @Autowired
    private DeviceCacheManager deviceCacheManager;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Autowired
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;
    @Autowired
    private SiteLevelUtils siteLevelUtils;
    @Autowired
    private PowerSupplySceneUtils powerSupplySceneUtils;
    @Autowired
    private FieldCacheManager fieldCacheManager;

    private static final Integer SIZE = 1;

    private static final String OVERVIEW_SHEET_NAME = "{\"en_US\":\"Power Standby Overview\",\"zh_CN\":\"备电概览\"}";
    //电池备电-概览导出统计sheet名
    private static final String STATISTICS_SHEET_NAME = "{\"en_US\":\"Statistics\",\"zh_CN\":\"统计\"}";
    //电池备电-分布导出sheet名
    private static final String DISTRIBUTION_SHEET_NAME = "{\"en_US\":\"Backup Distribution\",\"zh_CN\":\"备电分布\"}";
    //电池备电概览-导出文件名
    private static final String FILE_NAME = "{\"en_US\":\"Battery_backup_overview\",\"zh_CN\":\"电池备电概览\"}";
    //电池备电-分布导出文件名
    private static final String BACKUP_POWER_DISTRIBUTION_FILE_NAME = "{\"en_US\":\"Backup_Overview_Distribution\",\"zh_CN\":\"备电分布\"}";
    private static final List<String> sp_moc = Arrays.asList(MocOptional.SP.getId());
    private static final List<String> bp_moc = Arrays.asList(MocOptional.BATTERY_SET.getId());

    private static final String Power_Supply_Equipment = "powerSupplyEquipment";

    private static final String BATTERY_PACK = "batteryPack";

    private static final String DURATION_LI_LEAD = "backupPowerDurationLiAndLead";
    private static final String SP = MocOptional.SP.getId();
    private static final String BP = MocOptional.BATTPACK.getId();

    private static  final  String UNKNOWN_LEFT = "unknown";
    private static  final  String UNKNOWN_RIGHT = "{\"en_US\":\"Unknown\",\"zh_CN\":\"未知\"}";

    private static final String TOTAL_LOAD_POWER = "{\"en_US\":\"Not involved\",\"zh_CN\":\"不涉及\"}";


    private static Pair<String, String> unknownManufacturePair = Pair.of(UNKNOWN_LEFT, UNKNOWN_RIGHT);
    // 电池品牌未知的值 - id->name
    private static Pair<String, String> unknownBrandPair = Pair.of(UNKNOWN_LEFT, UNKNOWN_RIGHT);
    // 电池系列未知的值 - id->name
    private static Pair<String, String> unknownSeriesPair = Pair.of(UNKNOWN_LEFT, UNKNOWN_RIGHT);
    // 电池型号未知的值 - id->name
    private static Pair<String, String> unknownModelPair = Pair.of(UNKNOWN_LEFT, UNKNOWN_RIGHT);

    /**
     * 备电状态-条件查询
     *
     * @param logicGroupId
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public List<BackPowerOverviewVo> selectByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException {

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = backupPowerDomain.selectByLogicGroupId(logicGroupId, serviceBean);
        log.info("==========selectByLogicGroupId battBackupPowerOverviewPojos : {}==========", JSONObject.toJSONString(battBackupPowerOverviewPojos));
        List<BackPowerOverviewVo> backPowerOverviewVos = new ArrayList<>();
        int norNum = 0;
        int abnorNum = 0;
        int unEvaluateNum = 0;

        BackPowerOverviewVo norResponseBean = new BackPowerOverviewVo();
        BackPowerOverviewVo abNorResponseBean = new BackPowerOverviewVo();
        BackPowerOverviewVo unEvaluateResponseBean = new BackPowerOverviewVo();
        //通过数据库查询备电设备信息
        getStatusNum(serviceBean, battBackupPowerOverviewPojos, backPowerOverviewVos, norNum, abnorNum, unEvaluateNum, norResponseBean, abNorResponseBean, unEvaluateResponseBean);
        return backPowerOverviewVos;
    }

    private void getStatusNum(ServiceBaseInfoBean serviceBean, List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos, List<BackPowerOverviewVo> backPowerOverviewVos, int norNum, int abnorNum, int unEvaluateNum, BackPowerOverviewVo norResponseBean, BackPowerOverviewVo abNorResponseBean, BackPowerOverviewVo unEvaluateResponseBean) {
        if (!ObjectUtils.isEmpty(battBackupPowerOverviewPojos)) {
            for (BattBackupPowerOverviewPojo bean : battBackupPowerOverviewPojos) {
                //统计正常、不足和无法评估三种状态各有多少备电系统
                String status = bean.getStatus();
                log.info("==========getStatusNum status : {}==========", status);
                if (status.equalsIgnoreCase(BackupPowerStateEnum.NORMAL.getId())) {
                    norNum++;
                } else if (status.equalsIgnoreCase(BackupPowerStateEnum.DEFICIENCY.getId())) {
                    abnorNum++;
                } else if (status.equalsIgnoreCase(BackupPowerStateEnum.UNEVALUATE.getId())) {
                    unEvaluateNum++;
                }
            }
        }

        String norStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.NORMAL.getName(), serviceBean.getLanguageOption());
        String abnorStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.DEFICIENCY.getName(), serviceBean.getLanguageOption());
        String unEvaluateStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.UNEVALUATE.getName(), serviceBean.getLanguageOption());
        norResponseBean.setId(BackupPowerStateEnum.NORMAL.getId());
        norResponseBean.setName(norStatusName);
        norResponseBean.setNumber(norNum);

        abNorResponseBean.setId(BackupPowerStateEnum.DEFICIENCY.getId());
        abNorResponseBean.setName(abnorStatusName);
        abNorResponseBean.setNumber(abnorNum);

        unEvaluateResponseBean.setId(BackupPowerStateEnum.UNEVALUATE.getId());
        unEvaluateResponseBean.setName(unEvaluateStatusName);
        unEvaluateResponseBean.setNumber(unEvaluateNum);

        addBattStatus(backPowerOverviewVos, norNum, abnorNum, unEvaluateNum, norResponseBean, abNorResponseBean, unEvaluateResponseBean);
    }

    /**
     * 将状态数量添加到data中
     */
    private void addBattStatus(List<BackPowerOverviewVo> backPowerOverviewVos, int norNum, int abnorNum, int unEvaluateNum, BackPowerOverviewVo norResponseBean, BackPowerOverviewVo abNorResponseBean, BackPowerOverviewVo unEvaluateResponseBean) {
        backPowerOverviewVos.add(norResponseBean);
        backPowerOverviewVos.add(abNorResponseBean);
        backPowerOverviewVos.add(unEvaluateResponseBean);
    }

    /**
     * 备电状态增减情况-条件查询
     *
     * @param logicGroupId
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public List<BackPowerInDecreaseVo> selectInDecreaseByLogicGroupId(String logicGroupId, ServiceBaseInfoBean serviceBean) throws UedmException {

        String norStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.NORMAL.getName(), serviceBean.getLanguageOption());
        String abNorStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.DEFICIENCY.getName(), serviceBean.getLanguageOption());
        String unEvaluateStatusName = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.UNEVALUATE.getName(), serviceBean.getLanguageOption());
        List<BackPowerInDecreaseVo> backPowerInDecreaseVoList = new ArrayList<>();
        BackPowerInDecreaseVo norResponse = new BackPowerInDecreaseVo();
        BackPowerInDecreaseVo abNorResponse = new BackPowerInDecreaseVo();
        BackPowerInDecreaseVo unEvaluateResponse = new BackPowerInDecreaseVo();

        //从数据库获取查询数据
        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = backupPowerDomain.selectInDesByLogicGroupId(logicGroupId, serviceBean);
        //对获取的数据根据设备id进行分组
        Map<String, List<BattBackupPowerOverviewPojo>> collect = battBackupPowerOverviewPojos.stream().collect(Collectors.groupingBy(BattBackupPowerOverviewPojo::getId));
        log.info("==========selectInDecreaseByLogicGroupId collect : {}==========", JSONObject.toJSONString(collect));
        Set<String> keyset = collect.keySet();
        //初始化状态增减情况值
        BackPowerInDecreaseDto backPowerInDecreaseInfo = new BackPowerInDecreaseDto();
        //对每个设备对比两次状态评估情况
        for (Iterator<String> iter = keyset.iterator(); iter.hasNext(); ) {
            String key = iter.next();
            List<BattBackupPowerOverviewPojo> list = collect.get(key);
            Collections.sort(list, Comparator.comparing(BattBackupPowerOverviewPojo::getEvalTime).reversed());
            backPowerInDecreaseInfo.setNorInNumber(backPowerInDecreaseInfo.getNorInNumber() + getBackPowerInDecreaseInfo(list).getNorInNumber());
            backPowerInDecreaseInfo.setNorDeNumber(backPowerInDecreaseInfo.getNorDeNumber() + getBackPowerInDecreaseInfo(list).getNorDeNumber());
            backPowerInDecreaseInfo.setAbNorInNumber(backPowerInDecreaseInfo.getAbNorInNumber() + getBackPowerInDecreaseInfo(list).getAbNorInNumber());
            backPowerInDecreaseInfo.setAbNorDeNumber(backPowerInDecreaseInfo.getAbNorDeNumber() + getBackPowerInDecreaseInfo(list).getAbNorDeNumber());
            backPowerInDecreaseInfo.setUnEvaluateInNumber(backPowerInDecreaseInfo.getUnEvaluateInNumber() + getBackPowerInDecreaseInfo(list).getUnEvaluateInNumber());
            backPowerInDecreaseInfo.setUnEvaluateDeNumber(backPowerInDecreaseInfo.getUnEvaluateDeNumber() + getBackPowerInDecreaseInfo(list).getUnEvaluateDeNumber());
            backPowerInDecreaseInfo.setEvlTime(getBackPowerInDecreaseInfo(list).getEvlTime());
            backPowerInDecreaseInfo.setPreEvalTime(getBackPowerInDecreaseInfo(list).getPreEvalTime());
            log.info("==========selectInDecreaseByLogicGroupId backPowerInDecreaseInfo : {}==========", JSONObject.toJSONString(backPowerInDecreaseInfo));
        }

        //正常状态增减情况返回
        norResponse.setInNumber(backPowerInDecreaseInfo.getNorInNumber());
        norResponse.setDeNumber(backPowerInDecreaseInfo.getNorDeNumber());
        norResponse.setEvlTime(backPowerInDecreaseInfo.getEvlTime());
        norResponse.setPreEvalTime(backPowerInDecreaseInfo.getPreEvalTime());
        norResponse.setId(BackupPowerStateEnum.NORMAL.getId());
        norResponse.setName(norStatusName);

        //异常状态增减情况返回
        abNorResponse.setInNumber(backPowerInDecreaseInfo.getAbNorInNumber());
        abNorResponse.setDeNumber(backPowerInDecreaseInfo.getAbNorDeNumber());
        abNorResponse.setEvlTime(backPowerInDecreaseInfo.getEvlTime());
        abNorResponse.setPreEvalTime(backPowerInDecreaseInfo.getPreEvalTime());
        abNorResponse.setId(BackupPowerStateEnum.DEFICIENCY.getId());
        abNorResponse.setName(abNorStatusName);

        //无法评估状态增减情况返回
        unEvaluateResponse.setInNumber(backPowerInDecreaseInfo.getUnEvaluateInNumber());
        unEvaluateResponse.setDeNumber(backPowerInDecreaseInfo.getUnEvaluateDeNumber());
        unEvaluateResponse.setEvlTime(backPowerInDecreaseInfo.getEvlTime());
        unEvaluateResponse.setPreEvalTime(backPowerInDecreaseInfo.getPreEvalTime());
        unEvaluateResponse.setId(BackupPowerStateEnum.UNEVALUATE.getId());
        unEvaluateResponse.setName(unEvaluateStatusName);

        //组装返回data
        backPowerInDecreaseVoList.add(norResponse);
        backPowerInDecreaseVoList.add(abNorResponse);
        backPowerInDecreaseVoList.add(unEvaluateResponse);

        return backPowerInDecreaseVoList;
    }


    private BackPowerInDecreaseDto getBackPowerInDecreaseInfo(List<BattBackupPowerOverviewPojo> list) {
        BackPowerInDecreaseDto backPowerInDecreaseDto = new BackPowerInDecreaseDto();
        int norInNumber = 0;
        int norDeNumber = 0;
        int abNorInNumber = 0;
        int abNorDeNumber = 0;
        int unEvaluateInNumber = 0;
        int unEvaluateDeNumber = 0;
        Date evlTime = null;
        Date preEvlTime = null;
        if (list.size() >= SIZE) {
            //如果设备统计的数据多于两条，则取最新的两条做对比
            List<BattBackupPowerOverviewPojo> cacheList = list.subList(0, 1);
//            log.info("==========selectInDecreaseByLogicGroupId cacheList update : {}==========", JSONObject.toJSONString(cacheList));
            String currStatus = cacheList.get(0).getStatus();
            String preStatus = cacheList.get(0).getPreStatus();
            if (currStatus.equalsIgnoreCase(BackupPowerStateEnum.NORMAL.getId())) {
                if(StringUtils.isBlank(preStatus))
                {
                    norInNumber++;
                }
                else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.DEFICIENCY.getId())) {
                    norInNumber++;
                    abNorDeNumber++;
                } else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.UNEVALUATE.getId())) {
                    norInNumber++;
                    unEvaluateDeNumber++;
                }
            }
//            boolean norToAbNor = currStatus.equalsIgnoreCase(BackupPowerStateEnum.normal.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.deficiency.getId());
//            boolean norToUnEval = currStatus.equalsIgnoreCase(BackupPowerStateEnum.normal.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.unEvaluate.getId());
//            if (norToAbNor) {
//                norInNumber++;
//                abNorDeNumber++;
//            } else if (norToUnEval) {
//                norInNumber++;
//                unEvaluateDeNumber++;
//            }
            else {
                BackPowerInDecreaseDto otherStatusChange = getOtherInfo(list, norDeNumber, abNorInNumber, unEvaluateDeNumber, abNorDeNumber, unEvaluateInNumber);
                norDeNumber = otherStatusChange.getNorDeNumber();
                abNorInNumber = otherStatusChange.getAbNorInNumber();
                unEvaluateDeNumber = otherStatusChange.getUnEvaluateDeNumber();
                abNorDeNumber = otherStatusChange.getAbNorDeNumber();
                unEvaluateInNumber = otherStatusChange.getUnEvaluateInNumber();
            }
            try {
                evlTime = new SimpleDateFormat("yyyy-MM-dd").parse(cacheList.get(0).getEvalTime());
                if(list.size() > SIZE) {
                    preEvlTime = new SimpleDateFormat("yyyy-MM-dd").parse(list.get(1).getEvalTime());
                }
            } catch (ParseException e) {
                log.error("BackupPowerOverviewController-> evlTime parse fail.", e);
            }
        }

        backPowerInDecreaseDto.setEvlTime(evlTime) ;
        backPowerInDecreaseDto.setPreEvalTime(preEvlTime) ;
        backPowerInDecreaseDto.setNorInNumber(norInNumber) ;
        backPowerInDecreaseDto.setNorDeNumber(norDeNumber) ;
        backPowerInDecreaseDto.setAbNorInNumber(abNorInNumber) ;
        backPowerInDecreaseDto.setAbNorDeNumber(abNorDeNumber);
        backPowerInDecreaseDto.setUnEvaluateInNumber(unEvaluateInNumber);
        backPowerInDecreaseDto.setUnEvaluateDeNumber(unEvaluateDeNumber);
        return backPowerInDecreaseDto;
    }

    private BackPowerInDecreaseDto getOtherInfo(List<BattBackupPowerOverviewPojo> cacheList, int norDeNumber, int abNorInNumber, int unEvaluateDeNumber, int abNorDeNumber, int unEvaluateInNumber) {
        //如果设备统计的数据多于两条，则取最新的两条做对比
        BackPowerInDecreaseDto inDecreaseOtherDto = new BackPowerInDecreaseDto();
        String currStatus = cacheList.get(0).getStatus();
        String preStatus = cacheList.get(0).getPreStatus();
        if (currStatus.equalsIgnoreCase(BackupPowerStateEnum.DEFICIENCY.getId())) {
            if (StringUtils.isBlank(preStatus)) {
                abNorInNumber++;
            } else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.NORMAL.getId())) {
                abNorInNumber++;
                norDeNumber++;
            } else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.UNEVALUATE.getId())) {
                abNorInNumber++;
                unEvaluateDeNumber++;
            }
        }
        else
        {
            BackPowerInDecreaseDto unEvalDto = getUnEvalInfo(cacheList, norDeNumber, abNorDeNumber, unEvaluateInNumber);
            unEvaluateInNumber = unEvalDto.getUnEvaluateInNumber();
            norDeNumber = unEvalDto.getNorDeNumber();
            abNorDeNumber = unEvalDto.getAbNorDeNumber();
        }
//        boolean abNorToNor = currStatus.equalsIgnoreCase(BackupPowerStateEnum.deficiency.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.normal.getId());
//        boolean abNorToUnEval = currStatus.equalsIgnoreCase(BackupPowerStateEnum.deficiency.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.unEvaluate.getId());
//        if (abNorToNor) {
//            abNorInNumber++;
//            norDeNumber++;
//        } else if (abNorToUnEval) {
//            abNorInNumber++;
//            unEvaluateDeNumber++;
//        } else {
//            BackPowerInDecreaseDto unEvalDto = getUnEvalInfo(cacheList, norDeNumber, abNorDeNumber, unEvaluateInNumber);
//            unEvaluateInNumber = unEvalDto.getUnEvaluateInNumber();
//            norDeNumber = unEvalDto.getNorDeNumber();
//            abNorDeNumber = unEvalDto.getAbNorDeNumber();
//        }
        inDecreaseOtherDto.setAbNorInNumber(abNorInNumber);
        inDecreaseOtherDto.setNorDeNumber(norDeNumber);
        inDecreaseOtherDto.setUnEvaluateDeNumber(unEvaluateDeNumber);
        inDecreaseOtherDto.setUnEvaluateInNumber(unEvaluateInNumber);
        inDecreaseOtherDto.setAbNorDeNumber(abNorDeNumber);

        return inDecreaseOtherDto;
    }

    private BackPowerInDecreaseDto getUnEvalInfo(List<BattBackupPowerOverviewPojo> cacheList, int norDeNumber, int abNorDeNumber, int unEvaluateInNumber) {
        //如果设备统计的数据多于两条，则取最新的两条做对比
        BackPowerInDecreaseDto inDecreaseUnEvalDto = new BackPowerInDecreaseDto();
        String currStatus = cacheList.get(0).getStatus();
        String preStatus = cacheList.get(0).getPreStatus();
        if (currStatus.equalsIgnoreCase(BackupPowerStateEnum.UNEVALUATE.getId())) {
            if (StringUtils.isBlank(preStatus)) {
                unEvaluateInNumber++;
            } else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.NORMAL.getId())) {
                unEvaluateInNumber++;
                norDeNumber++;
            } else if (preStatus.equalsIgnoreCase(BackupPowerStateEnum.DEFICIENCY.getId())) {
                unEvaluateInNumber++;
                abNorDeNumber++;
            }
        }
//        boolean unEvalToNor = currStatus.equalsIgnoreCase(BackupPowerStateEnum.unEvaluate.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.normal.getId());
//        boolean unEvalToAbNor = currStatus.equalsIgnoreCase(BackupPowerStateEnum.unEvaluate.getId()) && preStatus.equalsIgnoreCase(BackupPowerStateEnum.deficiency.getId());
//        if (unEvalToNor) {
//            unEvaluateInNumber++;
//            norDeNumber++;
//        } else if (unEvalToAbNor) {
//            unEvaluateInNumber++;
//            abNorDeNumber++;
//        }
        inDecreaseUnEvalDto.setUnEvaluateInNumber(unEvaluateInNumber);
        inDecreaseUnEvalDto.setNorDeNumber(norDeNumber);
        inDecreaseUnEvalDto.setAbNorDeNumber(abNorDeNumber);

        return inDecreaseUnEvalDto;
    }


    /**
     * 备电详情-条件查询
     *
     * @param backPowerDetailDto
     * @param serviceBean
     * @return
     * @throws UedmException
     */
    @Override
    public PageInfo<BackPowerInDecrResponse> selectInDecreaseDetailByLogicGroupId(BackPowerDetailDto backPowerDetailDto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        return battPackStatusOverviewDomain.selectInDecreaseDetailByLogicGroupId(backPowerDetailDto, serviceBean);
    }

    @Override
    public String exportBackupOverview(BackupOverviewExportDto exportDto, ServiceBaseInfoBean serviceBean, HttpServletRequest request, HttpServletResponse response) throws UedmException {

        //获取文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String file_Name = i18nUtils.getMapFieldByLanguageOption(FILE_NAME, serviceBean.getLanguageOption());
        String fileName = file_Name + "_" + (sdf.format(new Date()));

        //3.3.1-统计接口
        BackupPowerStatisticsFilterDto dto = new BackupPowerStatisticsFilterDto(exportDto);

        String sp = i18nUtils.getMapFieldByLanguageOption(BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getName(), serviceBean.getLanguageOption());
        String bp = i18nUtils.getMapFieldByLanguageOption(BackupPowerDeviceTypeEnum.BATTERY_PACK.getName(), serviceBean.getLanguageOption());
        Map<String,Map<String, NameValueBean>> statisticsDataMap = statisticsBackupPowerByStatisticsDim(dto, serviceBean);
        Map<String,Map<String, NameValueBean>>  statisticsResultMapMap = new LinkedHashMap<>();

        if(null!=statisticsDataMap.get(Power_Supply_Equipment)){
            Map<String, NameValueBean> statisticsResultMap = new LinkedHashMap<>();
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.TOTAL.getId(), statisticsDataMap.get(Power_Supply_Equipment).get(BackupPowerOverviewStatisticsDimEnums.TOTAL.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.NORMAL.getId(), statisticsDataMap.get(Power_Supply_Equipment).get(BackupPowerOverviewStatisticsDimEnums.NORMAL.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.DEFICIENCY.getId(), statisticsDataMap.get(Power_Supply_Equipment).get(BackupPowerOverviewStatisticsDimEnums.DEFICIENCY.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.UNEVALUATE.getId(), statisticsDataMap.get(Power_Supply_Equipment).get(BackupPowerOverviewStatisticsDimEnums.UNEVALUATE.getId()));
            statisticsResultMapMap.put(sp,statisticsResultMap);
        }
        if(null!=statisticsDataMap.get(BATTERY_PACK)){
            Map<String, NameValueBean> statisticsResultMap = new LinkedHashMap<>();
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.TOTAL.getId(), statisticsDataMap.get(BATTERY_PACK).get(BackupPowerOverviewStatisticsDimEnums.TOTAL.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.NORMAL.getId(), statisticsDataMap.get(BATTERY_PACK).get(BackupPowerOverviewStatisticsDimEnums.NORMAL.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.DEFICIENCY.getId(), statisticsDataMap.get(BATTERY_PACK).get(BackupPowerOverviewStatisticsDimEnums.DEFICIENCY.getId()));
            statisticsResultMap.put(BackupPowerOverviewStatisticsDimEnums.UNEVALUATE.getId(), statisticsDataMap.get(BATTERY_PACK).get(BackupPowerOverviewStatisticsDimEnums.UNEVALUATE.getId()));
            statisticsResultMapMap.put(bp,statisticsResultMap);
        }
        //准备数据
        //表格表头
        List<BackupPowerConfigVo> backupPowerConfigVos = backupPowerConfigService.selectBackupPowerConfig(serviceBean.getUserName(), serviceBean.getLanguageOption());
        List<String> headers = backupPowerConfigVos.stream().filter(BackupPowerConfigVo::getEnable).map(BackupPowerConfigVo::getId).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        for (String str:headers)
        {
            if (str.equals(BackupPowerOverviewDimEnums.STATUS.getId()))
            {
                int index = headers.indexOf(BackupPowerOverviewDimEnums.STATUS.getId());
                headers.add(index+1,BackupPowerOverviewDimEnums.UN_KNOWN_REASON_KEY);
                break;
            }
        }
        log.info("BackupPowerOverviewServiceImpl -> start to exportBackupOverview headers.size:{}",headers.size());

        //表格数据-3.3.6
        TableDetailSelectDto detailSelectDto = new BackupOverviewExportDto();
        org.springframework.beans.BeanUtils.copyProperties(exportDto,detailSelectDto);
        PageInfo<Map<String, String>> mapPageInfo = selectByCondition(detailSelectDto, serviceBean);
        List<Map<String, String>> resultList = mapPageInfo.getList();


        List<ImageInfo> imageList = getImageInfoList(exportDto, serviceBean);

        FileExportWriter fw = wf.getWriter(ExportType.PICTURE);
        if (fw == null) {
            log.info("exportBackupOverview fw is null");
            return "";
        }

        ExportReportBO exportReportBO = new ExportReportBO();
        try {
            exportReportBO = constructData(resultList, headers, fw,fileName, imageList, serviceBean,statisticsResultMapMap,exportDto.getNodePosition());

            fw.setFormat(exportReportBO);
            fw.batchesGroupWriteData();
        } catch (Exception e) {
            log.error("exportBattCellData ", e);
            throw new UedmException(-1, e.getMessage());
        }
        finally {
            closeFile(fw);
        }

        File file = new File(FileUtils.pathManipulation(exportReportBO.getOutputFile()));
        if (file.exists()) {
            String srcStr = exportReportBO.getOutputFile();
            FileUtils.downloadPictureFile(srcStr, response, request);
        }

        return fileName;
    }

    public static void closeFile(FileExportWriter wb) {
        try {
            if (wb!= null)
            {
                wb.closeFile();
            }
        } catch (IOException e) {
            log.error("ExcelFileExportUtils close error!", e);
        }
    }

    private List<ImageInfo> getImageInfoList(BackupOverviewExportDto exportDto, ServiceBaseInfoBean serviceBean) {
        List<ImageInfo> imageList = new ArrayList<>();
        List<ImageBean> images = exportDto.getImages();
        if(!CollectionUtils.isEmpty(images)){
            for (ImageBean imageBean:images) {
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setImageName(imageBean.getImageName());
                imageInfo.setBase64Str(imageBean.getBase64Str());
                imageInfo.setYLine(imageBean.getYLine());
                imageInfo.setXLine(imageBean.getXLine());
                imageInfo.setDim(imageBean.getDim());
                imageInfo.setLang(serviceBean.getLanguageOption());
                imageList.add(imageInfo);
            }
        }
        return imageList;
    }

    /**
     * 构建数据
     * @param resultList
     * @param headers
     * @param fw
     * @param fileName
     * @param images
     * @param serviceBean
     * @param statisticsResultMapMap
     * @param position
     * @return
     * @throws UedmException
     */
    public ExportReportBO constructData(List<Map<String, String>> resultList, List<String> headers, FileExportWriter
            fw, String fileName, List<ImageInfo> images, ServiceBaseInfoBean serviceBean,
                                        Map<String,Map<String, NameValueBean>>  statisticsResultMapMap,String position) throws UedmException
    {
        try {

            ExportReportBO exportReportBO = getExportReportBO(resultList, headers, images, serviceBean, statisticsResultMapMap, position);

            String random = randomString(4);
            String mills = System.currentTimeMillis() + "" + random;
            String filePathStr1 = MASTER_DIRECTORY + mills + File.separator + fileName + fw.getExtension();
            String filePathStr = filePathStr1.replace("\\", "/");
            String fileDirS = MASTER_DIRECTORY + mills + "/";
            File fileDir = new File(fileDirS);

            if (!fileDir.exists())
            {
                boolean isMkdir = fileDir.mkdirs();
                log.info("is mkDir:" + isMkdir);
            }
            exportReportBO.setFileName(fileName);
            exportReportBO.setOutputFile(filePathStr);
            exportReportBO.setMills(mills);
            return exportReportBO;

        } catch (Exception e) {
            log.error("constructData error", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    private ExportReportBO getExportReportBO(List<Map<String, String>> resultList, List<String> headers, List<ImageInfo> images, ServiceBaseInfoBean serviceBean,
                                             Map<String,Map<String, NameValueBean>>  statisticsResultMapMap,String position) {
        ExportReportBO exportReportBO = new ExportReportBO();

        List<String[]> headerList = new ArrayList<>();
        List<String[][]> dataList = new ArrayList<>();
        List<String> titleList = new ArrayList<>();

        //统计
        String[][] satisticsData = new String[0][];
        if(!org.springframework.util.CollectionUtils.isEmpty(statisticsResultMapMap)){
            satisticsData = getStatisticsData(statisticsResultMapMap, position);
        }
        String[] statisticsHeader = getStatisticsheader(serviceBean);
        headerList.add(statisticsHeader);
        dataList.add(satisticsData);
        titleList.add(i18nUtils.getMapFieldByLanguageOption(STATISTICS_SHEET_NAME, serviceBean.getLanguageOption()));

        //概览
        String[] returnList = new String[headers.size()];
        Map<String, String> headerMap = BackupPowerOverviewDimEnums.initHeader();
        getEveryHead(serviceBean.getLanguageOption(), returnList, headers,headerMap);
        String[][] overViewData = getOverViewData(resultList, headers);

        headerList.add(returnList);
        dataList.add(overViewData);
        titleList.add(i18nUtils.getMapFieldByLanguageOption(OVERVIEW_SHEET_NAME, serviceBean.getLanguageOption()));
        log.info("getExportReportBO -> titleList:{}",titleList);

        exportReportBO.setDataMessList(dataList);
        exportReportBO.setColHeaderList(headerList);
        exportReportBO.setTitles(titleList);
        exportReportBO.setImages(images);
        return exportReportBO;
    }

    /**
     * 获取统计接口表头
     * @param serviceBean
     * @return
     */
    public String[] getStatisticsheader(ServiceBaseInfoBean serviceBean)
    {
        List<String> statisticsDims = BackupPowerOverviewStatisticsDimEnums.getAllId();
        int index = statisticsDims.size();
        String[] res = new String[index+2];
        res[1] = i18nUtils.getMapFieldByLanguageOption(
                BattExportParamEnums.POSITION.getName(), serviceBean.getLanguageOption());
        res[0] = i18nUtils.getMapFieldByLanguageOption(
                BackupPowerOverviewDimEnums.TYPE.getName(), serviceBean.getLanguageOption());
        for (int i = 0; i < statisticsDims.size(); i++) {
            String key = statisticsDims.get(i);
            String name = BackupPowerOverviewStatisticsDimEnums.getNameById(key);
            res[i+2] = i18nUtils.getMapFieldByLanguageOption(name, serviceBean.getLanguageOption());
        }
        return res;
    }

    private String[][] getStatisticsData(Map<String,Map<String, NameValueBean>>  statisticsResultMapMap, String position) {
        String[][] res = new String[statisticsResultMapMap.size()][6];
        int l = 0;

        for(Map.Entry<String, Map<String, NameValueBean>> entry : statisticsResultMapMap.entrySet()) {
            List<NameValueBean> beans = new ArrayList<>(entry.getValue().values());
            int index = beans.size();
            res[l][1] = position;
            res[l][0] = entry.getKey();
            for (int i = 0; i < index; i++) {
                if(null!=beans.get(i)) {
                    res[l][i + 2] = String.valueOf(beans.get(i).getNumber());
                }
                else{
                    res[l][i + 2] = null;
                }
            }
            l++;
        }
        return res;

    }

    private void  getEveryHead(String lang, String[] returnList,List<String> headers,Map<String, String> headerMap)
    {
        int k = 0;
        for (String headerId:headers)
        {
            String name = "";
            if (StringUtils.isNotBlank(BackupPowerOverviewDimEnums.getUnitById(headerId)))
            {
                String nameById = i18nUtils.getMapFieldByLanguageOption(headerMap.get(headerId), lang);
                name = nameById+"("+BackupPowerOverviewDimEnums.getUnitById(headerId)+")";
            }else
            {
                name = i18nUtils.getMapFieldByLanguageOption(headerMap.get(headerId), lang);
            }
            returnList[k++]=name;
        }
    }

    private String[][] getOverViewData(List<Map<String, String>> resultList,List<String> headers)
    {
        int index = headers.size();
        int size = resultList.size();
        int l = 0;
        String[][] res = new String[size][index];
        for (Map<String, String> stringStringMap : resultList)
        {
            for (int k = 0; k < index; k++) {
                res[l][k] = stringStringMap.getOrDefault(headers.get(k), "");
            }
            l++;
        }
        return res;
    }


    @Override
    public Map<String,Map<String, NameValueBean>> statisticsBackupPowerByStatisticsDim(BackupPowerStatisticsFilterDto dto, ServiceBaseInfoBean serviceBean) throws UedmException{
        Map<String,Map<String, NameValueBean>> res = new HashMap<>();
        if(null==dto.getDeviceType()||dto.getDeviceType().isEmpty()){
            res.put(BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getId(),statisticsSpByStatisticsDim(dto, serviceBean));
            res.put(BackupPowerDeviceTypeEnum.BATTERY_PACK.getId(),statisticsBpByStatisticsDim(dto, serviceBean));
        }else{
            if(dto.getDeviceType().contains(Power_Supply_Equipment)){
                res.put(BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getId(),statisticsSpByStatisticsDim(dto, serviceBean));
            }
            if(dto.getDeviceType().contains(BATTERY_PACK)){
                res.put(BackupPowerDeviceTypeEnum.BATTERY_PACK.getId(),statisticsBpByStatisticsDim(dto, serviceBean));
            }
        }
        return res;
    }

    public Map<String, NameValueBean> statisticsSpByStatisticsDim(BackupPowerStatisticsFilterDto dto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        Map<String, NameValueBean> result = new HashMap<>();
        try
        {
            String lang = serviceBean.getLanguageOption();
            String username = serviceBean.getUserName();
            //校验是否开启属性
            checkAttributeEnable(dto, serviceBean);
            //根据逻辑组id查询节点下所有开关电源
            List<String> logicGroupIdList = getLogicGroupIdList(dto.getLogicGroupId());
            List<BattBackupPowerEvalPojo> evalList = new ArrayList<>();
            try {
                evalList = battBackupPowerEvalMapper.selectByPathId(dto, logicGroupIdList, MocOptional.SP.getId());
            } catch (Exception e) {
                log.error("BackupPowerOverviewServiceImpl selectByPathId  select DB is error:{}",e.getMessage());
                throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
            }
            if (CollectionUtils.isEmpty(evalList)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim evalList is empty!");
                return result;
            }
            List<BattBackupPowerEvalPojo> resultList = backupPowerEvalDomain.getResultList(evalList, null, username);
            if (CollectionUtils.isEmpty(resultList)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim resultList is empty!");
                return result;
            }
            List<String> spMoIds = resultList.stream().map(BattBackupPowerEvalPojo::getId).collect(Collectors.toList());

            //过滤厂商等信息
            List<String> spMoIdsFromAsset = backupPowerAssetDomain.selectMoIdsFromAsset(new BackupPowerFilterDto(dto), spMoIds, lang);
            if (CollectionUtils.isEmpty(spMoIdsFromAsset)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim spMoIds from asset is empty!");
                return result;
            }
            List<String> finalSpMoIds = spMoIdsFromAsset;
            List<BattBackupPowerEvalPojo> finalEvalList = resultList.stream().filter(bean -> finalSpMoIds.contains(bean.getId())).collect(Collectors.toList());

            //组装数据
            result =  returnNumByDim(finalEvalList, dto.getDims(), lang);
            log.debug("BackupPowerOverviewServiceImpl-> statisticsBackupPowerByStatisticsDim result={}", result);
        }
        catch (UedmException ue)
        {
            log.error("statisticsSpByStatisticsDim-> statisticsBackupPowerByStatisticsDim uedmException error", ue);
            throw new UedmException(ue.getErrorId(), ue.getErrorDesc(), ue.getErrorData());
        }
        catch (Exception e)
        {
            log.error("statisticsSpByStatisticsDim-> statisticsBackupPowerByStatisticsDim Exception error", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError(e.getMessage());
        }
        return result;
    }

    public Map<String, NameValueBean> statisticsBpByStatisticsDim(BackupPowerStatisticsFilterDto dto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        Map<String, NameValueBean> result = new HashMap<>();
        try
        {
            String lang = serviceBean.getLanguageOption();
            String username = serviceBean.getUserName();
            //校验是否开启属性
            checkAttributeEnable(dto, serviceBean);
            List<String> logicGroupIdList = getLogicGroupIdList(dto.getLogicGroupId());
            List<BattBackupPowerEvalPojo> evalList = new ArrayList<>();
            try {
                evalList = battBackupPowerEvalMapper.selectByPathId(dto, logicGroupIdList, MocOptional.BATTERY_SET.getId());
            } catch (Exception e) {
                log.error("BackupPowerOverviewServiceImpl statisticsBpByStatisticsDim  select DB is error:{}",e.getMessage());
                throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
            }
            if (CollectionUtils.isEmpty(evalList)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim evalList is empty!");
                return result;
            }
            List<BattBackupPowerEvalPojo> resultList = backupPowerEvalDomain.getResultList(evalList, null, username);
            if (CollectionUtils.isEmpty(resultList)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim resultList is empty!");
                return result;
            }
            log.info("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim resultList.size:{}",resultList.size());
            List<BattBackupPowerEvalPojo> finalEvalList = filterBpAssetAttrParam(resultList,dto.getManufacturers(),dto.getBrands(),dto.getModels(),dto.getSeries());
            if (CollectionUtils.isEmpty(finalEvalList)) {
                log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim filter from battery is empty!");
                return result;
            }
            //组装数据
            result = returnNumByDim(finalEvalList, dto.getDims(), lang);
            log.debug("BackupPowerOverviewServiceImpl-> statisticsBackupPowerByStatisticsDim result={}", result);
        }
        catch (UedmException ue)
        {
            log.error("statisticsBpByStatisticsDim-> statisticsBackupPowerByStatisticsDim uedmException error", ue);
            throw new UedmException(ue.getErrorId(), ue.getErrorDesc(), ue.getErrorData());
        }
        catch (Exception e)
        {
            log.error("statisticsBpByStatisticsDim-> statisticsBackupPowerByStatisticsDim exception error", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError(e.getMessage());
        }
        return result;
    }

    public void checkAttributeEnable(BackupPowerStatisticsFilterDto dto, ServiceBaseInfoBean serviceBean) throws UedmException {
        Map<String, Boolean> attributeEnableMap = battAssetAttributeDomain.selectAttributeEnable(BatteryOverviewFilterRequestDto.attributeIds, serviceBean);
        Pair<Boolean, List<String>> checkAttributeRes = dto.checkAssetAttribute(attributeEnableMap);
        if(!checkAttributeRes.getLeft())
        {
            log.warn("BackupPowerOverviewServiceImpl statisticsBackupPowerByStatisticsDim -> the param is not open");
            throw new UedmException(UedmErrorCodeConstants.OBJECT_OPERATION_EXCEED_RANGE_NOT_ALLOW, null, JSON.toJSONString(checkAttributeRes.getRight()));
        }
    }

    @NotNull
    public List<String> getLogicGroupIdList(String logicGroupId) {
        List<String>  logicGroupIdList= new ArrayList<>();
        if(!Objects.equals(GlobalOptional.GLOBAL_ROOT,logicGroupId))
        {
            logicGroupIdList.add(logicGroupId);
        }
        return logicGroupIdList;
    }

    public Map<String, NameValueBean>  returnNumByDim(List<BattBackupPowerEvalPojo> finalEvalList, List<String> dims, String lang)
    {
        Map<String, NameValueBean> resultMap = new HashMap<>();
        for (String id: dims) {
            String name = i18nUtils.getMapFieldByLanguageOption(BackupPowerOverviewStatisticsDimEnums.getNameById(id), lang);
            if(BackupPowerOverviewStatisticsDimEnums.TOTAL.getId().equals(id))
            {
                NameValueBean bean = new NameValueBean(name, finalEvalList.size());
                resultMap.put(id, bean);
            }
            else if(BackupPowerOverviewStatisticsDimEnums.NORMAL.getId().equals(id))
            {
                List<BattBackupPowerEvalPojo> normalList = finalEvalList.stream().filter(bean -> BackupPowerStateEnum.NORMAL.getId().equals(bean.getStatus())).collect(Collectors.toList());
                NameValueBean bean = new NameValueBean(name, normalList.size());
                resultMap.put(id, bean);
            }
            else if(BackupPowerOverviewStatisticsDimEnums.DEFICIENCY.getId().equals(id))
            {
                List<BattBackupPowerEvalPojo> abnormalList = finalEvalList.stream().filter(bean -> BackupPowerStateEnum.DEFICIENCY.getId().equals(bean.getStatus())).collect(Collectors.toList());
                NameValueBean bean = new NameValueBean(name, abnormalList.size());
                resultMap.put(id, bean);
            }
            else if(BackupPowerOverviewStatisticsDimEnums.UNEVALUATE.getId().equals(id))
            {
                List<BattBackupPowerEvalPojo> unevalList = finalEvalList.stream().filter(bean -> BackupPowerStateEnum.UNEVALUATE.getId().equals(bean.getStatus())).collect(Collectors.toList());
                NameValueBean bean = new NameValueBean(name, unevalList.size());
                resultMap.put(id, bean);
            }
        }
        return resultMap;
    }

    @Override
    public PageInfo<Map<String, String>> selectByCondition(TableDetailSelectDto detailSelectDto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        //获取条件下监测对象ID
        List<String> allIds = getAllIds(detailSelectDto,  serviceBean);

        if(CollectionUtils.isEmpty(allIds))
        {
            return new PageInfo<>(new ArrayList<>());
        }
        List<Map<String, String>> resultList = new ArrayList<>();
        detailSelectDto.setSpIds(allIds);
        detailSelectDto.changeDeviceType();
        List<BattBackupPowerEvalPojo> allBackupPowerEvalPojo;
        try
        {
            allBackupPowerEvalPojo = backupPowerMapper.selectEvalDetailByCondition(detailSelectDto);
        }
        catch (Exception e)
        {
            log.error("BackupPowerOverviewServiceImpl selectByCondition  select DB is error:{}",e.getMessage());
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }
        allBackupPowerEvalPojo = filterByCondition(detailSelectDto,allBackupPowerEvalPojo);
        log.info("--optimize--allBackupPowerEvalPojo.size:{}", allBackupPowerEvalPojo.size());
        if(CollectionUtils.isEmpty(allBackupPowerEvalPojo))
        {
            log.info("selectByCondition all list is empty:{}",allBackupPowerEvalPojo);
            return  new PageInfo<>(resultList);
        }
        //根据监控对象id查询挂载站点
        Map<String, SiteBean> siteMap =new HashMap<>();
        Map<String, FieldEntity> fieldMap = fieldCacheManager.getFieldMapBeans();
        // 获取站点等级
        Map<String, String> siteLevelMap = getSiteLevel(serviceBean);
        log.info("selectByCondition siteLevelMap={}",siteLevelMap);
        // 获取供电场景
        Map<String, String> powerSupplySceneMap = getPowerSupplyScene(serviceBean);
        log.info("selectByCondition powerSupplySceneMap={}",powerSupplySceneMap);
        buildSiteMap(fieldMap ,siteMap);

        log.info("selectByCondition siteMap={}",siteMap.size());
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo = backupPowerDomain.filterDeletedSPDetailAndPage(detailSelectDto,allBackupPowerEvalPojo,siteMap,siteLevelMap,powerSupplySceneMap,serviceBean);
        List<BattBackupPowerEvalPojo> list = backupPowerEvalPojoPageInfo.getList();
        if(CollectionUtils.isEmpty(list))
        {
            log.info("selectByCondition list is empty:{}",list);
            return  new PageInfo<>(resultList);
        }
        log.info("selectByCondition list:{}",list.size());

        List<BatteryOverviewBean> dims = detailSelectDto.getDims();
        List<String>  dimIds=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dims))
        {
            dimIds=dims.stream()
                    .filter(item->StringUtils.isNoneBlank(item.getId()) && null!=item.getEnable() && item.getEnable())
                    .sorted(Comparator.comparing(BatteryOverviewBean::getDefaultIndex))
                    .map(BatteryOverviewBean::getId).collect(Collectors.toList());
        }

        // 属性id列表
        List<String> attributeIds = new ArrayList<>(BattOverViewDimEnums.keyAttributeMap.values());
        log.info("selectByCondition attributeIds.size():{}",attributeIds.size());
        log.info("selectByCondition dimIds.size():{}",dimIds.size());

        // 属性是否开启
        Map<String, Boolean> attributeMap = battAssetAttributeDomain.selectAttributeEnable(attributeIds, new ServiceBaseInfoBean(HeaderUtils.ROOT_USER,""));
        log.info("selectByCondition attributeMap:{}",attributeMap);
        Set<String> keyAttributeSet = BackupPowerOverviewDimEnums.keyAttributeMap.keySet();
        log.info("selectByCondition keyAttributeSet:{}",keyAttributeSet);
        list = setPojoListWhenValueNull(list,serviceBean);

        buildResultInfo(serviceBean, list, resultList, dimIds, attributeMap);
        PageInfo<Map<String, String>> resultPage = new PageInfo<>(resultList);
        resultPage.setTotal(backupPowerEvalPojoPageInfo.getTotal());
        return resultPage;
    }

    /* Started by AICoder, pid:wabd5gdf3dh058114a3d0b21d0aa702bfe4376b6 */
    private void buildSiteMap(Map<String, FieldEntity> fieldMap, Map<String, SiteBean> siteMap) {
        if (fieldMap.isEmpty()) {
            log.error("fieldCacheManager getFieldMapBeans is null:{}", fieldMap);
        }

        for (Map.Entry<String, FieldEntity> entry : fieldMap.entrySet()) {
            FieldEntity field = entry.getValue();
            if (field != null) {
                FieldDSEntity fieldDSEntity = new FieldDSEntity();
                try {
                    BeanUtils.copyProperties(fieldDSEntity, field);
                    fieldDSEntity.buildPriority();
                    fieldDSEntity.buildFieldPowerSupply();
                } catch (Exception e) {
                    log.error("getSiteList build Ex-attribute error:", e);
                }
                SiteBean siteBean = new SiteBean();
                buildSiteBean(field, fieldDSEntity, siteBean);

                siteMap.put(siteBean.getId(), siteBean);
            }
        }
    }
    /* Ended by AICoder, pid:wabd5gdf3dh058114a3d0b21d0aa702bfe4376b6 */

    /* Started by AICoder, pid:29d7bm1a5fu4b17142a808fd60ba0f16d710fe76 */
    private void buildSiteBean(FieldEntity field, FieldDSEntity fieldDSEntity, SiteBean siteBean) {
        siteBean.setId(field.getId());
        siteBean.setName(field.getName());
        siteBean.setPathName(field.getPathName());
        siteBean.setIdPath(field.toStringPathId());
        siteBean.setSiteLevel(fieldDSEntity.getSiteLevel());
        List<String> fieldPowerSupply = fieldDSEntity.getFieldPowerSupply();
        Collections.sort(fieldPowerSupply);
        siteBean.setPowerSupplyScene(String.join("-", fieldPowerSupply));
    }
    /* Ended by AICoder, pid:29d7bm1a5fu4b17142a808fd60ba0f16d710fe76 */

    /**
     * 将原本sql中使用in的筛选条件抽出
     */
    private List<BattBackupPowerEvalPojo> filterByCondition(TableDetailSelectDto detailSelectDto, List<BattBackupPowerEvalPojo> allBackupPowerEvalPojo) {
        log.info("--optimize--filterByCondition TableDetailSelectDto:{}", detailSelectDto);
        log.info("--optimize--filterByCondition allBackupPowerEvalPojo.size:{}", allBackupPowerEvalPojo.size());
        if (CollectionUtils.isEmpty(allBackupPowerEvalPojo)){
            return new ArrayList<>();
        }
        Stream<BattBackupPowerEvalPojo> stream = allBackupPowerEvalPojo.stream();

        stream = filterByType(detailSelectDto.getDeviceType(), stream);

        stream = filterByLogicGroupId(detailSelectDto.getLogicGroupId(),stream);

        stream = filterByIds(detailSelectDto.getSpIds(), stream);

        stream = filterByStatus(detailSelectDto.getStatus(), stream);

        stream = filterByScene(detailSelectDto.getApplicationScene(), stream);

        stream = filterByManufactures(detailSelectDto.getManufacturers(), stream);
        stream = filterByBrands(detailSelectDto.getBrands(), stream);
        stream = filterBySeries(detailSelectDto.getSeries(), stream);
        stream = filterByModels(detailSelectDto.getModels(), stream);

        stream = filterByName(detailSelectDto.getName(), stream);

        allBackupPowerEvalPojo = stream.collect(Collectors.toList());
        return allBackupPowerEvalPojo;
    }

    /* Started by AICoder, pid:8da94457c43c4aacbab77b0edc331709 */
    public Stream<BattBackupPowerEvalPojo> filterByIds(List<String> spIds, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isEmpty(spIds)){
            return new ArrayList<BattBackupPowerEvalPojo>().stream();
        }
        Set<String> idSet = new HashSet<>(spIds);
        return stream.filter(bean->idSet.contains(bean.getId()));
    }
    /* Ended by AICoder, pid:8da94457c43c4aacbab77b0edc331709 */
    public Stream<BattBackupPowerEvalPojo> filterByStatus(List<String> status, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isEmpty(status)){
            return stream;
        }
        return stream.filter(bean->status.contains(bean.getStatus()));
    }
    public Stream<BattBackupPowerEvalPojo> filterByScene(List<String> applicationScene, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isEmpty(applicationScene)){
            return stream;
        }
        return stream.filter(bean->applicationScene.contains(bean.getApplicationScene()));
    }
    /* Started by AICoder, pid:a2b0fc56ed5a47d6af425c205d696b4d */
    public Stream<BattBackupPowerEvalPojo> filterByType(List<String> deviceType, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isEmpty(deviceType)){
            return stream;
        }
        return stream.filter(bean->deviceType.contains(bean.getType()));
    }
    /* Ended by AICoder, pid:a2b0fc56ed5a47d6af425c205d696b4d */
    public Stream<BattBackupPowerEvalPojo> filterByManufactures(List<String> manufacturers, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isNotEmpty(manufacturers) && manufacturers.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean -> null==bean.getManufacture() || manufacturers.contains(bean.getManufacture()));
        }else if (CollectionUtils.isNotEmpty(manufacturers) && !manufacturers.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean -> manufacturers.contains(bean.getManufacture()));
        }
        return stream;
    }
    public Stream<BattBackupPowerEvalPojo> filterByBrands(List<String> brands, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isNotEmpty(brands) && brands.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean -> null==bean.getBrand() || brands.contains(bean.getBrand()));
        }else if (CollectionUtils.isNotEmpty(brands) && !brands.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean -> brands.contains(bean.getBrand()));
        }
        return stream;
    }
    public Stream<BattBackupPowerEvalPojo> filterBySeries(List<String> series, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isNotEmpty(series) && series.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean-> null==bean.getSeries() || series.contains(bean.getSeries()));
        }else if (CollectionUtils.isNotEmpty(series) && !series.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean-> series.contains(bean.getSeries()));
        }
        return stream;
    }
    public Stream<BattBackupPowerEvalPojo> filterByModels(List<String> models, Stream<BattBackupPowerEvalPojo> stream) {
        if (CollectionUtils.isNotEmpty(models) && models.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean-> null==bean.getModel() || models.contains(bean.getSeries()));
        }else if (CollectionUtils.isNotEmpty(models) && !models.contains(UNKNOWN_LEFT)){
            stream = stream.filter(bean-> models.contains(bean.getSeries()));
        }
        return stream;
    }
    public Stream<BattBackupPowerEvalPojo> filterByName(String name, Stream<BattBackupPowerEvalPojo> stream) {
        /* Started by AICoder, pid:899f1e591d2b4457874fdc96330bcbb3 */
        if (StringUtils.isEmpty(name)){
            return stream;
        }



        String finalSelectName = name.toLowerCase();
        //数据库中name和pathName皆是非空字段
        return stream.filter(bean-> bean.getName().toLowerCase().contains(finalSelectName)||bean.getPathNames().toLowerCase().contains(finalSelectName));
        /* Ended by AICoder, pid:899f1e591d2b4457874fdc96330bcbb3 */
    }

    public  List<BattBackupPowerEvalPojo> setPojoListWhenValueNull(List<BattBackupPowerEvalPojo> list,ServiceBaseInfoBean serviceBean){
        list.forEach(pojo->{
            pojo.setManufacture(null==pojo.getManufacture()?i18nUtils.getMapFieldByLanguageOption(unknownManufacturePair.getRight(), serviceBean.getLanguageOption()):pojo.getManufacture());
            pojo.setBrand(null==pojo.getBrand()?i18nUtils.getMapFieldByLanguageOption(unknownBrandPair.getRight(), serviceBean.getLanguageOption()):pojo.getBrand());
            pojo.setModel(null==pojo.getModel()?i18nUtils.getMapFieldByLanguageOption(unknownModelPair.getRight(), serviceBean.getLanguageOption()):pojo.getModel());
            pojo.setSeries(null==pojo.getSeries()?i18nUtils.getMapFieldByLanguageOption(unknownSeriesPair.getRight(), serviceBean.getLanguageOption()):pojo.getSeries());
        });
        return list;
    }



    public List<String>  getAllIds(TableDetailSelectDto detailSelectDto, ServiceBaseInfoBean serviceBean) throws UedmException {
        List<String> ids = new ArrayList<>();
        List<String> spIds = new ArrayList<>();
        List<String> batteryIds = new ArrayList<>();
        List<String> authIds = configurationManagerRpcImpl.getAuthPositionsByUser(serviceBean.getUserName());
        if(null==detailSelectDto.getDeviceType()||detailSelectDto.getDeviceType().isEmpty()) {
            //查询缓存中所有开关电源id和独立组网id，根据当前用户权限进行过滤
            Set<String> mocSet = new HashSet<>();
            mocSet.add(MocOptional.SP.getId());
            mocSet.add(MocOptional.BATTPACK.getId());
            ids = deviceCacheManager.getDeviceByMoc(mocSet)
                    .stream().map(DeviceEntity::getId).collect(Collectors.toList());
        }
        else {
            if (detailSelectDto.getDeviceType().contains(Power_Supply_Equipment)) {
                //查询缓存中所有开关电源id，根据当前用户权限进行过滤
                spIds=deviceCacheManager.getDevicesByMoc(MocOptional.SP.getId())
                        .stream().map(DeviceEntity::getId).collect(Collectors.toList());
                spIds=filterIdListByAuth(spIds,authIds);
                ids.addAll(spIds);
            }
            if(detailSelectDto.getDeviceType().contains(BATTERY_PACK)){
                //查询缓存中所有独立组网id，根据当前用户权限进行过滤
                batteryIds=deviceCacheManager.getDevicesByMoc(MocOptional.BATTPACK.getId())
                        .stream().map(DeviceEntity::getId).collect(Collectors.toList());
                batteryIds=filterIdListByAuth(batteryIds,authIds);
                ids.addAll(batteryIds);
            }
        }
        return ids;
    }
    public List<String> filterIdListByAuth(List<String> list, List<String> authIds) throws UedmException
    {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(authIds)){
            return list;
        }
        List<String> resultList = new ArrayList<>();
        Set<String> authIdSets = new HashSet<>(authIds);
        //判断id是否在有权限的ids中
        for (String item : list)
        {
            if (authIdSets.contains(item))
            {
                resultList.add(item);
            }
        }
        return resultList;
    }
    public Stream<BattBackupPowerEvalPojo> filterByLogicGroupId(String logicGroupId, Stream<BattBackupPowerEvalPojo> stream) {
        if (GlobalOptional.GLOBAL_ROOT.equals(logicGroupId)){
            return stream;
        }
        return stream.filter(value -> {
            List<String> parentIds = Arrays.asList(value.getPathIds().split("/"));
            return parentIds.contains(logicGroupId);
        });
    }

    private void buildResultInfo(ServiceBaseInfoBean serviceBean, List<BattBackupPowerEvalPojo> list, List<Map<String, String>> resultList, List<String> dimIds, Map<String, Boolean> attributeMap) {
        for(BattBackupPowerEvalPojo bean: list)
        {
            BattBackupPowerEvalBo battBackupPowerEvalBo = new BattBackupPowerEvalBo(bean);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if(null != bean.getAcStopAlarmTime()) {
                battBackupPowerEvalBo.setAcStopAlarmTime(sdf.format(bean.getAcStopAlarmTime()));
            }
            if(null != bean.getPowerOffAlarmTime()) {
                battBackupPowerEvalBo.setPowerOffAlarmTime(sdf.format(bean.getPowerOffAlarmTime()));
            }

            // 如果是独立组网，则没有负载总功率
            if(BP.equals(bean.getType())){
                battBackupPowerEvalBo.setTotalLoadPower(i18nUtils.getMapFieldByLanguageOption(TOTAL_LOAD_POWER,serviceBean.getLanguageOption()));
            }

            Map<String, String>  attributes;
            try
            {
                attributes = BeanUtils.describe(battBackupPowerEvalBo);
            }
            catch (Exception e)
            {
                log.error("Format conversion exception:{}",e.getMessage());
                attributes=new HashMap<>();
            }
            log.info("selectByCondition attributes:{}",attributes);
            if(null!=attributes)
            {
                buildResultMap(serviceBean, resultList, dimIds, attributeMap, attributes, bean.getId());
            }
        }
    }

    public List<String> reSetAssetAttrParams( List<String> manufacturers, List<String> brands, List<String> series, List<String> models,List<String> spIds, ServiceBaseInfoBean serviceBean) throws UedmException {
        BackupPowerFilterDto backupPowerFilterDto = new BackupPowerFilterDto();
        backupPowerFilterDto.setManufacturers(manufacturers);
        backupPowerFilterDto.setBrands(brands);
        backupPowerFilterDto.setSeries(series);
        backupPowerFilterDto.setModels(models);
        //过滤厂商等信息
        return backupPowerAssetDomain.selectMoIdsFromAsset(backupPowerFilterDto, spIds, serviceBean.getLanguageOption());
    }

    public void buildResultMap(ServiceBaseInfoBean serviceBean, List<Map<String, String>> resultList, List<String> dimIds, Map<String, Boolean> attributeMap, Map<String, String> attributes,String id) {
        Map<String, String> everyAttrValueMap = new HashMap<>();
        everyAttrValueMap.put("id",id);
        for(String dimId: dimIds)
        {
            String asstAttrId = BattOverViewDimEnums.keyAttributeMap.get(dimId);
            if(StringUtils.isNotBlank(asstAttrId))
            {
                if(!attributeMap.getOrDefault(asstAttrId,true))
                {
                    continue;
                }
            }
            putToResultMap(serviceBean, attributes, everyAttrValueMap, dimId);
        }
        resultList.add(everyAttrValueMap);
    }

    private void putToResultMap(ServiceBaseInfoBean serviceBean, Map<String, String> attributes, Map<String, String> everyAttrValueMap, String dimId) {
        String attrValue = attributes.get(dimId);
        if(null!=attrValue  && attrValue.length()>0)
        {
            if(Objects.equals(dimId, BackupPowerOverviewDimEnums.POSITION.getId())) {
                attrValue = attrValue.substring(0, attrValue.lastIndexOf("/"));
            }
            if(Objects.equals(dimId, BackupPowerOverviewDimEnums.APPLICATION_SCENE.getId()))
            {
                attrValue = i18nUtils.getMapFieldByLanguageOption(BackupPowerApplicationSceneEnum.getNameById(attrValue),serviceBean.getLanguageOption());
            }
            if(Objects.equals(dimId, BackupPowerOverviewDimEnums.STATUS.getId()))
            {
                buildStatusInfo(serviceBean, attributes, everyAttrValueMap, attrValue);
                attrValue = i18nUtils.getMapFieldByLanguageOption(BackupPowerStateEnum.getNameById(attrValue), serviceBean.getLanguageOption());
            }
            attrValue = getDurationResultAttrValue(attrValue, attributes, dimId);
            attrValue = getDeviceTypeAttrValue(attrValue, dimId, serviceBean);
        }
        everyAttrValueMap.put(dimId,attrValue);
    }

    private Map<String, String> getSiteLevel(ServiceBaseInfoBean serviceBean) {
        Map<String, String> siteLevIdNameMap = new HashMap<>();
        //获取站点等级
        Map<String, SiteLevelBean> siteLevMap = siteLevelUtils.getAllSiteLevelIdRelatedSiteLevelBeanMap(serviceBean.getLanguageOption());
        List<SiteLevelBean> siteLevelBeans = new ArrayList<>(siteLevMap.values());
        siteLevIdNameMap = siteLevelBeans.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).filter(bean -> StringUtils.isNotBlank(bean.getName()))
                .collect(Collectors.toMap(SiteLevelBean::getId, SiteLevelBean::getName, (key1, key2) -> key2));
        log.info("getSiteLevel attrValue={}", siteLevIdNameMap);
        return siteLevIdNameMap;
    }

    private Map<String, String> getPowerSupplyScene(ServiceBaseInfoBean serviceBean) {
        Map<String, String> powerSupplyIdNameMap = new HashMap<>();
        //获取供电场景
        Map<String, PowerSupplySceneBean> powerSupplyMap = powerSupplySceneUtils.getAllPowerSupplySceneIdRelatedBeanMaps(serviceBean.getLanguageOption());
        List<PowerSupplySceneBean> powerSupplyList = new ArrayList<>(powerSupplyMap.values());
        powerSupplyIdNameMap = powerSupplyList.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).filter(bean -> StringUtils.isNotBlank(bean.getName()))
                .collect(Collectors.toMap(PowerSupplySceneBean::getId, PowerSupplySceneBean::getName, (key1, key2) -> key2));
        log.info("getPowerSupplyScene attrValue={}", powerSupplyIdNameMap);
        return powerSupplyIdNameMap;
    }

    public String  getDurationResultAttrValue(String attrValue,Map<String, String> attributes,String dimId){
        String durationLiAndLead = attributes.get(DURATION_LI_LEAD);
        if(Objects.equals(dimId, BackupPowerOverviewDimEnums.BACKUP_POWER_DURATION.getId())){
            if(attrValue.indexOf('.')!=attrValue.length()-3){
                attrValue = attrValue +"0";
            }
            if(null!= durationLiAndLead) {
                int index1 = durationLiAndLead.indexOf(":");
                int index2 = durationLiAndLead.indexOf(",");
                int index3 = durationLiAndLead.lastIndexOf(":");
                attrValue = attrValue + " (" +durationLiAndLead.substring(index1+1,index2)+" / "+durationLiAndLead.substring(index3+1)+ ")";
            }
        }
        return attrValue;
    }

    public String  getDeviceTypeAttrValue(String attrValue,String dimId,ServiceBaseInfoBean serviceBean){
        if(Objects.equals(dimId, BackupPowerOverviewDimEnums.TYPE.getId()))
        {
            if(attrValue.equals(SP)){
                attrValue = Power_Supply_Equipment;
            }else{
                attrValue = BATTERY_PACK;
            }
            attrValue = i18nUtils.getMapFieldByLanguageOption(BackupPowerDeviceTypeEnum.getNameById(attrValue),serviceBean.getLanguageOption());
        }
        return attrValue;
    }

    public void buildStatusInfo(ServiceBaseInfoBean serviceBean, Map<String, String> attributes, Map<String, String> everyAttrValueMap, String attrValue) {
        String statusId= attrValue;
        everyAttrValueMap.put("statusId",statusId);
        if(Objects.equals(statusId, BackupPowerStateEnum.UNEVALUATE.getId()))
        {
            String statusDetail = attributes.get("statusDetail");
            List<String> alarmList = new ArrayList<>();
            List<String> currList = new ArrayList<>();
            List<String> returnList = new ArrayList<>();
            if(StringUtils.isNotBlank(statusDetail))
            {
                try {
                    List<String> statusDetailList = jsonService.jsonToObject(statusDetail, List.class, String.class);
                    for(String item:statusDetailList)
                    {
                        if(StringUtils.equals(BatteryBackupPowerStateDetailEnum.getTypeById(item),"alarm")){
                            alarmList.add(i18nUtils.getMapFieldByLanguageOption(BatteryBackupPowerStateDetailEnum.getNameById(item), serviceBean.getLanguageOption()));
                        }else {
                            currList.add(i18nUtils.getMapFieldByLanguageOption(BatteryBackupPowerStateDetailEnum.getNameById(item), serviceBean.getLanguageOption()));
                        }

                    }
                }
                catch (UedmException e)
                {
                    log.warn("map:{} object To Json error attrValue", statusDetail);
                }
            }
            if(!alarmList.isEmpty()){
                returnList.add(i18nUtils.getMapFieldByLanguageOption(BackupPowerOverviewDimEnums.ALARM,serviceBean.getLanguageOption()));
                returnList.addAll(alarmList);
                returnList.add("; "+i18nUtils.getMapFieldByLanguageOption(BackupPowerOverviewDimEnums.CURR,serviceBean.getLanguageOption()));
                returnList.addAll(currList);
            }else {
                returnList.add(i18nUtils.getMapFieldByLanguageOption(BackupPowerOverviewDimEnums.CURR,serviceBean.getLanguageOption()));
                returnList.addAll(currList);
            }

            statusDetail=returnList.toString().replace("[","")
                    .replace("]","")
                    .replace(",","");
            everyAttrValueMap.put("unknownReason",statusDetail);
        }
    }

//    public String buildSurplusDuration(String dimId, String attrValue,String lang)
//    {
//        if(Objects.equals(dimId, BackupPowerOverviewDimEnums.SURPLUS_DISCHARGE_DURATION.getId()))
//        {
//            try {
//                Map<String,Double> map = jsonService.jsonToObject(attrValue, Map.class,String.class,Double.class);
//                Map<String, String> resultMap = new HashMap<>();
//                for(Map.Entry<String,Double> entry:map.entrySet())
//                {
//                    String attrName = BackupPowerApplicationSceneEnum.getNameById(entry.getKey());
//                    if(StringUtils.isNotBlank(attrName))
//                    {
//                        attrName=i18nUtils.getMapFieldByLanguageOption(attrName,lang);
//                        Double attrValueDou =entry.getValue();
//                        String resultValue="--";
//                        if(null!=attrValueDou)
//                        {
//                            resultValue =String.valueOf(attrValueDou);
//                        }
//                        resultMap.put(attrName,resultValue);
//                    }
//                }
//                String resultListValue = resultMap.toString();
//                resultListValue=resultListValue.replace("{","")
//                                                .replace(",",";")
//                                                .replace("}","")
//                                                .replace("=",":");
//                if(StringUtils.isBlank(resultListValue))
//                {
//                    attrValue="--";
//                }
//                else
//                {
//                    attrValue=resultListValue;
//                }
//            }
//            catch (UedmException e)
//            {
//                log.warn("map:{} object To Json error attrValue", attrValue);
//            }
//        }
//        return attrValue;
//    }


    @Override
    public Map<String, DecreaseStatisticsByDimsVo> selectDecreaseStatistics(DecreaseStatisticsNewDto dto, ServiceBaseInfoBean serviceBean) throws UedmException{

        List<String> dims = dto.getDims();
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(dims))
        {
            // 无需要统计的维度id列表
            log.info("dims is empty. return new HashMap");
            return new HashMap<>();
        }
        if(null != dto.getDeviceType() && 0 != dto.getDeviceType().size()){
            dims.retainAll(dto.getDeviceType());
        }
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(dims))
        {
            // 无需要统计的列表
            log.info("selectDecreaseStatistics : dims is empty, return new HashMap");
            return new HashMap<>();
        }
        //初始化数据
        Map<String, DecreaseStatisticsByDimsVo> dimReStatisticsMap = new HashMap<>(dims.size());
        for(String dim : dims)
        {
            dimReStatisticsMap.put(dim, new DecreaseStatisticsByDimsVo(dim,
                    i18nUtils.getMapFieldByLanguageOption(BackupPowerStatisticsDimEnums.getNameById(dim), serviceBean.getLanguageOption()), new ArrayList<>()));
        }

        DecreaseStatisticsByDimsVo decreaseStatisticsByDimsVoSp = dimReStatisticsMap.get(BackupPowerStatisticsDimEnums.Power_Supply_Equipment.getId());
        // 属性id列表
        List<String> attributeIds = new ArrayList<>(BattOverViewDimEnums.keyAttributeMap.values());
        log.info("selectSpDecreaseStatistics attributeIds.size():{}",attributeIds.size());
        // 属性是否开启
        Map<String, Boolean> attributeMap = battAssetAttributeDomain.selectAttributeEnable(attributeIds, new ServiceBaseInfoBean(HeaderUtils.ROOT_USER,""));
        log.info("selectSpDecreaseStatistics attributeMap:{}",attributeMap);
        // 查询有权限的id
        List<String> authIds = configurationManagerRpcImpl.getAuthPositionsByUser(serviceBean.getUserName());

        dto.setBrands(buildAssetAttrEnable(attributeMap,  dto.getBrands(), AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BRAND));
        dto.setManufacturers(buildAssetAttrEnable(attributeMap,  dto.getManufacturers(),AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MANUFACTURER));
        dto.setModels(buildAssetAttrEnable(attributeMap,  dto.getModels(), AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_MODELS));
        dto.setSeries(buildAssetAttrEnable(attributeMap,  dto.getSeries(),AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_SERIES));

        List<BattBackupPowerEvalPojo> spCurrList = new ArrayList<>();
        List<BattBackupPowerEvalPojo> spPreList = new ArrayList<>();
        List<BattBackupPowerEvalPojo> bpCurrList = new ArrayList<>();
        List<BattBackupPowerEvalPojo> bpPreList = new ArrayList<>();

        // 获取上个月和本月开关电源和电池组的评估数据
        try
        {
            getAllEvalPojos(dto, spCurrList, bpCurrList, spPreList, bpPreList);
        }
        catch (Exception e)
        {
            log.error("BackupPowerOverviewServiceImpl getAllEvalPojos select DB is error:{}",e.getMessage());
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
        }

        if(null != decreaseStatisticsByDimsVoSp)
        {
            List<DecreaseStatisticsVo>  statisticsSP = selectSpDecreaseStatistics(attributeMap, dto, authIds, spCurrList, spPreList, serviceBean);
            decreaseStatisticsByDimsVoSp.setStatistics(statisticsSP);
        }

        DecreaseStatisticsByDimsVo decreaseStatisticsByDimsVoBp = dimReStatisticsMap.get(BackupPowerStatisticsDimEnums.BATTERY_PACK.getId());
        if(null != decreaseStatisticsByDimsVoBp)
        {
            List<DecreaseStatisticsVo>  statisticsBp = selectBpDecreaseStatistics(attributeMap, dto, authIds, bpCurrList, bpPreList, serviceBean);
            decreaseStatisticsByDimsVoBp.setStatistics(statisticsBp);
        }
        return  dimReStatisticsMap;
    }

    private void getAllEvalPojos(DecreaseStatisticsNewDto dto, List<BattBackupPowerEvalPojo> spCurrList, List<BattBackupPowerEvalPojo> bpCurrList, List<BattBackupPowerEvalPojo> spPreList, List<BattBackupPowerEvalPojo> bpPreList) throws UedmException {
        //根据当月时间查询当月数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String currMonth = sdf.format(new Date());
        dto.setEvalTime(currMonth);
        List<BattBackupPowerEvalPojo> currList = backupPowerMapper.selectBackupPowerEvalMByCondition(dto);
        //查询上月数据
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.MONTH,-1);
        Date time = instance.getTime();
        String preMonth = sdf.format(time);
        dto.setEvalTime(preMonth);
        List<BattBackupPowerEvalPojo> preList = backupPowerMapper.selectBackupPowerEvalMByCondition(dto);

        currList.forEach(bean->{
            if (SP.equals(bean.getType())){
                spCurrList.add(bean);
            }else if (BP.equals(bean.getType())){
                bpCurrList.add(bean);
            }
        });
        preList.forEach(bean->{
            if (SP.equals(bean.getType())){
                spPreList.add(bean);
            }else if (BP.equals(bean.getType())){
                bpPreList.add(bean);
            }
        });
    }

    public List<DecreaseStatisticsVo> selectSpDecreaseStatistics(Map<String, Boolean> attributeMap, DecreaseStatisticsNewDto dto, List<String> authIds, List<BattBackupPowerEvalPojo> currList,  List<BattBackupPowerEvalPojo> preList, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        //根据逻辑组id查询节点下所有开关电源
        List<String> allIds = deviceCacheManager.getDevicesByMoc(MocOptional.SP.getId())
                .stream().map(DeviceEntity::getId).collect(Collectors.toList());
        log.info("--optimize--selectSpDecreaseStatistics spCacheManager.getSpIds:{}", allIds.size());
        List<String> spIds = filterIdListByAuth(allIds, authIds);
        if(CollectionUtils.isEmpty(spIds))
        {
            log.info("backPowerInDecreaseSelect -> select cfg-ms spIds is empty:{}",spIds);
            return new ArrayList<>();
        }
        dto.setSpIds(spIds);
        dto.setDeviceType(new ArrayList<>());
        currList = filterByCondition(new TableDetailSelectDto(dto),currList);
        log.info("--optimize--selectSpDecreaseStatistics selectByCondition currList.size:{}", currList.size());
        preList = filterByCondition(new TableDetailSelectDto(dto), preList);
        log.info("--optimize--selectSpDecreaseStatistics selectByCondition preList.size:{}",preList.size());
        //根据状态组装获取当月每种状态数量
        Map<String, Long> currCountMap = currList.stream()
                .filter(item->StringUtils.isNotBlank(item.getStatus()))
                .collect(Collectors.groupingBy(BattBackupPowerEvalPojo::getStatus, Collectors.counting()));
        log.info("selectByCondition currCountMap:{}",currCountMap);
        //根据状态组装获取上月每种状态数量
        Map<String, Long> preCountMap = preList.stream()
                .filter(item->StringUtils.isNotBlank(item.getStatus()))
                .collect(Collectors.groupingBy(BattBackupPowerEvalPojo::getStatus, Collectors.counting()));
        log.info("selectByCondition preCountMap:{}",preCountMap);
        List<DecreaseStatisticsVo> result = new ArrayList<>();
        List<BackupPowerStateEnum> statusEnumList = Arrays.asList(BackupPowerStateEnum.values());
        Map<String, DecreaseStatisticsVo> resultVoMap =new HashMap<>();
        if(currCountMap.isEmpty() && preCountMap.isEmpty())
        {
            log.info("selectByCondition select by db is empty");
            return result;
        }
        statusEnumList.forEach(item->{
            DecreaseStatisticsVo decreaseStatisticsVo = new DecreaseStatisticsVo();
            decreaseStatisticsVo.setId(item.getId());
            decreaseStatisticsVo.setName(i18nUtils.getMapFieldByLanguageOption(item.getName(), serviceBean.getLanguageOption()));
            decreaseStatisticsVo.setSequence(String.valueOf(item.getSequence()));

            Long currValue = currCountMap.get(item.getId());
            Long preValue = preCountMap.get(item.getId());
            decreaseStatisticsVo.setCurrentNumber(null==currValue?null:String.valueOf(currValue));
            decreaseStatisticsVo.setPreNumber(null==preValue?null:String.valueOf(preValue));
            Long countVariation = countVariation(currValue, preValue);
            decreaseStatisticsVo.setChangeNumber(null==countVariation?null:String.valueOf(countVariation));
            resultVoMap.put(item.getId(),decreaseStatisticsVo);
        });
        result=resultVoMap.values().stream().sorted(Comparator.comparing(DecreaseStatisticsVo::getSequence)).collect(Collectors.toList());
        return result;
    }

    public List<DecreaseStatisticsVo> selectBpDecreaseStatistics(Map<String, Boolean> attributeMap, DecreaseStatisticsNewDto dto, List<String> authIds, List<BattBackupPowerEvalPojo> currList,  List<BattBackupPowerEvalPojo> preList, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        // 查询节点下所有电池组(独立组网)
        List<String> allIds = deviceCacheManager.getDevicesByMoc(MocOptional.BATTPACK.getId())
                .stream().map(DeviceEntity::getId).collect(Collectors.toList());
        log.info("--optimize--selectBpDecreaseStatistics battPackCacheManager.getBpIds:{}", allIds.size());
        List<String> spIds = filterIdListByAuth(allIds, authIds);
        if(CollectionUtils.isEmpty(spIds))
        {
            log.info("backPowerInDecreaseSelect -> select cfg-ms spIds is empty:{}",spIds);
            return new ArrayList<>();
        }
        dto.setSpIds(spIds);
        dto.setDeviceType(new ArrayList<>());
        currList = filterByCondition(new TableDetailSelectDto(dto), currList);
        log.info("--optimize--selectBpDecreaseStatistics selectByCondition currList.size:{}",currList.size());
        preList = filterByCondition(new TableDetailSelectDto(dto), preList);
        log.info("selectByCondition preList.size:{}",preList.size());
        //根据状态组装获取当月每种状态数量
        Map<String, Long> currCountMap = currList.stream()
                .filter(item->StringUtils.isNotBlank(item.getStatus()))
                .collect(Collectors.groupingBy(BattBackupPowerEvalPojo::getStatus, Collectors.counting()));
        log.info("selectByCondition currCountMap:{}",currCountMap);
        //根据状态组装获取上月每种状态数量
        Map<String, Long> preCountMap = preList.stream()
                .filter(item->StringUtils.isNotBlank(item.getStatus()))
                .collect(Collectors.groupingBy(BattBackupPowerEvalPojo::getStatus, Collectors.counting()));
        log.info("selectByCondition preCountMap:{}",preCountMap);
        List<DecreaseStatisticsVo> result = new ArrayList<>();
        List<BackupPowerStateEnum> statusEnumList = Arrays.asList(BackupPowerStateEnum.values());
        Map<String, DecreaseStatisticsVo> resultVoMap =new HashMap<>();
        if(currCountMap.isEmpty() && preCountMap.isEmpty())
        {
            log.info("selectByCondition select by db is empty");
            return result;
        }
        statusEnumList.forEach(item->{
            DecreaseStatisticsVo decreaseStatisticsVo = new DecreaseStatisticsVo();
            decreaseStatisticsVo.setId(item.getId());
            decreaseStatisticsVo.setName(i18nUtils.getMapFieldByLanguageOption(item.getName(), serviceBean.getLanguageOption()));
            decreaseStatisticsVo.setSequence(String.valueOf(item.getSequence()));

            Long currValue = currCountMap.get(item.getId());
            Long preValue = preCountMap.get(item.getId());
            decreaseStatisticsVo.setCurrentNumber(null==currValue?null:String.valueOf(currValue));
            decreaseStatisticsVo.setPreNumber(null==preValue?null:String.valueOf(preValue));
            Long countVariation = countVariation(currValue, preValue);
            decreaseStatisticsVo.setChangeNumber(null==countVariation?null:String.valueOf(countVariation));
            resultVoMap.put(item.getId(),decreaseStatisticsVo);
        });
        result=resultVoMap.values().stream().sorted(Comparator.comparing(DecreaseStatisticsVo::getSequence)).collect(Collectors.toList());
        return result;
    }

    public  List<BattBackupPowerEvalPojo>  filterBpAssetAttrParam(List<BattBackupPowerEvalPojo> currList,List<String> manufacturers, List<String> brands, List<String> models, List<String> series){
        BackupPowerFilterDto backupPowerFilterDto = new BackupPowerFilterDto();
        backupPowerFilterDto.setManufacturers(manufacturers);
        backupPowerFilterDto.setBrands(brands);
        backupPowerFilterDto.setSeries(series);
        backupPowerFilterDto.setModels(models);
        if(backupPowerFilterDto.noExistFilter())
        {
            //若供应商等筛选条件为空，则不无需通过资产过滤
            log.info("selectBpDecreaseStatistics filter asset condition is empty");
            return currList;
        }
        currList = filterBattBackupPowerEvalPojosByManu(currList, manufacturers);
        currList = filterBattBackupPowerEvalPojosByBrands(currList, brands);
        currList = filterBattBackupPowerEvalPojosByModels(currList, models);
        currList = filterBattBackupPowerEvalPojosBySeries(currList, series);
        return  currList;
    }

    private List<BattBackupPowerEvalPojo> filterBattBackupPowerEvalPojosByManu(List<BattBackupPowerEvalPojo> list, List<String> manufactures)
    {
        if (CollectionUtils.isNotEmpty(manufactures) && manufactures.contains(UNKNOWN_LEFT))
        {
            list = list.stream()
                    .filter(bean-> manufactures.contains(bean.getManufactureId()) || StringUtils.isBlank(bean.getManufactureId()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(manufactures))
        {
            list = list.stream().filter(bean-> manufactures.contains(bean.getManufactureId()))
                    .collect(Collectors.toList());
        }
        log.info("filterBpAssetAttrParam filter brands BattBackupPowerEvalPojos size={}",list.size());
        return list;
    }


    private List<BattBackupPowerEvalPojo> filterBattBackupPowerEvalPojosByModels(List<BattBackupPowerEvalPojo> list, List<String> models)
    {
        if (CollectionUtils.isNotEmpty(models) && models.contains(UNKNOWN_LEFT))
        {
            list = list.stream()
                    .filter(bean-> models.contains(bean.getModelId()) || StringUtils.isBlank(bean.getModelId()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(models))
        {
            list = list.stream().filter(bean-> models.contains(bean.getModelId()))
                    .collect(Collectors.toList());
        }
        log.info("filterBattBackupPowerEvalPojosByModels brands BattBackupPowerEvalPojos size={}",list.size());
        return list;
    }


    private List<BattBackupPowerEvalPojo> filterBattBackupPowerEvalPojosBySeries(List<BattBackupPowerEvalPojo> list, List<String> series)
    {
        if (CollectionUtils.isNotEmpty(series) && series.contains(UNKNOWN_LEFT))
        {
            list = Optional.ofNullable(list.stream()
                    .filter(bean-> series.contains(bean.getSeriesId()) || StringUtils.isBlank(bean.getSeriesId()))
                    .collect(Collectors.toList())).orElse(new ArrayList<>());
        }
        else if (CollectionUtils.isNotEmpty(series))
        {
            list = Optional.ofNullable(list.stream().filter(bean-> series.contains(bean.getSeriesId()))
                    .collect(Collectors.toList())).orElse(new ArrayList<>());
        }
        log.info("filterBattBackupPowerEvalPojosBySeries brands BattBackupPowerEvalPojos size={}",list.size());
        return list;
    }

    private List<BattBackupPowerEvalPojo> filterBattBackupPowerEvalPojosByBrands(List<BattBackupPowerEvalPojo> list, List<String> brands)
    {
        if (CollectionUtils.isNotEmpty(brands) && brands.contains(UNKNOWN_LEFT))
        {
            list = list.stream()
                    .filter(bean-> brands.contains(bean.getBrandId()) || StringUtils.isBlank(bean.getBrandId()))
                    .collect(Collectors.toList());
        }
        else if (CollectionUtils.isNotEmpty(brands))
        {
            list = list.stream().filter(bean-> brands.contains(bean.getBrandId()))
                    .collect(Collectors.toList());
        }
        log.info("filterBattBackupPowerEvalPojosByBrands brands BattBackupPowerEvalPojos size={}",list.size());
        return list;
    }



    @Nullable
    private Long countVariation(Long currValue, Long preValue) {
        Long resultValue=null;
        if(null!= currValue)
        {
            if(null!= preValue)
            {
                resultValue= currValue - preValue;
            }
            else
            {
                resultValue= currValue;
            }
        }
        else
        {
            if(null!= preValue)
            {
                resultValue= -preValue;
            }
        }
        return resultValue;
    }

    public List<String> buildAssetAttrEnable(Map<String, Boolean> attributeMap, List<String> assetNameList,String attrName) {
        List<String> attrList=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(assetNameList))
        {
            String asstAttrId = BattOverViewDimEnums.keyAttributeMap.get(attrName);
            if(attributeMap.getOrDefault(asstAttrId,true))
            {
                attrList=assetNameList;
            }
        }
        return attrList;
    }

    @Override
    public Map<String, DecreaseDistributionByDimsVo> backPowerInDecreaseSelect(BackPowerInDecreaseSelectNewDto backPowerInDecreaseSelectDto, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        List<String> dims = backPowerInDecreaseSelectDto.getDims();
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(dims))
        {
            // 无需要统计的维度id列表
            log.info("backPowerInDecreaseSelect: dims is empty, return");
            return new HashMap<>();
        }
        if(null != backPowerInDecreaseSelectDto.getDeviceType() && !backPowerInDecreaseSelectDto.getDeviceType().isEmpty()){
            dims.retainAll(backPowerInDecreaseSelectDto.getDeviceType());
        }
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(dims))
        {
            // 无需要统计的列表
            log.info("backPowerInDecreaseSelect: dims is empty, return new HashMap");
            return new HashMap<>();
        }
        //初始化数据
        Map<String, DecreaseDistributionByDimsVo> dimReDistributionMap = new HashMap<>(dims.size());
        for(String dim : dims)
        {
            dimReDistributionMap.put(dim, new DecreaseDistributionByDimsVo(dim,
                    i18nUtils.getMapFieldByLanguageOption(BackupPowerStatisticsDimEnums.getNameById(dim), serviceBean.getLanguageOption()), new ArrayList<>(),0));
        }
        // 查询有权限的id
        List<String> authIds = configurationManagerRpcImpl.getAuthPositionsByUser(serviceBean.getUserName());
        DecreaseDistributionByDimsVo decreaseDistributionByDimsVoSp = dimReDistributionMap.get(BackupPowerStatisticsDimEnums.Power_Supply_Equipment.getId());

        List<BattBackupPowerEvalPojo> allBattBackupPowerEvalPojos = backupPowerMapper.selectEvalDByCondition(backPowerInDecreaseSelectDto);
        List<BattBackupPowerEvalPojo> spEvalPojos = new ArrayList<>();
        List<BattBackupPowerEvalPojo> bpEvalPojos = new ArrayList<>();
        allBattBackupPowerEvalPojos.forEach(bean -> {
            if (SP.equals(bean.getType())) {
                spEvalPojos.add(bean);
            } else if (BP.equals(bean.getType())) {
                bpEvalPojos.add(bean);
            }
        });

        if(null != decreaseDistributionByDimsVoSp)
        {
            PageInfo<BackPowerInDecreaseSelectVo> sp =  backPowerInDecreaseSelectSp( backPowerInDecreaseSelectDto, authIds, spEvalPojos, serviceBean);
            List<BackPowerInDecreaseSelectVo>  statisticsSP = sp.getList();
            decreaseDistributionByDimsVoSp.setStatistics(statisticsSP);
            decreaseDistributionByDimsVoSp.setNum(sp.getTotal());
        }

        DecreaseDistributionByDimsVo decreaseDistributionByDimsVoBp = dimReDistributionMap.get(BackupPowerStatisticsDimEnums.BATTERY_PACK.getId());
        if(null != decreaseDistributionByDimsVoBp)
        {
            PageInfo<BackPowerInDecreaseSelectVo> bp = backPowerInDecreaseSelectBp(backPowerInDecreaseSelectDto, authIds, bpEvalPojos, serviceBean);
            List<BackPowerInDecreaseSelectVo>  statisticsBp = bp.getList();
            decreaseDistributionByDimsVoBp.setStatistics(statisticsBp);
            decreaseDistributionByDimsVoBp.setNum(bp.getTotal());
        }
        return  dimReDistributionMap;

    }




    public PageInfo<BackPowerInDecreaseSelectVo> backPowerInDecreaseSelectSp(BackPowerInDecreaseSelectNewDto backPowerInDecreaseSelectDto, List<String> authIds, List<BattBackupPowerEvalPojo> spEvalPojos, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        List<BackPowerInDecreaseSelectVo> backPowerInDecreaseSelectVos;

        List<String> allIds = deviceCacheManager.getDevicesByMoc(MocOptional.SP.getId())
                .stream().map(DeviceEntity::getId).collect(Collectors.toList());
        log.info("--optimize--backPowerInDecreaseSelect -> sp cache size is {}", allIds.size());

        // 根据权限过滤id
        allIds = filterIdListByAuth(allIds, authIds);

        if (CollectionUtils.isEmpty(allIds))
        {
            log.info("backPowerInDecreaseSelectSp -> filterIdListByAuth is empty:{}",allIds);
            return new PageInfo<>(new ArrayList<>());
        }

        //过滤当前状态、使用场景
        backPowerInDecreaseSelectDto.setDeviceType(new ArrayList<>());
        spEvalPojos = filterByCondition(new TableDetailSelectDto(backPowerInDecreaseSelectDto, allIds), spEvalPojos);
        log.info("--optimize--backPowerInDecreaseSelect -> filterBattBackupPowerEvalPojos size is {}", spEvalPojos.size());
        Map<String, BattBackupPowerEvalPojo> filterBattBackupEvalPojoMap = spEvalPojos.stream().filter(bean -> StringUtils.isNotBlank(bean.getId()))
                .collect(Collectors.toMap(BattBackupPowerEvalPojo::getId, bean->bean, (key1, key2) -> key2));
        Set<String>set=filterBattBackupEvalPojoMap.keySet();
        if (CollectionUtils.isEmpty(set))
        {
            log.info("backPowerInDecreaseSelect -> select DB is empty:{}",set);
            return new PageInfo<>(new ArrayList<>());
        }
        log.info("--optimize--backPowerInDecreaseSelect -> filterIds size is {}", set.size());

        backPowerInDecreaseSelectVos = setResult(filterBattBackupEvalPojoMap, backPowerInDecreaseSelectDto.getLogicGroupId());
        //排序
        int i = backPowerInDecreaseSelectDto.getSort().equals(SortEnum.getDescSortID()) ? -1 : 1;
        backPowerInDecreaseSelectVos.sort(new ComparatorUtil(new String[] { backPowerInDecreaseSelectDto.getOrder() }, i));

        // 分页
        List<BackPowerInDecreaseSelectVo> result;
        Integer num = backPowerInDecreaseSelectDto.getNumber();
        int total = 0;
        if (num != null)
        {
            result = PageUtils.getPageList(backPowerInDecreaseSelectVos, 1, num);
            total = result.size();
        }else
        {
            total = backPowerInDecreaseSelectVos.size();
            result = PageUtils.getPageList(backPowerInDecreaseSelectVos, serviceBean.getPageNo(), serviceBean.getPageSize());
        }
        PageInfo<BackPowerInDecreaseSelectVo> page = new PageInfo<>(result);
        page.setTotal(total);

        return page;
    }

    public PageInfo<BackPowerInDecreaseSelectVo> backPowerInDecreaseSelectBp(BackPowerInDecreaseSelectNewDto backPowerInDecreaseSelectDto, List<String> authIds, List<BattBackupPowerEvalPojo> bpEvalPojos, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        List<BackPowerInDecreaseSelectVo> backPowerInDecreaseSelectVos;
        List<String> bpIds = deviceCacheManager.getDevicesByMoc(MocOptional.BATTPACK.getId())
                .stream().map(DeviceEntity::getId).collect(Collectors.toList());
        log.info("--optimize--backPowerInDecreaseSelect -> bp cache size is {}", bpIds.size());

        // 根据权限过滤id
        bpIds = filterIdListByAuth(bpIds, authIds);

        if (CollectionUtils.isEmpty(bpIds))
        {
            log.info("backPowerInDecreaseSelectBp -> filterIdListByAuth is empty:{}",bpIds);
            return new PageInfo<>(new ArrayList<>());
        }
        //过滤当前状态、使用场景
        backPowerInDecreaseSelectDto.setDeviceType(new ArrayList<>());
        bpEvalPojos = filterByCondition(new TableDetailSelectDto(backPowerInDecreaseSelectDto, bpIds), bpEvalPojos);
        log.info("backPowerInDecreaseSelect -> filterBattBackupPowerEvalPojos size is {}", bpEvalPojos.size());
        Map<String, BattBackupPowerEvalPojo> filterBattBackupEvalPojoMap = bpEvalPojos.stream().filter(bean -> StringUtils.isNotBlank(bean.getId()))
                .collect(Collectors.toMap(BattBackupPowerEvalPojo::getId, bean->bean, (key1, key2) -> key2));
        Set<String>set=filterBattBackupEvalPojoMap.keySet();
        if (CollectionUtils.isEmpty(set))
        {
            log.info("backPowerInDecreaseSelect -> select DB is empty:{}",set);
            return new PageInfo<>(new ArrayList<>());
        }
        log.info("backPowerInDecreaseSelect -> filterIds size is {}", set.size());

        backPowerInDecreaseSelectVos = setResult(filterBattBackupEvalPojoMap, backPowerInDecreaseSelectDto.getLogicGroupId());
        //排序
        int i = backPowerInDecreaseSelectDto.getSort().equals(SortEnum.getDescSortID()) ? -1 : 1;
        backPowerInDecreaseSelectVos.sort(new ComparatorUtil(new String[] { backPowerInDecreaseSelectDto.getOrder() }, i));

        // 分页
        List<BackPowerInDecreaseSelectVo> result;
        Integer num = backPowerInDecreaseSelectDto.getNumber();
        int total = 0;
        if (num != null)
        {
            result = PageUtils.getPageList(backPowerInDecreaseSelectVos, 1, num);
            total = result.size();
        }else
        {
            total = backPowerInDecreaseSelectVos.size();
            result = PageUtils.getPageList(backPowerInDecreaseSelectVos, serviceBean.getPageNo(), serviceBean.getPageSize());
        }
        PageInfo<BackPowerInDecreaseSelectVo> page = new PageInfo<>(result);
        page.setTotal(total);
        return page;
    }
    public List<BackPowerInDecreaseSelectVo> setResult(Map<String, BattBackupPowerEvalPojo> filterBattBackupEvalPojoMap, String logicGroupId)
    {
        List<BackPowerInDecreaseSelectVo> result = new ArrayList<>();
        Map<String, BackPowerInDecreaseSelectVo> resultMap = new HashMap<>();
        if (filterBattBackupEvalPojoMap.isEmpty())
        {
            return result;
        }
        log.info("--optimize--BackupPowerOverviewServiceImpl -> getResult, filterBattBackupEvalPojoMap is {}", filterBattBackupEvalPojoMap.size());
        for (BattBackupPowerEvalPojo value : filterBattBackupEvalPojoMap.values()) {
            List<String> parentIds = Arrays.asList(value.getPathIds().split("/"));
            List<String> pathNames = Arrays.asList(value.getPathNames().split("/"));
            if (GlobalOptional.GLOBAL_ROOT.equals(logicGroupId)){
                int index = -1;
                getVoMap(value, pathNames, index, resultMap);
            }else if (parentIds.contains(logicGroupId)){
                int index = parentIds.indexOf(logicGroupId);
                getVoMap(value, pathNames, index, resultMap);
            }
        }
        result.addAll(resultMap.values());
        log.info("--optimize--BackupPowerOverviewServiceImpl -> getResult, result is {}", result.size());
        return result;
    }

    private void getVoMap(BattBackupPowerEvalPojo value, List<String> pathNames, int index, Map<String, BackPowerInDecreaseSelectVo> resultMap) {
        String position = String.join("/", pathNames.subList(0, index +2));
        BackPowerInDecreaseSelectVo backPowerInDecreaseSelectVo = resultMap.get(position);
        if (null==backPowerInDecreaseSelectVo){
            backPowerInDecreaseSelectVo = new BackPowerInDecreaseSelectVo();
            int total = 0;
            int normal = 0;
            int deficiency = 0;
            int unEvaluate = 0;
            backPowerInDecreaseSelectVo.setTotal(total);
            backPowerInDecreaseSelectVo.setNormal(normal);
            backPowerInDecreaseSelectVo.setDeficiency(deficiency);
            backPowerInDecreaseSelectVo.setUnEvaluate(unEvaluate);
            backPowerInDecreaseSelectVo.setPosition(position);
            resultMap.put(position, backPowerInDecreaseSelectVo);
        }
        String status = getStatus(value);
        setNumber(status, backPowerInDecreaseSelectVo);
    }

    private String getStatus(BattBackupPowerEvalPojo battBackupPowerEvalPojo)
    {
        return battBackupPowerEvalPojo == null ? "" : battBackupPowerEvalPojo.getStatus();
    }

    public void setNumber(String status, BackPowerInDecreaseSelectVo backPowerInDecreaseSelectVo)
    {
        int total = backPowerInDecreaseSelectVo.getTotal();
        int normal = backPowerInDecreaseSelectVo.getNormal();
        int deficiency = backPowerInDecreaseSelectVo.getDeficiency();
        int unEvaluate = backPowerInDecreaseSelectVo.getUnEvaluate();
        if(StringUtils.isNotBlank(status))
        {
            if (BackupPowerStateEnum.NORMAL.getId().equals(status))
            {
                backPowerInDecreaseSelectVo.setTotal(total + 1);
                backPowerInDecreaseSelectVo.setNormal(normal + 1);
            }
            else if (BackupPowerStateEnum.DEFICIENCY.getId().equals(status))
            {
                backPowerInDecreaseSelectVo.setTotal(total + 1);
                backPowerInDecreaseSelectVo.setDeficiency(deficiency + 1);
            }
            else if (BackupPowerStateEnum.UNEVALUATE.getId().equals(status))
            {
                backPowerInDecreaseSelectVo.setTotal(total + 1);
                backPowerInDecreaseSelectVo.setUnEvaluate(unEvaluate + 1);
            }
        }

        log.debug("total is {}, normal is {}, deficiency is {}, unEvaluate is {}", total, normal, deficiency, unEvaluate);
    }



    @Override
    public String exportBackupPowerDistribution(ExportBackupPowerDistributionDto exportDto, ServiceBaseInfoBean serviceBaseInfoBean, HttpServletRequest request, HttpServletResponse response) throws UedmException
    {
        SimpleDateFormat sdf = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_DAY);
        log.info("BackupPowerOverviewServiceImpl -> exportBackupPowerDistribution : exportDto is {}, serviceBean is {}", exportDto, serviceBaseInfoBean);
        String file_Name = i18nUtils.getMapFieldByLanguageOption(BACKUP_POWER_DISTRIBUTION_FILE_NAME, serviceBaseInfoBean.getLanguageOption());
        String fileName = i18nUtils.getMapFieldByLanguageOption(BackupPowerDeviceTypeExportEnum.getNameById(exportDto.getDims().get(0)), serviceBaseInfoBean.getLanguageOption()) + file_Name+ "_" + (sdf.format(new Date()));

        FileExportWriter fw = wf.getWriter(ExportType.PICTURE);
        if (fw == null)
        {
            log.error("BackupPowerOverviewServiceImpl -> exportBackupPowerDistribution : fw is null");
            return "";
        }

        ExportReportBO exportReportBO = new ExportReportBO();
        try
        {
            //获取数据-3.3.3接口
            BackPowerInDecreaseSelectNewDto backPowerInDecreaseSelectDto = new BackPowerInDecreaseSelectNewDto();
            org.springframework.beans.BeanUtils.copyProperties(exportDto,backPowerInDecreaseSelectDto);
            Map<String, DecreaseDistributionByDimsVo>  map = backPowerInDecreaseSelect(backPowerInDecreaseSelectDto, serviceBaseInfoBean);
            List<BackPowerInDecreaseSelectVo> dataList = map.get(exportDto.getDims().get(0)).getStatistics();

            //将数据转为map，表头id：对应值
            List<Map<String, String>> resultMapList = transferResultListToMap(dataList,exportDto.getPosition());

            //表头数据
            List<String> headers = BackupPowerDistributionEnums.getAllId();

            exportReportBO = exportConstructDetailData(resultMapList,headers, fw,fileName,serviceBaseInfoBean);

            fw.setFormat(exportReportBO);
            fw.batchesGroupWriteData();
        } catch (Exception e) {
            log.error("exportBackupPowerDistribution ", e);
            throw new UedmException(-1, "exportBackupPowerDistribution ");
        }
        finally {
            closeFile(fw);
        }

        File file = new File(FileUtils.pathManipulation(exportReportBO.getOutputFile()));
        if (file.exists())
        {
            String srcStr = exportReportBO.getOutputFile();
            FileUtils.downloadPictureFile(srcStr, response, request);
        }
        log.info("----fileName:{}",fileName);
        return fileName;
    }

    /**
     * 构建表头对应数据
     * @param dataList
     * @param position
     * @return
     */
    public List<Map<String, String>> transferResultListToMap(List<BackPowerInDecreaseSelectVo> dataList,String position)
    {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(dataList))
        {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        for (BackPowerInDecreaseSelectVo vo:dataList)
        {
            Map<String, String> map = new HashMap<>();
            map.put(BackupPowerDistributionEnums.POSITION.getId(),position);
            map.put(BackupPowerDistributionEnums.NAME.getId(),vo.getPosition());
            map.put(BackupPowerDistributionEnums.NORMAL.getId(),String.valueOf(vo.getNormal()));
            map.put(BackupPowerDistributionEnums.DEFICIENCY.getId(),String.valueOf(vo.getDeficiency()));
            map.put(BackupPowerDistributionEnums.UN_EVALUATE.getId(),String.valueOf(vo.getUnEvaluate()));

            result.add(map);
        }
        return result;
    }

    private ExportReportBO exportConstructDetailData(List<Map<String, String>> resultMapList,List<String> headers,
                                                     FileExportWriter fw, String fileName,ServiceBaseInfoBean serviceBean) throws UedmException
    {
        try {
            // set data
            ExportReportBO exportReportBO = getExportDetailExportReportBO(resultMapList,headers,serviceBean);

            String random = RandomUtils.randomString(4);
            String mills = System.currentTimeMillis() + "" + random;
            String filePathStr1 = MASTER_DIRECTORY + mills + File.separator + fileName + fw.getExtension();
            String filePathStr=filePathStr1.replace("\\", "/");
            String filedirS=MASTER_DIRECTORY + mills+"/";
            File filedir = new File(filedirS);
            if (!filedir.exists())
            {
                boolean isMkdir = filedir.mkdirs();
                log.info("constructDetailData -> is mkdired:" + isMkdir);
            }
            exportReportBO.setFileName(fileName);
            exportReportBO.setOutputFile(filePathStr);
            exportReportBO.setMills(mills);
            return exportReportBO;
        } catch (Exception e) {
            log.error("constructDetailData error", e);
            throw new UedmException(-1, e.getMessage());
        }
    }
    /**
     * set data
     * @param resultMapList
     * @return
     */
    @NotNull
    private ExportReportBO getExportDetailExportReportBO(List<Map<String, String>> resultMapList,List<String> headers,
                                                         ServiceBaseInfoBean serviceBean)
    {
        ExportReportBO exportReportBO = new ExportReportBO();

        // 头部表头
        List<String[]> headerList = new ArrayList<>();
        // sheet页数据
        List<String[][]> dataList = new ArrayList<>();
        // 页名称
        List<String> titleList = new ArrayList<>();

        Map<String, String> headerMap = BackupPowerDistributionEnums.initHeader();
        String[] returnList=new String[headers.size()];
        getHead(serviceBean.getLanguageOption(),returnList,headers,headerMap);

        String[][] data = getOverViewData(resultMapList,headers);

        headerList.add(returnList);
        dataList.add(data);
        titleList.add(i18nUtils.getMapFieldByLanguageOption(DISTRIBUTION_SHEET_NAME, serviceBean.getLanguageOption()));

        exportReportBO.setDataMessList(dataList);
        exportReportBO.setColHeaderList(headerList);
        exportReportBO.setTitles(titleList);
        return exportReportBO;
    }

    private void getHead(String lang, String[] returnList, List<String> headers,Map<String, String> header)
    {
        int k = 0;
        for (String headerId:headers)
        {
            returnList[k++]=i18nUtils.getMapFieldByLanguageOption(header.get(headerId), lang);
        }
    }

    public void forCi(Integer num){
        if(1 == num){
            log.info("0");
        }
        if(2 == num){
            log.info("1");
        }
        if(3 == num){
            log.info("2");
        }
        if(4 == num){
            log.info("3");
        }
        if(5 == num){
            log.info("4");
        }
        if(6 == num){
            log.info("5");
        }
        if(7 == num){
            log.info("6");
        }
        if(8 == num){
            log.info("7");
        }
    }

}
