package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 远程控制命令信息
 */
@Setter
@Getter
@ToString
public class RemoteDataBean
{
    /**
     * 监控对象id
     */
    private String moId;

    /**
     * 标准测点id
     */
    private String smpId;

    /**
     * 标准测点值
     */
    private String value;

    private String logId;

    /**
     * 用户名
     */
    private String username;

    /**
     * ip
     */
    private String ip;
}
