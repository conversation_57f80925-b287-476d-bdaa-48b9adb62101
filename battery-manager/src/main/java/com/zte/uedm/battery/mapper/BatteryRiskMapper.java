package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.BattRiskEvalPojo;
import com.zte.uedm.battery.controller.batteryrisk.dto.RiskEvalDetailDto;
import com.zte.uedm.battery.controller.batteryrisk.vo.BattRiskEvalBean;
import com.zte.uedm.battery.controller.batteryrisk.vo.ResultRiskEvalSelectVo;
import com.zte.uedm.battery.controller.batteryrisk.vo.ResultRiskEvalVo;
import com.zte.uedm.battery.controller.batteryrisk.vo.RiskHtyStatisticsExportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatteryRiskMapper {

    /**
     * 风险历史-详情查询
     * @param vo
     * @param battIdList
     * @param sort
     * @return
     */
    List<ResultRiskEvalVo> evalDetailSelect(@Param("vo") ResultRiskEvalSelectVo vo, @Param("ids")List<String> battIdList,
                                            @Param("sort") String sort);

    /**
     * 根据时间范围查找
     * @param startTime
     * @param endTime
     * @return
     */
    List<BattRiskEvalPojo> getBattRiskByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
