package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.MocHistoryQueryToolBean;
import com.zte.uedm.battery.bean.pv.HistoryDataRequestConditionBean;

/**
 * @ Author     ：10260977
 * @ Date       ：14:57 2022/6/24
 * @ Description：监控对象历史数据查询
 * @ Modified By：
 * @ Version: 1.0
 */
public interface MoHistoryDataQueryDomain
{
    /**
     * 历史数据查询构造
     * @param pyTypeMoc
     * @param smpId
     * @param startTime
     * @param endTime
     * @return
     */
    HistoryDataRequestConditionBean buildHistoryDataConditionBean(String pyTypeMoc, String smpId,
            String startTime, String endTime);

    /**
     * 前一天通用查询bean
     *
     * @param moc
     * @return
     */
    MocHistoryQueryToolBean buildYesterdayQueryBean(String moc);

    /**
     * 前两天通用查询bean
     *
     * @param moc
     * @return
     */
    MocHistoryQueryToolBean buildTwoDayQueryBean(String moc);

}
