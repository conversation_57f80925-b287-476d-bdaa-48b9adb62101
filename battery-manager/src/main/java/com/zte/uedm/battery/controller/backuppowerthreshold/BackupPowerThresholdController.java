package com.zte.uedm.battery.controller.backuppowerthreshold;


import com.github.pagehelper.PageInfo;
import com.zte.log.filter.UserThreadLocal;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.MocIdsVO;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.*;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.CategoryConfigAddVo;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.CategoryDetailVo;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.SpecialSelectVo;
import com.zte.uedm.battery.enums.BackupPowerScenarioEnum;
import com.zte.uedm.battery.service.BackupPowerThresholdDetailService;
import com.zte.uedm.battery.service.BackupPowerThresholdService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.redis.service.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.*;

/**
 * 备电特殊设置
 */
@Path("backup-power-threshold")
@Component
@Slf4j
//@Api(value = "backup-power-threshold")
@Api(value = "备电阀值")
public class BackupPowerThresholdController {

    @Autowired
    private BackupPowerThresholdService backupPowerThresholdService;
    @Autowired
    private JsonService jsonService;

    @Autowired
    private BackupPowerThresholdDetailService backupPowerThresholdDetailService;

    @Autowired
    private MessageSenderService msgSenderService;

    @Autowired
    private RedisService redisService;

    private static final String PARAM_IS_BLANK = "param is blank.";

    private static final String CAN_NOT_GET_USER = "can not get username";
    private static final String USER_NAME_ZH = "用户名称 ：";
    private static final String USER_NAME_EN = "userName ：";

    private static final String SPECIAL_SETTINGS_FOR_STANDBY_POWER_ZH="备电特殊设置";
    private static final String SPECIAL_SETTINGS_FOR_STANDBY_POWER_EN="Special settings for standby power";

    private static final String ADD_SPECIAL_SETTINGS_ZH="新增特殊设置";
    private static final String ADD_SPECIAL_SETTINGS_EN="Add Special settings";

    private static final String UPDATE_SPECIAL_SETTINGS_ZH="更新特殊设置";
    private static final String UPDATE_SPECIAL_SETTINGS_EN="Update Special settings";

    private static final String DELETE_SPECIAL_SETTINGS_ZH="删除特殊设置";
    private static final String DELETE_SPECIAL_SETTINGS_EN="Delete Special settings";

    @POST
    @Path("/category/select-by-condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询分类设置", notes = "查询分类设置", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -200, message = "操纵数据库发生异常"),
            @ApiResponse(code = -100, message = "参数或者服务为空"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean selectByCondition(CategoryDetailDto categoryDetailDto,
                                          @HeaderParam("language-option")String languageOption,
                                          @Context HttpServletRequest request)
    {
        //logicGroupId 为空判断
        if (null==categoryDetailDto || StringUtils.isBlank(categoryDetailDto.getLogicGroupId()))
        {
            log.error("BackupPowerThresholdController -> selectByCondition params is blank");
            //返回异常消息code&msg
            return ResponseBeanUtils.getRemoteResponseBean(-301,PARAM_IS_BLANK);
        }
        log.info("BackupPowerThresholdController->start to selectByCondition categoryDetailDto:{}",categoryDetailDto);
        int total = 0;
        List<CategoryDetailVo> data = null;
        try
        {
            String username = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            categoryDetailDto.checkOrderAndSortAvailable();
            log.info("BackupPowerThresholdController -> start to selectByCondition categoryDetailDto:{}",categoryDetailDto);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(username,ip,languageOption, categoryDetailDto.getPageNo(), categoryDetailDto.getPageSize());
            // service层
            PageInfo<CategoryDetailVo> categoryDetailsVos = backupPowerThresholdService.selectByCondition(categoryDetailDto,serviceBaseInfoBean);
            total = (int) categoryDetailsVos.getTotal();
            data = categoryDetailsVos.getList();
        }catch (UedmException e)
        {
            log.error("BackupPowerThresholdController -> selectByCondition backup power threshold fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,data,total);
    }


    /**
     * 备电分类设置-添加
     * @param configAddDto
     * @param languageOption
     * @param request
     * @return
     */
    @POST
    @Path("/category/add")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "备电分类设置-添加", notes = "备电分类设置-添加", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -301, message = "参数错误"),
            @ApiResponse(code = -200, message = "操纵数据库发生异常"),
            @ApiResponse(code = -100, message = "参数或者服务为空"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean addCategoryConfig(@NotNull CategoryConfigAddDto configAddDto,
                                          @HeaderParam("language-option") String languageOption,
                                          @Context HttpServletRequest request)
    {
        try
        {
            log.info("addCategoryConfig configAddDto {}", configAddDto);
            ValidationResult validationResult = ValidationUtils.validateForDefalut(configAddDto);
            if (validationResult.isHasErrors())
            {
                String errorMsg = jsonService.objectToJson(validationResult.getPropertyErrMsg().keySet());
                log.error("addCategoryConfig param verify error {}", errorMsg);
                return ResponseBeanUtils.getCheckParamResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, errorMsg, null);
            }
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean(userName, ip, languageOption, false, true);
            CategoryConfigAddVo result = backupPowerThresholdService.battAddCategoryConfig(configAddDto, baseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, result, result.total());
        } catch (UedmException e) {
            log.error("addCategoryConfig exception {}, {}", e.getMessage(), e);
            ResponseBean responseBean = ResponseBeanUtils.getResponseBeanByException(e);
            if (e.getErrorId().intValue() == UedmErrorCodeConstants.PARAMETER_EXCEED_ALLOWD_RANGE.intValue())
            {
                responseBean = ResponseBeanUtils.getCheckParamResponseBean(e.getErrorId(), e.getErrorData(), e.getErrorDesc());
            }
            return responseBean;
        }
    }

    /**
     * 备电分类设置-编辑
     * @param configUpdateDto
     * @param languageOption
     * @param request
     * @return
     */
    @PUT
    @Path("/category/update")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "备电分类设置-更新", notes = "备电分类设置-更新", httpMethod = HttpMethod.PUT)
    @ApiResponses({
            @ApiResponse(code = -301, message = "参数验证异常"),
            @ApiResponse(code = -200, message = "操纵数据库发生异常"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean updateCategoryConfig(@NotNull CategoryConfigUpdateDto configUpdateDto,
                                             @HeaderParam("language-option") String languageOption,
                                             @Context HttpServletRequest request)
    {
        try
        {
            log.info("updateCategoryConfig configUpdateDto {}", configUpdateDto);
            ValidationResult validationResult = ValidationUtils.validateForDefalut(configUpdateDto);
            if (validationResult.isHasErrors())
            {
                String errorMsg = jsonService.objectToJson(validationResult.getPropertyErrMsg().keySet());
                log.error("updateCategoryConfig param verify error {}", errorMsg);
                return ResponseBeanUtils.getCheckParamResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, errorMsg, null);
            }
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean(userName, ip, languageOption, false, true);
            List<String> result = backupPowerThresholdService.battUpdateCategoryConfig(configUpdateDto, baseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, result, result.size());
        } catch (UedmException e) {
            log.error("updateCategoryConfig exception {}, {}", e.getMessage(), e);
            ResponseBean responseBean = ResponseBeanUtils.getResponseBeanByException(e);
            if (e.getErrorId().intValue() == UedmErrorCodeConstants.PARAMETER_EXCEED_ALLOWD_RANGE.intValue())
            {
                responseBean = ResponseBeanUtils.getCheckParamResponseBean(e.getErrorId(), e.getErrorData(), e.getErrorDesc());
            }
            return responseBean;
        }
    }
    /**
     * 新增特殊设置
     * @param specialAddDto aa
     * @param request aa
     * @param languageOption
     * @return
     * @throws UedmException
     */
    @POST
    @Path("/special/add")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "新增特殊设置", notes = "新增特殊设置", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -304, message = "参数不在范围内"),
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean specialAdd(SpecialSetDto specialAddDto,
            @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption) throws UedmException {
        boolean operStatus=true;
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(userName, host, connectMode, SPECIAL_SETTINGS_FOR_STANDBY_POWER_ZH,SPECIAL_SETTINGS_FOR_STANDBY_POWER_EN,
                OperlogBean.LogRank.operlog_rank_notice, ADD_SPECIAL_SETTINGS_ZH, ADD_SPECIAL_SETTINGS_EN);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption);
        int total = 0;
        try {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(specialAddDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("specialAdd, the parameter is blank. {}", blankSets);
                operStatus=false;
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-100,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            if(null == specialAddDto.getScenarioType()) {
                return ResponseBeanUtils.getParameterBlankResponseBean();
            }
            if(!BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode().equals(specialAddDto.getScenarioType().toString()) &&
                    !BackupPowerScenarioEnum.SCENARIO_INDEPENDENT_NET.getCode().equals(specialAddDto.getScenarioType().toString())) {
                return ResponseBeanUtils.getCheckParamResponseBean(UedmErrorCodeConstants.PARAMETER_EXCEED_ALLOWD_RANGE,
                        "param scenarioType not valid!", "param scenarioType not valid!");
            }
            total = backupPowerThresholdDetailService.insert(specialAddDto, serviceBaseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, specialAddDto.getBackupSystemIds(), total);
        }
        catch (UedmException e)
        {
            operStatus=false;
            log.error("BackupPowerThresholdController-> Add special setting  fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally
        {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            String backupIds = jsonService.objectToJson(specialAddDto.getBackupSystemIds());
            detailZh.append(USER_NAME_ZH).append(operlogBean.getUserName()).append(" , 新增特殊设置 ids ：")
                    .append(backupIds).append(", 新增特殊设置 阈值 ：").append(specialAddDto.getThreshold());
            detailEn.append(USER_NAME_EN).append(operlogBean.getUserName()).append(" , Add special settings ids ：")
                    .append(backupIds).append(", Add special settings threshold: ").append(specialAddDto.getThreshold());
            log.info("Add special setting detailZh:{}, detailEn:{}",detailZh,detailEn);
            if(operStatus)
            {
                operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            else
            {
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            String operMsg = jsonService.objectToJson(operlogBean);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }

    }

    /**
     * 更新特殊设置
     * @param specialAddDto
     * @param request
     * @param languageOption
     * @return
     * @throws UedmException
     */
    @PUT
    @Path("/special/update")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "更新特殊设置", notes = "更新特殊设置", httpMethod = HttpMethod.PUT)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常"),
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean specialUpdate(SpecialSetDto specialAddDto,
            @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption) throws UedmException {
        boolean operStatus=true;
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(userName, host, connectMode, SPECIAL_SETTINGS_FOR_STANDBY_POWER_ZH,SPECIAL_SETTINGS_FOR_STANDBY_POWER_EN,
                OperlogBean.LogRank.operlog_rank_important, UPDATE_SPECIAL_SETTINGS_ZH, UPDATE_SPECIAL_SETTINGS_EN);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption);
        int total = 0;
        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(specialAddDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("specialUpdate, the parameter is blank. {}", blankSets);
                operStatus=false;
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-100,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            total = backupPowerThresholdDetailService.update(specialAddDto, serviceBaseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, specialAddDto.getBackupSystemIds(), total);
        }
        catch (UedmException e)
        {
            log.error("BackupPowerThresholdController-> update special setting  fail.", e);
            operStatus=false;
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally
        {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            String backupIds = jsonService.objectToJson(specialAddDto.getBackupSystemIds());
            detailZh.append(USER_NAME_ZH).append(operlogBean.getUserName()).append(" , 更新特殊设置 ids ：")
                    .append(backupIds).append(", 更新特殊设置 阈值 ：").append(specialAddDto.getThreshold());
            detailEn.append(USER_NAME_EN).append(operlogBean.getUserName()).append(" , update special settings ids ：")
                    .append(backupIds).append(", update special settings threshold: ").append(specialAddDto.getThreshold());
            log.info("update special setting detailZh:{}, detailEn:{}",detailZh,detailEn);
            if(operStatus)
            {
                operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            else
            {
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            String operMsg = jsonService.objectToJson(operlogBean);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    /**
     * 备电分类设置-删除
     * @param id
     * @param languageOption
     * @param request
     * @return
     */
    @DELETE
    @Path("/category/delete")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "备电分类设置-删除", notes = "备电分类设置-删除", httpMethod = HttpMethod.DELETE)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常"),
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean deleteCategoryConfig(@QueryParam("id") String id,
                                             @HeaderParam("language-option") String languageOption,
                                             @Context HttpServletRequest request)
    {
        log.info("deleteCategoryConfig id {}", id);
        if (StringUtils.isBlank(id))
        {
            log.error("deleteCategoryConfig id is blank");
            return ResponseBeanUtils.getCheckParamResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, null, PARAM_IS_BLANK);
        }
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean(userName, ip, languageOption, false, true);
            List<String> result = backupPowerThresholdService.battDeleteCategoryConfig(id, baseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, result, result.size());
        } catch (UedmException e) {
            log.error("deleteCategoryConfig exception {}, {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }
    /**
     *  删除特殊设置--恢复默认设置
     * @param ids
     * @param request
     * @param languageOption
     * @return
     * @throws UedmException
     */
    @DELETE
    @Path("/special/delete")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "删除特殊设置", notes = "删除特殊设置", httpMethod = HttpMethod.DELETE)
    @ApiResponses({
            @ApiResponse(code = -100,message = "参数为空"),
            @ApiResponse(code = 0,message = "删除成功"),
            @ApiResponse(code = -200,message = "操作数据库发生异常")
    })
    public ResponseBean specialDelete(List<String> ids,
            @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption) throws UedmException {
        boolean operStatus=true;
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(userName, host, connectMode, SPECIAL_SETTINGS_FOR_STANDBY_POWER_ZH,SPECIAL_SETTINGS_FOR_STANDBY_POWER_EN,
                OperlogBean.LogRank.operlog_rank_important, DELETE_SPECIAL_SETTINGS_ZH, DELETE_SPECIAL_SETTINGS_EN);
        int total = 0;
        try
        {
            if (CollectionUtils.isEmpty(ids))
            {
                log.warn("specialDelete, the parameter is blank. {}", ids);
                operStatus=false;
                return ResponseBeanUtils.getCheckParamResponseBean(-100,PARAM_IS_BLANK, null);
            }
            total = backupPowerThresholdDetailService.delete(ids,userName);
            return ResponseBeanUtils.getNormalResponseBean(0, ids, total);
        }
        catch (UedmException e)
        {
            log.error("BackupPowerThresholdController-> delete special setting  fail.", e);
            operStatus=false;
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally
        {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            String backupIds = jsonService.objectToJson(ids);
            detailZh.append(USER_NAME_ZH).append(operlogBean.getUserName()).append(" , 删除特殊设置 ids ：")
                    .append(backupIds);
            detailEn.append(USER_NAME_EN).append(operlogBean.getUserName()).append(" , delete special settings ids ：")
                    .append(backupIds);
            log.info("delete special setting detailZh:{}, detailEn:{}",detailZh,detailEn);
            if(operStatus)
            {
                operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            else
            {
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            String operMsg = jsonService.objectToJson(operlogBean);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }


    @POST
    @Path("/special/select-by-condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询特殊设置", notes = "查询特殊设置", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常"),
            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = -302, message = "排序字段或者排序顺序不在范围内"),
            @ApiResponse(code = -303, message = "开始时间在结束时间之后"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean selectSpecialByCondition(SpecialSelectByConditionDto specialSelectByConditionDto,
            @Context HttpServletRequest request,@QueryParam("pageNo")Integer pageNo, @QueryParam("pageSize")Integer pageSize,
            @HeaderParam("language-option") String languageOption) throws UedmException
    {
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        int total = 0;
        List<SpecialSelectVo> data=null;
        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(specialSelectByConditionDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("selectSpecialByCondition, the parameter is blank. {}", blankSets);
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            if(!specialSelectByConditionDto.checkOrSetDefaltOrder())
            {
                log.error("order or sort is Not in the range of optional values");
                return ResponseBeanUtils.getCheckParamResponseBean(-302,null,"order or sort is Not in the range of optional values");
            }
            if(!specialSelectByConditionDto.checkStartEndTime())
            {
                log.error("The end time cannot be greater than the start time");
                return ResponseBeanUtils.getCheckParamResponseBean(-303,null,"The end time cannot be greater than the start time");
            }
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName,host,
                    languageOption,pageNo,pageSize);
            PageInfo<SpecialSelectVo> resultPage = backupPowerThresholdDetailService.selectSpecialByCondition(
                    specialSelectByConditionDto, serviceBaseInfoBean);
            data=resultPage.getList();
            total=(int)resultPage.getTotal();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BackupPowerThresholdController-> select special setting  fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @POST
    @Path("/special/select-by-power-supply")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询特殊设置", notes = "查询特殊设置", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常"),
            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -100, message = "参数为空"),
            @ApiResponse(code = -302, message = "排序字段或者排序顺序不在范围内"),
            @ApiResponse(code = -303, message = "开始时间在结束时间之后"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean selectSpecialByPowerSupply(@HeaderParam("language-option") String languageOption)
    {
        List<IdNameBean> idNameBeanList = backupPowerThresholdDetailService.selectSpecialByPowerSupply(languageOption);
        return ResponseBeanUtils.getNormalResponseBean(0, idNameBeanList, idNameBeanList.size());
    }

    @GET
    @Path("/special/tabs-condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询tab是否显示", notes = "查询tab是否显示", httpMethod = HttpMethod.GET)
    @ApiResponses({

            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -502, message = "RPC调用错误"),
            @ApiResponse(code = -604, message = "其他临时错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean selectTabsCountByCondition(@QueryParam("logicGroupId") String logicGroupId, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption) throws UedmException
    {
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        try
        {
            if (StringUtils.isBlank(logicGroupId))
            {
                log.warn("selectTabsCountByCondition, the parameter logicGroupId is blank. ");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                        PARAM_IS_BLANK, PARAM_IS_BLANK, null, 0);
            }

            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName,host,
                    languageOption, null, null);
            Map<String, Integer> resultMap =
                    backupPowerThresholdDetailService.selectTabsCountByCondition(logicGroupId, serviceBaseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, resultMap, null);
        }
        catch (UedmException e)
        {
            log.error("queryScenarioBasedMonitorObjects -> select scenario based monitor objects fail!", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            return ResponseBeanUtils.getNormalResponseBean(-301, null, e.getMessage(), null, null);
        }
    }

    @POST
    @Path("/special/query-monitor-objects")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询符合备电时长设置业务场景的监控对象", notes = "查询符合备电时长设置业务场景的监控对象", httpMethod = HttpMethod.POST)
    @ApiResponses({

            @ApiResponse(code = -301, message = "参数为空"),
            @ApiResponse(code = -502, message = "RPC调用错误"),
            @ApiResponse(code = -604, message = "其他临时错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    public ResponseBean queryScenarioBasedMonitorObjects(MocIdsVO mocIdsVO, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption) {
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);
        try
        {
            log.info("queryScenarioBasedMonitorObjects mocIdsVo={}", jsonService.objectToJson(mocIdsVO));
            if (null == mocIdsVO || StringUtils.isBlank(mocIdsVO.getScenarioType()))
            {
                log.warn("queryScenarioBasedMonitorObjects, the parameter is blank. ");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                        PARAM_IS_BLANK, PARAM_IS_BLANK, null, 0);
            }
            if(StringUtils.isBlank(userName) || StringUtils.isBlank(host)) {
                log.warn("queryScenarioBasedMonitorObjects, get user login name/host error.");
                return ResponseBeanUtils.getNormalResponseBean(UedmErrorCodeConstants.OTHER_TEMPORARY_ERROR,
                        CAN_NOT_GET_USER, CAN_NOT_GET_USER, null, 0);
            }

            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName,host,
                    languageOption, mocIdsVO.getPageNo(), mocIdsVO.getPageNo());
            PageInfo<MonitorObjectDsBean> objectDsBeans =
                    backupPowerThresholdDetailService.queryScenarioBasedMonitorObjects(mocIdsVO, serviceBaseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0, objectDsBeans.getList(), (int) objectDsBeans.getTotal());
        }
        catch (UedmException e)
        {
            log.error("queryScenarioBasedMonitorObjects -> select scenario based monitor objects fail!", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            return ResponseBeanUtils.getNormalResponseBean(-301, null, e.getMessage(), null, null);
        }
    }


}
