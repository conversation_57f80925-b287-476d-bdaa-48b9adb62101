package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants;
import com.zte.uedm.battery.bean.SeasonStrategyBean;
import com.zte.uedm.battery.bean.UpDownloadFileBean;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.ErrorDescConstants;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.mapper.TemplateStrategyMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.TemplateStrategyService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.GlobalizationInfo;
import com.zte.uedm.common.bean.IsKafkaBean;
import com.zte.uedm.common.bean.log.OperLogContants;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.enums.EnergyTypeEnum;
import com.zte.uedm.common.enums.peak.CycleModelEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.util.PageUtils;
import com.zte.uedm.common.util.UedmLogUtils;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TemplateStrategyServiceImpl implements TemplateStrategyService
{

    /*默认版本*/
    private static Double DEFAULT_VERSION = 1.00;
    /*编辑时版本增量*/
    private static Double DEFAULT_ADD_VERSION = 0.01;
    /*策略模板默认详情数量*/
    private static int DEFAULT_NUM = 8;

    public static final int NAME_LENGTH = 50;
    public static final String OPERATION_DELETE = "delete";

    @Autowired
    private TemplateStrategyMapper templateStrategyMapper;

    @Autowired
    private GridStrategyMapper gridStrategyMapper;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DateTimeService dateTimeService;

    @Autowired
    private MessageSenderService msgSenderService;

    @Autowired
    private GridStrategyServiceImpl gridStrategyService;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Autowired
    private I18nUtils i18nUtils;

    /**
     * 新增设备模板策略
     *
     * @param strategyBaseDto
     * @return
     * @throws UedmException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTemplateStrategy(TemplateStrategyAddDto strategyBaseDto,String userCreate) throws UedmException
    {
        //seasonStrategyId参数校验
        String seasonStrategyId = strategyBaseDto.getSeasonStrategyId();
        checkId(seasonStrategyId);

        Integer count = templateStrategyMapper.countName(strategyBaseDto.getName());
        if (count > 0)
        {
            /*名称重复*/
            log.error("TemplateStrategyServiceImpl -> addTemplateStrategy name is repeat");
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, ErrorDescConstants.NAME_IS_REPEAT);
        }
        //设备模板策略PO
        TemplateStrategyPo bean = strategyBaseDto.packageTemplateStrategyPo();
        String id = UUID.randomUUID().toString();
        if (CycleModelEnum.getNotCycleModelId().equals(strategyBaseDto.getMode()))
        {
            checkCsu5Template(strategyBaseDto.getDetail(),strategyBaseDto.getHoliday());
            bean.setWeekendFlag(strategyBaseDto.getWeekendFlag());
            List<TemplateStrategyDetailCsuPo> details = getTemplateStrategyCSUDetails(
                    strategyBaseDto.getDetail(),strategyBaseDto.getHoliday(), id);
            templateStrategyMapper.insertTemplateStrategyCsuDetail(details);
        }
        else
        {
            /*校验模板数量*/
            checkTemplate(strategyBaseDto.getMode(), strategyBaseDto.getDetail().size(),
                    strategyBaseDto.getHoliday().size());
            bean.setFileId(strategyBaseDto.getFileId());
            List<TemplateStrategyDetailBcuaPo> details = getTemplateStrategyBCUADetails(strategyBaseDto.getDetail(),
                    strategyBaseDto.getHoliday(), id);
            templateStrategyMapper.insertTemplateStrategyDetail(details);
        }
        //当前时间
        String currentTime = dateTimeService.getCurrentTime();
        bean.setId(id);
        bean.setVersion(DEFAULT_VERSION);
        bean.setGmtCreate(currentTime);
        bean.setUserCreate(userCreate);
        //数据插入
        Integer total = templateStrategyMapper.insertTemplateStrategy(bean);
        log.info("TemplateStrategyServiceImpl -> insert, total: {}", total);
    }

    private void checkId(String id) throws UedmException
    {
        //对入参的SeasonStrategyId进行参数校验
        String energyType = EnergyTypeEnum.GRID.getEnergyType();
        List<StrategyCombinationVo> allBeans = gridStrategyMapper.getStrategyCombination(energyType);
        log.info("checkId: allBeans = {}", jsonService.objectToJson(allBeans));
        if (CollectionUtils.isEmpty(allBeans))
        {
            log.error("TemplateStrategyServiceImpl -> addTemplateStrategy check ids is null");
            throw new UedmException(UedmErrorCodeConstants.OBJECT_NOT_EXIST, ErrorDescConstants.INVALID_PARAMETER);
        }
        List<String> seasonStrategyIds = allBeans.stream().map(StrategyCombinationVo::getSeasonStrategyId)
                .collect(Collectors.toList());
        if (!seasonStrategyIds.contains(id))
        {
            log.error("TemplateStrategyServiceImpl -> addTemplateStrategy check id is error");
            throw new UedmException(UedmErrorCodeConstants.OBJECT_NOT_EXIST, ErrorDescConstants.NOT_AVAILABLE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editTemplateStrategy(TemplateStrategyEditDto templateStrategyEditDto, String user_modified) throws UedmException
    {
        //seasonStrategyId参数校验
        String seasonStrategyId = templateStrategyEditDto.getSeasonStrategyId();
        SeasonStrategyBean seasonStrategyBean = gridStrategyMapper.queryById(seasonStrategyId);
        if (seasonStrategyBean == null)
        {
            log.error("TemplateStrategyServiceImpl -> editTemplateStrategy check id is error");
            throw new UedmException(-200,  ErrorDescConstants.INVALID_PARAMETER);
        }
        Integer count = templateStrategyMapper.countName(templateStrategyEditDto.getName());
        TemplateStrategyDetailBo strategyDetailBo = templateStrategyMapper.selectTemplateById(
                templateStrategyEditDto.getId());
        if (strategyDetailBo == null)
        {
            log.error("TemplateStrategyServiceImpl -> editTemplateStrategy id is not exist");
            throw new UedmException(-202, "id is not exist");
        }
        if (!templateStrategyEditDto.getName().equals(strategyDetailBo.getName()) && count > 0)
        {
            /*名称重复*/
            log.error("TemplateStrategyServiceImpl -> editTemplateStrategy name is repeat");
            throw new UedmException(-UedmErrorCodeConstants.FILE_NAME_TOO_LONG, ErrorDescConstants.NAME_IS_REPEAT);
        }
        //设备模板策略PO
        TemplateStrategyPo bean = templateStrategyEditDto.packageTemplateStrategyPo();
        //当前时间
        String currentTime = dateTimeService.getCurrentTime();
        bean.setGmtModified(currentTime);
        bean.setUserModified(user_modified);

        /*删除之前的策略模板详情*/
        String mode = strategyDetailBo.getMode();
        if (CycleModelEnum.getNotCycleModelId().equals(mode))
        {
            templateStrategyMapper.deleteCsu5DetailStrategyByIds(Collections.singletonList(bean.getId()));
        }
        else
        {
            templateStrategyMapper.deleteDetailStrategyByIds(Collections.singletonList(bean.getId()));
        }

        //新增模板详情
        if (CycleModelEnum.getNotCycleModelId().equals(bean.getMode()))
        {
            checkCsu5Template(templateStrategyEditDto.getDetail(), templateStrategyEditDto.getHoliday());
            bean.setWeekendFlag(templateStrategyEditDto.getWeekendFlag());
            List<TemplateStrategyDetailCsuPo> csuDetails = getTemplateStrategyCSUDetails(
                    templateStrategyEditDto.getDetail(), templateStrategyEditDto.getHoliday(), bean.getId());
            templateStrategyMapper.insertTemplateStrategyCsuDetail(csuDetails);
        }
        else
        {
            bean.setFileId(templateStrategyEditDto.getFileId());
            /*校验模板数量*/
            checkTemplate(templateStrategyEditDto.getMode(), templateStrategyEditDto.getDetail().size(),
                    templateStrategyEditDto.getHoliday().size());
            List<TemplateStrategyDetailBcuaPo> editDetails = getTemplateStrategyBCUADetails(templateStrategyEditDto.getDetail(),
                    templateStrategyEditDto.getHoliday(), bean.getId());
            templateStrategyMapper.insertTemplateStrategyDetail(editDetails);
        }

        /*修改策略模板*/
        templateStrategyMapper.updateTemplateStrategy(bean);

        //更新模板策略的version。自增0.01
        log.info("update version +0.01");
        templateStrategyMapper.updateTemplateStrategyVersion(bean.getId());
    }

    /*校验模板周期模板和节假日模板数量*/
    public void checkTemplate(String mode, int detailNum, int holidayNum) throws UedmException
    {
        if (CycleModelEnum.getDayModelId().equals(mode))
        {
            /*日模式*/
            if (detailNum > 1 || holidayNum > 1)
            {
                /*模板数量过多*/
                log.error("TemplateStrategyServiceImpl -> editTemplateStrategy too many template");
                throw new UedmException(-202, ErrorDescConstants.TOO_MANY_TEMPLATE);
            }
        }
        else if (CycleModelEnum.getWeekModelId().equals(mode) || CycleModelEnum.getMonthModelId().equals(mode))
        {
            /*周和月模式*/
            checkMonthTemplate(detailNum, holidayNum);
        }
        else
        {
            /*mode格式错误*/
            log.error("TemplateStrategyServiceImpl -> editTemplateStrategy mode is error format");
            throw new UedmException(-203, ErrorDescConstants.MODE_ERROR);
        }
    }

    private void checkMonthTemplate(int detailNum, int holidayNum) throws UedmException
    {
        if (detailNum > 7 || holidayNum > 1)
        {
            /*模板数量过多*/
            log.error("TemplateStrategyServiceImpl -> editTemplateStrategy too many template");
            throw new UedmException(-202, ErrorDescConstants.TOO_MANY_TEMPLATE);
        }
    }

    @Override
    public PageInfo<TemplateStrategyBo> searchByConditions(TemplateQueryDto queryBean, String languageOption)
            throws UedmException
    {
        if (StringUtils.isNotBlank(queryBean.getName()) && queryBean.getName().length() > 50)
        {
            log.error("TemplateStrategyServiceImpl -> searchByConditions name is too long");
            throw new UedmException(-200, "name is too long");
        }
        List<TemplateStrategyBo> list = templateStrategyMapper.selectByConditions(queryBean);
        list = templateStrategyIsValid(list, queryBean.getLogicGroupId());          //根据位置过滤策略
        Iterator<TemplateStrategyBo> iterator = list.iterator();
        /*获取季节名称*/
        while (iterator.hasNext())
        {
            TemplateStrategyBo bo = iterator.next();
            if (StringUtils.isNotBlank(bo.getSeasonStrategyId()))
            {
                String seasonStrategyName = gridStrategyService.getSeasonStrategyName(bo.getSeasonStrategyId(),
                        languageOption);
                bo.setSeasonStrategyName(seasonStrategyName);
                if (StringUtils.isNotBlank(queryBean.getSeasonStrategyName()))
                {
                    if (!seasonStrategyName.contains(queryBean.getSeasonStrategyName()))
                    {
                        /*删除不符合条件元素*/
                        iterator.remove();
                    }
                }
            }
        }
        List<TemplateStrategyBo> pageList = PageUtils.getPageList(list, queryBean.getPageNo(), queryBean.getPageSize());
        //把设备类型ID转为国际化之后name传给前端；
        pageList.forEach(x -> x.setDeviceTypeName(i18nUtils.getMapFieldByLanguageOption(PeakDeviceTypeEnum.getShowNameById(x.getDeviceType()),languageOption)));

        PageInfo<TemplateStrategyBo> pageInfo = new PageInfo<>(pageList);
        pageInfo.setTotal(list.size());
        return pageInfo;
    }

    //模板策略属于该逻辑组
    public List<TemplateStrategyBo> templateStrategyIsValid(List<TemplateStrategyBo> list, String logicId) throws UedmException
    {
        log.info("Before templateStrategyIsValid, list size is {}", list.size());
        if (StringUtils.isBlank(logicId) || StringUtils.equalsIgnoreCase(GlobalOptional.GLOBAL_ROOT, logicId))
        {
            //为根节点不用过滤
            log.info("templateStrategyIsValid, logicId is root.");
            return  list;
        }
        //查询logic祖先节点
        List<String> parentIds = configurationManagerRpc.selectAllParentByLogicId(logicId);
        log.info("templateStrategyIsValid, parentIds is {}", parentIds);
        //筛选祖先节点包含的电价策略,并查询季节策略
        List<String> seasonStrategyIds = templateStrategyMapper.selectSeasonStrategyByLogicIds(parentIds);
        log.info("templateStrategyIsValid, seasonStrategyIds size is {}", seasonStrategyIds.size());
        Set<String> seasonStrategyIdSet = new HashSet<>(seasonStrategyIds);
        //根据季节策略筛选结果
        list = list.stream().filter(bean -> StringUtils.isBlank(bean.getSeasonStrategyId()) ||
                seasonStrategyIdSet.contains(bean.getSeasonStrategyId())).collect(Collectors.toList());
        log.info("After templateStrategyIsValid, list size is {}", list.size());
        return list;
    }

    public String getFileName(String fileId, String userName)
    {
        String fileName = "";
        List<String> fileIds = new ArrayList<>();
        fileIds.add(fileId);
        try
        {
            List<UpDownloadFileBean> upDownloadFileBeans = configurationManagerRpc.selectFileById(fileIds, userName);
            if (upDownloadFileBeans != null && !upDownloadFileBeans.isEmpty())
            {
                fileName = upDownloadFileBeans.get(0).getName();
            }
        }
        catch (UedmException e)
        {
            log.error("TemplateStrategyServiceImpl -> getFileName occur exception, {}", e);
        }
        return fileName;
    }

    @Override
    public TemplateStrategyDetailBo selectDetailByTemplateId(String id, String userName, String languageOption)
            throws UedmException
    {
        /*查询设备模板*/
        TemplateStrategyDetailBo templateStrategyDetailBo = templateStrategyMapper.selectTemplateById(id);
        if (templateStrategyDetailBo == null)
        {
            log.error("TemplateStrategyServiceImpl -> selectDetailByTemplateId id is not exist");
            throw new UedmException(-200, "id is not exist");
        }
        if (StringUtils.isNotBlank(templateStrategyDetailBo.getFileId()))
        {
            /*获取fileName*/
            templateStrategyDetailBo.setFileName(getFileName(templateStrategyDetailBo.getFileId(), userName));
        }
        if (StringUtils.isNotBlank(templateStrategyDetailBo.getSeasonStrategyId()))
        {
            /*获取季节策略名称*/
            String seasonStrategyName = gridStrategyService.getSeasonStrategyName(
                    templateStrategyDetailBo.getSeasonStrategyId(), languageOption);
            templateStrategyDetailBo.setSeasonStrategyName(seasonStrategyName);
        }
        String deviceType = templateStrategyDetailBo.getDeviceType();
        if(PeakDeviceTypeEnum.DIRECT.id.equals(deviceType)){
            templateStrategyDetailBo = getBcuaDetail(templateStrategyDetailBo);
        }
        if(PeakShiftConstants.CSU5.equals(deviceType)){
            templateStrategyDetailBo = getCsu5Detail(templateStrategyDetailBo);
        }
        return templateStrategyDetailBo;
    }
    public TemplateStrategyDetailBo getBcuaDetail(TemplateStrategyDetailBo templateStrategyDetailBo) throws UedmException {
        //根据ID查询Bcua设备模板详情列表
        List<TemplateStrategyDetailBcuaPo> details = templateStrategyMapper.selectDetailByTemplateId(templateStrategyDetailBo.getId());
        List<TemplateDetailBaseDto> detailList = new ArrayList<>();
        List<TemplateHolidayDto> holidayList = new ArrayList<>();
        for (TemplateStrategyDetailBcuaPo po : details)
        {
            if (po.getHolidayFlag())
            {
                /*节假日模式*/
                TemplateHolidayDto holidayVo = new TemplateHolidayDto();
                List<TemplateTimeGranVo> timeGran = jsonService.jsonToObject(po.getHolidayTimeGran(), List.class, TemplateTimeGranVo.class);
                List<TemplateTimeGranDetailVo> detail = jsonService.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class);
                holidayVo.setTimeGran(timeGran);
                holidayVo.setDetail(detail);
                holidayList.add(holidayVo);
            }
            else if (StringUtils.isNotBlank(po.getTimeGran()))
            {
                /*周期详情且不是空模板*/
                TemplateDetailBaseDto detailVo = new TemplateDetailBaseDto();
                List<Integer> timeGran = jsonService.jsonToObject(po.getTimeGran(), List.class, Integer.class);
                List<TemplateTimeGranDetailVo> detail = jsonService.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class );
                detailVo.setTimeGran(timeGran);
                detailVo.setDetail(detail);
                detailList.add(detailVo);
            }
        }
        templateStrategyDetailBo.setDetail(detailList);
        templateStrategyDetailBo.setHoliday(holidayList);
        return templateStrategyDetailBo;
    }
    public TemplateStrategyDetailBo getCsu5Detail(TemplateStrategyDetailBo templateStrategyDetailBo) throws UedmException {
        //根据ID查询Csu5设备模板详情列表
        List<TemplateStrategyDetailCsu5Po> details = templateStrategyMapper.selectCsu5DetailByTemplateId(templateStrategyDetailBo.getId());
        List<TemplateDetailBaseDto> detailList = new ArrayList<>();
        List<TemplateHolidayDto> holidayList = new ArrayList<>();
        for (TemplateStrategyDetailCsu5Po po : details)
        {
            TemplateTimeGranVo timeRange = new  TemplateTimeGranVo();
            timeRange.setBegin(po.getBeginDate());
            timeRange.setEnd(po.getEndDate());
            timeRange.setRemark(null);
            if((StringUtils.isNotBlank(po.getRemark())))
            {
                timeRange.setRemark(po.getRemark());
            }
            List<TemplateTimeGranDetailVo> detail = jsonService.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class);
            if (po.getHolidayFlag())
            {
                /*节假日模式*/
                TemplateHolidayDto holidayVo = new TemplateHolidayDto();
                holidayVo.setTimeRange(timeRange);
                holidayVo.setDetail(detail);
                holidayList.add(holidayVo);
            }
            else
            {
                /*周期详情且不是空模板*/
                TemplateDetailBaseDto detailVo = new TemplateDetailBaseDto();
                detailVo.setTimeRange(timeRange);
                detailVo.setDetail(detail);
                detailList.add(detailVo);
            }
        }
        templateStrategyDetailBo.setDetail(detailList);
        templateStrategyDetailBo.setHoliday(holidayList);
        return templateStrategyDetailBo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteTemplateStrategy(List<String> ids, OperlogBean operlogBean, IsKafkaBean isKafkaBean)
            throws UedmException
    {
        Integer count = null;

        try
        {
            if (ids == null || ids.isEmpty())
            {
                log.error("deleteTemplateStrategy ids is empty");
                throw new UedmException(-200, "ids is empty");
            }
            /*获取要删除的模板策略名称记录日志*/
            List<String> names = templateStrategyMapper.selectNameByIds(ids);

            /*删除设备模板策略*/
            count = templateStrategyMapper.deleteByIds(ids);

            //BCUA
            /*删除设备模板策略详情*/
            templateStrategyMapper.deleteDetailStrategyByIds(ids);
            /*获取已上传文件fileIds*/
            List<String> fileIds = templateStrategyMapper.selectFileIdByIds(ids);
            /*删除已上传文件*/
            configurationManagerRpc.deleteFileInfo(fileIds);

            //CSU5
            templateStrategyMapper.deleteCsu5DetailStrategyByIds(ids);

            //成功日志发送
            if (isKafkaBean.isOperlog())
            {
                GlobalizationInfo detail = new GlobalizationInfo();
                detail.setZh_CN("模板名称:" + jsonService.objectToJson(names));
                detail.setEn_US("Template Name:" + jsonService.objectToJson(names));
                logMsgSender(OPERATION_DELETE, jsonService.objectToJson(detail), OperLogContants.OperateResultSuccess, operlogBean);
            }
        }
        catch (Exception e)
        {
            //失败日志发送
            if (isKafkaBean.isOperlog())
            {
                GlobalizationInfo detail = new GlobalizationInfo();
                detail.setZh_CN("模板删除失败！");
                detail.setEn_US("Template Delete failed！");
                logMsgSender(OPERATION_DELETE, jsonService.objectToJson(detail), OperLogContants.OperateResultSuccess, operlogBean);
            }
            log.error("delete template failed!",e);
            throw new UedmException(-1, "delete failed!");
        }
        return count;
    }

    @Override
    public IssuedHistoryBo selectHistory(String task_id) throws UedmException
    {
        try
        {
            IssuedHistoryPo po = templateStrategyMapper.selectIssueHistoryDetailByTaskId(task_id);
            IssuedHistoryBo bo = new IssuedHistoryBo();
            TemplateStrategyDetailBo detailBo = null;
            if (po != null)
            {
                detailBo = jsonService.jsonToObject(po.getAllDetail(), TemplateStrategyDetailBo.class);
                bo.setVersion(detailBo.getVersion());
                bo.setTemplateDetail(detailBo);
            }
            return bo;
        }
        catch (Exception e)
        {
            log.error("TemplateStrategyServiceImpl -> selectHistory occur exception, {}", e);
            throw new UedmException(-200, "selectHistory occur exception");
        }
    }

    public List<TemplateStrategyDetailBcuaPo> getTemplateStrategyBCUADetails(List<TemplateDetailBaseDto> detail,
            List<TemplateHolidayDto> holiday, String template_strategy_id) throws UedmException
    {
        //当前时间
        String currentTime = dateTimeService.getCurrentTime();
        List<TemplateStrategyDetailBcuaPo> details = new ArrayList<>();
        if (!detail.isEmpty())
        {
            for (int i = 0; i < detail.size(); i++)
            {
                String id = UUID.randomUUID().toString();
                TemplateStrategyDetailBcuaPo bean = new TemplateStrategyDetailBcuaPo();
                TemplateDetailBaseDto detailBaseDto = detail.get(i);
                bean.setTimeGran(jsonService.objectToJson(detailBaseDto.getTimeGran()));

                bean.setId(id);
                bean.setTemplateStrategyId(template_strategy_id);
                bean.setTemplateIndex(i + 1);
                bean.setHolidayFlag(false);

                bean.setDetail(jsonService.objectToJson(detail.get(i).getDetail()));
                bean.setGmtCreate(currentTime);
                details.add(bean);
            }
        }
        int size = details.size();
        log.info("getTemplateStrategyDetails detail size ==> {}", size);
        /*节假日模板index在平时之后*/
        if (!holiday.isEmpty())
        {
            log.info("getTemplateStrategyDetails holiday size ==> {}", holiday.size());
            for (int i = size; i < holiday.size() + size; i++)
            {

                String id = UUID.randomUUID().toString();
                TemplateStrategyDetailBcuaPo bean = new TemplateStrategyDetailBcuaPo();
                TemplateHolidayDto detailBaseDto = holiday.get(i - size);
                bean.setHolidayTimeGran(jsonService.objectToJson(detailBaseDto.getTimeGran()));
                bean.setId(id);
                bean.setTemplateStrategyId(template_strategy_id);
                bean.setTemplateIndex(i + 1);
                bean.setHolidayFlag(true);

                bean.setDetail(jsonService.objectToJson(detailBaseDto.getDetail()));
                bean.setGmtCreate(currentTime);
                details.add(bean);
            }
        }
        return details;
    }

    public List<TemplateStrategyDetailCsuPo> getTemplateStrategyCSUDetails(List<TemplateDetailBaseDto> detail, List<TemplateHolidayDto> holiday,
            String template_strategy_id) throws UedmException
    {
        //当前时间
        String currentTime = dateTimeService.getCurrentTime();
        List<TemplateStrategyDetailCsuPo> details = new ArrayList<>();
        if (!detail.isEmpty())
        {
            for (int i = 0; i < detail.size(); i++)
            {
                String id = UUID.randomUUID().toString();
                TemplateStrategyDetailCsuPo bean = new TemplateStrategyDetailCsuPo();
                TemplateDetailBaseDto detailBaseDto = detail.get(i);
                TemplateTimeGranVo timeRange = detailBaseDto.getTimeRange();
                if (timeRange != null)
                {
                    bean.setRemark(timeRange.getRemark());
                    bean.setBeginDate(timeRange.getBegin());
                    bean.setEndDate(timeRange.getEnd());
                }
                bean.setId(id);
                bean.setTemplateStrategyId(template_strategy_id);
                bean.setTemplateIndex(i + 1);
                bean.setHolidayFlag(false);
                bean.setDetail(jsonService.objectToJson(detail.get(i).getDetail()));
                bean.setGmtCreate(currentTime);
                details.add(bean);
            }
        }
        int size = details.size();
        log.info("getTemplateStrategyDetails detail size ==> {}", size);
        /*节假日模板index在平时之后*/
        if (!holiday.isEmpty())
        {
            log.info("getTemplateStrategyDetails holiday size ==> {}", holiday.size());
            for (int i = size; i < holiday.size() + size; i++)
            {

                String id = UUID.randomUUID().toString();
                TemplateStrategyDetailCsuPo bean = new TemplateStrategyDetailCsuPo();
                TemplateHolidayDto holidayDto = holiday.get(i - size);

                TemplateTimeGranVo timeRange = holidayDto.getTimeRange();
                if (timeRange != null)
                {
                    bean.setRemark(timeRange.getRemark());
                    bean.setBeginDate(timeRange.getBegin());
                    bean.setEndDate(timeRange.getEnd());
                }
                bean.setId(id);
                bean.setTemplateStrategyId(template_strategy_id);
                bean.setTemplateIndex(i + 1);
                bean.setHolidayFlag(true);
                bean.setDetail(jsonService.objectToJson(holidayDto.getDetail()));
                bean.setGmtCreate(currentTime);
                details.add(bean);
            }
        }
        return details;
    }


    /**
     * 将操作日志推送到日志管理
     *
     * @param operation     insert,update,delete
     * @param detail        策略信息
     * @param operateResult
     * @param operlogBean
     * @throws UedmException
     */
    public void logMsgSender(String operation, String detail, String operateResult, OperlogBean operlogBean)
            throws UedmException
    {
        try
        {
            //操作结果
            operlogBean.setOperateResult(operateResult);

            //模块名称
            GlobalizationInfo globalizationInfo = new GlobalizationInfo();
            globalizationInfo.setEn_US("Template Strategy");
            globalizationInfo.setZh_CN("设备模板策略");
            operlogBean.setAppModule(jsonService.objectToJson(globalizationInfo));
            //公共配置
            String connectMode = UserThreadLocal.getLoginType();
            UedmLogUtils.newBaseOperLogBean(operlogBean, connectMode);

            //重要等级
            operlogBean.setRank(OperlogBean.LogRank.operlog_rank_important.toString());
            GlobalizationInfo globalInfoOperation = new GlobalizationInfo();
            GlobalizationInfo globalInfoDescription = new GlobalizationInfo();
            //操作
            if (OPERATION_DELETE.equals(operation))
            {
                globalInfoOperation.setEn_US("template strategy-delete");
                globalInfoOperation.setZh_CN("设备模板策略-删除");
                globalInfoDescription.setEn_US("template strategy-delete");
                globalInfoDescription.setZh_CN("设备模板策略-删除");
            }
            //详情
            operlogBean.setDetail(detail);
            operlogBean.setOperation(jsonService.objectToJson(globalInfoOperation));
            operlogBean.setDescriptionInfo(jsonService.objectToJson(globalInfoDescription));

            String message = jsonService.objectToJson(operlogBean);
            log.info("message {}", message);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, message);
        }
        catch (UedmException e)
        {
            throw new UedmException(-1, "log Message send failed!");
        }
    }

    public Boolean checkNameUnique(String id, String name) throws UedmException
    {
        try
        {
            if (name == null)
            {
                throw new UedmException(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, "name is null");
            }

            if (name.length() > NAME_LENGTH)
            {
                throw new UedmException(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, "name to long.");
            }

            List<TemplateStrategyDetailBo> allBos = templateStrategyMapper.selectTemplateByIdForCheckName(name, id);

            if (allBos.size() == 0)
            {
                //没有查出来除本身之外重名的Bean，因此名字未重名
                return true;
            }

            return false;
        }
        catch (UedmException e)
        {
            log.error("checkNameUnique failed!", e);
            throw new UedmException(e.getErrorId(), e.getMessage());
        }

    }


    public void checkCsu5Template(List<TemplateDetailBaseDto> detail,List<TemplateHolidayDto> holiday) throws UedmException
    {
        List<TemplateTimeGranDetailVo> detailVos = new ArrayList<>();
        List<TemplateTimeGranVo> timeGranVoList = new ArrayList<>();
        for (TemplateDetailBaseDto baseDto : detail)
        {

            List<TemplateTimeGranDetailVo> timeGranDetailVos = baseDto.getDetail();
            detailVos.addAll(timeGranDetailVos);
            getTimeRangeList(baseDto.getTimeRange(),timeGranVoList);
        }
        checkTimeInterval(timeGranVoList);
        Map<Integer, List<TemplateTimeGranDetailVo>> strategyTypeMap = detailVos.stream()
                .collect(Collectors.groupingBy(TemplateTimeGranDetailVo::getStrategyType));
        for (Map.Entry<Integer, List<TemplateTimeGranDetailVo>> entry : strategyTypeMap.entrySet())
        {
            List<TemplateTimeGranDetailVo> value = entry.getValue();
            //各策略类型不能超过4组
            if (value.size() > 4 )
            {
                log.error("Each strategy period cannot exceed 4 groups:{}",value.size());
                throw new UedmException(-205, ErrorDescConstants.POLICY_PERIODS_EXCEEDS_RANGE);
            }
        }

        if (CollectionUtils.isNotEmpty(holiday))
        {
            if (holiday.size() > 20)
            {
                log.error("The number of holiday periods should not exceed 20 groups :{}",holiday.size());
                throw new UedmException(-206, ErrorDescConstants.HOLIDAY_PERIODS_EXCEEDS_RANGE);
            }
            List<TemplateTimeGranVo> timeGranVos = new ArrayList<>();
            for (TemplateHolidayDto dto : holiday)
            {

                getTimeRangeList(dto.getTimeRange(),timeGranVos);
            }
            checkTimeInterval(timeGranVos);
        }
    }

    /**
     * 校验时间范围重复
     * @param timeGranVos
     * @throws UedmException
     */
    private void checkTimeInterval(List<TemplateTimeGranVo> timeGranVos) throws UedmException
    {
        if (timeGranVos.size() > 1)
        {
            Map<String, String> map = new HashMap<>();
            List<String> keyList = new ArrayList<>();
            for (TemplateTimeGranVo bean : timeGranVos)
            {
                map.put(bean.getBegin(), bean.getEnd());
                keyList.add(bean.getBegin());
            }
            Collections.sort(keyList);
            for (int i = 0; i < keyList.size(); i++)
            {
                if (i > 0)
                {
                    String endTime = map.get(keyList.get(i - 1));
                    if (StringUtils.isNotBlank(endTime) && keyList.get(i).compareTo(endTime) <= 0)
                    {
                        log.error("Time ranges overlap: {}", JSON.toJSONString(timeGranVos));
                        throw new UedmException(-208,ErrorDescConstants.TIME_RANGRS_OVERLAP);
                    }
                }
            }
        }
    }

    public void getTimeRangeList(TemplateTimeGranVo timeRange,List<TemplateTimeGranVo> timeGranVos) throws UedmException
    {
        if (timeRange == null || StringUtils.isAnyBlank(timeRange.getBegin(),timeRange.getEnd()))
        {
            log.error("Time ranges is empty :{}", timeRange);
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,
                    ErrorDescConstants.TIME_RANGRS_EMPTY);
        }
        timeGranVos.add(timeRange);
    }
}
