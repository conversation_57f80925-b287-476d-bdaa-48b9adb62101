package com.zte.uedm.battery.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class LogDetailEnBean
{
    @JsonProperty("Grid")
    private String name;

    @JsonProperty("EffectiveTime")
    private String time;

    @JsonProperty("MODE")
    private String mode;

    @JsonProperty("Cycle")
    private List<LogEnSeasonBean> cycle;

    @JsonProperty("Holiday")
    private List<LogEnHolidayBean> holiyday;
}
