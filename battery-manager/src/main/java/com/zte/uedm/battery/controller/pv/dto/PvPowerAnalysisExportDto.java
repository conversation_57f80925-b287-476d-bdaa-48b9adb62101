package com.zte.uedm.battery.controller.pv.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(description = "太阳能分析导出")
public class PvPowerAnalysisExportDto
{
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "粒度")
    private String grain;

    @ApiModelProperty(value = "位置")
    private List<String> positions;
}
