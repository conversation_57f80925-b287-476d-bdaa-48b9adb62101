package com.zte.uedm.battery.bean.overview;

import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.enums.batt.BattHealthStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
@ToString
public class OverviewHealthInfoBean {
    /**
     * 健康状态ID
     */
    private String statusId;

    /**
     * 健康状态名称
     */
    private String name;

    /**
     * SOH
     */
    private Double soh;

    /**
     * 评估原因
     */
    private String evalReason;

    /**
     * 评估来源 AI/设备
     */
    private String source;

    /**
     * 评估时间
     */
    private String evalTime;

    public static OverviewHealthInfoBean buildOverviewBean(BattHealthStatusEvalPo battHealthStatusEvalPo){
        OverviewHealthInfoBean overviewHealthInfoBean = new OverviewHealthInfoBean();
        BeanUtils.copyProperties(battHealthStatusEvalPo, overviewHealthInfoBean);
        overviewHealthInfoBean.setName(BattHealthStatusEnum.getNameById(overviewHealthInfoBean.getStatusId()));
        overviewHealthInfoBean.setEvalTime(DateUtils.getCurrentDay(battHealthStatusEvalPo.getEvaluateTime()));
        return overviewHealthInfoBean;
    }
}
