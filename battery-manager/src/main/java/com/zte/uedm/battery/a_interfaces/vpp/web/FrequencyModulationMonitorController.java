package com.zte.uedm.battery.a_interfaces.vpp.web;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.a_application.vpp.executor.FMOverviewService;
import com.zte.uedm.battery.a_application.vpp.executor.FrequencyModulationMonitorService;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import com.zte.uedm.battery.a_interfaces.vpp.web.dto.FrequencyModulationDetailDto;
import com.zte.uedm.battery.a_interfaces.vpp.web.dto.FrequencyModulationMonitorBaseDto;
import com.zte.uedm.battery.a_interfaces.vpp.web.dto.FrequencyModulationQueryDto;
import com.zte.uedm.battery.a_interfaces.vpp.web.vo.*;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.util.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/* Started by AICoder, pid:z6f129ed28vb6fe14ff50ae5621d179b8540f13c */
@Path("frequency-modulation-monitor")
@Component
@Api(value = "调频监控")
@Slf4j
public class FrequencyModulationMonitorController {

    @Autowired
    private FrequencyModulationMonitorService frequencyModulationMonitorService;

    @Autowired
    private FMOverviewService fmOverviewService;
    @Autowired
    private JsonService jsonService;

    private static final String PARAMS_IS_BLANK = "{\"zh_CN\":\"参数为空\",\"en_US\":\"params is blank\"}";
    private static final String THE_ORDER_VALUE_IS_NOT_UNIQUE = "{\"zh_CN\":\"排序值不唯一\",\"en_US\":\"The order value is not unique\"}";
    private static final String PARAMS_NOT_IN_RANGE = "{\"zh_CN\":\"参数不在可选范围:%s\",\"en_US\":\"The parameter is not within the optional range:%s\"}";
    private static final String DOES_NOT_MEET_THE_DEFAULT_IMMUTABLE_SETTING = "{\"zh_CN\":\"不符合默认的不可变设置:%s\",\"en_US\":\"Does not meet the default immutable setting:%s\"}";

    @POST
    @Path("/status-statistic")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频监控-状态统计", notes = "调频监控-状态统计", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取数据正常"),
            @ApiResponse(code = -301, message = "入参为空,获取数据异常")
    })
    public ResponseBean queryStatusStatistic(FrequencyModulationMonitorBaseDto dto) throws UedmException {
        try {
            if (dto == null || StringUtils.isBlank(dto.getLogicGroupId())) {
                log.warn("PeakShiftMonitorController -> queryStatusStatistic param is blank {}", dto);
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "queryStatusStatistic param is blank.");
            }
            FreqMonitorStatusStatisticVo result = frequencyModulationMonitorService.queryStatusStatistic(dto);
            return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        } catch (Exception e) {
            log.error("FrequencyModulationMonitorController frequencyModulationDetailRecord e", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @POST
    @Path("/detail-record")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频监控详情调频记录", notes = "调频监控详情调频记录", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取数据正常"),
            @ApiResponse(code = -301, message = "入参为空,获取数据异常")
    })
    public ResponseBean frequencyModulationDetailRecord(FrequencyModulationDetailDto detailDto, @QueryParam("pageNo") Integer pageNo, @QueryParam("pageSize") Integer pageSize) {
        try {
            if (detailDto == null || detailDto.getDeviceId() == null) {
                log.info("carbonReductionCoefficientSetting -> detailBean is blank");
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "detailBean is blank");
            }
            List<FMDetailRecordVO> fmDetailRecordVOList = frequencyModulationMonitorService.getDeviceFmRecord(detailDto);
            List<FMDetailRecordVO> pageList = PageUtils.getPageList(fmDetailRecordVOList, pageNo, pageSize);
            PageInfo<FMDetailRecordVO> pageInfo = new PageInfo<>(pageList);
            pageInfo.setTotal(fmDetailRecordVOList.size());
            return ResponseBeanUtils.getNormalResponseBean(0, pageInfo.getList(), (int) pageInfo.getTotal());
        } catch (Exception e) {
            log.error("FrequencyModulationMonitorController frequencyModulationDetailRecord e", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @POST
    @Path("/detail-overview")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频监控详情总览", notes = "调频监控详情总览", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取数据正常"),
            @ApiResponse(code = -301, message = "入参为空,获取数据异常")
    })
    public ResponseBean frequencyModulationDetailOverview(@QueryParam("deviceId") String deviceId, @HeaderParam("language-option") String languageOption) {
        try {
            if (StringUtils.isBlank(deviceId)) {
                log.info("deviceId is null");
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "deviceId is null");
            }
            FMDetailOverviewVO fmDetailOverviewVO = frequencyModulationMonitorService.getDeviceFmOverview(deviceId, languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, fmDetailOverviewVO, 1);
        } catch (Exception e) {
            log.error("FrequencyModulationMonitorController frequencyModulationDetailOverview e", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @POST
    @Path("/detail-diagram")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频监控详情调频曲线图", notes = "调频监控详情调频曲线图", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取数据正常"),
            @ApiResponse(code = -301, message = "入参为空,获取数据异常")
    })
    public ResponseBean frequencyModulationDetailDiagram(FrequencyModulationDetailDto detailDto) {
        try {
            if (detailDto == null || detailDto.getDeviceId() == null) {
                log.info("carbonReductionCoefficientSetting -> detailBean is blank");
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "detailBean is blank");
            }
            FMDetailDiagramVO fmDetailDiagramVO = frequencyModulationMonitorService.getDetailDiagram(detailDto);
            return ResponseBeanUtils.getNormalResponseBean(0, fmDetailDiagramVO, 1);
        } catch (Exception e) {
            log.error("FrequencyModulationMonitorController frequencyModulationDetailDiagram e", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @POST
    @Path("/query")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频监控界面查询", notes = "调频监控界面查询", httpMethod = HttpMethod.POST)
    public ResponseBean frequencyModulationQuery(FrequencyModulationQueryDto dto, @QueryParam("pageNo") Integer pageNo, @QueryParam("pageSize") Integer pageSize,
                                                 @HeaderParam("language-option") String languageOption, @Context HttpServletRequest request) throws Exception {
        if (dto == null || StringUtils.isBlank(dto.getLogicGroupId())) {
            log.warn("FrequencyModulationMonitorController -> frequencyModulationQuery param is blank {}", dto);
            return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "deviceId is blank.");
        }
        Pair<Boolean, List<String>> booleanListPair = dto.checkDimAvailable();
        if (!booleanListPair.getLeft()) {
            log.warn("FrequencyModulationMonitorController -> frequencyModulationQuery Dimension list verification failed {}", booleanListPair.getRight());
            return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_EXCEED_ALLOWD_RANGE, "Dimension list verification failed:" + booleanListPair.getRight());
        }
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            // 获取用户名
            String userName = Tools.getUserName(request);
            // 获取用户IP
            String host = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption, pageNo, pageSize);
            List<FrequencyModulationQueryVO> result = frequencyModulationMonitorService.frequencyModulationQuery(dto, serviceBaseInfoBean);
            pageInfo.setTotal(result.size());
            // 进行分页操作
            List<FrequencyModulationQueryVO> pageList = PageUtils.getPageList(result, pageNo, pageSize);
            List<Map<String, Object>> resultMapList = new ArrayList<>();
            List<FMOverviewPo> peakShiftMonitorDims = dto.getFmMonitorDims();
            peakShiftMonitorDims = peakShiftMonitorDims.stream().filter(FMOverviewPo::getEnable)
                    .sorted(Comparator.comparing(FMOverviewPo::getSequence)).collect(Collectors.toList());
            Map<String, String> dimBeanToMap = new HashMap<>();
            for (FMOverviewPo bean : peakShiftMonitorDims) {
                IdNameBean idNameBean = new IdNameBean(bean.getId(), bean.getName());
                String idNameJson = jsonService.objectToJson(idNameBean);
                dimBeanToMap.put(bean.getId(), idNameJson);
            }
            pageList.forEach(item -> {
                Map<String, Object> beanToMap = new HashMap<>();
                beanToMap.put("deviceId", item.getDeviceId());
                Map<String, Object> objectMap = JSON.parseObject(JSON.toJSONString(item), new TypeReference<Map<String, Object>>() {});
                dimBeanToMap.forEach((id, idNameJson) -> {
                    if (objectMap != null && !CollectionUtils.isEmpty(objectMap.keySet())) {
                        Object value = objectMap.get(id);
                        beanToMap.put(id, value);
                    }
                });
                resultMapList.add(beanToMap);
            });
            pageInfo.setList(resultMapList);
            return ResponseBeanUtils.getNormalResponseBean(0, pageInfo.getList(), (int) pageInfo.getTotal());
        } catch (Exception e) {
            log.error("FrequencyModulationMonitorController query occur exception", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @GET
    @Path("/overview/select")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频信息-自定义列表查询", notes = "调频信息-自定义列表查询", response = ResponseBean.class, httpMethod = "GET", tags = {"界面查询/调频列表维度查询"})
    public ResponseBean selectFmOverview(@Context HttpServletRequest request, @HeaderParam("language-option") String languageOption) {
        String userName = Tools.getUserName(request);
        try {
            List<FmOverviewBeanVO> peakDimList = fmOverviewService.selectFMOverview(userName, languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, peakDimList, peakDimList.size());
        } catch (UedmException e) {
            log.error("FrequencyModulationMonitorController selectFmOverview is error :{}", e.getMessage());
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @POST
    @Path("/overview/update")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "调频概览信息更新", notes = "调频概览信息更新", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -200, message = "操作数据库发生异常"),
            @ApiResponse(code = -301, message = "userName为空"),
            @ApiResponse(code = -302, message = "入参的sequence列表不一致"),
            @ApiResponse(code = -305, message = "id or sequence不在范围内"),
            @ApiResponse(code = -208, message = "不可变动的设定有错误")
    })
    public ResponseBean updateFmOverview(List<FMOverviewPo> updateBeanList, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption) throws UedmException {
        log.info("===== update updateFmOverview updateBeanList: {}", JSON.toJSONString(updateBeanList));
        Integer total = 0;
        List<String> errorList = new ArrayList<>();
        String userName = Tools.getUserName(request);

        try {
            ResponseBean response = checkArgs(updateBeanList, userName);
            if (response != null) {
                log.error("updateFmOverview checkArgs Parameter is empty or in duplicate order!");
                return response;
            }
            //获取用户概览维度列表  如果有从数据库中查，若没有从枚举类中获得默认情况
            List<FmOverviewBeanVO> peakDimList = fmOverviewService.selectFMOverview(userName, languageOption);
            log.debug("database peakDimList:{}", JSON.toJSONString(peakDimList, SerializerFeature.WriteMapNullValue));
            //根据概览维度列表，获取 id集合
            //根据概览维度列表，获取用户序列集合
            List<String> queryIdList = peakDimList.parallelStream().map(FmOverviewBeanVO::getId).collect(Collectors.toList());
            List<Integer> querySeqList = peakDimList.parallelStream().map(FmOverviewBeanVO::getSequence).collect(Collectors.toList());

            response = checkIdAndSeq(updateBeanList, queryIdList, querySeqList, languageOption);
            if (response != null) {
                log.error("updateFmOverviewBean id or Sequence Not in optional range");
                return response;
            }

            //遍历 概览维度列表 校验不可变动的设定 <4>
            if (checkEnable(updateBeanList, errorList, peakDimList)) {
                log.error("updatePeakShiftConfig Default immutable setting verification failed!");
                String i18nStr = String.format(DOES_NOT_MEET_THE_DEFAULT_IMMUTABLE_SETTING, errorList, errorList);
                log.info("checkIdAndSeq checkEnable:{}", i18nStr);
                return ResponseBeanUtils.getNormalResponseBean(-208, "Does not meet the default immutable setting", i18nStr, null, null);
            }

            total = fmOverviewService.updateFMOverview(updateBeanList, userName);

            return ResponseBeanUtils.getNormalResponseBean(0, null, total);
        } catch (UedmException e) {
            log.error("PeakShiftMonitorController -> updatePeakShiftConfig is error!", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    public ResponseBean checkArgs(List<FMOverviewPo> updateBeanList, String userName) {
        // 非空判断 <1>
        if (StringUtils.isEmpty(userName) || CollectionUtils.isEmpty(updateBeanList)) {
            log.error("update checkArgs -> parameter is blank:{} ,updateBeanList:{}", userName, updateBeanList);
            return ResponseBeanUtils.getNormalResponseBean(-301, "params is blank", PARAMS_IS_BLANK, null, null);
        }
        // 获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
        if (checkReqSeq(updateBeanList)) {
            return ResponseBeanUtils.getNormalResponseBean(-302, "The order value is not unique", THE_ORDER_VALUE_IS_NOT_UNIQUE, null, null);
        }
        return null;
    }

    private static boolean checkReqSeq(List<FMOverviewPo> updateBeanList) {
        boolean hasDuplicate = updateBeanList.stream()
                .map(FMOverviewPo::getSequence)
                .distinct()
                .count() != updateBeanList.size();
        if (hasDuplicate) {
            log.error("update checkReqSeq -> sequence is not unique");
        }
        return hasDuplicate;
    }

    @Nullable
    public ResponseBean checkIdAndSeq(List<FMOverviewPo> updateBeanList, List<String> queryIdList, List<Integer> querySeqList, String languageOption) {
        List<String> updateIds = updateBeanList.stream().map(FMOverviewPo::getId).collect(Collectors.toList());
        List<Integer> updateSeqs = updateBeanList.stream().map(FMOverviewPo::getSequence).collect(Collectors.toList());
        updateIds.removeAll(queryIdList);
        if (!updateIds.isEmpty()) {
            String format = String.format(PARAMS_NOT_IN_RANGE, updateIds, updateIds);
            return ResponseBeanUtils.getNormalResponseBean(-305, "The parameter is not within the optional range", format, null, null);
        }
        updateSeqs.removeAll(querySeqList);
        if (!updateSeqs.isEmpty()) {
            String format = String.format(PARAMS_NOT_IN_RANGE, updateSeqs, updateSeqs);
            log.info("checkIdAndSeq updateSeqs:{}", format);
            return ResponseBeanUtils.getNormalResponseBean(-305, "The parameter is not within the optional range", format, null, null);
        }
        return null;
    }

    public boolean checkEnable(List<FMOverviewPo> updateBeanList, List<String> errorList, List<FmOverviewBeanVO> peakDimList) {
        Map<String, FMOverviewPo> collect = updateBeanList.stream().collect(Collectors.toMap(FMOverviewPo::getId, Function.identity(), (key1, key2) -> key1));
        for (FmOverviewBeanVO queryBean : peakDimList) {
            String id = queryBean.getId();
            Boolean enable = queryBean.getEnable();
            if (queryBean.getDefaultFixed()) {
                FMOverviewPo updateBean = collect.get(id);
                if (updateBean != null && !updateBean.getEnable().equals(enable)) {
                    log.error("FrequencyModulationMonitorController checkEnable -> DefaultFixed is true");
                    errorList.add(updateBean.getName());
                    return true;
                }
            }
        }
        return false;
    }
}
/* Ended by AICoder, pid:z6f129ed28vb6fe14ff50ae5621d179b8540f13c */

