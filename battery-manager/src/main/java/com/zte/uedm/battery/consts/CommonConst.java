package com.zte.uedm.battery.consts;
import com.zte.uedm.service.config.optional.GlobalOptional;

import java.util.HashMap;
import java.util.Map;

public class CommonConst {

    public static final String YES = "Yes";

    public static final String START_TIME = "startTime";

    public static final String DURATION_START = "backupPowerDurationStart";

    public static final String DURATION_END = "backupPowerDurationEnd";

    public static final String DURATION_START_END = "backupPowerDurationStartAndEnd";

    public static final String DOT = ".";

    public static final Integer TWO = 2;

    public static final String DEVICE_TYPE = "backupPowerDurationStartAndEnd";

    public static final String DIMS = "dims";

    public static final String STATUS = "status";

    public static final String APPLICATION_SCENE = "applicationScene";

    public static final String MSG_BLANK = "param is blank.";

    public static final String  SEQUENCE  = "sequence is not unique";

    public static final String  EMPTY  = "updateDtoList is empty";
    /**
     * 采集器协议属性中的协议类型
     */
    public static final String PROTOCOL_TYPES = "protocol_types";

    public static final String  BCUA = "BCUA";

    public static final String  SCUC = "SCUC";

    public static final String  SNMP = "SNMP";

    public static final String  CSU = "CSU";

    public static final String  CSU5 = "CSU5";

    public static final String  CSU6 = "CSU6";
    public static final String  SF = "SF";


    public static final String PRICE_STRATEGY_ADD = "price_strategy_add";
    public static final String PRICE_STRATEGY_DETAIL_ADD = "price_strategy_detail_add";
    public static final String PRICE_STRATEGY_UPDATE = "price_strategy_update";
    public static final String PRICE_STRATEGY_DETAIL_UPDATE = "price_strategy_detail_update";
    public static final String PRICE_STRATEGY_DELETE = "price_strategy_delete";
    public static final String PRICE_STRATEGY_DETAIL_DELETE = "price_strategy_detail_delete";
    public static final String INTERVAL_STRATEGY_ADD = "interval_strategy_add";
    public static final String INTERVAL_STRATEGY_DETAIL_ADD = "interval_strategy_detail_add";
    public static final String INTERVAL_STRATEGY_DETAIL_DELETE = "interval_strategy_detail_delete";
    public static final String SEASON_STRATEGY_ADD = "season_strategy_add";
    public static final String SEASON_STRATEGY_DELETE = "season_strategy_delete";

    public static final String PRICE_STRATEGY_STATUS_INVALID = "price_strategy_status_invalid";
    public static final String PRICE_STRATEGY_STATUS_EFFECTIVE = "price_strategy_status_effective";
    public static final String INTERVAL_STRATEGY_STATUS_TWO = "interval_strategy_status_two";
    public static final String INTERVAL_STRATEGY_STATUS_ONE = "interval_strategy_status_one";

    /**
     * 同步旧数据到工程配置rpc
     */
    public static final String PRICE_STRATEGY_SYN = "price_strategy_syn";
    public static final String PRICE_STRATEGY_DETAIL_SYN = "price_strategy_detail_syn";
    public static final String SCOPE_STRATEGY_SYN = "scope_strategy_syn";
    public static final String SEASON_STRATEGY_SYN = "season_strategy_syn";
    public static final String INTERVAL_STRATEGY_SYN = "interval_strategy_syn";
    public static final String INTERVAL_STRATEGY_DETAIL_SYN = "interval_strategy_detail_syn";

    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";

    public static final String SCOPE_STRATEGY_ADD = "scope_strategy_add";

    public static final String SCOPE_STRATEGY_EDIT = "scope_strategy_edit";

    public static final String SCOPE_STRATEGY_DELETE = "scope_strategy_delete";

    public static final String INTERVAL_STRATEGY_EDIT = "interval_strategy_edit";

    public static final String INTERVAL_STRATEGY_DELETE = "interval_strategy_delete";

    public static final String LOGIC_GROUP_ROOT = GlobalOptional.GLOBAL_ROOT;

    /**
     * 电池根节点国际化
     */
    public static final String SCOPE_STRATEGY_WHOLE_NETWORK = "{\"zh_CN\": \"全网\",\"en_US\": \"Whole Network\"}";

    public static final String HOUR = "hour";

    public static final String DAY = "day";

    public static final String MONTH = "month";

    public static final String YEAR = "year";
    //告警等级
    public final static Integer NO_ALARM = 0;

    public final static String ZERO = "0";

    public final static String TREE_DECIMAL_PLACES_ZERO = "0.000";

    public final static String BATT = "batt";


    public static final String NOT_EXIST = "0";

    public static final String EXIST = "1";

    public static String SEPARATOR = "-";
    public static String FIRST_DAY_OF_MONTH = "01";
    public static String FIRST_SECEND = "T00:00:00";
    public static String LAST_SECEND = "T23:59:59";

    /**
     * 阶梯计费模式--档位
     */
    public static final Integer TIER_1 = 1;

    // 能源类型--市电
    public static final String ENERGY_TYPE_GRID = "0";
    // 能源类型--太阳能
    public static final String ENERGY_TYPE_SOLAR = "1";

    /**
     * pma
     */
    /**
     * pma数据开始时间字段
     */
    public static final String BEGIN_TIME = "begintime";

    /**
     * pma数据资源id
     */
    public static final String RES_ID = "res_id";

    /**
     * pma历史数据库redis key
     */
    public static final String IMOP_PMA_MODEL = "imop-pma-model";

    /**
     * AI健康度和寿命开关
     */
    public static final String AI_SWITCH_ON="ON";
    public static final String AI_SWITCH_OFF="OFF";
    public static final String AI_SWITCH_ID_SOH="ai.soh";
    public static final String AI_SWITCH_ID_LIFE="ai.life";
    public static final String AI_SWITCH_ID_EOL="life.eol";

    //SOH-licenseId
    public static final String SOH_PREDICTION = "uedm.maintenance.license.forecast.soh.prediction";

    public static final String SOH_OPTIMIZATION = "uedm.maintenance.license.soh.optimization";

    //健康阈值缓存
    public static final String THRESHOLD_HEALTH_CACHE = "THRESHOLD_HEALTH";

    public static final String FALSE = "false";

    public static final String TRUE = "true";

    public static final String ENCODE_GBK = "GBK";

    public static final String MASTER_DIRECTORY = "/home/<USER>/local/";

    //电池健康状态评估记录天表缓存
    public static final String CACHE_NAME_BATT_BACK_POWER_EVAL = "BATT_BACK_POWER_EVAL";
    //电池健康状态评估记录表缓存
    public static final String CACHE_NAME_BATT_HEALTH_STATUS_EVAL = "BATT_HEALTH_STATUS_EVAL";
    public static final String BATT_BACK_POWER = "backPower";
    public static final String BATT_HEALTH_STATUS = "healthStatus";

    public static final String NO_VALUE_PLACEHOLDER_STR = "--";
    public static final String UNIT_KW = "KW";
    public static final String UNIT_MW = "MW";

    // 电池累计放电电量
    public static final String BATT_DISCHARGE_QUANTITY = "battery.dischargekwh";

    // TODO 电池累计充电电量
    public static final String BATT_CHARGE_QUANTITY = "battery.chargekwh";

    //保存下电剩余发电时长的常量key
    public static final String LLVD_DISCHARGE_DURATION = "llvd.discharge.duration";
    public static final String  SCUC_PROTOCOl = "SCUC";
    public static final String  MOC_SCUC="r32.uedm.collector.scuc";
    public static final String  MOC_BCUA="r32.uedm.collector.bcua";
    public static final String  MOC_SNMP="r32.uedm.collector.snmp";
    //todo 暂时不知
    public static final String  CSU6__PROTOCOL = "";

    //调用selectSpHealthStatus方法的父方法名
    public static final String SETSPSTATUS = "setSpStatus";

    /* Started by AICoder, pid:z5e48b96f7mfec61474308a4f090e8965ae6f79c */
    public static final String LOG_ID_PREFIX_ENABLE_PEAK_CLEAN = "ENABLE_PEAK_CLEAN";

    public static final String LOG_ID_PREFIX_ENABLE_PEAK_WEEKEND_CLEAN = "ENABLE_PEAK_WEEKEND_CLEAN";

    public static final String LOG_ID_PREFIX_CHARGING_MODE_CLEAN = "CHARGING_MODE_CLEAN";

    public static final String LOG_ID_PREFIX_PEAK_SHIFT_DATE_CLEAN = "PEAK_SHIFT_DATE_CLEAN";

    public static final String LOG_ID_PREFIX_PEAK_SHIFT_TIME_CLEAN = "PEAK_SHIFT_TIME_CLEAN";

    public static final String LOG_ID_PREFIX_PEAK_SHIFT_DATE = "PEAK_SHIFT_DATE";

    public static final String LOG_ID_PREFIX_PEAK_SHIFT_TIME = "PEAK_SHIFT_TIME";

    public static final String LOG_ID_PREFIX_PEAK_SHIFT_TIME_TYPE = "PEAK_SHIFT_TIME_TYPE";

    public static final String PEAK_SHIFT_STOP = "PEAK_SHIFT_STOP";

    public static final String SPECIAL_PEAK_MODULE_ID = "ZXDT22_SF01(V3.07)_V3.07.34.00T03";

    /**
     * 六代错峰用电使能原始测点
     */
    public static final String CSU6_PEAK_SHIFT_ENABLE = "801800290801"; // 六代电源错峰用电使能

    public static final String CSU6_BATTERY_CHARGE_MODE = "801800283E01"; // 六代电源电池充电模式（用来判断是否支持错峰）For VERSION ZXDT22_SF01(V3.07)_V3.07.34.00T03

    /**
     * 六代电池原始测点：错峰日段
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_DATE_PERIOD = "80180028DE01";

    /**
     * 六代电池原始测点：错峰时段1
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_PERIOD_1 = "80180028DF01";

    /**
     * 六代电池原始测点：错峰时段2
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_PERIOD_2 = "80180028E001";

    /**
     * 六代电池原始测点：错峰时段3
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_PERIOD_3 = "80180028E101";

    /**
     * 六代电池原始测点：错峰时段4
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_PERIOD_4 = "80180028E201";

    /**
     * 六代电池原始测点：错峰时段5
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_PERIOD_5 = "80180028E301";

    /**
     * 六代电池原始测点：错峰时段1 错峰类型
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_TYPE_1 = "80180028E401";

    /**
     * 六代电池原始测点：错峰时段2 错峰类型
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_TYPE_2 = "80180028E501";

    /**
     * 六代电池原始测点：错峰时段3 错峰类型
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_TYPE_3 = "80180028E601";

    /**
     * 六代电池原始测点：错峰时段4 错峰类型
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_TYPE_4 = "80180028E701";

    /**
     * 六代电池原始测点：错峰时段5 错峰类型
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_TIME_TYPE_5 = "80180028E801";

    /**
     * 六代电源节假日
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_HOLIDAY = "80180028ED01";

    /**
     * 六代电源周末错峰使能
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_WEEKEND_ENABLE = "80180028EC01";

    /**
     * 六代电源周末错峰使能值
     */
    public static final String CSU6_BATTERY_PEAK_SHIFT_WEEKEND_ENABLE_VALUE = "1";

    public static final String CSU6_BATTERY_CHARGE_MODE_TIMING = "2"; // 电池充电模式（时段）
    /* Ended by AICoder, pid:z5e48b96f7mfec61474308a4f090e8965ae6f79c */

    /* Started by AICoder, pid:n3c6f0dba0o2c041470208a7308dc29f6ec8e86c */
    /**
     * 采集器协议属性中的采集周期
     */
    public static final String SIGNAL_CYCLE = "signal_cycle";

    /**
     * 采集器链路信息中的主链路
     */
    public static final String MASTER_LINK = "master";

    /**
     * 不同协议，使能原始测点id不一样
     */
    public static Map<String, String> ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP = new HashMap<>();

    /**
     * R321电池充放电状态原始测点id
     */
    public static final String ORIGIN_POINT_R321_BATTERY_STATUS = "320020-0{battery_num}-V1.00.03.00-V{snmp_version}";

    public static final String R321_BATTERY_STATUS_ORIGIN_POINT = "320020-0{battery_num}";

    public static final String SNMP_VERSION = "{snmp_version}";

    public static final String BATTERY_NUM = "{battery_num}";

    public static final String R321_BATTERY_STATUS_CHARGE = "0";

    /**
     * 25.01版本开始，SNMP的原始测点由{设备测点}-{电池编号}-{字典版本}-V{SNMP版本号}修改为{设备测点}-{电池编号}
     */
    public static final String SNMP_ENABLE_POINT = "330079";

    static {
        ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP.put("r32.uedm.protocol.snmpv1", "330079-V1.00.03.00-V1");
        ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP.put("r32.uedm.protocol.snmpv2", "330079-V1.00.03.00-V2");
        ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP.put("r32.uedm.protocol.snmpv3", "330079-V1.00.03.00-V3");
        // 25.01版本开始，SNMP的原始测点由{设备测点}-{电池编号}-{字典版本}-V{SNMP版本号}修改为{设备测点}-{电池编号}
        ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP.put(SNMP_VERSION, SNMP_ENABLE_POINT);
    }

    /**
     * SNMP 使能
     */
    public static final String ORIGIN_POINT_VALUE_SNMP_ENABLE_PEAK = "1";

    /**
     * SNMP 失能
     */
    public static final String ORIGIN_POINT_VALUE_SNMP_DIS_ENABLE_PEAK = "0";

    /**
     * 链路状态为0表示链路已连接
     */
    public static final String LINK_CONNECT_STATUS = "0";

    public static final String SUFFIX_XLS = ".xls";
    public static final String SUFFIX_XLSX = ".xlsx";

    public static final String SUFFIX_BIN = ".bin";

    public static final String PEAK_ENABLE_MAP_KEY_COLLECTOR_ID = "id";

    public static final String PEAK_ENABLE_MAP_KEY_MAIN_LINK = "mainLinkid";
    public static final String PEAK_ENABLE_MAP_KEY_SIGNAL_CYCLE = "signalCycle";
    public static final String PEAK_ENABLE_MAP_MODULE_ID = "moduleId";

    public static final String PEAK_ENABLE_SNMP_LOG = "PEAK_ENABLE_SNMP";

    /**
     * 南向文件传输响应
     */
    public static final String KAFKA_TOPIC_REMOTE_FILE_TRANSFER_RSP = "remote-file-transfer-rsp";

    public static final String PEAK_SNMP_LOG_PREFIX = "SNMP_DISTRIBUTE";

    /**
     * SNMP错峰下发文件传输结果：成功
     */
    public static final String SNMP_FILE_TRANSFER_SUCCESS = "0";

    /**
     * SNMP错峰设置结果：成功
     */
    public static final String SNMP_SET_RESULT_SUCCESS = "0";

    /**
     * SNMP错峰设置结果key
     */
    public static final String PEAK_SNMP_RESULT_SET_RESULT = "setResult";

    public static final String SNMP_BIN_FILE_NAME = "PEAKSHIFTTEMPLATE.bin";

    /**
     * 清除备份恢复注册的国际化日志名称
     */
    public static final String LOG_NAME_CLEAR_REGISTRATION = "{\"zh_CN\":\"清除备份恢复注册\",\"en_US\":\"Clear backup and restore registration\"}";
    /* Ended by AICoder, pid:n3c6f0dba0o2c041470208a7308dc29f6ec8e86c */

    //重试次数
    public static final String PEAK_SHIFT_STOP_STATUS = "status";
    public static final String PEAK_SHIFT_STOP_RETRY_TIMES = "retryTimes";
    public static final String PEAK_SHIFT_STOP_INFO = "taskInfo";
    public static final int PEAK_SHIFT_STOP_WITHOUT_REDIS = 0;

}
