package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterOverviewRequestDto;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池概览维度ervice
 *
 * <AUTHOR>
 */
public interface BatteryOverviewConfigService {

    List<BatteryOverviewBean> selectOverviewConfig(String userName, String languageOption) throws UedmException;


    Integer updateOverviewConfig(List<BatteryOverviewUpdateBean> updateBeanList, String userName) throws
    UedmException;


    PageInfo<BatteryOverviewBean> searchOverviewConfig(BatteryConfigSearchBean searchBean, String userName,
                                                       String languageOption) throws UedmException;

    ResponseBean selectOverviewByCondition(BatteryOverviewFilterOverviewRequestDto conditionBean, ServiceBaseInfoBean serviceBean) throws UedmException;

    String exportOverview(BatteryOverviewExportBean exportBean, HttpServletRequest request, HttpServletResponse response, ServiceBaseInfoBean serviceBean) throws UedmException;

    ResponseBean parameterCheck(BatteryOverviewExportBean exportBean, ServiceBaseInfoBean serviceBean)throws UedmException;
}
