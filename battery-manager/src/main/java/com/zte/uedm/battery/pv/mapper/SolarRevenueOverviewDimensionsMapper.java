package com.zte.uedm.battery.pv.mapper;

import com.zte.uedm.battery.pv.bean.SolarRevenueOverviewDimensionsBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SolarRevenueOverviewDimensionsMapper {

    //框架占位，后续删除，不要使用
    List<SolarRevenueOverviewDimensionsBean> select();


    /**
     * 根据用户获取太阳能收益概览维度
     * @param userName
     * @return
     * @throws UedmException
     */
    List<SolarRevenueOverviewDimensionsBean> selectByUserName(@Param("userName") String userName) throws UedmException;

    Integer insertSolarRevenueOverviewDims(List<SolarRevenueOverviewDimensionsBean> SolarRevenueOverviewDimensionsBeanList);
    Integer updateSolarRevenueOverviewDims(List<SolarRevenueOverviewDimensionsBean> SolarRevenueOverviewDimensionsBeanList);
}
