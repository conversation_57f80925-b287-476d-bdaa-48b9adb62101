package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.PeakShiftDeviceStatusBean;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.util.BatchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class PeakShiftDeviceStatusSchduleJob
{

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Autowired
    private GridStrategyMapper gridStrategyMapper;

    @Autowired
    private DateTimeService dateTimeService;


    private static final Map<String, String> statusMap = new HashMap<>();

    static
    {
        statusMap.put("enabled","0");
        statusMap.put("part","0");
        statusMap.put("disabled","1");
        statusMap.put("unknown","1");
    }
    public static String getRealColType(String type){
        return statusMap.getOrDefault(type,"1");
    }

    @Scheduled(cron = "0 3 0 * * ?") //每天00:03执行
    public void execute()
    {
        try
        {
            List<PeakShiftDeviceStatusBean> list = getDeviceStatusInfos();
            BatchUtils.doInBatch(5000,list,(item)->{
                gridStrategyMapper.batchDelete(item);
                int count = gridStrategyMapper.batchInsert(item);
                log.info(" insert count : {}",count);
            });
        }
        catch (Exception e)
        {
            log.error("PeakShiftDeviceStatusSchduleJob execute occur exception:", e);
        }
    }

    private List<PeakShiftDeviceStatusBean> getDeviceStatusInfos() throws UedmException
    {
        List<DevicePeakCacheInfoBean> list = configurationManagerRpc.queryAllList();
        log.info("list size is {}",list.size());
        List<PeakShiftDeviceStatusBean> statusBeanList = new ArrayList<>();
        for (DevicePeakCacheInfoBean bean : list)
        {
            PeakShiftDeviceStatusBean statusBean = new PeakShiftDeviceStatusBean();
            statusBean.setDeviceId(bean.getDeviceId());
            statusBean.setStatus(getRealColType(bean.getRunningStatus()));
            statusBean.setTime(dateTimeService.getCurrentTime().split(" ")[0]);
            statusBeanList.add(statusBean);
        }
        return statusBeanList;
    }
}
