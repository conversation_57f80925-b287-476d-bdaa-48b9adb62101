package com.zte.uedm.battery.a_domain.aggregate.peakshift.repository;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.DeviceAccumulatedKwhGainEntity;

import java.util.List;

public interface PeakShiftMonitorRepository {

    List<DeviceAccumulatedKwhGainEntity> queryDeviceAccumulatedKwhGain(String beginTime, String endTime,
                                                                   List<String> deviceId) throws UedmException;

    List<DeviceAccumulatedKwhGainEntity> queryDeviceStrategyTypeKwhGain(String beginTime, String endTime,
                                                                        List<String> deviceId) throws UedmException;

    List<DeviceAccumulatedKwhGainEntity> queryDeviceDailyKwhGain(String beginTime, String endTime,
                                                                       List<String> deviceId) throws UedmException;

    List<DeviceAccumulatedKwhGainEntity> queryDeviceDailyStrategyTypeKwhGain(String beginTime, String endTime,
                                                                        List<String> deviceId) throws UedmException;
}
