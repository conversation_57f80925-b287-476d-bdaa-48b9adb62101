package com.zte.uedm.battery.rpc.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class HistoryDataQueryDto {

    /**
     * pageSize 分页大小
     */
    private Integer pageSize;

    /**
     * pageNo 第几页
     */
    private Integer pageNo;

    /**
     * grain 汇聚粒度
     */
    private String grain;

    /**
     * grain 汇聚粒度可填项
     */
    private String grainWrite;

    /**
     * startTime 记录开始时间
     */
    private String startTime;

    /**
     * endTime 记录结束时间
     */
    private String endTime;

    /**
     * moc 监控对象类型
     */
    private List<String> moc;

    /**
     * moc 监控对象类型名字
     */
    private List<String> mocName;

    /**
     * smpId 标准测点id
     */
    private List<List<String>> smpId;

    /**
     * pointType 测点类型
     */
    private String pointType;


    /**
     * position 位置id
     */
    private List<String> positionId;


    /**
     * position 位置
     */
    private List<String> position;

    private String languageOption;
}
