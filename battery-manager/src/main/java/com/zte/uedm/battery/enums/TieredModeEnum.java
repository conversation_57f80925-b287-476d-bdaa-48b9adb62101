package com.zte.uedm.battery.enums;

/**
 * <AUTHOR>
 * @description 阶梯计费周期
 * @date 2023/11/20
 **/
public enum TieredModeEnum {

    /**
     * 月
     */
    MONTH("month", 0),

    /**
     * 年
     */
    YEAR("year", 1);

    private String tieredMode;
    private Integer tieredModeCode;

    TieredModeEnum(String tieredMode, Integer tieredModeCode) {
        this.tieredMode = tieredMode;
        this.tieredModeCode = tieredModeCode;
    }

    /**
     * 根据CODE获取对应的枚举
     *
     * @param tieredMode
     * @return
     */
    public static TieredModeEnum getByTieredMode(String tieredMode) {
        for (TieredModeEnum tieredModeEnum : TieredModeEnum.values()) {
            if (tieredModeEnum.getTieredMode().equals(tieredMode)) {
                return tieredModeEnum;
            }
        }
        return null;
    }

    /**
     * @return
     */
    public String getTieredMode() {
        return this.tieredMode;
    }

    /**
     * @return
     */
    public Integer getTieredModeCode() {
        return this.tieredModeCode;
    }
}
