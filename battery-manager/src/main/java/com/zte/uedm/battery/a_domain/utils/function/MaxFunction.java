package com.zte.uedm.battery.a_domain.utils.function;

import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;

import java.util.Map;

/**
 * 取最大值
 */
public class MaxFunction extends AbstractVariadicFunction
{


    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
        double max = Double.NEGATIVE_INFINITY;
        for (AviatorObject arg : args) {
            double value = ((Number) arg.getValue(env)).doubleValue();
            if (value > max) {
                max = value;
            }
        }
        return new AviatorDouble(max);
    }

    @Override
    public String getName()
    {
        return "Max";
    }
}