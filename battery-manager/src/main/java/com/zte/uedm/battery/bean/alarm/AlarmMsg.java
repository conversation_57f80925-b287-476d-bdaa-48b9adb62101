package com.zte.uedm.battery.bean.alarm;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class AlarmMsg
{
    private String moc;
    private String alarmsource;
    private Long commenttime;
    private Long alarmraisedtime;
    private String ackuserid;
    private String link;
    private boolean naffiltered;
    private int clearstate;
    private Long nmcreasoncode;
    private Long acktime;
    private String commentuserid;
    private boolean admc;
    private Long alarmchangedtime;
    private int ackstate;
    private AdditionalText additionaltext;
    private String me;
    private String timezoneid;
    private Long id;
    private String alarmkey;
    private Long alarmcode;
    private String acksystemid;
    private int visible;
    private String commenttext;
    private int alarmtype;
    private Long reasoncode;
    private long servertime;
    private int perceivedseverity;
    private String commentsystemid;
    private String neip;
    private String restype;
    private Long sequence;
    private String ackinfo;
    private int relationflag;
    private AlarmTitle alarmtitle;
    private String position;
    private MaintainStatus maintainstatus;
    private String aid;
}