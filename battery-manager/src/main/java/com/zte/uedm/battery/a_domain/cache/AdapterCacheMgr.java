package com.zte.uedm.battery.a_domain.cache;


import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AdapterCacheMgr<K,V>{
    /**
     * 根据id查询
     * @param ids
     * @return
     */
     List<AdapterEntity> selectByIds(List<String> ids);

    Map<String, AdapterEntity> selectMapByIds(Set<String> adapterIds) throws UedmException;

    /**
     * 根据原始测点获取对应的适配组件id
     * @param adapterPointIds
     * @return
     */
    List<String> selectAdapterPointId(Set<String> adapterPointIds);
}
