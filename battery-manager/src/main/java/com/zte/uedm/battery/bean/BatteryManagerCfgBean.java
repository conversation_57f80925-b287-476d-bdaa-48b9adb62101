package com.zte.uedm.battery.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 电池管理配置
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@ApiModel(description = "电池配置管理")
public class BatteryManagerCfgBean {
    @ApiModelProperty(value = "标识")
    private Integer id;
    @ApiModelProperty(value = "监控标识")
    private String moOid;
    @ApiModelProperty(value = "配置类型")
    private String cfgType;
    @ApiModelProperty(value = "配置值")
    private String cfgValue ;
    @ApiModelProperty(value = "单位")
    private String cfgValueUnit ;
    @ApiModelProperty(value = "gmt新增")
    private String gmtCreate;
    @ApiModelProperty(value = "gmt修改")
    private String gmtModified;


}
