package com.zte.uedm.battery.a_infrastructure.safe.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.safe.BatteryEkeyRelationEntity;
import com.zte.uedm.battery.a_domain.safe.repository.BatteryEkeyRelationRepository;
import com.zte.uedm.battery.a_infrastructure.safe.converter.BatteryEkeyRelationConverter;
import com.zte.uedm.battery.a_infrastructure.safe.po.BatteryEkeyRelationPo;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.BatteryEkeyRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class BatteryEkeyRelationRepositoryImpl implements BatteryEkeyRelationRepository {

    @Resource
    private BatteryEkeyRelationMapper batteryEkeyRelationMapper;
    private static final String DB_MO_ID_FIELD = "mo_id";

    @Override
    @Transactional
    public Integer updateOne(BatteryEkeyRelationPo ekeyRelationPo) {
        QueryWrapper<BatteryEkeyRelationPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(DB_MO_ID_FIELD,ekeyRelationPo.getMoId());
        BatteryEkeyRelationPo batteryEkeyRelationPo = batteryEkeyRelationMapper.selectOne(queryWrapper);
        if (batteryEkeyRelationPo != null)
        {
            return batteryEkeyRelationMapper.update(ekeyRelationPo,queryWrapper);
        }
        else
        {
            return batteryEkeyRelationMapper.insert(ekeyRelationPo);
        }
    }

    @Override
    public List<BatteryEkeyRelationEntity> selectBatteryList(List<String> moIds) {
        LambdaQueryWrapper<BatteryEkeyRelationPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(moIds),BatteryEkeyRelationPo::getMoId,moIds);
        List<BatteryEkeyRelationPo> batteryEkeyRelationPos = batteryEkeyRelationMapper.selectList(queryWrapper);
        return BatteryEkeyRelationConverter.converterPoToEnList(batteryEkeyRelationPos);
    }

    @Override
    public Integer updateBatch(List<BatteryEkeyRelationPo> updateBatch)
    {
        if (CollectionUtils.isEmpty(updateBatch))
        {
            return 0;
        }
        try {
            return batteryEkeyRelationMapper.updateBatch(updateBatch);
        } catch (Exception e) {
            log.error("BatteryEkeyRelationRepositoryImpl [updateBatch] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }
}
