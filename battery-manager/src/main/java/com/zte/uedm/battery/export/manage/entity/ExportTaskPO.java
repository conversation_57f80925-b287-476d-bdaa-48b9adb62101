package com.zte.uedm.battery.export.manage.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class ExportTaskPO {
        private String id;
        private String exportKey;
        private String params;
        private Integer status;
        private String progress;
        private String createUser;
        private Date gmtCreate;
        private String fileName;
        private String filePath;
        private Date completeTime;
        private String serviceBean;
}
/* Ended by AICoder, pid:k1f873257bg48d614daa09b900bd9d2297704b23 */

