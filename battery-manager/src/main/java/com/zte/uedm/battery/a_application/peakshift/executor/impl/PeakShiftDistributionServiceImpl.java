package com.zte.uedm.battery.a_application.peakshift.executor.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_application.peakshift.converter.PeakShiftDistributionConverter;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftDistributionService;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.PeakShiftDeviceTaskEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.PeakShiftDistributionDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.PeakShiftStrategyDistributionEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.PeakShiftTaskEntity;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftDistributionCrudDomainService;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftDistributionTaskDomainService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftConfigServiceImpl;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskPo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftDetailDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftDeviceTaskDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftStrategyDistributionDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftTaskDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.*;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.uedm.basis.util.base.i18n.I18nUtils.ZH_CN;
import static com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants.*;
import static com.zte.uedm.battery.service.impl.AutoPeakShiftStrategyServiceImpl.*;

@Service
@Slf4j
public class PeakShiftDistributionServiceImpl implements PeakShiftDistributionService {

    @Autowired
    private PeakShiftDistributionCrudDomainService peakShiftDistributionCrudDomainService;

    @Autowired
    private PeakShiftDistributionTaskDomainService peakShiftDistributionTaskDomainService;
    @Autowired
    private PeakShiftConfigServiceImpl peakShiftConfigService;

    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Override
    public PageInfo<PeakShiftStrategyDistributionVo> selectByCondition(PeakShiftStrategyDistributionDto distributionDto, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        List<PeakShiftStrategyDistributionEntity> peakShiftStrategyDistributionEntities = peakShiftDistributionCrudDomainService.selectByCondition(distributionDto, serviceBaseInfoBean);
        List<PeakShiftStrategyDistributionVo> peakShiftStrategyDistributionVos = PeakShiftDistributionConverter.INSTANCE.distributionEntityToVo(peakShiftStrategyDistributionEntities);
        String name = i18nUtils.getMapFieldByLanguageOption(AUTO_TASK, serviceBaseInfoBean.getLanguageOption());
        for (PeakShiftStrategyDistributionVo pemplateStrategyVo : peakShiftStrategyDistributionVos) {
            if(DEFAULT_USER.equals(pemplateStrategyVo.getCreator())){
                pemplateStrategyVo.setName(pemplateStrategyVo.getName().replace(AUTOTASK, name));
            }
        }


        PageInfo<PeakShiftStrategyDistributionVo> peakShiftTaskVoPageInfo = new PageInfo<>(peakShiftStrategyDistributionVos);
        peakShiftTaskVoPageInfo.setTotal(peakShiftStrategyDistributionVos.size());
        if (serviceBaseInfoBean.isPage()) {
            peakShiftStrategyDistributionVos = PageUtil.getPageList(peakShiftStrategyDistributionVos, serviceBaseInfoBean.getPageNo(), serviceBaseInfoBean.getPageSize());
        }
        peakShiftTaskVoPageInfo.setList(peakShiftStrategyDistributionVos);

        return peakShiftTaskVoPageInfo;
    }

    @Override
    public Integer insert(PeakShiftTaskDto peakShiftTaskBeanDto, String userName, ServiceBaseInfoBean serviceBean, String languageOption) throws UedmException, InterruptedException {
        return peakShiftDistributionCrudDomainService.insert(peakShiftTaskBeanDto,userName,serviceBean,languageOption);
    }

    @Override
    public Integer update(PeakShiftTaskDto peakShiftTaskBeanDto, String userName, ServiceBaseInfoBean serviceBean, String languageOption) throws UedmException, InterruptedException {
        return peakShiftDistributionCrudDomainService.update(peakShiftTaskBeanDto,userName,serviceBean,languageOption);
    }

    @Override
    public void delete(String id, ServiceBaseInfoBean serviceBean) throws UedmException {
        peakShiftDistributionCrudDomainService.delete(id,serviceBean);
    }

    @Override
    public Boolean duplicateNameCheck(String id, String name) throws UedmException {
        return peakShiftDistributionTaskDomainService.duplicateNameCheck(id,name);
    }

    @Override
    public PeakShiftTaskDetailVo detailById(PeakShiftDetailDto requestBean, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        PeakShiftDistributionDetailEntity peakShiftDistributionDetailEntity = peakShiftDistributionTaskDomainService.detailById(requestBean, serviceBaseInfoBean);
        return PeakShiftDistributionConverter.INSTANCE.detailEntityToDetailVo(peakShiftDistributionDetailEntity);
    }

    @Override
    public Pair<Integer, String> doStatusFlip(String id, String status, ServiceBaseInfoBean serviceBean) throws UedmException {
        return peakShiftDistributionTaskDomainService.doStatusFlip(id,status,serviceBean);
    }

    @Override
    public List<PeakShiftTaskPo> checkTaskByname(String bean) throws UedmException {
        List<PeakShiftTaskEntity> peakShiftTaskEntities = peakShiftDistributionTaskDomainService.checkTaskByname(bean);
        return PeakShiftDistributionConverter.INSTANCE.taskEntityToTaskPo(peakShiftTaskEntities);
    }

    /* Started by AICoder, pid:6f45b78f7fg89ff14bc10b3f109a2c1f1e975c8f */
    @Override
    public PageInfo<PeakShiftDeviceTasKVo> selectDeviceTaskInfoByCondition(PeakShiftDeviceTaskDto requestBean, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException {
        List<PeakShiftDeviceTaskEntity> peakShiftDeviceTasks = peakShiftDistributionTaskDomainService.getPeakShiftDeviceTasks(requestBean, serviceBaseInfoBean);
        //权限过滤
        Set<String> stringSet = peakShiftDeviceTasks.stream().map(PeakShiftDeviceTaskEntity::getId).collect(Collectors.toSet());
        Set<String> resourceIds = configurationManagerRpcImpl.getAuthPositionsByUser(serviceBaseInfoBean.getUserName(),stringSet);
        peakShiftDeviceTasks = peakShiftDeviceTasks.stream().filter(moObjectConfiguration -> resourceIds.contains(moObjectConfiguration.getId())).collect(Collectors.toList());
        List<PeakShiftDeviceTasKVo> peakShiftDeviceTasKVos = PeakShiftDistributionConverter.INSTANCE.deviceTaskEntityToDeviceTaskVo(peakShiftDeviceTasks);
        List<PeakShiftConfigVo> peakShiftConfigVos = peakShiftConfigService.getPeakShiftConfigVos(ZH_CN);
        /* Started by AICoder, pid:yc0dencae5f5cea1460c0863f037a415ffc08c02 */
        Map<String, String> strategyTypeMap = peakShiftConfigVos.stream()
                .collect(Collectors.toMap(PeakShiftConfigVo::getId, PeakShiftConfigVo::getStrategyType));
        peakShiftDeviceTasKVos.forEach(taskVo -> {
            String strategyType = strategyTypeMap.getOrDefault(taskVo.getId(), null);
            taskVo.setStrategyType(strategyType);
            taskVo.setStrategyTypeCode(PEAK_SHIFT_CONFIG_CODE.get(strategyType));
        });
        /* Ended by AICoder, pid:yc0dencae5f5cea1460c0863f037a415ffc08c02 */
        PageInfo<PeakShiftDeviceTasKVo> peakShiftDeviceTaskVoPageInfo = new PageInfo<>(peakShiftDeviceTasKVos);
        peakShiftDeviceTaskVoPageInfo.setTotal(peakShiftDeviceTasKVos.size());
        if (serviceBaseInfoBean.isPage()) {
            peakShiftDeviceTasKVos = PageUtil.getPageList(peakShiftDeviceTasKVos, serviceBaseInfoBean.getPageNo(), serviceBaseInfoBean.getPageSize());
        }
        peakShiftDeviceTaskVoPageInfo.setList(peakShiftDeviceTasKVos);
        return peakShiftDeviceTaskVoPageInfo;
    }
    /* Ended by AICoder, pid:6f45b78f7fg89ff14bc10b3f109a2c1f1e975c8f */

}
