package com.zte.uedm.battery.controller.batterytesttask.dto;

import com.zte.uedm.battery.enums.batttest.BattTestStatusEnums;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import java.util.*;

@Getter
@Setter
@ToString
@Slf4j
@ApiModel(description = "设备详情")
public class DeviceDetailDto
{
    @NotBlank(message = "taskId can not  be blank")
    @ApiModelProperty(value = "任务标识")
    private String taskId;
    @ApiModelProperty(value = "状态")
    private List<String> status;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "排序关键字")
    private String order;
    @ApiModelProperty(value = "排序顺序")
    private String sort;


    public Pair<Boolean, Map<String, List<String>>> checkParams()
    {
        Map<String, List<String>> checkResultMap = new HashMap<>();
        Boolean checkStatus=true;
        if(CollectionUtils.isNotEmpty(status))
        {
            List<String> allId = BattTestStatusEnums.getAllId();
            for(String str: status)
            {
                if(!allId.contains(str))
                {
                    log.error("status is not in the range of optional values");
                    checkStatus=false;
                    checkResultMap.put("status",allId);
                    break;
                }
            }
        }
        return Pair.of(checkStatus,checkResultMap);
    }

    /**
     * 排序关键字和顺序校验
     * @throws UedmException
     */
    public Pair<Boolean, Map<String, Set<String>>> checkOrderSort()
    {
        boolean checkResult=true;  //结果：true表示通过，false：未通过
        Set<String> sortIds = SortEnum.getSortIds();
        Map<String, Set<String>> resultMap = new HashMap<>();
        if(StringUtils.isNotBlank(this.order))
        {
            if(!DeviceDetailEnum.checkOrderInRange(this.order))
            {
                log.error("order is Not in the range of optional values");
                Set<String> allOrderKeySet = DeviceDetailEnum.getAllOrderKeySet();
                resultMap.put("order",allOrderKeySet);
                checkResult=false;
            }
            this.order=DeviceDetailEnum.getOrderValue(this.order);
        }
        else
        {
            this.order= DeviceDetailEnum.defaultOrder();
        }

        if(StringUtils.isNotBlank(this.sort))
        {
            if(!sortIds.contains(this.sort))
            {
                log.error("sort is Not in the range of optional values");
                resultMap.put("sort",sortIds);
                checkResult=false;
            }
        }
        else
        {
            this.sort=SortEnum.getAscSortID();
        }
        log.info("SelectEvalDetailDto checkOrderSort  order:{},sort:{}",order,sort);
        return Pair.of(checkResult,resultMap);
    }

}

/**
 *排序关键字枚举量
 */
@Slf4j
enum DeviceDetailEnum {
    NAME("name", "name"),
    PATH_NAME("pathName", "pathName"),
    STATUS("status", "statusId"),
    PRE_TEST_TIME_RANGE("preTestTimeRange", "preTestTimeRange"),
    NEXT_TEST_TIME_RANGE("nextTestTimeRange", "nextTestTimeRange");

    private String orderKey;
    private String orderValue;

    DeviceDetailEnum(String orderKey, String orderValue) {
        this.orderKey = orderKey;
        this.orderValue = orderValue;
    }

    public String getOrderKey() {
        return orderKey;
    }

    public String getOrderValue() {
        return orderValue;
    }

    /**
     * 获取所有枚举id组成Set
     *
     * @return
     */
    public static Set<String> getAllOrderKeySet() {
        Set<String> result = new HashSet<>();
        DeviceDetailEnum[] enums = DeviceDetailEnum.values();
        for (DeviceDetailEnum statusEnum : enums) {
            result.add(statusEnum.getOrderKey());
        }
        return result;
    }

    /**
     * 根据key获取
     *
     * @param orderKey
     * @return
     */
    public static String getOrderValue(String orderKey) {
        DeviceDetailEnum[] arr = DeviceDetailEnum.values();
        for (DeviceDetailEnum enu : arr) {
            if (enu.getOrderKey().equals(orderKey)) {
                return enu.getOrderValue();
            }
        }
        return "";
    }

    /**
     * 判断order是否在可选值范围内
     * true:在    false:不在
     *
     * @param orderKey
     * @return
     */
    public static boolean checkOrderInRange(String orderKey) {
        boolean orderInRangeStatus = true;
        Set<String> allOrderKeySet = getAllOrderKeySet();
        if (!allOrderKeySet.contains(orderKey)) {
            orderInRangeStatus = false;
        }
        return orderInRangeStatus;
    }

    /**
     * 设置默认排序
     *
     * @return
     */
    public static String defaultOrder() {
        return DeviceDetailEnum.STATUS.getOrderValue();
    }
}
