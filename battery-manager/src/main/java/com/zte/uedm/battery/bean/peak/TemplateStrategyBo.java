package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 设备模板策略Bo，对应条件查询所需数据格式
 */

@Getter
@Setter
@ToString
public class TemplateStrategyBo implements Serializable
{
    /**
     * 主键
     */
    private String id;
    /**
     * 策略模板名称
     */
    private String name;
    /**
     * BCUA，CSU5
     */
    private String deviceType;

    /**
     * BCUA，CSU5的中英文名称
     */
    private String deviceTypeName;

    /**
     * web, excel
     */
    private String source;
    /**
     * 季节策略ID
     */
    private String seasonStrategyId;
    /**
     * 季节策略名称（web配置时候专用），Excel导入的时候为null
     */
    private String seasonStrategyName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本
     */
    private String version;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 创建用户
     */
    private String userCreate;
    /**
     * 更新时间
     */
    private String gmtModified;
    /**
     * 修改用户
     */
    private String userModified;

    public String getVersion()
    {
        return "V" + version;
    }
}
