package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.domain.SolarRevenueOverviewDimDomain;
import com.zte.uedm.battery.pv.bean.SolarRevenueOverviewDimensionsBean;
import com.zte.uedm.battery.pv.mapper.SolarRevenueOverviewDimensionsMapper;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class SolarRevenueOverviewDimDomainImpl implements SolarRevenueOverviewDimDomain {
    @Autowired
    private SolarRevenueOverviewDimensionsMapper solarRevenueOverviewDimensionsMapper;

    @Override
    public List<SolarRevenueOverviewDimensionsBean> selectByUserName(String userName) throws UedmException {
        if (StringUtils.isBlank(userName)) {
            log.warn("SolarRevenueOverviewDimDomainImpl selectByUserName userName is empty!");
            return Collections.emptyList();
        }
        try {
            return solarRevenueOverviewDimensionsMapper.selectByUserName(userName);
        } catch (Exception e) {
            log.error("SolarRevenueOverviewDimDomainImpl selectByUserName error", e);
            throw UedmErrorCodeDatabaseUtil.databaseSelectFailed("Data query error!");
        }
    }

    @Override
    public Integer insertSolarRevenueOverviewDims(List<SolarRevenueOverviewDimensionsBean> SolarRevenueOverviewDimensionsBeanList) throws UedmException {
        if (CollectionUtils.isEmpty(SolarRevenueOverviewDimensionsBeanList)) {
            log.warn("SolarRevenueOverviewDimDomainImpl insertSolarRevenueOverviewDims data is empty!");
            return 0;
        }
        try {
            return solarRevenueOverviewDimensionsMapper.insertSolarRevenueOverviewDims(SolarRevenueOverviewDimensionsBeanList);
        } catch (Exception e) {
            log.error("SolarRevenueOverviewDimDomainImpl insertSolarRevenueOverviewDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseAddFailed("Data insert error!");
        }
    }

    @Override
    public Integer updateSolarRevenueOverviewDims(List<SolarRevenueOverviewDimensionsBean> SolarRevenueOverviewDimensionsBeanList) throws UedmException {
        if (CollectionUtils.isEmpty(SolarRevenueOverviewDimensionsBeanList)) {
            log.warn("SolarRevenueOverviewDimDomainImpl updateSolarRevenueOverviewDims data is empty!");
            return 0;
        }
        try {
            return solarRevenueOverviewDimensionsMapper.updateSolarRevenueOverviewDims(SolarRevenueOverviewDimensionsBeanList);
        } catch (Exception e) {
            log.error("SolarRevenueOverviewDimDomainImpl updateSolarRevenueOverviewDims error", e);
            throw UedmErrorCodeDatabaseUtil.databaseUpdateFailed("Data update error!");
        }
    }
}
