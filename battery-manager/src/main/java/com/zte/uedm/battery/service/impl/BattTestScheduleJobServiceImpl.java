package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.bean.MoBasicInfoVo;
import com.zte.uedm.battery.domain.BatteryTestDomain;
import com.zte.uedm.battery.enums.batttest.BattTestCommandResultEnums;
import com.zte.uedm.battery.enums.batttest.BattTestTypeEnums;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.BattTestScheduleJobService;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.consts.GlobalBaseConstants;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_BATT_TEST_FAIL_ALARM;
import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_STATE;

@Service
@Slf4j
public class BattTestScheduleJobServiceImpl implements BattTestScheduleJobService
{
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Autowired
    private DateTimeService dateTimeService;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private BatteryTestDomain batteryTestDomain;
    @Value("${battery-test-task.command_issued_cache_clean_time:3}")
    public Long COMMAND_ISSUED_CACHE_CLEAN_TIME;   //命令下发缓存数据定时清空，默认为三天

    public static final long TIME_OUT = 30;   //超时常量（30分钟）
    //正常
    private static final String ALARM = "1";
    //测试中
    private static final String TEST = "2";

    @Override
    public void battTestResultUpdateJob(String type) throws UedmException
    {
        log.debug("BattTestScheduleJobServiceImpl : start to battTestResultUpdateJob");
        String cacheName = type.concat(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE);
        String testType = type.equals(BatteryConstant.TEMPORARY) ? BattTestTypeEnums.TEMPORARY.getId() : BattTestTypeEnums.PERIODIC.getId();
        //取出所有缓存
        Map<String, Object> allValueMap = redisService.getCacheMap(cacheName);
        log.debug("BattTestScheduleJobServiceImpl : allValueMap size is {}", allValueMap.size());
        if(allValueMap.isEmpty())
        {
            return;
        }
        Map<String, Map<String, Object>> map = new HashMap<>();
        for(Map.Entry<String, Object> entry : allValueMap.entrySet())
        {
            if(null != entry.getValue())
            {
                Map<String, Object> valueMap = (Map<String, Object>) entry.getValue();
                map.put(entry.getKey(), valueMap);
            }
        }
        String nowTime = dateTimeService.getCurrentTime();
        //处理超时数据
        map = dealTimeOutData(map, nowTime, cacheName);
        log.debug("BattTestScheduleJobServiceImpl : after dealTimeOutData map size is {}", map.size());
        //过滤成功、失败数据
        map = filterEndData(map);
        log.debug("BattTestScheduleJobServiceImpl : after filterEndData map size is {}", map.size());
        //处理命令下发状态的数据, 包括开关电源id和电池组id
        List<String> spIds = new ArrayList<>(map.keySet());
        //查询关联电池组
        List<String> battpackIds = new ArrayList<>();
        Map<String, String> battpackRelationSpMap = new HashMap<>();
        dealAllBattpack(battpackIds, battpackRelationSpMap, spIds);
        //查询实时数据更新命令状态
        updateCommandStatus(battpackIds, battpackRelationSpMap, map, cacheName, testType);
    }

    private Map<String, Map<String, Object>> dealTimeOutData(Map<String, Map<String, Object>> map, String timeNow, String cacheName) throws UedmException
    {
        Map<String, Map<String, Object>> tempMap = new HashMap<>();
        for(Map.Entry<String, Map<String, Object>> entry : map.entrySet())
        {
            Map<String, Object> valueMap = entry.getValue();
            //清除超过三天的数据
            if(timeOutCheck(valueMap, timeNow, COMMAND_ISSUED_CACHE_CLEAN_TIME*24*60))
            {
                redisService.delete(cacheName, entry.getKey());
                continue;
            }
            //命令超时
            if(BattTestCommandResultEnums.COMMAND_ISSUING.getId().equals(valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE)) && timeOutCheck(valueMap, timeNow, TIME_OUT))
            {
                valueMap.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, BattTestCommandResultEnums.COMMAND_FAIL.getId());
                redisService.put(cacheName, entry.getKey(), valueMap);
                continue;
            }
            tempMap.put(entry.getKey(), entry.getValue());
        }
        return tempMap;
    }

    private Map<String, Map<String, Object>> filterEndData(Map<String, Map<String, Object>> map)
    {
        Map<String, Map<String, Object>> tempMap = new HashMap<>();
        for(Map.Entry<String, Map<String, Object>> entry : map.entrySet())
        {
            Map<String, Object> valueMap = entry.getValue();
            if(null != valueMap && !BattTestCommandResultEnums.COMMAND_ISSUING.getId().equals(valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE)))
            {
                continue;
            }
            tempMap.put(entry.getKey(), entry.getValue());
        }
        return tempMap;
    }

    public boolean timeOutCheck(Map<String, Object> valueMap, String timeNow, long time) throws UedmException
    {
        Object timeObject = valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME);
        log.debug("timeObject is {}", timeObject);
        String format = "yyyy-MM-dd HH:mm:ss";
        String commandTime = jsonService.jsonToObject(jsonService.objectToJson(timeObject), String.class);
        log.debug("commandTime is {}", commandTime);
        // 超时判断
        long diff = 0l;
        try {
            diff = getTimeDiff(commandTime, timeNow, format);
            log.debug("Timeout is {}", diff);
        } catch (ParseException e)
        {
            log.error("get timeOut error");
        }
        return diff >= time;
    }

    private static long getTimeDiff(String startTime, String endTime, String format) throws ParseException {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        //long nd = 1000 * 24 * 60 * 60;                //一天的毫秒数
        //long nh = 1000 * 60 * 60;                   //一小时的毫秒数
        long nm = 1000 * 60;                      //一分钟的毫秒数
        long diff;
        //计算两个时间的毫秒时间差异
        diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
        long min = diff / nm;               //分钟差异
        log.debug("day : {}.", +min);
        return min;
    }

    private void dealAllBattpack(List<String> battpackIds, Map<String, String> battpackRelationSpMap, List<String> spIds)
            throws UedmException
    {
        Map<String, List<MoBasicInfoVo>> spRelationBattpack = configurationManagerRpc.getChildMonitorObjects(spIds, MocOptional.BATTERY_SET.getId());
        for(Map.Entry<String, List<MoBasicInfoVo>> entry : spRelationBattpack.entrySet())
        {
            if(!CollectionUtils.isEmpty(entry.getValue()))
            {
                for(MoBasicInfoVo moBasicInfoVo : entry.getValue())
                {
                    battpackIds.add(moBasicInfoVo.getId());
                    battpackRelationSpMap.put(moBasicInfoVo.getId(), entry.getKey());
                }
            }
        }
    }

    private void updateCommandStatus(List<String> battpackIds, Map<String, String> battpackRelationSpMap, Map<String, Map<String, Object>> map, String cacheName, String testType)
            throws UedmException
    {
        List<Map<String, Map<String, String>>> dataList = dataRedis.selectRealData(battpackIds,
                Arrays.asList(BATTERYSET_SMPID_STATE, BATTERYSET_SMPID_BATT_TEST_FAIL_ALARM));
        log.debug("updateCommandStatus dataList size is {}", dataList.size());
        log.debug("updateCommandStatus battpackIds is {}, battpackRelationSpMap is {}, map is {}, dataList is {}", battpackIds, battpackRelationSpMap, dataList);
        for(int i = 0; i< dataList.size(); i++)
        {
            Map<String, Map<String, String>> realDataMap = dataList.get(i);
            String battpackId = battpackIds.get(i);
            String spId = battpackRelationSpMap.get(battpackId);
            Map<String, Object> commandStatusValue = map.get(spId);
            if(null == commandStatusValue)
            {
                return;
            }
            Map<String, String> battTestFailAlarmValue = realDataMap.get(BATTERYSET_SMPID_BATT_TEST_FAIL_ALARM);
            if(compareValue(battTestFailAlarmValue, ALARM))
            {
                commandStatusValue.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, BattTestCommandResultEnums.COMMAND_FAIL.getId());
                redisService.put(cacheName, spId, commandStatusValue);
                continue;
            }
            Map<String, String> battTestStateValue = realDataMap.get(BATTERYSET_SMPID_STATE);
            if(compareValue(battTestStateValue, TEST))
            {
                commandStatusValue.put(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, BattTestCommandResultEnums.COMMAND_SUCCESS.getId());
                redisService.put(cacheName, spId, commandStatusValue);
                /* Started by AICoder, pid:m45a0o1ac7ae26b14bbf0abe800b570c11972891 */
                Map<String, Object> valueMap = redisService.getCache(cacheName, spId);
                String causeTime = String.valueOf(valueMap.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME));
                log.info("batt test:{}, {}", spId, causeTime);
                /* Ended by AICoder, pid:m45a0o1ac7ae26b14bbf0abe800b570c11972891 */
                //新增测试记录
                addBattTestRecord(spId, testType, commandStatusValue, causeTime);
            }
        }
    }

    public void addBattTestRecord(String spId, String testType, Map<String, Object> commandStatusValue, String causeTime)
            throws UedmException
    {
        String userName = "";
        if(null != commandStatusValue.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME))
        {
            userName = commandStatusValue.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME).toString();
        }
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, "zh_CN");
        String taskId = null;
        if(null != commandStatusValue.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID))
        {
            taskId = commandStatusValue.get(BatteryConstant.REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID).toString();
        }
        batteryTestDomain.insertTestRecordInfo(Arrays.asList(spId), testType, taskId, serviceBean, causeTime);
    }

    private boolean compareValue(Map<String, String> realData, String value)
    {
        if (null == realData)
        {
            return false;
        }
        Double valueDouble = Double.valueOf(value);
        Double data = Double.valueOf(realData.get(GlobalBaseConstants.STANDARD_VALUE_KEY));
        return valueDouble.equals(data);
    }
}

