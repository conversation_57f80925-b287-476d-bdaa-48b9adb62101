package com.zte.uedm.battery.bean.peak;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(description = "错峰任务重试")
public class PeakShiftTaskRetryBean
{
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务标识")
    private String taskId;

    /**
     * 设备id集合
     */
    @ApiModelProperty(value = "设备标识")
    private List<String> deviceIds;
}
