package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.MocIdsVO;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerconfig.service.impl.BackupPowerConfigServiceImpl;
import com.zte.uedm.battery.domain.BatteryBackupPowerEvalDomain;
import com.zte.uedm.battery.domain.BatteryCurrentDomain;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.BatteryCurrentService;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
@Service
@Slf4j
public class BatteryCurrentServiceImpl implements BatteryCurrentService {
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Autowired
    private BatteryCurrentDomain batteryCurrentDomain;

    @Autowired
    private BackupPowerConfigServiceImpl backupPowerConfigService;

    @Override
    public void batteryCurrentEntrance() throws UedmException {
        //获取开关电源所有监控对象
        List<MoObjectConfiguration> moObjectConfigurations = configurationManagerRpcImpl.getMonitorObjectListWhenIdsBig(new ArrayList<>(), MocOptional.DC_POWER.getId());

        log.info("BatteryCurrentServiceImpl->batteryCurrentEntrance moObjectConfigurations:{}", moObjectConfigurations.size());

        batteryCurrentDomain.countBatteryCurrent(moObjectConfigurations,MocOptional.SP.getId());

        //获取所有是电池组的监控对象

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(BattConst.USERNAME, GlobalConstants.SYSTEM_IP, "zh_CN");
        MocIdsVO mocIdsVO = new MocIdsVO(Collections.singletonList(GlobalOptional.GLOBAL_ROOT), null);

        PageInfo<MonitorObjectDsBean> spMonitorObjectBeanPage = null;
        try {
            spMonitorObjectBeanPage = backupPowerConfigService.getBatteryPackMonitorObjectBeanPage(mocIdsVO, serviceBaseInfoBean, null, null);
            List<MoObjectConfiguration> battPackObjectConfigurations = new ArrayList<>();
            for (MonitorObjectDsBean monitorObjectDsBean : spMonitorObjectBeanPage.getList()) {
                MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
                moObjectConfiguration.setId(monitorObjectDsBean.getId());
                moObjectConfiguration.setPath(monitorObjectDsBean.getPath());
                moObjectConfiguration.setPathId(monitorObjectDsBean.getIdPath());
                moObjectConfiguration.setName(monitorObjectDsBean.getName());
                battPackObjectConfigurations.add(moObjectConfiguration);
            }
            log.info("BatteryCurrentServiceImpl->batteryCurrentEntrance BattPackObjectConfigurations:{}", battPackObjectConfigurations.size());

            batteryCurrentDomain.countBatteryCurrent(battPackObjectConfigurations,MocOptional.BATTERY_SET.getId());
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("BatteryBackupPowerEvalServiceImpl->batteryBackupPowerEvalEntrance BattPackObjectConfigurations failed", e);
        }


    }
}
