package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.MocHistoryQueryToolBean;
import com.zte.uedm.battery.bean.pv.HistoryDataRequestConditionBean;
import com.zte.uedm.battery.domain.MoHistoryDataQueryDomain;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.service.DateTimeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ Author     ：10260977
 * @ Date       ：14:58 2022/6/24
 * @ Description：历史数据查询实现类
 * @ Modified By：
 * @ Version: 1.0
 */
@Service
@Slf4j
public class MoHistoryDataQueryDomainImpl implements MoHistoryDataQueryDomain
{
    @Autowired
    private DateTimeService dateTimeService;

    @Override
    public HistoryDataRequestConditionBean buildHistoryDataConditionBean(String pyTypeMoc, String smpId, String startTime,
            String endTime)
    {
        HistoryDataRequestConditionBean historyData = new HistoryDataRequestConditionBean();
        historyData.setDataType(BatteryConstant.HISTORY_DATA_TYPE_COLLECT);
        historyData.setGrain("day");
        historyData.setPageNo(1);
        historyData.setPageSize(100000);
        historyData.setStartTime(startTime);
        historyData.setEndTime(endTime);
        historyData.setMoc(pyTypeMoc);
        historyData.setSmpId(smpId);
        historyData.setMdcType(0);
        return historyData;
    }

    @Override
    public MocHistoryQueryToolBean buildYesterdayQueryBean(String moc)
    {
        MocHistoryQueryToolBean mocHistoryQuery = new MocHistoryQueryToolBean();
        //时间
        String currentTime = dateTimeService.getCurrentTime();
        Date date = new Date();
        Date mintimeBefore = DateUtils.getMinTimeOfDayBeforeOneDay(date);
        String startTime = DateUtils.getStrDate(mintimeBefore);
        //平台存的数据是**** 00:00:00 -- **** 00:00:00 跨天
        Date maxTimeBefore = DateUtils.getMinTimeOfDay(date);
        String endTime = DateUtils.getStrDate(maxTimeBefore);
        //记录日期 2021-03-08
        String recordDate = startTime.substring(0, 10);
        //pytype
        String pvType = "";
        if (StringUtils.isNotEmpty(moc))
        {
            int i = moc.lastIndexOf(".");
            pvType = moc.substring(i + 1);
        }
        log.info("MoHistoryDataQueryDomainImpl buildYesterdayQueryBean recordDate is {}, pvType is {}", recordDate, pvType);
        mocHistoryQuery.setRecordDate(recordDate);
        mocHistoryQuery.setCurrentTime(currentTime);
        mocHistoryQuery.setStartTime(startTime);
        mocHistoryQuery.setEndTime(endTime);
        mocHistoryQuery.setPvType(pvType);
        return mocHistoryQuery;
    }

    /**
     * 前两天通用查询bean
     *
     * @param moc
     * @return
     */
    @Override
    public MocHistoryQueryToolBean buildTwoDayQueryBean(String moc)
    {
        MocHistoryQueryToolBean mocHistoryQuery = new MocHistoryQueryToolBean();
        //时间
        String currentTime = dateTimeService.getCurrentTime();
        Date date = new Date();
        Date mintimeBefore = DateUtils.getMinTimeOfTwoDayBeforeOneDay(date);
        String startTime = DateUtils.getStrDate(mintimeBefore);
        //平台存的数据是**** 00:00:00 -- **** 00:00:00 跨天
        Date maxTimeBefore = DateUtils.getMinTimeOfDayBeforeOneDay(date);
        String endTime = DateUtils.getStrDate(maxTimeBefore);
        //记录日期 2021-03-08
        String recordDate = startTime.substring(0, 10);

        //pytype
        String pvType = "";
        if (StringUtils.isNotEmpty(moc))
        {
            int i = moc.lastIndexOf(".");
            pvType = moc.substring(i + 1);
        }
        log.info("MoHistoryDataQueryDomainImpl buildTwoDayQueryBean recordDate is {}, pvType is {}", recordDate, pvType);
        mocHistoryQuery.setRecordDate(recordDate);
        mocHistoryQuery.setCurrentTime(currentTime);
        mocHistoryQuery.setStartTime(startTime);
        mocHistoryQuery.setEndTime(endTime);
        mocHistoryQuery.setPvType(pvType);
        return mocHistoryQuery;
    }

}
