package com.zte.uedm.battery.opti.domain.service.bean.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 特殊风险，单独触发
 */
public enum BattRiskEvalDataRuleEnum
{
    /**
     * 寿命风险
     */
    LIFE_RISK_RULE("rule001_01"),
    LIFE_RISK_RULE2("rule001_02"),
    LIFE_RISK_RULE3("rule001_03"),


    /**
     * 电池健康异常
     */
    BATT_HEALTH_EXCEPTION("rule002"),

    /**
     * 电池亚健康
     */
    BATT_SUB_HEALTH("rule003");
    private String id;

    public String getId()
    {
        return id;
    }

    BattRiskEvalDataRuleEnum(String id)
    {
        this.id = id;
    }

    /**
     * 寿命相关风险
     * @return
     */
    public static List<String> getLifeIds()
    {
        List<String> list = new ArrayList<>();
        list.add(LIFE_RISK_RULE.getId());
        list.add(LIFE_RISK_RULE2.getId());
        list.add(LIFE_RISK_RULE3.getId());
        return list;
    }

    /**
     * 健康相关风险
     * @return
     */
    public static List<String> getHealthIds()
    {
        List<String> list = new ArrayList<>();
        list.add(BATT_HEALTH_EXCEPTION.getId());
        list.add(BATT_SUB_HEALTH.getId());
        return list;
    }

    /**
     * 获取所有特殊风险id
     * @return
     */
    public static List<String> getAllIds() {
        List<String> allIds = new ArrayList<>();
        BattRiskEvalDataRuleEnum[] arr = BattRiskEvalDataRuleEnum.values();
        for (BattRiskEvalDataRuleEnum enu : arr) {
            if (StringUtils.isNotBlank(enu.getId())) {
                allIds.add(enu.getId());
            }
        }
        return allIds;
    }
}
