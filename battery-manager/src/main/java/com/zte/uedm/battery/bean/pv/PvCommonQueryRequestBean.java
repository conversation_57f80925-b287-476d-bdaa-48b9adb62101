package com.zte.uedm.battery.bean.pv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import net.bytebuddy.implementation.bind.annotation.Super;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Setter
@Getter
@ToString(callSuper = true)
@ApiModel(description = "太阳能查询")
public class PvCommonQueryRequestBean extends ExceptionHistoryQueryRequestBean
{
    /**
     * 太阳能类别
     */
    @ApiModelProperty(value = "太阳能类别")
    private List<String> pvTypes;

    /**
     * 太阳能名称
     */
    @ApiModelProperty(value = "太阳能名称")
    private String name;

    /**
     * 查询粒度
     */
    @ApiModelProperty(value = "粒度")
    private String grain;
    /**
     * 发电量开始
     */
    @ApiModelProperty(value = "发电量开始")
    private Double generationBegin;
    /**
     * 发电量结束
     */
    @ApiModelProperty(value = "发电量结束")
    private Double generationEnd;
    /**
     * 用电量开始
     */
    @ApiModelProperty(value = "用电量开始")
    private Double consumptionBegin;
    /**
     * 用电量结束
     */
    @ApiModelProperty(value = "用电量结束")
    private Double consumptionEnd;
    /**
     * 发/用电量占比开始
     */
    @ApiModelProperty(value = "发/用电量占比开始")
    private Double ratioBegin;
    /**
     * 发/用电量占比结束
     */
    @ApiModelProperty(value = "发/用电量占比结束")
    private Double ratioEnd;
    /**
     * 排序关键字
     */
    @ApiModelProperty(value = "排序关键字")
    private String order;
    /**
     * desc asc
     */
    @ApiModelProperty(value = "排序顺序")
    private String sort;

    /* Started by AICoder, pid:rab423f294dc74d142720b33009e9607dd5325f4 */
    public boolean checkGrain() {
        return StringUtils.isEmpty(grain) || "y".equals(grain) || "d".equals(grain) || "m".equals(grain);
    }

    /* Ended by AICoder, pid:rab423f294dc74d142720b33009e9607dd5325f4 */
}
