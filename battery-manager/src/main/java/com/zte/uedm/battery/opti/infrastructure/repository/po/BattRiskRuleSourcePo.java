package com.zte.uedm.battery.opti.infrastructure.repository.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class BattRiskRuleSourcePo
{
    /**
     * 风险参数id
     */
    private String id;
    /**
     * 风险参数名称
     */
    private String name;
    /**
     * 来源类型
     */
    private String sourceType;
    /**
     * 来源类型分组
     */
    private String sourceTypeGroup;
    /**
     * 数值类型
     */
    private String valueType;
    /**
     * 映射字段
     */
    private String mappingId;
    /**
     * 值映射
     */
    private String valueMapping;
    /**
     * 值定义
     */
    private String valueDefine;
    /**
     * 是否内置
     */
    private Boolean internal;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date gmtModified;
}
