package com.zte.uedm.battery.domain.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.bean.MoBasicInfoVo;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.domain.BattConfigurationDomain;
import com.zte.uedm.battery.domain.BattLoopDomain;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BattLoopDomainImpl implements BattLoopDomain
{
    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Autowired
    private BattTypeDomain battTypeDomain;
    @Autowired
    private BattConfigurationDomain battConfigurationDomain;
    @Autowired
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;
    @Autowired
    private BattAssetDomain battAssetDomain;
    @Autowired
    private DeviceCacheManager deviceCacheManager;
    @Autowired
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Autowired
    private BatteryAttributeUtils batteryAttributeUtils;

    private final static int SIZE = 1000; //批量查询数量

    @Override
    public Map<String, List<MoBasicInfoVo>> getRelationLoops(List<String> idList) throws UedmException
    {
        if(CollectionUtils.isEmpty(idList))
        {
            log.warn("param is blank!");
        }
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
                //查询开关电源的电池
        BatchUtils.doInBatch(SIZE, idList, (tems) -> {
            try
            {
                spRelationBatt.putAll(configurationManagerRpc.getChildMonitorObjects(tems, MocOptional.BATTERY.getId()));
            }
            catch (Exception e)
            {
                log.error("getRelationLoops, configurationManagerRpc getChildMonitorObjects error: ", e);
            }
        });
        //Map<String, List<MoBasicInfoVo>> spRelationBatt = configurationManagerRpc.getChildMonitorObjects(idList, MocType.BATT);
        log.debug("spRelationBatt is {}", spRelationBatt);
        //查询回路id
        Map<String, List<MoBasicInfoVo>> spRelationLoop = new HashMap<>();
        //查询开关电源的电池
        BatchUtils.doInBatch(SIZE, idList, (tems) -> {
            try
            {
                spRelationLoop.putAll(configurationManagerRpc.getChildBattLoops(tems));
            }
            catch (Exception e)
            {
                log.error("configurationManagerRpc getChildBattLoops error: ", e);
            }
        });
        //Map<String, List<MoBasicInfoVo>> spRelationLoop = configurationManagerRpc.getChildBattLoops(idList);
        log.debug("spRelationLoop is {}", spRelationLoop);
        List<List<MoBasicInfoVo>> values = new ArrayList<>(spRelationBatt.values());
        List<String> collect = values.stream().flatMap(Collection::stream).filter(Objects::nonNull).map(MoBasicInfoVo::getId).distinct().collect(Collectors.toList());
        boolean assetEnable = battAssetDomain.selectAssetEnable();
        List<BattTypeBean> batteryTypes = battTypeDomain.getBatteryTypeWithTest(collect, assetEnable);
        Map<String, BattTypeEnum> batteryTypeMap = batteryTypes.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())).filter(bean -> bean.getBattType()!=null)
                .collect(Collectors.toMap(BattTypeBean::getId, BattTypeBean::getBattType, (key1,key2)->key2));
        Map<String, List<MoBasicInfoVo>> result = new HashMap<>();
        for(Map.Entry<String, List<MoBasicInfoVo>> entry : spRelationBatt.entrySet())
        {
            List<MoBasicInfoVo> batts = entry.getValue();
            List<MoBasicInfoVo> loops = spRelationLoop.get(entry.getKey());
            if(null == loops)
            {
                loops = new ArrayList<>();
            }

            result.put(entry.getKey(), getAllLoops(batts, loops, batteryTypeMap));
        }
        log.debug("result is {}", result);
        return result;
    }

    private List<MoBasicInfoVo> getAllLoops(List<MoBasicInfoVo> batts, List<MoBasicInfoVo> loops, Map<String, BattTypeEnum> batteryTypeMap)
    {
        List<String> loopIds = loops.stream().map(MoBasicInfoVo::getId).distinct().collect(Collectors.toList());
        Set<String> loopSet = new HashSet<>(loopIds);
        Map<String, MoBasicInfoVo> battMap = batts.stream().filter(bean-> StringUtils.isNotBlank(bean.getId())).collect(Collectors
                .toMap(MoBasicInfoVo::getId, bean->bean, (key1, key2) -> key2));
        List<String> battIds = batts.stream().map(MoBasicInfoVo::getId).distinct().collect(Collectors.toList());
        Set<String> battSet = new HashSet<>(battIds);
        battSet.removeAll(loopSet);
        for(String battId : battSet)
        {
            BattTypeEnum battTypeEnum = batteryTypeMap.get(battId);
            MoBasicInfoVo batt = battMap.get(battId);
            if(null != battTypeEnum && null != batt && !loopSet.contains(batt.getParentId()))
            {
                if(BattTypeEnum.PBAC.getCode().equals(battTypeEnum.getCode()))
                {
                    loops.add(batt);
                }
            }
        }
        return loops;
    }

    @Override
    public Map<String, List<MoBasicInfoVo>> getRelationBatts(List<String> idList) throws UedmException
    {

        if(CollectionUtils.isEmpty(idList))
        {
            log.warn("param is blank!");
        }
        Set<String> idSet = new HashSet<>(idList);
        //查询开关电源的电池
        List<DeviceEntity> batteryEntities = deviceCacheManager.getDevicesByMoc(MocOptional.BATTERY.getId());
        Map<String, List<MonitorObjectBean>> spRelationBatt = resourceCollectorRelationCacheManager.getSpRelatedBatterylistMap(idList, batteryEntities);
        Map<String, List<MoBasicInfoVo>> result = spRelationBatt.entrySet().parallelStream()
                .filter(entry -> idSet.contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> getAllBatts(entry.getValue())
                ));
        log.debug("result is {}", result);
        log.info("result.size() is {}", result.size());
        return result;
    }

//    private List<MoBasicInfoVo> getAllBatts(List<MoBasicInfoVo> batts, List<MoBasicInfoVo> loops)
    private List<MoBasicInfoVo> getAllBatts(List<MonitorObjectBean> batts)
    {
//        List<String> loopIds = loops.stream().map(MoBasicInfoVo::getId).distinct().collect(Collectors.toList());
//        Set<String> loopSet = new HashSet<>(loopIds);
        List<MoBasicInfoVo> currentBatts = batts.stream()
                .map(MoBasicInfoVo::new)
                .filter(batt -> !batteryAttributeUtils.getIsLoopByStr(batt.getExtendAttribute()))
                .collect(Collectors.toList());
        return currentBatts;
    }
}
