package com.zte.uedm.battery.a_interfaces.peakshift.web.converter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.StrategyCombinationEntity;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.StrategyCombinationVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:7d114c8b5d36437a98ac8c18c6e24007 */
@Mapper
public interface StrategyCombinationVoConverter {
    StrategyCombinationVoConverter INSTANCE = Mappers.getMapper(StrategyCombinationVoConverter.class);

    @Mappings({})
    StrategyCombinationVo strategyCombinationEntityToVo(StrategyCombinationEntity strategyCombinationEntity);

    List<StrategyCombinationVo> listStrategyCombinationEntityToVo(List<StrategyCombinationEntity> strategyCombinationEntities);

    @Mappings({})
    StrategyCombinationEntity strategyCombinationVoToEntity(StrategyCombinationVo strategyCombinationVo);

    @Mappings({})
    List<StrategyCombinationEntity> listStrategyCombinationVoToEntity(List<StrategyCombinationVo> strategyCombinationVos);
}
/* Ended by AICoder, pid:7d114c8b5d36437a98ac8c18c6e24007 */
