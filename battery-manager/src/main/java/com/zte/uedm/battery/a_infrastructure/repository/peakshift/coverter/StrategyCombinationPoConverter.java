package com.zte.uedm.battery.a_infrastructure.repository.peakshift.coverter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.StrategyCombinationEntity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.StrategyCombinationPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:c926eab1c8184ed19abc44c89d7fd3e2 */
@Mapper
public interface StrategyCombinationPoConverter {
    StrategyCombinationPoConverter INSTANCE = Mappers.getMapper(StrategyCombinationPoConverter.class);

    @Mappings({})
    StrategyCombinationEntity strategyCombinationPoToEntity(StrategyCombinationPo strategyCombinationPo);

    List<StrategyCombinationEntity> listStrategyCombinationPoToEntity(List<StrategyCombinationPo> strategyCombinationPoList);

    @Mappings({})
    StrategyCombinationPo strategyCombinationEntityToPo(StrategyCombinationEntity strategyCombinationEntity);

    @Mappings({})
    List<StrategyCombinationPo> listStrategyCombinationEntityToPo(List<StrategyCombinationEntity> strategyCombinationEntityList);
}
/* Ended by AICoder, pid:c926eab1c8184ed19abc44c89d7fd3e2 */