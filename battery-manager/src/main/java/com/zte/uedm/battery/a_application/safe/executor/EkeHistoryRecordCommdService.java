package com.zte.uedm.battery.a_application.safe.executor;

import com.zte.uedm.battery.a_interfaces.safe.web.dto.BatteryEkeyExportDto;
import com.zte.uedm.common.exception.UedmException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

public interface EkeHistoryRecordCommdService {
    /**
     *  电子钥匙导入
     * @param fis
     * @param request
     * @return
     */
    boolean importEkey(InputStream fis, HttpServletRequest request) throws Exception;

    /**
     * 电子钥匙导出
     * @param request
     * @param response
     */
    void exportEkey(HttpServletRequest request, HttpServletResponse response, BatteryEkeyExportDto dto) throws Exception;

    /**
     * 电子钥匙查询
     * @return
     */
    String exportQuery();

    /**
     *   电子钥匙重新生成
     * @param encryptEkey
     * @param request
     * @throws UedmException
     */
    void changeEkey(String encryptEkey, String sha256, HttpServletRequest request) throws Exception;

    /**
     *   电子钥匙初始化
     * @throws UedmException
     */
    void initEkey() throws UedmException;
}
