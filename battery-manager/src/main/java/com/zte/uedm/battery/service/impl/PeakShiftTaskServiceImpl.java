package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.bean.PeakShiftBatteryDataBean;
import com.zte.uedm.battery.bean.PeakShiftBatteryDetailDataBean;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.battery.bean.alarm.AlarmSchBean;
import com.zte.uedm.battery.mapper.PeakShiftMapper;
import com.zte.uedm.battery.service.AlarmService;
import com.zte.uedm.battery.service.PeakShiftDetailRptService;
import com.zte.uedm.battery.service.PeakShiftService;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class PeakShiftTaskServiceImpl
{
    @Autowired
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Autowired
    private PeakShiftDetailRptService peakShiftDetailRptService;

    //设置定时统计任务
    public void setPeakShiftDetailTask()
    {
        Runnable setPeakShiftDetailTask = new SetPeakShiftDetailTask(peakShiftConfigBaseServiceImpl,
                peakShiftDetailRptService);
        //启动300秒后执行一次

        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.SECOND, 300);
        log.info("---PeakShiftTaskServiceImpl setPeakShiftDetailTask, ca.getTime()---is {}", ca.getTime());
        ThreadPoolTaskScheduler threadPoolTaskScheduler1 = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler1.initialize();
        threadPoolTaskScheduler1.setThreadNamePrefix("temp-setPeakShiftDetailTask");
        threadPoolTaskScheduler1.schedule(setPeakShiftDetailTask, ca.getTime());

        //每天三点半执行一次
        log.info("---PeakShiftTaskServiceImpl setPeakShiftDetailTask, set setPeakShiftDetailTask time");
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.initialize();
        threadPoolTaskScheduler1.setThreadNamePrefix("setPeakShiftDetailTask");
        threadPoolTaskScheduler.schedule(setPeakShiftDetailTask, new CronTrigger("0 30 0 * * ?"));
    }

    public static class SetPeakShiftDetailTask implements Runnable
    {
        private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;
        private PeakShiftDetailRptService peakShiftDetailRptService;

        public SetPeakShiftDetailTask(PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl,
                PeakShiftDetailRptService peakShiftDetailRptService)
        {
            this.peakShiftConfigBaseServiceImpl = peakShiftConfigBaseServiceImpl;
            this.peakShiftDetailRptService = peakShiftDetailRptService;
        }

        @SneakyThrows
        @Override
        public void run()
        {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            Calendar ca = Calendar.getInstance();
            ca.setTime(new Date());
            ca.add(Calendar.DATE, 1);
            String timeEnd = df.format(ca.getTime());
            ca.add(Calendar.DATE, -2);
            String timeStart = df.format(ca.getTime());

//            peakShiftConfigBaseServiceImpl.init();

            peakShiftDetailRptService.execute(timeStart, timeEnd);

            peakShiftDetailRptService.statistics(timeStart, timeEnd);

        }
    }
}
