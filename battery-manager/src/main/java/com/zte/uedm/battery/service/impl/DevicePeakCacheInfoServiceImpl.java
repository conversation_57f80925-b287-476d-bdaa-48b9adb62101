/* Started by AICoder, pid:t755bk2bbfc01b9146e808b95008d00f7101064b */
package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.a_domain.factory.PeakShiftFactory;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.bean.MocSiteRelateBean;
import com.zte.uedm.battery.bean.peak.DeviceLatestPeakShiftTaskVo;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.rpc.impl.MpServiceRpcImpl2;
import com.zte.uedm.battery.service.BackupPowerThresholdDetailService;
import com.zte.uedm.battery.service.DevicePeakCacheInfoService;
import com.zte.uedm.battery.service.PeakShiftTaskService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.DeviceUtils;
import com.zte.uedm.common.bean.PeakShiftGain;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceExecStatusEnum;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceStatusEnum;
import com.zte.uedm.common.consts.RedisConstants;
import com.zte.uedm.common.consts.originalpoint.ZTEEMLOriginalPointConstat;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.NumberHandleUtils;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.GlobalOptional;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import com.zte.uedm.service.mp.api.adapter.dto.CollectorAndAdapterPointDto;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_CAPACITY_RATE;
import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_RATED_CAPACITY;

@Service
@Slf4j
/****
 *
 * <AUTHOR>
 *
 */
public class DevicePeakCacheInfoServiceImpl implements DevicePeakCacheInfoService {

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MpServiceRpcImpl2 mpServiceRpcImpl2;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PeakShiftTaskService peakShiftTaskService;

    @Resource
    private StandardDataService standardDataService;

    @Resource
    private AdapterPointDataService adapterPointDataService;

    @Autowired
    private PeakShiftDeviceServiceImpl peakShiftDeviceService;

    @Autowired
    private BackupPowerThresholdDetailService backupPowerThresholdDetailService;

    private List<DevicePeakCacheInfoBean> infoList = Collections.synchronizedList(new ArrayList<>());

    private Map<String, DevicePeakCacheInfoBean> infoMapByDeviceId =  new ConcurrentHashMap<>();

    private List<DevicePeakCacheInfoBean> infoListBak = Collections.synchronizedList(new ArrayList<>());

    private Map<String, DevicePeakCacheInfoBean> infoMapByDeviceIdBak = new ConcurrentHashMap<>();

    @Autowired
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Autowired
    private CollectorCacheManager collectorCacheManager;

    @Autowired
    private MocCacheManager mocCacheManager;

    @Autowired
    private FieldCacheManager fieldCacheManager;

    @Autowired
    private AdapterCacheManager adapterCacheManager;

    @Autowired
    private PeakShiftFactory peakShiftFactory;

    @Value("${peak.cache-time}")
    private Integer peakCacheTime;

    @Value("${peak.min-thread-size}")
    private Integer minThreadSize;

    @Value("${peak.max-thread-size}")
    private Integer maxThreadSize;

    @Value("${peak.threshold-size}")
    private Integer thresholdSize;

    //执行状态和当前策略未提供测点，暂时不做
    //执行状态原始测点
    private static final String EXEC_STATUS = "name";

    //当前策略原始测点
    private static final  String CURR_STRATEGY = "value";

    //判断是否支持错峰原始测点的module id
    private static final String MODULE_ID = "ZXDUPA_BCUA(V1.0)_V1.00.00.01";

    //判断是否支持错峰原始测点，测点存在则支持，反之，不支持
    private static final String ENABLE_PEAK = "330194";

    //运行状态原始测点
    private static final String RUNNING_STATUS = "330194";

    //bcua设备电池数量
    private static final Integer NUM=32;

    //链路状态为0表示链路已连接
    private static String LINK_CONNECT_STATUS = "0";



    @Override
    public void initCache()
    {
        log.info("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin============");

        try
        {
            //初始化临时信息变量
            infoListBak=Collections.synchronizedList(new ArrayList<>());
            infoMapByDeviceIdBak=new ConcurrentHashMap<>();

            List<String> deviceList=Collections.synchronizedList(new ArrayList<>());

            long t1 = System.currentTimeMillis();
            //获取所有Device并初始化infoList和infoMapByDeviceId，执行状态和当前策略未提供测点，暂时不做
            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getDevice============");
            getDevice(deviceList);
            long t2 = System.currentTimeMillis();
            log.info("initCache consume time 1 {}", t2 - t1);

            //获取设备对应站点信息
            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getDeviceSiteRelate============");
            getDeviceSiteRelate();
            long t3 = System.currentTimeMillis();
            log.info("initCache consume time 2 {}", t3 - t2);

            //设备路径信息
            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getDevicePathInfo============");
            // TODO 在getDevice设置路径
            getDevicePathInfo();
            long t4 = System.currentTimeMillis();
            log.info("initCache consume time 3 {}", t4 - t3);

            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getEnablePeakInfo============");
            //设备是否支持错峰信息
            getEnablePeakInfo();
            long t5 = System.currentTimeMillis();
            log.info("initCache consume time 4 {}", t5 - t4);

            //获取redis信息，包含累计电费收益、累计充电量、累计放电量、电池容量、电池执行状态、电池充放电状态
            //获取原始测点信息
            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getRedisAndOriginPointInfo============");
            getRedisAndOriginPointInfo(deviceList);
            long t6 = System.currentTimeMillis();
            log.info("initCache consume time 5 {}", t6 - t5);

            //不在站点下的设备
            Set<String> withoutSiteIds = infoListBak.stream().filter(bean -> StringUtils.isBlank(bean.getSiteId()))
                    .map(DevicePeakCacheInfoBean::getDeviceId).collect(Collectors.toSet());
            //将数据赋给real数据变量,排除未和站点关联的设备
            infoList = infoListBak.stream().filter(bean -> !withoutSiteIds.contains(bean.getDeviceId())).collect(Collectors.toList());

            /* Started by AICoder, pid:23972r898fc38ec149970935e00bc604b8845e72 */
            // 排除未和站点关联的设备
            infoMapByDeviceId = infoMapByDeviceIdBak.entrySet().stream()
                    .filter(entry -> !withoutSiteIds.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            /* Ended by AICoder, pid:23972r898fc38ec149970935e00bc604b8845e72 */

            //初始化临时信息变量
            infoListBak=new ArrayList<>();
            infoMapByDeviceIdBak=new HashMap<>();

            log.info("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info end, infoList size is {}, infoMapByDeviceId size is {}============"
                    ,infoList.size(),infoMapByDeviceId.size());
            log.debug("==============DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info end, infoList is {}============",infoList);
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
        }
    }

    public void getDevice(List<String> deviceList) throws Exception {
        // 获取有关联关系的采集器id
        Set<String> relationCollectorIds;
        try {
            relationCollectorIds = resourceCollectorRelationCacheManager.getAllRelations().stream()
                    .map(ResourceCollectorRelationEntity::getCollectorId)
                    .collect(Collectors.toSet());
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("DevicePeakCacheInfoServiceImpl getDevice get cache error", e);
            throw new UedmException(e.getErrorId(), e.getMessage());
        }

        // 获取采集器详情
        List<CollectorEntity> list;
        try {
            list = collectorCacheManager.selectCollectorByIds(relationCollectorIds);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("getDevice get collector cache error, param {}", relationCollectorIds, e);
            throw new UedmException(e.getErrorId(), e.getErrorDesc(), e.getErrorData());
        }

        // 查询各设备的最新策略的生效时间和失效时间
        List<DeviceLatestPeakShiftTaskVo> rpcLis = getDeviceLatestStrategyTaskInfo();
        log.debug("getDevice task info: {}", rpcLis);
        Map<String, DeviceLatestPeakShiftTaskVo> rpcMap = new ConcurrentHashMap<>();
        rpcLis.parallelStream().forEach(bean -> rpcMap.put(bean.getDeviceId(), bean));

        list.parallelStream().forEach(monitorDeviceViewBean -> {
            String deviceId = monitorDeviceViewBean.getId();
            String moc = monitorDeviceViewBean.getMoc();
            Object protocolAttribute = monitorDeviceViewBean.getProtocolAttribute();
            String parentId = monitorDeviceViewBean.getParentId();
            if (StringUtils.isBlank(parentId)) {
                parentId = GlobalOptional.GLOBAL_ROOT;
            }
            deviceList.add(deviceId);

            DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();

            // 获取设备类型
            String deviceType = DeviceUtils.parseDeviceType(moc, protocolAttribute);
            devicePeakCacheInfoBean.setDeviceType(deviceType);
            devicePeakCacheInfoBean.setModuleId(monitorDeviceViewBean.getAdapterId());
            devicePeakCacheInfoBean.setDeviceTypeName(moc);
            devicePeakCacheInfoBean.setDeviceId(deviceId);
            devicePeakCacheInfoBean.setDeviceName(monitorDeviceViewBean.getName());
            devicePeakCacheInfoBean.setParentId(parentId);

            devicePeakCacheInfoBean.setEnablePeak(false);

            DeviceLatestPeakShiftTaskVo shiftTaskVo = Optional.ofNullable(rpcMap.get(deviceId))
                    .orElse(new DeviceLatestPeakShiftTaskVo());
            devicePeakCacheInfoBean.setEffectiveDate(shiftTaskVo.getEffectiveDate());
            devicePeakCacheInfoBean.setExpirationDate(shiftTaskVo.getExpirationDate());

            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getUnknownId());
            devicePeakCacheInfoBean.setExecStatus(PeakShiftDeviceExecStatusEnum.getUnknownId());
            devicePeakCacheInfoBean.setCurrStrategy(PeakShiftDeviceExecStatusEnum.getUnknownId());

            infoMapByDeviceIdBak.put(deviceId, devicePeakCacheInfoBean);
            infoListBak.add(devicePeakCacheInfoBean);
        });
    }

    public void getDeviceSiteRelate() throws com.zte.uedm.basis.exception.UedmException {
        //获取所有站点
        Map<String, FieldEntity> siteMap = fieldCacheManager.selectByMoc(MocOptional.SITE.getId())
                .stream().collect(Collectors.toMap(FieldEntity::getId, bean -> bean, (oldBean, newBean) -> newBean));
        Set<String> siteIds = siteMap.keySet();

        // 站点采集器关联信息
        List<MocSiteRelateBean> siteRelateBeans = Collections.synchronizedList(new ArrayList<>());
        collectorCacheManager.getAllCollector()
                .parallelStream().forEach(bean->{
                    if (bean.getPathId() != null) {
                        for (String path : bean.getPathId()) {
                            if (siteIds.contains(path)) {
                                MocSiteRelateBean relateBean = new MocSiteRelateBean();
                                relateBean.setId(bean.getId());
                                relateBean.setName(bean.getName());
                                relateBean.setMoc(bean.getMoc());

                                FieldEntity site = siteMap.get(path);
                                relateBean.setSiteId(site.getId());
                                relateBean.setSiteName(site.getName());
                                siteRelateBeans.add(relateBean);
                            }
                        }
                    }

                });

        siteRelateBeans.parallelStream().forEach(mocSiteRelateBean -> {
            DevicePeakCacheInfoBean devicePeakCacheInfoBean = infoMapByDeviceIdBak.get(mocSiteRelateBean.getId());
            if (devicePeakCacheInfoBean != null) {
                devicePeakCacheInfoBean.setSiteId(mocSiteRelateBean.getSiteId());
                devicePeakCacheInfoBean.setSiteName(mocSiteRelateBean.getSiteName());
            }
        });
    }

    public void getDevicePathInfo(){
        List<CollectorEntity> collectorList = collectorCacheManager.getAllCollector();

        collectorList.parallelStream().forEach(collector -> {
            DevicePeakCacheInfoBean devicePeakCacheInfoBean = infoMapByDeviceIdBak.get(collector.getId());
            if (devicePeakCacheInfoBean != null) {
                devicePeakCacheInfoBean.setPosition(collector.getPathName());
            }
        });
    }

    public void getEnablePeakInfo() {
        try {
            // 获取采集器与测点的关联关系
            // 1.获取需要查询的采集器id
            Set<String> collectorIds = infoMapByDeviceIdBak.keySet();
            // 2.获取适配器与采集器的关联关系
            Map<String, String> relationAdaptionMap = collectorCacheManager.selectCollectorByIds(collectorIds)
                    .stream().filter(item -> StringUtils.isNotBlank(item.getAdapterId()))
                    .collect(Collectors.toMap(CollectorEntity::getId, CollectorEntity::getAdapterId));
            Set<String> adapterIds = new HashSet<>(org.apache.commons.collections4.CollectionUtils.emptyIfNull(relationAdaptionMap.values()));
            // 3.从适配器缓存中查找对应测点
            Map<String, AdapterEntity> adapterMap = adapterCacheManager.selectMapByIds(adapterIds);

            // 获取电池充电模式的原始测点值（六代T03协议通过电池充电模式测点判断是否支持错峰）
            Map<String, Map<String, AdapterPointDataVo>> mapCSU6 = getOmp(CommonConst.CSU6, ZTEEMLOriginalPointConstat.CSU6_BATTERY_CHARGE_MODE, new ArrayList<>(collectorIds));

            // 修改为使用并行流执行
            collectorIds.parallelStream()
                    .filter(collectorId -> infoMapByDeviceIdBak.get(collectorId) != null)
                    .filter(collectorId -> StringUtils.isNotBlank(relationAdaptionMap.get(collectorId)))
                    .forEach(collectorId -> {
                        DevicePeakCacheInfoBean devicePeakCacheInfoBean = infoMapByDeviceIdBak.get(collectorId);
                        devicePeakCacheInfoBean.setEnablePeak(false);

                        String adapterId = relationAdaptionMap.get(collectorId);

                        PeakShiftCommonService peakShiftService = peakShiftFactory.generateByDeviceType(devicePeakCacheInfoBean.getDeviceType());
                        if (peakShiftService != null) {

                            AdapterEntity adapterCacheBean = adapterMap.getOrDefault(adapterId, new AdapterEntity());
                            List<AdapterPointEntity> adapterPoints = adapterCacheBean.getAdapterPoints();

                            // 除T03协议外均通过查找是否 存在支持错峰的原始测点 判断是否支持错峰
                            List<String> points = Optional.ofNullable(adapterPoints).orElse(new ArrayList<>())
                                    .stream().map(AdapterPointEntity::getId).collect(Collectors.toList());

                            devicePeakCacheInfoBean.setEnablePeak(peakShiftService.isEnablePeak2(points, mapCSU6, devicePeakCacheInfoBean.getDeviceType(), devicePeakCacheInfoBean.getModuleId()));

                        }
                    });
        } catch (Exception e) {
            log.error("getDevicePathInfo exception!", e);
        }
    }

    public void getRedisAndOriginPointInfo(List<String> deviceList)
    {
        try{
            //获取设备对应电池或电池组（R321需要累加采集器关联的所有电池额定容量）
            Map<String, List<ResourceCollectorRelationEntity>> battOrBattPack = getBattOrBattPack(deviceList);

            // 电池或电池组的id
            List<String> battOrBattPackIds = battOrBattPack.values().stream().filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(bean -> bean != null && bean.getResourceId() != null)
                    .map(ResourceCollectorRelationEntity::getResourceId)
                    .distinct()
                    .collect(Collectors.toList());

            // 获取电池容量
            Map<String, Map<String, Object>> battCapacityMap = standardDataService.batchQueryByResourceIdAndStandPointId(
                    battOrBattPackIds, Arrays.asList(BATTERY_SMPID_RATED_CAPACITY,
                            BATTERYSET_SMPID_CAPACITY_RATE));

            //根据链路状态更新缓存值
            // 采集器和主链路的关联关系
            Map<String, String> deviceMap = new HashMap<>();
            try {
                collectorCacheManager.selectCollectorByIds(new HashSet<>(deviceList))
                        .stream().filter(collectorCacheBean -> collectorCacheBean.getLinkInfo() != null)
                        .forEach(x -> {
                            List<Map<String, Object>> linkInfo = (List<Map<String, Object>>) x.getLinkInfo();
                            Optional<Map<String, Object>> master = linkInfo.stream().filter(y -> y.containsValue("master")).findFirst();
                            if (master.isPresent()) {
                                Map<String, Object> stringObjectMap = master.get();
                                String linkId = stringObjectMap.keySet().iterator().next();
                                deviceMap.put(x.getId(), linkId);
                            }
                        });
            } catch (com.zte.uedm.basis.exception.UedmException e) {
                log.error("getDeviceOnlineName get collector cache error", e);
            }

            log.debug("DevicePeakCacheInfoServiceImpl,deviceMap is {}",deviceMap);
            //key:mainLinkId,value:status
            Map<String,String> linkStatusMap = peakShiftDeviceService.queryAllLinkMonitor();
            log.debug("DevicePeakCacheInfoServiceImpl,linkStatusMap is {}",linkStatusMap);

            //获取原始测点数据
            Map<String, Map<String, AdapterPointDataVo>> mapAdapterPointMap = getOmp(PeakDeviceTypeEnum.CSU5.id, ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE, deviceList);
            log.debug("mapCSU is {}",mapAdapterPointMap);
            Map<String, Map<String, AdapterPointDataVo>> mapBCUA = getOmp(CommonConst.BCUA, ZTEEMLOriginalPointConstat.PEAK_SHIFT_ENABLE, deviceList);
            log.debug("mapBCUA is {}",mapBCUA);
            mapAdapterPointMap.putAll(mapBCUA);
            Map<String, Map<String, AdapterPointDataVo>> mapSNMP = getOmp(CommonConst.SNMP, "", deviceList);
            log.debug("mapSNMP is {}",mapSNMP);
            mapAdapterPointMap.putAll(mapSNMP);
            Map<String, Map<String, AdapterPointDataVo>> mapCSU6 = getOmp(CommonConst.CSU6, ZTEEMLOriginalPointConstat.CSU6_BATTERY_CHARGE_MODE, deviceList);
            log.debug("mapCSU6 is {}", mapCSU6);
            mapAdapterPointMap.putAll(mapCSU6);

            //设备累计电费收益、累计充电量、累计放电量、电池容量,设备状态
            deviceList.parallelStream()
                    .filter(deviceId -> infoMapByDeviceIdBak.get(deviceId) != null)
                    .forEach(deviceId->{
                        DevicePeakCacheInfoBean devicePeakCacheInfoBean=infoMapByDeviceIdBak.get(deviceId);

                        PeakShiftCommonService peakShiftService = peakShiftFactory.generateByDeviceType(devicePeakCacheInfoBean.getDeviceType());
                        if (peakShiftService != null) {
                            try {
                                //获取设备运行状态
                                log.debug("============== DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getRedisAndOriginPointInfo, device running status============");
                                Map<String, AdapterPointDataVo> runningMap = mapAdapterPointMap.get(deviceId);
                                peakShiftService.setRunningStatus(devicePeakCacheInfoBean, deviceMap, linkStatusMap, runningMap);
                                log.debug("DevicePeakCacheInfoServiceImpl, device is {},running status value is {}", deviceId, devicePeakCacheInfoBean.getRunningStatus());

                                //获取电池容量,电池充放电状态和执行状态
                                log.debug("============== DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getRedisAndOriginPointInfo, batt capacity============");
                                List<ResourceCollectorRelationEntity> battOrBattPackList = battOrBattPack.getOrDefault(deviceId, new ArrayList<>());
                                String battCapacityPoint = peakShiftService.getBattCapacityPoint();
                                setBatteryCapacityAndBatteryStatusAndExecStatus(devicePeakCacheInfoBean, battOrBattPackList, battCapacityPoint, battCapacityMap);

                                //获取累计电费收益、累计充电量、累计放电量
                                log.debug("============== DevicePeakCacheInfoServiceImpl, init Device Peak Cache Info begin, getRedisAndOriginPointInfo, peakShiftGain============");
                                PeakShiftGain peakShiftGain = redisService.getCache(RedisConstants.PEAK_SHIFT_GAIN, deviceId);

                                peakShiftGain = peakShiftGain == null ? new PeakShiftGain() : peakShiftGain;

                                devicePeakCacheInfoBean.setTotalCharge(peakShiftGain.getTotalCharge());
                                devicePeakCacheInfoBean.setTotalDischarge(peakShiftGain.getTotalDischarge());
                                devicePeakCacheInfoBean.setElectricBenefit(peakShiftGain.getTotalElectricBenefit());
                            } catch (Exception e) {
                                log.error("DevicePeakCacheInfoServiceImpl getRedisAndOriginPointInfo error", e);
                            }
                        }
            });

        }catch (Exception e)
        {
            log.error("getRedisAndOriginPointInfo exception!",e);
        }
    }

    /**
     * 过滤出指定设备类型的采集器id，获取相应原始测点值
     * @param collectorType
     * @param ompId
     * @param deviceList
     * @return
     * @throws UedmException
     */
    public Map<String, Map<String, AdapterPointDataVo>> getOmp(String collectorType, String ompId, List<String> deviceList) throws UedmException {
        if (CollectionUtils.isEmpty(deviceList)) {
            return new HashMap<>();
        }
        // 过滤出指定设备类型的采集器id
        List<String> ids = deviceList.stream()
                .filter(id -> collectorType.equals(infoMapByDeviceIdBak.getOrDefault(id, new DevicePeakCacheInfoBean()).getDeviceType()))
                .collect(Collectors.toList());

        // 如果过滤后的ids为空，则直接返回空的HashMap
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        // SNMP不同协议版本，原始测点不一样
        if (CommonConst.SNMP.equals(collectorType)) {
            return dealSNMPOmp(ids);
        }
        return getOmp(ids, ompId);
    }
    public Map<String, List<ResourceCollectorRelationEntity>> getBattOrBattPack(List<String> collectorIds) throws com.zte.uedm.basis.exception.UedmException {
        // 过滤SNMP设备
        Set<String> snmpList = collectorIds.stream()
                .filter(id -> CommonConst.SNMP.equals(infoMapByDeviceId.getOrDefault(id, new DevicePeakCacheInfoBean()).getDeviceType()))
                .collect(Collectors.toSet());

        // 获取SNMP与电池的关联关系
        Map<String, List<ResourceCollectorRelationEntity>> collectorRelatedDeviceMap = resourceCollectorRelationCacheManager.getCollectorRelatedDevice(snmpList, MocOptional.BATTERY.getId());

        // 获取非SNMP设备
        Set<String> nonSnmpList = collectorIds.stream()
                .filter(id -> !snmpList.contains(id)).collect(Collectors.toSet());

        // 获取非SNMP设备与电池组的关联关系
        Map<String, List<ResourceCollectorRelationEntity>> nonSnmpCollectorRelatedDeviceMap = resourceCollectorRelationCacheManager.getCollectorRelatedDevice(nonSnmpList, MocOptional.BATTERY_SET.getId());

        // 合并两个Map
        collectorRelatedDeviceMap.putAll(nonSnmpCollectorRelatedDeviceMap);

        return collectorRelatedDeviceMap;
    }

    /**
     * SNMP不同版本，原始测点不同，需要分别获取原始测点值
     * @param ids
     * @return
     * @throws UedmException
     */
    public Map<String, Map<String, AdapterPointDataVo>> dealSNMPOmp(List<String> ids) throws UedmException {
        // 支持SNMP V1、V2、V3
        Map<String, String> originPointSnmpEnablePeakMap = CommonConst.ORIGIN_POINT_SNMP_ENABLE_PEAK_MAP;
        Set<String> protocolIds = originPointSnmpEnablePeakMap.keySet();
        Map<String, List<String>> protocolMap = new HashMap<>();

        // 获取采集器的协议id
        try {
            List<CollectorEntity> cacheBeanList = collectorCacheManager.selectCollectorByIds(new HashSet<>(ids));
            if (cacheBeanList == null || cacheBeanList.isEmpty()) {
                log.error("Collector cache list is empty or null");
                return new HashMap<>();
            }

            // 按照不同协议分类
            for (CollectorEntity collectorCacheBean : cacheBeanList) {
                String protocolId = collectorCacheBean.getProtocolId();
                if (protocolIds.contains(protocolId)) {
                    protocolMap.computeIfAbsent(protocolId, k -> new ArrayList<>()).add(collectorCacheBean.getId());
                }
            }
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("getOmp get collector cache error", e);
            throw new UedmException(-1, "get collector cache error!");
        }

        Map<String, Map<String, AdapterPointDataVo>> result = new HashMap<>();
        // 分别获取原始测点值
        for (Map.Entry<String, List<String>> entry : protocolMap.entrySet()) {
            String protocolId = entry.getKey();
            List<String> idList = entry.getValue();
            if (!idList.isEmpty()) {
                Map<String, Map<String, AdapterPointDataVo>> ompMap = getOmp(idList, originPointSnmpEnablePeakMap.get(protocolId));
                result.putAll(ompMap);
            }
        }

        //25.01版本开始，SNMP的原始测点由{设备测点}-{电池编号}-{字典版本}-V{SNMP版本号}修改为{设备测点}-{电池编号}
        //若330079测点有值，将原先的测点值覆盖
        Map<String, Map<String, AdapterPointDataVo>> enablePointMap = getOmp(ids, CommonConst.SNMP_ENABLE_POINT);
        result.putAll(enablePointMap);
        return result;
    }

    public Map<String, Map<String, AdapterPointDataVo>> getOmp(List<String> deviceList, String ompId) throws UedmException {
        Map<String, Map<String, AdapterPointDataVo>> resultMap = new HashMap<>();

        // 构造获取原始测点的参数
        List<String> ompIds = Collections.singletonList(ompId);
        List<CollectorAndAdapterPointDto> ompParam = deviceList.stream()
                .map(collectorId -> {
                    CollectorAndAdapterPointDto adapterPointDto = new CollectorAndAdapterPointDto();
                    adapterPointDto.setCollectorId(collectorId);
                    adapterPointDto.setAdapterPointIds(ompIds);
                    return adapterPointDto;
                })
                .collect(Collectors.toList());

        // 从mp获取数据
        Map<String, Map<String, Map<String, AdapterPointDataVo>>> collectorValueMap = null;
        try {
            collectorValueMap = adapterPointDataService.getByCollectorIdAndAdapterId(ompParam);
        } catch (Exception e) {
            log.error("getOmpIndexSetList get data from mp error", e);
            throw new UedmException(-1, "get data error!");
        }
        // 数据转换
        for (Map.Entry<String, Map<String, Map<String, AdapterPointDataVo>>> entry : MapUtils.emptyIfNull(collectorValueMap).entrySet()) {
            String collectorId = entry.getKey();
            Map<String, Map<String, AdapterPointDataVo>> pointMap = entry.getValue();
            if (pointMap == null || pointMap.get(ompId) == null) {
                continue;
            }
            Map<String, AdapterPointDataVo> valueMap = pointMap.get(ompId);
            resultMap.put(collectorId, valueMap);
        }
        return resultMap;
    }
    /* Ended by AICoder, pid:t755bk2bbfc01b9146e808b95008d00f7101064b */

    private void setBatteryCapacityAndBatteryStatusAndExecStatus(DevicePeakCacheInfoBean devicePeakCacheInfoBean, List<ResourceCollectorRelationEntity> battOrBattPackList, String pointId,
                                                                 Map<String, Map<String, Object>> battCapacityMap ) throws UedmException {
        double battCapacity = 0D;

        for (ResourceCollectorRelationEntity deviceMonitorBean : battOrBattPackList) {
            String id = deviceMonitorBean.getResourceId();

            Map<String, Object> standPointMap = battCapacityMap.getOrDefault(id, new HashMap<>());
            Object valueObj = standPointMap.get(pointId);
            if (valueObj == null) {
                continue;
            }

            Map<String, String> pointMap = jsonService.jsonToObject(jsonService.objectToJson(valueObj), Map.class, String.class, String.class);
            String value = pointMap.get(CURR_STRATEGY);

            if (NumberHandleUtils.isNum(value)) {
                Double v = Double.parseDouble(value);
                battCapacity = battCapacity + v;
            }
        }
        devicePeakCacheInfoBean.setBattCapacity(getString(battCapacity));
    }
    /* Ended by AICoder, pid:t9dea2554cwcbf914251098fa0429c174e683dcf */

    private List<DeviceLatestPeakShiftTaskVo> getDeviceLatestStrategyTaskInfo() throws Exception {
        List<DeviceLatestPeakShiftTaskVo> peakShiftTaskPoLis = peakShiftTaskService.getDeviceLatestStrategyTaskInfo();
        return peakShiftTaskPoLis;
    }

    private String getString(Object value){
        return value==null?null:String.format("%.2f",value);
    }

    @Override
    public String getRunningStatusName(String id,String language){
        return i18nUtils.getMapFieldByLanguageOption(PeakShiftDeviceStatusEnum.getNameById(id),language);
    }

    @Override
    public List<DevicePeakCacheInfoBean> getAllList() throws UedmException {
        return jsonService.jsonToObject(jsonService.objectToJson(infoList),List.class,DevicePeakCacheInfoBean.class);
    }

    @Override
    public Map<String, DevicePeakCacheInfoBean> getAllMap() throws UedmException {
        return jsonService.jsonToObject(jsonService.objectToJson(infoMapByDeviceId),Map.class, String.class,
                DevicePeakCacheInfoBean.class);
    }

    @Override
    public DevicePeakCacheInfoBean getInfoByDeviceId(String deviceId) throws UedmException {
        return jsonService.jsonToObject(jsonService.objectToJson(infoMapByDeviceId.get(deviceId)),DevicePeakCacheInfoBean.class);
    }

    //清空缓存数据
    @Override
    public void clearCacheData(){
        log.info("==============DevicePeakCacheInfoServiceImpl, clear Device Peak Cache Info begin============");

        try
        {
            //清空缓存数据
            infoList.clear();
            infoMapByDeviceId.clear();

            infoListBak.clear();
            infoMapByDeviceIdBak.clear();
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
        }

        log.info("==============DevicePeakCacheInfoServiceImpl, clear Device Peak Cache Info end============");
    }
}



