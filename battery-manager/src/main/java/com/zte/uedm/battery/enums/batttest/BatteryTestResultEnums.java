package com.zte.uedm.battery.enums.batttest;

import com.zte.uedm.battery.controller.backuppowerconfig.enums.BackupPowerOverviewDimEnums;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * @FileDesc : 测试结果枚举
 * <AUTHOR> 00253634
 * @date Date : 2023年03月15日 下午5:00
 * @Version : 1.0
 */
public enum BatteryTestResultEnums
{
    NO("no", "0", "{\"zh_CN\":\"无\",\"en_US\":\"No\"}", 1),
    NORMAL("normal", "1", "{\"zh_CN\":\"正常\",\"en_US\":\"Normal\"}", 2),
    FAULT ("fault", "2", "{\"zh_CN\":\"异常\",\"en_US\":\"Fault\"}", 3);
    
    private String id;
    private String value;
    private String name;
    private Integer sequence;

    BatteryTestResultEnums(String id, String value, String name, Integer sequence)
    {
        this.id = id;
        this.value = value;
        this.name = name;
        this.sequence = sequence;
    }

    public String getId()
    {
        return this.id;
    }

    public String getName()
    {
        return this.name;
    }

    public String getValue()
    {
        return this.value;
    }

    public Integer getSequence()
    {
        return this.sequence;
    }

    public static List<String> getAllIds() {
        List<String> allIds = new ArrayList<>();
        BatteryTestResultEnums[] arr = BatteryTestResultEnums.values();
        for (BatteryTestResultEnums enu : arr) {
            if (StringUtils.isNotBlank(enu.getId())) {
                allIds.add(enu.getId());
            }
        }
        return allIds;
    }

    /**
     * 按顺序获取所有名称
     * @return
     */
    public static List<Pair<String,String>> getAllIdName()
    {
        BatteryTestResultEnums[] values = BatteryTestResultEnums.values();
        List<Pair<String,String>> pairList = new ArrayList<>();
        List<BatteryTestResultEnums> battTestTypeEnums = Arrays.asList(values);
        battTestTypeEnums.sort(Comparator.comparingInt(BatteryTestResultEnums::getSequence));
        for(BatteryTestResultEnums value : battTestTypeEnums)
        {
            pairList.add(Pair.of(value.getId(),value.getName()));
        }
        return pairList;
    }

    /**
     * 根据维度id获取名字
     * @param id
     * @return
     */
    public static String getNameById(String id)
    {
        BatteryTestResultEnums[] values = BatteryTestResultEnums.values();
        for(BatteryTestResultEnums value : values)
        {
            if(value.getId().equals(id))
            {
                return value.getName();
            }
        }
        return "";
    }

}
