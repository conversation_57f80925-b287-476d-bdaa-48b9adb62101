package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.PeakShiftParamVo;
import com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

public interface PeakShiftIssuedTaskDomain
{
    PeakShiftParamVo queryCsuDeviceParam(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 通过任务id查询策略模板的历史快照
     * @param taskId
     * @return
     * @throws UedmException
     */
    TemplateStrategyDetailBo getHistoryStrategyByTaskId(String taskId) throws UedmException;

    PeakShiftParamVo queryCsu6DeviceParam(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;
}
