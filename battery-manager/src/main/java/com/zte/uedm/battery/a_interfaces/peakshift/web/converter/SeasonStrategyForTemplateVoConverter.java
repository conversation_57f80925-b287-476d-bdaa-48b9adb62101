package com.zte.uedm.battery.a_interfaces.peakshift.web.converter;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.SeasonStrategyForTemplateEntity;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.SeasonStrategyForTemplateVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:acbbb9ded0f04af8b0be48e9d013f137 */
@Mapper
public interface SeasonStrategyForTemplateVoConverter {
    SeasonStrategyForTemplateVoConverter INSTANCE = Mappers.getMapper(SeasonStrategyForTemplateVoConverter.class);

    @Mappings({})
    SeasonStrategyForTemplateVo seasonStrategyForTemplateEntityToSeasonStrategyForTemplateVo(SeasonStrategyForTemplateEntity seasonStrategyForTemplateEntity);

    List<SeasonStrategyForTemplateVo> listSeasonStrategyForTemplateEntityToSeasonStrategyForTemplateVo(List<SeasonStrategyForTemplateEntity> entityList);

    @Mappings({})
    SeasonStrategyForTemplateEntity seasonStrategyForTemplateVoToSeasonStrategyForTemplateEntity(SeasonStrategyForTemplateVo seasonStrategyForTemplateVo);

    @Mappings({})
    List<SeasonStrategyForTemplateEntity> listSeasonStrategyForTemplateVoToSeasonStrategyForTemplateEntity(List<SeasonStrategyForTemplateVo> voList);
}
/* Ended by AICoder, pid:acbbb9ded0f04af8b0be48e9d013f137 */
