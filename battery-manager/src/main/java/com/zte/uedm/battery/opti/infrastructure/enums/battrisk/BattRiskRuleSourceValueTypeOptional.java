package com.zte.uedm.battery.opti.infrastructure.enums.battrisk;

import java.util.ArrayList;
import java.util.List;

public class BattRiskRuleSourceValueTypeOptional
{
    public static final List<BattRiskRuleSourceValueTypeOptional> instances = new ArrayList<>();

    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_NUMBER_A = new BattRiskRuleSourceValueTypeOptional("numberA","{\"en_US\":\"Number A\",\"zh_CN\":\"模拟量\"}");
    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_NUMBER_D = new BattRiskRuleSourceValueTypeOptional("numberD","{\"en_US\":\"Number D\",\"zh_CN\":\"数字量\"}");
    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_STRING = new BattRiskRuleSourceValueTypeOptional("string","{\"en_US\":\"String\",\"zh_CN\":\"字符串\"}");
    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_DATE_STRING = new BattRiskRuleSourceValueTypeOptional("dateString","{\"en_US\":\"Data string\",\"zh_CN\":\"日期字符串\"}");
    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_TIME_STRING = new BattRiskRuleSourceValueTypeOptional("timeString","{\"en_US\":\"Data string\",\"zh_CN\":\"时间字符串\"}");

    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_EXIST_D = new BattRiskRuleSourceValueTypeOptional("existD","{\"en_US\":\"Exist D\",\"zh_CN\":\"存在枚举\"}");
    public static final BattRiskRuleSourceValueTypeOptional SOURCE_TYPE_DURATION = new BattRiskRuleSourceValueTypeOptional("duration","{\"en_US\":\"Duration\",\"zh_CN\":\"持续时长\"}");

    private String id;
    private String name;

    public BattRiskRuleSourceValueTypeOptional(){}
    private BattRiskRuleSourceValueTypeOptional(String id, String name)
    {
        this.id = id;
        this.name = name;

        instances.add(this);
    }

    public static BattRiskRuleSourceValueTypeOptional valueOf(String id)
    {
        for(BattRiskRuleSourceValueTypeOptional valueType : instances)
        {
            if(valueType.id.equalsIgnoreCase(id))
            {
                return valueType;
            }
        }
        return null;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(obj != null && obj instanceof BattRiskRuleSourceValueTypeOptional)
        {
            BattRiskRuleSourceValueTypeOptional sourceValueType = (BattRiskRuleSourceValueTypeOptional) obj;
            return this.id.equalsIgnoreCase(sourceValueType.id);
        }
        return false;
    }
    /* Started by AICoder, pid:3872380ec2k014814c28082d30d5d80316c44560 */
    @Override
    public int hashCode() {
        return this.id.hashCode();
    }
    /* Ended by AICoder, pid:3872380ec2k014814c28082d30d5d80316c44560 */

    @Override
    public String toString()
    {
        return "{ id: " + this.id + ", name: " +this.name + " }";
    }
}
