package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 为模板策略而建的季节策略Bean
 * 包括scope_strategy, season_strategy，interval_strategy各个策略表
 */

@Getter
@Setter
@ToString
public class StrategyCombinationVo
{
    /**
     * 电网策略名称
     */
    private String scopeStrategyName;

    /**
     * 季节策略ID
     */
    private String seasonStrategyId;

    /**
     * 季节策略Name,电网名称+生效日期
     */
    private String seasonStrategyName;

    /**
     * 生效开始时间
     */
    private String effectiveTime;

    /**
     * 生效结束时间
     */
    private String expirationTime;

    /**
     * 模式,这里的模式排除节假日模式，只看普通周期的模式
     * 0：日模式 1：周模式 2：月模式
     */
    private Integer mode;

    /**
     * 状态
     * 0：待生效 1：生效中 2：已结束
     */
    private Integer status;

}
