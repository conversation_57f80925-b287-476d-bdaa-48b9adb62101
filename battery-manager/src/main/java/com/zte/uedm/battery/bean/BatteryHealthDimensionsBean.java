package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2023年03月01日 上午10:14
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
public class BatteryHealthDimensionsBean
{
    private String id;
    private String name;
    private String userName;
    private String unit;
    private Integer sequence ;
    private Boolean enable ;
    private Integer defaultIndex;
    private Boolean defaultEnable ;
    private Boolean defaultFixed ;
    private String creator;
    private Date gmtCreate;
    private String updater;
    private Date gmtModified;
    private Boolean sortable;
    private Boolean assetAttributeShow = true;
}
