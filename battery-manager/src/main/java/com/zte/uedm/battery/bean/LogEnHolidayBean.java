package com.zte.uedm.battery.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class LogEnHolidayBean
{
    @JsonProperty("Period Setting")
    private List<LogEnTimeIntervalBean> timeIntervalBeanList;

    @JsonProperty("Daily Template")
    private List<LogEnIntervalDetailBean> detailBeanList;
}
