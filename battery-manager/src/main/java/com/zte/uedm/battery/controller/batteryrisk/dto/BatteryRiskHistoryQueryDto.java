package com.zte.uedm.battery.controller.batteryrisk.dto;

import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.consts.TimeFormatConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Getter
@Setter
@ToString
@Slf4j
public class BatteryRiskHistoryQueryDto {
    /**
     * 电池id
     */
    private String batteryId;

    /**
     * 评估时间起 "yyyy-mm-dd"
     */
    private String evaluateTimeStart;

    /**
     * 评估时间止 "yyyy-mm-dd"
     */
    private String evaluateTimeEnd;


    /**
     * 两者有值,校验时间
     */
    public boolean checkTime()
    {
        if (null!=evaluateTimeStart && null!=evaluateTimeEnd)
        {
            int i = evaluateTimeStart.compareTo(evaluateTimeEnd);
            if (i >= 0) {
                //开始时间大于等于结束时间
                return false;
            }
        }
        //开始时间小于结束时间
        return true;
    }

    /**
     * 计算评估天数
     */
    public int getEvalDay(){
        if (!StringUtils.isEmpty(evaluateTimeStart) && !StringUtils.isEmpty(evaluateTimeEnd)) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateTypeConst.DATE_FORMAT_1);
            LocalDateTime timeStart = LocalDateTime.parse(evaluateTimeStart, dateTimeFormatter);
            LocalDateTime timeEnd = LocalDateTime.parse(evaluateTimeEnd, dateTimeFormatter);
            if(timeStart ==null||timeEnd==null){return -1;}
            return (int)Duration.between(timeStart, timeEnd).toDays();
        }
        return -1;
    }


}
