package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.PeakShiftCsuAllStrategyBean;
import com.zte.uedm.battery.bean.PeakShiftCsu5StrategyDetailBean;
import com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyDetailPo;
import com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo;
import com.zte.uedm.battery.domain.PeakShiftStrategyDomain;
import com.zte.uedm.battery.mapper.PeakShiftMapper;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PeakShiftStrategyDomainImpl implements PeakShiftStrategyDomain
{
    @Autowired
    private PeakShiftMapper peakShiftMapper;

    @Override
    public List<PeakShiftCsuAllStrategyBean> selectStrategyDetailByDeviceAndTimeRange(String deviceId, String startTime, String endTime) throws UedmException
    {
        if (StringUtils.isAnyBlank(deviceId, startTime, endTime))
        {
            log.error("selectStrategyDetailByDeviceAndTimeRange input param is error!");
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "param is must input!");
        }
        List<PeakShiftCsu5StrategyPo> peakShiftCsu5StrategyPos = peakShiftMapper.selectCsuAllStrategyByDeviceId(deviceId);
        //判断时间是否有交集
        List<PeakShiftCsuAllStrategyBean> peakShiftAllStrategyBos = peakShiftCsu5StrategyPos.stream().filter(bean -> endTime.compareTo(bean.getStartTime()) >= 0 && startTime.compareTo(bean.getEndTime())<=0)
                .map(bean -> {
                    PeakShiftCsuAllStrategyBean bo = new PeakShiftCsuAllStrategyBean();
                    BeanUtils.copyProperties(bean, bo);
                    List<PeakShiftCsu5StrategyDetailPo> detailPos= peakShiftMapper.selectCsuStrategyDetailByDeviceId(bean.getId());
                    List<PeakShiftCsu5StrategyDetailBean> peakShiftCsu5StrategyDetailBos = detailPos.stream().map(detailPo -> {
                        PeakShiftCsu5StrategyDetailBean peakShiftCsu5StrategyDetailBo = new PeakShiftCsu5StrategyDetailBean();
                        BeanUtils.copyProperties(detailPo, peakShiftCsu5StrategyDetailBo);
                        return peakShiftCsu5StrategyDetailBo;
                    }).collect(Collectors.toList());
                    bo.setPeakShiftCsu5StrategyDetailBos(peakShiftCsu5StrategyDetailBos);
                    return bo;
                }).collect(Collectors.toList());

        return peakShiftAllStrategyBos;
    }
}
