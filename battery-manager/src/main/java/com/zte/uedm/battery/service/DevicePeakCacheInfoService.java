/* Started by AICoder, pid:ufb1a97eacq20411421b089790186256b0c64c79 */
package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.common.exception.UedmException;
import java.util.List;
import java.util.Map;

/**
 * 设备峰值缓存信息服务接口
 */
public interface DevicePeakCacheInfoService {

    /**
     * 初始化缓存
     */
    void initCache();

    /**
     * 清空缓存
     */
    void clearCacheData();

    /**
     * 获取运行状态的国际化名称
     *
     * @param id       状态ID
     * @param language 语言
     * @return 国际化名称
     */
    String getRunningStatusName(String id, String language);

    /**
     * 获取所有设备信息列表
     *
     * @return 设备信息列表
     * @throws UedmException 异常信息
     */
    List<DevicePeakCacheInfoBean> getAllList() throws UedmException;

    /**
     * 获取所有设备信息映射表
     *
     * @return 设备信息映射表
     * @throws UedmException 异常信息
     */
    Map<String, DevicePeakCacheInfoBean> getAllMap() throws UedmException;

    /**
     * 根据设备ID获取设备信息
     *
     * @param deviceId 设备ID
     * @return 设备信息
     * @throws UedmException 异常信息
     */
    DevicePeakCacheInfoBean getInfoByDeviceId(String deviceId) throws UedmException;
}
/* Ended by AICoder, pid:ufb1a97eacq20411421b089790186256b0c64c79 */