package com.zte.uedm.battery.controller;

import com.github.pagehelper.PageInfo;
import com.zte.log.bean.OperationMethodEnum;
import com.zte.log.bean.UedmOperationLogger;
import com.zte.log.filter.UserThreadLocal;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.ScopeStrategyQueryBean;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.ErrorDescConstants;
import com.zte.uedm.battery.service.GridStrategyService;
import com.zte.uedm.battery.service.TemplateStrategyService;
import com.zte.uedm.battery.util.PageUtil;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.IsKafkaBean;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("device-template-strategy-old")
@Component
//@Api(value = "device-template-strategy")
@Api(value = "设备模块策略")
@Slf4j
public class TemplateStrategyController
{

    @Autowired
    private TemplateStrategyService templateStrategyService;

    @Autowired
    private GridStrategyService gridStrategyService;

    private static final String SUCCESS = "success";

    @POST
    @Path("/add")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "新增设备模板策略", notes = "新增设备模板策略", httpMethod = HttpMethod.POST)
    @UedmOperationLogger(method = OperationMethodEnum.ADD, module = "module_name_template_strategy",operation = "template_strategy_insert")
    @ApiResponses({
            @ApiResponse(code=-100,message="参数为空"),@ApiResponse(code=-202,message="模板数量过多"),
            @ApiResponse(code=-203, message="模式为错误格式"),@ApiResponse(code=-204,message="无效参数"),
            @ApiResponse(code=-205,message="各个策略的时段不能超过4组"), @ApiResponse(code=-206,message="节假日时段不超过20组"),
            @ApiResponse(code=-208,message="时间范围重复"),@ApiResponse(code= -301,message ="时间范围为空"),
            @ApiResponse(code=-302,message="名称重复")
    })
    public ResponseBean insert(TemplateStrategyAddDto strategyBaseDto, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] /device-template-strategy/add strategyBaseDto={}", strategyBaseDto);

            if (strategyBaseDto == null)
            {
                log.error("TemplateStrategyController -> addTemplateStrategy parameter is error");
                throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ErrorDescConstants.PARAMETER_IS_BLANK);
            }
            ValidationResult result = ValidationUtils.validateForAdd(strategyBaseDto);
            if (result.isHasErrors())
            {
                log.error("TemplateStrategyController -> strategyBaseDto parameter is has errors = {}", result);
                throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ErrorDescConstants.PARAMETER_IS_BLANK);
            }
            templateStrategyService.addTemplateStrategy(strategyBaseDto, Tools.getUserName(request));
            UserThreadLocal.setLogDetail(strategyBaseDto);
            return ResponseBeanUtils.getNormalResponseBean(0, SUCCESS, 1);
        }
        catch (UedmException e)
        {
            log.error("TemplateStrategyController insert occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @POST
    @Path("/edit")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "编辑设备模板策略", notes = "编辑设备模板策略", httpMethod = HttpMethod.POST)
    @UedmOperationLogger(method = OperationMethodEnum.UPDATE, module = "module_name_template_strategy",operation = "template_strategy_update")
    @ApiResponses({
            @ApiResponse(code=-100,message="参数为空"),@ApiResponse(code=-202,message="模板数量过多"),
            @ApiResponse(code=-203, message="模式为错误格式"),@ApiResponse(code=-204,message="无效参数"),
            @ApiResponse(code=-205,message="各个策略的时段不能超过4组"), @ApiResponse(code=-206,message="节假日时段不超过20组"),
            @ApiResponse(code=-208,message="时间范围重复"),@ApiResponse(code= -301,message ="时间范围为空"),
            @ApiResponse(code=-302,message="名称重复")
    })
    public ResponseBean edit(TemplateStrategyEditDto templateStrategyEditDto, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] /device-template-strategy/edit  templateStrategyDto={}", templateStrategyEditDto);
            if (templateStrategyEditDto == null)
            {
                log.error("TemplateStrategyController -> editTemplateStrategy parameter is error");
                throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ErrorDescConstants.PARAMETER_IS_BLANK);
            }
            ValidationResult result = ValidationUtils.validateForAdd(templateStrategyEditDto);
            if (result.isHasErrors())
            {
                log.error("TemplateStrategyController -> editTemplateStrategy parameter is has errors = {}", result);
                throw new UedmException(ParameterExceptionEnum.BLANK.getCode(), ErrorDescConstants.PARAMETER_IS_BLANK);
            }
            templateStrategyService.editTemplateStrategy(templateStrategyEditDto, Tools.getUserName(request));
            UserThreadLocal.setLogDetail(templateStrategyEditDto);
            return ResponseBeanUtils.getNormalResponseBean(0, SUCCESS, 1);
        }
        catch (UedmException e)
        {
            log.error("TemplateStrategyController edit occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @POST
    @Path("/select-by-condition")
    @Produces({ MediaType.APPLICATION_JSON })
//    @ApiOperation(value = "select-by-condition", notes = "select-by-condition", response = ResponseBean.class, httpMethod = HttpMethod.POST)
    @ApiOperation(value = "获取末班策略", notes = "获取末班策略", response = ResponseBean.class, httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code=-200,message="名称过长")})
    public ResponseBean selectStrategyByCondition(TemplateQueryDto queryBean, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] /device-template-strategy/select-by-condition queryBean={}", queryBean);
            PageInfo<TemplateStrategyBo> pageInfo = templateStrategyService.searchByConditions(queryBean,
                    languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, pageInfo.getList(),
                    Long.valueOf(pageInfo.getTotal()).intValue());
        }
        catch (UedmException e)
        {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @POST
    @Path("/check-name")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "重名校验e", notes = "重名校验", response = ResponseBean.class, httpMethod = HttpMethod.POST)

    public ResponseBean checkName(ScopeStrategyQueryBean queryBean)
    {
        try
        {
            //ScopeStrategyQueryBean这个bean只用到id和name，其余字段没有使用
            log.info("[IN] /device-template-strategy/check-name queryBean={}", queryBean);
            Boolean result = templateStrategyService.checkNameUnique(queryBean.getId(), queryBean.getName());
            return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        }
        catch (UedmException e)
        {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @GET
    @Path("/one-season-strategy")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "queryOneSeasonStrategyFotTemplate", notes = "查询单个策略的详情和名称", response = ResponseBean.class, httpMethod = HttpMethod.GET)
    public ResponseBean queryOneSeasonStrategyFotTemplate(@QueryParam("seasonStrategyId") String seasonStrategyId,
            @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] seasonStrategyId:{}", seasonStrategyId);
            SeasonStrategyForTemplateVo result = gridStrategyService.queryOneSeasonStrategyFotTemplate(seasonStrategyId,
                    languageOption);
            return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        }
        catch (UedmException e)
        {
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @GET
    @Path("/detail")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "详情查询策略", notes = "详情查询策略", httpMethod = HttpMethod.GET)
    public ResponseBean detail(@QueryParam("id") String id, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption)
    {
        log.info("[IN] /device-template-strategy/detail id={}", id);
        if (StringUtils.isBlank(id))
        {
            return ResponseBeanUtils.getNormalResponseBean(-200, "id is not empty", 0);
        }
        try
        {
            TemplateStrategyDetailBo detail = templateStrategyService.selectDetailByTemplateId(id,
                    Tools.getUserName(request), languageOption);
            if (detail != null)
            {
                return ResponseBeanUtils.getNormalResponseBean(0, detail, 1);
            }
            return ResponseBeanUtils.getNormalResponseBean(-200, "query is empty", 0);
        }
        catch (UedmException e)
        {
            log.error("PeakShiftController detail occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @GET
    @Path("/detail-history")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "历史快照查询", notes = "历史快照查询", httpMethod = HttpMethod.GET)
    public ResponseBean historyDetail(@QueryParam("taskId") String taskId,
            @HeaderParam("language-option") String languageOption)
    {
        log.info("[IN] /device-template-strategy/detail-history taskId={}", taskId);
        if (StringUtils.isBlank(taskId))
        {
            return ResponseBeanUtils.getNormalResponseBean(-200, "taskId is not empty", 0);
        }
        try
        {
            IssuedHistoryBo result = templateStrategyService.selectHistory(taskId);
            if (result != null)
            {
                return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
            }
            return ResponseBeanUtils.getNormalResponseBean(-200, "query is empty", 0);
        }
        catch (UedmException e)
        {
            log.error("PeakShiftController detail occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
    }

    @GET
    @Path("/select-season-strategy")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "queryAllSeasonStrategy", notes = "查询所有待生效和已生效的策略", response = ResponseBean.class, httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = -1, message = "查询失败")
    })
    public ResponseBean queryAllSeasonStrategy(@QueryParam("sortBy") String sortBy, @QueryParam("order") String order,
            @QueryParam("pageSize") Integer pageSize, @QueryParam("pageNo") Integer pageNo,
            @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] sortBy:{}, order:{}", sortBy, order);
            List<StrategyCombinationVo> allBeans = gridStrategyService.getStrategyCombination(sortBy, order,languageOption);
            List<StrategyCombinationVo> result = PageUtil.getPageList(allBeans, pageNo, pageSize);
            return ResponseBeanUtils.getNormalResponseBean(0, result, allBeans.size());
        }
        catch (UedmException e)
        {
            log.error("queryAllSeasonStrategy failed!", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @DELETE
    @Path("/delete")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "删除设备模板策略", notes = "删除设备模板策略", httpMethod = HttpMethod.DELETE,tags = {"模块内/错峰/设备错略模板"})
    @ApiResponses({
            @ApiResponse(code=-1,message="发生内部错误"),
            @ApiResponse(code=-200,message="传参为空")
    })
    public ResponseBean delete(List<String> ids, @Context HttpServletRequest request,
            @HeaderParam("language-option") String languageOption)
    {
        try
        {
            log.info("[IN] /device-template-strategy/delete ids={}", ids);
            //设置用户名与IP
            OperlogBean operlogBean = new OperlogBean(Tools.getUserName(request), Tools.getRemoteHost(request));
            IsKafkaBean isKafkaBean = new IsKafkaBean();
            int count = templateStrategyService.deleteTemplateStrategy(ids, operlogBean, isKafkaBean);
            return ResponseBeanUtils.getNormalResponseBean(0, SUCCESS, count);
        }
        catch (UedmException e)
        {
            log.error("TemplateStrategyController delete occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

}