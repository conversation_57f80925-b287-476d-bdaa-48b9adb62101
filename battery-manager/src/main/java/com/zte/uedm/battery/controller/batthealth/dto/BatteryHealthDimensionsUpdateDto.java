package com.zte.uedm.battery.controller.batthealth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2023年03月02日 下午4:24
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
@ApiModel(value = "用户维度总览")
public class BatteryHealthDimensionsUpdateDto
{   @ApiModelProperty(value = "标识")
    private String id;
    @ApiModelProperty(value = "维度")
    private Integer sequence;
    @ApiModelProperty(value = "设定开关")
    private Boolean enable;
}
