package com.zte.uedm.battery.domain;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattTestTaskDeviceBean;
import com.zte.uedm.battery.bean.pojo.BattTestTaskDevicesPo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

/**
 * 电池测试任务设备
 */
public interface BattTestTaskDeviceDomain
{
    /**
     * 查询测试任务设备
     * @return
     * @throws UedmException
     */
    List<BattTestTaskDeviceBean> selectTaskByIds(List<String> taskIds) throws UedmException;

    Integer updateTestDeviceInfo(List<BattTestTaskDeviceBean> list) throws UedmException;

    /**
     * 根据任务id查询电池测试任务设备表
     * @param taskId
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<BattTestTaskDevicesPo> selectByTaskId(String taskId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 获取所有设备id列表
     * @return
     */
    List<String> selectAllDevicesIds() throws UedmException;

    /**
     * 获取所有设备
     * @return
     * @throws UedmException
     */
    List<BattTestTaskDevicesPo> selectAll() throws UedmException;
}
