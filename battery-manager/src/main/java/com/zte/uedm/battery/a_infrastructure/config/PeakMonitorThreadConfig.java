package com.zte.uedm.battery.a_infrastructure.config;


import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/* Started by AICoder, pid:dabf2o5f2amb52e1417f0844103f45264666138c */
@Component
@Getter
@Slf4j
public class PeakMonitorThreadConfig {

    private static final int KEEP_ALIVE_TIME = 60;
    private static final int QUEUE_CAPACITY = 5; // 慎改

    @Bean(name = "peakMonitorThreadExecutor")
    public ExecutorService createThreadPoolInstance() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("peak-monitor-pool-%d").build();
        int numberOfThreads = Runtime.getRuntime().availableProcessors();
        int coreSize = Math.max(1, numberOfThreads - 2);
        log.info("createThreadPoolInstance coreSize = {}", coreSize);

        return new ThreadPoolExecutor(
                coreSize,
                numberOfThreads * 2,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(QUEUE_CAPACITY),
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
/* Ended by AICoder, pid:dabf2o5f2amb52e1417f0844103f45264666138c */
