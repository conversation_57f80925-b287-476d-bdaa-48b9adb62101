package com.zte.uedm.battery.opti.domain.utils.function.bean;

import com.zte.uedm.battery.opti.domain.service.bean.RiskCalulateData;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 时间范围数据转换
 */
@Getter
@Setter
@ToString
public class RiskTimeRangeOriginalData
{
    private String id;

    private String value;

    private Date time;

    public RiskTimeRangeOriginalData()
    {

    }

    public RiskTimeRangeOriginalData(String id, RiskCalulateData riskCalulateData)
    {
        this.id = id;
        this.value = riskCalulateData.getValue();
        this.time = riskCalulateData.getTime();
    }
}
