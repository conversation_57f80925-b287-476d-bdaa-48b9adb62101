package com.zte.uedm.battery.a_domain.service.peakshift.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.OriginalDataBean;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailCsu5Entity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.TemplateStrategyRepository;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.SouthApplySetStatusDataBean;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskWithDevicePo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.SeasonStrategyForTemplateVo;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.domain.PeakShiftIssuedTaskDomain;
import com.zte.uedm.battery.enums.peak.BattStatusEnum;
import com.zte.uedm.battery.enums.peak.PeakShiftingStrategyEnum;
import com.zte.uedm.battery.service.PeakShiftService;
import com.zte.uedm.battery.service.PeakShiftTaskSchduleJobService;
import com.zte.uedm.battery.service.impl.DevicePeakCacheInfoServiceImpl;
import com.zte.uedm.battery.util.DeviceUtils;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.SouthExtraDataBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceExecStatusEnum;
import com.zte.uedm.common.configuration.enums.PeakShiftDeviceStatusEnum;
import com.zte.uedm.common.consts.TimeFormatConstants;
import com.zte.uedm.common.consts.originalpoint.ZTEEMLOriginalPointConstat;
import com.zte.uedm.common.enums.asset.BatteryTypeEnums;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.JsonUtil;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_CAPACITY_RATE;
import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_HEALTH;
import static com.zte.uedm.battery.service.impl.AutoPeakShiftStrategyServiceImpl.AUTOSTRATEGY;

@Component("PEAK_SHIFT_CSU5")
@Slf4j
public class PeakShiftCSU5ServiceImpl extends PeakShiftCommonService {

    @Autowired
    private TemplateStrategyRepository templateStrategyRepository;
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Resource
    private DeviceCacheManager deviceCacheManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MsgSenderService msgSenderService;

    @Autowired
    private StandardDataService standardDataService;

    @Autowired
    private PeakShiftService peakShiftService;

    @Autowired
    private DevicePeakCacheInfoServiceImpl devicePeakCacheInfoService;

    @Autowired
    private PeakShiftTaskSchduleJobService peakShiftTaskSchduleJobService;

    @Autowired
    private PeakShiftIssuedTaskDomain peakShiftIssuedTaskDomain;

    @Setter
    private long timeout = 300L;

    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    private static final String PEAK_SHIFT_STRATEGY_MODULE_ZH = "智能错峰-错峰模版";
    private static final String PEAK_SHIFT_STRATEGY_MODULE_EN = "Peakshift-Strategy";

    private static final String PEAK_SHIFT_STRATEGY_OPERATION_ZH = "生成自动错峰策略";
    private static final String PEAK_SHIFT_STRATEGY_OPERATION_EN = "Product Peakshift Strategy";

    private static String name = null;

    private static final String RATE_CAPACITY = "rated_capacity";

    public static final String SPECIAL_PEAK_MODULE_ID = "ZXDT22_SF01(V3.07)_V3.07.34.00T03";
    public static final String VERSION_T03_CHARGE_MODE = "2";
    // 链路状态为0表示链路已连接
    private static String LINK_CONNECT_STATUS = "0";

    /* Started by AICoder, pid:c5bb85bae0w66401453f09e3601bbc338548e1fb */


    @Override
    public void setPeakStartStopRecord(SouthApplySetStatusDataBean southDataBean) {}

    @Override
    public void processingDeviceData(SouthApplySetStatusDataBean southDataBean) throws InterruptedException, com.zte.uedm.common.exception.UedmException {
        String deviceId = southDataBean.getMdId();
        String logId = southDataBean.getLogId();
        Integer status = southDataBean.getStatus();

        String taskId = !logId.contains("|") ? "" : logId.substring(logId.indexOf("|") + 1);
        if (status != null && status == 0) {
            // csu5下发成功
            log.info("onMsg setCsu5ParamSuccess!");
            TimeUnit.SECONDS.sleep(timeout);
            peakShiftTaskSchduleJobService.setCsu5ParamSuccess(taskId, deviceId);
        } else {
            // csu5下发失败
            log.info("onMsg setCsu5ParamFail!");
            peakShiftTaskSchduleJobService.setCsu5ParamFail(taskId, deviceId);
        }
    }

    /* Started by AICoder, pid:lbb21rf823ab0ea14c6d0bb2802a6730ad93a01a */
    @Override
    public TemplateStrategyDetailEntity getTemplateDetail(TemplateStrategyDetailEntity templateStrategyDetailBo) throws UedmException {
        // 根据ID查询Csu5设备模板详情列表
        List<TemplateStrategyDetailCsu5Entity> details = templateStrategyRepository.selectCsu5DetailByTemplateId(templateStrategyDetailBo.getId());
        List<TemplateDetailBaseDto> detailList = new ArrayList<>();
        List<TemplateHolidayDto> holidayList = new ArrayList<>();
        for (TemplateStrategyDetailCsu5Entity po : details) {
            TemplateTimeGranVo timeRange = new TemplateTimeGranVo();
            timeRange.setBegin(po.getBeginDate());
            timeRange.setEnd(po.getEndDate());
            timeRange.setRemark(null);
            if (StringUtils.isNotBlank(po.getRemark())) {
                timeRange.setRemark(po.getRemark());
            }
            List<TemplateTimeGranDetailVo> detail = JsonUtils.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class);
            if (po.getHolidayFlag()) {
                /* 节假日模式 */
                TemplateHolidayDto holidayVo = new TemplateHolidayDto();
                holidayVo.setTimeRange(timeRange);
                holidayVo.setDetail(detail);
                holidayList.add(holidayVo);
            } else {
                /* 周期详情且不是空模板 */
                TemplateDetailBaseDto detailVo = new TemplateDetailBaseDto();
                detailVo.setTimeRange(timeRange);
                detailVo.setDetail(detail);
                detailList.add(detailVo);
            }
        }
        templateStrategyDetailBo.setDetail(detailList);
        templateStrategyDetailBo.setHoliday(holidayList);
        return templateStrategyDetailBo;
    }
    /* Ended by AICoder, pid:lbb21rf823ab0ea14c6d0bb2802a6730ad93a01a */
    /* Ended by AICoder, pid:c5bb85bae0w66401453f09e3601bbc338548e1fb */

    /**
     * 计算充电时长 =E/E*C, 向上取整 电池额定容量和 (E) 充电系数（C）
     * @param batteryByCollectIdAndFilterLoop 所有电池信息（非电池回路）
     * @param collectId  数据采集器ID
     * @param batteryType 电池类型
     * @param coefficientPoint 充电系数测点
     * @param defaultCoefficient 默认值
     * @return
     * @throws com.zte.uedm.common.exception.UedmException
     */
    private BigDecimal calculateChargingDurationForBatteryType(Map<String, List<String>> batteryByCollectIdAndFilterLoop, String collectId, BatteryTypeEnums batteryType, String coefficientPoint, BigDecimal defaultCoefficient) throws com.zte.uedm.common.exception.UedmException, UedmException {
        log.info("Starting calculateChargingDurationForBatteryType with collectId: {}, batteryType: {}", collectId, batteryType);

        List<String> batteryIds = batteryByCollectIdAndFilterLoop.get(batteryType.getId());
        log.info("Retrieved batteryIds for {}: {}", batteryType, JSON.toJSONString(batteryIds));
        if(CollectionUtils.isEmpty(batteryIds)){
            return BigDecimal.ZERO;
        }
        BigDecimal allBatteryCapacity = BigDecimal.ZERO;
        BigDecimal battCap = BigDecimal.ZERO;
        for (String batteryId : batteryIds) {
            DeviceEntity deviceById = deviceCacheManager.getDeviceById(batteryId);
            Map<String,Object> tributemap = JsonUtils.jsonToObject(JsonUtils.objectToJson(deviceById.getExattribute()), Map.class);
            if(!tributemap.containsKey(RATE_CAPACITY) || tributemap.get(RATE_CAPACITY) == null){
                continue;
            }
            battCap = new BigDecimal(tributemap.get(RATE_CAPACITY).toString());
            allBatteryCapacity = allBatteryCapacity.add(battCap);
        }

        log.info("Calculated total battery capacity for {}: {}", batteryType, allBatteryCapacity);

        if (allBatteryCapacity.compareTo(BigDecimal.ZERO) == 0) {
            log.info("Total battery capacity for {} is zero, returning zero charging duration", batteryType);
            return BigDecimal.ZERO;
        }

        List<OriginalDataBean> originalDataEntities = getOriginalValueByDeviceId(collectId, coefficientPoint);
        BigDecimal coefficient = originalDataEntities.isEmpty() ? defaultCoefficient : new BigDecimal(originalDataEntities.get(0).getValue());
        log.info("Using charging coefficient for {}: {}", batteryType, coefficient);

        BigDecimal chargingDuration = allBatteryCapacity.divide(allBatteryCapacity.multiply(coefficient), 2, RoundingMode.HALF_UP);
        log.info("Calculated charging duration for {}: {}", batteryType, chargingDuration);

        return chargingDuration;
    }

    /**
     * 获取充电时长和放电时长
     * @param devicePeakCacheInfoBean 错峰采集器信息
     * @return 充电时长
     */
    @Override
    public Map<String, BigDecimal> calculateChargeDischargeDuration(DevicePeakCacheInfoBean devicePeakCacheInfoBean) throws UedmException {
        log.info("Starting calculateChargeDischargeDuration with devicePeakCacheInfoBean: {}", JSON.toJSONString(devicePeakCacheInfoBean));

        // 获取电池信息
        Map<String, List<String>> batteryByCollectIdAndFilterLoop = getBatteryByCollectIdAndFilterLoop(devicePeakCacheInfoBean.getDeviceId());
        log.info("Retrieved batteryByCollectIdAndFilterLoop: {}", JSON.toJSONString(batteryByCollectIdAndFilterLoop));

        HashMap<String, BigDecimal> chargeDischargeDuration = new HashMap<>();

        // 计算充电时长
        BigDecimal chargingDuration = null;
        try {
            chargingDuration = calculateChargingDuration(batteryByCollectIdAndFilterLoop, devicePeakCacheInfoBean.getDeviceId());
        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.warn("calculateChargingDuration error: {}", e.getMessage(), e);
        }
        log.info("Calculated chargingDuration: {}", chargingDuration);

        // 计算放电时长
        BigDecimal disChargingDuration = null;
        disChargingDuration = calculateDisChargingDuration(batteryByCollectIdAndFilterLoop, devicePeakCacheInfoBean.getDeviceId());
        log.info("Calculated disChargingDuration: {}", disChargingDuration);


        chargeDischargeDuration.put(PeakShiftConstants.CHARGE_TIME, chargingDuration);
        chargeDischargeDuration.put(PeakShiftConstants.DIS_CHARGE_TIME, disChargingDuration);
        log.info("Constructed chargeDischargeDuration map: {}", JSON.toJSONString(chargeDischargeDuration));

        return chargeDischargeDuration;
    }

    /**
     * 铅酸、铁锂、未知类型电池分别计算放电时长取最小
     * @param batteryByCollectIdAndFilterLoop
     * @param collectId
     * @return
     */
    /* Started by AICoder, pid:8984a50860ycfd3145d008aee08dd8190a294942 */
    private BigDecimal calculateDisChargingDuration(Map<String, List<String>> batteryByCollectIdAndFilterLoop, String collectId) throws UedmException {
        log.info("Starting calculateDischargingDuration for collectId: {}", collectId);

        // 计算铅酸电池、锂电池和未知类型电池的放电时长
        BigDecimal vrlaDischargingDuration = calculateDischargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.vrla);
        log.info("Calculated VRLA dischargingDuration: {}", vrlaDischargingDuration);

        BigDecimal liDischargingDuration = calculateDischargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.li_ion);
        log.info("Calculated Li-ion dischargingDuration: {}", liDischargingDuration);

        BigDecimal unknownDischargingDuration = calculateDischargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.unknown);
        log.info("Calculated Unknown dischargingDuration: {}", unknownDischargingDuration);

        // 返回最小放电时长
        return Stream.of(vrlaDischargingDuration, liDischargingDuration, unknownDischargingDuration)
                .filter(duration -> duration.compareTo(BigDecimal.ZERO) > 0)
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }
    /* Ended by AICoder, pid:8984a50860ycfd3145d008aee08dd8190a294942 */

    /**
     * 铅酸、铁锂、未知类型电池分别计算放电时长取最大
     * @param batteryByCollectIdAndFilterLoop
     * @param collectId
     * @return
     * @throws com.zte.uedm.common.exception.UedmException
     */
    /* Started by AICoder, pid:hbb8el53646ef9114ff10b46d0a8291458b96f76 */
    private BigDecimal calculateChargingDuration(Map<String, List<String>> batteryByCollectIdAndFilterLoop, String collectId) throws com.zte.uedm.common.exception.UedmException, UedmException {
        log.info("Starting calculateChargingDuration for collectId: {}", collectId);

        // 计算铅酸电池、锂电池和未知类型电池的充电时长
        BigDecimal vrlaChargingDuration = calculateChargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.vrla, PeakShiftConstants.CSU5_LOW_POWER_CHARGING_COEFFICIENT_POINT_VRLA, BigDecimal.valueOf(0.15));
        log.info("Calculated VRLA chargingDuration: {}", vrlaChargingDuration);

        BigDecimal liChargingDuration = calculateChargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.li_ion,  PeakShiftConstants.CSU5_LOW_POWER_CHARGING_COEFFICIENT_POINT_LI_ION, BigDecimal.valueOf(0.32));
        log.info("Calculated Li-ion chargingDuration: {}", liChargingDuration);

        BigDecimal unKnownChargingDuration = calculateChargingDurationForBatteryType(batteryByCollectIdAndFilterLoop, collectId, BatteryTypeEnums.unknown,  null, BigDecimal.valueOf(0.15));
        log.info("Calculated Unknown chargingDuration: {}", unKnownChargingDuration);

        // 返回最大充电时长
        return Stream.of(vrlaChargingDuration, liChargingDuration, unKnownChargingDuration)
                .filter(duration -> duration.compareTo(BigDecimal.ZERO) > 0)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }
    /* Ended by AICoder, pid:hbb8el53646ef9114ff10b46d0a8291458b96f76 */

    /**
     * 计算放电时长 =E/I, 向上取整 电池额定容量和 (E) 所有电池七天平均放电电流的和（I）
     * 预测可放电时长T11 = E/I ，向上取整(容量平均值/电流平均值)
     * 错峰放电时长T13= 预测可放电时长和设计备电时长取小的值，向上取整
     * @param batteryByCollectIdAndFilterLoop   所有电池信息（非电池回路）
     * @param collectId    数据采集器ID
     * @param batteryType  电池类型
     * @return
     */
    /* Started by AICoder, pid:66315m090f17e4614c84090900f41c566016b671 */
    private BigDecimal calculateDischargingDurationForBatteryType(Map<String, List<String>> batteryByCollectIdAndFilterLoop, String collectId, BatteryTypeEnums batteryType) throws UedmException {
        log.info("Starting calculateDischargingDuration with collectId: {} and batteryType: {}", collectId, batteryType);

        // 获取指定类型的电池ID列表
        List<String> batteryIds = batteryByCollectIdAndFilterLoop.getOrDefault(batteryType.getId(), Collections.emptyList());
        log.info("Retrieved batteryIds for {}: {}", batteryType, batteryIds);

        if (batteryIds.isEmpty()) {
            log.info("No batteries found for type: {}", batteryType);
            return BigDecimal.ZERO;
        }

        BigDecimal disCurrWeekSum = BigDecimal.ZERO;
        BigDecimal allBatteryCapacity = BigDecimal.ZERO;
        BigDecimal battCap = BigDecimal.ZERO;
        // 查询电池七天平均放电电流
        Map<String, BigDecimal> avgCurrMap = queryDisCurrWeek(batteryIds);


        for (String battId : batteryIds) {
            DeviceEntity deviceById = deviceCacheManager.getDeviceById(battId);
            Map<String,Object> tributemap = JsonUtils.jsonToObject(JsonUtils.objectToJson(deviceById.getExattribute()), Map.class);
            if(!tributemap.containsKey(RATE_CAPACITY)){
                continue;
            }
            battCap = new BigDecimal(tributemap.get(RATE_CAPACITY).toString());
            BigDecimal battSoh = calculateStdSum(battId, BATTERY_SMPID_HEALTH);


            BigDecimal avgDisChargingCurr = avgCurrMap.get(battId);

            log.debug("BCUA -- calculateDischargeTime -- battCap:{} avgDisChargingCurr: {}", battCap, avgDisChargingCurr);

            if (battCap.compareTo(BigDecimal.ZERO) == 0 || avgDisChargingCurr == null || avgDisChargingCurr.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            disCurrWeekSum = disCurrWeekSum.add(avgDisChargingCurr);
            allBatteryCapacity = allBatteryCapacity.add(battCap.multiply(battSoh != null ? battSoh : BigDecimal.ONE));
        }
        allBatteryCapacity= allBatteryCapacity.divide(BigDecimal.valueOf(100));
        log.info("BCUA -- calculateDischargeTime -- allBatteryCapacity:{} disCurrWeekSum: {}", allBatteryCapacity, disCurrWeekSum);

        if (disCurrWeekSum.compareTo(BigDecimal.ZERO) == 0 || allBatteryCapacity.compareTo(BigDecimal.ZERO) == 0) {
            log.info("Discharging current or total battery capacity is zero, returning zero discharging duration");
            return BigDecimal.ZERO;
        }

        // 预测可放电时长 T11
        BigDecimal disChargingDurationT11 = allBatteryCapacity.divide(disCurrWeekSum, 2, RoundingMode.HALF_UP);
        log.info("Calculated discharging duration T11: {}", disChargingDurationT11);

        // 获取设计备电时长
        String dcPowerid = getDcPowerid(collectId);
        log.info("Retrieved dcPowerid: {}", dcPowerid);
        BigDecimal threshold = queryThreshold(dcPowerid);
        log.info("Retrieved threshold data: {}", threshold);

        // 错峰放电时长 T13 = 预测可放电时长和设计备电时长取小的值
        BigDecimal result = disChargingDurationT11.min(threshold);
        log.info("Calculated final discharging duration: {}", result);

        return result;
    }

    /* Ended by AICoder, pid:66315m090f17e4614c84090900f41c566016b671 */

    /* Started by AICoder, pid:90595904cetae5e14b1e0996207698586fb855e0 */
    @Override
    public com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto processStrategy(SeasonStrategyForTemplateVo strategy, DevicePeakCacheInfoBean devicePeakCacheInfoBean, BigDecimal dischargeDuration, BigDecimal chargeDuration) {
        Calendar cal = Calendar.getInstance();
        String today = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        String name = devicePeakCacheInfoBean.getDeviceName();
        List<TemplateDetailVo> detail = strategy.getDetail();
        com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto templateStrategyAddDto = new TemplateStrategyAddDto();
        templateStrategyAddDto.setSeasonStrategyId(strategy.getSeasonStrategyId());
        templateStrategyAddDto.setDeviceType(devicePeakCacheInfoBean.getDeviceType());
        String deviceId = devicePeakCacheInfoBean.getDeviceId();
        String deviceNewId = deviceId.substring(deviceId.lastIndexOf("-") + 1);
        templateStrategyAddDto.setName(devicePeakCacheInfoBean.getDeviceName() + "-" + deviceNewId + "-" + AUTOSTRATEGY);
        templateStrategyAddDto.setMode("3");
        templateStrategyAddDto.setSource("web");
        String year = today.substring(0, 4);

        List<TemplateTimeGranDetailVo> voDetails = detail.stream()
                .flatMap(templateDetailVo -> templateDetailVo.getDetail().stream())
                .collect(Collectors.toList());

        if (extracted(voDetails, name)) return null;

        List<TemplateDetailBaseDto> templateDetailBaseDtoList = new ArrayList<>();
        for (TemplateDetailVo templateDetailVo : detail) {
            TemplateDetailBaseDto templateDetailBaseDto = new TemplateDetailBaseDto();
            List<TemplateTimeGranDetailVo> voDetail = templateDetailVo.getDetail();
            // 校验总时长是否超过24小时
            BigDecimal totalDuration = chargeDuration.add(dischargeDuration);
            if (totalDuration.compareTo(new BigDecimal("24")) > 0) {
                dischargeDuration = new BigDecimal("24").subtract(chargeDuration);
            }

            processDetail(voDetail, dischargeDuration);

            voDetail = getTemplateTimeGranDetailVos(voDetail);

            if (!checkInitialIntervals(voDetail)) { return null; }

            voDetail.sort(Comparator.comparing(vo -> {
                // 将字符串时间转换为LocalTime
                return LocalTime.parse(vo.getBeginTime(), DateTimeFormatter.ofPattern("H:mm"));
            }));

            templateDetailBaseDto.setDetail(voDetail);
            templateDetailBaseDto.setTimeGran(templateDetailVo.getTimeGran());

            TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
            templateTimeGranVo.setBegin(year + "-" + templateDetailVo.getDateRange().get(0));
            templateTimeGranVo.setEnd(year + "-" + templateDetailVo.getDateRange().get(1));

            templateDetailBaseDto.setTimeRange(templateTimeGranVo);
            templateDetailBaseDtoList.add(templateDetailBaseDto);
        }

        List<TemplateTimeGranDetailVo> newVoDetails = templateDetailBaseDtoList.stream()
                .flatMap(templateDetailVo -> templateDetailVo.getDetail().stream())
                .collect(Collectors.toList());

        if (extracted(newVoDetails, name)) return null;

        templateStrategyAddDto.setDetail(templateDetailBaseDtoList);
        templateStrategyAddDto.setWeekendFlag(true);
        templateStrategyAddDto.setHoliday(new ArrayList<>());

        return templateStrategyAddDto;
    }

    @Override
    public boolean isEnablePeak(List<String> points) {
        return points != null && points.contains(ZTEEMLOriginalPointConstat.WEEKDAY_PEAK_ENABLE);
    }

    @Override
    public boolean isEnablePeak2(List<String> points, Map<String, Map<String, AdapterPointDataVo>> map, String deviceType, String moduleId) {
        return isEnablePeak(points);
    }

    @Override
    public void setRunningStatus(DevicePeakCacheInfoBean devicePeakCacheInfoBean, Map<String, String> deviceMap, Map<String, String> linkStatusMap, Map<String, AdapterPointDataVo> runningMap) throws Exception {
        Map<String, String> indexMapLink = new LinkedHashMap<>();
        devicePeakCacheInfoBean.setRunningStatusMap(indexMapLink);
        log.debug("deviceId is {},get all Map is {}", devicePeakCacheInfoBean.getDeviceId(), runningMap);
        if (runningMap != null && runningMap.size() > 0) {
            for (Map.Entry<String, AdapterPointDataVo> keyMap : runningMap.entrySet()) {
                AdapterPointDataVo kvMap = JsonUtil.stringToObject(JsonUtil.objectToString(keyMap.getValue()),
                        AdapterPointDataVo.class);
                if (!ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE.equals(kvMap.getAdapterPointId())) {
                    continue;
                }
                setRunningStatus(devicePeakCacheInfoBean, kvMap, deviceMap, linkStatusMap);
            }
        }else {
            log.debug("map is null");
            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getUnknownId());
        }
    }

    public void setRunningStatus(DevicePeakCacheInfoBean devicePeakCacheInfoBean, AdapterPointDataVo kvMap,
                                 Map<String, String> deviceMap, Map<String, String> linkStatusMap) throws Exception {
        String mainLinkId = deviceMap.get(devicePeakCacheInfoBean.getDeviceId());
        String linkedStatus = linkStatusMap.get(mainLinkId);
        if (null == linkedStatus || !linkedStatus.equals(CommonConst.LINK_CONNECT_STATUS)) {
            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getUnknownId());
        } else if ((ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE_TIMING).equals(kvMap.getValue())) {
            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getEnabledId());
        } else if ((ZTEEMLOriginalPointConstat.PEAK_SHIFT_DISABLE_VALUE).equals(kvMap.getValue()) || (ZTEEMLOriginalPointConstat.PEAK_SHIFT_ENABLE_ENABLE_VALUE).equals(kvMap.getValue())) {
            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getDisabledId());
        } else {
            devicePeakCacheInfoBean.setRunningStatus(PeakShiftDeviceStatusEnum.getUnknownId());
        }
    }

    @Override
    public List<RemoteControlBean> buildDeviceEnablePeakBean(String userName, PeakShiftDeviceEnableDto peakShiftDeviceEnableDto, CollectorEntity bean) throws com.zte.uedm.common.exception.UedmException {
        Map<String, Object> map = new HashMap<>();
        map.put(CommonConst.PEAK_ENABLE_MAP_KEY_COLLECTOR_ID, bean.getId());
        map.put(CommonConst.PEAK_ENABLE_MAP_MODULE_ID, bean.getAdapterId());
        String mainLinkId = DeviceUtils.parseMasterLink(bean.getLinkInfo());
        map.put(CommonConst.PEAK_ENABLE_MAP_KEY_MAIN_LINK, mainLinkId);
        // 采集周期
        Integer signalCycle = DeviceUtils.parseSignalCycle(bean.getProtocolAttribute());
        map.put(CommonConst.PEAK_ENABLE_MAP_KEY_SIGNAL_CYCLE, signalCycle);
        // 待设置的原始测点值
        boolean setEnable = peakShiftDeviceEnableDto.getEnable();
        String pointValue = setEnable ? ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE_TIMING : ZTEEMLOriginalPointConstat.PEAK_SHIFT_DISABLE_VALUE;

        SouthExtraDataBean extraData = new SouthExtraDataBean();
        extraData.setUserName(userName);
        RemoteControlBean remoteControlBean = new RemoteControlBean();
        remoteControlBean.setExtraData(extraData);
        remoteControlBean.setOmpId(ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE);
        remoteControlBean.setValue(pointValue);
        remoteControlBean.setRelationPosition("1");//
        remoteControlBean.setPointIndex("1");//CSU为0
        remoteControlBean.setDevice(map);
        remoteControlBean.setLogId(ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE + "|" + remoteControlBean.getValue() + "|" + UUID.randomUUID());
        return Collections.singletonList(remoteControlBean);
    }

    @Override
    public Pair<String, String> findExecStatusAndCurrPeakStrategy(String deviceId, String batteryStatus,
                                                                  PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws com.zte.uedm.common.exception.UedmException {
        //取当前时间点的设备策略
        SimpleDateFormat format = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_BASE);
        SimpleDateFormat formatDay = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_DAY);
        Date date = new Date();
        String currentTime = format.format(date);
        String currentDate = formatDay.format(date);
        String execStatus = PeakShiftDeviceExecStatusEnum.getAbnormalId();
        String currPeakStrategy = PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId();

        PeakShiftCsuAllStrategyBean peakShiftCsuAllStrategyBean = peakShiftMonitorBaseDataBean.getCsuStrategyDetailMap().get(deviceId);
        if (peakShiftCsuAllStrategyBean == null) {
            log.info("no strategy device:{}", deviceId);
            //没有查到一条错峰策略
            return Pair.of(currPeakStrategy, execStatus);
        }

        List<PeakShiftCsu5StrategyDetailBean> detailStrategyBeans = peakShiftCsuAllStrategyBean.getPeakShiftCsu5StrategyDetailBos();

        execStatus = getExecStatusInHoliday(execStatus, currentDate, detailStrategyBeans);
        if (PeakShiftDeviceExecStatusEnum.getNormalId().equals(execStatus)) {
            log.info("holiday no peakshift.now:{},device:{}", currentTime, deviceId);
            //execStatus变为正常了，说明是节假日
            return Pair.of(currPeakStrategy, execStatus);
        }

        //判断周末是否支持错峰
        execStatus = getExecStatusWeekend(execStatus, date, peakShiftCsuAllStrategyBean);
        if (PeakShiftDeviceExecStatusEnum.getNormalId().equals(execStatus)) {
            log.info("weekend no peakshift.now:{},device:{}", currentTime, deviceId);
            //execStatus变为正常了，说明是周末，并且不错峰
            return Pair.of(currPeakStrategy, execStatus);
        }

        //非节假日策略
        return getNotHolidayStrategyAndStatus(detailStrategyBeans, currentTime,
                currentDate, currPeakStrategy, execStatus, batteryStatus);
    }

    public List<PeakShiftCsuAllStrategyBean> queryStrategyAllDetails(StrategyDetailQueryDto strategyDetailQueryDto) throws com.zte.uedm.common.exception.UedmException {
        return peakShiftService.selectStrategyDetailByDeviceAndTimeRange(strategyDetailQueryDto.getDeviceId(), strategyDetailQueryDto.getStartTime(),
                strategyDetailQueryDto.getEndTime());
    }

    public String getExecStatusInHoliday(String execStatus, String currentDate, List<PeakShiftCsu5StrategyDetailBean> detailStrategyBeans) {
        List<PeakShiftCsu5StrategyDetailBean> holidayDetailStrategyBeans =
                detailStrategyBeans.stream().filter(PeakShiftCsu5StrategyDetailBean::getHolidayFlag).collect(Collectors.toList());

        if(!org.springframework.util.CollectionUtils.isEmpty(holidayDetailStrategyBeans))
        {
            for(PeakShiftCsu5StrategyDetailBean holidayBean : holidayDetailStrategyBeans)
            {
                if(currentDate.compareTo(holidayBean.getBeginDate()) >= 0 && currentDate.compareTo(holidayBean.getEndDate()) <= 0)
                {
                    //节假日范围内，无错峰策略，执行状态正常
                    execStatus = PeakShiftDeviceExecStatusEnum.getNormalId();
                    break;
                }
            }
            //没有节假日日期匹配，则继续往下走，为异常
        }
        return execStatus;
    }

    public String getExecStatusWeekend(String execStatus, Date date, PeakShiftCsuAllStrategyBean peakShiftCsuAllStrategyBean) {
        if(!peakShiftCsuAllStrategyBean.getWeekendFlag())
        {
            //周末不错峰，则判断今天是否错峰
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int weekNum = cal.get(Calendar.DAY_OF_WEEK);
            if(Calendar.SUNDAY == weekNum || Calendar.SATURDAY == weekNum)
            {
                //周末，无错峰策略，执行状态正常
                execStatus = PeakShiftDeviceExecStatusEnum.getNormalId();
            }
            //没有处于周末，则继续往下走
        }
        return execStatus;
    }

    public Pair<String, String> getNotHolidayStrategyAndStatus(List<PeakShiftCsu5StrategyDetailBean> detailStrategyBeans, String currentTime,
                                                               String currentDate, String currPeakStrategy, String execStatus, String batteryStatus) {
        List<PeakShiftCsu5StrategyDetailBean> notHolidayDetailStrategyBeans =
                detailStrategyBeans.stream().filter(x -> !x.getHolidayFlag()).collect(Collectors.toList());
        if(!org.springframework.util.CollectionUtils.isEmpty(notHolidayDetailStrategyBeans))
        {
            String time = currentTime.split(" ")[1];
            log.debug("currentTime:{}",currentTime);
            for(PeakShiftCsu5StrategyDetailBean notHolidayBean : notHolidayDetailStrategyBeans)
            {
                log.debug("notHolidayBean:{}",notHolidayBean);
                if(currentDate.compareTo(notHolidayBean.getBeginDate()) >= 0 && currentDate.compareTo(notHolidayBean.getEndDate()) <= 0)
                {
                    //左开右闭
                    if(time.compareTo(notHolidayBean.getBeginTime()) >= 0 && time.compareTo(notHolidayBean.getEndTime()) < 0)
                    {
                        //找到具体策略类型
                        log.info("find the strategy:{}",notHolidayBean);
                        currPeakStrategy = notHolidayBean.getStrategyType();
                        execStatus = findExecStatusCSU5(currPeakStrategy,batteryStatus);
                        break;
                    }
                    //时刻未对应，找不到策略，next
                }
                //日期未匹配，next
            }
        }
        return Pair.of(currPeakStrategy,execStatus);
    }

    public String findExecStatusCSU5(String currPeakStrategy, String battStatus) {
        log.info("currPeakStrategy,{};battStatus:{}",currPeakStrategy,battStatus);
        String execStatus = PeakShiftDeviceExecStatusEnum.getAbnormalId();
        //除了尖峰是应该对应放电，其他策略时段均对应充电，否则异常
        if (PeakShiftingStrategyEnum.STRATEGYVALLEY.getId().equals(currPeakStrategy))
        {
            execStatus = findExecStatuscurrPeakStrategy0(battStatus);
        }
        if (PeakShiftingStrategyEnum.STRATEGYFLAT.getId().equals(currPeakStrategy))
        {
            execStatus = findExecStatuscurrPeakStrategy0(battStatus);
        }
        if (PeakShiftingStrategyEnum.STRATEGYPEAK.getId().equals(currPeakStrategy))
        {
            execStatus = findExecStatuscurrPeakStrategy0(battStatus);
        }
        if (PeakShiftingStrategyEnum.STRATEGYTIP.getId().equals(currPeakStrategy))
        {
            execStatus = findExecStatuscurrPeakStrategy3(battStatus);
        }
        return execStatus;
    }

    public String findExecStatuscurrPeakStrategy3(String battStatus) {
        if (BattStatusEnum.BATTTEST.getId().equals(battStatus) || BattStatusEnum.BATTDISCHARGE.getId().equals(battStatus))
        {
            return PeakShiftDeviceExecStatusEnum.getNormalId();
        }
        else return PeakShiftDeviceExecStatusEnum.getAbnormalId();
    }

    public String findExecStatuscurrPeakStrategy0(String battStatus) {
        if (BattStatusEnum.BATTFLOAT.getId().equals(battStatus) || BattStatusEnum.BATTEQUAL.getId().equals(battStatus) || BattStatusEnum.BATTCHARGE.getId().equals(battStatus))
        {
            return PeakShiftDeviceExecStatusEnum.getNormalId();
        }
        else return PeakShiftDeviceExecStatusEnum.getAbnormalId();
    }


    @Override
    public String getBattCapacityPoint() {
        return BATTERYSET_SMPID_CAPACITY_RATE;
    }

    @Override
    public String getBatteryStatus(String collectorId, PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws com.zte.uedm.common.exception.UedmException {

        return getCommonBatteryStatus(collectorId, peakShiftMonitorBaseDataBean);
    }

    @Override
    public TemplateStrategyDetailEntity getLatestStrategy(String collectorId, ServiceBaseInfoBean serviceBean) throws com.zte.uedm.common.exception.UedmException {
        PeakShiftParamVo peakShiftParamVo = peakShiftIssuedTaskDomain.queryCsuDeviceParam(collectorId, serviceBean);
        return transferTo(peakShiftParamVo);
    }

    @Override
    public PeakShiftMonitorBaseDataBean prepareBaseData(List<DevicePeakCacheInfoBean> list) throws com.zte.uedm.common.exception.UedmException {
        PeakShiftMonitorBaseDataBean baseDataBean = new PeakShiftMonitorBaseDataBean();
        Set<String> collectorIds = list.stream().map(DevicePeakCacheInfoBean::getDeviceId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        // 设置设备关联的电池组、电池组状态标准测点值
        setBatterySetStatusMap(collectorIds, baseDataBean);

        // 获取电池最近下发的策略详情
        setCSUStrategy(collectorIds, baseDataBean);
        return baseDataBean;
    }

    /* Started by AICoder, pid:13d151e6a5y1cfc144ce0be3006db14b85c78f76 */
    public boolean extracted(List<TemplateTimeGranDetailVo> detail, String name) {
        if (!checkInitialIntervals(detail)) {
            OperlogBean oper = new OperlogBean(GlobalConstants.SYSTEM, GlobalConstants.SYSTEM_IP, UserThreadLocal.getLoginType(),
                    PEAK_SHIFT_STRATEGY_MODULE_ZH, PEAK_SHIFT_STRATEGY_MODULE_EN,
                    OperlogBean.LogRank.operlog_rank_important, PEAK_SHIFT_STRATEGY_OPERATION_ZH, PEAK_SHIFT_STRATEGY_OPERATION_EN);
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            boolean operStatus = true;
            try {
                operStatus = processOperationStatus(name, detailZh, detailEn);
            } catch (com.zte.uedm.common.exception.UedmException e) {
                throw new RuntimeException(e);
            }
            oper.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            String operMsg = null;
            try {
                operMsg = jsonService.objectToJson(oper);
            } catch (com.zte.uedm.common.exception.UedmException e) {
                throw new RuntimeException(e);
            }
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
            return true;
        }
        return false;
    }

    public List<TemplateTimeGranDetailVo> getTemplateTimeGranDetailVos(List<TemplateTimeGranDetailVo> templateTimeGranDetailVoList) {
        List<TemplateTimeGranDetailVo> mergedList = new ArrayList<>();
        if (!templateTimeGranDetailVoList.isEmpty()) {
            TemplateTimeGranDetailVo current = null;
            for (TemplateTimeGranDetailVo bean : templateTimeGranDetailVoList) {
                if (current == null) {
                    current = bean;
                } else if (Objects.equals(current.getStrategyType(), bean.getStrategyType()) &&
                        current.getEndTime().equals(bean.getBeginTime())) {
                    current.setEndTime(bean.getEndTime());
                } else {
                    mergedList.add(current);
                    current = bean;
                }
            }
            if (current != null) {
                mergedList.add(current);
            }
        }
        return mergedList;
    }
    /* Ended by AICoder, pid:13d151e6a5y1cfc144ce0be3006db14b85c78f76 */
    private String getDcPowerid(String collectorId)  {
        List<String> deviceIds = null;
        try {
            deviceIds = resourceCollectorRelationCacheManager
                    .getRelationsByCollectors(Collections.singleton(collectorId))
                    .stream()
                    .map(ResourceCollectorRelationEntity::getResourceId)
                    .collect(Collectors.toList());
        } catch (UedmException e) {
            log.warn("resourceCollectorRelationCacheManager getRelationsByCollectors error : {}", e.getMessage(), e);
        }

        List<DeviceEntity> batterSetDevices = deviceCacheManager.getDeviceByIdsAndMoc(deviceIds, MocOptional.DC_POWER.getId());

        // 如果batterSetDevices列表为空，返回collectorId，否则返回第一个设备的ID
        String result = batterSetDevices.isEmpty() ? collectorId : batterSetDevices.get(0).getId();
        return result;
    }



    public boolean checkInitialIntervals(List<TemplateTimeGranDetailVo> intervals) {
        // 将intervals按照strategyType分组，并统计每种strategyType的数量
        Map<Integer, Long> strategyTypeCountMap = intervals.stream()
                .collect(Collectors.groupingBy(
                        TemplateTimeGranDetailVo::getStrategyType,
                        Collectors.counting()
                ));

        // 检查是否存在数量大于4的策略类型
        return strategyTypeCountMap.values().stream().noneMatch(count -> count > 4);
    }

    public void processDetail(List<TemplateTimeGranDetailVo> intervals, BigDecimal dischargeDuration) {
        // 计算尖峰时段总时长
        int totalPeakDuration = 0;
        Map<Integer, Integer> strategyTypeCounts = new HashMap<>();
        Map<TemplateTimeGranDetailVo, Integer> beginMinutesMap = new HashMap<>();
        Map<TemplateTimeGranDetailVo, Integer> endMinutesMap = new HashMap<>();
        for (TemplateTimeGranDetailVo interval : intervals) {
            int beginMinutes = timeToMinutes(interval.getBeginTime());
            int endMinutes = timeToMinutes(interval.getEndTime());
            beginMinutesMap.put(interval, beginMinutes);
            endMinutesMap.put(interval, endMinutes);
            int duration = endMinutes - beginMinutes;
            int type = interval.getStrategyType();
            strategyTypeCounts.put(type, strategyTypeCounts.getOrDefault(type, 0) + 1);
            if (type == 3) {
                totalPeakDuration += duration;
            }
        }

        // 将放电时长转换为分钟
        int dischargeMinutes = dischargeDuration.multiply(BigDecimal.valueOf(60)).intValue();

        if (dischargeMinutes <= totalPeakDuration) {
            // 不需要调整
            return;
        }

        int remainingMinutes = dischargeMinutes - totalPeakDuration;

        if (!data(intervals, beginMinutesMap, endMinutesMap, strategyTypeCounts, remainingMinutes, 2, 3)) {
            if (!data(intervals, beginMinutesMap, endMinutesMap, strategyTypeCounts, remainingMinutes, 1, 3)) {
                log.warn("The number of peak periods is greater than 4");
            }
        }
    }

    public boolean processOperationStatus(String name, StringBuilder detailZh, StringBuilder detailEn) throws com.zte.uedm.common.exception.UedmException {

        detailZh.append("采集器：").append(name).
                append("无法生成自动策略，原因为：策略中相同时段存在4个以上").append("\n");
        detailEn.append("Collector：").append(name).
                append("Unable to generate automatic template, reason: There are more than 4 instances of the same time period in the strategy").append("\n");

        return true;
    }

    public boolean data(List<TemplateTimeGranDetailVo> intervals,
                        Map<TemplateTimeGranDetailVo, Integer> beginMinutesMap,
                        Map<TemplateTimeGranDetailVo, Integer> endMinutesMap,
                        Map<Integer, Integer> strategyTypeCounts,
                        int remainingMinutes,
                        int fromType,
                        int toType) {
        List<TemplateTimeGranDetailVo> sortedIntervals = new ArrayList<>();
        for (TemplateTimeGranDetailVo interval : intervals) {
            if (interval.getStrategyType() == fromType) {
                sortedIntervals.add(interval);
            }
        }
        sortedIntervals.sort((a, b) -> endMinutesMap.get(b) - endMinutesMap.get(a));

        for (TemplateTimeGranDetailVo interval : sortedIntervals) {
            if (remainingMinutes <= 0) {
                break;
            }
            int duration = endMinutesMap.get(interval) - beginMinutesMap.get(interval);
            if (remainingMinutes >= duration) {
                // 整个时间段转换
                if (strategyTypeCounts.getOrDefault(toType, 0) >= 4) {
                    continue;
                }
                interval.setStrategyType(toType);
                strategyTypeCounts.put(fromType, strategyTypeCounts.get(fromType) - 1);
                strategyTypeCounts.put(toType, strategyTypeCounts.getOrDefault(toType, 0) + 1);
                remainingMinutes -= duration;
            } else {
                // 部分转换，需要拆分
                if (strategyTypeCounts.getOrDefault(toType, 0) >= 4) {
                    continue;
                }
                int newBegin = endMinutesMap.get(interval) - remainingMinutes;
                TemplateTimeGranDetailVo newInterval = new TemplateTimeGranDetailVo(minutesToTime(newBegin), interval.getEndTime(), toType);
                interval.setEndTime(minutesToTime(newBegin));
                endMinutesMap.put(interval, newBegin);
                beginMinutesMap.put(newInterval, newBegin);
                endMinutesMap.put(newInterval, endMinutesMap.get(interval));
                intervals.add(newInterval);
                strategyTypeCounts.put(toType, strategyTypeCounts.getOrDefault(toType, 0) + 1);
                remainingMinutes = 0;
            }
        }
        return remainingMinutes <= 0;
    }

    public int timeToMinutes(String timeStr) {
        String[] parts = timeStr.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        return hours * 60 + minutes;
    }

    public String minutesToTime(int minutes) {
        int hours = minutes / 60;
        int mins = minutes % 60;
        if (hours >= 24) {
            hours -= 24;
        }
        return String.format("%d:%02d", hours, mins);
    }
    /* Ended by AICoder, pid:w6dc0l6361j2f741478c0961c1679538b15482d9 */

    @Override
    /* Started by AICoder, pid:51ab4ubf421adcd14e0a08dfb04de62283c22cfd */
    public void stopPeakShiftingStrategy(CollectorEntity collector, PeakShiftTaskWithDevicePo peakShiftTaskWithDevicePo) {
        try {
            Map<String, Object> map = objectMapper.convertValue(collector, Map.class) == null ? new HashMap<>() : objectMapper.convertValue(collector, Map.class);
            RemoteControlBean remoteControlBean;
            remoteControlBean = new RemoteControlBean();
            SouthExtraDataBean extraData = new SouthExtraDataBean();
            extraData.setType(PeakShiftConstants.CSU5);
            extraData.setTaskId(peakShiftTaskWithDevicePo.getId());
            remoteControlBean.setOmpId(ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE);
            remoteControlBean.setValue("0");
            remoteControlBean.setRelationPosition("1");
            remoteControlBean.setPointIndex("1");
            remoteControlBean.setDevice(map);
            remoteControlBean.setExtraData(extraData);
            remoteControlBean.setLogId("PEAK_SHIFT_STOP" + "-" + UUID.randomUUID().toString() + "|" + peakShiftTaskWithDevicePo.getId());
            log.info("CSU5_remoteControlBean:{}", JSON.toJSONString(remoteControlBean));
            msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                    jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
        } catch (Exception e) {
            log.error("CSU5 stop peak shifting strategy failed, collector:{}, peakShiftTaskWithDevicePo:{}", JSON.toJSONString(collector), JSON.toJSONString(peakShiftTaskWithDevicePo), e);
        }
    }
    /* Ended by AICoder, pid:51ab4ubf421adcd14e0a08dfb04de62283c22cfd */
}
