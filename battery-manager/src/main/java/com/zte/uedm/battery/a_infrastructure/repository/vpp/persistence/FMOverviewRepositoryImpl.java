package com.zte.uedm.battery.a_infrastructure.repository.vpp.persistence;

import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.battery.a_domain.aggregate.vpp.model.entity.FMOverviewEntity;
import com.zte.uedm.battery.a_domain.aggregate.vpp.repository.FMOverviewRepository;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.converter.FMOverviewPoConverter;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper.FMOverviewMapper;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/* Started by AICoder, pid:1bcfel321dh74c214f600925a0346a4d34d888dd */
@Slf4j
@Repository
public class FMOverviewRepositoryImpl implements FMOverviewRepository {

    @Autowired
    private FMOverviewMapper fmOverviewMapper;

    @Override
    public List<FMOverviewEntity> selectFmOverviewBean(String userName) throws UedmException {
        try {
            List<FMOverviewPo> fmOverviewPos = fmOverviewMapper.selectFmOverviewBean(userName);
            return FMOverviewPoConverter.INSTANCE.listFMOverviewPoToFMOverviewEntity(fmOverviewPos);
        } catch (Exception e) {
            log.error("FMOverviewRepositoryImpl selectFmOverviewBean occur error message : {}", e.getMessage(), e);
            throw new UedmException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getCode(), ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }

    @Override
    public int deleteFmOverviewBean(String userName) throws UedmException {
        try {
            return fmOverviewMapper.deleteFmOverviewBean(userName);
        } catch (Exception e) {
            log.error("FMOverviewRepositoryImpl deleteFmOverviewBean occur error message : {}", e.getMessage(), e);
            throw new UedmException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getCode(), ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }

    @Override
    public void insertFmOverviewBean(List<FMOverviewPo> list) throws UedmException {
        try {
            fmOverviewMapper.insertFmOverviewBean(list);
        } catch (Exception e) {
            log.error("FMOverviewRepositoryImpl insertFmOverviewBean occur error message : {}", e.getMessage(), e);
            throw new UedmException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getCode(), ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }

    @Override
    public Integer updateFmOverviewBean(List<FMOverviewPo> beanList) throws UedmException {
        try {
            return fmOverviewMapper.updateFmOverviewBean(beanList);
        } catch (Exception e) {
            log.error("FMOverviewRepositoryImpl updateFmOverviewBean occur error message : {}", e.getMessage(), e);
            throw new UedmException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getCode(), ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }
}
/* Ended by AICoder, pid:1bcfel321dh74c214f600925a0346a4d34d888dd */
