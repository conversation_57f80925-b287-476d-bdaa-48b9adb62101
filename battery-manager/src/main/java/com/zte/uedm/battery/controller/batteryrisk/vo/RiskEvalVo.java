package com.zte.uedm.battery.controller.batteryrisk.vo;


import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
@Getter
@Setter
@ToString
@ApiModel(description = "风险历史-详情查询结果")
public class RiskEvalVo {

    @ApiModelProperty(value = "风险id")
    private String riskId;

    @ApiModelProperty(value = "电池id")
    private String battId;

    @ApiModelProperty(value = "电池名称")
    private String name;

    @ApiModelProperty(value = "位置")
    private String position;

    @ApiModelProperty(value = "电池类型")
    private IdNameBean battType;

    @ApiModelProperty(value = "风险名称")
    private String riskName;

    @ApiModelProperty(value = "风险等级")
    private IdNameBean riskLevel;

    @ApiModelProperty(value = "原因-国际化")
    private String riskCause;

    @ApiModelProperty(value = "建议-国际化")
    private String riskSuggestion;

    @ApiModelProperty(value = "评估时间(年月日)")
    private String evaluateTime;


}
