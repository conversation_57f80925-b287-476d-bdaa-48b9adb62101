package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.pojo.BattTestRecordPojo;
import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPo;
import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPojo;
import com.zte.uedm.battery.controller.batttest.bo.TestImpactTrendSelectBo;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BattTestMapper
{
    /**
     * 查询最新的测试状态
     * @param ids
     * @return
     */
    List<IdNameBean> selectBattTestStatus(@Param("ids") List<String> ids);

    /**
     * 批量新增电池测试记录
     *
     * @param battTestRecordPojos
     * @return
     * @throws UedmException
     */
    Integer insertBeans(@Param("list") List<BattTestRecordPojo> battTestRecordPojos);

    /**
     * 批量新增电池测试记录
     *
     * @param relationDataRecordPojos
     * @return
     * @throws UedmException
     */
    Integer insertBattTestRelationDataRecordPojos(@Param("list") List<BattTestRelationDataRecordPojo> relationDataRecordPojos);

    /**
     * 根据记录id查询所有关联数据记录
     * @param recordPoId
     * @return
     * @throws UedmException
     */
    List<BattTestRelationDataRecordPo> selectByRecordPoId(String recordPoId)throws UedmException;

    /**
     * 根据记录id查询关联记录详情
     * @return
     */
    List<BattTestRelationDataRecordPojo> selectDataRecord(@Param("list") List<String> recordIds);

    /**
     * 批量更新电池关联测试记录详情
     * @param relationDataRecordPos
     * @return
     */
    Integer updateBeans(@Param("list") List<BattTestRelationDataRecordPojo> relationDataRecordPos);

    /**
     * 根据开关电源（电池组）ids等多条件查询
     * @param testImpactTrendSelectBo
     * @return
     */
    List<BattTestRecordPojo> selectByCondition(TestImpactTrendSelectBo testImpactTrendSelectBo);
}
