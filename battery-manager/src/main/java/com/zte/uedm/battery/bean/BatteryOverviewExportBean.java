package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterRequestDto;
import com.zte.uedm.battery.enums.overview.BattOverViewDimEnums;
import com.zte.uedm.common.bean.log.OperlogDetail;
import com.zte.uedm.common.enums.SortEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 错峰用电策略电池历史数据Bean
 */
@Setter
@Getter
@ToString
@ApiModel(description = "电池概览")
public class BatteryOverviewExportBean extends BatteryOverviewFilterRequestDto
{

	/**
	 * 电池概览维度列表
	 */
	@ApiModelProperty(value = "电池概览维度列表")
	private List<BatteryOverviewBean> battOverViewdims;

	/**
	 * 电池工况维度列表
	 */
	@ApiModelProperty(value = "电池工况维度列表")
	private List<String> workCoditionDims;

	/**
	 * 电池资产维度列表
	 */
	@ApiModelProperty(value = "电池资产维度列表")
	private List<String> assetdims;

	/**
	 * 统计维度列表
	 */
	@ApiModelProperty(value = "统计维度列表")
	private List<String> statisticsDims;

	/**
	 *  图片信息列表
	 */
	@ApiModelProperty(value = "图片信息列表")
	private List<ImageBean> images;

	/**
	 * export file name;
	 */
	@ApiModelProperty(value = "导出文件名称")
	private String fileName;

	/**
	 *  排序字段
	 */
	@ApiModelProperty(value = "排序字段")
	private String order;

	/**
	 *  顺序
	 */
	@ApiModelProperty(value = "顺序")
	private String sort;

	/**
	 *  导出位置
	 */
	@OperlogDetail(zhName = "导出位置",enName = "Export position")
	@ApiModelProperty(value = "导出位置")
	private String position;

	/**
	 * 校验纬度是否为空
	 * @return
	 */
	public boolean checkBattOverViewdimsIsEmpty()
	{
		boolean flag = true;
		if (org.apache.commons.collections.CollectionUtils.isEmpty(battOverViewdims))
		{
			return false;
		}
		List<String> dimIdList = battOverViewdims.stream().map(BatteryOverviewBean::getId)
				.filter(org.apache.commons.lang.StringUtils::isNotBlank).collect(Collectors.toList());
		if (org.springframework.util.CollectionUtils.isEmpty(dimIdList))
		{
			flag = false;
		}
		return flag;
	}

	/**
	 * 校验纬度是否在范围内
	 * @return
	 */
	public Pair<Boolean, List<String>> checkBattOverViewdimsInRange()
	{
		Boolean check = true;

		List<String> dimIdList = battOverViewdims.stream().map(BatteryOverviewBean::getId)
				.filter(org.apache.commons.lang.StringUtils::isNotBlank).collect(Collectors.toList());

		List<String> dimsList = BattOverViewDimEnums.getAllBattStatisticsOverviewDim();
		for(String dim : dimIdList)
		{
			if (!dimsList.contains(dim)) {
				check = false;
				break;
			}
		}

		return Pair.of(check, new ArrayList<>(dimsList));
	}

	public Pair<Boolean, List<String>> checkOrder()
	{
		Boolean check = true;
		List<String> errMs = new ArrayList<>();

		//order为空给默认值 -> 默认值：name
		if(StringUtils.isBlank(this.order))
		{
			this.order = BattOverViewDimEnums.NAME.getId();
			return Pair.of(check, errMs);
		}

		List<String> enumIdList = BattOverViewDimEnums.getBattOverviewSelectOrderOptionalList();
		if(!enumIdList.contains(this.order))
		{
			check = false;
		}
		return Pair.of(check, enumIdList);
	}

	public Pair<Boolean, List<String>> checkSort()
	{
		Boolean check = true;
		List<String> errMs = new ArrayList<>();

		if(StringUtils.isBlank(this.sort))
		{
			return Pair.of(check, errMs);
		}

		Set<String> sortIdList = SortEnum.getSortIds();

		if(!sortIdList.contains(this.sort))
		{
			check = false;
		}
		return Pair.of(check, new ArrayList<>(sortIdList));
	}


	/**
	 * 校验非空字段
	 * @return true: 通过校验， false:未通过校验
	 */
	public Pair<Boolean, List<String>> checkEmpty()
	{
		Boolean check = true;
		List<String> errorKey = new ArrayList<>();
		if(StringUtils.isBlank(super.getLogicId()))
		{
			errorKey.add("logicId");
			check = false;
		}
		return Pair.of(check, errorKey);
	}
}
