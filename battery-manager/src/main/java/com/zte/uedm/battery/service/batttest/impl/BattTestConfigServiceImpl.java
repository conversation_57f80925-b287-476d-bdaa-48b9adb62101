/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattTestConfigServiceImpl
 * 文件作者：00248587
 * 开发时间：2023/3/14
 */
package com.zte.uedm.battery.service.batttest.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.battery.bean.BattTestImpactTrendPo;
import com.zte.uedm.battery.controller.batterytest.dto.BattImpactTrendDimsUpdateDto;
import com.zte.uedm.battery.controller.batterytest.vo.BattImpactTrendVo;
import com.zte.uedm.battery.bean.BattTestProportionDimPo;
import com.zte.uedm.battery.controller.batterytest.dto.BattTestProportionUpdateDto;
import com.zte.uedm.battery.controller.batterytest.vo.SelectBattTestConfigVo;
import com.zte.uedm.battery.dao.BattTestDao;
import com.zte.uedm.battery.dao.BattTestProportionDimDao;
import com.zte.uedm.battery.enums.battlife.BattLifeListDimEnums;
import com.zte.uedm.battery.enums.battlife.BatteryTestProportionEnums;
import com.zte.uedm.battery.enums.batttest.BatteryTestImpactEnums;
import com.zte.uedm.battery.service.batttest.BattTestConfigService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeOtherUtil;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BattTestConfigServiceImpl implements BattTestConfigService
{

    @Autowired
    private BattTestDao battTestDao;
    @Autowired
    private BattTestProportionDimDao battTestProportionDimDao;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private MessageSenderService msgSenderService;
    @Autowired
    private JsonService jsonService;

    private static final String BATTERY_IMPACT_TREND_ZH ="电池放电影响趋势列定制";
    private static final String BATTERY_IMPACT_TREND_EN="Battery test impact trend configuration";
    private static final String BATTERY_IMPACT_TREND_UPDATE_DIM_ZH="电池放电影响趋势列定制更新";
    private static final String BATTERY_IMPACT_TREND_UPDATE_DIM_EN="Battery test impact trend configuration update";

    @Override
    public List<BattImpactTrendVo> selectImpactTrendConfig(String userName, String languageOption) throws UedmException
    {
        List<BattImpactTrendVo> resultList = new ArrayList<>();
        try
        {
            //1、查询数据库中寿命列表
            List<BattTestImpactTrendPo> battTestImpactTrendPos = battTestDao.selectImpactTrendConfig(userName);
            log.debug("=====  BattTestConfigServiceImpl selectImpactTrendConfig :=====\n data base List: {}", resultList);


            if(CollectionUtils.isEmpty(battTestImpactTrendPos))
            {
                resultList = getBattTestImpactTrendConfigBean(userName, languageOption);
            }
            else
            {
                resultList = new ArrayList<>(Lists.transform(battTestImpactTrendPos, (bean)->{
                    BattImpactTrendVo battLifeConfigVo = new BattImpactTrendVo();
                    BeanUtils.copyProperties(bean, battLifeConfigVo);
                    apply(battLifeConfigVo,languageOption);
                    return battLifeConfigVo;
                }));

                if(battTestImpactTrendPos.size() < BattLifeListDimEnums.values().length)
                {
                    //若数据库有值，后续枚举新增元素，则返回值增加新增元素，并入库
                    List<BattImpactTrendVo> addResultlist  = getBattTestImpactTrendConfigBean(userName, languageOption, battTestImpactTrendPos);
                    resultList.addAll(addResultlist);
                }

            }
            log.debug("=====  BattTestConfigServiceImpl selectImpactTrendConfig  :=====\n result list: {}", JSON.toJSONString(resultList));
            log.info("=====  BattTestConfigServiceImpl selectImpactTrendConfig  :=====\n result list.size: {}", resultList.size());
            return resultList;
        }
        catch (UedmException ue)
        {
            log.error("BattTestConfigServiceImpl-> selectImpactTrendConfig error", ue);
            throw new UedmException(ue.getErrorId(), ue.getErrorDesc(), "selectImpactTrendConfig query error.");
        }
        catch (Exception e)
        {
            log.error("BattTestConfigServiceImpl-> selectImpactTrendConfig error", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError(e.getMessage());
        }
    }

    private List<BattImpactTrendVo> getBattTestImpactTrendConfigBean(String userName, String languageOption) throws UedmException {
        List<BattImpactTrendVo> resultList = new ArrayList<>();
        List<BattTestImpactTrendPo> inserts = new ArrayList<>();
        for (BatteryTestImpactEnums value :BatteryTestImpactEnums.values()) {
            insertAndhandleResult(userName, languageOption, inserts, value, resultList);
        }
        //入库
        battTestDao.insertBattImpactTrendConfigByBeans(inserts);

        return resultList;
    }

    private List<BattImpactTrendVo> getBattTestImpactTrendConfigBean(String userName, String languageOption, List<BattTestImpactTrendPo> battTestImpactTrendPos) throws UedmException
    {
        List<String> dimsql = battTestImpactTrendPos.stream().map(BattTestImpactTrendPo::getId).distinct().collect(Collectors.toList());
        List<BattImpactTrendVo> resultList = new ArrayList<>();
        //新增属性
        List<BattTestImpactTrendPo> inserts = new ArrayList<>();
        for (BatteryTestImpactEnums value :BatteryTestImpactEnums.values()) {
            if(!dimsql.contains(value.getId())) {
                insertAndhandleResult(userName, languageOption, inserts, value, resultList);
            }
        }
        battTestDao.insertBattImpactTrendConfigByBeans(inserts);
        return resultList;
    }

    private void insertAndhandleResult(String userName, String languageOption,
                                       List<BattTestImpactTrendPo> inserts, BatteryTestImpactEnums value,
                                       List<BattImpactTrendVo> resultList)
    {
        BattTestImpactTrendPo battTestImpactTrendPo = new BattTestImpactTrendPo();
        battTestImpactTrendPo.setId(value.getId());
        battTestImpactTrendPo.setUserName(userName);
        battTestImpactTrendPo.setSequence(value.getDefaultIndex());
        battTestImpactTrendPo.setEnable(value.getDefaultEnable());
        battTestImpactTrendPo.setName(value.getName());
        battTestImpactTrendPo.setUpdater(userName);
        battTestImpactTrendPo.setCreator(userName);
        battTestImpactTrendPo.setGmtModified(new Date());
        battTestImpactTrendPo.setGmtCreate(new Date());
        inserts.add(battTestImpactTrendPo);

        BattImpactTrendVo battImpactTrendVo = new BattImpactTrendVo();
        BeanUtils.copyProperties(battTestImpactTrendPo, battImpactTrendVo);
        battImpactTrendVo.setDefaultEnable(value.getDefaultEnable());
        battImpactTrendVo.setDefaultFixed(value.getDefaultFixed());
        battImpactTrendVo.setDefaultIndex(value.getDefaultIndex());
        battImpactTrendVo.setSortable(value.getSortable());
        battImpactTrendVo.setName(i18nUtils.getMapFieldByLanguageOption(battImpactTrendVo.getName(), languageOption));
        resultList.add(battImpactTrendVo);
    }

    private void apply(BattImpactTrendVo item, String languageOption)
    {
        item.setName(i18nUtils.getMapFieldByLanguageOption(item.getName(), languageOption));
        for (BatteryTestImpactEnums value :BatteryTestImpactEnums.values()) {
            if (StringUtils.equals(item.getId(), value.getId())){
                item.setDefaultIndex(value.getDefaultIndex());
                item.setDefaultFixed(value.getDefaultFixed());
                item.setDefaultEnable(value.getDefaultEnable());
                item.setSortable(value.getSortable());
                break;
            }
        }
    }

    @Override
    public Integer updateImpactTrendConfig(List<BattImpactTrendDimsUpdateDto> updateBeanList, ServiceBaseInfoBean serviceBean) throws UedmException
    {
        Integer num = 0;
        String operMsg = "";
        String userName = serviceBean.getUserName();
        String ip = serviceBean.getRemoteHost();
        String languageOption = serviceBean.getLanguageOption();
        boolean operStatus = true;
        // 操作记录日志信息
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(userName, ip, connectMode, BATTERY_IMPACT_TREND_ZH,BATTERY_IMPACT_TREND_EN,
                OperlogBean.LogRank.operlog_rank_notice, BATTERY_IMPACT_TREND_UPDATE_DIM_ZH, BATTERY_IMPACT_TREND_UPDATE_DIM_EN);
        try {
            //检查入参
            checkParamList(updateBeanList);

            //获取放电影响趋势表头列表
            List<BattImpactTrendVo> battImpactTrendVos = selectImpactTrendConfig(userName, languageOption);

            log.info("BattTestConfigServiceImpl updateImpactTrendConfig ->battImpactTrendVos from db :{}", JSON.toJSONString(battImpactTrendVos, SerializerFeature.WriteMapNullValue));
            //根据列表，获取 id集合
            List<String> queryIdList =
                    battImpactTrendVos.stream().map(BattImpactTrendVo::getId).collect(Collectors.toList());
            //根据列表，获取用户序列集合
            List<Integer> querySeqList =
                    battImpactTrendVos.stream().map(BattImpactTrendVo::getSequence).collect(Collectors.toList());

            ///检查参数异常
            checkParamExp(updateBeanList, battImpactTrendVos, queryIdList, querySeqList);

            List<BattTestImpactTrendPo> updates = Lists.transform(updateBeanList, (bean)->{
                BattTestImpactTrendPo po = new BattTestImpactTrendPo();
                po.setId(bean.getId());
                po.setUserName(userName);
                po.setSequence(bean.getSequence());
                po.setEnable(bean.getEnable());
                po.setUpdater(userName);
                po.setGmtModified(new Date());
                return po;
            });
            log.info("BattTestConfigServiceImpl-> updateImpactTrendConfig : updates.size = {}", updates.size());
            num = battTestDao.updateBattImpactTrendConfigByBeans(updates);
            log.info("BattTestConfigServiceImpl-> updateImpactTrendConfig : success updated num = {}", num);
            return num;
        }
        catch (UedmException ue)
        {
            operStatus=false;
            log.error("BattTestConfigServiceImpl-> updateImpactTrendConfig error", ue);
            throw new UedmException(ue.getErrorId(), ue.getErrorDesc(), ue.getErrorData());
        }
        catch (Exception e)
        {
            operStatus=false;
            log.error("BattTestConfigServiceImpl-> updateImpactTrendConfig error", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError(e.getMessage());
        }
        finally
        {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();

            detailZh.append("更新列定制详情：");
            detailEn.append("Update config: ");
            detailZh.append("用户名称 ：").append(operlogBean.getUserName()).append(" , 更新数据 ：")
                    .append(JSON.toJSONString(updateBeanList));
            detailEn.append("userName ：").append(operlogBean.getUserName()).append(" , Update Data ：")
                    .append(JSON.toJSONString(updateBeanList));

            if(operStatus)
            {
                operlogBean.refreshOperSuccess(detailZh.append(operMsg).toString(), detailEn.append(operMsg).toString(), new ArrayList<>());
            }
            else
            {
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
            operMsg = jsonService.objectToJson(operlogBean);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    private void checkParamList(List<BattImpactTrendDimsUpdateDto> updateBeanList) throws UedmException {
        //非空判断 <1>
        if (CollectionUtils.isEmpty(updateBeanList))
        {
            log.warn("updateImpactTrendConfig -> param is blank");
            throw new UedmException(-301, "param is blank");
        }

        //获取入参的sequence列表 做去重 并进行合理性校验 不一致，则报错 <2>
        if (checkReqSeq(updateBeanList))
        {
            log.warn("updateImpactTrendConfig -> sequence is not unique");
            //-302 不唯一
            throw new UedmException(-302, "sequence is not unique");
        }
    }

    private boolean checkReqSeq(List<BattImpactTrendDimsUpdateDto> updateBeanList)
    {
        List<Integer> reqSeqList = updateBeanList.stream().map(BattImpactTrendDimsUpdateDto::getSequence).distinct()
                .collect(Collectors.toList());
        if(reqSeqList.size() != updateBeanList.size()){
            log.warn("BattTestConfigServiceImpl updateImpactTrendConfig -> sequence is not unique");
            return true;
        }
        return false;
    }

    private void checkParamExp(List<BattImpactTrendDimsUpdateDto> updateBeanList, List<BattImpactTrendVo> battImpactTrendVos, List<String> queryIdList, List<Integer> querySeqList) throws UedmException {
        List<String> errorList = new ArrayList<>();
        for (BattImpactTrendDimsUpdateDto updateBean : updateBeanList)
        {
            //入参对象id
            if (checkId(queryIdList, errorList, updateBean))
            {
                log.warn("updateImpactTrendConfig -> id is not a given data");
                //-305 不在可选值范围内
                throw new UedmException(-305, "id is not a given data", JSON.toJSONString(errorList));
            }
            //sequence 每个元素的int唯一 <2>
            if (checkSequence(querySeqList, updateBean))
            {
                log.warn("updateImpactTrendConfig -> sequence is not a given data");
                //-302 每个元素的int不唯一
                throw new UedmException(-302, "sequence is not a given data");
            }

            //遍历列表 校验不可变动的设定 <4>
            if (checkEnable(battImpactTrendVos, errorList, updateBean))
            {
                log.warn("updateImpactTrendConfig -> the param is faixed");
                throw new UedmException(-208, "the param is faixed", JSON.toJSONString(errorList));
            }
        }
    }

    private boolean checkEnable(List<BattImpactTrendVo> battImpactTrendVos, List<String> errorList, BattImpactTrendDimsUpdateDto updateBean)
    {
        String updateId = updateBean.getId();
        BattImpactTrendVo voByUpdateId = battImpactTrendVos.stream().filter(item -> item.getId().equals(updateId)).collect(Collectors.toList()).get(0);
        boolean enable = voByUpdateId.getEnable();
        if(voByUpdateId.getDefaultFixed())
        {
            if (!updateBean.getEnable().equals(enable)) {
                log.warn("BattTestConfigServiceImpl updateImpactTrendConfig -> DefaultFixed is true");
                errorList.add(updateBean.getId());
                return true;
            }
        }
        return false;
    }

    private boolean checkSequence(List<Integer> querySeqList, BattImpactTrendDimsUpdateDto updateBean)
    {
        Integer sequence = updateBean.getSequence();
        if (!querySeqList.contains(sequence)) {
            log.warn("BattTestConfigServiceImpl updateImpactTrendConfig -> sequence is not a given data");
            return true;
        }
        return false;
    }

    private boolean checkId(List<String> queryIdList, List<String> errorList, BattImpactTrendDimsUpdateDto updateBean)
    {
        String id = updateBean.getId();
        //id 在可选值范围内判断 <3>
        if (!queryIdList.contains(id)) {
            log.warn("BattTestConfigServiceImpl updateImpactTrendConfig -> id is not a given data");
            errorList.add(id);
            return true;
        }
        return false;
    }

    public List<SelectBattTestConfigVo> selectBattTestProportion(String userName, String languageOption) throws UedmException
    {
        List<SelectBattTestConfigVo> resultList = new ArrayList<>();
        try
        {
            //查询数据库是否存在备电维度数据
            List<BattTestProportionDimPo> battTestProportionDimPos = battTestProportionDimDao.selectBattTestProportion(userName);
            log.debug("=====  BackupPowerConfigServiceImpl selectBackupPowerConfig :=====\n data base List: {}", JSON.toJSONString(battTestProportionDimPos));

            List<String> poIds = battTestProportionDimPos.stream().map(BattTestProportionDimPo::getId).filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
            List<String> allDimIds = BatteryTestProportionEnums.getAllDimIds();

            if (battTestProportionDimPos.size()!=BatteryTestProportionEnums.getAllDimIds().size() || !checkDimIdExist(poIds, allDimIds))
            {
                //数据库中保存的id与实际id数据不一致，清空数据
                int deleteNum = battTestProportionDimDao.deleteBattTestProportionByUserName(userName);
                log.info("selectBattTestProportion deleteBattTestProportionByUserName -> deleteNum:{}",deleteNum);
                battTestProportionDimPos = new ArrayList<>();
            }

            if(CollectionUtils.isEmpty(battTestProportionDimPos))
            {
                resultList = getBattTestProportionVo(userName, languageOption);
            }else
            {
                resultList = battTestProportionDimPos.stream().map((bean) -> {
                    SelectBattTestConfigVo selectBattTestConfigVo = new SelectBattTestConfigVo();
                    BeanUtils.copyProperties(bean, selectBattTestConfigVo);
                    buildVo(selectBattTestConfigVo, languageOption);
                    return selectBattTestConfigVo;
                }).collect(Collectors.toList());

            }
            log.info("=====  BackupPowerConfigServiceImpl selectBackupPowerConfig  :=====\n result list.size: {}", resultList.size());
            log.debug("=====  BackupPowerConfigServiceImpl selectBackupPowerConfig  :=====\n result list: {}", JSON.toJSONString(resultList));
            return resultList;
        }
        catch (UedmException ue)
        {
            log.error("BackupPowerConfigServiceImpl-> selectBackupPowerConfig error", ue);
            throw new UedmException(ue.getErrorId(), ue.getErrorDesc(), "BackupPowerConfigServiceImpl query error.");
        }
    }

    private boolean checkDimIdExist(List<String> poIds, List<String> allDimIds) {
        for (String poId: poIds)
        {
            if (!allDimIds.contains(poId))
            {
                log.info("selectBackupPowerConfig checkDimIdExist -> Data is wrong.");
                return false;
            }
        }
        return true;
    }

    @NotNull
    private List<SelectBattTestConfigVo> getBattTestProportionVo(String userName,String languageOption) throws UedmException {
        List<SelectBattTestConfigVo> resultList = new ArrayList<>();
        List<BattTestProportionDimPo> inserts = new ArrayList<>();
        for (BatteryTestProportionEnums value :BatteryTestProportionEnums.values()) {
            BattTestProportionDimPo battTestProportionDimPo = new BattTestProportionDimPo();
            battTestProportionDimPo.setId(value.getId());
            battTestProportionDimPo.setUserName(userName);
            battTestProportionDimPo.setSequence(value.getDefaultIndex());
            battTestProportionDimPo.setEnable(value.getDefaultEnable());
            battTestProportionDimPo.setName(value.getName());
            battTestProportionDimPo.setUpdater(userName);
            battTestProportionDimPo.setCreator(userName);
            battTestProportionDimPo.setGmtModified(new Date());
            battTestProportionDimPo.setGmtCreate(new Date());
            inserts.add(battTestProportionDimPo);

            SelectBattTestConfigVo selectBattTestConfigVo = new SelectBattTestConfigVo();
            BeanUtils.copyProperties(battTestProportionDimPo, selectBattTestConfigVo);
            selectBattTestConfigVo.setDefaultEnable(value.getDefaultEnable());
            selectBattTestConfigVo.setDefaultFixed(value.getDefaultFixed());
            selectBattTestConfigVo.setDefaultIndex(value.getDefaultIndex());
            selectBattTestConfigVo.setUnit(value.getUnit());
            selectBattTestConfigVo.setSortable(value.getSortable());
            selectBattTestConfigVo.setName(i18nUtils.getMapFieldByLanguageOption(selectBattTestConfigVo.getName(),languageOption));
            resultList.add(selectBattTestConfigVo);
        }
        //入库并记录操作
        battTestProportionDimDao.insertBattTestProportionByBeans(inserts);
        msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, JSON.toJSONString(inserts));

        return resultList;
    }

    private void buildVo(SelectBattTestConfigVo item, String languageOption)
    {
        item.setName(i18nUtils.getMapFieldByLanguageOption(item.getName(), languageOption));
        for (BatteryTestProportionEnums value :BatteryTestProportionEnums.values()) {
            if (StringUtils.equals(item.getId(), value.getId())){
                item.setDefaultIndex(value.getDefaultIndex());
                item.setDefaultFixed(value.getDefaultFixed());
                item.setDefaultEnable(value.getDefaultEnable());
                item.setSortable(value.getSortable());
                break;
            }
        }
    }

    @Override
    public Integer updateBattTestProportion(List<BattTestProportionUpdateDto> updateDtoList, String userName, String languageOption) throws UedmException
    {
        Integer num = 0;
        try {
            List<BattTestProportionDimPo> updates = Lists.transform(updateDtoList, (bean)->{
                BattTestProportionDimPo po = new BattTestProportionDimPo();
                po.setId(bean.getDimId());
                po.setUserName(userName);
                po.setSequence(bean.getSequence());
                po.setEnable(bean.getEnable());
                po.setUpdater(userName);
                po.setGmtModified(new Date());
                return po;
            });
            log.info("BackupPowerConfigServiceImpl-> updateBattTestProportion : updates.size = {}", updates.size());
            num = battTestProportionDimDao.updateBattTestProportionByBeans(updates);
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, JSON.toJSONString(updates));
            log.info("BackupPowerConfigServiceImpl-> updateBattTestProportion : updated num = {}", num);
            return num;
        }
        catch (Exception e)
        {
            log.error("BackupPowerConfigServiceImpl-> updateBattTestProportion error", e);
            throw UedmErrorCodeOtherUtil.otherTemporaryError(e.getMessage());
        }
    }
}
