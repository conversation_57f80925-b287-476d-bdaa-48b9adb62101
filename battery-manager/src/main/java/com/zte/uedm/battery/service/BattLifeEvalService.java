package com.zte.uedm.battery.service;

import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.consts.BattEvalFailedReasonEnum;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.controller.battlife.po.BattLifeRatedCfgPo;
import com.zte.uedm.battery.controller.battlife.po.BattLifeSpecificCfgPo;
import com.zte.uedm.battery.domain.BattDischargeDepthCycleTimesDomain;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.domain.BatteryHisDataDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.service.battlife.BattLifeAIConfigService;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.BatchUtils;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.*;

@Slf4j
public abstract class BattLifeEvalService {

    protected static final List<String> battSmpIdList;

    public static final String BATT_AI_LIFE_OVER_TIME_CACHE = "Batt-ai-over-time:";

    @Resource
    protected DateTimeService dateTimeService;

    @Resource
    protected ConfigurationService configurationService;

    @Resource
    protected MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Resource
    protected ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Resource
    protected BattLifeEvalDomain battLifeEvalDomain;

    @Resource
    protected BatteryHisDataDomain batteryHisDataDomain;

    @Resource
    protected BattTypeDomain battTypeDomain;

    @Resource
    protected BattDischargeDepthCycleTimesDomain battDischargeDepthCycleTimesDomain;

    @Resource
    protected JsonService jsonService;

    @Resource(name = "pmaServiceUtils")
    protected PmaServiceUtils pmaService;

    @Autowired
    protected RedisService redisService;

    @Autowired
    private DataRedis dataRedis;

    @Resource
    protected BattLifeAIConfigService battLifeAIConfigService;


//    @Value("${battery.cycles.lead_acid}")
//    private Integer pbacCycleTimes;
//
//    @Value("${battery.cycles.lithium_iron}")
//    private Integer lfpCycleTimes;

    protected static final String ZERO = " 00:00:00";

    static {
        battSmpIdList = Arrays.asList(BATTERY_SMPID_ACCUM_DISCHARGE_TIMES,
                BATTERY_SMPID_ACCUM_CYCLE_TIMES);
    }

    /**
     * 数据分发处理
     */
    public abstract void dispatchHandle(List<MoObjectConfiguration> moObjectConfigurationList, Map<String, BattTypeEnum> battTypeEnumMap) throws UedmException;

    public void evalBattLife() throws Exception {
        // 获取所有的电池
        List<MoObjectConfiguration> battList = configurationManagerRpcImpl.getMonitorObjectListWhenIdsBig(Collections.emptyList(), MocOptional.BATTERY.getId());
        if (CollectionUtils.isNotEmpty(battList)) {
            List<BattTypeBean> battTypeBeanList = battTypeDomain.getBatteryTypeByMoIdFilterLoop();
            Map<String, BattTypeEnum> battTypeEnumMap = battTypeBeanList.stream().filter(bean -> StringUtils.isNotBlank(bean.getId())&&bean.getBattType()!=null)
                    .collect(Collectors.toMap(BattTypeBean::getId, BattTypeBean::getBattType, (key1,key2)->key2));
            dispatchHandle(battList, battTypeEnumMap);
        }
    }

    public BatteryLifeEvalAgg getDischargeDepthCycleTimesMap(List<MoObjectConfiguration> list, BattTypeEnum battTypeEnum) throws UedmException {
        BatteryLifeEvalAgg batteryLifeEvalAgg = new BatteryLifeEvalAgg();
        Map<String, List<BatteryUseLifeBeanVO>> map = new HashMap<>();
        Map<String, Integer> theoreticalLifeMap = new HashMap<>();
        List<String> battIdList = list.stream()
                .map(MoObjectConfiguration::getId)
                .collect(Collectors.toList());
        //考虑到电池类型可能改变，电池放电深度循环次数配置表里的数据需要更新，不能直接取表里的数据
//        // 查询电池放电深度循环次数配置表
//        List<BattDischargeDepthCycleTimesBean> idList = battDischargeDepthCycleTimesDomain.selectBybattIdList(battIdList);
//        for (BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean : idList) {
//            String dischargeDepthCycleTimes = battDischargeDepthCycleTimesBean.getDischargeDepthCycleTimes();
//            List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = jsonService.jsonToObject(dischargeDepthCycleTimes, ArrayList.class, BatteryUseLifeBeanVO.class);
//            map.put(battDischargeDepthCycleTimesBean.getBattId(), batteryUseLifeBeanVOList);
//            theoreticalLifeMap.put(battDischargeDepthCycleTimesBean.getBattId(), battDischargeDepthCycleTimesBean.getTheoreticalLife());
//        }
//
//        List<String> existIdList = idList.stream().map(BattDischargeDepthCycleTimesBean::getBattId)
//                .collect(Collectors.toList());
//        // 未在表中有记录的数据
//        List<String> notExistIdList = battIdList.stream()
//                .filter(item -> !existIdList.contains(item))
//                .collect(Collectors.toList());
        // 记录默认数据
        if (CollectionUtils.isNotEmpty(battIdList)) {
            List<BattDischargeDepthCycleTimesBean> dischargeDepthCycleTimesList = battDischargeDepthCycleTimesDomain
                    .selectByIdsAndType(battIdList, battTypeEnum.getCode());
            for (BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean : dischargeDepthCycleTimesList) {
                String dischargeDepthCycleTimes = battDischargeDepthCycleTimesBean.getDischargeDepthCycleTimes();
                List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = jsonService.jsonToObject(dischargeDepthCycleTimes, ArrayList.class, BatteryUseLifeBeanVO.class);
                map.put(battDischargeDepthCycleTimesBean.getBattId(), batteryUseLifeBeanVOList);
                theoreticalLifeMap.put(battDischargeDepthCycleTimesBean.getBattId(), battDischargeDepthCycleTimesBean.getTheoreticalLife());
            }
        }
        batteryLifeEvalAgg.setUseLifeMap(map);
        batteryLifeEvalAgg.setTheoreticalLifeMap(theoreticalLifeMap);
        return batteryLifeEvalAgg;
    }

    public void handle(List<MoObjectConfiguration> battList, BattTypeEnum battTypeEnum, BatteryLifeEvalAgg batteryLifeEvalAgg) throws UedmException {
//        List<StandardPointBean> allStandardPointBean = batteryHisDataDomain.getAllStandardPointBean();
//        List<StandardPointBean> standardPointBeanList = allStandardPointBean.stream()
//                .filter(standardPointBean -> battSmpIdList.contains(standardPointBean.getId())).collect(Collectors.toList());
        // 解决性能问题
        List<StandardPointEntity> standardPointBeanList = configurationManagerRpcImpl.getStandardPointByIds(battSmpIdList, MocOptional.BATTERY.getId());
        List<String> moIds = battList.stream().map(bean -> bean.getId()).collect(Collectors.toList());
        List<String> smpIds = standardPointBeanList.stream().map(bean -> bean.getId()).collect(Collectors.toList());
        Map<String, Map<String,List<HistoryAiBean>>> historyData = getBatchHisData(moIds,smpIds);
        log.info("historyData is {}", historyData);//todo debug
        for (MoObjectConfiguration moObject : battList) {
            Integer floatingLife = null;
            Double cycleLife = null;
            BattLifeEvalDto battLifeEvalDto = fillUpBaseInfo(moObject, battTypeEnum);
            battLifeEvalDto.setUnknownReasonList(new ArrayList<>());
            try {

                // 设置SOH
                battLifeEvalDto.setSoh(getStandardPointValue(moObject.getId(), BATTERY_SMPID_HEALTH, Float.class));

                // 获取电池启用日期
                Integer battRunDays = battLifeEvalDomain.getBattRunDays(moObject.getId(), battLifeEvalDto);
                battLifeEvalDto.setOperatingDays(battRunDays);

                // 计算电池循环寿命
                cycleLife = calculateCycleLife(moObject, battLifeEvalDto, batteryLifeEvalAgg, standardPointBeanList, historyData);

                // 计算电池浮动寿命
                floatingLife = calculateFloatingLife(battLifeEvalDto, batteryLifeEvalAgg, moObject.getId());

                // 设置剩余循环次数
                Integer leftCycleTimes = getLeftCycleTimes(battLifeEvalDto);
                battLifeEvalDto.setLeftCycleTimes(leftCycleTimes);

                Integer life = getMin(battLifeEvalDto, cycleLife, floatingLife);
                battLifeEvalDto.setLife(life);
                if (life == null) {
                    String join = StringUtils.join(battLifeEvalDto.getUnknownReasonList(), ",");
                    battLifeEvalDto.setUnknownReason(join);
                }
            } catch (Exception e) {
                log.error("eval battery life error", e);
            } finally {
                log.info("record data: [{}]", battLifeEvalDto);
                battLifeEvalDomain.recordEvalByDay(battLifeEvalDto);
                battLifeEvalDomain.recordEvalByMonth(battLifeEvalDto);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends Number> T getStandardPointValue(String batteryId, String smpId, Class<T> clazz) {
        try {
            Map<String, Map<String, String>> map = dataRedis.selectRealData(batteryId, Collections.singletonList(smpId));
            Map<String,String> ratedCapacityTemp =  Optional.ofNullable(map.get(smpId)).orElse(new HashMap<>());
            String ratedCapacity = ratedCapacityTemp.get("value");
            if (ratedCapacity == null || clazz == null) {
                return null;
            }
            return clazz.getDeclaredConstructor(String.class).newInstance(ratedCapacity);

        } catch (Exception e) {
            return null;
        }
    }

    public Integer getLeftCycleTimes(BattLifeEvalDto battLifeEvalDto) {
        Integer accumCycleTimes = battLifeEvalDto.getAccumCycleTimes();
        Integer ratedCycleTimes = battLifeEvalDto.getRatedCycleTimes();
        Integer leftCycleTimes =  (null != accumCycleTimes && null != ratedCycleTimes) ? ratedCycleTimes - accumCycleTimes : null;
        return null == leftCycleTimes ? null : leftCycleTimes < 0 ? 0 : leftCycleTimes;
    }

    public BattLifeEvalDto fillUpBaseInfo(MoObjectConfiguration moObject, BattTypeEnum battTypeEnum) {
        BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
        battLifeEvalDto.setId(moObject.getId());
        battLifeEvalDto.setName(moObject.getName());
        battLifeEvalDto.setPathNames(moObject.getPath());
        battLifeEvalDto.setPathIds(moObject.getPathId());
        battLifeEvalDto.setBattType(battTypeEnum.getCode());
        battLifeEvalDto.setCreator("admin");
        // 使用专属或者通用配置的值
        Map<String, Object> configMap = queryRatedLifeAndCycles(moObject.getId(),battTypeEnum);
        Integer cycleTimes = (Integer) configMap.get("ratedCyclesNumber");
        battLifeEvalDto.setRatedCycleTimes(cycleTimes);
        Integer ratedUsefulLife = null == configMap.get("ratedUsefulLife") ? null : ((Integer) configMap.get("ratedUsefulLife"));
        battLifeEvalDto.setRatedUsefulLife(ratedUsefulLife);

        return battLifeEvalDto;
    }

    public Map<String, Object> queryRatedLifeAndCycles(String batteryId, BattTypeEnum battTypeEnum) {
        // 先查询专属表，如果专属表没有数据，则查询通用表
        Map<String, Object> result = new HashMap<>();
        Integer ratedUsefulLife = null;
        Integer ratedCycleTimes = null;
        try {
            BattLifeSpecificCfgPo battLifeSpecificCfgPo = battLifeAIConfigService.selectById(batteryId);
            if (null == battLifeSpecificCfgPo) {
                BattLifeRatedCfgPo battLifeRatedCfgPo = battLifeAIConfigService.selectRatedCfg();
                if (null == battLifeRatedCfgPo) {
                    return result;
                }
                else {
                    // 先判断锂电，后判断是铅酸还是未知
                     ratedCycleTimes = battTypeEnum == BattTypeEnum.LFP ? battLifeRatedCfgPo.getRatedLiIonCycleTimes()
                            : battTypeEnum == BattTypeEnum.PBAC ? battLifeRatedCfgPo.getRatedLeadAcidCycleTimes() : null;
                     ratedUsefulLife = battTypeEnum == BattTypeEnum.LFP ? battLifeRatedCfgPo.getRatedLiIonLife()
                            : battTypeEnum == BattTypeEnum.PBAC ? battLifeRatedCfgPo.getRatedLeadAcidLife() : null;

                }
            } else {
                ratedCycleTimes = battLifeSpecificCfgPo.getRatedCycleTimes();
                ratedUsefulLife = battLifeSpecificCfgPo.getRatedLife();

            }
        } catch (UedmException e) {
            log.error("get rated settings error", e);
            return new HashMap<>();
        }
        result.put("ratedCyclesNumber", ratedCycleTimes);
        Integer life2Month = null == ratedUsefulLife ? null : ratedUsefulLife * 12;
        result.put("ratedUsefulLife", life2Month);
        return result;
    }

    /**
     * 获取电池循环寿命
     */
    public Double calculateCycleLife(MoObjectConfiguration moObjectConfiguration,
                                     BattLifeEvalDto battLifeEvalDto,
                                     BatteryLifeEvalAgg batteryLifeEvalAgg,
                                     List<StandardPointEntity> standardPointBeanList,
                                     Map<String, Map<String,List<HistoryAiBean>>> historyMapMap) throws UedmException {
        Double cycleLife = null;
        try {
            String currentTime = dateTimeService.getCurrentTime();
            String yesterday = TimeUtils.addDays(currentTime, -1);
            battLifeEvalDto.setEvalTime(TimeUtils.dateFormat(currentTime, DateTypeConst.DATE_FORMAT_2));
            String startDate = TimeUtils.dateFormat(yesterday, DateTypeConst.DATE_FORMAT_2) + ZERO;
            String nextDay = TimeUtils.addDays(startDate, 1);
            String endDate = TimeUtils.dateFormat(nextDay, DateTypeConst.DATE_FORMAT_2) + ZERO;

            MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
            monitorObjectBean.setId(moObjectConfiguration.getId());

            // 获取历史数据
            Map<String, List<HistoryAiBean>> hisDataMap = historyMapMap.getOrDefault(moObjectConfiguration.getId(), new HashMap<>());
            // 获取有效放电容量总量
            Double battDischargeCap = battLifeEvalDomain.calcBattDischargeCap(moObjectConfiguration.getId(), startDate, endDate, battLifeEvalDto);
            if (Objects.isNull(battDischargeCap)) {
                battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_HISTORY_DATA_FAILED.getCode());
            }
            battLifeEvalDto.setAccumDischargeCap(battDischargeCap);

            if (hisDataMap != null) {
                log.debug("hisDataMap is {}, moid is {}", hisDataMap, moObjectConfiguration.getId());
                Batt1720Algorithm algorithm = new Batt1720Algorithm();
                // 获取蓄电池纳入监控的电池运行总天数
                algorithm.setBattRunDays(battLifeEvalDto.getOperatingDays());

                fillUpBattEvalDto(moObjectConfiguration.getId(),hisDataMap, battLifeEvalDto, algorithm);

                algorithm.setAccumDischargeCap(battDischargeCap);
                Double avgDischarge = algorithm.calcAvgDischarge();
                battLifeEvalDto.setAvgDischargeDepth(avgDischarge);

                Integer cycleTimes = getCycleTimes(moObjectConfiguration.getId(), avgDischarge, batteryLifeEvalAgg.getUseLifeMap(), battLifeEvalDto);
                algorithm.setCycleTimes(cycleTimes);
                battLifeEvalDto.setCycleTimes(cycleTimes);
                cycleLife = algorithm.calcCycleLife();
                log.info("calculate cycle life success, battId: [{}], floating life: [{}]", moObjectConfiguration.getId(), cycleLife);
            }
        } catch (Exception e) {
            log.error("calculate cycle life error.",e);
        }
        return cycleLife;
    }

    /**
     * 获取电池浮动寿命
     */
    public Integer calculateFloatingLife(BattLifeEvalDto battLifeEvalDto, BatteryLifeEvalAgg batteryLifeEvalAgg, String battId) throws UedmException {
        Integer result = null;
        try {
            Integer life = batteryLifeEvalAgg.getTheoreticalLifeMap().get(battId);
            Optional.ofNullable(life)
                    .orElseThrow(() -> {
                        battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_FLOATING_LIFE_FAILED.getCode());
                        return new UedmException(-1, "get theoreticalLife error.");
                    });
            Optional.ofNullable(battLifeEvalDto.getOperatingDays())
                    .orElseThrow(() -> {
                        battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_FLOATING_LIFE_FAILED.getCode());
                        return new UedmException(-1, "get theoreticalLife error.");
                    });
            String months = BigDecimal.valueOf(battLifeEvalDto.getOperatingDays())
                    .divide(BigDecimal.valueOf(30), 0, RoundingMode.HALF_UP).toPlainString();
            // 当（当前日期 - 电池启用日期) 很大时，会出现理论寿命 -（当前日期 - 电池启用日期）< 0，导致电池浮充寿命小于0，此时电池浮充寿命设置为0
            int floatLife = life - Integer.parseInt(months);
            result = Math.max(floatLife, 0);
            log.info("calculate floating life success, battId: [{}], floating life: [{}]", battId, result);
        } catch (Exception e) {
            log.error("calculate floating life error.");
        }
        return result;
    }

    public Integer getMin(BattLifeEvalDto battLifeEvalDto, Double cycleLife, Integer floatLife) throws UedmException {
        Integer min = null;
        if (cycleLife == null && null == floatLife) {
            battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.EVALUATE_FAILED.getCode());
            throw new UedmException(-1,
                    BattEvalFailedReasonEnum.EVALUATE_FAILED.getEnglishDesc());
        } else if (cycleLife == null || null == floatLife) {
            min = cycleLife == null ? floatLife : cycleLife.intValue();
        } else {
            min = Math.min(floatLife, cycleLife.intValue());
        }
        return min;
    }

    public void fillUpBattEvalDto(String moId,Map<String, List<HistoryAiBean>> hisDataMap, BattLifeEvalDto battLifeEvalDto, Batt1720Algorithm algorithm) throws UedmException {
        // 累计放电次数
        Integer accumDischargeTimes = getIntegerCurrValue(hisDataMap, BATTERY_SMPID_ACCUM_DISCHARGE_TIMES);
        if (Objects.nonNull(accumDischargeTimes)) {
            battLifeEvalDto.setAccumDischargeTimes(accumDischargeTimes);
            algorithm.setAccumDischargeTimes(accumDischargeTimes);
        } else {
            battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_BATT_SMPID_ACCUM_DISCHARGE_TIMES_FAILED.getCode());
        }
        // 额定容量
        Double rateCapacity = getDoubleCurrValue(moId,BATTERY_SMPID_RATED_CAPACITY);
        if (Objects.nonNull(rateCapacity)) {
            battLifeEvalDto.setRatedCap(rateCapacity);
            algorithm.setRatedCapacity(rateCapacity);
        } else {
            battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_BATT_SMPID_RATED_CAPACITY_FAILED.getCode());
        }
        // 累计循环次数
        Integer accumCycleTimes = getIntegerCurrValue(hisDataMap, BATTERY_SMPID_ACCUM_CYCLE_TIMES);
        if (Objects.nonNull(accumCycleTimes)) {
            battLifeEvalDto.setAccumCycleTimes(accumCycleTimes);
            algorithm.setAccumCycleTimes(accumCycleTimes);
        } else {
            battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_BATT_SMPID_ACCUM_CYCLE_TIMES_FAILED.getCode());
        }
    }

    private Double getDoubleCurrValue(String moId ,String smpId) throws UedmException {
        Map<String, Map<String, String>> map = dataRedis.selectRealData(moId, Collections.singletonList(smpId));
        Map<String,String> ratedCapacity =  Optional.ofNullable(map.get(smpId)).orElse(new HashMap<>());
        String value = ratedCapacity.get("value");
        return value==null?null :Double.valueOf(value);
    }
    
    private Integer getIntegerCurrValue(Map<String, List<HistoryAiBean>> hisDataMap, String smpId)
    {
        Integer res = null;
        List<HistoryAiBean> historyAiBeanList = Optional.ofNullable(hisDataMap.get(smpId)).orElse(null);
        if (historyAiBeanList!=null && historyAiBeanList.size() > 0 && historyAiBeanList.get(0) != null && historyAiBeanList.get(0).getCurValue() != null)
        {
            res = Integer.valueOf(historyAiBeanList.get(0).getCurValue());
        }

        return res;
    }
    
    /**
     * 获取放电深度对应的循环次数
     */
    public Integer getCycleTimes(String id, Double avgDischarge, Map<String, List<BatteryUseLifeBeanVO>> dischargeDepthCycleTimesMap,
                                 BattLifeEvalDto battLifeEvalDto) throws UedmException {
        log.info("avgDischarge: [{}]", avgDischarge);
        if (MapUtils.isNotEmpty(dischargeDepthCycleTimesMap)) {
            List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = dischargeDepthCycleTimesMap.get(id);
            if (CollectionUtils.isNotEmpty(batteryUseLifeBeanVOList)) {
                return batteryUseLifeBeanVOList.stream()
                        .filter(item -> item.getMax() >= avgDischarge && item.getMin() < avgDischarge)
                        .map(BatteryUseLifeBeanVO::getCycleTimes)
                        .findFirst()
                        .orElseThrow(() -> {
                            battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_CYCLE_TIMES_FAILED.getCode());
                            return new UedmException(-1, "get cycleTimes error.");
                        });
            }
        }
        battLifeEvalDto.getUnknownReasonList().add(BattEvalFailedReasonEnum.GET_CYCLE_TIMES_FAILED.getCode());
        throw new UedmException(-1, "get cycleTimes error.");
    }
    

    /**
     * 获取历史数据(批量)
     */
    public Map<String, Map<String,List<HistoryAiBean>>> getBatchHisData(List<String> moIds, List<String> smpIds) throws UedmException
    {
        if (CollectionUtils.isEmpty(moIds) || CollectionUtils.isEmpty(smpIds))
        {
            return new HashMap<>();
        }
        try {
            List<HistoryAiBean> data = new ArrayList<>();
            String currentTime = dateTimeService.getCurrentTime();
            String yesterday = TimeUtils.addDays(currentTime, -1);
            String startDate = TimeUtils.dateFormat(yesterday, DateTypeConst.DATE_FORMAT_2) + ZERO;
            String nextDay = TimeUtils.addDays(startDate, 1);
            String endDate = TimeUtils.dateFormat(nextDay, DateTypeConst.DATE_FORMAT_2) + ZERO;
            List<SpIdDto> last = smpIds.stream().map(smpid -> new SpIdDto(smpid, GlobalConstants.LAST,null)).collect(Collectors.toList());
            BatchUtils.doInBatch(1000, moIds, (item) -> {
                List<HistoryAiBean> idBatch = new ArrayList<>();
                try
                {
                    idBatch = pmaService.selectDataByCondition(item,MocOptional.BATTERY.getId(),last,startDate,endDate,"collect","day");
                }
                catch (Exception e)
                {
                    log.warn("selectDataByCondition failed!", e);
                    throw new RuntimeException();
                }
                data.addAll(idBatch);
            });

            return data.stream().filter(bean -> !StringUtils.isAnyBlank(bean.getSmpId(),bean.getResId()))
                    .collect(Collectors.groupingBy(HistoryAiBean::getResId, Collectors.groupingBy(HistoryAiBean::getSmpId)));
        }
        catch (Exception e)
        {
            log.warn("getBatchHisData error!", e);
            throw new UedmException(-1, "getBatchHisData error!");
        }
    }
}
