package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailPo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PeakShiftTaskDetailMapper
{

    /**
     * takIds and deviceIds查询任务详情表
     * @param taskIds
     * @param deviceIds
     * @return
     */
    public List<PeakShiftTaskDetailPo> selectByCondition(@Param("taskIds")  List<String> taskIds, @Param("deviceIds")  List<String> deviceIds);

    /**
     * 根据任务ID查询设备详情
     * @param taskId
     * @return
     */
    public List<PeakShiftTaskDetailPo> selectByTaskId(@Param("taskId")  String taskId,@Param("deviceStatus")List<String> deviceStatus);

    /**
     * 新增错峰任务设备详情表
     * @param beans
     * @return
     * @
     */
    Integer insertPeakShiftTaskDetail(@Param("beans") List<PeakShiftTaskDetailPo> beans) ;

    /**
     * 根据任务ID删除错峰任务详情
     * @param taskId
     * @return
     */
    Integer deleteByTaskId(@Param("taskId") String taskId);

    /**
     *
     * @param taskId
     * @return
     * @
     */
    Integer updateByTaskIdAndState(@Param("taskId")String taskId ,@Param("status")String status ) ;

    List<String> checkDeviceState(@Param("deviceIds")List<String> deviceIds,@Param("status")String status) ;

    PeakShiftDeviceChildBeanVo selectFileInfo(@Param("id") String id, @Param("status") String status) ;

    /**
     * 注：如果同一设备ID，且更新时间相同，则该设备ID会出现多条数据结果
     * 查询设备最新的、状态为status的文件ID与更新时间
     * @param ids
     * @param status
     * @return
     * @
     */
    List<PeakShiftDeviceChildBeanVo> selectFileInfoList(@Param("ids") List<String> ids, @Param("status") String status) throws UedmException;

    /**
     * 查询ids中状态为status的
     * @param ids
     * @param status
     * @return
     * @
     */
    List<String> selectOptionalDeviceStatus(@Param("ids") List<String> ids, @Param("status") String status) throws UedmException;

    Integer updateDeviceStatus(@Param("detailId") String detailId, @Param("status")String status) ;

    /**
     * 开机时更新待下发状态为失败
     * @param status
     * @param oldStatus
     * @return
     * @
     */
    Integer updateInProgressDevice(@Param("status")String status, @Param("oldStatus")String oldStatus) throws UedmException;

    /**
     * 开机时更新待下发状态为失败
     * @param taskId
     * @param status
     * @return
     * @
     */
    Integer updateDeviceStatusByTaskId(@Param("taskId")String taskId, @Param("deviceId")String deviceId, @Param("status")String status, @Param("currentTime")String currentTime);

    /**
     * 选出最新的下发时间，根据设备ID
     * @param deviceId
     * @return
     */
    PeakShiftDeviceTaskBo selectLatestTaskByDeviceId(String deviceId) ;


    /**
     * 根据条件选出设备相关信息，连带任务信息一起
     * @param bean 查询bean
     * @return
     */
    List<PeakShiftDeviceTaskBean> selectDeviceTaskInfoByCondition(PeakShiftDeviceTaskVo bean) ;

    /**
     * 状态翻转为下发中
     * @param taskId
     * @param currentTime
     * @return
     * @
     */
    Integer updateStatusToProgress(@Param("taskId")String taskId ,@Param("ids") List<String> ids,@Param("currentTime")String currentTime );

    /**
     * 根据taskId和status查询设备id
     * @param taskId
     * @param status
     * @return
     * @
     */
    List<String> selectlDeviceByTaskIdAndStatus(@Param("taskId")String taskId ,@Param("status")String status) ;

    /**
     *统计各个状态的设备数量
     * @param taskId
     * @return
     */
    DeviceStatusCountBean selectCountGroupByStatus(@Param("taskId")String taskId);

    /**
     * 查询该任务下设备总数
     * @param taskId
     * @return
     */
    Integer selectTotalByTaskId(@Param("taskId")String taskId);


    List<PeakShiftTaskDetailPo> selectSuccessDevices(@Param("taskId")String taskId);

    /**
     * 根据新增条件选出设备相关信息
     * @param bean 查询bean
     * @return
     */
    List<PeakShiftDeviceTaskBean> selectDeviceTaskInfoByNewCondition(PeakShiftDeviceTaskVo bean);

    /**
     * 查询任务id
     * @param detailId
     * @return
     */
    String selectTaskIdByDetailId(String detailId);

    String getLogicGroupByTemplateStrategyId(String templateStrategyId);

    List<PendingBean> queryAllPendingDevice();

    Integer updateByTimeAndState(@Param("timeout")String timeout ,@Param("status")String status);

    List<String> selectTaskIds(@Param("timeout")String timeout ,@Param("status")String status);


    void updateTaskLogId(@Param("taskId")String taskId, @Param("logId") String logId, @Param("deviceId") String deviceId);

    void updateFtpPath(@Param("taskId") String taskId, @Param("fileId") String fileId, @Param("ftpPtah") String ftpPtah);

    String selectAnyTaskIdByFileId(@Param("fileId") String fileId);
}