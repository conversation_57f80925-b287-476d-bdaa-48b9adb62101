package com.zte.uedm.battery.a_domain.safe;


import com.zte.uedm.battery.a_interfaces.safe.web.dto.BatteryBatchOperationResponse;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;

import java.util.List;

public interface BatteryProtectService
{
    /**
     * 设置设防状态
     * @param ids
     * @param protectState
     * @param msg
     * @param serviceBaseInfoBean
     * @return
     */
    BatteryBatchOperationResponse setProtectState(List<String> ids,
                                                  String protectState,
                                                  String msg,
                                                  ServiceBaseInfoBean serviceBaseInfoBean) throws Exception;
}
