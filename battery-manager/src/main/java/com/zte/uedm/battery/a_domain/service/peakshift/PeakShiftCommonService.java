package com.zte.uedm.battery.a_domain.service.peakshift;


import com.alibaba.fastjson.JSON;
import com.zte.oes.dexcloud.redis.redisson.service.RedissonException;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.OriginalDataBean;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailBcuaEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.SouthApplySetStatusDataBean;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskWithDevicePo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.SeasonStrategyForTemplateVo;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.bean.peak.PeakShiftStrategyDetailBean;
import com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo;
import com.zte.uedm.battery.consts.DateTypeConst;
import com.zte.uedm.battery.domain.BatteryCurrentStorageDomin;
import com.zte.uedm.battery.domain.impl.BackupPowerThresholdDetailDomainImpl;
import com.zte.uedm.battery.enums.peak.PeakShiftingStrategyEnum;
import com.zte.uedm.battery.enums.peak.TemplateCodeEnum;
import com.zte.uedm.battery.mapper.PeakShiftDeviceFileMapper;
import com.zte.uedm.battery.mapper.PeakShiftMapper;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.consts.TimeFormatConstants;
import com.zte.uedm.common.enums.asset.BatteryTypeEnums;
import com.zte.uedm.common.enums.peak.CycleModelEnum;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import com.zte.uedm.service.mp.api.adapter.dto.CollectorAndAdapterPointDto;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERYSET_SMPID_STATE;

@Slf4j
public abstract class PeakShiftCommonService {
    @Resource
    private AdapterPointDataService adapterPointDataService;
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Resource
    private DeviceCacheManager deviceCacheManager;
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private BatteryCurrentStorageDomin batteryCurrentStorageDomin;
    @Autowired
    private BackupPowerThresholdDetailDomainImpl backupPowerThresholdDetailDomainImpl;
    @Autowired
    private BatteryAttributeUtils batteryAttributeUtils;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private StandardDataService standardDataService;
    @Autowired
    private PeakShiftDeviceFileMapper peakShiftDeviceFileMapper;

    @Autowired
    private PeakShiftMapper peakShiftMapper;


    public abstract TemplateStrategyDetailEntity getTemplateDetail(TemplateStrategyDetailEntity templateStrategyDetailBo) throws UedmException;

    public abstract void setPeakStartStopRecord(SouthApplySetStatusDataBean southDataBean) throws com.zte.uedm.common.exception.UedmException;

    public abstract void processingDeviceData(SouthApplySetStatusDataBean southDataBean) throws Exception;

    public abstract TemplateStrategyAddDto processStrategy(SeasonStrategyForTemplateVo strategy, DevicePeakCacheInfoBean devicePeakCacheInfoBean, BigDecimal dischargeDuration, BigDecimal chargeDuration);

    public abstract boolean isEnablePeak(List<String> points);
    public abstract boolean isEnablePeak2(List<String> points, Map<String, Map<String, AdapterPointDataVo>> map, String deviceType, String moduleId);
    public abstract void setRunningStatus(DevicePeakCacheInfoBean devicePeakCacheInfoBean, Map<String, String> deviceMap,
                                          Map<String, String> linkStatusMap, Map<String, AdapterPointDataVo> runningMap) throws Exception;
    public abstract List<RemoteControlBean> buildDeviceEnablePeakBean(String userName, PeakShiftDeviceEnableDto peakShiftDeviceEnableDto, CollectorEntity bean) throws com.zte.uedm.common.exception.UedmException;

    public abstract Pair<String, String> findExecStatusAndCurrPeakStrategy(String deviceId, String batteryStatus,
                                                                           PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws com.zte.uedm.common.exception.UedmException;

    public abstract String getBattCapacityPoint();

    public abstract void stopPeakShiftingStrategy(CollectorEntity adapterId, PeakShiftTaskWithDevicePo peakShiftTaskWithDevicePo);

    public abstract String getBatteryStatus(String collectorId,
                                            PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws com.zte.uedm.common.exception.UedmException;

    public abstract TemplateStrategyDetailEntity getLatestStrategy(String collectorId, ServiceBaseInfoBean serviceBean) throws com.zte.uedm.common.exception.UedmException;

    public String getCommonBatteryStatus(String collectorId, PeakShiftMonitorBaseDataBean peakShiftMonitorBaseDataBean) throws com.zte.uedm.common.exception.UedmException {

        // 电池组状态
        Map<String, Map<String, Object>> battPackStatusMap = peakShiftMonitorBaseDataBean.getBattPackStatusMap();
        // 设备电池组关联关系
        Map<String, List<ResourceCollectorRelationEntity>> battpackMap = peakShiftMonitorBaseDataBean.getBattpackMap();
        List<ResourceCollectorRelationEntity> battpackList = MapUtils.emptyIfNull(battpackMap).get(collectorId);

        if (battpackList != null && !battpackList.isEmpty()) {
            // BCUA和CSU5均为锂电，关联同一个设备的电池组，始终为同样的充放电状态，因此取一个即可
            ResourceCollectorRelationEntity battpackBean = battpackList.get(0);
            String id = battpackBean.getResourceId();

            /* Started by AICoder, pid:95eacc990baa79c143f009e820f0090113c65c30 */
            Map<String, Object> standPointMap = MapUtils.emptyIfNull(battPackStatusMap).get(id);
            if (standPointMap == null || standPointMap.get(BATTERYSET_SMPID_STATE) == null) {
                log.warn("getBatteryStatus battery set status not found {}", id);
                return "";
            }
            /* Ended by AICoder, pid:95eacc990baa79c143f009e820f0090113c65c30 */

            Object pointValueObj = standPointMap.get(BATTERYSET_SMPID_STATE);
            Map<String, String> battpackState = jsonService.jsonToObject(jsonService.objectToJson(pointValueObj), Map.class, String.class, String.class);

            String batteryStatus = battpackState.get("value");
            if (!StringUtils.isBlank(batteryStatus)) {
                // 取整处理
                double batteryStatusDouble = Double.parseDouble(batteryStatus);
                int batteryStatusInt = (int) batteryStatusDouble;
                return String.valueOf(batteryStatusInt);
            }
        }

        return "";
    }

    public abstract PeakShiftMonitorBaseDataBean prepareBaseData(List<DevicePeakCacheInfoBean> list) throws com.zte.uedm.common.exception.UedmException;

    public void setBatterySetStatusMap(Set<String> collectorIdList,PeakShiftMonitorBaseDataBean baseDataBean) throws com.zte.uedm.common.exception.UedmException {
        // 获取采集器关联的电池组
        Map<String, List<ResourceCollectorRelationEntity>> map ;
        try {
            map = resourceCollectorRelationCacheManager.getCollectorRelatedDevice(collectorIdList, MocOptional.BATTERY_SET.getId());
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            log.error("PeakShiftMonitorServiceImpl setCurrentStrategyAndExecStatus get cache error", e);
            throw new com.zte.uedm.common.exception.UedmException(e.getErrorId(), e.getMessage());
        }
        log.debug("setCurrentStrategyAndExecStatus map={}", map);

        List<String> battPackIds = org.apache.commons.collections4.CollectionUtils.emptyIfNull(map.values())
                .stream()
                .flatMap(Collection::stream)
                .map(ResourceCollectorRelationEntity::getResourceId)
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        // 获取电池组状态标准测点
        Map<String, Map<String, Object>> battPackStatusMap ;
        try {
            battPackStatusMap = standardDataService.batchQueryByResourceIdAndStandPointId(battPackIds, Collections.singletonList(BATTERYSET_SMPID_STATE));
        } catch (RedissonException e) {
            log.error("PeakShiftMonitorServiceImpl setCurrentStrategyAndExecStatus get standard point error", e);
            throw new com.zte.uedm.common.exception.UedmException(-1, "get standard point error");
        }

        // 设置关联的电池组id
        baseDataBean.setBattpackMap(map);
        // 设置电池组状态标准测点值
        baseDataBean.setBattPackStatusMap(battPackStatusMap);
    }

    /* Started by AICoder, pid:i8ef5a0f97u000c140be0bc14070d54bb537a120 */
    public void setDirectStrategy(Set<String> collectorIdList, PeakShiftMonitorBaseDataBean baseDataBean) {
        // 获取设备下发的策略
        List<PeakShiftDeviceStrategyBean> peakShiftDeviceStrategyBeans = peakShiftDeviceFileMapper.selectDeviceStrategyBeanByDeviceIds(new ArrayList<>(collectorIdList));
        if (CollectionUtils.isEmpty(peakShiftDeviceStrategyBeans)) {
            log.error("setDirectStrategy no found strategy for {} devices", collectorIdList.size());
            return;
        }

        // 按照DeviceId分组，并筛选出每个设备最近一次下发的策略
        Map<String, PeakShiftDeviceStrategyBean> latestStrategyMap = peakShiftDeviceStrategyBeans.stream()
                .filter(bean -> StringUtils.isNotBlank(bean.getDeviceId()) && bean.getVersion() != null)
                .collect(Collectors.toMap(
                        PeakShiftDeviceStrategyBean::getDeviceId,
                        Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(PeakShiftDeviceStrategyBean::getVersion)),
                        ConcurrentHashMap::new));

        if (latestStrategyMap.isEmpty()) {
            log.warn("No valid strategies found after filtering");
            return;
        }

        // 批量获取策略详情
        List<String> strategyIds = new ArrayList<>(latestStrategyMap.values()).stream()
                .map(PeakShiftDeviceStrategyBean::getIntervalStrategyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<PeakShiftDeviceStrategyDetailBean> detailList = peakShiftDeviceFileMapper.findStrategyDetailById(strategyIds);
        if (CollectionUtils.isEmpty(detailList)) {
            log.error("setDirectStrategy not found details for strategy IDs: {}", JSON.toJSONString(strategyIds));
            return;
        }

        // 构建策略详情映射
        Map<String, PeakShiftDeviceStrategyDetailBean> detailMap = detailList.stream()
                .collect(Collectors.toMap(PeakShiftDeviceStrategyDetailBean::getId, Function.identity()));

        // 构建最终的直接策略详情映射
        Map<String, PeakShiftDeviceStrategyDetailBean> directStrategyDetailMap = latestStrategyMap.entrySet().stream()
                .filter(entry -> detailMap.containsKey(entry.getValue().getIntervalStrategyId()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> detailMap.get(entry.getValue().getIntervalStrategyId())));

        baseDataBean.setDirectStrategyDetailMap(directStrategyDetailMap);
    }
    /* Ended by AICoder, pid:i8ef5a0f97u000c140be0bc14070d54bb537a120 */

    public void setCSUStrategy(Set<String> collectorIdList, PeakShiftMonitorBaseDataBean baseDataBean) {

        // 获取设备下发的策略
        List<PeakShiftCsu5StrategyPo> strategyList = peakShiftMapper.selectCsuAllStrategyByDeviceIds(new ArrayList<>(collectorIdList));
        if (CollectionUtils.isEmpty(strategyList)) {
            log.error("setCSUStrategy no found strategy for {} devices", collectorIdList.size());
            return;
        }

        SimpleDateFormat format = new SimpleDateFormat(TimeFormatConstants.TIME_FORMAT_BASE);
        Date date = new Date();
        String currentTime = format.format(date);

        // 按照DeviceId分组，并筛选出每个设备最近一次下发的策略
        Map<String, PeakShiftCsu5StrategyPo> latestStrategyMap = strategyList.stream()
                .filter(bean -> StringUtils.isNotBlank(bean.getDeviceId()) && bean.getVersion() != null)
                .filter(bean -> currentTime.compareTo(bean.getStartTime()) >= 0 && currentTime.compareTo(bean.getEndTime()) <= 0)
                .collect(Collectors.toMap(
                        PeakShiftCsu5StrategyPo::getDeviceId,
                        Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(PeakShiftCsu5StrategyPo::getVersion)),
                        ConcurrentHashMap::new));

        if (latestStrategyMap.isEmpty()) {
            log.warn("setCSUStrategy No valid strategies found after filtering");
            return;
        }

        // 批量获取策略详情
        List<String> strategyIds = new ArrayList<>(latestStrategyMap.values()).stream()
                .map(PeakShiftCsu5StrategyPo::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<PeakShiftCsu5StrategyDetailBean> detailList = peakShiftMapper.selectCsuStrategyDetailByDeviceIds(strategyIds);
        if (CollectionUtils.isEmpty(detailList)) {
            log.error("setDirectStrategy not found details for strategy IDs: {}", JSON.toJSONString(strategyIds));
            return;
        }

        // 按照csu5StrategyId分组
        Map<String, List<PeakShiftCsu5StrategyDetailBean>> detailMap = detailList.stream()
                .collect(Collectors.groupingBy(PeakShiftCsu5StrategyDetailBean::getCsu5StrategyId, ConcurrentHashMap::new, Collectors.toList()));

        // 构建最终的策略详情映射
        Map<String, PeakShiftCsuAllStrategyBean> strategyDetailMap = new ConcurrentHashMap<>();
        latestStrategyMap.entrySet().parallelStream()
                .filter(entry -> detailMap.containsKey(entry.getValue().getId()))
                .forEach(entry->{
                    PeakShiftCsu5StrategyPo bean = entry.getValue();
                    String collectorId = entry.getKey();
                    PeakShiftCsuAllStrategyBean bo = new PeakShiftCsuAllStrategyBean();
                    BeanUtils.copyProperties(bean, bo);

                    List<PeakShiftCsu5StrategyDetailBean> detailPos= detailMap.get(bean.getId());
                    bo.setPeakShiftCsu5StrategyDetailBos(detailPos);
                    strategyDetailMap.put(collectorId, bo);
                });

        baseDataBean.setCsuStrategyDetailMap(strategyDetailMap);
    }

    /**
     * 查找当前策略（通用方法）
     * @param peakShiftDeviceStrategyDetailBean
     * @return
     * @throws com.zte.uedm.basis.exception.UedmException
     */
    public String commonFindCurrPeakStrategy(PeakShiftDeviceStrategyDetailBean peakShiftDeviceStrategyDetailBean) throws com.zte.uedm.basis.exception.UedmException {
        String currentTime = getCurrentTime();
        String monthAndDay = currentTime.substring(5, 10);
        String holiday = peakShiftDeviceStrategyDetailBean.getHolidayDateStr();
        List<String> holidayList = JSON.parseArray(holiday, String.class);
        String templateDetailStr = peakShiftDeviceStrategyDetailBean.getTemplateDetailStr();
        List<PeakShiftDeviceTemplateBean> templateDetailBeanList = JSON.parseArray(templateDetailStr, PeakShiftDeviceTemplateBean.class);
        Integer mode = Integer.parseInt(PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId());
        for (int i = 0; i < holidayList.size(); i++)
        {
            if (holidayList.get(i).equals(monthAndDay))
            {
                Integer holidayMode = peakShiftDeviceStrategyDetailBean.getHolidayMode();
                mode = findHolidayMode(templateDetailBeanList,holidayMode);
                return mode.toString();
            }
        }
        String datesAndTemp = peakShiftDeviceStrategyDetailBean.getDatesAndTemp();
        List<ModeTemplateDto> datesAndTempList = JSON.parseArray(datesAndTemp, ModeTemplateDto.class);
        if (peakShiftDeviceStrategyDetailBean.getMode()==Integer.parseInt(TemplateCodeEnum.DAILYTEMPLATE.getId()))
        {
            Integer day = 1;
            mode = findNormalMode(day,templateDetailBeanList,datesAndTempList);
            return mode.toString();
        }
        if (peakShiftDeviceStrategyDetailBean.getMode()==Integer.parseInt(TemplateCodeEnum.WEEKTEMPLATE.getId()))
        {
            String yearMonthDay = currentTime.substring(0,10);
            Integer weekNum = calDayOfWeek(yearMonthDay);
            mode = findNormalMode(weekNum,templateDetailBeanList,datesAndTempList);
            return mode.toString();
        }
        if (peakShiftDeviceStrategyDetailBean.getMode()==Integer.parseInt(TemplateCodeEnum.MONTHTEMPLATE.getId()))
        {
            String month = currentTime.substring(8,10);
            Integer monthNum = Integer.parseInt(month);
            mode = findNormalMode(monthNum,templateDetailBeanList,datesAndTempList);
        }
        return mode.toString();
    }

    public Integer findNormalMode(Integer dayOrWeekOrMonth, List<PeakShiftDeviceTemplateBean> templateDetailBeanList, List<ModeTemplateDto> datesAndTempList)
    {
        Integer mode = Integer.parseInt(PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId());
        ModeTemplateDto modeTemplateDto=datesAndTempList.stream().filter(p->p.getNum().equals(dayOrWeekOrMonth)).findAny().orElse(new ModeTemplateDto());
        Integer templateNum=modeTemplateDto.getTemplateNum();
        if(templateNum == null){
            return Integer.parseInt(PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId());
        }
        List<PeakShiftDeviceTemplateBean> PeakShiftDeviceTemplateBeanList=templateDetailBeanList.stream().filter(p->p.getNum().equals(templateNum)).collect(Collectors.toList());
        String currentTime = getCurrentTime();
        String hourAndMinuteSecond = currentTime.substring(11,19);
        for (int k = 0; k < PeakShiftDeviceTemplateBeanList.size(); k++) {
            if (hourAndMinuteSecond.compareTo(PeakShiftDeviceTemplateBeanList.get(k).getStartTime()) >= 0 && hourAndMinuteSecond.compareTo(PeakShiftDeviceTemplateBeanList.get(k).getEndTime()) <= 0) {
                mode = PeakShiftDeviceTemplateBeanList.get(k).getMode();
            }
        }
        return mode;
    }

    public Integer findHolidayMode(List<PeakShiftDeviceTemplateBean> templateDetailBeanList, Integer holidayMode)
    {
        List<PeakShiftDeviceTemplateBean> holidayOneDayList = new ArrayList();
        Integer strategyMode = Integer.parseInt(PeakShiftingStrategyEnum.STRATEGYUNKNOWN.getId());
        for (int i = 0; i < templateDetailBeanList.size(); i++)
        {
            if (Objects.equals(templateDetailBeanList.get(i).getNum(), holidayMode))
            {
                holidayOneDayList.add(templateDetailBeanList.get(i));
            }
        }
        String currentTime = getCurrentTime();
        String hourAndMinuteSecond = currentTime.substring(11,19);
        for (int i = 0; i < holidayOneDayList.size(); i++) {
            if (hourAndMinuteSecond.compareTo(holidayOneDayList.get(i).getStartTime())>=0 && hourAndMinuteSecond.compareTo(holidayOneDayList.get(i).getEndTime())<=0)
            {
                strategyMode=holidayOneDayList.get(i).getMode();
            }
        }
        return strategyMode;
    }

    public String getCurrentTime() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.now().format(df);
    }

    /**
     * 计算星期几
     * @param date
     * @return
     */
    public Integer calDayOfWeek(String date) {
        SimpleDateFormat format = new SimpleDateFormat(DateTypeConst.DATE_FORMAT_2);
        int week = 0;
        try {
            Date date1 = format.parse(date);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date1);
            week = cal.get(Calendar.DAY_OF_WEEK) - 1;
            if (week == 0) {
                week = 7;
            }
        } catch (Exception e) {
            log.error("get day of week failure", e);
        }
        return week;
    }

    /* Started by AICoder, pid:zeae1fa331obb951498909acf04a313411220e97 */
    public TemplateStrategyDetailEntity parseTemplateDetailDefault(TemplateStrategyDetailEntity templateStrategyDetailBo,
                                                                   List<TemplateStrategyDetailBcuaEntity> details) throws UedmException {
        List<TemplateDetailBaseDto> detailList = new ArrayList<>();
        List<TemplateHolidayDto> holidayList = new ArrayList<>();
        for (TemplateStrategyDetailBcuaEntity po : details) {
            if (po.getHolidayFlag()) {
                /*节假日模式*/
                TemplateHolidayDto holidayVo = new TemplateHolidayDto();
                List<TemplateTimeGranVo> timeGran = JsonUtils.jsonToObject(po.getHolidayTimeGran(), List.class, TemplateTimeGranVo.class);
                List<TemplateTimeGranDetailVo> detail = JsonUtils.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class);
                holidayVo.setTimeGran(timeGran);
                holidayVo.setDetail(detail);
                holidayList.add(holidayVo);
            } else if (StringUtils.isNotBlank(po.getTimeGran())) {
                /*周期详情且不是空模板*/
                TemplateDetailBaseDto detailVo = new TemplateDetailBaseDto();
                List<Integer> timeGran = JsonUtils.jsonToObject(po.getTimeGran(), List.class, Integer.class);
                List<TemplateTimeGranDetailVo> detail = JsonUtils.jsonToObject(po.getDetail(), List.class, TemplateTimeGranDetailVo.class);
                detailVo.setTimeGran(timeGran);
                detailVo.setDetail(detail);
                detailList.add(detailVo);
            }
        }
        templateStrategyDetailBo.setDetail(detailList);
        templateStrategyDetailBo.setHoliday(holidayList);
        return templateStrategyDetailBo;
    }

    /* Ended by AICoder, pid:zeae1fa331obb951498909acf04a313411220e97 */
    public abstract Map<String, BigDecimal> calculateChargeDischargeDuration(DevicePeakCacheInfoBean devicePeakCacheInfoBean) throws UedmException;

    public List<OriginalDataBean> getOriginalValueByDeviceId(String collectorId, String pointId) {
        try {
            List<OriginalDataBean> result = new ArrayList<>();
            CollectorAndAdapterPointDto adapterPointDto = new CollectorAndAdapterPointDto();
            adapterPointDto.setCollectorId(collectorId);
            adapterPointDto.setAdapterPointIds(Collections.singletonList(pointId));
            Map<String, Map<String, AdapterPointDataVo>> pointMap = adapterPointDataService.getByCollectorIdAndAdapterId(adapterPointDto);
            log.debug("queryDeviceData originalDataMap is {}", JSON.toJSONString(pointMap));

            if (pointMap == null || pointMap.isEmpty()) {
                log.warn("device data is empty! collector is {}", collectorId);
                return result;
            }

            for (Map.Entry<String, Map<String, AdapterPointDataVo>> entry : pointMap.entrySet()) {
                Map<String, AdapterPointDataVo> value = entry.getValue();
                if (value == null || value.isEmpty()) {
                    continue;
                }
                for (Map.Entry<String, AdapterPointDataVo> indexEntry : value.entrySet()) {
                    AdapterPointDataVo dataVo = indexEntry.getValue();
                    if (dataVo == null) {
                        continue;
                    }
                    OriginalDataBean originalDataBean = new OriginalDataBean();
                    originalDataBean.setValue(dataVo.getValue());
                    originalDataBean.setOmpId(dataVo.getAdapterPointId());
                    originalDataBean.setIndex(dataVo.getIndex());
                    originalDataBean.setUpdateTime(dataVo.getUpdateTime());
                    result.add(originalDataBean);
                }

            }
            return result;
        } catch (Exception ex) {
            log.error("getDmuSmuOriginalValueByDeviceId error", ex);
        }
        return new ArrayList<>();
    }

    public Map<String, List<String>> getBatteryByCollectIdAndFilterLoop(String collectorId) throws UedmException {
        // 初始化结果Map并添加"0"和"1"两个key，值为空的列表
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(BatteryTypeEnums.vrla.getId(), new ArrayList<>());
        resultMap.put(BatteryTypeEnums.li_ion.getId(), new ArrayList<>());
        resultMap.put(BatteryTypeEnums.unknown.getId(), new ArrayList<>());

        // 获取设备ID列表并获取设备信息
        List<String> deviceIds = resourceCollectorRelationCacheManager
                .getRelationsByCollectors(Collections.singleton(collectorId))
                .stream()
                .map(ResourceCollectorRelationEntity::getResourceId)
                .collect(Collectors.toList());
        log.info("getBatteryByCollectIdAndFilterLoop deviceIds {}", deviceIds);
        List<DeviceEntity> deviceByIdsAndMoc = deviceCacheManager.getDeviceByIdsAndMoc(deviceIds, MocOptional.BATTERY.getId());
        log.info("getBatteryByCollectIdAndFilterLoop deviceByIdsAndMoc size{}", deviceByIdsAndMoc.size());
        log.debug("getBatteryByCollectIdAndFilterLoop deviceByIdsAndMoc {}", deviceByIdsAndMoc);
        // 遍历设备列表进行过滤和分组
        for (DeviceEntity bean : deviceByIdsAndMoc) {
            if (!batteryAttributeUtils.getIsLoop(bean.getExattribute())) {
                String batteryTypeCode = batteryAttributeUtils.getBatteryTypeCode(bean.getExattribute());
                if (resultMap.containsKey(batteryTypeCode)) {
                    resultMap.get(batteryTypeCode).add(bean.getId());
                }
            }
        }

        return resultMap;
    }
    /* Started by AICoder, pid:mdc50t0730xfad214ab00ae4009f2c24f721d547 */

    /**
     * 返回标准测点的值，若取不到则返回0
     */
    public BigDecimal calculateStdSum(String moId, String spId) {
        BigDecimal result = BigDecimal.ZERO;
        List<String> spIds = Collections.singletonList(spId);
        Map<String, Map<String, String>> valueMap = new HashMap<>();
        try {
            valueMap = dataRedis.selectRealData(moId, spIds);
        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.warn("Invalid number format for value: {}", e.getMessage(),e);
        }
        log.info("---calculateStdSum--- valueMap.size {}", valueMap.size());
        log.debug("---calculateStdSum--- valueMap {}", valueMap);

        String value = dataRedis.getSmpValueFromMap(valueMap, spId);
        if (value != null && !value.isEmpty()) {
            try {
                result = new BigDecimal(value);
                return result;
            } catch (NumberFormatException e) {
                log.warn("Invalid number format for value: {}", value, e);
            }
        }
        return result;
    }

    /* Ended by AICoder, pid:mdc50t0730xfad214ab00ae4009f2c24f721d547 */
    /* Started by AICoder, pid:zb9e7fef3ez8df314ff60b14f06fcf36d8b1ea74 */

    /**
     * 查询7天内平均放电电流
     */
    public Map<String,BigDecimal> queryDisCurrWeek(List<String> moIds) {
        Map<String,BigDecimal> result = new HashMap<>();
        try {

            // 获取电池电流数据列表
            List<BatteryCurrentPojo> batteryCurrentPojoList = batteryCurrentStorageDomin.getBattCurrWResultBybattIds(moIds);

            // 检查列表是否为空或null，避免空指针异常
            if (batteryCurrentPojoList == null || batteryCurrentPojoList.isEmpty()) {
                return result;
            }

            // 平均放电电流
            /* Started by AICoder, pid:n0d52x725d46f1b1493c0883a0713c078f79ccc9 */
            result = batteryCurrentPojoList.stream()
                    .collect(Collectors.toMap(
                            BatteryCurrentPojo::getBattId,  // Key: id
                            pojo -> {
                                BigDecimal avgCurr = pojo.getAvgCurr();
                                return (avgCurr == null) ? BigDecimal.ZERO : avgCurr;  // Value: avgCurr or BigDecimal.ZERO if null
                            },
                            (existing, replacement) -> existing  // 合并函数，当键冲突时保留第一个值
                    ));
            /* Ended by AICoder, pid:n0d52x725d46f1b1493c0883a0713c078f79ccc9 */


            return result;

        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.error("queryDisCurrWeek error: {}", e.getMessage(), e);
            return result;
        }
    }

    /* Ended by AICoder, pid:zb9e7fef3ez8df314ff60b14f06fcf36d8b1ea74 */

    /**
     * 查询设计备电时长
     * 根据备电场景传入不同的id，开关电源场景id传直流电源的 独立组网场景id传电池组的
     */
    /* Started by AICoder, pid:gb7e0cd34a1c4dd14ae80ae500671713116967b8 */
    public BigDecimal queryThreshold(String id) {
        try {
            // 使用Optional来处理可能的null值
            Optional<BattBackupPowerThresholdDetailPojo> pojoOptional = Optional.ofNullable(backupPowerThresholdDetailDomainImpl.selectById(id));

            // 如果pojo存在，则返回其阈值，否则返回BigDecimal.ZERO
            return pojoOptional.map(pojo -> new BigDecimal(pojo.getThreshold()))
                    .orElse(BigDecimal.ZERO);
        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.error("queryThreshold error: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /* Ended by AICoder, pid:gb7e0cd34a1c4dd14ae80ae500671713116967b8 */

    /**
     * 获取最近一次从设备获取的策略信息
     *
     * @param collectorId
     * @return
     */
    public TemplateStrategyDetailEntity parseDirectLatestStrategy(String collectorId) throws com.zte.uedm.common.exception.UedmException {
        TemplateStrategyDetailEntity result = new TemplateStrategyDetailEntity();

        // 查询从设备获取的策略信息
        List<PeakShiftDeviceStrategyBean> allStrategyList = peakShiftDeviceFileMapper.findStrategyByDeviceId(collectorId);

        // 取version最大的策略
        Optional<PeakShiftDeviceStrategyBean> latestStrategy = CollectionUtils.emptyIfNull(allStrategyList).stream()
                .filter(bean -> bean.getVersion() != null)
                .max(Comparator.comparing(PeakShiftDeviceStrategyBean::getVersion));
        if (!latestStrategy.isPresent()) {
            log.warn("parseDirectLatestStrategy not found max version strategy {}", collectorId);
            return result;
        }

        PeakShiftDeviceStrategyBean latest = latestStrategy.get();
        log.info("parseDirectLatestStrategy get latest strategy {}", JSON.toJSONString(latest));
        String intervalStrategyId = latest.getIntervalStrategyId();
        // 获取策略详情
        PeakShiftDeviceStrategyDetailBean latestDetailBean = peakShiftDeviceFileMapper.selectPeakShiftDeviceStrategyDetailBeanById(intervalStrategyId);
        if (latestDetailBean == null) {
            log.warn("parseDirectLatestStrategy null latestDetailBean for intervalStrategyId {}", intervalStrategyId);
            return result;
        }

        // 日期与模版的映射关系
        List<ModeTemplateDto> timeGrainMappingList = jsonService.jsonToObject(latestDetailBean.getDatesAndTemp(), List.class, ModeTemplateDto.class);
        log.info("parseDirectLatestStrategy timeGrainMappingList size:{}",CollectionUtils.emptyIfNull(timeGrainMappingList).size());
        // 节假日
        List<String> holidays = jsonService.jsonToObject(latestDetailBean.getHolidayDateStr(), List.class, String.class);
        log.info("parseDirectLatestStrategy holidays size:{}",CollectionUtils.emptyIfNull(holidays).size());
        // 时段详情
        List<PeakShiftDeviceTemplateBean> templateBeanList = jsonService.jsonToObject(latestDetailBean.getTemplateDetailStr(), List.class, PeakShiftDeviceTemplateBean.class);
        // 节假日模版编号
        Integer holidayTemplateNum = latestDetailBean.getHolidayMode();

        // 时段按照模版分类
        Map<Integer, List<PeakShiftDeviceTemplateBean>> strategyInfoByTemplateMap = CollectionUtils.emptyIfNull(templateBeanList).stream()
                .filter(bean -> bean != null && bean.getNum() != null)
                .collect(Collectors.groupingBy(PeakShiftDeviceTemplateBean::getNum));

        List<TemplateDetailBaseDto> detailList = new ArrayList<>();
        List<TemplateHolidayDto> holidayList = new ArrayList<>();
        // 解析模版详情
        convertStrategy(strategyInfoByTemplateMap, timeGrainMappingList, holidays, holidayTemplateNum, collectorId, detailList, holidayList);

        log.info("parseDirectLatestStrategy after convert {} detail size:{} holiday size:{}", collectorId, detailList.size(), holidayList.size());
        result.setHoliday(holidayList);
        result.setDetail(detailList);
        result.setMode(String.valueOf(latestDetailBean.getMode()));

        // 获取最新策略文件Id
        PeakShiftDeviceChildBeanVo fileBean = peakShiftDeviceFileMapper.selectDeviceFileIdAndTime(collectorId);
        if (fileBean != null) {
            result.setFileId(fileBean.getFileId());
        }

        log.debug("parseDirectLatestStrategy after convert {} {}", collectorId, JSON.toJSONString(result));
        return result;
    }

    private void convertStrategy(Map<Integer, List<PeakShiftDeviceTemplateBean>> strategyInfoByTemplateMap,
                                 List<ModeTemplateDto> timeGrainMappingList,List<String> holidays,
                                 Integer holidayTemplateNum,String collectorId,
                                 List<TemplateDetailBaseDto> detailList,List<TemplateHolidayDto> holidayList
                                 ) {
        for (Map.Entry<Integer, List<PeakShiftDeviceTemplateBean>> entry : strategyInfoByTemplateMap.entrySet()) {
            // 模版编号
            Integer templateNum = entry.getKey();
            // 时段策略
            List<PeakShiftDeviceTemplateBean> timeStratgyList = entry.getValue();
            if (CollectionUtils.isEmpty(timeStratgyList)) {
                log.warn("parseDirectLatestStrategy timeStratgyList is empty {} {}", collectorId, templateNum);
                continue;
            }
            List<TemplateTimeGranDetailVo> detailStrategyList = new ArrayList<>();
            for (PeakShiftDeviceTemplateBean peakShiftDeviceTemplateBean : timeStratgyList) {
                TemplateTimeGranDetailVo timeGranDetailVo = new TemplateTimeGranDetailVo();
                timeGranDetailVo.setNum(peakShiftDeviceTemplateBean.getNum());
                timeGranDetailVo.setBeginTime(peakShiftDeviceTemplateBean.getStartTime());
                timeGranDetailVo.setEndTime(peakShiftDeviceTemplateBean.getEndTime());
                timeGranDetailVo.setStrategyType(peakShiftDeviceTemplateBean.getMode());
                detailStrategyList.add(timeGranDetailVo);
            }

            // 是否是节假日时段
            if (templateNum.equals(holidayTemplateNum) && CollectionUtils.isNotEmpty(holidays)) {
                TemplateHolidayDto templateHoliday = new TemplateHolidayDto();
                // 将节假日日期转为范围区间
                List<TemplateTimeGranVo> dateRangeList = convertHolidayRange(holidays);
                templateHoliday.setTimeGran(dateRangeList);
                templateHoliday.setDetail(detailStrategyList);
                holidayList.add(templateHoliday);
                continue;
            }

            //此模版对应的日期编号
            List<Integer> dateList = CollectionUtils.emptyIfNull(timeGrainMappingList).stream()
                    .filter(bean -> bean!=null && bean.getNum() != null && templateNum.equals(bean.getTemplateNum()))
                    .map(ModeTemplateDto::getNum).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dateList)) {
                log.warn("parseDirectLatestStrategy dateList is empty {} {}", collectorId, templateNum);
                continue;
            }

            TemplateDetailBaseDto templateDetail = new TemplateDetailBaseDto();
            templateDetail.setDetail(detailStrategyList);
            templateDetail.setTimeGran(dateList);
            detailList.add(templateDetail);
        }

    }

    /* Started by AICoder, pid:ybb43s1794r2c131458d09fb10e696492e698129 */
    /**
     * 将节假日期号变为范围区间
     * @param holidayList 示例:["01-09","01-10","11-31","12-01"]
     * @return
     */
    public List<TemplateTimeGranVo> convertHolidayRange(List<String> holidayList) {
        if (holidayList == null || holidayList.isEmpty()) {
            return new ArrayList<>();
        }

        holidayList.sort(String::compareTo);

        List<TemplateTimeGranVo> result = new ArrayList<>();
        String currentBegin = holidayList.get(0);
        String currentEnd = currentBegin;

        for (int i = 1; i < holidayList.size(); i++) {
            String[] parts = holidayList.get(i).split("-");
            String[] prevParts = holidayList.get(i - 1).split("-");

            int monthDiff = Integer.parseInt(parts[0]) - Integer.parseInt(prevParts[0]);
            int dayDiff = Integer.parseInt(parts[1]) - Integer.parseInt(prevParts[1]);

            // 检查是否是连续的日期，包括跨月的情况
            boolean isContinuous = (monthDiff == 0 && dayDiff == 1) ||
                    (monthDiff == 1 && Integer.parseInt(prevParts[1]) == YearMonth.of(YearMonth.now().getYear(), Integer.parseInt(prevParts[0])).lengthOfMonth() && Integer.parseInt(parts[1]) == 1);

            if (isContinuous) {
                // 连续的日期，更新end
                currentEnd = holidayList.get(i);
            } else {
                // 非连续的日期，保存当前区间并开始新的区间
                TemplateTimeGranVo range = new TemplateTimeGranVo();
                range.setBegin(currentBegin);
                range.setEnd(currentEnd);
                result.add(range);
                currentBegin = holidayList.get(i);
                currentEnd = currentBegin;
            }
        }

        // 添加最后一个区间
        TemplateTimeGranVo lastRange = new TemplateTimeGranVo();
        lastRange.setBegin(currentBegin);
        lastRange.setEnd(currentEnd);
        result.add(lastRange);

        return result;
    }
    /* Ended by AICoder, pid:ybb43s1794r2c131458d09fb10e696492e698129 */

    /* Started by AICoder, pid:ze3aff3686c02c5142d0098aa057a86cad07c55b */

    public TemplateStrategyDetailEntity transferTo(PeakShiftParamVo peakShiftParamVo)
    {
        TemplateStrategyDetailEntity templateStrategyAddCsuDto = new TemplateStrategyDetailEntity();
        templateStrategyAddCsuDto.setWeekendFlag(peakShiftParamVo.getWeekendFlag());
        templateStrategyAddCsuDto.setMode(CycleModelEnum.getNotCycleModelId());

        List<PeakShiftHolidayStrategyBean> holidayBeans = peakShiftParamVo.getHoliday();
        if (!org.springframework.util.CollectionUtils.isEmpty(holidayBeans)) {
            List<TemplateHolidayDto> holiday = holidayBeans.stream().map(bean -> {
                TemplateHolidayDto holidayDto = new TemplateHolidayDto();
                TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
                templateTimeGranVo.setBegin(bean.getBeginDate());
                templateTimeGranVo.setEnd(bean.getEndDate());
                holidayDto.setTimeRange(templateTimeGranVo);
                return holidayDto;
            }).collect(Collectors.toList());
            templateStrategyAddCsuDto.setHoliday(holiday);
        }

        List<PeakShiftNormalStrategyBean> normal = peakShiftParamVo.getNormal();
        if (!org.springframework.util.CollectionUtils.isEmpty(normal)) {
            normalTransferToDetail(templateStrategyAddCsuDto, normal);
        }

        return templateStrategyAddCsuDto;
    }

    private void normalTransferToDetail(TemplateStrategyDetailEntity templateStrategyAddCsuDto, List<PeakShiftNormalStrategyBean> normal)
    {
        Map<String, List<Pair<String, PeakShiftStrategyDetailBean>>> stringPairMap = new HashMap<>();
        for (PeakShiftNormalStrategyBean peakShiftNormalStrategyBean : normal)
        {
            String strategyType = peakShiftNormalStrategyBean.getStrategyType();
            List<PeakShiftStrategyDetailBean> peakShiftStrategyDetailBeans = peakShiftNormalStrategyBean.getDetail();
            if (org.springframework.util.CollectionUtils.isEmpty(peakShiftStrategyDetailBeans))
            {
                continue;
            }
            for (PeakShiftStrategyDetailBean peakShiftStrategyDetailBean : peakShiftStrategyDetailBeans)
            {
                String key = peakShiftStrategyDetailBean.getBeginDate() + peakShiftStrategyDetailBean.getEndDate();
                List<Pair<String, PeakShiftStrategyDetailBean>> list = new ArrayList<>();
                if (stringPairMap.containsKey(key))
                {
                    list = stringPairMap.get(key);
                }
                list.add(Pair.of(strategyType, peakShiftStrategyDetailBean));
                stringPairMap.put(key, list);
            }
        }

        List<TemplateDetailBaseDto> details = new ArrayList<>();
        for (Map.Entry<String, List<Pair<String, PeakShiftStrategyDetailBean>>> entry : stringPairMap.entrySet()) {
            List<Pair<String, PeakShiftStrategyDetailBean>> detailList = entry.getValue();

            if (detailList.size() > 0) {
                TemplateTimeGranVo timeRange = new TemplateTimeGranVo();
                timeRange.setBegin(detailList.get(0).getRight().getBeginDate());
                timeRange.setEnd(detailList.get(0).getRight().getEndDate());

                List<TemplateTimeGranDetailVo> strategyDetail = detailList.stream().map(bean -> {
                    PeakShiftStrategyDetailBean peakShiftStrategyDetailBean = bean.getRight();
                    TemplateTimeGranDetailVo templateTimeGranDetailVo = new TemplateTimeGranDetailVo();
                    templateTimeGranDetailVo.setStrategyType(Integer.parseInt(bean.getLeft()));
                    templateTimeGranDetailVo.setBeginTime(peakShiftStrategyDetailBean.getBeginTime().substring(0, 5));   //取时分给前端，例如：12:00:00 -> 12:00
                    templateTimeGranDetailVo.setEndTime(peakShiftStrategyDetailBean.getEndTime().substring(0, 5));   //取时分给前端，例如：12:00:00 -> 12:00
                    return templateTimeGranDetailVo;
                }).sorted(Comparator.comparing(TemplateTimeGranDetailVo::getBeginTime)).collect(Collectors.toList());
                TemplateDetailBaseDto detail = new TemplateDetailBaseDto();
                detail.setTimeRange(timeRange);
                detail.setDetail(strategyDetail);
                details.add(detail);
            }
        }
        templateStrategyAddCsuDto.setDetail(details);
    }
    /* Ended by AICoder, pid:ze3aff3686c02c5142d0098aa057a86cad07c55b */

}
