/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BackupPowerOverviewDao
 * 文件作者：00248587
 * 开发时间：2023/3/7
 */
package com.zte.uedm.battery.dao;


import com.zte.uedm.battery.bean.BattBackupPowerEvalPojo;
import com.zte.uedm.battery.controller.backuppower.dto.BackupPowerFilterDto;
import com.zte.uedm.battery.controller.backuppower.dto.BackupPowerStatisticsFilterDto;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BackupPowerOverviewDao
{
    List<BattBackupPowerEvalPojo> selectBackupPowerEvalDByCondition(BackupPowerStatisticsFilterDto dto, List<String> spMoIds) throws UedmException;
}
