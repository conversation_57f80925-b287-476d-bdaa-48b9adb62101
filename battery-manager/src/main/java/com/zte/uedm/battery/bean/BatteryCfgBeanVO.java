package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电池管理配置
 * 
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@ApiModel(description = "电池管理配置")
public class BatteryCfgBeanVO
{
    @ApiModelProperty(value = "标识")
    private Integer id;
    @ApiModelProperty(value = "监控对象标识")
    private String moOid;
    @ApiModelProperty(value = "配置类型")
    private String cfgType;

}
