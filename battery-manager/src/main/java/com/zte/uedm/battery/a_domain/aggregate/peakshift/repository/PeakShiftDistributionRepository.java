package com.zte.uedm.battery.a_domain.aggregate.peakshift.repository;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.*;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskDetailPo;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskPo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftDeviceTaskDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftStrategyDistributionDto;
import com.zte.uedm.battery.bean.PeakShiftDeviceTaskVo;
import com.zte.uedm.common.exception.UedmException;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PeakShiftDistributionRepository {

    List<PeakShiftTaskEntity> selectTaskByCondition(PeakShiftStrategyDistributionDto distributionDto) throws UedmException;

    List<PeakShiftTaskDetailEntity> selectTaskDetailByCondition(List<String> ids, List<String> deviceIds) throws UedmException;

    Integer insertPeakShiftTask(PeakShiftTaskPo taskPo) throws UedmException;

    PeakShiftTaskEntity selectById(String id) throws UedmException;

    Integer updatePeakShiftTask(PeakShiftTaskPo peakShiftTaskBean) throws UedmException;

    Integer deleteByTaskId(String id) throws UedmException;

    void deleteById(String id) throws UedmException;

    Integer insertPeakShiftTaskDetail(List<PeakShiftTaskDetailPo> list) throws UedmException;

    List<String> selectlDeviceByTaskIdAndStatus(String peakShift,String tag) throws UedmException;

    List<PeakShiftTaskEntity> checkTaskByname(String name) throws UedmException;

    List<String> selectDuplicateNames(String id, String name) throws UedmException;

    DeviceStatusCountEntity selectCountGroupByStatus(@Param("taskId")String taskId) throws UedmException;

    Integer selectTotalByTaskId(@Param("taskId")String taskId) throws UedmException;

    List<PeakShiftTaskDetailEntity> selectByTaskId(@Param("taskId")  String taskId, @Param("deviceStatus")List<String> deviceStatus) throws UedmException;

    Integer updateStatusById(String id, String status) throws UedmException;

    List<PeakShiftDeviceTaskEntity> selectDeviceTaskInfoByNewCondition(PeakShiftDeviceTaskDto bean) throws UedmException;

    List<PendingEntity> queryAllPendingDevice() throws UedmException;

    String getLogicGroupByTemplateStrategyId(String templateStrategyId) throws UedmException;

    void updateFtpPath(@Param("taskId") String taskId, @Param("fileId") String fileId, @Param("ftpPtah") String ftpPtah);


}
