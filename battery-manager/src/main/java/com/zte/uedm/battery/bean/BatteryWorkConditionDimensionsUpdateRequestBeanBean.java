package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@ApiModel(description = "电池工况维度更新参数")
public class BatteryWorkConditionDimensionsUpdateRequestBeanBean {
    @ApiModelProperty(value = "标识")
    private String id;
    @ApiModelProperty(value = "维度")
    private Integer sequence;
    @ApiModelProperty(value = "是否开启")
    private Boolean enable ;
}