package com.zte.uedm.battery.a_domain.aggregate.safe;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BatteryEkeyRelationEntity {

    /**
     * 电池id
     */
    private String moId;

    /**
     * 电子钥匙（密文）
     */
    private String ekey;


    /**
     * 创建时间
     */
    private String gmtCreate;


    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String gmtModified;


    /**
     * 更新人
     */
    private String updater;
}
