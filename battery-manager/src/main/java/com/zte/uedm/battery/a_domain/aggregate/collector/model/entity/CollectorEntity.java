package com.zte.uedm.battery.a_domain.aggregate.collector.model.entity;


import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "采集器表")
@EqualsAndHashCode(callSuper = true)
public class CollectorEntity extends ResourceBaseEntity {

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "协议Id")
    private String protocolId;

    @ApiModelProperty(value = "适配版本id")
    private String adapterId;

    @ApiModelProperty(value = "关联链路")
    private Object linkInfo;

    @ApiModelProperty(value = "采集器唯一标识")
    private String identification;

    @ApiModelProperty(value = "协议通信属性")
    private Object protocolAttribute;
}
