package com.zte.uedm.battery.bean.overview;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.bean.BattRiskEvalPojo;
import com.zte.uedm.battery.controller.batteryrisk.vo.RiskDetailObjValue;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
@Slf4j
public class OverviewRiskInfoBean {

    /**
     * 风险Id
     */
    private String riskId;

    /**
     * 风险等级Id
     */
    private String riskLevel;

    /**
     * 风险详情
     */
    private String riskDetail;

    /**
     * 评估时间
     */
    private String evalTime;

    public static Map<String, List<OverviewRiskInfoBean>> buildOverviewBean(List<BattRiskEvalPojo> list) {
        return list.stream().collect(Collectors.groupingBy(
                pojo -> pojo.getBattId(),
                Collectors.mapping(pojo -> {
                    OverviewRiskInfoBean overviewRiskInfoBean = new OverviewRiskInfoBean();
                    BeanUtils.copyProperties(pojo, overviewRiskInfoBean);
                    return overviewRiskInfoBean;
                }, Collectors.toList())
        ));
    }

    public RiskDetailObjValue parseDetail() {
        try {
            return JSON.parseObject(this.riskDetail, RiskDetailObjValue.class);
        } catch (Exception e) {
            log.error("parse battery risk err", e);
            return null;
        }
    }

}
