package com.zte.uedm.battery.a_application.safe.executor.impl;

import com.google.common.collect.Lists;
import com.zte.log.filter.UserThreadLocal;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.basis.cons.MicroServiceOptional;
import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_application.safe.executor.EkeHistoryRecordCommdService;
import com.zte.uedm.battery.a_domain.aggregate.safe.BatteryEkeyRelationEntity;
import com.zte.uedm.battery.a_domain.aggregate.safe.EkeHistoryRecordEntity;
import com.zte.uedm.battery.a_domain.safe.impl.BatteryProtectServiceImpl;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.CollectorDeliveryBean;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.EleKeyDeliveryBean;
import com.zte.uedm.battery.a_infrastructure.safe.converter.BatteryEkeyRelationConverter;
import com.zte.uedm.battery.a_infrastructure.safe.enums.EleKeyTypeEnums;
import com.zte.uedm.battery.a_infrastructure.safe.persistence.BatteryEkeyRelationRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.safe.persistence.EkeHistoryRecordRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.safe.po.BatteryEkeyRelationPo;
import com.zte.uedm.battery.a_infrastructure.safe.po.EkeHistoryRecordPo;
import com.zte.uedm.battery.a_interfaces.safe.web.dto.BatteryEkeyExportDto;
import com.zte.uedm.battery.a_interfaces.safe.web.vo.EkeyChangeLogVo;
import com.zte.uedm.battery.bean.LogInputBean;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.common.bean.KafkaBean;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.battery.util.EncryptUtils;
import com.zte.uedm.common.bean.log.OperLogContants;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.function.canvas.utils.CommonUtils;
import com.zte.uedm.function.security.api.CryptoUtil;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.BATTERY_SMPID_SERIAL_NUMBER;
import static com.zte.uedm.component.kafka.producer.constants.KafkaTypeOptional.KAFKA_TYPE_EKEY_DELIVERY;

@Slf4j
@Service
public class EkeHistoryRecordCommdImpl implements EkeHistoryRecordCommdService {

    @Resource
    private ConfigService configService;
    @Resource
    private EkeHistoryRecordRepositoryImpl ekeHistoryRecordRepositoryImpl;
    @Resource
    private BatteryEkeyRelationRepositoryImpl batteryEkeyRelationRepositoryImpl;
    @Resource
    private StandardDataService standardDataService;
    @Resource
    private BlankService blankService;
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Resource
    private BatteryProtectServiceImpl batteryProtectService;
    @Resource
    private MessageSenderService messageSenderService;
    @Resource
    private JsonService jsonService;
    @Resource
    private DateTimeService dateTimeService;
    @Resource
    private LogUtils logUtils;
    private static final int EXPECTED_EKEY_LENGTH = 32;
    private static final String MAP_KEY_VALUE = "value";
    private static final String DEVICE_TYPE = "802e00";
    private static final String MASTER_EKEY_DIRECTORY = "/home/<USER>/ekeydownload/";

    /* Started by AICoder, pid:x0932pb6c6h147b142780843f00cf60290a1bff5 */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean importEkey(InputStream fis, HttpServletRequest request) throws Exception {
        if (!isNetwork())
        {
            return false;
        }
        boolean flag = false;
        EkeHistoryRecordPo ekeyPo = new EkeHistoryRecordPo();
        String ekey = readContent(fis);
        String encryptKey = CryptoUtil.encrypt(ekey, MicroServiceOptional.MICRO_SERVICE_BATTERY_MANAGER);
        String ekeySha256 = EncryptUtils.generateSHA256(ekey);

        if (StringUtils.isNotEmpty(ekey) && ekey.length() == EXPECTED_EKEY_LENGTH)
        {
            //发送kafka
            changeEkey(encryptKey, ekeySha256, request);
            //发送日志
            EkeHistoryRecordEntity ekeyEntity = ekeHistoryRecordRepositoryImpl.select(ekeyPo);
            sendLogDetail(ekeyEntity);
            flag = true;
        }
        return flag;
    }

    private void sendLogDetail(EkeHistoryRecordEntity ekeyEntity) {
        EkeHistoryRecordEntity logEntity = new EkeHistoryRecordEntity();
        logEntity.setId(ekeyEntity.getId());
        logEntity.setIsCurr(ekeyEntity.getIsCurr());
        logEntity.setGmtCreate(ekeyEntity.getGmtCreate());
        logEntity.setCreator(ekeyEntity.getCreator());
        UserThreadLocal.setLogDetail(logEntity);
    }
    /* Ended by AICoder, pid:x0932pb6c6h147b142780843f00cf60290a1bff5 */

    public boolean isNetwork()
    {
        String batteryTheft = configService.getGlobalProperty(GlobalConstants.CFG_CENTER_BATTERY_THEFT);
        log.info("isNetwork batteryTheft is {}",batteryTheft);
        return batteryTheft.equals(GlobalConstants.BATTERY_THEFT_NETWORK);
    }

    /* Started by AICoder, pid:o73a244de6945ef14a1e095770c40909ece15e58 */
    @Override
    public void exportEkey(HttpServletRequest request, HttpServletResponse response, BatteryEkeyExportDto dto) throws Exception {

        EkeHistoryRecordEntity recordEntity = ekeHistoryRecordRepositoryImpl.select(new EkeHistoryRecordPo());
        File tempFile = new File( MASTER_EKEY_DIRECTORY + File.separator + "ekey" + System.currentTimeMillis() + GlobalConstants.TXT_EXTENSION);
        File parentFile = tempFile.getParentFile();
        if (parentFile !=null && !parentFile.exists()) {
            parentFile.mkdirs();
        }
        if (!tempFile.exists()) {
            boolean newFile = tempFile.createNewFile();
            log.info("exportEkey filePath:{},is new file ? =====> {}", tempFile.getAbsolutePath(),newFile);
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))){
            if (recordEntity.getEkey() != null && isNetwork())
            {
                String encryptKey = CryptoUtil.decrypt(recordEntity.getEkey(), MicroServiceOptional.MICRO_SERVICE_BATTERY_MANAGER);
                writer.write(encryptKey);
                writer.flush();
                String zipFileName = MASTER_EKEY_DIRECTORY + File.separator + "ekey" + System.currentTimeMillis() + GlobalConstants.FILE_ZIP_EXTENSION;
                File file = FileUtils.zipProtected(tempFile, zipFileName, dto.getPasswd());
                FileUtils.downloadFile(file.getAbsolutePath(),response,request);
                log.info("exportEkey downloaded successfully.");
            }
        }
        catch (Exception e)
        {
            log.error("exportEkey error",e);
            throw new UedmException(ErrorCodeOptional.FILE_FORMAT_ERROR);
        }
        finally
        {
            if (tempFile != null)
            {
                sendLogDetail(recordEntity);
                tempFile.delete();
            }
        }
    }
    /* Ended by AICoder, pid:o73a244de6945ef14a1e095770c40909ece15e58 */

    @Override
    public String exportQuery() {
        EkeHistoryRecordEntity recordEntity = ekeHistoryRecordRepositoryImpl.select(new EkeHistoryRecordPo());
        return recordEntity.getGmtCreate() != null ? recordEntity.getGmtCreate() : GlobalConstants.EMPTY_STR;
    }

    private static String readContent(InputStream inputStream) throws Exception {
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line).append("\n");
            }
        }

        /* Started by AICoder, pid:cafa7nc68c76d7e143cb0a7a2055a70689147ffe */
        // 移除最后一个换行符
        if (contentBuilder.length() > 0 && contentBuilder.charAt(contentBuilder.length() - 1) == '\n') {
            contentBuilder.deleteCharAt(contentBuilder.length() - 1);
        }
        /* Ended by AICoder, pid:cafa7nc68c76d7e143cb0a7a2055a70689147ffe */

        return contentBuilder.toString();
    }

    /* Started by AICoder, pid:f022624ac58764f142b70aa4c1d1853ce543061c */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeEkey(String encryptEkey, String sha256, HttpServletRequest request) throws Exception {
        String userName = Tools.getUserName(request);

        List<BatteryEkeyRelationEntity> batteryEkeyRelationEntities = batteryEkeyRelationRepositoryImpl.selectBatteryList(new ArrayList<>());
        EkeHistoryRecordEntity oldKey = ekeHistoryRecordRepositoryImpl.select(new EkeHistoryRecordPo());
        if (CollectionUtils.isEmpty(batteryEkeyRelationEntities)) {
            log.warn("no battery ekey relation.");
            EkeHistoryRecordPo newKey = updateEKey(encryptEkey, sha256, userName);
            setEkeyChangeLogDetail(oldKey, newKey);
            return;
        }

        Map<String, String> oldKeyMap = batteryEkeyRelationEntities.stream()
                .collect(Collectors.toMap(BatteryEkeyRelationEntity::getMoId, BatteryEkeyRelationEntity::getEkey));

        //过滤通讯异常电池和不在设防状态电池
        List<String> allBattery = batteryEkeyRelationEntities.stream()
                .map(BatteryEkeyRelationEntity::getMoId)
                .filter(bean -> batteryProtectService.validateCanProtect(bean, GlobalConstants.DISARM))
                .collect(Collectors.toList());

        Map<String, Map<String, Object>> allBattPointData = standardDataService.batchQueryByResourceIdAndStandPointId(allBattery, Arrays.asList(BATTERY_SMPID_SERIAL_NUMBER));

        List<String> collectorIds = resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(allBattery);
        if (CollectionUtils.isEmpty(collectorIds))
        {
            log.warn("No collector IDs found for battery id size is {}", allBattery.size());
            EkeHistoryRecordPo newKey = updateEKey(encryptEkey, sha256, userName);
            setEkeyChangeLogDetail(oldKey, newKey);
            return;
        }

        Map<String, String> snMap = allBattPointData.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> getValueByCondition(BATTERY_SMPID_SERIAL_NUMBER, entry.getValue())));

        Map<String, List<String>> collectorRelationBattMap = resourceCollectorRelationCacheManager.queryCollectorIdWithResMapByCollectAndRes(collectorIds, allBattery);

        EkeHistoryRecordPo newKey = updateEKey(encryptEkey, sha256, userName);
        changeEkeyRelation(encryptEkey, userName, batteryEkeyRelationEntities);

        sendChangeKeyMsgToDevice(collectorRelationBattMap, oldKeyMap, snMap, encryptEkey);
        setEkeyChangeLogDetail(oldKey, newKey);
    }

    public void setEkeyChangeLogDetail(EkeHistoryRecordEntity oldKey, EkeHistoryRecordPo newKey)
    {
        EkeyChangeLogVo logVo = new EkeyChangeLogVo();
        logVo.setOldEkeyId(oldKey.getId());
        logVo.setNewEkeyId(newKey.getId());
        logVo.setOldCreator(oldKey.getCreator());
        logVo.setNewCreator(newKey.getCreator());
        logVo.setOldCreateTime(oldKey.getGmtCreate());
        logVo.setNewCreateTime(newKey.getGmtCreate());
        UserThreadLocal.setLogDetail(logVo);
    }

    private void sendChangeKeyMsgToDevice(Map<String, List<String>> collectorRelationBattMap, Map<String, String> oldKeyMap, Map<String, String> snMap, String newKey) {
        List<CollectorDeliveryBean> collectorDeliveryBeanList = new ArrayList<>();
        collectorRelationBattMap.forEach((collectorId, battIds) -> {
            List<String> batchBattIds = new ArrayList<>();
            battIds.forEach(id -> {
                batchBattIds.add(id);

                if (batchBattIds.size() == 100) {
                    sendChangeKeyMsgToDevice(collectorId, batchBattIds, oldKeyMap, snMap, newKey, collectorDeliveryBeanList);
                    batchBattIds.clear();
                }
            });

            // 处理剩余的未满20个的电池ID
            if (!batchBattIds.isEmpty()) {
                sendChangeKeyMsgToDevice(collectorId, batchBattIds, oldKeyMap, snMap, newKey, collectorDeliveryBeanList);
            }
        });
        log.info("send change Ekey msg to device size is {}", collectorDeliveryBeanList.size());

        Lists.partition(collectorDeliveryBeanList, 200).forEach(collectorDeliveryBeanSubList -> {
            try {
                KafkaBean bean = createKafkaBean(collectorDeliveryBeanSubList);
                messageSenderService.sendMsgAsync(KafkaTopicConstants.SOUTH_EVENT_TOPIC, jsonService.objectToJson(bean));
            } catch (Exception e) {
                log.error("send change ekey msg to device error, error size is {}", collectorDeliveryBeanSubList.size());
            }
        });
    }

    private EkeHistoryRecordPo updateEKey(String encryptEkey, String sha256, String userName) {
        EkeHistoryRecordPo updatePo = new EkeHistoryRecordPo();
        updatePo.setIsCurr(false);
        ekeHistoryRecordRepositoryImpl.update(updatePo);

        EkeHistoryRecordPo ekeyPo = new EkeHistoryRecordPo();
        ekeyPo.setId(UUID.randomUUID().toString());
        ekeyPo.setEkey(encryptEkey);
        ekeyPo.setEkeySha256(sha256);
        ekeyPo.setIsCurr(true);
        ekeyPo.setCreator(userName);
        ekeyPo.setGmtCreate(dateTimeService.getCurrentTime());
        ekeHistoryRecordRepositoryImpl.insert(ekeyPo);
        return ekeyPo;
    }

    private void changeEkeyRelation(String encryptEkey, String userName, List<BatteryEkeyRelationEntity> batteryEkeyRelationEntities) {
        String currentTime = dateTimeService.getCurrentTime();
        List<BatteryEkeyRelationPo> pos = batteryEkeyRelationEntities.stream()
                .map(entity -> {
                    entity.setEkey(encryptEkey);
                    entity.setUpdater(userName);
                    entity.setGmtModified(currentTime);
                    return BatteryEkeyRelationConverter.converterEnToPo(entity);
                })
                .collect(Collectors.toList());
        batteryEkeyRelationRepositoryImpl.updateBatch(pos);
    }

    private void sendChangeKeyMsgToDevice(String collectorId, List<String> battIds, Map<String, String> oldKeyMap,
                                          Map<String, String> snMap, String newKey, List<CollectorDeliveryBean> collectorDeliveryBeanList) {
        log.info("sendChangeKeyMsgToDevice begin!");

        try {
            CollectorDeliveryBean collectorDeliveryBean = createCollectorDeliveryBean(collectorId, battIds, oldKeyMap, snMap, newKey);
            collectorDeliveryBeanList.add(collectorDeliveryBean);
       } catch (Exception e) {
            log.error("sendChangeKeyMsgToDevice collectorId:{}sendMsgAsync error!", collectorId, e);
        }
        log.debug("sendChangeKeyMsgToDevice end!");
    }

    private CollectorDeliveryBean createCollectorDeliveryBean(String collectorId, List<String> battIds, Map<String, String> oldKeyMap, Map<String, String> snMap, String newKey) {
        CollectorDeliveryBean collectorDeliveryBean = new CollectorDeliveryBean();
        collectorDeliveryBean.setCollectorId(collectorId);
        collectorDeliveryBean.setLogId(UUID.randomUUID().toString());
        collectorDeliveryBean.setFunctionType(EleKeyTypeEnums.MODIFY.getCode());
        collectorDeliveryBean.setDeviceType(DEVICE_TYPE);

        List<EleKeyDeliveryBean> ekeyData = battIds.stream()
                .map(battId -> {
                    EleKeyDeliveryBean eleKeyDeliveryBean = new EleKeyDeliveryBean();
                    eleKeyDeliveryBean.setSn(snMap.getOrDefault(battId, GlobalConstants.EMPTY_STR));
                    eleKeyDeliveryBean.setOldKey(oldKeyMap.getOrDefault(battId, GlobalConstants.EMPTY_STR));
                    eleKeyDeliveryBean.setNewKey(newKey);
                    return eleKeyDeliveryBean;
                })
                .collect(Collectors.toList());
        collectorDeliveryBean.setEleKeyDelData(ekeyData);

        return collectorDeliveryBean;
    }

    private KafkaBean createKafkaBean(List<CollectorDeliveryBean> collectorDeliveryBeans) {
        KafkaBean bean = new KafkaBean();
        bean.setType(KAFKA_TYPE_EKEY_DELIVERY.getId());
        bean.setData(collectorDeliveryBeans);
        return bean;
    }

    @SuppressWarnings("unchecked")
    private String getValueByCondition(String smpId, Map<String, Object> map) {
        Map<String, String> reidsMap = new HashMap<>();
        if (!blankService.isBlank(map) && map.containsKey(smpId)) {
            reidsMap = (Map<String, String>) map.get(smpId);
        }
        return reidsMap.getOrDefault(MAP_KEY_VALUE, GlobalConstants.EMPTY_STR);
    }

    /* Ended by AICoder, pid:f022624ac58764f142b70aa4c1d1853ce543061c */

    /* Started by AICoder, pid:zf000z380e7415414f1d0b2680bbc232b22040cb */
    @Override
    public void initEkey() {
        try {
            // 检查电子钥匙是否存在
            if (!ekeHistoryRecordRepositoryImpl.existEKey()) {
                // 生成新的电子钥匙并加密
                String randomString = CommonUtils.randomString(32);
                String encryptString = CryptoUtil.encrypt(randomString, MicroServiceOptional.MICRO_SERVICE_BATTERY_MANAGER);
                String sha256 = EncryptUtils.generateSHA256(randomString);

                // 创建新的电子钥匙记录
                EkeHistoryRecordPo ekeyPo = createNewEkeyPo(encryptString, sha256);
                ekeHistoryRecordRepositoryImpl.insert(ekeyPo);
                //发送操作日志
                initKeyLogSender(ekeyPo);
            } else {
                // 电子钥匙已经存在，记录警告日志
                log.warn("eKey has existed, skip init eKey.");
            }
        } catch (Exception e) {
            log.error("initEkey error", e);
        }
    }

    private EkeHistoryRecordPo createNewEkeyPo(String encryptString, String sha256) {
        EkeHistoryRecordPo ekeyPo = new EkeHistoryRecordPo();
        ekeyPo.setId(UUID.randomUUID().toString());
        ekeyPo.setEkey(encryptString);
        ekeyPo.setEkeySha256(sha256);
        ekeyPo.setIsCurr(true);
        ekeyPo.setCreator("System");
        ekeyPo.setGmtCreate(dateTimeService.getCurrentTime());
        return ekeyPo;
    }

    /* Ended by AICoder, pid:zf000z380e7415414f1d0b2680bbc232b22040cb */

    /* Started by AICoder, pid:w6565m2c6a17ff214933092a80cf8224c943742a */
    private void initKeyLogSender(EkeHistoryRecordPo ekeyPo) throws com.zte.uedm.common.exception.UedmException {
        String id = ekeyPo.getId();
        String creator = ekeyPo.getCreator();
        String gmtCreate = ekeyPo.getGmtCreate();

        String sbZh = String.format("电子钥匙ID：%s, 创建时间: %s, 创建人: %s", id, gmtCreate, creator);
        String sbEn = String.format("ekey id：%s, create time: %s, creator: %s", id, gmtCreate, creator);

        LogInputBean logInputBean = new LogInputBean();
        logInputBean.setUser(GlobalConstants.SYSTEM);
        logInputBean.setHost(GlobalConstants.SYSTEM_IP);
        logInputBean.setRank(OperlogBean.LogRank.operlog_rank_important);
        logInputBean.setOperateResult(OperLogContants.OperateResultSuccess);
        logInputBean.setDetailEn(sbEn);
        logInputBean.setDetailZh(sbZh);
        logInputBean.setModuleNameZh("电池防盗配置");
        logInputBean.setModuleNameEn("Battery theft Config");
        logInputBean.setOperationEn("ekey Initialize");
        logInputBean.setOperationZh("电子钥匙初始化");
        logInputBean.setOperateResource(new String[]{id});

        logUtils.generalLogSender(logInputBean);
    }
    /* Ended by AICoder, pid:w6565m2c6a17ff214933092a80cf8224c943742a */
}
