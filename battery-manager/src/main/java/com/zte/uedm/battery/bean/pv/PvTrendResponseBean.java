package com.zte.uedm.battery.bean.pv;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @ Author     ：10260977
 * @ Date       ：20:15 2022/3/7
 * @ Description：太阳能趋势分析响应类
 * @ Modified By：
 * @ Version: 1.0
 */
@Setter
@Getter
@ToString
public class PvTrendResponseBean
{
    /**
     * 太阳能系统id
     */
    private String pvId;
    /**
     * pv系统名称
     */
    private String name;
    /**
     * 记录日期
     */
    private String threshold;
    /**
     * 对比年
     */
    private String compareYear;

    List<PvTrendDataBean> data;
}
