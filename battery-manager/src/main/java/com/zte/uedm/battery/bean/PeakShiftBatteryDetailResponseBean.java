package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class PeakShiftBatteryDetailResponseBean
{

    private String siteId;
    private String battId;
    //battpack or sp mo_id
    private String id;

    //充电"0"，放电"1"
    private String type;

    private String startTime;

    private String endTime;

    private String startVoltage;

    private String endVoltage;

    private Boolean llvd1;

    private String gmtCreate;

    private String gmtModified;
    private String siteName;
    private String battName;
    private String position;
    private String powerSystemName;
}
