package com.zte.uedm.battery.a_infrastructure.cache.manager;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;
import com.zte.uedm.battery.a_domain.cache.AdapterCacheMgr;
import com.zte.uedm.battery.a_domain.cache.provider.AdapterCacheDataProvider;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.component.caffeine.service.CacheMgr;
import com.zte.uedm.component.caffeine.service.impl.CacheBaseManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 适配缓存
 */
@Slf4j
@Component("CACHE_NAME_ADAPTER_INSTANCE")
public class AdapterCacheManager extends CacheBaseManager implements AdapterCacheMgr<String, AdapterEntity>,
        CacheMgr<BaseCacheBean> {
    private static final String CACHE_NAME = "CACHE_NAME_ADAPTER_INSTANCE";

    @Autowired
    private AdapterCacheDataProvider adapterCacheDataProvider;

    @Override
    public String getCacheName() {
        return CACHE_NAME;
    }

    @Override
    public CacheDataProvider getCacheDataProvider() {
        return adapterCacheDataProvider;
    }

    @Override
    public Set<String> init() {
        Set<String> keys = new HashSet<>();
        try {
            keys = super.init();
        } catch (UedmException e) {
            log.error("AdapterCacheManager init error", e);
        }
        return keys;
    }
    @Override
    /* Started by AICoder, pid:z7349f6d449a776143a60ac580669d1ff6107440 */
    public List<AdapterEntity> selectByIds(List<String> ids){
        List<AdapterEntity> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(ids)){
            return list;
        }
        try {
            list= queryByKeys(new HashSet<>(ids))
                    .stream()
                    .filter(Objects::nonNull)
                    .map(AdapterEntity.class::cast)
                    .collect(Collectors.toList());
        } catch (UedmException e) {
            log.error("AdapterCacheManager selectByIds", e);
        }
          return list;
    }
    /* Ended by AICoder, pid:z7349f6d449a776143a60ac580669d1ff6107440 */
    public List<AdapterEntity> selectAll(){
        List<AdapterEntity> list = new ArrayList<>();
        try {
            list= queryAll()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(AdapterEntity.class::cast)
                    .collect(Collectors.toList());
        } catch (UedmException e) {
            log.error("AdapterCacheManager selectByIds", e);
        }
        return list;
    }

    /* Started by AICoder, pid:ea8659f16fc77f214a320a56c0c42b1f96f5895f */
    @Override
    public Map<String, AdapterEntity> selectMapByIds(Set<String> adapterIds) throws UedmException {
        Map<String, AdapterEntity> result = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(adapterIds)) {
            return result;
        }

        result = queryByKeys(adapterIds).stream()
                .filter(Objects::nonNull)
                .map(AdapterEntity.class::cast)
                .collect(Collectors.toMap(AdapterEntity::getId, entity -> entity, (oldEntity, newEntity) -> newEntity));

        log.info("AdapterCacheManager selectByIds param size: {}, data size: {}", adapterIds.size(), result.size());
        return result;
    }
    /* Ended by AICoder, pid:ea8659f16fc77f214a320a56c0c42b1f96f5895f */

    /* Started by AICoder, pid:n5c0eseaa4d614014a620b32b069e012a338e381 */
    @Override
    public List<String> selectAdapterPointId(Set<String> adapterPointIds) {
        try {
            return queryAll()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(AdapterEntity.class::cast)
                    .flatMap(adapterEntity ->
                            adapterEntity.getAdapterPoints().stream()
                                    .filter(adapterPoint -> adapterPointIds.contains(adapterPoint.getId()))
                                    .map(adapterPoint -> adapterEntity.getId())
                    )
                    .collect(Collectors.toList());
        } catch (UedmException e) {
            log.error("AdapterCacheManager selectAdapterPointId", e);
            return Collections.emptyList();
        }
    }
    /* Ended by AICoder, pid:n5c0eseaa4d614014a620b32b069e012a338e381 */

}
