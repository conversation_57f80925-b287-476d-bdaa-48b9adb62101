package com.zte.uedm.battery.pv.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 太阳能监控概览维度bean
 * @Date 2023/9/25 上午10:54
 * @Version 1.0
 */
@Getter
@Setter
@ToString
public class SolarMonitorOverviewDimensionsBean {
    /**
     * 太阳能监控概览维度
     */
    private String id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 顺序
     */
    private Integer sequence;

    /**
     * 维度名称
     */
    private String name;

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 是否固定
     */
    private boolean defaultFixed;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date gmtModified;
}
