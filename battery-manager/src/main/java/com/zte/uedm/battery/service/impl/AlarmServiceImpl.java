package com.zte.uedm.battery.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.battery.bean.alarm.*;
import com.zte.uedm.battery.rpc.HistoryAlarmServiceRpc;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.consts.GlobalBaseConstants;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.enums.AlarmLevelEnum;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeRpcUtil;
import com.zte.uedm.service.config.optional.MocOptional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.uedm.battery.rpc.ActiveAlarmServiceRpc;
import com.zte.uedm.battery.service.AlarmService;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;

import lombok.extern.slf4j.Slf4j;
import retrofit2.Call;
import retrofit2.Response;

import static com.zte.uedm.battery.util.constant.BatteryConstant.VISIBLE;

@Service
@Slf4j
public class AlarmServiceImpl implements AlarmService
{

    private static final int ALARM_STATE_UN_RESTORED = 1; // 告警状态-未恢复

    private static final int ALARM_STATE_RESTORED = 2; // 告警状态-已恢复

    private static final int HISTORY_ALARM_TOPPAGE_COUNT = 1000; // 平台历史告警首页最大数量

    private static final int HISTORY_ALARM_MAX_COUNT = 3000; // 返回历史告警的最大数量

    private static final int HISTORY_ALARM_IDSPAGE_COUNT = 400; // 平台历史告警ids分页的单页最大数量

    @Autowired
    private ActiveAlarmServiceRpc activeAlarmServiceRpc;

    @Autowired
    private BlankService blankService;
    @Autowired
    private JsonService jsonService;

    @Autowired
    private HistoryAlarmServiceRpc historyAlarmServiceRpc;

    /**
     * 根据条件查询告警（是否可见）
     * @param oids
     * @param visibles
     * @return
     * @throws UedmException
     */
    @Override
    public List<Alarm> getAlarmByOid(List<String> oids, List<Integer> visibles) throws UedmException {
    	try
        {
            ActiveAlarmBody body = new ActiveAlarmBody();
            body.setPage(1);
            body.setPagesize(1000);

            AlarmConditionBean condition = new AlarmConditionBean();
            List<AlarmPositionPath> positionPaths = new ArrayList<AlarmPositionPath>();
            for (String id : oids)
            {
                AlarmPositionPath positionPath = new AlarmPositionPath();
                positionPath.setMe(id);
                positionPaths.add(positionPath);
            }
            condition.setPositionpaths(positionPaths);
            condition.setVisibles(visibles);
            body.setCondition(condition);
            String username = UserThreadLocal.getuserName();
            String lang = UserThreadLocal.getLanguageOption();
            log.info("userName is {}, lang is {}", username, lang);
            Call<AlarmResponse> call = activeAlarmServiceRpc.getActiveAlarm(body, lang, username);
            Response<AlarmResponse> response = call.execute();
            AlarmResponse alarmResponse = response.body();
            log.info("getActiveAlarm : " + jsonService.objectToJson(alarmResponse));
            for (Alarm alarm : alarmResponse.getAlarms())
            {
                alarm.setAlarmshowtime(TimeUtils.getStrTime(alarm.getAlarmraisedtime()));
                alarm.setMoOid(alarm.getMe());
            }
            return alarmResponse.getAlarms();
        }
        catch (Exception e)
        {
            log.error("getActiveAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }


    @Override
    public List<Alarm> getAlarmData(AlarmSchBean alarmSchBean) throws UedmException
    {
        List<Alarm> list = new ArrayList<>();
        if (alarmSchBean == null)
        {
            return list;
        }
        List<Alarm> activeAlarmList = getActiveAlarmFromPass(alarmSchBean);
        List<Alarm> historyAlarmList = getHistoryAlarmFromPass(alarmSchBean);
        list.addAll(activeAlarmList);
        list.addAll(historyAlarmList);

        return list;
    }

    @Override
    public List<Alarm> getAlarmDataBatch(AlarmBatchSchBean alarmBatchSchBean) throws UedmException
    {
        List<Alarm> list = new ArrayList<>();
        if (alarmBatchSchBean == null)
        {
            return list;
        }
        List<Alarm> activeAlarmList = getBatchActiveAlarmFromPass(alarmBatchSchBean);
        List<Alarm> historyAlarmList = getBatchHistoryAlarmFromPass(alarmBatchSchBean);
        list.addAll(activeAlarmList);
        list.addAll(historyAlarmList);

        return list;
    }

    @Override
    public List<Alarm> getLastHistoryAlarmData(AlarmSchBean alarmSchBean) throws UedmException
    {
        List<Alarm> alarmList = new ArrayList<>();
        try
        {
            HistoryAlarmBody body = new HistoryAlarmBody();
            body.setQuerynum(1);

            AlarmConditionBean condition = setHistoryAlarmCondition(alarmSchBean);

            body.setCondition(condition);
            log.info("body:{} ",body.getCondition().toString());
            Call<AlarmResponse> call = historyAlarmServiceRpc.getHistoryAlarm(body);
            Response<AlarmResponse> response = call.execute();
            log.info("getHistoryAlarm:{}", jsonService.objectToJson(response.body()));
            log.info("response.isSuccessful():{}", response.isSuccessful());
            AlarmResponse alarmResponse = response.body();
            alarmList = alarmResponse.getAlarms();
            return alarmList;
        }
        catch (Exception e)
        {
            log.error("getHistoryAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    /**
     * 调用平台接口查询当前告警
     *
     * @param alarmSchBean
     *            查询条件
     * @return 当前告警列表
     * @throws UedmException
     *             自定义异常
     */
    private List<Alarm> getActiveAlarmFromPass(AlarmSchBean alarmSchBean) throws UedmException
    {
        try
        {
            ActiveAlarmBody body = new ActiveAlarmBody();
            body.setPage(1);
            body.setPagesize(1000);

            AlarmConditionBean condition = setAlarmCondition(alarmSchBean);
            body.setCondition(condition);
            log.debug("body : " + jsonService.objectToJson(body));
            String username = UserThreadLocal.getuserName();
            String lang = UserThreadLocal.getLanguageOption();
            log.debug("userName is {}, lang is {}", username, lang);
            Call<AlarmResponse> call = activeAlarmServiceRpc.getActiveAlarm(body,lang,username);
            Response<AlarmResponse> response = call.execute();
            log.debug("getActiveAlarm : " + jsonService.objectToJson(response));
            AlarmResponse alarmResponse = response.body();
            for (Alarm alarm : alarmResponse.getAlarms())
            {
                alarm.setAlarmshowtime(TimeUtils.getStrTime(alarm.getAlarmraisedtime()));
            }
            return alarmResponse.getAlarms();
        }
        catch (Exception e)
        {
            log.error("getActiveAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    /**
     * 根据查询条件设置平台告警的Condition
     *
     * @param alarmBatchSchBean 查询条件
     * @return 告警的Condition
     * @throws UedmException
     */
    private AlarmConditionBean setAlarmConditionBatch(AlarmBatchSchBean alarmBatchSchBean) throws UedmException, ParseException
    {
        AlarmConditionBean condition = new AlarmConditionBean();
        if (CollectionUtils.isNotEmpty(alarmBatchSchBean.getIds()))
        {
            List<AlarmPositionPath> positionPaths = new ArrayList<>();
            for(String id : alarmBatchSchBean.getIds())
            {
                AlarmPositionPath positionPath = new AlarmPositionPath();
                positionPath.setMe(id);
                positionPaths.add(positionPath);
                condition.setPositionpaths(positionPaths);
            }
        }
        List<AlarmCode> acs = alarmBatchSchBean.getAlarmCodes();
        condition.setAlarmcodeconds(acs);
        if (!blankService.isBlank(alarmBatchSchBean.getSeverities()))
        {
            condition.setSeverities(alarmBatchSchBean.getSeverities());
        }

        if (!blankService.isBlank(alarmBatchSchBean.getAckState()))
        {
            condition.setAckstates(alarmBatchSchBean.getAckState());
        }
        String startRaisedTime = alarmBatchSchBean.getAlarmRaisedTimeStart();
        String endRaisedTime = alarmBatchSchBean.getAlarmRaisedTimeEnd();
        if (!blankService.isBlank(startRaisedTime) && !blankService.isBlank(endRaisedTime))
        {
            AlarmRaisedTime alarmRaisedTime = new AlarmRaisedTime();
            alarmRaisedTime.setMode(0);
            alarmRaisedTime.setStart(TimeUtils.getLongTime(startRaisedTime));
            alarmRaisedTime.setEnd(TimeUtils.getLongTime(endRaisedTime));
            condition.setAlarmraisedtime(alarmRaisedTime);
        }
        if(CollectionUtils.isNotEmpty(alarmBatchSchBean.getMocs()))
        {
            List<AlarmMocTypeBean> mocs = new ArrayList<>();
            for (String moc : alarmBatchSchBean.getMocs())
            {
                AlarmMocTypeBean alarmMocTypeBean = new AlarmMocTypeBean();
                alarmMocTypeBean.setMoc(moc);
                mocs.add(alarmMocTypeBean);
            }
            condition.setMocs(mocs);
        }
        return condition;
    }

    /**
     * 调用平台接口查询当前告警
     *
     * @param alarmBatchSchBean
     *            查询条件
     * @return 当前告警列表
     * @throws UedmException
     *             自定义异常
     */
    public List<Alarm> getBatchActiveAlarmFromPass(AlarmBatchSchBean alarmBatchSchBean) throws UedmException
    {
        try
        {
            ActiveAlarmBody body = new ActiveAlarmBody();
            body.setPage(1);
            body.setPagesize(HISTORY_ALARM_TOPPAGE_COUNT);

            AlarmConditionBean condition = setAlarmConditionBatch(alarmBatchSchBean);
            condition.setVisibles(Arrays.asList(VISIBLE));
            body.setCondition(condition);
            log.debug("body : " + jsonService.objectToJson(body));
            String lang = UserThreadLocal.getLanguageOption();
            log.debug("lang is {}", lang);
            Call<AlarmResponse> call = activeAlarmServiceRpc.getActiveAlarmWithLang(body,lang);
            Response<AlarmResponse> response = call.execute();
            log.debug("getActiveAlarm : " + jsonService.objectToJson(response));
            AlarmResponse alarmResponse = response.body();
            int totalcount = 0;
            List<Alarm> alarms = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(alarmResponse)) {
                List<Alarm> list = alarmResponse.getAlarms();
                alarms.addAll(list);
                totalcount = alarmResponse.getTotalcount();
            }
            if (totalcount > HISTORY_ALARM_TOPPAGE_COUNT) {
                for (int i = 0; i < totalcount/HISTORY_ALARM_TOPPAGE_COUNT; i++)
                {
                    body.setPage(i+2);
                    Call<AlarmResponse> callTemp = activeAlarmServiceRpc.getActiveAlarmWithLang(body,lang);
                    Response<AlarmResponse> responseTemp = callTemp.execute();
                    log.debug("getActiveAlarmTemp : " + jsonService.objectToJson(responseTemp));
                    AlarmResponse alarmResponseTemp = response.body();
                    if (ObjectUtils.isNotEmpty(alarmResponseTemp)) {
                        List<Alarm> list = alarmResponseTemp.getAlarms();
                        alarms.addAll(list);
                    }
                }
            }
            for (Alarm alarm : alarms)
            {
                alarm.setAlarmshowtime(TimeUtils.getStrTime(alarm.getAlarmraisedtime()));
            }
            return alarms;
        }
        catch (Exception e)
        {
            log.error("getActiveAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }


    /**
     * 调用平台接口查询历史告警
     *
     * @param alarmBatchSchBean
     *            查询条件
     * @return 历史告警列表
     * @throws UedmException
     *             自定义异常
     */
    public List<Alarm> getBatchHistoryAlarmFromPass(AlarmBatchSchBean alarmBatchSchBean) throws UedmException
    {
        try
        {
            HistoryAlarmBody body = new HistoryAlarmBody();
            body.setQuerynum(HISTORY_ALARM_TOPPAGE_COUNT);

            AlarmConditionBean condition = setAlarmConditionBatch(alarmBatchSchBean);
            condition.setVisibles(Arrays.asList(VISIBLE));
            body.setCondition(condition);
            Call<AlarmResponse> call = historyAlarmServiceRpc.getHistoryAlarm(body);
            Response<AlarmResponse> response = call.execute();
            log.debug("getHistoryAlarm: " + jsonService.objectToJson(response));

            AlarmResponse alarmResponse = response.body();
            if(null == alarmResponse)
            {
                return new ArrayList<>();
            }
            List<Alarm> alarmList = alarmResponse.getAlarms();
            if (alarmResponse.getTotalcount() > HISTORY_ALARM_MAX_COUNT)
            {
                alarmList.addAll(getHisAlarmOverTopPage(alarmResponse.getIds().subList(0, HISTORY_ALARM_MAX_COUNT)));
            }
            else if (alarmResponse.getTotalcount() > HISTORY_ALARM_TOPPAGE_COUNT)
            {
                alarmList.addAll(getHisAlarmOverTopPage(alarmResponse.getIds()));
            }

            for (Alarm alarm : alarmList)
            {
                alarm.setAlarmshowtime(TimeUtils.getStrTime(alarm.getAlarmraisedtime()));
            }

            return alarmList;
        }
        catch (Exception e)
        {
            log.error("getHistoryAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    public List<Alarm> getAlarmListForBattery(List<String> mocIdList, String startTime, String endTime) throws UedmException
    {
        if(CollectionUtils.isNotEmpty(mocIdList)) {
            try
            {
                ActiveAlarmBody body = new ActiveAlarmBody();
                body.setPage(null);
                body.setPagesize(null);
                AlarmConditionBean condition = setAlarmConditionForBattery(mocIdList, startTime, endTime);  //评估任务，查询所有告警（不设置visible字段）
                body.setCondition(condition);
                log.debug("getFullNotConfirmAlarmList body : " + jsonService.objectToJson(body));
                Call<AlarmResponse> call = activeAlarmServiceRpc.getActiveAlarmWithLang(body, GlobalBaseConstants.LANGUAGE_OPTION1_CHINESE);
                Response<AlarmResponse> response = call.execute();
                log.debug("getFullNotConfirmAlarmList: " + jsonService.objectToJson(response));
                AlarmResponse alarmResponse = response.body();
                return null == alarmResponse ? new ArrayList<>() : alarmResponse.getAlarms();
            }
            catch (Exception e)
            {
                log.error("getFullNotConfirmAlarmList", e);
                throw UedmErrorCodeRpcUtil.rpcInterfaceCallFailed("call getActiveAlarmWithLang failed!");
            }
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public AlarmResponse getAlarmListForSolarMonitor(List<String> mocIdList, Integer page, Integer pageSize) throws UedmException {
        if (CollectionUtils.isNotEmpty(mocIdList)) {
            try {
                ActiveAlarmBody body = new ActiveAlarmBody();
                body.setPage(page);
                body.setPagesize(pageSize);
                AlarmConditionBean condition = setAlarmConditionForSolarMonitor(mocIdList); //查询可见告警（设置visibles字段）
                body.setCondition(condition);
                log.debug("getAlarmListForSolarMonitor body : " + jsonService.objectToJson(body));
                Call<AlarmResponse> call = activeAlarmServiceRpc.getActiveAlarmWithLang(body, GlobalBaseConstants.LANGUAGE_OPTION1_CHINESE);
                Response<AlarmResponse> response = call.execute();
                log.debug("getAlarmListForSolarMonitor: " + jsonService.objectToJson(response));
                AlarmResponse alarmResponse = response.body();
                return alarmResponse;
            } catch (Exception e) {
                log.error("getAlarmListForSolarMonitor", e);
                throw UedmErrorCodeRpcUtil.rpcInterfaceCallFailed("call getActiveAlarmWithLang failed!");
            }
        } else {
            return null;
        }
    }

    /**
     * 调用平台接口查询历史告警
     *
     * @param alarmSchBean
     *            查询条件
     * @return 历史告警列表
     * @throws UedmException
     *             自定义异常
     */
    private List<Alarm> getHistoryAlarmFromPass(AlarmSchBean alarmSchBean) throws UedmException
    {
        try
        {
            HistoryAlarmBody body = new HistoryAlarmBody();
            body.setQuerynum(HISTORY_ALARM_TOPPAGE_COUNT);

            AlarmConditionBean condition = setAlarmCondition(alarmSchBean);

            body.setCondition(condition);
            Call<AlarmResponse> call = historyAlarmServiceRpc.getHistoryAlarm(body);
            Response<AlarmResponse> response = call.execute();
            log.debug("getHistoryAlarm: " + jsonService.objectToJson(response));

            AlarmResponse alarmResponse = response.body();
            List<Alarm> alarmList = alarmResponse.getAlarms();
            if (alarmResponse.getTotalcount() > HISTORY_ALARM_MAX_COUNT)
            {
                alarmList.addAll(getHisAlarmOverTopPage(alarmResponse.getIds().subList(0, HISTORY_ALARM_MAX_COUNT)));
            }
            else if (alarmResponse.getTotalcount() > HISTORY_ALARM_TOPPAGE_COUNT)
            {
                alarmList.addAll(getHisAlarmOverTopPage(alarmResponse.getIds()));
            }

            for (Alarm alarm : alarmList)
            {
                alarm.setAlarmshowtime(TimeUtils.getStrTime(alarm.getAlarmraisedtime()));
            }

            return alarmList;
        }
        catch (Exception e)
        {
            log.error("getHistoryAlarm", e);
            throw new UedmException(-1, e.getMessage());
        }
    }

    /**
     * 根据查询条件设置平台告警的Condition
     *
     * @param alarmSchBean
     *            查询条件
     * @return 告警的Condition
     * @throws UedmException
     */
    private AlarmConditionBean setAlarmCondition(AlarmSchBean alarmSchBean) throws UedmException, ParseException
    {
        AlarmConditionBean condition = new AlarmConditionBean();
        if (!blankService.isBlank(alarmSchBean.getId()))
        {
            List<AlarmPositionPath> positionPaths = new ArrayList<>();
            AlarmPositionPath positionPath = new AlarmPositionPath();
            positionPath.setMe(alarmSchBean.getId());
            positionPaths.add(positionPath);
            condition.setPositionpaths(positionPaths);
        }
        List<AlarmCode> acs = alarmSchBean.getAlarmCodes();
        condition.setAlarmcodeconds(acs);
        if (!blankService.isBlank(alarmSchBean.getSeverities()))
        {
            condition.setSeverities(alarmSchBean.getSeverities());
        }

        if (!blankService.isBlank(alarmSchBean.getAckState()))
        {
            condition.setAckstates(alarmSchBean.getAckState());
        }
        String startRaisedTime = alarmSchBean.getAlarmRaisedTimeStart();
        String endRaisedTime = alarmSchBean.getAlarmRaisedTimeEnd();
        if (!blankService.isBlank(startRaisedTime) && !blankService.isBlank(endRaisedTime))
        {
            AlarmRaisedTime alarmRaisedTime = new AlarmRaisedTime();
            alarmRaisedTime.setMode(0);
            alarmRaisedTime.setStart(TimeUtils.getLongTime(startRaisedTime));
            alarmRaisedTime.setEnd(TimeUtils.getLongTime(endRaisedTime));
            condition.setAlarmraisedtime(alarmRaisedTime);
        }
        condition.setVisibles(Arrays.asList(VISIBLE));
        return condition;
    }

    private AlarmConditionBean setAlarmConditionForBattery(List<String> moIdList, String startRaisedTime, String endRaisedTime) throws ParseException {
        AlarmConditionBean condition = new AlarmConditionBean();
        List<AlarmPositionPath> positionPaths = new ArrayList<>();
        for (String moId : moIdList)
        {
            AlarmPositionPath positionPath = new AlarmPositionPath();
            positionPath.setMe(moId);
            positionPaths.add(positionPath);
        }
        condition.setPositionpaths(positionPaths);
        condition.setAlarmcodeconds(BatteryConstant.filterAlarmCodeList);
        Sortinfo sortinfo = new Sortinfo();
        sortinfo.setSortfield("alarmraisedtime");
        // alarmSchBean.getSortdirection() must be (defult 0) or 1
        sortinfo.setSortdirection(0);
        condition.setSortinfo(sortinfo);
        List<Integer> ackStates = new ArrayList<>();
        // 只查未确认的告警信息
        ackStates.add(new Integer("2"));
        condition.setAckstates(ackStates);
        if (!blankService.isBlank(startRaisedTime) && !blankService.isBlank(endRaisedTime))
        {
            AlarmRaisedTime alarmRaisedTime = new AlarmRaisedTime();
            alarmRaisedTime.setMode(0);
            alarmRaisedTime.setStart(TimeUtils.getLongTime(startRaisedTime));
            alarmRaisedTime.setEnd(TimeUtils.getLongTime(endRaisedTime));
            condition.setAlarmraisedtime(alarmRaisedTime);
        }
        return condition;
    }

    private AlarmConditionBean setAlarmConditionForSolarMonitor(List<String> moIdList) throws ParseException {
        AlarmConditionBean condition = new AlarmConditionBean();
        List<AlarmPositionPath> positionPaths = new ArrayList<>();
        for (String moId : moIdList) {
            AlarmPositionPath positionPath = new AlarmPositionPath();
            positionPath.setMe(moId);
            positionPaths.add(positionPath);
        }
        condition.setPositionpaths(positionPaths);
        //告警级别,查询所有级别的告警
        List<Integer> severities = Arrays.asList(AlarmLevelEnum.critical.getId(),
                AlarmLevelEnum.major.getId(), AlarmLevelEnum.minor.getId(),
                AlarmLevelEnum.warning.getId());
        condition.setSeverities(severities);
        //网元类型
        List<AlarmMocTypeBean> mocs = new ArrayList<>();
        List<String> mocTypeList = Arrays.asList(MocOptional.SITE.getId(), MocOptional.PV.getId());
        for (String moc : mocTypeList) {
            AlarmMocTypeBean alarmMocTypeBean = new AlarmMocTypeBean();
            alarmMocTypeBean.setMoc(moc);
            mocs.add(alarmMocTypeBean);
        }
        condition.setMocs(mocs);
        condition.setVisibles(Arrays.asList(VISIBLE));
        return condition;
    }

    /**
     * 根据查询条件设置平台历史告警的Condition
     *
     * @param alarmSchBean
     *            查询条件
     * @return 告警的Condition
     * @throws UedmException
     */
    private AlarmConditionBean setHistoryAlarmCondition(AlarmSchBean alarmSchBean) throws ParseException
    {
        AlarmConditionBean condition = new AlarmConditionBean();
        if (!blankService.isBlank(alarmSchBean.getId()))
        {
            List<AlarmPositionPath> positionPaths = new ArrayList<>();
            AlarmPositionPath positionPath = new AlarmPositionPath();
            positionPath.setMe(alarmSchBean.getId());
            positionPaths.add(positionPath);
            condition.setPositionpaths(positionPaths);
        }
        List<AlarmCode> acs = alarmSchBean.getAlarmCodes();
        condition.setAlarmcodeconds(acs);
        if (StringUtils.isNotBlank(alarmSchBean.getSortfield())) {
            Sortinfo sortinfo = new Sortinfo();
            sortinfo.setSortfield(alarmSchBean.getSortfield());
            // alarmSchBean.getSortdirection() must be (defult 0) or 1
            if (1 == alarmSchBean.getSortdirection()) {
                sortinfo.setSortdirection(1);
            }
            condition.setSortinfo(sortinfo);
        }
        if (!blankService.isBlank(alarmSchBean.getSeverities()))
        {
            condition.setSeverities(alarmSchBean.getSeverities());
        }

        if (!blankService.isBlank(alarmSchBean.getAckState()))
        {
            condition.setAckstates(alarmSchBean.getAckState());
        }
        String startRaisedTime = alarmSchBean.getAlarmRaisedTimeStart();
        String endRaisedTime = alarmSchBean.getAlarmRaisedTimeEnd();
        if (!blankService.isBlank(startRaisedTime) && !blankService.isBlank(endRaisedTime))
        {
            AlarmRaisedTime alarmRaisedTime = new AlarmRaisedTime();
            alarmRaisedTime.setMode(0);
            alarmRaisedTime.setStart(TimeUtils.getLongTime(startRaisedTime));
            alarmRaisedTime.setEnd(TimeUtils.getLongTime(endRaisedTime));
            condition.setAlarmraisedtime(alarmRaisedTime);
        }
        return condition;
    }

    /**
     * 根据告警id查询历史告警
     *
     * @param ids
     *            告警id组成的字符串
     * @return 对应id的历史告警
     * @throws UedmException
     **/
    private List<Alarm> getHistoryAlarmById(String ids) throws UedmException
    {
        try
        {
            Call<AlarmResponse> call = historyAlarmServiceRpc.getHistoryAlarmById(ids);
            Response<AlarmResponse> response = call.execute();
            log.info("response>>>: " + jsonService.objectToJson(response));
            AlarmResponse alarmResponse = response.body();
            return alarmResponse.getAlarms();
        }
        catch (Exception e)
        {
            log.error("getHistoryAlarmById", e);
            throw new UedmException(-1, e.getMessage());
        }
    }


    /**
     * 获取平台首页之后的所有历史告警
     *
     * @param ids
     *            历史告警id集合
     * @return 平台首页之后的所有历史告警
     * @throws UedmException
     **/
    protected List<Alarm> getHisAlarmOverTopPage(List<Long> ids) throws UedmException
    {
        List<Alarm> list = new ArrayList<>();

        for (int i = HISTORY_ALARM_TOPPAGE_COUNT; i < ids.size(); i = i + HISTORY_ALARM_IDSPAGE_COUNT)
        {
            if (i + HISTORY_ALARM_IDSPAGE_COUNT > ids.size())
            {
                list.addAll(getHistoryAlarmById("{\"ids\":" + ids.subList(i, ids.size()).toString()
                        + ",\"sortinfo\":{\"sortfield\":\"alarmclearedtime\",\"sortdirection\":0}}"));
            }
            else
            {
                list.addAll(getHistoryAlarmById("{\"ids\":" + ids.subList(i, i + HISTORY_ALARM_IDSPAGE_COUNT).toString()
                        + ",\"sortinfo\":{\"sortfield\":\"alarmclearedtime\",\"sortdirection\":0}}"));
            }
        }
        return list;
    }
}
