/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：MoAssetInstanceVo
 * 文件作者：00248587
 * 开发时间：2023/3/7
 */
package com.zte.uedm.battery.rpc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 监控对象关联资产信息
 */
@Setter
@Getter
@ToString
public class MoAssetInstanceVo
{

    /**
     * 监控对象id
     */
    private String id;
    /**
     * 资产id
     */
    private String assetNumber;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 制造商
     */
    private String manufacture;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 系列
     */
    private String series;
    /**
     * 型号
     */
    private String model;
    /**
     * 启用日期
     */
    private String startDate;

    /**
     * 电池生产日期
     */
    private String productionDate;
}
