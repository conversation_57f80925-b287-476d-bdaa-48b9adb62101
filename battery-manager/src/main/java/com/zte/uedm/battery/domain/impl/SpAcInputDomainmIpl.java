package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.alarm.ActiveAlarmQueryEntity;
import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.battery.domain.SpAcInputDomain;
import com.zte.uedm.battery.enums.batttest.AcInputStatusEnums;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.service.impl.AlarmPgCacheServiceImpl;
import com.zte.uedm.battery.service.impl.AlarmServiceImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SpAcInputDomainmIpl implements SpAcInputDomain {

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Autowired
    private AlarmServiceImpl alarmServiceImpl;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private MonitorManagerRpcImpl monitorManagerRpcImpl;
    @Autowired
    private AlarmPgCacheServiceImpl alarmPgCacheService;

    /**
     * 交流输入状态告警码
     */
    private static final List<String> alarmCode = new ArrayList<>();

    static {
        alarmCode.add("50031004");
        alarmCode.add("50031005");
        alarmCode.add("50031006");
        alarmCode.add("50031026");
    }

    /**
     *
     */
    private static final String COMMUNICATION_NORMAL = "0";


    /**
     * 根据监控对象id列表，moc类型，判断交流输入状态
     */
    public Map<String, IdNameBean> getAcInputStatusBySpIds(List<String> spIds,String moc,List<String> alarmCode,String languageOption)throws UedmException
    {

        Map<String, IdNameBean> resultMap = new HashMap<>();
        ActiveAlarmQueryEntity activeAlarmQueryEntity = new ActiveAlarmQueryEntity();
        activeAlarmQueryEntity.setMeList(spIds);
        //获取实时告警码
        List<AlarmDTO> alarmByOid = alarmPgCacheService.getByCondition(activeAlarmQueryEntity);   //计算交流输入状态，不涉及显示，查询所有告警


        log.info("BatteryTestServiceImpl selectByCondition alarmByOid->  alarmByOid.size:{}", alarmByOid.size());
        Map<String, List<AlarmDTO>> alarmMap = alarmByOid.stream().collect(Collectors.groupingBy(AlarmDTO::getMe));

        for (String spid:spIds){
            IdNameBean acInputStatusBean = new IdNameBean();
            acInputStatusBean.setId(AcInputStatusEnums.NORMAL.getId());
            String name = AcInputStatusEnums.NORMAL.getName();
            String mapFieldByLanguageOption = i18nUtils.getMapFieldByLanguageOption(name, languageOption);
            acInputStatusBean.setName(mapFieldByLanguageOption);
            List<AlarmDTO> strings = alarmMap.get(spid);
            if (null != strings) {
                List<String> codeList = strings.stream().map(bean->String.valueOf(bean.getAlarmcode())).collect(Collectors.toList());
                codeList.retainAll(alarmCode);
                if (!CollectionUtils.isEmpty(codeList)) {
                    acInputStatusBean.setId(AcInputStatusEnums.ABNORMAL.getId());
                    String abnormalName = AcInputStatusEnums.ABNORMAL.getName();
                    String mapFieldByLanguageOption1 = i18nUtils.getMapFieldByLanguageOption(abnormalName, languageOption);
                    acInputStatusBean.setName(mapFieldByLanguageOption1);
                }
            }
            resultMap.put(spid,acInputStatusBean);
        }
        return resultMap;
    }

    @Override
    public Map<String, IdNameBean> getAcInputStatusByCondition(List<String> spIds, String languageOption) throws UedmException
    {
        Map<String, IdNameBean> result = new HashMap<>();
        Map<String, IdNameBean> acInputStatusMap = getAcInputStatusBySpIds(spIds, MocOptional.ACDP.getId(), alarmCode, languageOption);
        log.info("BatteryTestServiceImpl getAcInputStatusByCondition acInputStatusMap size:{}", acInputStatusMap.size());
        log.debug("BatteryTestServiceImpl getAcInputStatusByCondition acInputStatusMap :{}", acInputStatusMap);
        //通过RPC获取监控对象通讯状态,key监控对象moId，Value状态（0:正常）
        Map<String, String> objectCommunicationStatusMap = monitorManagerRpcImpl.queryObjectCommunicationStatus(spIds);
        log.info("BatteryTestServiceImpl getAcInputStatusByCondition objectCommunicationStatusMap size:{}", objectCommunicationStatusMap.size());
        log.debug("BatteryTestServiceImpl getAcInputStatusByCondition objectCommunicationStatusMap :{}", objectCommunicationStatusMap);
        for(String spId : spIds)
        {
            if(StringUtils.isNotBlank(spId))
            {
                IdNameBean acInputStatus = getAcInputStatus(acInputStatusMap, spId, objectCommunicationStatusMap);
                result.put(spId, acInputStatus);
            }
        }
        return result;
    }

    public IdNameBean getAcInputStatus(Map<String, IdNameBean> acInputStatusMap, String spId,Map<String, String> objectCommunicationStatusMap)
    {
        String comm = objectCommunicationStatusMap.get(spId);
        IdNameBean acInputStatusBean = new IdNameBean();
        if (COMMUNICATION_NORMAL.equals(comm))
        {
            acInputStatusBean = acInputStatusMap.get(spId);
        }
        return acInputStatusBean;
    }
}
