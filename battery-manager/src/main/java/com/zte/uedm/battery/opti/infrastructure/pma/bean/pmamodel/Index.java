package com.zte.uedm.battery.opti.infrastructure.pma.bean.pmamodel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class Index implements Serializable {

    @JSONField(name = "nfctid")
    private String nfctId = "";

    @JSONField(name = "id")
    private String id = ""; // 指标ID

    @JSONField(name = "motid")
    private String motId = ""; // 测量对象类型ID

    @JSONField(name = "name")
    private String name;// 指标的名称

    @JSONField(name = "desc")
    private String desc = "";// 指标的详细描述

    @JSONField(name = "groupid")
    private String groupId;// 计数器的分组ID

    @JSONField(name = "counttype")
    private int countType;// 计算类型；0：计算公式 1：存储过程

    @JSONField(name = "indexcount")
    private String indexCount;// 计算公式或者存储过程名称

    @JSONField(name = "visibleflag")
    private int visibleFlag;// 是否显示；0:显示 1:隐藏

    @JSONField(name = "isthreshold")
    private int isThreshold;// 是否支持设置门限值；0：可以 1：不可以

    @JSONField(name = "datatypeshow")
    private int dataTypeShow; // 指标显示格式: // 指标显示格式: 0--整形 1--浮点型 2--百分型 3--布尔型


    @JSONField(name = "direction")
    private int direction;// 门限设置方向；0：向上

    @JSONField(name = "createtype")
    private int createType;// 创建类型;0:预定义 1：用户自定义

    @JSONField(name = "type")
    private int type;// 指标类型；0：KPI 1：PI 2：SPI

    /**
     * 带小数点型数据输出格式化样式定制；取值说明：当DATATYPESHOW选择为浮点型或百分数型时，用户可根据自己需要定制输出结果的精度，
     * 比如，输出结果为小数点后5位，那么设置为#,##0.00000(###0.00000); 需要精度满足小数点后6位，那么选择
     * #,##0.000000(###0.000000); 其余以此类推;最大支持小数点后15位。对百分数，格式如 #;##0.000%，不包含
     * %，最大支持小数点后13位。
     */
    @JSONField(name = "formatstr")
    private String formatStr = "";

    @JSONField(name = "alarmcode")
    private int alarmCode;// 告警码

    @JSONField(name = "alarmreasoncode")
    private String alarmReasonCode = "";// 告警原因码

    @JSONField(name = "thresholdvalues")
    private String[] thresholdValues;// 告警门限值和粘滞值数组

    @JSONField(name = "product")
    private String product;// 制式信息，为空时和计数器的制式相同

    @JSONField(name = "aggregatemethod")
    private short aggregateMethod;// 指标汇总方式：0-先时间后空间；1-先空间后时间，默认为0

    @JSONField(name = "operatorsname")
    private String operatorsName;// 运营商标识，后续根据跟字段加载KPI(暂时先不动)

}
