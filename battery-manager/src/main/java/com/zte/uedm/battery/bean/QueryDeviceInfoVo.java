package com.zte.uedm.battery.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@ToString
/**
 * 接收查询条件的Bean
 */
@ApiModel(description = "设备信息")
public class QueryDeviceInfoVo
{

    /**
     * 监控设备ID list
     */
    @ApiModelProperty(value = "监控设备标识")
    private List<String> deviceIds;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;


}
