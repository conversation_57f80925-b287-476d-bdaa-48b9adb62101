package com.zte.uedm.battery.service;

import com.zte.uedm.battery.bean.pv.PvCommonQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvPowerAnalysisResponseBean;
import com.zte.uedm.battery.bean.pv.PvTrendQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvTrendResponseBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

/**
 * @ Author     ：10260977
 * @ Date       ：10:08 2021/3/16
 * @ Description：太阳能发用电量分析
 * @ Modified By：
 * @ Version: 1.0
 */
public interface PvPowerAnalysisService
{
    /**
     * 太阳能用电量分析概览
     * @param pageNo
     * @param pageSize
     * @param languageOption
     * @return
     * @throws UedmException
     */
    List<PvPowerAnalysisResponseBean> pvPowerAnalysisOverview(PvCommonQueryRequestBean pvCommonQueryRequestBean, String userName,
            Integer pageNo, Integer pageSize, String languageOption) throws UedmException;

    /**
     * 太阳能用电量分析趋势
     * @param pageNo
     * @param pageSize
     * @param languageOption
     * @return
     * @throws UedmException
     */
    PvTrendResponseBean pvPowerAnalysisTrend(PvTrendQueryRequestBean pvTrendQueryRequestBean, String userName,
            Integer pageNo, Integer pageSize, String languageOption) throws UedmException;

}
