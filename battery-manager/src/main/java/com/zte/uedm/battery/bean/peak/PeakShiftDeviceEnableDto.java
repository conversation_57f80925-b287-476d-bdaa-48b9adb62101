/* Started by AICoder, pid:odf0fr451cid758143090be3307f15674442373a */
package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.common.consts.originalpoint.ZTEEMLOriginalPointConstat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Setter
@Getter
@ToString
@Slf4j
public class PeakShiftDeviceEnableDto {

    @ApiModelProperty(value = "设备id")
    @NotBlank(message = "id can not be blank")
    private String id; // 设备id

    @NotBlank(message = "name can not be blank")
    @ApiModelProperty(value = "设备名称")
    private String name; // 设备名称

    private Boolean enable; // 启用/停用; true：启用  false：停用

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 将启停boolean值转换成String
     *
     * @return
     */
    public String enable2String() {
        if (this.getEnable() && this.getDeviceType().equals(CommonConst.BCUA)) {
            return ZTEEMLOriginalPointConstat.PEAK_SHIFT_ENABLE_ENABLE_VALUE; // 启动
        }
        if (this.getEnable() && StringUtils.equalsAny(this.getDeviceType(), PeakDeviceTypeEnum.CSU5.id, PeakDeviceTypeEnum.CSU6.id)) {
            return ZTEEMLOriginalPointConstat.BATTERY_CHARGE_MODE_TIMING; // 启动
        } else {
            return ZTEEMLOriginalPointConstat.PEAK_SHIFT_DISABLE_VALUE; // 停止
        }
    }

    public boolean checkDeviceType() {
        List<String> allRanges = PeakDeviceTypeEnum.getAllRanges();
        if (StringUtils.isBlank(this.deviceType)) {
            return false;
        } else if (!allRanges.contains(this.deviceType)) {
            return false;
        } else {
            log.warn("PeakShiftDeviceEnableDto the deviceType is empty");
            return true;
        }
    }
}
/* Ended by AICoder, pid:odf0fr451cid758143090be3307f15674442373a */