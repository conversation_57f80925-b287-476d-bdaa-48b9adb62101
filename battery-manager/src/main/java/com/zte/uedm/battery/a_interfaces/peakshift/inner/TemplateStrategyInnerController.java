package com.zte.uedm.battery.a_interfaces.peakshift.inner;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.response.ResponseBeanUtils;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.battery.a_application.peakshift.executor.StrategyTemplateService;
import com.zte.uedm.battery.a_interfaces.peakshift.inner.dto.QueryFileIdRpcDto;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("device-template-strategy")
@Component
@Api(value = "设备模块策略inner")
@Slf4j
public class TemplateStrategyInnerController {

    @Autowired
    private StrategyTemplateService strategyTemplateService;

    private static final String SUCCESS = "success";

    @POST
    @Path("/query-file-id")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询fileId", notes = "查询fileId", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = -100, message = "参数为空或者参数错误")
    })
    public ResponseBean getFileId(QueryFileIdRpcDto dto) {
        /* Started by AICoder, pid:791c0116433a42ef937460c1a61afb59 */
        try {
            List<String> fileIds = strategyTemplateService.getFileId(dto);
            return ResponseBeanUtils.getNormalResponseBean(0, fileIds, 1);
        } catch (UedmException e) {
            log.error("TemplateStrategyController query occur exception, {}", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        /* Ended by AICoder, pid:791c0116433a42ef937460c1a61afb59 */
    }

}
