package com.zte.uedm.battery.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public interface PeakShiftTaskService
{
    List<PeakShiftTaskPo> checkTaskByname(String bean) throws UedmException;

    /**
     * 获取文件信息（RPC接口）
     * @param ids
     * @return
     * @throws UedmException
     */
    List<PeakShiftDeviceChildBeanVo> getFileInfo(List<String> ids) throws UedmException;

    /**
     * 获取可选设备状态（RPC接口）
     * @param ids
     * @return
     * @throws UedmException
     */
    Map<String, Boolean> getOptionalDeviceStatus(List<String> ids) throws UedmException;

    int setDeviceStatus(String detailId, String status) throws UedmException;

    PeakShiftDeviceChildBeanVo getFileIdByDeviceId(String deviceId, String status) throws UedmException;

    int updateInProgressDevice();

    PeakShiftDeviceTaskBo selectLatestTaskByDeviceId(String deviceId) throws UedmException;

    /**
     * 新增/编辑错峰策略任务时的重名校验
     * @param id 任务id
     * @param name 任务名称
     * @return
     * @throws UedmException
     */
    Boolean duplicateNameCheck(String id, String name) throws UedmException;

    /**
     * 任务状态翻转
     * @param id 任务id
     * @param status 目标状态
     * @return Pair<错误码, 错误描述>
     */
    Pair<Integer, String> doStatusFlip(String id, String status, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 查询各设备的最近一次策略任务信息
     * @return
     * @throws Exception
     */
    List<DeviceLatestPeakShiftTaskVo> getDeviceLatestStrategyTaskInfo() throws Exception;

    void manualRetry(PeakShiftTaskRetryBean retryBean, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    void delete(String id, ServiceBaseInfoBean serviceBean) throws UedmException;

    /**
     * 根据设备ID list获取 设备task信息
     * @param requestBean 查询条件
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    PageInfo<PeakShiftDeviceTaskBean> getDeviceTaskBeansByDeviceList(PeakShiftDeviceTaskVo requestBean,
            ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;

    /**
     * 根据设备ID 获取设备错峰参数
     * @param deviceId 查询条件
     * @param serviceBaseInfoBean
     * @return
     * @throws UedmException
     */
    TemplateStrategyDetailEntity queryCsuDeviceTransferParam(String deviceId, ServiceBaseInfoBean serviceBaseInfoBean) throws UedmException;
}
