package com.zte.uedm.battery.pv.bean;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/20
 **/
@Setter
@Getter
@ToString
@ApiModel(description = "pv当前计费方式为阶梯计费方式-当前周期模式下面发电量")
public class SolarRevenueTieredPvGenerationBean {
    /**
     * 太阳能id
     */
    private String pvId;

    /**
     * 周期内发电量求和
     */
    private String sumGeneration;
}
