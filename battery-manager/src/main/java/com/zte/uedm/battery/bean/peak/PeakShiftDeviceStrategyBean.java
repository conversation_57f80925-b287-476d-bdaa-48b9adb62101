/* Started by AICoder, pid:m3316t6430j4c97141180ba92080af489734a114 */
package com.zte.uedm.battery.bean.peak;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@ToString
public class PeakShiftDeviceStrategyBean {
    /**
     * 表id
     */
    private String id;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 时段策略id
     */
    private String intervalStrategyId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 策略开始时间
     */
    private String startDate;

    /**
     * 策略结束时间
     */
    private String endDate;
}
/* Ended by AICoder, pid:m3316t6430j4c97141180ba92080af489734a114 */