package com.zte.uedm.battery.a_interfaces.vpp.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.sql.Timestamp;

/* Started by AICoder, pid:pf83c6e5a1i5f931462d0a78806409112bc9f224 */
@Setter
@Getter
@ToString
public class FrequencyModulationMonitorDimVo {
    @ApiModelProperty("监控设备id")
    private String deviceId;

    @ApiModelProperty("调频开始时间")
    private Timestamp startTime;

    @ApiModelProperty("调频结束时间")
    private Timestamp endTime;

    @ApiModelProperty("设备响应时间")
    private String responseTime;

    @ApiModelProperty("创建数据时间")
    private String createTime;
}
/* Ended by AICoder, pid:pf83c6e5a1i5f931462d0a78806409112bc9f224 */
