package com.zte.uedm.battery.domain;

import com.zte.uedm.battery.bean.pojo.BattTestOverviewDimPo;
import com.zte.uedm.common.exception.UedmException;

import java.util.List;

public interface BattTestOverviewDimDomain {

    /**
     * 通过用户查询电池测试概览维度
     */
    List<BattTestOverviewDimPo> selectByUserName(String scene,String userName) throws UedmException;

    /**
     * 批量新增维度数据
     */
    Integer insertBattTestOverviewDims(List<BattTestOverviewDimPo> battTestOverviewDimPoList) throws UedmException;

    /**
     * 批量更新维度数据
     */
    Integer updateBattTestOverviewDims(List<BattTestOverviewDimPo> battTestOverviewDimPoList) throws UedmException;
}
