package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter
@Setter
@ToString
public class BatteryChargeDischargeHistoryDeliveryBean {

    /**
     * 采集器id
     */
    private String collectorId;

    /**
     * 标志id，业务侧唯一标识
     */
    private String logId;

    /**
     * 是否开启解析
     * 用于判断南向框架是否进行文件解析
     */
    private Boolean parseEnable;

    /**
     * 文件解析方式
     */
    private String parseFormat;

    /**
     * 下发时间
     * 格式：时间戳
     */
    private String deliverTime;

    /**
     * 功能参数
     * 字段timeOut Integer 超时时间，单位分钟, 非空，范围1~1440，超时响应获取失败
     */
    private Map<String, Object> functionParameter;
}
