package com.zte.uedm.battery.dao.impl;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.dao.BatteryBaseInfoDao;
import com.zte.uedm.battery.domain.impl.BatteryRemainDischargringDurationEvalDomainImpl;
import com.zte.uedm.battery.mapper.BatteryBaseInfoMapper;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeDatabaseUtil;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import com.zte.uedm.service.config.optional.GlobalOptional;

@Service
@Slf4j
public class BatteryBaseInfoDaoImpl implements BatteryBaseInfoDao {

    @Autowired
    private BatteryBaseInfoMapper batteryBaseInfoMapper;

    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Value("${overview.thread.db:5}")
    private Integer threadNum;

    /*缓存有效时间*/
    @Value("${overview.cache.valid-time:30000}")
    private Integer validTime;

    /*等待查询sql执行的最大时间*/
    @Value("${overview.cache.wait-time:180000}")
    private Integer waitTime;

    private ForkJoinPool forkJoinPool = null;

    @Autowired
    private CommonCacheService cacheService;

    private final static String BATTERY_SEARCH_NAME = "CACHE_NAME_BATTERY_SEARCH";
    private final static String BATTERY_SEARCH_ALL_KEY = "ALL_DATA";

    private final static ReentrantLock lock = new ReentrantLock();

    private final static AtomicLong cacheTime = new AtomicLong(0);


    @Override
    public List<BatteryBaseInfoBean> selectByCondition(String logicId) throws UedmException {
        if (StringUtils.isBlank(logicId) || GlobalOptional.GLOBAL_ROOT.equals(logicId)) {
            return getALLWithLock();
        }
        long time1 = System.currentTimeMillis();
        // 从缓存获取所有电池，按照pathid过滤
        List<String> batteryId = deviceCacheManager.getDevicesByMoc(MocOptional.BATTERY.getId()).parallelStream()
                .filter(entity -> null != entity.getPathId() && Arrays.asList(entity.getPathId()).contains(logicId))
                .map(DeviceEntity::getId)
                .collect(Collectors.toList());

        long time2 = System.currentTimeMillis();
        log.info("select battery filter from cache time:{} size:{}", time2 - time1, batteryId.size());
        List<BatteryBaseInfoBean> beans = Collections.synchronizedList(new ArrayList<>());
        // 开线程分批查询
        if (CollectionUtils.isNotEmpty(batteryId)) {
            int times = batteryId.size() / 1000 + (batteryId.size() % 1000 == 0 ? 0 : 1);
            CountDownLatch countDownLatch = new CountDownLatch(times);
            ForkJoinPool forkJoinPool = new ForkJoinPool(3);
            try {
                Lists.partition(batteryId, 1000).forEach(ids -> {
                    forkJoinPool.execute(() -> {
                        long time3 = System.currentTimeMillis();
                        List<BatteryBaseInfoBean> beanList = batteryBaseInfoMapper.selectByIdList(ids);
                        log.info("select battery by id consume time:{}", System.currentTimeMillis() - time3);
                        if (CollectionUtils.isNotEmpty(beanList)) {
                            beans.addAll(beanList);
                        }
                        countDownLatch.countDown();
                    });
                });
                countDownLatch.await(120, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("select battery error", e);
            }finally {
                forkJoinPool.shutdown();
            }
        }
        log.info("select battery search from db time:{}", System.currentTimeMillis() - time2);
        return filterMonitorObjectData(beans);
    }

    public List<BatteryBaseInfoBean> getALLWithLock() {
        List<BatteryBaseInfoBean> result=null;
        boolean isSearched = false;
        long cacheTimestamp = cacheTime.get();
        long start = System.currentTimeMillis();
        log.info("getALLWithLock, cache timestamp:{}", cacheTimestamp);
        //如果缓存过期，需要从数据库查询，并更新缓存
        if (start - cacheTimestamp > validTime) {
            try {
                // 如果锁没有释放，不去争抢锁，尝试从缓存获取数据
                if (lock.isLocked()) {waitCache(waitTime);}
                else if (lock.tryLock(waitTime, TimeUnit.SECONDS)) {
                    log.info("getALLWithLock, get lock success");
                    // 查询较为耗时，需要双重判断
                    if (System.currentTimeMillis() - cacheTime.get() > validTime) {
                        log.info("getALLWithLock, start search from db");
                        // 从数据库查询
                        result = getALL();
                        // 更新缓存和时间戳
                        cacheService.put(BATTERY_SEARCH_NAME, BATTERY_SEARCH_ALL_KEY, result);
                        cacheTime.set(System.currentTimeMillis());
                        isSearched = true;
                    }
                }
            } catch (Exception e) {
                log.error("getALLWithLock, get lock error", e);
            }finally {
                //释放持有的锁
                try {
                    if (lock.isHeldByCurrentThread()) lock.unlock();
                } catch (Exception e) {log.error("getALLWithLock, unlock error",e);}
                log.info("getALLWithLock, unlock end");
            }
        }
        // 从缓存获取数据，处理null（CI不通过拆分的方法）
        result = dealResult(isSearched,result);
        log.info("getALLWithLock, consume time:{},size:{}", System.currentTimeMillis() - start, result.size());
        return result;
    }

    private List<BatteryBaseInfoBean> dealResult(boolean isSearched, List<BatteryBaseInfoBean> result) {
        if (!isSearched) {
            log.info("getALLWithLock, search from cache");
            result = cacheService.getCache(BATTERY_SEARCH_NAME, BATTERY_SEARCH_ALL_KEY, List.class);
        }
        return result == null ? new ArrayList<>() : result;
    }

    public void waitCache(int validTime) throws InterruptedException {
        long startWaitTime = System.currentTimeMillis();
        log.info("getALLWithLock, waiting...");
        int waitTime = validTime;
        while (waitTime > 0) {
            if (System.currentTimeMillis() - cacheTime.get() <= validTime) {
                break;
            }
            int sleepTime = 100;
            Thread.sleep(sleepTime);
            waitTime = waitTime - sleepTime;
        }
        log.info("getALLWithLock, end wait, time:{}", System.currentTimeMillis() - startWaitTime);
    }

    private List<BatteryBaseInfoBean> getALL() throws UedmException {
        List<BatteryBaseInfoBean> batteryBaseInfoBeans = Collections.synchronizedList(new ArrayList<>());

        try {
            long time0 = System.currentTimeMillis();
            int allNum = batteryBaseInfoMapper.getAllNum();
            final CountDownLatch latch = new CountDownLatch(allNum / 10000 + (allNum % 10000 == 0 ? 0 : 1));
            for (int i = 0; i < allNum; i += 10000) {
                int offset = i;
                getForkJoinPool().execute(() -> {
                    long time1 = System.currentTimeMillis();
                    List<BatteryBaseInfoBean> beans = batteryBaseInfoMapper.selectByLogicId(null, 10000, offset);
                    log.info("select battery consume time:{}", System.currentTimeMillis() - time1);
                    if (beans!=null && !beans.isEmpty()) {
                        batteryBaseInfoBeans.addAll(beans);
                    }
                    latch.countDown();
                });
            }
            latch.await(120, TimeUnit.SECONDS);
            log.info("select count consume time :{} result:{}", System.currentTimeMillis() - time0,batteryBaseInfoBeans.size());
        } catch (Exception e) {
            log.error("selectByCondition -> Database operation query failed", e);
            throw UedmErrorCodeDatabaseUtil.databaseSelectFailed("selectByCondition -> Database operation query failed");
        }
        //剔除缓存中不存在的电池，并赋值name、path_id、path_name
        return filterMonitorObjectData(batteryBaseInfoBeans);
    }

    /**
     * 剔除缓存中没有的电池，为电池的name、pathId、pathName赋值
     * @param beans
     * @throws UedmException
     */

    /* Started by AICoder, pid:ubbd0de23d96379146fb0a4eb0450545136243ab */
    public List<BatteryBaseInfoBean> filterMonitorObjectData(List<BatteryBaseInfoBean> beans) throws UedmException {
        if (CollectionUtils.isEmpty(beans)) {
            return beans;
        }


        long time1 = System.currentTimeMillis();
        List<String> beanIdList = beans.stream()
                .map(BatteryBaseInfoBean::getId)
                .collect(Collectors.toList());

        List<DeviceEntity> batteryDevices = deviceCacheManager.getDeviceByIdsAndMoc(beanIdList, MocOptional.BATTERY.getId());

        long time2 = System.currentTimeMillis();
        log.info("battery monitor data time1:{}", time2 - time1);

        if (CollectionUtils.isEmpty(batteryDevices)) {
            log.warn("filterMonitorObjectData: data in cache is empty");
            return new ArrayList<>();
        }

        Map<String, DeviceEntity> deviceEntityMap = batteryDevices.stream()
                .collect(Collectors.toMap(DeviceEntity::getId,
                        bean -> bean,
                        (oldValue, newValue) -> oldValue));

        List<BatteryBaseInfoBean> result = Collections.synchronizedList(new ArrayList<>(beans.size()));
        // 剔除缓存不存在的电池
        beans.parallelStream().forEach(bean -> {
            DeviceEntity entity = deviceEntityMap.get(bean.getId());
            if (entity != null) {
                bean.setName(entity.getName());
                bean.setPathId(entity.toStringPathId());
                bean.setPathName(entity.getPathName());
                result.add(bean);
            }
        });

        log.info("battery monitor data time2:{} before size:{} after size:{}", System.currentTimeMillis() - time2,beans.size(),result.size());

        return result;
    }
    /* Ended by AICoder, pid:ubbd0de23d96379146fb0a4eb0450545136243ab */


    private ForkJoinPool getForkJoinPool() {
        if (forkJoinPool == null) {
            forkJoinPool = new ForkJoinPool(threadNum);
        }
        return forkJoinPool;
    }

}
