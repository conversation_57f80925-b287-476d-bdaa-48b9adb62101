package com.zte.uedm.battery.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.zte.uedm.battery.bean.BattBackupPowerEvalPojo;
import com.zte.uedm.battery.mapper.BattBackupPowerEvalMapper;
import com.zte.uedm.service.config.optional.MocOptional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.BattStandbyPowerRcdHisBean;
import com.zte.uedm.battery.mapper.BattStandbyPowerRcdHisMapper;
import com.zte.uedm.battery.service.BattStandbyPowerRcdHisService;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;

import lombok.extern.slf4j.Slf4j;

/**
 * 电池备电配置历史记录mapper
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = UedmException.class)
public class BattStandbyPowerRcdHisServiceImpl implements BattStandbyPowerRcdHisService
{

    @Autowired
    private BattStandbyPowerRcdHisMapper battStandbyPowerRcdHisMapper;

    @Autowired
    private ConfigurationService configurationServiceImpl;

    @Autowired
    private BattBackupPowerEvalMapper battBackupPowerEvalMapper;

    @Override
    public void insert(List<BattStandbyPowerRcdHisBean> beanList) throws UedmException
    {
        battStandbyPowerRcdHisMapper.insert(beanList);
    }

    @Override
    public Integer update(BattStandbyPowerRcdHisBean bean) throws UedmException
    {
        try
        {
            int num = battStandbyPowerRcdHisMapper.update(bean);
            return num;
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB
                    .getDesc());
        }

    }

    @Override
    public List<BattStandbyPowerRcdHisBean> select(BattStandbyPowerRcdHisBean bean) throws UedmException
    {
        try
        {
            return battStandbyPowerRcdHisMapper.select(bean);
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB
                    .getDesc());
        }
    }

    @Override
    public BattStandbyPowerRcdHisBean selectLastestRcdByTime(String id, String time) throws UedmException, Exception
    {
        BattStandbyPowerRcdHisBean result = null;

        // 查询业务对象下的开关电源
        List<String> logicIds = new ArrayList<String>();
        logicIds.add(id);
        List<MoObjectConfiguration> spList = configurationServiceImpl.getMonitorObjectList(logicIds,
                MocOptional.DC_POWER.getId());

        // 查询各个开关电源在某个时刻之前最新的备电时长，并选取时长最短的数据返回
        for (MoObjectConfiguration sp : spList)
        {
            BattStandbyPowerRcdHisBean dto = new BattStandbyPowerRcdHisBean();
            dto.setMoOid(sp.getId());
            dto.setRaiseTime(time);
//            BattStandbyPowerRcdHisBean resultTemp = battStandbyPowerRcdHisMapper.selectLastestRcdByTime(dto);
            BattBackupPowerEvalPojo resultTemp;
            try
            {
                resultTemp = battBackupPowerEvalMapper.selectLastestRcdByTime(dto);
            }
            catch (Exception e)
            {
                log.error("BattStandbyPowerRcdHisServiceImpl selectLastestRcdByTime  select DB is error:{}",e.getMessage());
                throw new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc());
            }
            if (null != resultTemp)
            {
                BattStandbyPowerRcdHisBean temp = new BattStandbyPowerRcdHisBean();
                temp.setRaiseTime(resultTemp.getEvalTime());
                if (null!=resultTemp.getBackupPowerDuration())
                {
                    temp.setStandbyPowerDuration(String.valueOf(resultTemp.getBackupPowerDuration()));
                }
                temp.setMoOid(resultTemp.getId());
                temp.setEvaluateResult(resultTemp.getStatus());

                // 如果一个存在多个开关电源，由于开关电源一般会同时放电，那么这里取最小的值
                if (null == result || null == result.getStandbyPowerDuration())
                {
                    result = temp;
                }
                else if (null != resultTemp.getBackupPowerDuration())
                {
                    result = Double.parseDouble(result.getStandbyPowerDuration()) <= (Double.parseDouble(String.valueOf(resultTemp
                            .getBackupPowerDuration()))) ? result : temp;
                }
            }
        }
        return result;
    }

}
