package com.zte.uedm.battery.a_domain.safe.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.battery.a_domain.safe.OperationLogService;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryLogRequestBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryTheftLogViewBean;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.OperationLogMapper;
import com.zte.uedm.battery.a_interfaces.safe.web.vo.BatteryUnlockRecordVo;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.LogTypeEnum;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.bean.log.OperationLogQueryBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @FileDesc :
 * <AUTHOR> 6774001720
 * @date Date : 2024年07月26日 10:56
 * @Version : 1.0
 */
@Service
@Slf4j
public class OperationLogServiceImpl implements OperationLogService
{
    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private DateTimeService timeService;
    @Autowired
    private JsonService jsonService;

    //执行状态
    private static final Integer PROCEED_STATUS = 1;
    private static final Integer OTHER_STATUS = 2;

    @Override
    public PageInfo<OperationLogBean> selectBatteryArmLog(BatteryLogRequestBean requestBean)
    {
        PageHelper.startPage(requestBean.getPageNo(), requestBean.getPageSize());
        OperationLogQueryBean operationLogQueryBean = buildQueryDto(requestBean);
        operationLogQueryBean.setType(LogTypeEnum.BatteryArm.name());
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [selectBatteryArmLog] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        return new PageInfo<>(operationLogBeans);
    }

    @Override
    public PageInfo<BatteryTheftLogViewBean> selectBatteryTheftLog(BatteryLogRequestBean requestBean)
    {
        PageHelper.startPage(requestBean.getPageNo(), requestBean.getPageSize());
        OperationLogQueryBean operationLogQueryBean = buildQueryDto(requestBean);
        operationLogQueryBean.setType(LogTypeEnum.BatteryTheft.name());
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [selectBatteryTheftLog] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        // 通过初始的pageInfo获取到总数据条数，然后转换成将要返回的分页对象
        PageInfo<OperationLogBean> pageInfo = new PageInfo<>(operationLogBeans);
        PageInfo<BatteryTheftLogViewBean> resultPage = convertPageInfo(pageInfo);
        List<BatteryTheftLogViewBean> resultList = new ArrayList<>();
        resultPage.setList(resultList);
        for (OperationLogBean operationLogBean : operationLogBeans)
        {
            try
            {
                String detail = operationLogBean.getDetail();
                BatteryTheftLogViewBean batteryTheftLogViewBean = jsonService.jsonToObject(detail, BatteryTheftLogViewBean.class);
                batteryTheftLogViewBean.setUpdateTime(operationLogBean.getUpdateTime());
                resultList.add(batteryTheftLogViewBean);
            }
            catch (UedmException e)
            {
                log.error("selectBatteryTheftLog error.",e);
            }
        }
        return resultPage;
    }

    private static <T, R> PageInfo<R> convertPageInfo(PageInfo<T> source) {
        PageInfo<R> target = new PageInfo<>();
        target.setPageNum(source.getPageNum());
        target.setPageSize(source.getPageSize());
        target.setPages(source.getPages());
        target.setTotal(source.getTotal());
        return target;
    }

    @Override
    public PageInfo<BatteryUnlockRecordVo> selectUnlockRecord(BatteryLogRequestBean batteryLogRequestBean, ServiceBaseInfoBean serviceBaseInfoBean)
    {
        PageHelper.startPage(batteryLogRequestBean.getPageNo(), batteryLogRequestBean.getPageSize());
        OperationLogQueryBean operationLogQueryBean = buildQueryDto(batteryLogRequestBean);
        operationLogQueryBean.setType(LogTypeEnum.BatteryUnLock.getId());
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        }  catch (Exception e) {
            log.error("OperationLogServiceImpl [selectUnlockRecord] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        operationLogBeans.stream().sorted(Comparator.comparing(OperationLogBean::getUpdateTime).reversed()).collect(Collectors.toList());
        // 通过初始的pageInfo获取到总数据条数，然后转换成将要返回的分页对象
        PageInfo<OperationLogBean> pageInfo = new PageInfo<>(operationLogBeans);
        PageInfo<BatteryUnlockRecordVo> resultPage = convertPageInfo(pageInfo);
        List<BatteryUnlockRecordVo> resultList = new ArrayList<>();
        resultPage.setList(resultList);
        String unlockName = I18nUtils.getMapFieldByLanguageOption(LogTypeEnum.BatteryUnLock.getName(), serviceBaseInfoBean.getLanguageOption());
        for (OperationLogBean operationLogBean : operationLogBeans)
        {
            BatteryUnlockRecordVo batteryUnlockRecordVo = new BatteryUnlockRecordVo();
            batteryUnlockRecordVo.setOperationDate(operationLogBean.getUpdateTime());
            batteryUnlockRecordVo.setOperator(operationLogBean.getOperator());
            batteryUnlockRecordVo.setOperType(unlockName);
            batteryUnlockRecordVo.setStatus(operationLogBean.getStatus());
            batteryUnlockRecordVo.setDetail(operationLogBean.getDetail());
            resultList.add(batteryUnlockRecordVo);
        }
        return resultPage;
    }

    @Override
    public String addLog(OperationLogBean logBean) {
        String id = logBean.getId();
        if (id == null)
        {
            id = UUID.randomUUID().toString();
            logBean.setId(id);
        }
        String currentTime = timeService.getCurrentTime();
        if (logBean.getCreateTime()==null)
        {
            logBean.setCreateTime(currentTime);
        }
        if (logBean.getUpdateTime()==null)
        {
            logBean.setUpdateTime(currentTime);
        }
        try {
            operationLogMapper.addOperationLog(logBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [addLog] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        return id;
    }

    private OperationLogQueryBean buildQueryDto(BatteryLogRequestBean requestBean)
    {
        OperationLogQueryBean bean = new OperationLogQueryBean();
        bean.setResourceId(requestBean.getId());
        bean.setBeginTime(requestBean.getBeginTime());
        bean.setEndTime(requestBean.getEndTime());
        return bean;
    }

    /* Started by AICoder, pid:rfdf514b69mdc7814a190af1d0ef080f9ba57c8e */
    @Override
    public List<OperationLogBean> selectByCondition(OperationLogQueryBean operationLogQueryBean) {
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [selectByCondition] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        return operationLogBeans;
    }

    /* Ended by AICoder, pid:rfdf514b69mdc7814a190af1d0ef080f9ba57c8e */

    /* Started by AICoder, pid:abe3d8c428l13f81463c0acad06729143d04d99f */
    @Override
    public void updateLog(OperationLogBean logBean) {
        if (StringUtils.isBlank(logBean.getId())) {
            throw new IllegalArgumentException("log id can not be null");
        }
        if (logBean.getStatus() == null) {
            throw new IllegalArgumentException("log status can not be null");
        }
        String updateTime = logBean.getUpdateTime();
        if (updateTime == null) {
            logBean.setUpdateTime(timeService.getCurrentTime());
        }
        try {
            operationLogMapper.updateOperationLog(logBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [updateLog] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
    }

    /* Ended by AICoder, pid:abe3d8c428l13f81463c0acad06729143d04d99f */

    public Integer getUnlockStatus(String moId,String type){
        OperationLogQueryBean operationLogQueryBean = new OperationLogQueryBean();
        operationLogQueryBean.setResourceId(moId);
        operationLogQueryBean.setType(type);
        List<OperationLogBean> operationLogBeans = null;
        try {
            operationLogBeans = operationLogMapper.selectByCondition(operationLogQueryBean);
        } catch (Exception e) {
            log.error("OperationLogServiceImpl [getUnlockStatus] occur error message = {}", e.getMessage(), e);
            throw new RuntimeException(ErrorCodeOptional.DATABASE_OPERATION_ERROR.getI18nDesc());
        }
        List<Integer> statusList = operationLogBeans.stream().map(OperationLogBean::getStatus).collect(Collectors.toList());
        //1为执行中，若包含则直接返回1
        if(!CollectionUtils.isEmpty(statusList) && statusList.contains(1)){
            return 1;
        }
        return 2;
    }
}
