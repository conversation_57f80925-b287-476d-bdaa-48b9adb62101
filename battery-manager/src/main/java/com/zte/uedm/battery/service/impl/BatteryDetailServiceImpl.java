package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.obj.ValueOptionsOptional;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.StandardPointCacheManager;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.controller.batthealth.vo.IdNameBeanStatusDetailVo;
import com.zte.uedm.battery.controller.batthealth.vo.IdNameBeanStatusVo;
import com.zte.uedm.battery.controller.batthealth.vo.SohDetailObjValue;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.impl.BatteryBackupPowerEvalDomainImpl;
import com.zte.uedm.battery.enums.BattSourceTypeEnum;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.BatteryDetailService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_infrastructure.common.StandPointConstants.*;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2022年05月11日 下午3:10
 * @Version : 1.0
 */
@Component
@Slf4j
public class BatteryDetailServiceImpl implements BatteryDetailService
{
    @Autowired
    private DataRedis dataRedis;
    @Autowired
    private BlankService blankService;

    @Autowired
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private I18nUtils i18nUtils;

    @Autowired(required = true)
    private BattCfgInfoDomain battCfgInfoDomain;
    @Autowired
    private AssetRpcImpl assetRpcImpl;
    @Autowired
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;
    @Autowired
    private BattSohDomain battSohDomain;
    @Autowired
    private BattLifeEvalDomain battLifeEvalDomain;

    @Autowired
    private BattLifeDomain battLifeDomain;

    @Autowired
    private BatteryBackupPowerEvalDomainImpl batteryBackupPowerEvalDomain;

    @Autowired
    private DeviceCacheManager deviceCacheManager;

    @Autowired
    private StandardPointCacheManager standardPointCacheManager;

    @Override
    public BatteryDetailOverviewBean getBatteryDetailOverview(String id, String languageOption, HttpServletRequest request) throws
            UedmException, com.zte.uedm.basis.exception.UedmException {
        Map<String, Map<String, String>> map = dataRedis.selectRealData(id);
        BatteryDetailOverviewBean batteryDetailOverviewBean = new BatteryDetailOverviewBean();

        Object device = deviceCacheManager.selectByKey(id);
        if(null != device)
        {
            DeviceEntity deviceEntity = (DeviceEntity) device;
            batteryDetailOverviewBean.setId(id);
            batteryDetailOverviewBean.setName(deviceEntity.getName());

            List<BattHealthStatusBean> healthLevels = battSohDomain.selectAllBattHealthLevels();
            Map<String, String> healthIdNameMap = healthLevels.stream().filter(bean->!StringUtils.isAnyBlank(bean.getId(),bean.getName())).collect(Collectors.toMap(BattHealthStatusBean::getId, BattHealthStatusBean::getName,(k1, k2)->k2));
            List<BattHealthStatusEvalPo> healths = battHealthStatusEvalDomain.selectLastByMoIds(Arrays.asList(id));
            if(CollectionUtils.isNotEmpty(healths))
            {
                String healthInknownId = battSohDomain.selectUnknownLevelId();
                String statusId = Optional.ofNullable(healths.get(0)).map(BattHealthStatusEvalPo::getStatusId).orElse(healthInknownId);
                String healthName = i18nUtils.getMapFieldByLanguageOption(healthIdNameMap.get(statusId), languageOption);
                IdNameBeanStatusVo idNameBeanStatusVo = new IdNameBeanStatusVo();
                idNameBeanStatusVo.setId(statusId);
                idNameBeanStatusVo.setName(healthName);
                BattHealthStatusEvalPo battHealthStatusEvalPo = Optional.ofNullable(healths.get(0)).get();
                //健康度从评估表中获取
                batteryDetailOverviewBean.setHealth(battHealthStatusEvalPo.getSoh());
                String evalReason = battHealthStatusEvalPo.getEvalReason();
                IdNameBeanStatusDetailVo sohDetailShowVo = new IdNameBeanStatusDetailVo();
                if (StringUtils.isNotBlank(evalReason) && evalReason.startsWith("{")) {
                    SohDetailObjValue sohDetailObjValue = JSON.parseObject(evalReason, SohDetailObjValue.class);
                    sohDetailShowVo.setRule(i18nUtils.getMapFieldByLanguageOption(sohDetailObjValue.getEvalRule(),languageOption));
                    if (CollectionUtils.isNotEmpty(sohDetailObjValue.getEvalSource())) {
                        List<String> source = sohDetailObjValue.getEvalSource().stream().filter(StringUtils::isNotBlank).map(item ->
                                i18nUtils.getMapFieldByLanguageOption(item, languageOption)
                        ).collect(Collectors.toList());
                        sohDetailShowVo.setIndicatorInfo(source);
                    }
                    //添加sohResoucre
                    sohDetailShowVo.setAlarm(sohDetailObjValue.getAlarm()==null?false:sohDetailObjValue.getAlarm());
                    buildSohResource(languageOption, batteryDetailOverviewBean, battHealthStatusEvalPo, sohDetailShowVo);
                }
                idNameBeanStatusVo.setDetail(sohDetailShowVo);
                batteryDetailOverviewBean.setSoh(idNameBeanStatusVo);

            }
            // 电池详情页面的剩余时长计算
            Map<String,String> dischargeDurationMap = batteryBackupPowerEvalDomain.getBatterySurplusDischargeDuration("DetailOverview:"+id,Collections.singletonList(id));
            if (!blankService.isBlank(map)) {

                //电池累计放电次数
                batteryDetailOverviewBean.setDischargeTimes(toInt(getSmpValue(map,
                        BATTERY_SMPID_ACCUM_DISCHARGE_TIMES)));
                //电池累计充电次数
                batteryDetailOverviewBean.setChargeTimes(toInt(getSmpValue(map,
                        BATTERY_SMPID_ACCUM_CHARGE_TIMES)));
                //电池SOC
                batteryDetailOverviewBean.setSoc(toDouble(getSmpValue(map,
                                BATTERY_SMPID_PRST_SOC),
                        standardPointCacheManager.getStandardByIdMoc(BATTERY_SMPID_PRST_SOC, MocOptional.BATTERY.getId())));
                //电池充放电状态
                batteryDetailOverviewBean.setCharge(setDIValue(getSmpValue(map,
                                BATTERY_SMPID_CHARGE_DISCHARGE_STATUS),
                        standardPointCacheManager.getStandardByIdMoc(BATTERY_SMPID_CHARGE_DISCHARGE_STATUS, MocOptional.BATTERY.getId()), languageOption));
            }
            Map<String, BattLifeEvalDBean> lifeMap = battLifeEvalDomain.getBattLifeEvalDayMap(Collections.singletonList(id));

            //查询等级表(获取寿命等级id，寿命名称name，寿命等级阈值threshold)
            List<BattLifeLevelsPojo> battLifeLevelsPojos = battLifeDomain.getAlls();
            log.info("getBatteryDetailOverview -> battLifeLevelsPojos.size:{}", battLifeLevelsPojos.size());

            Map<String, String> battLifeIdName = battLifeLevelsPojos.stream()
                    .filter(bean->StringUtils.isNotBlank(bean.getId())).filter(bean->StringUtils.isNotBlank(bean.getName()))
                    .collect(Collectors.toMap(BattLifeLevelsPojo::getId, BattLifeLevelsPojo::getName, (key1, key2) -> key2));

            //将数据中的阈值转为对象
            List<BattLifeLevelsBo> battLifeLevelsBos = battLifeLevelsPojos.stream().map(item->{
                BattLifeLevelsBo bo = new BattLifeLevelsBo();
                bo.setId(item.getId());
                try {
                    if(StringUtils.isNotBlank(item.getThreshold())) {
                        bo.setThreshold(jsonService.jsonToObject(item.getThreshold(), BattLifeThresholdBean.class));
                    }
                } catch (Exception e) {
                    log.error("threshold json -> threshold bean is error.", e);
                }
                return bo;
            }).collect(Collectors.toList());

            BattLifeEvalDBean battLifeEvalDBean = lifeMap.get(id);
            if(battLifeEvalDBean != null) {
                Integer lifeInt = battLifeEvalDBean.getLife();
                String lifeIdByLife = getLifeIdByLife(battLifeLevelsBos, lifeInt);
                String life = battLifeIdName.get(lifeIdByLife);
                life = i18nUtils.getMapFieldByLanguageOption(life, languageOption);
                log.info("getBatteryDetailOverview -> battLifeEvalDBean != null after I18 life:{}", life);
                batteryDetailOverviewBean.setLife(life);
                batteryDetailOverviewBean.setLifeSource(battLifeEvalDBean.getSource());
            }
            // 剩余放电时长
            batteryDetailOverviewBean.setRemainDischargeDuration(dischargeDurationMap.get(id));
        }
        return batteryDetailOverviewBean;
    }

    public void buildSohResource(String languageOption, BatteryDetailOverviewBean batteryDetailOverviewBean, BattHealthStatusEvalPo battHealthStatusEvalPo, IdNameBeanStatusDetailVo sohDetailShowVo) {
        String sohSource = battHealthStatusEvalPo.getSource();
        if(StringUtils.isNotBlank(sohSource)){
            //告警判断
            sohDetailShowVo.setSource(!sohDetailShowVo.getAlarm()?i18nUtils.getMapFieldByLanguageOption(BattSourceTypeEnum.getNameByCode(sohSource), languageOption):null);
            batteryDetailOverviewBean.setSohSource(sohSource);
        }
    }

    public String getLifeIdByLife(List<BattLifeLevelsBo> battLifeLevelsBos, Integer life)
    {
        String lifeId = "";
        for(BattLifeLevelsBo bo : battLifeLevelsBos)
        {
            if(bo.inThreshold(life))
            {
                lifeId = bo.getId();
                break;
            }
        }
        return lifeId;
    }

    private Double toDouble(String smpValue, StandardPointEntity standardPoint)
    {
        if(!StringUtils.isEmpty(smpValue) && null != standardPoint)
        {
            Integer precision = standardPoint.getPrecision();
            BigDecimal bigDecimal = new BigDecimal(smpValue);
            smpValue = bigDecimal.setScale(precision, BigDecimal.ROUND_HALF_UP).toString();
            return Double.parseDouble(smpValue);
        }
        else
        {
            return null;
        }
    }

    private Integer toInt(String smpValue)
    {
        if(!StringUtils.isEmpty(smpValue))
        {
            return (int)Double.parseDouble(smpValue);
        }
        else
        {
            return null;
        }
    }

    @Override
    public BatteryAssetBean getBatteryAssetInfo(String id, String languageOption, HttpServletRequest request)
            throws UedmException, com.zte.uedm.basis.exception.UedmException {
        Object device = deviceCacheManager.selectByKey(id);

        BatteryAssetBean bean = assetRpcImpl.getAssetByMoId(id, languageOption);
        if(null != device && null != bean)
        {
            DeviceEntity deviceEntity = (DeviceEntity) device;
            /* Started by AICoder, pid:k6b7617ebb3f9cb14a210b3c80dc690c8231df44 */
            String startDate = bean.getStartDate() == null ? null : (bean.getStartDate().length() > 10 ? bean.getStartDate().substring(0, 10) : bean.getStartDate());
            /* Ended by AICoder, pid:k6b7617ebb3f9cb14a210b3c80dc690c8231df44 */
            log.info("----getBatteryAssetInfo-----startDate {} ",startDate);
            bean.setStartDate(startDate);
            bean.setName(deviceEntity.getName());
        }

        return bean;
    }


//    /**
//     * 查询电池寿命
//     * @param id
//     * @return
//     * @throws UedmException
//     */
//    private String getBattLife(String id) throws UedmException
//    {
//        List<BattCfgInfo> battCfgInfoList = battCfgInfoDomain.findBattCfgInfoByMoId(id);
//        Date birthday = getBirthday(battCfgInfoList);
//        if (birthday == null)
//        {
//            return null;
//        }
//        else
//        {
//            int monthDiff = getMonthDiff(new Date(), birthday);
//            if (monthDiff > 0)
//            {
//                return String.valueOf(monthDiff);
//            }
//            else
//            {
//                return "0";
//            }
//        }
//    }

//    private Date getBirthday(List<BattCfgInfo> battCfgInfoList)
//    {
//        Date birthday = null;
//        if (battCfgInfoList != null && !battCfgInfoList.isEmpty())
//        {
//            birthday = battCfgInfoList.get(0).getBirthday();
//        }
//        return birthday;
//    }

//    /**
//     * 获取两个日期相差的月数
//     */
//    private int getMonthDiff(Date d1, Date d2)
//    {
//        Calendar c1 = Calendar.getInstance();
//        Calendar c2 = Calendar.getInstance();
//        c1.setTime(d1);
//        c2.setTime(d2);
//        int year1 = c1.get(Calendar.YEAR);
//        int year2 = c2.get(Calendar.YEAR);
//        int month1 = c1.get(Calendar.MONTH);
//        int month2 = c2.get(Calendar.MONTH);
//        int day1 = c1.get(Calendar.DAY_OF_MONTH);
//        int day2 = c2.get(Calendar.DAY_OF_MONTH);
//        // 获取年的差值
//        int yearInterval = year1 - year2;
//        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
//        if (month1 < month2 || month1 == month2 && day1 < day2)
//        {
//            yearInterval--;
//        }
//        // 获取月数差值
//        int monthInterval = (month1 + 12) - month2;
//        if (day1 < day2)
//        {
//            monthInterval--;
//        }
//        monthInterval %= 12;
//        int monthsDiff = yearInterval * 12 + monthInterval;
//        return monthsDiff;
//    }

    /* Started by AICoder, pid:k82bc3cfe6tc3f314633098ed0b196233359e960 */
    private String setDIValue(String smpValue, StandardPointEntity standardPoint, String languageOption) throws UedmException {
        if (!StringUtils.isEmpty(smpValue) && null != standardPoint && CollectionUtils.isNotEmpty(standardPoint.getValueOptions())) {
            Double valueNumber = Double.parseDouble(smpValue);
            String id = String.valueOf(valueNumber.intValue());

            List<ValueOptionsOptional> valueList = standardPoint.getValueOptions().stream()
                    .filter(valueOptionsOptional -> valueOptionsOptional.getId().equals(id))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(valueList)) {
                ValueOptionsOptional valueOptionsOptional = valueList.get(0);
                smpValue = i18nUtils.getMapFieldByLanguageOption(valueOptionsOptional.getName(), languageOption);
            }
        }

        return smpValue;
    }
    /* Ended by AICoder, pid:k82bc3cfe6tc3f314633098ed0b196233359e960 */

    private String getSmpValue(Map<String, Map<String, String>> map, String key)
    {
        String keyValue = "";
        if(map.containsKey(key))
        {
            Map<String, String> pointMap = map.get(key);
            keyValue = pointMap.containsKey("value") ? pointMap.get("value") : "";
        }
        return keyValue;
    }
}
