package com.zte.uedm.battery.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
public class BattBackupPowerEvalPojo
{
    /**
     *备电系统id
     */
    String id;
    /**
     * 备电系统名称
     */
    String name;
    /**
     *评估时间
     */
    String evalTime;

    /**
     * 状态变更时间
     */
    String statusChangeTime;

    /**
     *系统类型
     */
    String type;
    /**
     *位置路径id串
     */
    String pathIds;
    /**
     *位置路径名称串
     */
    String pathNames;
    /**
     *状态
     */
    String status;
    /**
     *上次状态
     */
    String preStatus;
    /**
     *状态详情
     */
    String statusDetail;
    /**
     *上次状态详情
     */
    String preStatusDetail;
    /**
     *备电时长
     */
    Double backupPowerDuration;

    /**
     *备电时长(混用展示)
     */
    String backupPowerDurationLiAndLead;
    /**
     *剩余放电时长
     */
    String surplusDischargeDuration;
    /**
     *备电阈值
     */
    Double thresholdDuration;
    /**
     *使用场景
     */
    String applicationScene;

    /**
     *制造商
     */
    String manufacture;
    /**
     *品牌
     */
    String brand;
    /**
     *系列
     */
    String series;
    /**
     *型号
     */
    String model;

    /**
     *制造商id
     */
    String manufactureId;
    /**
     *品牌id
     */
    String brandId;
    /**
     *系列id
     */
    String seriesId;
    /**
     *型号id
     */
    String modelId;

    /**
     *创建者
     */
    String creator;
    /**
     * 创建时间
     */
    Date gmtCreate;
    /**
     *更新者
     */
    String updater;
    /**
     *更新时间
     */
    Date gmtModified;

    /**
     * 额定容量
     */
    String ratedCapacity;
    /**
     * 满充容量
     */
    String fullChargeCapacity;
    /**
     * 平均放电电流
     */
    String averageDischargeCurrent;
    /**
     * 负载总功率
     */
    String totalLoadPower;
    /**
     * 交流停电告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date acStopAlarmTime;
    /**
     * 下电告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date powerOffAlarmTime;
    /**
     * 站点等级
     */
    private String siteLevel;
    /**
     * 供电场景
     */
    private String powerSupplyScene;

}
