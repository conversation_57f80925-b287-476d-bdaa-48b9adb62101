package com.zte.uedm.battery.a_domain.service.peakshift.impl;

import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.zte.oes.dexcloud.ftpclient.service.FtpClientService;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.ImportFileResponseEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.TemplateStrategyDetailEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.UpDownloadFileEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.UpDownloadFileParameterEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.PeakShiftDeviceFileRepository;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.UpDownloadFileRepository;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftTemplateFileService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftDeviceFileUploadPO;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.UpDownloadFileDataBasePO;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftFileDeviceDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.*;
import com.zte.uedm.battery.bean.IntervalDetailBean;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.enums.peak.PeakShiftDeviceStatusEnum;
import com.zte.uedm.battery.service.PeakShiftTaskService;
import com.zte.uedm.common.consts.RedisConstants;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.redis.service.RedisService;
import com.zte.ums.zenap.protocol.ftpclient.api.FtpClient;
import com.zte.ums.zenap.protocol.ftpclient.api.FtpClientException;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.NetPermission;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PeakShiftTemplateFileServiceImpl implements PeakShiftTemplateFileService {


    @Autowired
    private CollectorCacheManager collectorCacheManager;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PeakShiftTaskService peakShiftTaskService;

    @Autowired
    private UpDownloadFileRepository upDownloadFileRepository;

    @Autowired
    private FtpClientService ftpClientService;

    @Autowired
    private DateTimeService dateTimeService;

    @Value("${peak-shift.ftp-path}")
    private String defaultFtpPath;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private PeakShiftDeviceFileRepository peakShiftDeviceFileRepository;

    @Resource(name = "PEAK_SHIFT_SNMP")
    private PeakShiftSNMPServiceImpl peakShiftSNMPService;

    /**
     * 获取bcua模板存放路径
     * @return
     */
    @Value("${peak-shift.bcuaTemplatePath}")
    private String bcuaTemplateFilePath;
    /**
     * 获取bcua模板名称
     * @return
     */
    @Value("${peak-shift.bcuaTemplateName}")
    private String bcuaTemplateName;

    /**
     * 获取bcua设备策略模板存放路径
     * @return
     */
    @Value("${peak-shift.bcuaTemplateStrategyPath}")
    private String bcuaTemplateStrategyFilePath;
    /**
     * 获取bcua设备策略模板文件名称
     * @return
     */
    @Value("${peak-shift.bcuaTemplateStrategyName}")
    private String bcuaTemplateStrategyName;

    public static final String BUILT_INIT_TEMPLATE = "builtInitTemplate6b200b52";   //初始化模板id
    public static final String BUILT_INIT_FILE = "builtInitFile4c800254"; //初始化文件id
    public static final String BUILT_INIT_TEMPLATE_FILE = "builtInitTemplateFile4c800254"; //初始化策略空白模板文件id

    private static final String REDIS_SESSION_NAME = "PeakShiftStrategyFile";
    private static final long SESSION_EXPIRE_TIME = 300;
    private static final String CONTENT_DISPOSITION = "Content-Disposition";
    private static final String RESPONSE_HEADER_FILE_NAME = "File-Name";
    public static final String DEFAULT_USER = "System";
    private static final String DEVICE_USER = "DeviceSystem";
    private static final String REDIS_UPLOAD_STATUS_SESSION_NAME = "BCUAUploadStatus";
    private static final String UTF_8 = "UTF-8";
    private final ClassPathResource classPathResource = new ClassPathResource("file/" + "peakShiftDeviceStrategy.xls");
    private static final Map<String, Integer> templateMap = new HashMap<>();
    static {
        templateMap.put("Template1", 1);
        templateMap.put("Template2", 2);
        templateMap.put("Template3", 3);
        templateMap.put("Template4", 4);
        templateMap.put("Template5", 5);
        templateMap.put("Template6", 6);
        templateMap.put("Template7", 7);
        templateMap.put("Template8", 8);
    }
    private static final Map<String, Integer> modeMap = new HashMap<>();
    static {
        modeMap.put("Day Mode", 0);
        modeMap.put("Week Mode", 1);
        modeMap.put("Month Mode", 2);
    }
    private static final Map<String,String> mode_Map = new HashMap<>();
    static {
        mode_Map.put("0","Day Mode");
        mode_Map.put("1","Week Mode");
        mode_Map.put("2","Month Mode");
    }
    private static final Map<String, Integer> priceLevelMap = new HashMap<>();
    static {
        priceLevelMap.put("Valley", 0);
        priceLevelMap.put("Average", 1);
        priceLevelMap.put("Peak", 2);
        priceLevelMap.put("Tip", 3);
    }
    private static final String SHEET9_NAME = "Template8";
    private static final Map<Integer, String> strategyTypeMap = new HashMap<>();
    static {
        strategyTypeMap.put(0,"Valley");
        strategyTypeMap.put(1,"Average");
        strategyTypeMap.put(2,"Peak");
        strategyTypeMap.put(3,"Tip");
    }
    private static final List<String> basicHeaderNameList = Arrays.asList("Para. Name","Para. Value","Unit");

    private static final List<String> templateHeaderNameList = Arrays.asList("Time Period No.","Start Time","End Time","Price Level");

    private static final List<String> holidayHeaderNameList = Arrays.asList("Holiday No.","Holidays");

    private static final List<String> modeHeaderNameList = Arrays.asList("No.","Dates","Templates");

    private static final String PEAK_SHIFT_MODE = "Peak Shift Mode";

    private static final String HOLIDAY_TEMPLATE = "Holiday Template";

    private static final String TEMPLATE_NUMBER = "Number of Templates";

    private static final String CONFIG_TEMPLATE_VERSION = "Configuration Template Version";

    private static final String HOLIDAY_TEMPLATER_NUMBER = "8";

    private static final String VERSION = "1.0";

    private static final String SHEET1_NAME = "Basic Parameter Configuration";
    private static final String SHEET10_NAME = "Holiday Configuration";

    private static final String SHEET11_NAME = "Mode Configuration";

    private static final String DATE_FORMAT = "h:mm";
    private static final String SCHEDULEDATE = "scheduleDate";
    private static final Integer FILE_TYPE_INIT = 1;
    public static final Integer FILE_TYPE_UPLOAD = 0;

    public static final String FILE_MODULE_TYPE_BCUA = "BCUA";
    private static final String FILE_MODULE_TYPE_SNMP = "SNMP";

    public static final String PEAK_FTP_PATH = "/battery/peak/snmp/";
    public static int RPC_CALL = 1;


    /**
     * 根据mac地址获取此文件正在下发中的策略模版文件信息
     *
     * @param macCode
     * @param userName
     * @return
     */
    @Override
    public PeakShiftDeviceChildBeanVo getFileIdByMac(String macCode, String userName) throws UedmException, com.zte.uedm.common.exception.UedmException {
        log.info("getFileIdByMac got device {}", macCode);
        //缓存中无mac对应的设备id时，从redis查（redis中在南向建链的时候就缓存了Mac和设备的映射信息）
        String device = getDeviceIdByMac(macCode);
        if (StringUtils.isBlank(device)) {
            log.error("getFileIdByMac, no such device in cfg. {}", macCode);
            device = getDeviceByMacFromRedis(macCode);
        }
        if (StringUtils.isBlank(device)) {
            log.error("getFileIdByMac, no such device in redis. {}", macCode);
            return null;
        }
        log.info("getFileIdByMac mac:{} dedviceId:{}", macCode, device);
        // 获取正在下发中的策略模版文件
        return peakShiftTaskService.getFileIdByDeviceId(device, PeakShiftDeviceStatusEnum.IN_PROGRESS.getId());
    }

    @Override
    public Map<Integer, String> downFileByMacCode(PeakShiftFileDeviceDto deviceBean, String languageOption, String userName, HttpServletResponse response) throws Exception{
        Map<Integer, String> resultMap = new HashMap<>();

        String macCode = deviceBean.getDeviceId();
        // 获取下发中的策略模版文件
        PeakShiftDeviceChildBeanVo fileBean = getFileIdByMac(macCode, userName);
        log.info("got device file {}", fileBean);
        if (null == fileBean || StringUtils.isBlank(fileBean.getFileId())) {
            log.error("got no such device id");
            resultMap.put(-305, "fail");
            return resultMap;
        }

        redisService.put(REDIS_SESSION_NAME, macCode, "value", SESSION_EXPIRE_TIME);
        log.info("got redis put success: {}", (Object) redisService.getCache(REDIS_SESSION_NAME, macCode));

        String fileId = fileBean.getFileId();
        // 获取文件路径
        UpDownloadFileEntity upDownloadFileEntity = upDownloadFileRepository.selectById(fileId);
        log.info("downFileByMacCode upDownloadFileEntities {}", JSON.toJSONString(upDownloadFileEntity));
        if (upDownloadFileEntity==null) {
            log.error("file id {} not exist", fileId);
            resultMap.put(-305, "fail");
            return resultMap;
        }

        // 如果是rpc调用，返回ftp路径
        if (deviceBean.getRpc() == RPC_CALL) {
            String filePath = upDownloadFileEntity.getFilePath();
            String name = upDownloadFileEntity.getName();
            resultMap.put(0, name + ":" + filePath);
            return resultMap;
        }

        //下载文件
        downloadFile(response, Collections.singletonList(upDownloadFileEntity));
        resultMap.put(0, "success");
        return resultMap;
    }

    @Override
    public void downloadFile(HttpServletResponse response, List<UpDownloadFileEntity> fileEntities) throws Exception {
        try {
            for (UpDownloadFileEntity currUpDownloadFileBean : fileEntities) {
                String filePath = currUpDownloadFileBean.getFilePath();
                String filename = currUpDownloadFileBean.getName();
                downLoadByPath(response, filePath, filename);
            }
        } catch (Exception e) {
            log.error("downloadFile error", e);
            throw new UedmException(-200, "download error!");
        }finally {
            closeFile(response.getOutputStream());
        }
    }

    public static void closeFile(ServletOutputStream os) {
        try {
            if (os!= null)
            {
                os.close();
            }
        } catch (IOException e) {
            log.error("universalDownLoad ServletOutputStream close error!", e);
        }
    }

    /* Started by AICoder, pid:577adl006c970bd14a010b8ce02e4c2077b40c30 */
    public void downLoadByPath(HttpServletResponse response, String filePath, String filename)
            throws UedmException
    {
        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {

            // JDK1.8 try()语句自动关闭流
            log.info("start to downLoadByPath,filename:{} filePath:{}", filename, filePath);
            //清空reponse
            response.reset();
            if (filePath.contains(".zip"))
            {
                response.setContentType("application/x-zip-compressed;charset=UTF-8");
            }
            else
            {
                response.setContentType("application/octet-stream;charset=UTF-8");
            }
            response.setHeader(CONTENT_DISPOSITION, "attachment;fileName=" + URLEncoder.encode(filename, "utf8"));
            defaultFtpClient.getStream(response.getOutputStream(), filePath);
            log.info("downLoadByPath success");
        } catch (Exception e) {
            log.error("downLoadByPath error", e);
            throw new UedmException(-200, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:577adl006c970bd14a010b8ce02e4c2077b40c30 */

    @Override
    public String getDeviceIdByMac(String mac) {
        log.info("service got mac: {}", mac);
        // mac是采集器的唯一标识
        CollectorEntity collectorCacheBean = collectorCacheManager.getAllCollector()
                .stream().filter(item -> StringUtils.isNotBlank(item.getIdentification()) && item.getIdentification().equals(mac))
                .findFirst().orElse(null);

        if (collectorCacheBean != null) {
            return collectorCacheBean.getId();
        }
        return null;
    }

    @Override
    public void uploadDeviceFile(PeakShiftFileDeviceDto deviceBean, InputStream fis, FormDataContentDisposition disp,
                                 String deviceId, String languageOption, HttpServletRequest request) throws Exception {
        String userName = Tools.getUserName(request);
        String remoteHost = Tools.getRemoteHost(request);
        String fileName = new String(disp.getFileName().getBytes(StandardCharsets.ISO_8859_1), "UTF-8");
        log.info("{} upload file name is: {}", remoteHost, fileName);

        String currentTime = dateTimeService.getCurrentTime();

        //对文件流进行操作，保存在本地项目方便取流数据
        File file = dealPeakShiftFile(fis);

        UpDownloadFileParameterEntity upDownloadFileParamterEntity = new UpDownloadFileParameterEntity();
        upDownloadFileParamterEntity.setOperator(DEVICE_USER);

        // 解析文件更新设备策略表;文件上传至ftp,存入文件表
        UpDownloadFileDataBasePO fileBean = getUpDownloadFileBean(file, disp, deviceId,upDownloadFileParamterEntity, currentTime);
        log.info("file upload bean. {}", fileBean);

        // 更新设备上传状态：已上传
        Integer num = changeDeviceUploadResult(deviceId, fileBean.getId(), userName, true);
        log.info("uploadDeviceFile update file status. {}", num);
    }

    @Override
    public int changeDeviceUploadResult(String deviceId, String fileId, String userName, boolean status) throws com.zte.uedm.common.exception.UedmException, UedmException {
        userName = userName == null ? DEVICE_USER : userName;
        PeakShiftDeviceFileUploadPO uploadBean = new PeakShiftDeviceFileUploadPO();
        uploadBean.setDeviceId(deviceId);
        uploadBean.setFileId(fileId);
        uploadBean.setStatus(status);
        uploadBean.setCreator(userName);
        uploadBean.setUpdater(userName);
        uploadBean.setGmtCreate(dateTimeService.getCurrentTime());
        uploadBean.setGmtModified(dateTimeService.getCurrentTime());
        return peakShiftDeviceFileRepository.setDeviceFileResult(uploadBean);
    }

    @Override
    public void downFileByIds(List<String> fileIds, HttpServletResponse response) throws Exception {
        List<UpDownloadFileEntity> upDownloadFileEntities = upDownloadFileRepository.selectByIds(fileIds);
        if (CollectionUtils.isEmpty(upDownloadFileEntities)) {
            log.error("downFileByIds file not exist {}", JSON.toJSONString(fileIds));
            throw new UedmException(-1, "not found file");
        }

        downloadFile(response, upDownloadFileEntities);
    }

    /* Started by AICoder, pid:727300fb11j4aec14f7b0b8b92181d00e8e3f21b */
    @Override
    public void exportFile(TemplateStrategyDetailEntity detail, HttpServletRequest request, HttpServletResponse response, String languageOption) throws  UedmException {
        try(ServletOutputStream out = response.getOutputStream();
            Workbook workbook = new XSSFWorkbook();
        )
        {
            response.reset();
            response.setCharacterEncoding(UTF_8);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            String fileName = detail.getName()+"_"+detail.getVersion()+".xls";
            fileName = URLEncoder.encode(fileName, UTF_8);
            response.setHeader(CONTENT_DISPOSITION, "attachment;filename=".concat(fileName));

            convertToExcel(detail, out, workbook);
        }
        catch (Exception e)
        {
            log.error("exportFile error",e);
            throw new UedmException(UedmErrorCodeConstants.FILE_EXPORT_ERROR,"file export error");
        }
    }

    /* Ended by AICoder, pid:l7f56226fez3a7514e110b18e078be1164675d75 */
    public void convertToExcel(TemplateStrategyDetailEntity detail, OutputStream out, Workbook workbook) throws IOException {
        CellStyle cellStyle = workbook.createCellStyle();
        DataFormat dataFormat = workbook.createDataFormat();
        cellStyle.setDataFormat(dataFormat.getFormat(DATE_FORMAT));

        writerBasic(workbook, detail);

        Map<String, List<Integer>> map = writerTemplate(workbook, detail, cellStyle);

        writerHolidayTemplate(detail.getHoliday(), workbook, cellStyle);

        writerHoliday(detail.getHoliday(), workbook);

        writerMode(map, workbook, detail.getMode());

        workbook.write(out);
    }
    /* Ended by AICoder, pid:l7f56226fez3a7514e110b18e078be1164675d75 */

    @Override
    @Transactional(rollbackFor = com.zte.uedm.common.exception.UedmException.class)
    public ImportFileResponseEntity importFile(FormDataMultiPart fileData, HttpServletRequest request, String languageOption) throws com.zte.uedm.common.exception.UedmException, FtpClientException, UedmException {
        FormDataBodyPart formDataBodyPart = fileData.getField("file");
        // 将前端传过来的文件数据以输入流的形式取出来
        InputStream currFormDataStream = formDataBodyPart.getValueAs(InputStream.class);

        // 取出上传文件的其他细节：form-data；filename="new2.zip" name = "file"
        FormDataContentDisposition formDataContentDisposition = formDataBodyPart.getFormDataContentDisposition();
        String fileName = getFileName(formDataContentDisposition);
        log.info("fileName ={}.",fileName);
        fileName = checkFileName(fileName);
        String fileSuffix = getFileSuffix(formDataContentDisposition);
        String currentTime = dateTimeService.getCurrentTime();

        String fileId = UUID.randomUUID().toString();
        String parentPath = getRemotePath();
        log.info("importFile remotePath : {}", parentPath);

        // 文件保存至本地方便后续解析
        File file = new File(fileId);
        copyInputStreamToDesFile(currFormDataStream, file);

        // 上传文件至ftp
        /* Started by AICoder, pid:0a54dr87147ee6b14c490b7ed0dca90b3f89859e */
        String filePath;
        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            defaultFtpClient.mkdir(parentPath);
            filePath = parentPath + File.separator + fileId + "." + fileSuffix;
            defaultFtpClient.put(file.getAbsolutePath(), filePath, false);
        } catch (Exception e) {
            log.error("importFile put file error", e);
            throw new UedmException(-1, "put file to ftp error");
        }
        /* Ended by AICoder, pid:0a54dr87147ee6b14c490b7ed0dca90b3f89859e */

        String userName = Tools.getUserName(request);
        // 文件信息入库
        UpDownloadFileDataBasePO upDownloadFileBean = new UpDownloadFileDataBasePO();
        upDownloadFileBean.setFileType(FILE_TYPE_UPLOAD);
        upDownloadFileBean.setFileSuffix(fileSuffix);
        upDownloadFileBean.setFilePath(filePath);
        upDownloadFileBean.setOriginalName(fileName);
        upDownloadFileBean.setName(fileName);
        upDownloadFileBean.setId(fileId);
        upDownloadFileBean.setModule(FILE_MODULE_TYPE_BCUA);
        upDownloadFileBean.setOperator(userName == null ? DEFAULT_USER : userName);
        upDownloadFileBean.setGmtCreate(currentTime);
        upDownloadFileBean.setGmtModified(currentTime);
        upDownloadFileRepository.insertFile(upDownloadFileBean);

        //解析文件内容
        ImportFileResponseEntity responseBean = dealFIleIntoBean(file);
        responseBean.setFileId(fileId);
        responseBean.setFileName(fileName);

        // 删除临时文件
        if (file.exists()) {
            file.delete();
        }

        return responseBean;
    }

    @Override
    public List<DetailHistoryResposeBean> getOrGeneratePeakFile(List<String> fileIds, TemplateStrategyDetailEntity detail, String version, String userName) throws UedmException{
        List<UpDownloadFileEntity> fileBeans  = null;
        if (!CollectionUtils.isEmpty(fileIds)) {
            fileBeans = upDownloadFileRepository.selectByIds(fileIds);
        }

        List<DetailHistoryResposeBean> resultList = new ArrayList<>();

        // 获取xls文件信息，若文件不存在则生成新文件
        DetailHistoryResposeBean excelBean = getExcelBean(fileBeans, detail, version, userName);
        resultList.add(excelBean);
        // 获取bin文件信息，若文件不存在则生成新文件
        DetailHistoryResposeBean binBean = getBinBean(fileBeans, detail, version, userName);
        resultList.add(binBean);
        return resultList;
    }

    @Override
    public void initPeakTemFile() {
        initFileByPath(bcuaTemplateFilePath, bcuaTemplateName, BUILT_INIT_FILE);
        initFileByPath(bcuaTemplateStrategyFilePath, bcuaTemplateStrategyName, BUILT_INIT_TEMPLATE_FILE);
    }
    /* Started by AICoder, pid:xaa9fy902cn541a149be0949c0e20e4a92919cc7 */
    public void initFileByPath(String filePath, String fileName,String fileId) {
        log.info("initFileByPath start: {},{},{}", filePath, fileName, fileId);
        if(!StringUtils.isAnyBlank(filePath,fileName))
        {
            int i = fileName.lastIndexOf(".");
            String fileSuffix = fileName.substring(i);
            try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
                // 上传至ftp，覆盖或新增
                defaultFtpClient.mkdir(defaultFtpPath);
                String remotePath = defaultFtpPath + fileId + fileSuffix;
                defaultFtpClient.put(filePath, remotePath, false);

                String currentTime = dateTimeService.getCurrentTime();

                UpDownloadFileDataBasePO upDownloadFileDataBasePO = new UpDownloadFileDataBasePO();
                upDownloadFileDataBasePO.setId(fileId);
                upDownloadFileDataBasePO.setFileType(FILE_TYPE_INIT);
                upDownloadFileDataBasePO.setName(fileName);
                upDownloadFileDataBasePO.setOriginalName(fileName);
                upDownloadFileDataBasePO.setFilePath(remotePath);
                upDownloadFileDataBasePO.setFileSuffix(fileSuffix);
                upDownloadFileDataBasePO.setGmtModified(currentTime);
                upDownloadFileDataBasePO.setOperator(DEFAULT_USER);
                upDownloadFileDataBasePO.setModule(FILE_MODULE_TYPE_BCUA);

                //根据id查询  有存在则更新,无则新增
                UpDownloadFileEntity peakShiftTemplateFileBean = upDownloadFileRepository.selectById(fileId);
                log.info("initFileByPath peakShiftTemplateFileBean: {} {}", JSON.toJSONString(peakShiftTemplateFileBean), fileId);

                if (null == peakShiftTemplateFileBean) {
                    upDownloadFileDataBasePO.setGmtCreate(currentTime);
                    upDownloadFileRepository.insertFile(upDownloadFileDataBasePO);
                    log.info("initFileByPath add file {}", upDownloadFileDataBasePO);
                } else {
                    upDownloadFileRepository.update(upDownloadFileDataBasePO);
                    log.info("initFileByPath update file {}", upDownloadFileDataBasePO);
                }
            } catch (Exception e) {
                log.error("initFileByPath error", e);
            }
        }
    }
    /* Ended by AICoder, pid:xaa9fy902cn541a149be0949c0e20e4a92919cc7 */


    @Override
    public int deleteFiles(List<String> fileIds) throws UedmException {
        if (CollectionUtils.isEmpty(fileIds)) {
            log.warn("deleteFiles file id is empty");
            return 0;
        }
        List<UpDownloadFileEntity> upDownloadFileEntities = upDownloadFileRepository.selectByIds(fileIds);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(upDownloadFileEntities)) {
            log.error("deleteFiles file is empty {}", JSON.toJSONString(fileIds));
            return 0;
        }

        log.info("deleteFiles id {}", JSON.toJSONString(fileIds));
        int result = upDownloadFileRepository.deleteByIds(fileIds);

        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            for (UpDownloadFileEntity upDownloadFileEntity : upDownloadFileEntities) {
                String filePath = upDownloadFileEntity.getFilePath();
                if (StringUtils.isNotBlank(filePath)) {
                    log.debug("deleteFiles ftp file {}", filePath);
                    defaultFtpClient.delete(filePath);
                }
            }
        } catch (Exception e) {
            log.error("deleteFiles delete Ftp file error", e);
        }
        return result;
    }

    /**
     * 发送给南向的错峰文件名称固定为PEAKSHIFTTEMPLATE.bin
     * 为了防止文件被覆盖，需要将文件移动到以采集器为父目录的文件夹下
     * @param fileId
     * @param collectId
     * @return
     */
    @Override
    public String moveFtpFile(String fileId, String collectId) throws com.zte.uedm.common.exception.UedmException {
        UpDownloadFileEntity upDownloadFileBean = null;
        try {
            upDownloadFileBean = upDownloadFileRepository.selectById(fileId);
        } catch (UedmException e) {
            throw new com.zte.uedm.common.exception.UedmException(e.getErrorId(), e.getMessage());
        }
        if (upDownloadFileBean == null || StringUtils.isEmpty(upDownloadFileBean.getFilePath())) {
            log.error("uploadFileToFtp not find file fileId {}", fileId);
            return null;
        }

        String filePath = upDownloadFileBean.getFilePath();
        // 以采集器id作为父目录
        String parentPath = PEAK_FTP_PATH + collectId + File.separator;
        String remotePath = parentPath + CommonConst.SNMP_BIN_FILE_NAME;

        String uuid = UUID.randomUUID().toString();
        File tempFile = new File(uuid);

        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            // 先将文件下载至本地
            String localPath = tempFile.getAbsolutePath();
            defaultFtpClient.getFile(localPath, filePath);
            log.info("moveFtpFile down .{}", localPath);

            // 再上传文件至新目录
            defaultFtpClient.mkdir(parentPath);
            defaultFtpClient.put(localPath, remotePath, false);
            log.info("moveFtpFile upload. {}", remotePath);

            //文件信息存表
            String currentTime = dateTimeService.getCurrentTime();
            UpDownloadFileDataBasePO fileDataBasePO = new UpDownloadFileDataBasePO();
            fileDataBasePO.setFileType(FILE_TYPE_UPLOAD);
            fileDataBasePO.setFileSuffix(CommonConst.SUFFIX_BIN);
            fileDataBasePO.setFilePath(remotePath);
            fileDataBasePO.setOriginalName(CommonConst.SNMP_BIN_FILE_NAME);
            fileDataBasePO.setName(CommonConst.SNMP_BIN_FILE_NAME);
            fileDataBasePO.setId(uuid);
            fileDataBasePO.setGmtCreate(currentTime);
            fileDataBasePO.setGmtModified(currentTime);
            fileDataBasePO.setModule(FILE_MODULE_TYPE_BCUA);
            fileDataBasePO.setOperator(DEFAULT_USER);
            upDownloadFileRepository.insertFile(fileDataBasePO);

        } catch (Exception e) {
            log.error("uploadFileToFtp upload file error.", e);
            throw new com.zte.uedm.common.exception.UedmException(-1, "upload file to ftp error!");
        }finally {
            if (tempFile.exists()) {
                tempFile.delete();
                log.info("moveFtpFile delete temp File {}", uuid);
            }
        }

        return remotePath;
    }

    /* Started by AICoder, pid:qa92c6fa0fa91d8141d00a16e04bd736c5a2dca9 */
    @Override
    public void convertAndSaveFile(String fileId, String collectId) throws UedmException {

        UpDownloadFileEntity upDownloadFileEntity = upDownloadFileRepository.selectById(fileId);
        if (upDownloadFileEntity == null) {
            log.error("convertAndSaveFile not found file {}", fileId);
            return;
        }
        log.info("convertAndSaveFile ftp Path {}", upDownloadFileEntity.getFilePath());

        String tempFileId = UUID.randomUUID().toString();
        File file = new File(tempFileId);
        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            defaultFtpClient.getFile(file.getAbsolutePath(), upDownloadFileEntity.getFilePath());

            // 文件解析为bean
            PeakShiftDeviceStrategyDetailBean detailBean = parseFileToBean(file);
            // 更新设备文件版本
            setPeakShiftDeviceStrategyBean(collectId, detailBean.getId(), new PeakShiftDeviceStrategyBean());
            // 新增错峰策略详情
            peakShiftDeviceFileRepository.insertPeakShiftDeviceStrategyDetail(detailBean);
            // 设备文件记录表
            changeDeviceUploadResult(collectId, fileId, DEFAULT_USER, true);
        } catch (Exception e) {
            log.error("convertAndSaveFile error", e);
        }finally {
            if (file.exists()) {
                file.delete();
            }
        }
    }
    /* Ended by AICoder, pid:qa92c6fa0fa91d8141d00a16e04bd736c5a2dca9 */

    public DetailHistoryResposeBean getExcelBean(List<UpDownloadFileEntity> fileBeans, TemplateStrategyDetailEntity detail,
                                                 String version, String user) throws UedmException {

        DetailHistoryResposeBean result = new DetailHistoryResposeBean();

        // 过滤xls文件
        Optional<UpDownloadFileEntity> optional = Optional.ofNullable(fileBeans).orElse(new ArrayList<>())
                .stream().filter(bean -> CommonConst.SUFFIX_XLS.equals(bean.getFileSuffix()))
                .findAny();
        if (optional.isPresent()) {
            UpDownloadFileEntity upDownloadFileBean = optional.get();
            result.setFileId(upDownloadFileBean.getId());
            result.setFileName(upDownloadFileBean.getName());
            result.setVersion(version);
            result.setTempelateStrategyId(detail.getId());
            log.info("getExcelBean excel exist {}", result.getFileId());
            return result;
        }

        // 无.xls文件，生成新文件
        String parentPath = getRemotePath();
        String fileName = detail.getName() + "_" + version + CommonConst.SUFFIX_XLS;
        String fileId = UUID.randomUUID().toString();
        File file = new File(fileId);
        log.info("getExcelBean excel not exist, new file:{}", fileId);

        try (FileOutputStream out = new FileOutputStream(file);
             HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
             FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            HSSFCellStyle cellStyle = hssfWorkbook.createCellStyle();
            cellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat(DATE_FORMAT));

            writerBasic(hssfWorkbook, detail);
            Map<String, List<Integer>> map = writerTemplate(hssfWorkbook, detail, cellStyle);
            writerHolidayTemplate(detail.getHoliday(), hssfWorkbook, cellStyle);
            writerHoliday(detail.getHoliday(), hssfWorkbook);
            writerMode(map, hssfWorkbook, detail.getMode());
            hssfWorkbook.write(out);

            // 文件上传至ftp
            defaultFtpClient.mkdir(parentPath);
            String filePath = parentPath + File.separator + fileId + CommonConst.SUFFIX_XLS;
            defaultFtpClient.put(file.getAbsolutePath(), filePath, false);

            //上传信息保存入库
            UpDownloadFileDataBasePO upDownloadFileBean = new UpDownloadFileDataBasePO();
            upDownloadFileBean.setFileType(FILE_TYPE_UPLOAD);
            upDownloadFileBean.setFileSuffix(CommonConst.SUFFIX_XLS);
            upDownloadFileBean.setFilePath(filePath);
            upDownloadFileBean.setOriginalName(fileName);
            upDownloadFileBean.setName(fileName);
            upDownloadFileBean.setId(fileId);
            upDownloadFileBean.setOperator(user == null ? DEFAULT_USER : user);
            String currentTime = dateTimeService.getCurrentTime();
            upDownloadFileBean.setGmtCreate(currentTime);
            upDownloadFileBean.setGmtModified(currentTime);
            upDownloadFileBean.setModule(FILE_MODULE_TYPE_BCUA);
            upDownloadFileRepository.insertFile(upDownloadFileBean);

        } catch (Exception e) {
            log.error("exportFile error", e);
            throw new UedmException(UedmErrorCodeConstants.FILE_EXPORT_ERROR, "file export error");
        }finally {
            log.info("delete tem file {}", fileId);
            if (file.exists()) {
                file.delete();
            }
        }
        result.setFileId(fileId);
        result.setFileName(fileName);
        result.setVersion(version);
        result.setTempelateStrategyId(detail.getId());
        return result;
    }


    public DetailHistoryResposeBean getBinBean(List<UpDownloadFileEntity> fileBeans, TemplateStrategyDetailEntity detail,
                                               String version,String user) throws UedmException {

        DetailHistoryResposeBean result = new DetailHistoryResposeBean();

        // 过滤bin文件
        Optional<UpDownloadFileEntity> optional = Optional.ofNullable(fileBeans).orElse(new ArrayList<>())
                .stream().filter(bean -> CommonConst.SUFFIX_BIN.equals(bean.getFileSuffix()))
                .findAny();
        if (optional.isPresent()) {
            UpDownloadFileEntity upDownloadFileBean = optional.get();
            result.setFileId(upDownloadFileBean.getId());
            result.setFileName(upDownloadFileBean.getName());
            result.setVersion(version);
            result.setTempelateStrategyId(detail.getId());
            log.info("getBinBean bin file exist {}", upDownloadFileBean.getId());
            return result;
        }

        // 无.bin文件，生成新文件
        String parentPath = getRemotePath();
        String fileName = detail.getName() + "_" + version + CommonConst.SUFFIX_BIN;
        String fileId = UUID.randomUUID().toString();
        log.info("getBinBean bin file not exist {}", fileId);

        // 二进制报文转文件
        Path path = Paths.get(fileId);
        try (OutputStream os = Files.newOutputStream(path);
             FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {

            // 解析策略模版二进制报文
            byte[] bytes = peakShiftSNMPService.convertTemplatesToBytes(detail);
            log.debug("getBinBean bytes: ");
            for (int i = 0; i < bytes.length; i++) {
                log.debug("getBinBean {} :{}",i, byteToBinaryString(bytes[i]));
            }
            log.debug("getBinBean end");

            // 将字节数组转换为ByteBuffer，并设置为小端模式
            ByteBuffer buffer = ByteBuffer.wrap(bytes);
            buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN);

            // 将ByteBuffer中的数据写入到输出流中
            while (buffer.hasRemaining()) {
                os.write(buffer.get());
            }

            log.info("getBinBean generate file:{}", fileId);

            // 文件上传至ftp
            defaultFtpClient.mkdir(parentPath);
            String filePath = parentPath + File.separator + fileId + CommonConst.SUFFIX_BIN;
            defaultFtpClient.putStream(Files.newInputStream(path), filePath, false);

            //上传信息保存入库
            UpDownloadFileDataBasePO upDownloadFileBean = new UpDownloadFileDataBasePO();
            upDownloadFileBean.setFileType(FILE_TYPE_UPLOAD);
            upDownloadFileBean.setFileSuffix(CommonConst.SUFFIX_BIN);
            upDownloadFileBean.setFilePath(filePath);
            upDownloadFileBean.setOriginalName(fileName);
            upDownloadFileBean.setName(fileName);
            upDownloadFileBean.setId(fileId);
            upDownloadFileBean.setOperator(user == null ? DEFAULT_USER : user);
            String currentTime = dateTimeService.getCurrentTime();
            upDownloadFileBean.setGmtCreate(currentTime);
            upDownloadFileBean.setGmtModified(currentTime);
            upDownloadFileBean.setModule(FILE_MODULE_TYPE_SNMP);
            upDownloadFileRepository.insertFile(upDownloadFileBean);

        } catch (Exception e) {
            log.error("getBinBean generate file error ", e);
            throw new UedmException(UedmErrorCodeConstants.FILE_EXPORT_ERROR, "file export error");
        }

        result.setFileId(fileId);
        result.setFileName(fileName);
        result.setVersion(version);
        result.setTempelateStrategyId(detail.getId());
        return result;
    }

    private String byteToBinaryString(byte b) {
        StringBuilder sb = new StringBuilder();
        for (int i = 7; i >= 0; --i) {
            int value = (b >> i) & 0x1;
            sb.append(value);
        }
        return sb.toString();
    }


    /* Started by AICoder, pid:bb35bt8eb2d251f14c24080730f97085dd58bd6a */
    public ImportFileResponseEntity dealFIleIntoBean(File file) throws com.zte.uedm.common.exception.UedmException
    {
        ImportFileResponseEntity responseBean = new ImportFileResponseEntity();

        List<BasicTemplateVO> basicTemplate = new ArrayList<>();
        List<String> holidayVOS = new ArrayList<>();
        List<ModeTemplateDto> modeTemplateDtos = new ArrayList<>();
        try
        {
            List<Object> basicTemplateObj = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 0, new BasicTemplateVO());
            List<Object> holidayObjVOS = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 9, new HolidayFileVO());
            List<Object> modeTemplateObjVOS = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 10, new ModeTemplateVO());

            //对模板数据进行处理
            basicTemplateObj.forEach(o -> {
                BasicTemplateVO vo = (BasicTemplateVO) o;
                basicTemplate.add(vo);
            });
            holidayObjVOS.forEach(o -> {
                HolidayFileVO vo = (HolidayFileVO) o;
                holidayVOS.add(vo.getHolidayStr());
            });
            modeTemplateObjVOS.forEach(o -> {
                ModeTemplateVO vo = (ModeTemplateVO) o;
                ModeTemplateDto dto = new ModeTemplateDto();
                dto.setNum(Integer.valueOf(vo.getDates()));
                dto.setTemplateNum(templateMap.get(vo.getTemplate()));
                modeTemplateDtos.add(dto);
            });
        }
        catch (IOException e)
        {
            log.error("getIntervalDetailBeanList error",e);
            throw new com.zte.uedm.common.exception.UedmException(UedmErrorCodeConstants.PARAMETER_FORMAT_TANSFER_FAILED,"File content format error");
        }
        List<IntervalDetailBean> intervalDetailBeanList = getIntervalDetailBeanList(file);
        Map<Integer, List<IntervalDetailBean>> detailMap = intervalDetailBeanList.stream()
                .collect(Collectors.groupingBy(IntervalDetailBean::getNum));
        checkTemplateDetail(detailMap);
        Map<Integer, List<Integer>> modeTemplateMap = modeTemplateDtos.stream().collect(
                Collectors.groupingBy(ModeTemplateDto::getTemplateNum,
                        Collectors.mapping(ModeTemplateDto::getNum, Collectors.toList())));

        List<IntervalVo> detail = new ArrayList<>();
        Integer peakShiftMode = modeMap.get(
                basicTemplate.stream().filter(p -> PEAK_SHIFT_MODE.equals(p.getName())).findAny()
                        .orElse(new BasicTemplateVO()).getValue());
        Integer holidayTemplate = templateMap.get(
                basicTemplate.stream().filter(p -> HOLIDAY_TEMPLATE.equals(p.getName())).findAny()
                        .orElse(new BasicTemplateVO()).getValue());
        for (Integer key : detailMap.keySet())
        {
            if(modeTemplateMap.get(key) == null)
            {
                continue;
            }
            IntervalVo intervalBean = new IntervalVo();
            if (!peakShiftMode.equals(0))
            {
                intervalBean.setTimeGran(modeTemplateMap.get(key));
            }
            intervalBean.setDetail(detailMap.get(key));
            detail.add(intervalBean);
        }

        responseBean.setMode(String.valueOf(peakShiftMode));
        responseBean.setDetail(detail);

        List<IntervalDetailBean> intervalDetailBeans = detailMap.get(holidayTemplate);
        // 节假日策略为空时，前端要求传null
        if (!org.springframework.util.CollectionUtils.isEmpty(intervalDetailBeans)) {
            List<HolidayDetailVo> periodTime = getPeriodTime(holidayVOS);
            List<HolidayVo> holidayBeans = new ArrayList<>();
            HolidayVo holidayBean = new HolidayVo();

            // 节假日日期范围为空时，前端要求传null，传空数组前端无法校验
            holidayBean.setTimeGran(org.springframework.util.CollectionUtils.isEmpty(periodTime) ? null : periodTime);
            holidayBean.setDetail(intervalDetailBeans);
            holidayBeans.add(holidayBean);

            responseBean.setHoliday(holidayBeans);
        }
        return responseBean;
    }
    /* Ended by AICoder, pid:bb35bt8eb2d251f14c24080730f97085dd58bd6a */

    /**
     * 获取假期模式详情，连续日期转为时间段表示
     * @param timeList
     * @return
     * @throws com.zte.uedm.common.exception.UedmException
     */
    /* Started by AICoder, pid:p6433s6a54h01f014e360be9302fe48b5220370b */
    public List<HolidayDetailVo> getPeriodTime(List<String> timeList) throws com.zte.uedm.common.exception.UedmException
    {
        try
        {
            if (org.springframework.util.CollectionUtils.isEmpty(timeList))
            {
                return new ArrayList<>();
            }
            List<Map<String, Object>> object = new ArrayList<>();
            for (String date : timeList)
            {
                Map<String, Object> map = new HashMap<>();
                map.put(SCHEDULEDATE, date);
                object.add(map);
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd");
            Calendar calendar = Calendar.getInstance();
            Date startDate = dateFormat.parse(String.valueOf(object.get(0).get(SCHEDULEDATE)));

            boolean is = false;
            List<Map<String, Object>> tempList = new ArrayList<>();
            for (int i = 0; i < object.size(); i++)
            {
                if (is)
                {
                    startDate = dateFormat.parse(String.valueOf(object.get(i).get(SCHEDULEDATE)));
                    is = false;
                }
                Date parse = dateFormat.parse(String.valueOf(object.get(i).get(SCHEDULEDATE)));
                Date parseNext = getParseNext(object,i,dateFormat);

                calendar.setTime(parse);
                calendar.add(Calendar.DATE, 1);
                Date time2 = calendar.getTime();

                //如果parse+1天不等于parse1
                if (parseNext.compareTo(time2) != 0 || (i + 1) == object.size())
                {
                    Date endDate = getEndDate(parseNext,parse,i,object);
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("begin", dateFormat.format(startDate));
                    map1.put("end", dateFormat.format(endDate));
                    tempList.add(map1);
                    startDate = parseNext;
                    is = true;
                }

            }
            return getList(tempList);
        }
        catch (Exception e)
        {
            log.error("getPeriodTime() date parse error",e);
            throw new com.zte.uedm.common.exception.UedmException(UedmErrorCodeConstants.PARAMETER_FORMAT_TANSFER_FAILED,"date parse error");
        }
    }

    private List<HolidayDetailVo> getList(List<Map<String, Object>> tempList)
    {
        List<HolidayDetailVo> list = new ArrayList<>();
        Iterator<Map<String, Object>> it = tempList.iterator();
        while (it.hasNext())
        {
            Map<String, Object> timeMap = it.next();
            String beginTime = "";
            String endTime = "";
            for (Map.Entry<String, Object> entry : timeMap.entrySet())
            {
                if (entry.getKey().equals("begin"))
                {
                    beginTime = (String) entry.getValue();
                }
                if (entry.getKey().equals("end"))
                {
                    endTime = (String) entry.getValue();
                }
            }
            HolidayDetailVo bean = new HolidayDetailVo();
            bean.setBegin(beginTime);
            bean.setEnd(endTime);
            list.add(bean);
        }
        return list;
    }


    private Date getEndDate(Date parseNext,Date parse,int i,List<Map<String, Object>> object)
    {

        if ((i + 1) == object.size())
        {       //如果循环到最后一个
            return parseNext;
        }
        else
        {
            return parse;
        }
    }

    private Date getParseNext(List<Map<String, Object>> object,int i,SimpleDateFormat dateFormat) throws ParseException
    {
        if (object.size() == (i + 1)) {

            return dateFormat.parse(String.valueOf(object.get(i).get(SCHEDULEDATE)));
        } else {
            return dateFormat.parse(String.valueOf(object.get(i + 1).get(SCHEDULEDATE)));
        }
    }
    /* Ended by AICoder, pid:p6433s6a54h01f014e360be9302fe48b5220370b */
    public void checkTemplateDetail(Map<Integer, List<IntervalDetailBean>> map) throws com.zte.uedm.common.exception.UedmException
    {
        for (Integer key : map.keySet())
        {
            List<IntervalDetailBean> beans = map.get(key);
            if (!org.springframework.util.CollectionUtils.isEmpty(beans))
            {
                checkTimePeriod(beans);
            }
        }
    }

    /**
     * 校验template页时段覆盖0:00-24:00
     * @param beans
     * @throws com.zte.uedm.common.exception.UedmException
     */
    public void checkTimePeriod(List<IntervalDetailBean> beans) throws com.zte.uedm.common.exception.UedmException
    {
        beans = beans.stream().sorted(Comparator.comparing(IntervalDetailBean::getBeginTime)).collect(Collectors.toList());
        Map<String, String> timeMap = new HashMap<>();
        List<String> keyList = new ArrayList<>();
        for (IntervalDetailBean bean : beans)
        {
            timeMap.put(bean.getBeginTime(), bean.getEndTime());
            keyList.add(bean.getBeginTime());
        }
        Collections.sort(keyList);
        for (int i = 0; i < keyList.size(); i++)
        {
            if (i > 0)
            {
                if (!keyList.get(i).equals(timeMap.get(keyList.get(i - 1))))
                {
                    throw new com.zte.uedm.common.exception.UedmException(-3061, "Template TimePeriod error");
                }
            }
        }
        String lastKey = keyList.get(keyList.size() - 1);
        if (!"00:00".equals(keyList.get(0)) || !"24:00".equals(timeMap.get(lastKey)))
        {
            throw new com.zte.uedm.common.exception.UedmException(-3061, "Template TimePeriod error");
        }
    }

    /**
     * 解析时段详情
     * @return
     * @throws com.zte.uedm.common.exception.UedmException
     */
    //TODO 已调整
    public List<IntervalDetailBean> getIntervalDetailBeanList(File file) throws com.zte.uedm.common.exception.UedmException
    {
        try
        {
            List<IntervalDetailBean> intervalDetailBeanList = new ArrayList<>();
            List<TemplateVO> templateVOList = new ArrayList<>();

            List<Object> templateObj1 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 1, new TemplateVO());
            List<Object> templateObj2 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 2, new TemplateVO());
            List<Object> templateObj3 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 3, new TemplateVO());
            List<Object> templateObj4 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 4, new TemplateVO());
            List<Object> templateObj5 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 5, new TemplateVO());
            List<Object> templateObj6 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 6, new TemplateVO());
            List<Object> templateObj7 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 7, new TemplateVO());
            List<Object> templateObj8 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 8, new TemplateVO());

            //对模板数据进行处理
            templateObj1.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(1);
                templateVOList.add(vo);
            });
            templateObj2.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(2);
                templateVOList.add(vo);
            });
            templateObj3.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(3);
                templateVOList.add(vo);
            });
            templateObj4.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(4);
                templateVOList.add(vo);
            });
            templateObj5.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(5);
                templateVOList.add(vo);
            });
            templateObj6.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(6);
                templateVOList.add(vo);
            });
            templateObj7.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(7);
                templateVOList.add(vo);
            });
            templateObj8.forEach(o -> {
                TemplateVO vo = (TemplateVO) o;
                vo.setNum(8);
                templateVOList.add(vo);
            });
            //对templateVo装换成需要的bean
            templateVOList.forEach(o -> {
                IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
                intervalDetailBean.setNum(o.getNum());
                intervalDetailBean.setBeginTime(replaceBeginTime2(o.getStartTime()));
                intervalDetailBean.setEndTime(replaceEndTime2(o.getEndTime()));
                intervalDetailBean.setStrategyType(priceLevelMap.get(o.getPriceLevel()));
                intervalDetailBeanList.add(intervalDetailBean);
            });
            return intervalDetailBeanList;
        }
        catch (IOException e)
        {
            log.error("getIntervalDetailBeanList error",e);
            throw new com.zte.uedm.common.exception.UedmException(UedmErrorCodeConstants.PARAMETER_FORMAT_TANSFER_FAILED,"File content format error");
        }
    }

    public void copyInputStreamToDesFile(InputStream in, File des) throws com.zte.uedm.common.exception.UedmException
    {
        try (OutputStream outputStream = new FileOutputStream(des);)
        {
            byte[] buff = new byte[1024];
            int byteRead;
            checkPerm(buff);
            while ((byteRead = in.read(buff)) != - 1)
            {
                outputStream.write(buff, 0, byteRead);
            }
        }
        catch (IOException e)
        {
            log.error("", e);
            throw new com.zte.uedm.common.exception.UedmException(- 1, e.getMessage());
        }
        finally
        {
            close(in);
        }
    }

    public void close(InputStream inputStream)
    {
        try
        {
            if (null != inputStream)
            {
                inputStream.close();
            }
        }
        catch (IOException e)
        {
            log.error("", e);
        }
    }

    public static void checkPerm(Object object)
    {
        SecurityManager sm = System.getSecurityManager();
        log.debug("sm:{},object:{}",sm,object);
        if(sm != null)
        {
            sm.checkPermission(new NetPermission("setResponseCache"));
        }
    }


    private void writerBasic(Workbook workbook,TemplateStrategyDetailEntity detail)
    {
        Sheet sheet = workbook.createSheet(SHEET1_NAME);
        List<List<String>> basicTemplate = getBasicTemplateList(detail);
        writerExcel(sheet,basicHeaderNameList,basicTemplate);
    }

    private List<List<String>> getBasicTemplateList(TemplateStrategyDetailEntity detail)
    {
        List<BasicTemplateVO> baseList = new ArrayList<>();
        String mode = mode_Map.get(detail.getMode());
        BasicTemplateVO basicTemplateVO = new BasicTemplateVO();
        basicTemplateVO.setName(TEMPLATE_NUMBER);
        basicTemplateVO.setValue(HOLIDAY_TEMPLATER_NUMBER);
        BasicTemplateVO basicTemplateVO1 = new BasicTemplateVO();
        basicTemplateVO1.setName(PEAK_SHIFT_MODE);
        basicTemplateVO1.setValue(mode);
        BasicTemplateVO basicTemplateVO2 = new BasicTemplateVO();
        basicTemplateVO2.setName(HOLIDAY_TEMPLATE);
        basicTemplateVO2.setValue(SHEET9_NAME);
        BasicTemplateVO basicTemplateVO3 = new BasicTemplateVO();
        basicTemplateVO3.setName(CONFIG_TEMPLATE_VERSION);
        basicTemplateVO3.setValue(VERSION);
        baseList.add(basicTemplateVO);
        baseList.add(basicTemplateVO1);
        baseList.add(basicTemplateVO2);
        baseList.add(basicTemplateVO3);
        List<List<String>> data = new ArrayList<>();
        for (BasicTemplateVO templateVO : baseList)
        {
            List<String> oneRowData = new ArrayList<>();
            oneRowData.add(templateVO.getName());
            oneRowData.add(templateVO.getValue());
            oneRowData.add(templateVO.getUnit());
            data.add(oneRowData);
        }
        return data;
    }

    private void writerExcel(Sheet sheet,List<String> headList,List<List<String>> dataList)
    {

        Row row = sheet.createRow(0);
        for (int i = 0; i < headList.size(); i++) {
            //创建单元格
            Cell cell = row.createCell(i);
            cell.setCellValue(headList.get(i));
        }
        int rowTemp = 1;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(rowTemp);
            for (int j = 0; j < dataList.get(i).size(); j++) {
                //创建单元格
                Cell cell = row.createCell(j);
                cell.setCellValue(dataList.get(i).get(j));
            }
            rowTemp++;
        }
    }

    private void writerExcelWithStyle(Sheet sheet,List<String> headList,List<List<String>> dataList,
                                      CellStyle cellStyle)
    {

        Row row = sheet.createRow(0);
        for (int i = 0; i < headList.size(); i++) {
            //创建单元格
            Cell cell = row.createCell(i);
            cell.setCellValue(headList.get(i));
        }
        int rowTemp = 1;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(rowTemp);
            for (int j = 0; j < dataList.get(i).size(); j++) {
                //创建单元格
                Cell cell = row.createCell(j);
                cell.setCellValue(dataList.get(i).get(j));
                if (j > 0 && j < 3)
                {
                    cell.setCellValue(Double.valueOf(dataList.get(i).get(j)));
                    cell.setCellStyle(cellStyle);
                }
            }
            rowTemp++;
        }
    }

    /**
     * 写入周期模式模板页数据
     * @param workbook
     * @param detail
     * @return
     */
    public Map<String,List<Integer>> writerTemplate(Workbook workbook,TemplateStrategyDetailEntity detail,CellStyle cellStyle)
    {
        List<TemplateDetailBaseDto> list = detail.getDetail();
        Map<String,List<Integer>> map = new HashMap<>();
        int count = 0;
        if (!org.springframework.util.CollectionUtils.isEmpty(list))
        {
            count = list.size();
            for (int i = 0; i < count; i++)
            {
                List<List<String>> templateList = new ArrayList<>();
                TemplateDetailBaseDto intervalBean = list.get(i);
                List<Integer> timeGran = null;
                if (intervalBean != null)
                {
                    templateList = getTemplateList(intervalBean.getDetail());
                    timeGran = intervalBean.getTimeGran();
                }
                Sheet sheet = workbook.createSheet("Template" + (i + 1));
                writerExcelWithStyle(sheet,templateHeaderNameList,templateList,cellStyle);
                map.put(sheet.getSheetName(),timeGran);
            }
        }
        if (count < 7)
        {
            for (int i = count; i < 7; i++)
            {
                List<List<String>> templateList = defaultTemplate();
                Sheet sheet = workbook.createSheet("Template" + (i + 1));
                writerExcelWithStyle(sheet,templateHeaderNameList,templateList,cellStyle);
            }
        }
        return map;
    }

    /**
     * 写入节假日模式模板页数据
     * @param holiday
     * @param hssfWorkbook
     */
    public void writerHolidayTemplate(List<TemplateHolidayDto> holiday,Workbook hssfWorkbook, CellStyle cellStyle)
    {
        List<List<String>> holidayTemplateData = defaultTemplate();
        if (!org.springframework.util.CollectionUtils.isEmpty(holiday))
        {
            TemplateHolidayDto holidayBean = holiday.get(0);
            if (holidayBean != null)
            {
                holidayTemplateData = getTemplateList(holidayBean.getDetail());
            }
        }
        Sheet sheet = hssfWorkbook.createSheet(SHEET9_NAME);
        writerExcelWithStyle(sheet,templateHeaderNameList,holidayTemplateData,cellStyle);
    }

    public List<List<String>> getTemplateList(List<TemplateTimeGranDetailVo> beanDetail)
    {
        List<List<String>> list = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(beanDetail))
        {
            int num = 1;
            Set<String> beginSet = beanDetail.stream().filter(bean -> StringUtils.isNotBlank(bean.getBeginTime())).map(TemplateTimeGranDetailVo::getBeginTime).collect(Collectors.toSet());
            for (TemplateTimeGranDetailVo bean : beanDetail)
            {
                List<String> objectList = new ArrayList<>();
                objectList.add(String.valueOf(num));
                num++;
                String startTime = updateStartTime(bean.getBeginTime());
                String endTime = updateEndTime(bean.getEndTime(),beginSet);
                startTime = updateTime(startTime);
                endTime = updateTime(endTime);
                objectList.add(startTime);
                objectList.add(endTime);
                objectList.add(strategyTypeMap.get(bean.getStrategyType()));
                list.add(objectList);
            }
        }
        return list;
    }

    public String updateStartTime(String timeStr)
    {
        String[] strings = timeStr.split(":");
        if (strings[0].length() == 2 && strings[0].startsWith("0"))
        {
            strings[0] = strings[0].substring(1);
        }
        return StringUtils.join(strings, ":")+":00";
    }

    /**
     * 转换结束时间格式，并减一分钟
     * @param timeStr
     * @return
     */
    public String updateEndTime(String timeStr,Set<String> beginSet)
    {
        String[] strings = timeStr.split(":");
        Integer hour = Integer.valueOf(strings[0]);
        Integer minute = Integer.valueOf(strings[1]);
        log.info("updateEndTime timeStr:{} begin:{}", timeStr, JSON.toJSONString(beginSet));
        if (beginSet.contains(timeStr) || (hour == 24 && minute == 0)) {
            if (minute == 0) {
                minute = 59;
                hour--;
            } else {
                minute--;
            }
        }
        strings[0] = String.valueOf(hour);
        strings[1] = String.format("%02d",minute);
        return StringUtils.join(strings, ":")+":59";
    }


    public List<List<String>> defaultTemplate()
    {
        List<String> objectList = new ArrayList<>();
        objectList.add("1");
        String startTime = "0:00:00";
        String endTime = "23:59:59";
        startTime = updateTime(startTime);
        endTime = updateTime(endTime);
        objectList.add(startTime);
        objectList.add(endTime);
        objectList.add(strategyTypeMap.get(1));
        return Arrays.asList(objectList);
    }
    /* Ended by AICoder, pid:727300fb11j4aec14f7b0b8b92181d00e8e3f21b */
    public String updateTime(String timeStr)
    {
        String[] strings = timeStr.split(":");
        Integer hour = Integer.valueOf(strings[0]);
        Integer minute = Integer.valueOf(strings[1]);
        Integer second = Integer.valueOf(strings[2]);
        if (hour.equals(0) && minute.equals(0))
        {
            return "0";
        }
        BigDecimal num1 = new BigDecimal(hour * 60 * 60 + minute * 60 + second);
        BigDecimal num2 = new BigDecimal(60 * 60 * 24);
        return num1.divide(num2,10, RoundingMode.DOWN).toString();
    }

    /**
     * 写入节假日模式配置页数据
     * @param holiday
     * @param hssfWorkbook
     */
    public void writerHoliday(List<TemplateHolidayDto> holiday,Workbook hssfWorkbook)
    {
        List<List<String>> holidayData = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(holiday))
        {
            TemplateHolidayDto holidayBean = holiday.get(0);
            if (holidayBean != null)
            {
                List<TemplateTimeGranVo> timeGran = holidayBean.getTimeGran();
                if (!org.springframework.util.CollectionUtils.isEmpty(timeGran))
                {
                    timeGran = timeGran.stream().sorted(Comparator.comparing(TemplateTimeGranVo::getBegin)).collect(Collectors.toList());
                    List<String> list = new ArrayList<>();
                    for (TemplateTimeGranVo bean : timeGran)
                    {
                        List<String> days = getDays(bean.getBegin(), bean.getEnd());
                        list.addAll(days);
                    }
                    int num = 1;
                    for (String day : list)
                    {
                        List<String> objectList = new ArrayList<>();
                        objectList.add(String.valueOf(num));
                        objectList.add(day);
                        num +=1;
                        holidayData.add(objectList);
                    }
                }
            }
        }

        Sheet sheet = hssfWorkbook.createSheet(SHEET10_NAME);
        writerExcel(sheet,holidayHeaderNameList,holidayData);
    }

    public List<String> getDays(String startTime, String endTime)
    {
        // 返回的日期集合
        List<String> days = new ArrayList<>();
        startTime = startTime.replace("-", "/");
        endTime = endTime.replace("-", "/");

        DateFormat dateFormat = new SimpleDateFormat("M/d");
        try
        {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd))
            {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        }
        catch (Exception e)
        {
            log.error("date parse error", e);
        }
        return days;
    }

    /**
     * 写入周期模式配置页数据
     * @param map
     * @param hssfWorkbook
     */
    public void writerMode(Map<String, List<Integer>> map,Workbook hssfWorkbook,String mode)
    {
        int num = 1;
        List<List<String>> modeData = new ArrayList<>();
        if (mode.equals("0"))
        {
            List<String> objectList = new ArrayList<>();
            objectList.add("1");
            objectList.add("1");
            objectList.add("Template1");
            modeData.add(objectList);
        }
        else
        {
            if (map != null)
            {
                Map<Integer, String> treeMap = new TreeMap<>();
                for (String key : map.keySet())
                {
                    List<Integer> list = map.get(key);
                    if (!org.springframework.util.CollectionUtils.isEmpty(list))
                    {
                        for (Integer integer : list)
                        {
                            treeMap.put(integer,key);
                        }

                    }
                }
                for (Integer key : treeMap.keySet())
                {
                    List<String> objectList = new ArrayList<>();
                    objectList.add(String.valueOf(num));
                    num++;
                    objectList.add(String.valueOf(key));
                    objectList.add(treeMap.get(key));
                    modeData.add(objectList);
                }
            }
        }
        Sheet sheet = hssfWorkbook.createSheet(SHEET11_NAME);
        writerExcel(sheet,modeHeaderNameList,modeData);
    }


    /* Started by AICoder, pid:4c25cs0023n16db142190b95e098685ebb247795 */
    public UpDownloadFileDataBasePO getUpDownloadFileBean(File file, FormDataContentDisposition disp, String deviceId, UpDownloadFileParameterEntity bean, String currentTime) throws IOException, com.zte.uedm.common.exception.UedmException {
        try( InputStream fis = new BufferedInputStream(Files.newInputStream(file.toPath())))
        {
            File fileFromPath = classPathResource.getFile();
            // 文件解析为bean
            PeakShiftDeviceStrategyDetailBean detailBean = parseFileToBean(fileFromPath);
            // 更新设备文件版本
            setPeakShiftDeviceStrategyBean(deviceId, detailBean.getId(), new PeakShiftDeviceStrategyBean());
            // 新增错峰策略详情
            peakShiftDeviceFileRepository.insertPeakShiftDeviceStrategyDetail(detailBean);

            // 文件上传至ftp,文件信息存表
            return deviceUploadIntoDB(disp, fis, bean, currentTime);
        }catch (Exception e)
        {
            log.error("PeakShiftFileController getUpDownloadFileBean is error {}",e.getMessage());
            throw new com.zte.uedm.common.exception.UedmException(-200,e.getMessage());
        }
    }

    public UpDownloadFileDataBasePO deviceUploadIntoDB(FormDataContentDisposition disp, InputStream fis, UpDownloadFileParameterEntity bean,
                                                       String currentTime) throws com.zte.uedm.common.exception.UedmException, FtpClientException, UedmException {
        log.debug("bean param operator {}", bean.getOperator());
        log.debug("bean param file size{}", bean.getFileSize());
        log.debug("bean param file length {}", bean.getFileLength());
        log.debug("got currentTime {}", currentTime);
        InputStream currFormDataStream = fis;

        String fileSuffix = getFileSuffix(disp);
        String fileName = getFileName(disp);
        fileName = checkFileName(fileName);
        String fileId = UUID.randomUUID().toString();
        // 生成ftp路径：默认路径/年/月/日/文件id
        String parentPath = getRemotePath();
        log.info("deviceUploadIntoDB upload file {},{},{},{}", fileId, fileName, fileSuffix, parentPath);

        /* Started by AICoder, pid:6cda5qd3cbt571514f9708ee4046571e8cb14a7c */
        String filePath;
        // 文件上传至ftp
        try (FtpClient defaultFtpClient = ftpClientService.getDefaultFtpClient()) {
            defaultFtpClient.mkdir(parentPath);
            filePath = parentPath + File.separator + fileId + "." + fileSuffix;
            defaultFtpClient.putStream(currFormDataStream, filePath, false);
        } catch (Exception e) {
            log.error("deviceUploadIntoDB put file error", e);
            throw new UedmException(-1, "put file to ftp error");
        }
        /* Ended by AICoder, pid:6cda5qd3cbt571514f9708ee4046571e8cb14a7c */

        //文件信息存表
        UpDownloadFileDataBasePO upDownloadFileBean = new UpDownloadFileDataBasePO();
        upDownloadFileBean.setFileType(FILE_TYPE_UPLOAD);
        upDownloadFileBean.setFileSuffix(fileSuffix);
        upDownloadFileBean.setFilePath(filePath);
        upDownloadFileBean.setOriginalName(fileName);
        upDownloadFileBean.setName(fileName);
        upDownloadFileBean.setId(fileId);
        upDownloadFileBean.setGmtCreate(currentTime);
        upDownloadFileBean.setGmtModified(currentTime);
        upDownloadFileBean.setModule(FILE_MODULE_TYPE_BCUA);
        upDownloadFileBean.setOperator(bean.getOperator() == null ? DEFAULT_USER : bean.getOperator());
        upDownloadFileRepository.insertFile(upDownloadFileBean);

        return upDownloadFileBean;
    }
    /* Ended by AICoder, pid:4c25cs0023n16db142190b95e098685ebb247795 */
    public String getRemotePath() {
        String currentTime = dateTimeService.getCurrentTime();
        //年
        String year = currentTime.substring(0, 4);
        //月
        String mouth = currentTime.substring(5, 7);
        //日
        String day = currentTime.substring(8, 10);
        String relativePath = defaultFtpPath +  year + File.separator + mouth + File.separator + day;
        log.info("getRemotePath :{}", relativePath);
        return relativePath;
    }

    public String getFileName(FormDataContentDisposition formDataContentDisposition) throws com.zte.uedm.common.exception.UedmException
    {
        try
        {
            String originFileName = formDataContentDisposition.getFileName();
            byte[] bytes = originFileName.getBytes("ISO8859-1");
            String fileName = new String(bytes, UTF_8);
            return fileName;
        }
        catch (Exception e)
        {
            log.error("getFileName error", e);
            throw new com.zte.uedm.common.exception.UedmException(-200, e.getMessage());
        }
    }

    public String getFileSuffix(FormDataContentDisposition formDataContentDisposition)
            throws com.zte.uedm.common.exception.UedmException
    {
        try
        {
            String originFileName = formDataContentDisposition.getFileName();
            byte[] bytes = originFileName.getBytes("ISO8859-1");
            String fileName = new String(bytes, UTF_8);
            int indexOf = fileName.lastIndexOf(".");
            String fileSuffix = fileName.substring(indexOf + 1);
            return fileSuffix;
        }
        catch (Exception e)
        {
            log.error("getFileSuffix error", e);
            throw new com.zte.uedm.common.exception.UedmException(-200, e.getMessage());
        }
    }


    public void setPeakShiftDeviceStrategyBean(String deviceId, String detailBeanId, PeakShiftDeviceStrategyBean deviceStrategyBean) throws UedmException {
        Integer version = peakShiftDeviceFileRepository.selectMaxVersionByDeviceId(deviceId);
        String currentTime = dateTimeService.getCurrentTime();
        if (version != null) {
            peakShiftDeviceFileRepository.updatePeakShiftDeviceStrategyEndDate(deviceId, version, currentTime);
            deviceStrategyBean.setVersion(version + 1);
        } else {
            deviceStrategyBean.setVersion(0);
        }
        deviceStrategyBean.setId(UUID.randomUUID().toString());
        deviceStrategyBean.setDeviceId(deviceId);
        deviceStrategyBean.setIntervalStrategyId(detailBeanId);
        deviceStrategyBean.setStartDate(currentTime);
        deviceStrategyBean.setEndDate("9999-12-31 00:00:00");
        peakShiftDeviceFileRepository.insertPeakShiftDeviceStrategyBean(deviceStrategyBean);
    }

    /* Started by AICoder, pid:7ec5cufadbf35d11445b090941838c114e518c90 */
    public PeakShiftDeviceStrategyDetailBean parseFileToBean(File file) throws IOException {
        List<TemplateVO> templateVOList = new ArrayList<>();
        List<BasicTemplateVO> basicTemplate = new ArrayList<>();
        List<String> holidayVOS = new ArrayList<>();
        List<ModeTemplateDto> modeTemplateDtos = new ArrayList<>();
        List<PeakShiftDeviceTemplateBean> templateBeanList = new ArrayList<>();

        List<Object> basicTemplateObj = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 0, new BasicTemplateVO());
        List<Object> templateObj1 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 1, new TemplateVO());
        List<Object> templateObj2 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 2, new TemplateVO());
        List<Object> templateObj3 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 3, new TemplateVO());
        List<Object> templateObj4 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 4, new TemplateVO());
        List<Object> templateObj5 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 5, new TemplateVO());
        List<Object> templateObj6 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 6, new TemplateVO());
        List<Object> templateObj7 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 7, new TemplateVO());
        List<Object> templateObj8 = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 8, new TemplateVO());
        List<Object> holidayObjVOS = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 9, new HolidayFileVO());
        List<Object> modeTemplateObjVOS = bisToList(new BufferedInputStream(Files.newInputStream(file.toPath())), 10, new ModeTemplateVO());

        //对模板数据进行处理
        templateObj1.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(1);
            templateVOList.add(vo);
        });
        templateObj2.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(2);
            templateVOList.add(vo);
        });
        templateObj3.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(3);
            templateVOList.add(vo);
        });
        templateObj4.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(4);
            templateVOList.add(vo);
        });
        templateObj5.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(5);
            templateVOList.add(vo);
        });
        templateObj6.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(6);
            templateVOList.add(vo);
        });
        templateObj7.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(7);
            templateVOList.add(vo);
        });
        templateObj8.forEach(o -> {
            TemplateVO vo = (TemplateVO) o;
            vo.setNum(8);
            templateVOList.add(vo);
        });
        //对templateVo装换成需要的bean
        templateVOList.forEach(o -> {
            PeakShiftDeviceTemplateBean templateBean = new PeakShiftDeviceTemplateBean();
            templateBean.setNum(o.getNum());
            templateBean.setStartTime(replaceBeginTime(o.getStartTime()));
            templateBean.setEndTime(replaceEndTime(o.getEndTime()));
            templateBean.setMode(priceLevelMap.get(o.getPriceLevel()));
            templateBeanList.add(templateBean);
        });
        basicTemplateObj.forEach(o -> {
            BasicTemplateVO vo = (BasicTemplateVO) o;
            basicTemplate.add(vo);
        });
        holidayObjVOS.forEach(o -> {
            HolidayFileVO vo = (HolidayFileVO) o;
            //日期进行转换成00-00
            if (StringUtils.isNotBlank(vo.getHolidayStr())) {
                holidayVOS.add(replaceDate(vo.getHolidayStr()));
            }
        });
        modeTemplateObjVOS.forEach(o -> {
            ModeTemplateVO vo = (ModeTemplateVO) o;
            ModeTemplateDto dto = new ModeTemplateDto();
            dto.setNum(Integer.valueOf(vo.getDates()));
            dto.setTemplateNum(templateMap.get(vo.getTemplate()));
            modeTemplateDtos.add(dto);
        });
        //对读出来的数据进行转成bean进行保存操作
        PeakShiftDeviceStrategyDetailBean detailBean = new PeakShiftDeviceStrategyDetailBean();
        String detailBeanId = UUID.randomUUID().toString();
        Integer templateNum = 0;
        Integer peakShiftMode = modeMap
                .get(basicTemplate.stream().filter(p -> "Peak Shift Mode".equals(p.getName())).findAny().orElse(new BasicTemplateVO()).getValue());
        Integer holidayTemplate = templateMap
                .get(basicTemplate.stream().filter(p -> "Holiday Template".equals(p.getName())).findAny().orElse(new BasicTemplateVO()).getValue());
        String version = basicTemplate.stream().filter(p -> "Configuration Template Version".equals(p.getName())).findAny()
                .orElse(new BasicTemplateVO()).getValue();
        String holidayDateStr = "[]";
        String datesAndTemp = "[]";
        String templateDetailStr = "[]";
        try {
            holidayDateStr = jsonService.objectToJson(holidayVOS);
            datesAndTemp = jsonService.objectToJson(modeTemplateDtos);
            templateDetailStr = jsonService.objectToJson(templateBeanList);
            templateNum = Integer.valueOf(
                    basicTemplate.stream().filter(p -> "Number of Templates".equals(p.getName())).findAny().orElse(new BasicTemplateVO()).getValue());
        } catch (Exception e) {
            log.error("deal peak device file object to json failure", e);
        }
        detailBean.setId(detailBeanId);
        detailBean.setTemplateNum(templateNum);
        detailBean.setMode(peakShiftMode);
        detailBean.setVersion(version);
        detailBean.setHolidayMode(holidayTemplate);
        detailBean.setHolidayDateStr(holidayDateStr);
        detailBean.setDatesAndTemp(datesAndTemp);
        detailBean.setTemplateDetailStr(templateDetailStr);
        return detailBean;
    }
    /* Ended by AICoder, pid:7ec5cufadbf35d11445b090941838c114e518c90 */

    public String replaceEndTime2(String timeStr)
    {
        String[] strings = timeStr.split(":");
        //结束时间加1分钟
        Integer minute = Integer.valueOf(strings[1]) + 1;
        Integer hour = Integer.valueOf(strings[0]);
        if (minute.equals(60)) {

            minute = 0;
            hour += 1;
        }
        strings[0] = String.valueOf(hour);
        strings[1] = String.valueOf(minute);
        //一位的时候前面补一个0
        for (int i = 0; i < strings.length; i++)
        {
            strings[i] = String.format("%02d",Integer.valueOf(strings[i]));
        }
        return StringUtils.join(strings, ":");
    }

    public String replaceBeginTime2(String timeStr)
    {
        String[] strings = timeStr.split(":");
        //一位的时候前面补一个0
        for (int i = 0; i < strings.length; i++)
        {
            strings[i] = String.format("%02d", Integer.valueOf(strings[i]));
        }
        return StringUtils.join(strings, ":");
    }
    /**
     * 将1/1形势的日期转化成01-01
     *
     * @param dateStr
     * @return
     */
    /* Started by AICoder, pid:27008td311z15d31440b0a4120cb47013579cc98 */
    public String replaceDate(String dateStr) {
        String[] strings = dateStr.split("/");
        //一位的时候前面补一个0
        for (int i = 0; i < strings.length; i++) {
            String str = strings[i];
            if (str.length() == 1) {
                strings[i] = "0" + str;
            }
        }
        return StringUtils.join(strings, "-");
    }
    /* Ended by AICoder, pid:27008td311z15d31440b0a4120cb47013579cc98 */
    /* Started by AICoder, pid:w7008id311715d31440b0a4120cb47113572cc98 */
    public String replaceBeginTime(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return "00:00:00";
        }
        String[] strings = timeStr.split(":");
        //一位的时候前面补一个0
        for (int i = 0; i < strings.length; i++) {
            String str = strings[i];
            if (str.length() == 1) {
                strings[i] = "0" + str;
            }
        }
        return StringUtils.join(strings, ":") + ":00";
    }
    /* Ended by AICoder, pid:w7008id311715d31440b0a4120cb47113572cc98 */
    /* Started by AICoder, pid:c7008ld311g15d31440b0a4120cb47113572cc98 */
    public String replaceEndTime(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return "23:59:59";
        }
        String[] strings = timeStr.split(":");
        //一位的时候前面补一个0
        for (int i = 0; i < strings.length; i++) {
            String str = strings[i];
            if (str.length() == 1) {
                strings[i] = "0" + str;
            }
        }
        return StringUtils.join(strings, ":") + ":59";
    }
    /* Ended by AICoder, pid:c7008ld311g15d31440b0a4120cb47113572cc98 */

    /* Started by AICoder, pid:sd53aqdd39w6b671404c0a5da041334108d0b5ee */
    public List<Object> bisToList(BufferedInputStream bis, Integer sheetNo, Object obj) {
        try {
            List<Object> resList = new ArrayList<>();

            Class<?> cla = null;
            if (obj instanceof BasicTemplateVO) {
                cla = BasicTemplateVO.class;
            }
            else if (obj instanceof TemplateVO) {
                cla = TemplateVO.class;
            }
            else if (obj instanceof HolidayFileVO) {
                cla = HolidayFileVO.class;
            }
            else if (obj instanceof ModeTemplateVO) {
                cla = ModeTemplateVO.class;
            }

            if (cla != null) {
                EasyExcelFactory.read(bis, cla, new AnalysisEventListener<Object>() {
                    @Override
                    public void invoke(Object o, AnalysisContext analysisContext) {
                        resList.add(o);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // No operation needed
                    }
                }).sheet(sheetNo).doRead();
            }

            return resList;
        } finally {
            try {
                if (bis != null) {
                    bis.close();
                }
            } catch (Exception e) {
                log.warn("Resource close error", e);
            }
        }
    }
    /* Ended by AICoder, pid:sd53aqdd39w6b671404c0a5da041334108d0b5ee */

    /* Started by AICoder, pid:6b24dw45fd5371f1482a093f7072ee1d9984dd99 */
    public File dealPeakShiftFile(InputStream fis) throws IOException {
        //获取文件
        File file = classPathResource.getFile();

        byte[] b = new byte[1024];
        int len = 0;
        try (FileOutputStream fos = new FileOutputStream(file)) {
            check(fis);
            while ((len = fis.read(b)) != -1) {
                fos.write(b, 0, len);// 写入数据
            }
        }
        return file;
    }
    /* Ended by AICoder, pid:6b24dw45fd5371f1482a093f7072ee1d9984dd99 */

    private void check(Object object) {
        SecurityManager sm = System.getSecurityManager();
        if (sm != null) {
            sm.checkPermission(new NetPermission("setResponseCache"));
        }
    }

    public static String checkFileName(String fileName)
    {
        if(fileName.startsWith(" ") || fileName.contains(".."))
        {
            log.error("fileName is error fileName=" + fileName);
            fileName = "";
        }
        return fileName;
    }

    public String getDeviceByMacFromRedis(String mac)
    {
        if (StringUtils.isBlank(mac)) {
            log.error("mac is blank");
            return null;
        }

        try {
            return redisService.getCache(RedisConstants.REDIS_CACHE_NAME_MAC_TO_DEVICE, mac);
        } catch (com.zte.uedm.common.exception.UedmException e) {
            log.error("getDeviceByMacFromRedis is error!", e);
            return null;
        }
    }
}
