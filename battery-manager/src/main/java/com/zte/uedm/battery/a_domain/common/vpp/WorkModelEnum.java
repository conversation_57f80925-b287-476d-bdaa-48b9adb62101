package com.zte.uedm.battery.a_domain.common.vpp;


/* Started by AICoder, pid:z794911254307f41433309fc0035174ed462b82e */
public enum WorkModelEnum {

    BACKUP_ELECTRICITY("0", "备电", "Backup Electricity"),
    FREQUENCY_MODULATION("1", "调频", "Frequency Modulation"),
    OFF_PEAK("2", "错峰", "Peak Shifting");

    private String code;
    private String name;
    private String nameEn;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getNameEn() {
        return this.nameEn;
    }

    WorkModelEnum(String code, String name, String nameEn) {
        this.code = code;
        this.name = name;
        this.nameEn = nameEn;
    }

    public String getBattSourceTypeName() {
        return "{\"en_US\":\"" + this.getNameEn() + "\",\"zh_CN\":\"" + this.getName() + "\"}";
    }

    public static String getNameByCode(String code) {
        WorkModelEnum[] enums = WorkModelEnum.values();
        for (WorkModelEnum workModelEnum : enums) {
            if (workModelEnum.getCode().equals(code)) {
                return workModelEnum.getBattSourceTypeName();
            }
        }
        return "";
    }
}
/* Ended by AICoder, pid:z794911254307f41433309fc0035174ed462b82e */
