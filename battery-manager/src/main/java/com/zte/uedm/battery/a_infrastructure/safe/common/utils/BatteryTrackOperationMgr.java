package com.zte.uedm.battery.a_infrastructure.safe.common.utils;

import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;

import java.util.ArrayList;
import java.util.List;

public class BatteryTrackOperationMgr
{
    private static final String MONITOR_MANAGE_ZH = "监控管理";
    private static final String MONITOR_MANAGE_EN = "Monitor manage";
    private static final String BATT_UNLOCK_ZH = "电池解锁";
    private static final String BATT_UNLOCK_EN = "Battery unlock";
    private static final String BATT_PROTECTION_ZH = "电池设防";
    private static final String BATT_PROTECTION_EN = "Battery protection";
    private static final String BATT_REMOVE_PROTECTION_ZH = "电池撤防";
    private static final String BATT_REMOVE_PROTECTION_EN = "Removing battery protection";

    /**
     * 电池解锁/--操作初始化
     * @param serviceBean
     */
    public static void initBatteryTrackUnlockOperBean(ServiceBaseInfoBean serviceBean) {
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(serviceBean.getUserName(), serviceBean.getRemoteHost(), connectMode, MONITOR_MANAGE_ZH, MONITOR_MANAGE_EN,
                OperlogBean.LogRank.operlog_rank_veryimportant, BATT_UNLOCK_ZH, BATT_UNLOCK_EN);
        serviceBean.initOperlogBean(operlogBean);
    }

    /* Started by AICoder, pid:e2a0715701i91c71479908a0c07ea31407057c51 */
    /**
     *电池解锁/撤防/设防--操作成功
     * @param serviceBean
     * @param id
     */
    public static void refreshBatteryTrackSuccessDetail(ServiceBaseInfoBean serviceBean, String id) {
        OperlogBean operlogBean = serviceBean.getOperlogBean();
        if (operlogBean != null) {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            detailZh.append("电池id：").append(id);
            detailEn.append("battery id：").append(id);
            operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
        }
    }

    /* Ended by AICoder, pid:e2a0715701i91c71479908a0c07ea31407057c51 */
    /* Started by AICoder, pid:p2c7f93073ib74514ac90ac9c0369c135b85c7b6 */
    /**
     * 电池解锁/撤防/设防--操作成功
     * @param serviceBean 服务信息 bean，包含操作日志对象
     * @param ids 操作成功的电池ID列表
     */
    public static void refreshBatteryTrackListSuccessDetail(ServiceBaseInfoBean serviceBean, List<String> ids) {
        OperlogBean operlogBean = serviceBean.getOperlogBean();
        if (operlogBean != null) {
            StringBuilder detailZh = new StringBuilder();
            StringBuilder detailEn = new StringBuilder();
            detailZh.append("电池id：").append(ids);
            detailEn.append("battery id：").append(ids);
            operlogBean.refreshOperSuccess(detailZh.toString(), detailEn.toString(), new ArrayList<>());
        }
    }
    /* Ended by AICoder, pid:p2c7f93073ib74514ac90ac9c0369c135b85c7b6 */

    /**
     * 刷新日志信息
     * @param serviceBean
     * @param e
     */
    /* Started by AICoder, pid:c83f31f79a7d597145e608fa10be2511aae28bdd */
    public static void refreshBatteryTrackOperFailDetail(ServiceBaseInfoBean serviceBean, UedmException e) {
        if (serviceBean != null && e != null) {
            OperlogBean operlogBean = serviceBean.getOperlogBean();
            if (operlogBean != null) {
                StringBuilder detailZh = new StringBuilder();
                StringBuilder detailEn = new StringBuilder();
                detailZh.append(e.getMessage()).append("\n");
                detailEn.append(e.getMessage()).append("\n");
                operlogBean.refreshOperFail(detailZh.toString(), detailEn.toString(), new ArrayList<>());
            }
        }
    }
    /* Ended by AICoder, pid:c83f31f79a7d597145e608fa10be2511aae28bdd */

    public static void initBatteryTrackProtectOperBean(ServiceBaseInfoBean serviceBean)
    {
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(serviceBean.getUserName(), serviceBean.getRemoteHost(), connectMode,
                MONITOR_MANAGE_ZH, MONITOR_MANAGE_EN, OperlogBean.LogRank.operlog_rank_veryimportant, BATT_PROTECTION_ZH, BATT_PROTECTION_EN);
        serviceBean.initOperlogBean(operlogBean);
    }

    public static void initBatteryTrackTheftOperBean(ServiceBaseInfoBean serviceBean) {
        String connectMode = UserThreadLocal.getLoginType();
        OperlogBean operlogBean = new OperlogBean(serviceBean.getUserName(), serviceBean.getRemoteHost(), connectMode,
                MONITOR_MANAGE_ZH, MONITOR_MANAGE_EN, OperlogBean.LogRank.operlog_rank_veryimportant, BATT_REMOVE_PROTECTION_ZH, BATT_REMOVE_PROTECTION_EN);
        serviceBean.initOperlogBean(operlogBean);
    }

}
