package com.zte.uedm.battery.a_infrastructure.repository.vpp.converter;

import com.zte.uedm.battery.a_domain.aggregate.vpp.model.entity.FMOverviewEntity;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/* Started by AICoder, pid:g333fe66d12960a14fbc09c0c092d1071e0945bb */
@Mapper
public interface FMOverviewPoConverter {

    FMOverviewPoConverter INSTANCE = Mappers.getMapper(FMOverviewPoConverter.class);

    List<FMOverviewEntity> listFMOverviewPoToFMOverviewEntity(List<FMOverviewPo> poList);

    FMOverviewEntity fMOverviewPoToFMOverviewEntity(FMOverviewPo fmOverviewPo);
}
/* Ended by AICoder, pid:g333fe66d12960a14fbc09c0c092d1071e0945bb */
