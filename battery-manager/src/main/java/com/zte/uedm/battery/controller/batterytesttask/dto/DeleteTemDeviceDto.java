package com.zte.uedm.battery.controller.batterytesttask.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(description = "删除临时设备")
public class DeleteTemDeviceDto
{
    @NotBlank(message = "taskId ban not be blank")
    @ApiModelProperty(value = "任务标识")
    private String taskId;
    @NotEmpty(message = "deviceIds ban not be empty")
    @ApiModelProperty(value = "设备标识集")
    private List<String> deviceIds;

}
