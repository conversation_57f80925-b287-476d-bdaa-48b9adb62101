package com.zte.uedm.battery.export.manage.entity;

import com.zte.uedm.battery.util.SecurityCheckUtils;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;

import java.io.Closeable;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import static com.zte.uedm.battery.consts.CommonConst.MASTER_DIRECTORY;

/**
 * base64转图片工具类
 */
@Slf4j
public class Base64Convert {
    public final static String BASE64_JPG_HEAD ="data:image/jpeg;base64,";
    public final static String BASE64_PNG_HEAD ="data:image/png;base64,";

    /**
     * @Description： base64字符串转化成图片
     * @param: imgStr
     * @Return:
     */
    public static boolean generateImage(String imgStr, String photoname) {
        //对字节数组字符串进行Base64解码并生成图片
        //图像数据为空
        if (imgStr == null) {
            return false;
        }
        BASE64Decoder decoder = new BASE64Decoder();
        OutputStream out = null;
        try {
            String base64Data = imgStr;
            if (imgStr.startsWith(BASE64_JPG_HEAD) || imgStr.startsWith(BASE64_PNG_HEAD)) {
                base64Data = imgStr.split(",")[1];
            }
            //Base64解码
            byte[] bytes = decoder.decodeBuffer(base64Data);
            for (int i = 0; i < bytes.length; ++i) {
                if (bytes[i] < 0) {
                    //调整异常数据
                    bytes[i] += 256;
                }
            }
            //新生成的图片
            String imgFilePath = MASTER_DIRECTORY + photoname;
            out = new FileOutputStream(imgFilePath);
            SecurityCheckUtils.checkPerm();
            out.write(bytes);
            out.flush();
            return true;
        } catch (Exception e) {
            log.error("", e);
            return false;
        } finally {
            try {
                close(out);
            } catch (IOException e) {
                log.error("", e);
            }
        }
    }

    public static void close(Closeable c) throws IOException {
        if (c != null) {
            c.close();
        }
    }
}
