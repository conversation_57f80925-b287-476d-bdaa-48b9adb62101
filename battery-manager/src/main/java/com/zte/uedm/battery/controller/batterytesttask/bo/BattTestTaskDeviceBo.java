package com.zte.uedm.battery.controller.batterytesttask.bo;

import com.zte.uedm.battery.bean.pojo.BattTestTaskDevicesPo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Optional;

@Setter
@Getter
@ToString
@Accessors(chain = true)
public class BattTestTaskDeviceBo
{
    /**
     * 设备id
     */
    private String id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 需要重试
     */
    private Boolean needRetry;
    /**
     * 重试原因
     */
    private String retryReason;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date gmtModified;

    public BattTestTaskDeviceBo(){}

    public BattTestTaskDeviceBo(BattTestTaskDevicesPo po)
    {
        Optional.ofNullable(po).ifPresent(item->{
            this.setId(item.getId())
                    .setTaskId(item.getTaskId())
                    .setNeedRetry(item.isNeedRetry())
                    .setRetryReason(item.getRetryReason())
                    .setCreator(item.getCreator())
                    .setGmtCreate(item.getGmtCreate())
                    .setUpdater(item.getUpdater())
                    .setGmtModified(item.getGmtModified());
        });
    }
}
