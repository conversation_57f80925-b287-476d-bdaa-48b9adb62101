package com.zte.uedm.battery.bean.pv;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @ Author     ：10260977
 * @ Date       ：19:02 2021/3/2
 * @ Description：太阳能异常记录
 * @ Modified By：
 * @ Version: 1.0
 */
@Setter
@Getter
@ToString
public class PvExceptionRecordBean
{
    private String id;

    private String pvId;

    private String alarmType;

    private String generateDate;

    private String endDate;
    /**
     * 异常状态 0-未结束；1-告警恢复；2-发电量恢复正常；3-人工清除
     */
    private String state;

    private String gmtCreate;

    private String gmtModified;


}
