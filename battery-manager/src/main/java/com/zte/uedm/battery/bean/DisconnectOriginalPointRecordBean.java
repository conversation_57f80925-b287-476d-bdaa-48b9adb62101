package com.zte.uedm.battery.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class DisconnectOriginalPointRecordBean
{
    /**
     * id，唯一键
     */
    private String id;
    /**
     * 生效开始时间
     */
    private Date startTime;
    /**
     * 生效结束时间
     */
    private Date endTime;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 原始测点id
     */
    private String ompId;
    /**
     * 原始测点index
     */
    private String ompIndex;
    /**
     * 原始测点value
     */
    private String ompValue;
    /**
     * 创建者
     */
    private String creator;
    /**
     *更新者
     */
    private String updater;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 构建原始测点历史记录Map的key
     * @param disconnectOriginalPiontRecordBeans
     * @return
     */
    public String buildRecordMapKey(DisconnectOriginalPointRecordBean disconnectOriginalPiontRecordBeans)
    {
        return disconnectOriginalPiontRecordBeans.getDeviceId() + disconnectOriginalPiontRecordBeans.getOmpId() + disconnectOriginalPiontRecordBeans.getOmpIndex();
    }

}
