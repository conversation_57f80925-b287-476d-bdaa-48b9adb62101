package com.zte.uedm.battery.enums.batttest;

import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public enum BatterySetOverviewDimsEnums {

    NAME("name", "{\"zh_CN\":\"名称\",\"en_US\":\"Name\"}", "2",1, true, true, null, true),
    POSITION("position", "{\"zh_CN\":\"位置\",\"en_US\":\"Position\"}", "2",2, true, true, null, true),
    TEST_STATUS("testStatus", "{\"zh_CN\":\"测试状态\",\"en_US\":\"Test Status\"}", "2",3, true, false, null, false),
    DELIVERY_TIME("deliveryTime", "{\"zh_CN\":\"下发时间\",\"en_US\":\"Delivery Time\"}", "2",4, true, false, null, true),
    TEST_START_TIME("testStartTime", "{\"zh_CN\":\"测试开始时间\",\"en_US\":\"Test Start Time\"}", "2",5, true, false, null, true),
    DURATION("duration", "{\"zh_CN\":\"测试持续时长\",\"en_US\":\"Duration\"}", "2",6, true, false, null, true),
    TEST_TYPE("testType", "{\"zh_CN\":\"测试类型\",\"en_US\":\"Test Type\"}", "2",7, true, false, null, false),
    BACKUP_POWER_STATUS("backupPowerStatus", "{\"zh_CN\":\"测试后备电状态\",\"en_US\":\"Power Standby Status after Test\"}", "2",8, false, false, null, false),
    PRE_BACKUP_POWER_STATUS("preBackupPowerStatus", "{\"zh_CN\":\"测试前备电状态\",\"en_US\":\"Power Standby Status before Test\"}", "2",9, false, false, null, false),
    HEALTH_STATUS("healthStatus", "{\"zh_CN\":\"测试后健康状态\",\"en_US\":\"Health Status after Test\"}", "2",10, false, false, null, false),
    PRE_HEALTH_STATUS("preHealthStatus", "{\"zh_CN\":\"测试前健康状态\",\"en_US\":\"Health Status before Test\"}", "2",11, false, false, null, false),
    SITE_LEVEL("siteLevel", "{\"zh_CN\":\"站点等级\",\"en_US\":\"Site Level\"}", "2",12, false, false, null, false),
    POWER_SUPPLY_SCENE("powerSupplyScene", "{\"zh_CN\":\"供电场景\",\"en_US\":\"Power Supply Scene\"}", "2",13, false, false, null, false);

    /**
     * id
     */
    private String id;

    /**
     * 中英文名称
     */
    private String name;
    /**
     * 测试设备类型
     */
    private String scene;

    /**
     * 默认顺序
     */
    private Integer defaultIndex;

    /**
     * 默认是否启动
     */
    private Boolean defaultEnable;

    /**
     * 是否固定
     */
    private Boolean defaultFixed;

    /**
     * 单位
     */
    private String unit;

    /**
     * 是否排序
     */
    private Boolean sortable;

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }
    public String getScene() {return scene;}

    public Integer getDefaultIndex() {
        return defaultIndex;
    }

    public Boolean getDefaultEnable() {
        return defaultEnable;
    }

    public Boolean getDefaultFixed() {
        return defaultFixed;
    }

    public String getUnit() {
        return unit;
    }

    public Boolean getSortable() {
        return sortable;
    }

    public static List<String> getIdsWithIdName() {
        List<String> ids = new ArrayList<>();
        ids.add(BatterySetOverviewDimsEnums.TEST_TYPE.getId());
        ids.add(BatterySetOverviewDimsEnums.PRE_HEALTH_STATUS.getId());
        ids.add(BatterySetOverviewDimsEnums.PRE_BACKUP_POWER_STATUS.getId());
        ids.add(BatterySetOverviewDimsEnums.HEALTH_STATUS.getId());
        ids.add(BatterySetOverviewDimsEnums.BACKUP_POWER_STATUS.getId());
        ids.add(BatterySetOverviewDimsEnums.TEST_STATUS.getId());
        ids.add(BatterySetOverviewDimsEnums.POWER_SUPPLY_SCENE.getId());
        ids.add(BatterySetOverviewDimsEnums.SITE_LEVEL.getId());
        ids.add(BatterySetOverviewDimsEnums.DURATION.getId());
        return ids;
    }

    BatterySetOverviewDimsEnums(String id, String name, String scene, Integer defaultIndex, Boolean defaultEnable,
                                Boolean defaultFixed, String unit, Boolean sortable) {
        this.id = id;
        this.name = name;
        this.scene = scene;
        this.defaultIndex = defaultIndex;
        this.defaultEnable = defaultEnable;
        this.defaultFixed = defaultFixed;
        this.unit = unit;
        this.sortable = sortable;
    }

    private static Map<String, BatterySetOverviewDimsEnums> map;

    static {
        map = Arrays.stream(BatterySetOverviewDimsEnums.values())
                .collect(Collectors.toMap(BatterySetOverviewDimsEnums::getId, a -> a));
    }

    /**
     * 根据id获取名称
     */
    public static String getNameById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(BatterySetOverviewDimsEnums::getName)
                .orElse(null);
    }

    /**
     * 根据id获取默认顺序
     */
    public static Integer getDefaultIndexById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(BatterySetOverviewDimsEnums::getDefaultIndex)
                .orElse(0);
    }

    /**
     * 根据id获取是否启用默认值
     */
    public static boolean getDefaultEnableById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(BatterySetOverviewDimsEnums::getDefaultEnable)
                .orElse(Boolean.FALSE);
    }

    /**
     * 根据id获取是否固定值
     */
    public static boolean getDefaultFixedById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(BatterySetOverviewDimsEnums::getDefaultFixed)
                .orElse(Boolean.FALSE);
    }

    /**
     * 根据id获取是否分类
     */
    public static boolean getSortableById(String id) {
        return Optional.ofNullable(map.get(id))
                .map(BatterySetOverviewDimsEnums::getSortable)
                .orElse(Boolean.FALSE);
    }

    /**
     * 获取所有id
     */
    public static List<String> getAllIds() {
        return Arrays.stream(BatterySetOverviewDimsEnums.values())
                .map(BatterySetOverviewDimsEnums::getId)
                .collect(Collectors.toList());
    }

    /**
     * id校验
     */
    public static void checkIdIsNeeded(List<String> ids) throws UedmException {
        if (CollectionUtils.isEmpty(ids)) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim ids is empty!");
            throw new UedmException(-200, "Dim ids is empty!");
        }
        ids.removeAll(BatterySetOverviewDimsEnums.getAllIds());
        if (ids.size() > 0) {
            log.error("BatteryTestHistoryServiceImpl updateUserRecordIndexDim dim id beyond limit!");
            throw new UedmException(-302, "Dim id beyond limit");
        }
    }

    public static Map<String, BatterySetOverviewDimsEnums> getAllCannotModifyEnums() {
        BatterySetOverviewDimsEnums[] values = BatterySetOverviewDimsEnums.values();
        return Arrays.stream(values)
                .filter(bean -> !bean.getDefaultFixed()).filter(bean -> StringUtils.isNotBlank(bean.getId())).
                collect(Collectors.toMap(BatterySetOverviewDimsEnums::getId, a -> a));
    }

    /**
     * 根据id获取单位
     * @param id
     * @return
     */
    public static String getUnitById(String id)
    {
        if(StringUtils.isBlank(id))
        {
            return null;
        }

        BatterySetOverviewDimsEnums[] values = BatterySetOverviewDimsEnums.values();
        for(BatterySetOverviewDimsEnums value : values)
        {
            if(value.id.equals(id))
            {
                return value.getUnit();
            }
        }
        return null;
    }
}
