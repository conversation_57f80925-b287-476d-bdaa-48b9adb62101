package com.zte.uedm.battery.pv.service.impl;


import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.bean.RemoteControlBean;
import com.zte.uedm.battery.pv.service.PvGenerationStorageIntervalService;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.battery.schedule.AutoPeakShiftStrategySchduleJob;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.KafkaTopicConstants;
import com.zte.uedm.common.bean.SouthExtraDataBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.configuration.point.bean.OriginalDataBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.redis.service.RedisService;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants.AUTO_PEAK_SHIFT_STRATEGY_TIME;
import static com.zte.uedm.battery.util.constant.PvConstant.HISTORY_ENERGY_STORAGE_INTERVAL;

/**
 * <AUTHOR>
 * @description 太阳能发电量保存间隔设置，补采场景
 * @date 2023/9/6
 **/
@Service
@Slf4j
@Setter
@RefreshScope
public class PvGenerationStorageIntervalServiceImpl implements PvGenerationStorageIntervalService {
    private static String OMPID = "800200280501";
    private static String HISTORY_DATA_STORAGE_INTERVAL_KEY = "value";
    private static String DEFAULT_OPERATOR = "scheduleJob";
    private static int MP_MANAGER_QUERY_QUANTITY = 500;
    public final static String REDIS_KEY_ORIGINAL_DATA_INDEX = "ordi:";
    public final static String REDIS_KEY_ORIGINAL_DATA = "original-data:";

    @Autowired
    private JsonService jsonService;

    @Value("${uedm.schedule.pv_energy_storage_interval}")
    private String defaultInterval;

    @Autowired
    private ConfigService configService;

    @Autowired
    private MessageSenderService msgSenderService;

    @Resource
    private RedisService redisService;
    @Autowired
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;
    @Autowired
    private MpRpcImpl mpRpcImpl;

    @Override
    public int setPvGenerationStorageInterval() throws UedmException {
        log.info("setPvGenerationStorageInterval begin");
        int dealQuantity = 0;
        //List<MonitorDeviceBaseBean> monitorDeviceList = configurationManagerRpcImpl.getRelatedMonitorDevicesByMoc(PV_MOC, null, null, LANGUAGE_OPTION_CN);
        List<MonitorDeviceBaseBean> monitorDeviceList = realGroupRelationSiteUtils.getRelatedMonitorDevicesByMoc(MocOptional.PV.getId());
        if (CollectionUtils.isEmpty(monitorDeviceList)) {
            log.info("setPvGenerationStorageInterval no pv related monitor devices.");
            return dealQuantity;
        }
        log.debug("setPvGenerationStorageInterval-debug the pv related monitor devices is {}.",monitorDeviceList);
        log.info("setPvGenerationStorageInterval the pv related monitor devices size {},will process:{} each time.",monitorDeviceList.size(),MP_MANAGER_QUERY_QUANTITY);
        //分批处理，一次500
        for (int i = 0; i < monitorDeviceList.size(); i += MP_MANAGER_QUERY_QUANTITY) {
            int end = (Math.min((i + MP_MANAGER_QUERY_QUANTITY), monitorDeviceList.size()));
            List<MonitorDeviceBaseBean> monitorDeviceListB = monitorDeviceList.subList(i, end);
            dealQuantity += batchSetStorageInterval(monitorDeviceListB);
        }
        log.info("setPvGenerationStorageInterval end，the processed device quantity:{}", dealQuantity);
        return dealQuantity;
    }

    private int batchSetStorageInterval(List<MonitorDeviceBaseBean> monitorDeviceList) throws UedmException {
        //获取需要修改的监控设备id
        log.info("batchSetStorageInterval begin");
        List<String> needModifiedIds = getNeedModifyDeviceIds(monitorDeviceList);
        log.info("batchSetStorageInterval this time needModifiedIds is {}",needModifiedIds);
        List<MonitorDeviceBaseBean> monitorDeviceListF = filterByIds(monitorDeviceList, needModifiedIds);
        if (CollectionUtils.isEmpty(monitorDeviceListF)) {
            return 0;
        }
        sendMsgToDevice(monitorDeviceListF);
        log.info("batchSetStorageInterval end,processed device quantity:{}",monitorDeviceListF.size());
        return monitorDeviceListF.size();
    }

    @Override
    public void sendMsgToDevice(List<MonitorDeviceBaseBean> list) throws UedmException {
        log.info("sendMsgToDevice begin.");
        for (MonitorDeviceBaseBean bean : list) {
            Map<String, Object> map = jsonService.jsonToObject(jsonService.objectToJson(bean), Map.class);
            SouthExtraDataBean extraData = new SouthExtraDataBean();
            extraData.setUserName(DEFAULT_OPERATOR);
            RemoteControlBean remoteControlBean = new RemoteControlBean();
            remoteControlBean.setOmpId(OMPID);
            remoteControlBean.setValue(getDefaultInterval());
            remoteControlBean.setRelationPosition("1");
            remoteControlBean.setPointIndex("1");
            remoteControlBean.setDevice(map);
            log.info("MonitorDeviceBaseBean:{}", map);
            msgSenderService.sendMsgAsync(KafkaTopicConstants.KAFKA_TOPIC_SOUTH_FRAMEWORK_REMOTE_CONTROL,
                    jsonService.objectToJson(Collections.singletonList(remoteControlBean)));
        }
        log.info("sendMsgToDevice end");
    }

    public List<MonitorDeviceBaseBean> filterByIds(List<MonitorDeviceBaseBean> list, List<String> needModifiedIds) {
        List<MonitorDeviceBaseBean> monitorDeviceListF;
        if (!CollectionUtils.isEmpty(needModifiedIds)) {
            monitorDeviceListF = list.stream().filter(item -> needModifiedIds.contains(item.getId())).collect(Collectors.toList());
        } else {
            monitorDeviceListF = new ArrayList<>();
        }
        return monitorDeviceListF;
    }

    public List<String> getNeedModifyDeviceIds(List<MonitorDeviceBaseBean> monitorDeviceList) throws UedmException {
        List<String> deviceIds = new ArrayList<>();
//        Map<String, Map<String, List<Map<String, String>>>> originalDataMap = new HashMap<>(16);
        Map<String, Map<String, Map<String, AdapterPointDataVo>>> originalDataMap = new HashMap<>(16);
        //查询监控设备的原始值
        if (!CollectionUtils.isEmpty(monitorDeviceList)) {
            List<String> deviceIdList = monitorDeviceList.stream().map(ResourceBaseBean::getId).collect(Collectors.toList());
            try {
                originalDataMap = mpRpcImpl.getOriginalDataByCollectList(deviceIdList, Arrays.asList(OMPID));

            } catch (UedmException e) {
                log.error("getNeedModifyDeviceIds query redis error!", e);
            }
        }
        if (originalDataMap.isEmpty()) {
            log.info("device data is empty!");
            return deviceIds;
        }
        Map<String, Map<String, Map<String, AdapterPointDataVo>>> finalOriginalDataMap = originalDataMap;
        deviceIds = monitorDeviceList.stream().map(bean -> getNeedModifiedItems(finalOriginalDataMap, bean)).collect(Collectors.toList());
        deviceIds.removeAll(Collections.singleton(null));
        return deviceIds;
    }

    @Nullable
    public String getNeedModifiedItems(Map<String, Map<String, Map<String, AdapterPointDataVo>>> originalDataMap, MonitorDeviceBaseBean bean) {
        String item = null;
        Map<String, Map<String, AdapterPointDataVo>> mpMap = originalDataMap.get(bean.getId());
        if (null != mpMap) {
            Map<String, AdapterPointDataVo> originalValueMap = mpMap.get(OMPID);
            if (null != originalValueMap && !originalValueMap.isEmpty()) {
//                Map<String, String> mapTmp = originalDataList.get(0);
                if (checkIfNeedModify(originalValueMap)) {
                    item = bean.getId();
                }
            }
        }
        return item;
    }

    private boolean checkIfNeedModify(Map<String, AdapterPointDataVo> originalValueMap) {
        boolean rtn = true;
        //获取到了value的值，且值与配置中心不符,才需要设置
        for (String index : originalValueMap.keySet()) {
            AdapterPointDataVo adapterPointDataVo = originalValueMap.get(index);
            if (adapterPointDataVo != null && StringUtils.equals(adapterPointDataVo.getValue(), getDefaultInterval())) {
                return false;
            }
        }
/*        if (null != originalValueMap &&
                !StringUtils.equals(originalValueMap.get(HISTORY_DATA_STORAGE_INTERVAL_KEY), defaultInterval)) {
            rtn = true;
        }*/
        return rtn;
    }

    @Override
    public Map<String, Map<String, List<Object>>> queryOriginPointDataByCondition(List<String> mdIds, List<String> ompIds) throws UedmException {
        Map<String, Map<String, List<Object>>> listMap = new HashMap<>(16);
        //记录设备对应测点的索引
        List<Set<Object>> ompIdIndexs = new ArrayList<>();
        //获取所有设备，所有原始测点的索引
        List<Map<String, Set<String>>> mdOmpDataIndexs = redisService.getOmpDataIndex(REDIS_KEY_ORIGINAL_DATA_INDEX, mdIds, ompIds);
        if (mdOmpDataIndexs == null || mdOmpDataIndexs.isEmpty()) {
            for (String mdId : mdIds) {
                Map<String, List<Object>> mdMap = new HashMap<>();
                listMap.put(mdId, mdMap);
            }
            return listMap;
        }
        for (Map<String, Set<String>> allDevice : mdOmpDataIndexs) {
            //获取某个设备对应测点的索引
            Set<Object> ompIdIndex = new HashSet<>();
            for (Map.Entry<String, Set<String>> oneDevice : allDevice.entrySet()) {
                ompIdIndex.addAll(oneDevice.getValue());
            }
            ompIdIndexs.add(ompIdIndex);
        }
        log.info("getByCondition mdOmpDataIndexs data {},{},{}", mdIds, ompIds, mdOmpDataIndexs);
        //获取所有设备，所有带索引的原始测点数据
        List<Map<String, Object>> allMdOmpData = redisService.getOmpData(REDIS_KEY_ORIGINAL_DATA, mdIds, ompIdIndexs);
        log.info("getByCondition allDeviceData {},{},{}", mdIds, ompIdIndexs, allMdOmpData);
        getResultListMap(mdIds, ompIds, allMdOmpData, listMap);
        return listMap;
    }

    private void getResultListMap(List<String> mdIds, List<String> ompIds, List<Map<String, Object>> allMdOmpData,
                                  Map<String, Map<String, List<Object>>> listMap) throws UedmException {
        for (int i = 0, len = mdIds.size(); i < len; i++) {
            String mdId = mdIds.get(i);
            //某个设备中所有带索引的原始测点数据
            Map<String, Object> oneMdData = allMdOmpData.get(i);
            Map<String, List<Object>> mdMap = new HashMap<>(16);
            for (Map.Entry<String, Object> ompData : oneMdData.entrySet()) {
                //某个带索引的原始测点数据
                Object value = ompData.getValue();
                OriginalDataBean dataItem = jsonService.jsonToObject((String) value ,OriginalDataBean.class);
                List<Object> ompDataList = mdMap.computeIfAbsent(dataItem.getOmpId(), k -> new ArrayList<>());
                if (ompIds.contains(dataItem.getOmpId())) {
                    ompDataList.add(dataItem);
                }
            }
            listMap.put(mdId, mdMap);
        }
    }

    private String getDefaultInterval() {
        return Optional.ofNullable(configService.getGlobalProperty(HISTORY_ENERGY_STORAGE_INTERVAL)).orElse(defaultInterval);
    }

}
