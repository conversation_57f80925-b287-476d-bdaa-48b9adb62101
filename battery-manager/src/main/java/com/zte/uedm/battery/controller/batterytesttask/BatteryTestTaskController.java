package com.zte.uedm.battery.controller.batterytesttask;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.BatteryTestRetryVo;
import com.zte.uedm.battery.controller.batterytesttask.dto.*;
import com.zte.uedm.battery.controller.batterytesttask.vo.*;
import com.zte.uedm.battery.service.BatteryTestTaskService;
import com.zte.uedm.battery.util.ResponseBeanUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.common.util.ValidationResult;
import com.zte.uedm.common.util.ValidationUtils;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.function.sm.exception.AuthorityException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.text.ParseException;
import java.util.*;

@Path("battery-test-task")
@Component
@Slf4j
//@Api(value = "battery-test-task")
@Api(value = "电池测试任务")
public class BatteryTestTaskController
{
    @Autowired
    private JsonService jsonService;
    @Autowired
    private BatteryTestTaskService batteryTestTaskService;
    @Autowired
    private MessageSenderService msgSenderService;

    private static final String PARAM_IS_BLANK = "param is blank.";

    /**
     * 任务状态列表-查询
     * @param request
     * @param languageOption
     * @return
     */
    @GET
    @Path("/status-level/select-alls")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务状态列表-查询", notes = "任务状态列表-查询", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功")
    })
    public ResponseBean selectAllStatusLevels(@Context HttpServletRequest request,@HeaderParam("language-option") String languageOption)
    {
        log.info("BatteryTestTaskController->start to select  status level.");
        int total = 0;
        List<IdNameBean> data = null;
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            data =  batteryTestTaskService.selectAllStatusLevels(serviceBean);
            total=data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestTaskController-> select  status level fail.", e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    @DELETE
    @Path("/delete-by-ids")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "删除任务", notes = "删除任务", httpMethod = HttpMethod.DELETE)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -1,message = "发生异常"),
    })
    public ResponseBean deleteTask(List<String> ids, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
        BatteryTeskTaskOperationMgr.initTaskDeleteOperBean(serviceBean);  // 初始化日志内容

        try
        {
            if(CollectionUtils.isEmpty(ids))
            {
                log.error("BatteryTestTaskController->deleteTask, param is blank.");
                return ResponseBeanUtils.getRemoteResponseBean(-301, PARAM_IS_BLANK);
            }

            Integer num = batteryTestTaskService.deleteTasks(ids, serviceBean);

            return  ResponseBeanUtils.getNormalResponseBean(0, null, num);

        } catch (UedmException e) {
            log.error("BatteryTestTaskController->deleteTask occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally {
            // 发送操作记录日志
            String operMsg = JSON.toJSONString(serviceBean.getOperlogBean());
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    @PUT
    @Path("/edit")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "编辑任务内容", notes = "编辑任务内容", httpMethod = HttpMethod.PUT)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -200,message = "数据查询失败"),
            @ApiResponse(code = -202,message = "任务状态不支持更新"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -302,message = "名称重复"),
            @ApiResponse(code = -303,message = "参数长度越界"),
            @ApiResponse(code = -304,message = "参数不在范围"),
            @ApiResponse(code = -3012,message = "任务在系统中不存"),
            @ApiResponse(code = -3013,message = "数据被其他任务占用"),
    })
    public ResponseBean editTask(TaskEditRequestDto dto, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
        BatteryTeskTaskOperationMgr.initTaskEditOperBean(serviceBean);  // 初始化日志内容

        try
        {
            Optional.ofNullable(dto).orElseThrow(()->new UedmException(-301, PARAM_IS_BLANK));
            Pair<Boolean, List<String>> checkBlankRes = dto.checkBlank();
            if(!checkBlankRes.getLeft())
            {
                log.error("BatteryTestTaskController->editTask, param is blank. dto={}", dto);
                return ResponseBeanUtils.getCheckParamResponseBean(-301, JSON.toJSONString(checkBlankRes.getRight()), PARAM_IS_BLANK);
            }

            Pair<Boolean, List<String>> lengthCheckRes = dto.checkLengthLimit();
            if(!lengthCheckRes.getLeft())
            {
                log.error("BatteryTestTaskController->editTask,length check is fail.");
                return ResponseBeanUtils.getCheckParamResponseBean(-303, JSON.toJSONString(lengthCheckRes.getRight()), "param length exceed limit.");
            }

            if(!dto.checkPeriodRange())
            {
                log.error("BatteryTestTaskController->editTask,check period range is fail.");
                return ResponseBeanUtils.getRemoteResponseBean(-304, "param exceed allowd range");
            }
            Pair<String, Integer> data = batteryTestTaskService.editTask(dto, serviceBean);

            return  ResponseBeanUtils.getNormalResponseBean(0, data.getLeft(), data.getRight());

        } catch (UedmException e) {
            log.error("BatteryTestTaskController->editTaskStatus occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally {
            // 发送操作记录日志
            String operMsg = JSON.toJSONString(serviceBean.getOperlogBean());
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    @PUT
    @Path("/status/edit")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "转换任务状态", notes = "转换任务状态", httpMethod = HttpMethod.PUT)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -202,message = "任务状态不支持更新"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -1,message = "操作数据库发生异常"),
            @ApiResponse(code = -3042,message = "状态覆盖不符合规则"),
            @ApiResponse(code = -204,message = "任务标识在系统中不存"),
            @ApiResponse(code = -3041,message = "状态超过范围")
    })
    public ResponseBean editTaskStatus(TaskStatusEditRequestDto dto, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
        BatteryTeskTaskOperationMgr.initStatusEditOperBean(serviceBean);  // 初始化日志内容

        try
        {
            if(dto == null || !dto.checkBlank())
            {
                log.error("BatteryTestTaskController->editTaskStatus, param is blank. dto={}", dto);
                return ResponseBeanUtils.getRemoteResponseBean(-301, PARAM_IS_BLANK);
            }

            Pair<Boolean, Set<String>> statusInRangePair = dto.checkStatusInRange();
            if(!statusInRangePair.getLeft())
            {
                log.error("BatteryTestTaskController->editTaskStatus, status exceed allowed range.");
                return ResponseBeanUtils.getCheckParamResponseBean(-3041, JSON.toJSONString(statusInRangePair.getRight()), "status exceed allowd range.");
            }

            String id = batteryTestTaskService.editTaskStatus(dto, serviceBean);

            return  ResponseBeanUtils.getNormalResponseBean(0, id, 1);

        } catch (UedmException e) {
            log.error("BatteryTestTaskController->editTaskStatus occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
        finally {
            // 发送操作记录日志
            String operMsg = JSON.toJSONString(serviceBean.getOperlogBean());
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }

    @POST
    @Path("/select-by-condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "周期性计划任务-查询", notes = "周期性计划任务-查询", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -401,message = "数据查询失败")
    })
    public ResponseBean selectTestTaskByCondition(TaskQueryRequestDto taskStatusQueryRequestDto, @Context HttpServletRequest request,@HeaderParam("language-option") String languageOption)
    {
        try
        {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            Pair<Integer, List<BattTestTaskVo>> dataPair = batteryTestTaskService
                    .selectTestTaskByCondition(taskStatusQueryRequestDto, serviceBean);
            int total = dataPair.getLeft();
            return ResponseBeanUtils.getNormalResponseBean(0, dataPair.getRight(), total);
        }
        catch (UedmException e)
        {
            log.error("BatteryTestTaskController selectTestTaskByCondition error,", e);
            return ResponseBeanUtils.getRemoteResponseBean(e.getErrorId(),e.getErrorData(), e.getErrorDesc(),"",0);
        } catch (Exception e) {
            log.error("BatteryTestTaskController selectTestTaskByCondition error,", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "BatteryTestTaskController selectTestTaskByCondition error");
        }
    }

    /**
     * 周期性计划任务-编辑详情
     * @param id
     * @param request
     * @param languageOption
     * @return
     */
    @GET
    @Path("/edit-detail/select-by-id")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = " 周期性计划任务-编辑详情", notes = " 周期性计划任务-编辑详情", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -200,message = "查询标识发生异常"),
    })
    public ResponseBean selectEditDetailById(@QueryParam("id") String id, @Context HttpServletRequest request,@HeaderParam("language-option") String languageOption)
    {
        if (StringUtils.isEmpty(id))
        {
            log.error("BatteryTestTaskController->selectEditDetailById id is empty");
            return ResponseBeanUtils.getNormalResponseBean(-301, "", "fail", null, 0);
        }
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            BattTestTaskVo result = batteryTestTaskService.selectById(id,serviceBean);
            return  ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        } catch (UedmException e) {
            log.error("BatteryTestTaskController->selectEditDetailById occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    /**
     * 周期性计划任务-展示详情
     * @param id
     * @param request
     * @param languageOption
     * @return
     */
    @GET
    @Path("/show-detail/select-by-id")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = " 周期性计划任务-展示详情", notes = " 周期性计划任务-展示详情", httpMethod = HttpMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -1,message = "操作数据库发生异常"),
    })
    public ResponseBean selectShowDetailById(@QueryParam("id") String id, @Context HttpServletRequest request,@HeaderParam("language-option") String languageOption)
    {
        if (StringUtils.isEmpty(id))
        {
            log.error("BatteryTestTaskController->selectShowDetailById id is empty");
            return ResponseBeanUtils.getNormalResponseBean(-301, "", "fail", null, 0);
        }
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            BattTestTaskVo result = batteryTestTaskService.selectShowDetailById(id,serviceBean);
            return  ResponseBeanUtils.getNormalResponseBean(0, result, 1);
        } catch (UedmException e) {
            log.error("BatteryTestTaskController->selectShowDetailById occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getResponseBeanByUedmException(e);
        }
    }

    /**
     * 重试
     * @param request
     * @param languageOption
     * @return
     */
    @POST
    @Path("/retry")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "重试", notes = "重试", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -1,message = "远程调用发生异常"),
            @ApiResponse(code = -207,message = "远程控制失败"),
    })
    public ResponseBean retryBatteryTest(BatteryTestRetryVo vo, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        log.info("BatteryTestTaskController->start to retry battery test.");
        try {
            String userName = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
            List<String> data =  batteryTestTaskService.retryBatteryTest(vo, serviceBean);
            int total=data.size();
            return ResponseBeanUtils.getNormalResponseBean(0, data, total);
        }
        catch (Exception e)
        {
            log.error("BatteryTestTaskController-> retry battery test fail.", e);
            return ResponseBeanUtils.getRemoteResponseBean(-1,"retry battery test fail");
        }
    }

    @POST
    @Path("/add")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "增加周期性任务", notes = "增加周期性任务", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -200,message = "添加任务发生异常"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -302,message = "任务名称重复"),
            @ApiResponse(code = -303,message = "参数过长"),
            @ApiResponse(code = -304,message = "参数超出范围"),
            @ApiResponse(code = -3011,message = "任务没和设备有关联"),
    })
    public ResponseBean addTask(TaskEditRequestDto dto, @Context HttpServletRequest request, @HeaderParam("language-option") String languageOption)
    {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption);
        // 初始化日志内容
        BatteryTeskTaskOperationMgr.initTaskAddOperBean(serviceBean);

        try
        {
            Optional.ofNullable(dto).orElseThrow(()->new UedmException(-301, PARAM_IS_BLANK));
            Pair<Boolean, List<String>> checkBlankRes = dto.checkBlank();
            if(!checkBlankRes.getLeft())
            {
                log.error("BatteryTestTaskController->addTask, param is blank. dto={}", dto);
                return ResponseBeanUtils.getCheckParamResponseBean(-301, JSON.toJSONString(checkBlankRes.getRight()), PARAM_IS_BLANK);
            }

            Pair<Boolean, List<String>> lengthCheckRes = dto.checkLengthLimit();
            if(!lengthCheckRes.getLeft())
            {
                log.error("BatteryTestTaskController->addTask,length check is fail.");
                return ResponseBeanUtils.getCheckParamResponseBean(-303, JSON.toJSONString(lengthCheckRes.getRight()), "param length exceed limit.");
            }

            if(!dto.checkPeriodRange())
            {
                log.error("BatteryTestTaskController->addTask,check period range is fail.");
                return ResponseBeanUtils.getRemoteResponseBean(-304, "param exceed allowd range");
            }
            Integer integer = batteryTestTaskService.addTask(dto, serviceBean);

            return  ResponseBeanUtils.getNormalResponseBean(0, "success", integer);

        } catch (UedmException e)
        {
            log.error("BatteryTestTaskController addTask occur exception {}", e.getMessage(), e);
            return ResponseBeanUtils.getRemoteResponseBean(e.getErrorId(), e.getErrorData(),e.getErrorDesc(), "",0);
        }
        finally
        {
            // 发送操作记录日志
            String operMsg = JSON.toJSONString(serviceBean.getOperlogBean());
            msgSenderService.sendMsgAsync(OperlogBean.IMOP_LOG_MANAGE_TOPIC, operMsg);
        }
    }
    /**
     *  设备筛选 - 查询
     * @param deviceSelectedDto
     * @param pageNo
     * @param pageSize
     * @param languageOption
     * @param request
     * @return
     */
    @POST
    @Path("/device-selected/select-by-condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "设备筛选 - 查询", notes = "设备筛选 - 查询", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -200,message = "操作数据库发生异常"),
            @ApiResponse(code = -301,message = "参数为空"),
            @ApiResponse(code = -100,message = "任务标识或者服务为空"),
            @ApiResponse(code = -304,message = "参数超出范围")
    })
    public ResponseBean selectByCondition(DeviceSelectedDto deviceSelectedDto,@QueryParam("pageNo")Integer pageNo, @QueryParam("pageSize")Integer pageSize,
                                          @HeaderParam("language-option")String languageOption,
                                          @Context HttpServletRequest request)
    {
        int total = 0;
        DeviceSelectedVo data = null;
        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(deviceSelectedDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("selectByCondition, the parameter blankSets is blank. {}", blankSets);
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            Pair<Boolean, Map<String, List<String>>> checkResult = deviceSelectedDto.checkParams();
            if (!checkResult.getLeft())
            {
                log.warn("selectByCondition, the parameter is blank. {}");
                String errorMsg = jsonService.objectToJson(checkResult.getRight());
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, "Parameter is not in the range of optional values", null, 0);
            }

            String username = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            log.info("BatteryTestTaskController -> start to selectByCondition deviceSelectedDto:{}",deviceSelectedDto);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(username,ip,languageOption, pageNo,pageSize);
            // service层
            data = batteryTestTaskService.selectByCondition(deviceSelectedDto, serviceBaseInfoBean);
            if(data.getTotal() != null){
                total = data.getTotal();
            }else {
                total = 0;
            }

        }catch (Exception e)
        {
            log.error("BatteryTestTaskController -> selectByCondition  is  fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,data,total);
    }


    /**
     * 任务关联设备测试情况查询
     * @param detailDto
     * @param languageOption
     * @param request
     * @return
     */
    @POST
    @Path("/device-detail/select-by-id")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务关联设备测试情况查询", notes = "任务关联设备测试情况查询", httpMethod = HttpMethod.POST)
    @ApiResponses({
            @ApiResponse(code = 0,message = "成功"),
            @ApiResponse(code = -301,message = "参数为空或超出范围"),
            @ApiResponse(code = -304,message = "排序关键字和顺序参数超出范围"),
            @ApiResponse(code = -1,message = "数据库操作异常"),
            @ApiResponse(code = -200,message = "操作数据库发生异常"),
            @ApiResponse(code = -100,message = "任务标识或者服务为空"),
    })
    public ResponseBean selectById(DeviceDetailDto detailDto,@HeaderParam("language-option")String languageOption,
                                   @Context HttpServletRequest request)
    {
        int total = 0;
        GetTaskRelationDeviceVo data= null;
        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(detailDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("selectById, the parameter blankSets is blank. {}", blankSets);
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            Pair<Boolean, Map<String, List<String>>> checkResult = detailDto.checkParams();
            if (!checkResult.getLeft())
            {
                log.warn("selectById, the parameter is blank. {}");
                String errorMsg = jsonService.objectToJson(checkResult.getRight());
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, "Parameter is not in the range of optional values", null, 0);
            }
            Pair<Boolean, Map<String, Set<String>>> booleanMapPair = detailDto.checkOrderSort();
            if(!booleanMapPair.getLeft())
            {
                Map<String, Set<String>> rangeMap = booleanMapPair.getRight();
                String rangeMapMsg = jsonService.objectToJson(rangeMap);
                return ResponseBeanUtils.getNormalResponseBean(-304,rangeMapMsg,"sort or order is not in the range of optional values" , null, 0);
            }

            String username = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            log.info("BatteryTestTaskController -> start to selectByCondition detailDto:{}",detailDto);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(username,ip,languageOption, detailDto.getPageNo(),detailDto.getPageSize());
            // service层
            Pair<GetTaskRelationDeviceVo, Integer> resultPair = batteryTestTaskService.selectByIdAndStatus(detailDto, serviceBaseInfoBean);
            data=resultPair.getLeft();
            total =resultPair.getRight();

        }catch (UedmException | ParseException e)
        {
            log.error("BatteryTestTaskController -> selectById  is  fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,data,total);
    }

    /**
     * 任务的关联临时设备列表 - 查询
     * @param taskId
     * @param pageNo
     * @param pageSize
     * @param languageOption
     * @param request
     * @return
     */
    @GET
    @Path("/tem-device/select-by-taskid")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务的关联临时设备列表 - 查询", notes = "任务的关联临时设备列表 - 查询", httpMethod = HttpMethod.GET)
    public ResponseBean selectTemDeviceByTaskid(@QueryParam("taskId") String taskId,@QueryParam("pageNo")Integer pageNo,
                         @QueryParam("pageSize")Integer pageSize, @HeaderParam("language-option")String languageOption,
                                   @Context HttpServletRequest request)
    {
        int total = 0;
        List<TemDeviceSelectVo> data = null;
        try
        {
            if (StringUtils.isBlank(taskId))
            {
                log.warn("selectTemDeviceByTaskid, the parameter taskId is blank. {}", taskId);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        null, PARAM_IS_BLANK, null, 0);
            }

            String username = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            log.info("BatteryTestTaskController -> start to selectByCondition taskId:{}",taskId);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(username,ip,languageOption, pageNo,pageSize);
            // service层
            PageInfo<TemDeviceSelectVo> deviceDetailVoPageInfo = batteryTestTaskService.selectTemByTaskId(taskId, serviceBaseInfoBean);
            data=deviceDetailVoPageInfo.getList();
            total = (int)deviceDetailVoPageInfo.getTotal();

        }catch (UedmException e)
        {
            log.error("BatteryTestTaskController -> selectTemDeviceByTaskid  is  fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        } catch (AuthorityException e) {
            log.error("BatteryTestTaskController -> selectTemDeviceByTaskid  is  fail.",e);
            return ResponseBeanUtils.getRemoteResponseBean(e.getErrorId(), e.getErrorDesc());
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,data,total);
    }

    /**
     * 任务的关联临时设备列表 - 删除
     * @param deleteTemDeviceDto
     * @param languageOption
     * @param request
     * @return
     */
    @DELETE
    @Path("/tem-device")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务的关联临时设备列表 - 删除", notes = "任务的关联临时设备列表 - 删除", httpMethod = HttpMethod.DELETE)
    public ResponseBean deleteTemDevice(DeleteTemDeviceDto deleteTemDeviceDto, @HeaderParam("language-option")String languageOption,
                                        @Context HttpServletRequest request)
    {
        int total = 0;
        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(deleteTemDeviceDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("deleteTemDevice, the parameter blankSets is blank. {}", blankSets);
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            String userName = Tools.getUserName(request);
            String host = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption);
            total= batteryTestTaskService.deleteTemDevice(deleteTemDeviceDto,serviceBaseInfoBean);
        }catch (UedmException e)
        {
            log.error("BatteryTestTaskController -> deleteTemDevice  is  fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,null,total);
    }



    /**
     * 任务的关联临时设备列表 - 保存
     * @param saveDto
     * @param languageOption
     * @param request
     * @return
     */
    @POST
    @Path("/tem-device/save")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务的关联临时设备列表 - 保存", notes = "任务的关联临时设备列表 - 保存", httpMethod = HttpMethod.POST)
    public ResponseBean saveTemDevice(DeleteTemDeviceSaveDto saveDto, @HeaderParam("language-option")String languageOption,
                                      @Context HttpServletRequest request)
    {
        int total = 0;
        String userName = Tools.getUserName(request);
        String host = Tools.getRemoteHost(request);

        try
        {
            Set<String> blankSets = new HashSet<>();
            ValidationResult validationResult = ValidationUtils.validateForDefalut(saveDto);
            if (validationResult.isHasErrors())
            {
                blankSets.addAll(validationResult.getPropertyErrMsg().keySet());
            }
            if (!CollectionUtils.isEmpty(blankSets))
            {
                log.warn("saveTemDevice, the parameter blankSets is blank. {}", blankSets);
                String errorMsg = jsonService.objectToJson(blankSets);
                return ResponseBeanUtils.getNormalResponseBean(-301,
                        errorMsg, PARAM_IS_BLANK, null, 0);
            }
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean(userName, host, languageOption);
            total= batteryTestTaskService.saveTemDevice(saveDto,baseInfoBean);
        }catch (UedmException e)
        {
            log.error("BatteryTestTaskController -> deleteTemDevice  is  fail.",e);
            return ResponseBeanUtils.getResponseBeanByException(e);
        }
        return ResponseBeanUtils.getNormalResponseBean(0,null,null,null,total);
    }

    @POST
    @Path("/devices/search")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "任务设备查询", notes = "任务设备查询", httpMethod = HttpMethod.POST)
    public ResponseBean searchDevicesByName(DeviceSearchDto deviceSearchDto, @HeaderParam("language-option")String languageOption,
            @Context HttpServletRequest request)
    {

        try
        {
            deviceSearchDto.checkNameLimit();
            String username = Tools.getUserName(request);
            String ip = Tools.getRemoteHost(request);
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean(username,ip,languageOption, deviceSearchDto.getPageNo(),deviceSearchDto.getPageSize());
            Pair<List<DeviceSearchDetailVo>, Integer> searchDevices = batteryTestTaskService
                    .searchDevicesByCondition(deviceSearchDto, serviceBaseInfoBean);
            return ResponseBeanUtils.getNormalResponseBean(0,searchDevices.getLeft(),searchDevices.getRight());

        }catch (UedmException e)
        {
            log.error("BatteryTestTaskController searchDevicesByName  is  fail.",e);
            return ResponseBeanUtils.getRemoteResponseBean(e.getErrorId(), e.getErrorData(), e.getErrorDesc(),"", 0);
        } catch (Exception e) {
            log.error("BatteryTestTaskController searchDevicesByName  is  fail.",e);
            return ResponseBeanUtils.getRemoteResponseBean(-1, "BatteryTestTaskController searchDevicesByName  is  fail.");
        }

    }

    @POST
    @Path("/statistics")
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "自动测试统计查询", notes = "自动测试统计查询", httpMethod = HttpMethod.POST, tags = {"电池放电测试/自动测试统计查询"})
    public ResponseBean selectBattTestTaskStatistics(BattTestTaskStatisticDto battTestTaskStatisticDto, @HeaderParam("language-option")String languageOption,
                                            @Context HttpServletRequest request)
    {
        String userName = Tools.getUserName(request);
        String ip = Tools.getRemoteHost(request);
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean(userName, ip, languageOption, battTestTaskStatisticDto.getPageNo(), battTestTaskStatisticDto.getPageSize());

        try
        {
            //参数空校验
            if (battTestTaskStatisticDto.checkBlank())
            {
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, PARAM_IS_BLANK);
            }
            //参数可选值校验
            Pair<Boolean, Map<String, List<String>>> checkResult = battTestTaskStatisticDto.checkParam();
            if (!checkResult.getLeft())
            {
                return ResponseBeanUtils.getRemoteResponseBean(UedmErrorCodeConstants.PARAMETER_EXCEED_ALLOWD_RANGE, JSON.toJSONString(checkResult.getRight()));
            }

            Map<String, BattTestTaskStatisticVo> res = batteryTestTaskService
                    .selectBattTestTaskStatistics(battTestTaskStatisticDto, serviceBean);
            return ResponseBeanUtils.getNormalResponseBean(0, res, res.size());

        }catch (UedmException e)
        {
            log.error("BatteryTestTaskController searchDevicesByName  is  fail.",e);
            return ResponseBeanUtils.getRemoteResponseBean(e.getErrorId(), e.getErrorData(), e.getErrorDesc(),"", 0);
        }

    }
}
