package com.zte.uedm.battery.a_domain.aggregate.device.model.entity;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Map;

@Getter
@Setter
public class DeviceDSEntity extends DeviceEntity {
    private static final Logger log = LoggerFactory.getLogger(DeviceDSEntity.class);

    //装机容量属性（额定功率）
    private static final String RATED_POWER="rated_power";

    private String logicGroupId;
    //额定功率
    private BigDecimal powerRating;
    public static DeviceDSEntity convertEntityToBean(DeviceEntity entity)
    {
        DeviceDSEntity bean = new DeviceDSEntity();
        BeanUtils.copyProperties(entity, bean);
        return bean;
    }

    /**
     *装机容量
     * @param
     * @return
     * @throws UedmException
     */
    public void buildPowerRating() throws UedmException {
        String capacity ="";
         Object exattribute = getExattribute();
        if(null!= exattribute){
            log.debug("getPowerRating s:{}",exattribute.toString());
            Map<String,Object> map  = JsonUtils.jsonToObject(JsonUtils.objectToJson(exattribute),Map.class);
            capacity = (String)map.get(RATED_POWER);
        }
        log.debug("getPowerRating capacity:{}",capacity);
         this.setPowerRating(StringUtils.isNotBlank(capacity)?new BigDecimal(capacity):BigDecimal.ZERO);
    }
}
