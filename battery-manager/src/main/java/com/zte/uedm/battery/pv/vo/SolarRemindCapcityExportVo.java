package com.zte.uedm.battery.pv.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@ToString
@Api("风险历史导出")
@Slf4j
/* Started by AICoder, pid:4101e617fa8549ec9d3d5a93b48f847c */
public class SolarRemindCapcityExportVo {
    @ApiModelProperty(value = "逻辑位置标识")
    private String logicGroupId;
    @ApiModelProperty(value = "站点名称名称")
    private String name;
    @ApiModelProperty(value = "已配置")
    private Boolean flag=true;
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "位置")
    private String position;

    public Boolean isPage(){
        if (pageNo != null){
            return true;
        }
        return false;
    }


}
/* Ended by AICoder, pid:4101e617fa8549ec9d3d5a93b48f847c */
