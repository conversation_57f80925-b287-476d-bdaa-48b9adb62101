package com.zte.uedm.battery.bean;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

/**
 * 错峰用电策略电池历史数据Bean
 */
@Setter
@Getter
@ToString
@Slf4j
public class ImageBean implements Comparable<ImageBean>, Serializable
{

	/**
	 * 图片数据（base64格式）
	 */
	@ApiModelProperty(value = "base64图片字符串")
	private String base64Str;

	/**
	 *  图片名称
	 */
	@ApiModelProperty(value = "图片名称")
	private String imageName;

	/**
	 *  横坐标位置
	 */
	@ApiModelProperty(value = "图片的x坐标")
	private Integer xLine;

	/**
	 *  纵坐标位置
	 */
	@ApiModelProperty(value = "图片的y坐标")
	private Integer yLine;

	/**
	 *  维度（资产/工况）
	 */
	private String dim;

	@Override
	public int compareTo(@NotNull ImageBean o) {
		// 首先根据y坐标排序
		log.info("ImageBean compareTo o: {}",o.toString());
		int sort = this.getYLine() - o.getYLine();
		// 返回值0代表相等，1表示大于，-1表示小于；
		if (sort==0){
			// 再根据x坐标排序
			return this.getXLine()-o.getXLine();
		}
		return sort;
	}
}
