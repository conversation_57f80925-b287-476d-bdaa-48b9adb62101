package ft.schedule;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.impl.AssetDomainImpl;
import com.zte.uedm.battery.domain.impl.BattTypeDomainImpl;
import com.zte.uedm.battery.domain.impl.MoHistoryDataQueryDomainImpl;
import com.zte.uedm.battery.opti.application.scheduler.BattRiskCalculateScheduler;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.AssetRpc;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.impl.AiForecastServiceRpcImpl;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.schedule.BattHealthStatusEvalSchduleJob;
import com.zte.uedm.battery.service.BattHealthEvalJobService;
import com.zte.uedm.battery.service.BattHealthStatusEvaluateService;
import com.zte.uedm.battery.service.SystemConfigService;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.battery.service.impl.*;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.battery.util.BatteryHealthyBeanUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.redis.service.RedisService;
import ft.fake.rpc.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

public class BattHealthStatusEvalSchduleJobFTest {
    @InjectMocks
    private BattHealthStatusEvalSchduleJob battHealthStatusEvalSchduleJob;
    @Resource
    private BattHealthEvalJobService battHealthEvalService = new BattHealthEvalJobServiceImpl();
    @Mock
    private BattRiskCalculateScheduler battRiskCalculateScheduler;
    @Resource
    private BattTypeDomain battTypeDomain = new BattTypeDomainImpl();
    @Mock
    private ConfigurationManagerRpcImpl cfgRpc = new ConfigurationManagerRpcImplFake();
    @Resource
    private AssetDomain assetDomain = new AssetDomainImpl();
    @Mock
    private DataRedis dataRedis;

    @Mock
    private AssetRpc assetRpc;
    @Resource
    private JsonService jsonService = new JsonService();
    @Resource
    private I18nUtils i18nUtils = new I18nUtils();
    @Mock
    private ConfigurationRpc configurationRpcs;
    @Resource
    private BatteryAttributeUtils batteryAttributeUtils = new BatteryAttributeUtils();

    @Mock
    private BattAlarmDomain battAlarmDomain = new BattAlarmDomainFake();

    @Mock
    private BattConfigurationDomain battConfigurationDomain = new BattConfigurationDomainFake();

    @Mock
    private MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Resource
    private MoHistoryDataQueryDomain moHistoryDataQueryDomain = new MoHistoryDataQueryDomainImpl();

    @Resource
    private DateTimeService dateTimeService = new DateTimeService();

    @Mock
    private AssetRpcImpl assetRpcImpl = new AssetRpcImplFake();

    @Mock
    private BatteryHisDataDomain batteryHisDataDomain = new BatteryHisDataDomainFake();
    @Resource
    private BatteryHealthyBeanUtils batteryHealthyBeanUtils = new BatteryHealthyBeanUtils();
    @Mock
    private AiForecastServiceRpcImpl aiForecastServiceRpc;
    @Mock
    private BattAISwitchService battAISwitchService = new battAISwitchServiceFake();
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc = new ConfigurationManagerRpcImplFake();
    @Resource
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain = new BattHealthStatusEvalDomainFake();


    @Resource
    private Map<String, BattHealthStatusEvaluateService> hs = new HashMap<>();
    @Resource(name = "Li-ion")
    private BattHealthStatusEvaluateService battHealthStatusEvaluateService = new BattLfpHealthStatusEvaluateServiceImpl();
    @Resource
    private BattLeadAcidHealthStatusEvaluateServiceImpl battLeadAcidHealthStatusEvaluateService = new BattLeadAcidHealthStatusEvaluateServiceImpl();
    @Mock
    private RedisService redisService;
    @Mock
    private SystemConfigService configService;
    @Mock
    private CommonCacheService cacheService;
    @Mock
    private BattSohDomain battSohDomain;
    @Mock
    private BattOverviewDomain battOverviewDomain;


    @Before
    public void setup() throws ClassNotFoundException, IllegalAccessException, InstantiationException, UedmException, NoSuchFieldException {
        FtMockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:o47ea23f89f4a46143660ac0b086bb220ce6051a */

    @Test
    public void UEDM_454702_given_定时任务正常执行_when_获取电池类型为空_then_不进行电池健康评估() throws UedmException {
        ConfigurationManagerRpcImplFake cfgRpc1 = (ConfigurationManagerRpcImplFake) cfgRpc;
        cfgRpc1.branch = 1;
        battHealthStatusEvalSchduleJob.execute();
        verify(battOverviewDomain, never()).updateOverviewEvalResult(any());
    }

    @Test
    public void UEDM_454696_given_定时任务正常执行_when_电池缓存为空_then_不进行电池健康评估() throws UedmException {
        BattConfigurationDomainFake battConfigurationDomain1 = (BattConfigurationDomainFake) battConfigurationDomain;
        battConfigurationDomain1.branch = 1;
        battHealthStatusEvalSchduleJob.execute();
        verify(battOverviewDomain, never()).updateOverviewEvalResult(any());
    }

    @Test
    public void UEDM_454698_given_定时任务正常执行_when_电池类型无对应的评估规则_then_不进行电池健康评估() throws UedmException {
        BattHealthEvalJobServiceImpl battHealthEvalService1 = (BattHealthEvalJobServiceImpl) battHealthEvalService;
        hs.put("Na-ion", battHealthStatusEvaluateService);
        battHealthEvalService1.setHs(hs);
        battHealthStatusEvalSchduleJob.execute();
        verify(battOverviewDomain, never()).updateOverviewEvalResult(any());
    }

    /* Ended by AICoder, pid:o47ea23f89f4a46143660ac0b086bb220ce6051a */

    /* Started by AICoder, pid:df22bd4c17f4e3314f65098b6050a021c31347a7 */
    @Test
    public void UEDM_454700_given_定时任务正常执行_when_电池不存在健康程度的测点_then_不进行电池健康评估()
            throws UedmException, NoSuchFieldException, IllegalAccessException {

        // 获取服务类的Class对象
        Class<BattLfpHealthStatusEvaluateServiceImpl> aClass = (Class<BattLfpHealthStatusEvaluateServiceImpl>) battHealthStatusEvaluateService.getClass();

        // 获取并设置battHealthStatusEvalDomain字段的值
        Field battHealthStatusEvalDomainField = aClass.getDeclaredField("battHealthStatusEvalDomain");
        battHealthStatusEvalDomainField.setAccessible(true);
        battHealthStatusEvalDomainField.set(battHealthStatusEvaluateService, battHealthStatusEvalDomain);

        // 将battHealthStatusEvaluateService放入hs映射中
        BattHealthEvalJobServiceImpl battHealthEvalService1 = (BattHealthEvalJobServiceImpl) battHealthEvalService;
        hs.put("Li-ion", battHealthStatusEvaluateService);
        battHealthEvalService1.setHs(hs);

        // 执行调度任务
        battHealthStatusEvalSchduleJob.execute();

        // 验证updateOverviewEvalResult方法从未被调用
        Mockito.verify(battOverviewDomain, Mockito.never()).updateOverviewEvalResult(Mockito.any());
    }

    /* Ended by AICoder, pid:df22bd4c17f4e3314f65098b6050a021c31347a7 */

    @Test
    public void UEDM_454694_given_定时任务正常执行_when_电池评估所需数据正常_then_正常进行电池健康评估() throws UedmException, NoSuchFieldException, IllegalAccessException {
        /* Started by AICoder, pid:k95ebie203a295914b3508d710aae255ff608430 */
        Class<BattLfpHealthStatusEvaluateServiceImpl> aClass = (Class<BattLfpHealthStatusEvaluateServiceImpl>) battHealthStatusEvaluateService.getClass();
        Field battHealthStatusEvalDomainField = aClass.getDeclaredField("battHealthStatusEvalDomain");
        battHealthStatusEvalDomainField.setAccessible(true);
        battHealthStatusEvalDomainField.set(battHealthStatusEvaluateService, battHealthStatusEvalDomain);
        Field batteryHealthyBeanUtilsField = aClass.getDeclaredField("batteryHealthyBeanUtils");
        batteryHealthyBeanUtilsField.setAccessible(true);
        batteryHealthyBeanUtilsField.set(battHealthStatusEvaluateService, batteryHealthyBeanUtils);
        Field battLeadAcidHealthStatusEvaluateServiceField = aClass.getDeclaredField("battLeadAcidHealthStatusEvaluateService");
        battLeadAcidHealthStatusEvaluateServiceField.setAccessible(true);
        battLeadAcidHealthStatusEvaluateServiceField.set(battHealthStatusEvaluateService, battLeadAcidHealthStatusEvaluateService);
        Field jsonServiceField = aClass.getDeclaredField("jsonService");
        jsonServiceField.setAccessible(true);
        jsonServiceField.set(battHealthStatusEvaluateService, jsonService);
        Field battOverviewDomainField = aClass.getDeclaredField("battOverviewDomain");
        battOverviewDomainField.setAccessible(true);
        battOverviewDomainField.set(battHealthStatusEvaluateService, battOverviewDomain);

        BattHealthEvalJobServiceImpl battHealthEvalService1 = (BattHealthEvalJobServiceImpl) battHealthEvalService;
        hs.put("Li-ion", battHealthStatusEvaluateService);
        battHealthEvalService1.setHs(hs);

        BatteryHisDataDomainFake batteryHisDataDomain1 = (BatteryHisDataDomainFake) batteryHisDataDomain;
        batteryHisDataDomain1.branch = 1;

        battHealthStatusEvalSchduleJob.execute();
        Mockito.verify(battOverviewDomain, Mockito.atLeastOnce()).updateOverviewEvalResult(Mockito.any());

        /* Ended by AICoder, pid:k95ebie203a295914b3508d710aae255ff608430 */
    }
}
