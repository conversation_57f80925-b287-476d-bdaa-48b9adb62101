package ft;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.bean.BattRiskEvalBean;
import com.zte.uedm.battery.mapper.BattRiskRuleMapperFake;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleRepository;
import com.zte.uedm.battery.opti.infrastructure.repository.persistence.BattRiskRuleRepositoryImpl;
import com.zte.uedm.battery.rpc.AssetRpcFake;
import com.zte.uedm.battery.util.BatteryRiskUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class BatteryRiskFTest {
    @InjectMocks
    private BatteryRiskUtils batteryRiskUtils;
    @Resource
    private BattRiskRuleRepository battRiskRuleRepository = new BattRiskRuleRepositoryImpl();
    @Resource
    private BattRiskRuleMapperFake battRiskRuleMapper = new BattRiskRuleMapperFake();
    @Resource
    private AssetRpcFake assetRpc = new AssetRpcFake();
    @Mock
    private JsonService jsonService = new JsonService();




    @Before
    public void setup() throws ClassNotFoundException, IllegalAccessException, InstantiationException, UedmException {
        FtMockitoAnnotations.initMocks(this);


    }
    @SneakyThrows
    // @Test
    public void UEDM_282536_given_请求参数合法_风险项隐藏_在维保期范围内_when_调用电池总览接口_then_页面不展示风险项(){
        //
        //given
        battRiskRuleMapper.setDisplay("");
         assetRpc.setStatus("2");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("rule01");
        bean.setBattId("1");
        bean.setRiskLevel("level_1");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        //then
        Assert.assertEquals("normal",riskEvalBeans.get(0).getRiskLevel());
        //




    }
    @SneakyThrows
    // @Test
    public void UEDM_282532_given_请求参数合法_风险项隐藏_不在维保期范围内_when_调用电池总览接口_then_页面展示风险项(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("1");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_1");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        //then
        Assert.assertEquals("level_1",riskEvalBeans.get(0).getRiskLevel());
        //

    }
    @SneakyThrows
    // @Test
    public void UEDM_282535_given_请求参数合法_风险项显示_when_调用电池总览接口_then_页面展示风险项(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("1");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        //then
        Assert.assertEquals("level_2",riskEvalBeans.get(0).getRiskLevel());


        //

    }
    @SneakyThrows
    // @Test
    public void UEDM_282530_given_请求参数合法_风险项隐藏_在维保期范围内_when_调用总维度电池数量接口_then_不计入电池风险总数(){
        //given
        battRiskRuleMapper.setDisplay("yes");
        assetRpc.setStatus("2");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
         riskEvalBeans = riskEvalBeans.stream().filter(b -> "normal".equals(b.getRiskLevel())).collect(Collectors.toList());
        //then
        Assert.assertEquals(0,riskEvalBeans.size());


        //


    }
    @SneakyThrows
    // @Test
    public void UEDM_282533_given_请求参数合法_风险项隐藏_不在维保期范围内_when_调用总维度电池数量接口_then_计入电池风险总数(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        //then
        Assert.assertEquals(1,riskEvalBeans.size());

    }
    @SneakyThrows
    // @Test
    public void UEDM_282529_given_请求参数合法_风险项显示_when_调用总维度电池数量接口_then_计入电池风险总数(){
        //given
        battRiskRuleMapper.setDisplay("yes");
        assetRpc.setStatus("");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        //then
        Assert.assertEquals(1,riskEvalBeans.size());

    }
    @SneakyThrows
    // @Test
    public void UEDM_282531_given_请求参数合法_风险项隐藏_在维保期范围内_when_调用风险扇比接口_then_不计入电池风险等级数量(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("2");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        riskEvalBeans = riskEvalBeans.stream().filter(b -> !"normal".equals(b.getRiskLevel())).collect(Collectors.toList());

        //then
       Assert.assertEquals(1,riskEvalBeans.size());

    }
    @SneakyThrows
    // @Test
    public void UEDM_282537_given_请求参数合法_风险项隐藏_不在维保期范围内_when_调用风险扇比接口_then_计入电池风险等级数量(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("1");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);
        riskEvalBeans = riskEvalBeans.stream().filter(b -> !"normal".equals(b.getRiskLevel())).collect(Collectors.toList());

        //then
        Assert.assertEquals(1,riskEvalBeans.size());
    }
    @SneakyThrows
    // @Test
    public void UEDM_282534_given_请求参数合法_风险项显示_when_调用风险扇比接口接口_then_计入电池风险等级数量(){
        //given
        battRiskRuleMapper.setDisplay("");
        assetRpc.setStatus("3");

        //when
        List<BattRiskEvalBean> riskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("1");
        bean.setRiskLevel("level_2");
        riskEvalBeans.add(bean);
        batteryRiskUtils.dealBlank(riskEvalBeans);


        //then
        Assert.assertEquals(1,riskEvalBeans.size());

    }

}
