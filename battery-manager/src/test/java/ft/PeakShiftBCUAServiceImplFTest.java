package ft;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.TemplateStrategyRepository;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftConfigService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftBCUAServiceImpl;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftConfigServiceImpl;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.SeasonStrategyForTemplateVo;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.peak.TemplateDetailVo;
import com.zte.uedm.battery.bean.peak.TemplateTimeGranDetailVo;
import com.zte.uedm.battery.domain.BatteryCurrentStorageDomin;
import com.zte.uedm.battery.domain.impl.BackupPowerThresholdDetailDomainImpl;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonUtils.class})
public class PeakShiftBCUAServiceImplFTest {
    /* Started by AICoder, pid:gebce8db7eu2bd414c72089071337c8f2e47b76a */
    @InjectMocks
    private PeakShiftBCUAServiceImpl peakShiftBCUAService;

    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Mock
    private DeviceCacheManager deviceCacheManager;

    @Mock
    private BackupPowerThresholdDetailDomainImpl backupPowerThresholdDetailDomainImpl;

    @Resource
    private PeakShiftConfigService peakShiftConfigService = new PeakShiftConfigServiceImpl();

    @Mock
    private BatteryAttributeUtils batteryAttributeUtils;

    @Mock
    private DataRedis dataRedis;

    @Mock
    private AdapterPointDataService adapterPointDataService;

    @Mock
    private BatteryCurrentStorageDomin batteryCurrentStorageDomin;

    @Mock
    private TemplateStrategyRepository templateStrategyRepository;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    // @Test
    public void UEDM_462360_间隔时间均为尖峰时段_放电时长小于尖峰时长_when_processDetail_then_无需调整() throws Exception {
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        SeasonStrategyForTemplateVo strategy = new SeasonStrategyForTemplateVo();
        List<TemplateDetailVo> detailList = new ArrayList<>();
        TemplateDetailVo templateDetailVo = new TemplateDetailVo();
        List<TemplateTimeGranDetailVo> intervals = new ArrayList<>();
        intervals.add(new TemplateTimeGranDetailVo("0:00", "4:00", 1));
        intervals.add(new TemplateTimeGranDetailVo("4:00", "8:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("8:00", "12:00", 0));
        intervals.add(new TemplateTimeGranDetailVo("12:00", "16:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("16:00", "19:30", 1));
        intervals.add(new TemplateTimeGranDetailVo("19:30", "24:00", 3));
        templateDetailVo.setDetail(intervals);
        templateDetailVo.setDateRange(Collections.singletonList("12-30,12-31"));
        detailList.add(templateDetailVo);
        strategy.setDetail(detailList);
        strategy.setSeasonStrategyId("testSeasonStrategyId");

        devicePeakCacheInfoBean.setDeviceId("deviceId");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        devicePeakCacheInfoBean.setDeviceName("deviceName");

        try {
            peakShiftBCUAService.processStrategy(strategy, devicePeakCacheInfoBean, new BigDecimal("6"), new BigDecimal("6"));
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // @Test
    public void UEDM_462358_given_尖峰时段已达限制数量_调整其他时段时超出限制_when_processDetail_then_无法继续调整并记录日志() throws Exception {
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        SeasonStrategyForTemplateVo strategy = new SeasonStrategyForTemplateVo();
        List<TemplateDetailVo> detailList = new ArrayList<>();
        TemplateDetailVo templateDetailVo = new TemplateDetailVo();
        List<TemplateTimeGranDetailVo> intervals = new ArrayList<>();
        intervals.add(new TemplateTimeGranDetailVo("0:00", "4:00", 1));
        intervals.add(new TemplateTimeGranDetailVo("4:00", "8:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("8:00", "12:00", 0));
        intervals.add(new TemplateTimeGranDetailVo("12:00", "16:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("16:00", "19:30", 1));
        intervals.add(new TemplateTimeGranDetailVo("19:30", "24:00", 3));
        templateDetailVo.setDetail(intervals);
        templateDetailVo.setDateRange(Collections.singletonList("12-30,12-31"));
        detailList.add(templateDetailVo);
        strategy.setDetail(detailList);
        strategy.setSeasonStrategyId("testSeasonStrategyId");

        devicePeakCacheInfoBean.setDeviceId("deviceId");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        devicePeakCacheInfoBean.setDeviceName("deviceName");

        try {
            peakShiftBCUAService.processStrategy(strategy, devicePeakCacheInfoBean, new BigDecimal("6"), new BigDecimal("6"));
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // @Test
    public void UEDM_462356_given_非尖峰时段调整后仍无法满足放电时长_有谷时段_when_processDetail_then_继续调整谷时段到尖峰() throws Exception {
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        SeasonStrategyForTemplateVo strategy = new SeasonStrategyForTemplateVo();
        List<TemplateDetailVo> detailList = new ArrayList<>();
        TemplateDetailVo templateDetailVo = new TemplateDetailVo();
        List<TemplateTimeGranDetailVo> intervals = new ArrayList<>();
        intervals.add(new TemplateTimeGranDetailVo("0:00", "4:00", 1));
        intervals.add(new TemplateTimeGranDetailVo("4:00", "8:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("8:00", "12:00", 0));
        intervals.add(new TemplateTimeGranDetailVo("12:00", "16:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("16:00", "19:30", 1));
        intervals.add(new TemplateTimeGranDetailVo("19:30", "24:00", 3));
        templateDetailVo.setDetail(intervals);
        templateDetailVo.setDateRange(Collections.singletonList("12-30,12-31"));
        detailList.add(templateDetailVo);
        strategy.setDetail(detailList);
        strategy.setSeasonStrategyId("testSeasonStrategyId");

        devicePeakCacheInfoBean.setDeviceId("deviceId");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        devicePeakCacheInfoBean.setDeviceName("deviceName");

        try {
            peakShiftBCUAService.processStrategy(strategy, devicePeakCacheInfoBean, new BigDecimal("6"), new BigDecimal("6"));
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // @Test
    public void UEDM_462354_given_间隔时间含有尖峰和非尖峰时段_放电时长大于尖峰时长_when_processDetail_then_调整非尖峰时段到尖峰() throws Exception {
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        SeasonStrategyForTemplateVo strategy = new SeasonStrategyForTemplateVo();
        List<TemplateDetailVo> detailList = new ArrayList<>();
        TemplateDetailVo templateDetailVo = new TemplateDetailVo();
        List<TemplateTimeGranDetailVo> intervals = new ArrayList<>();
        intervals.add(new TemplateTimeGranDetailVo("0:00", "4:00", 1));
        intervals.add(new TemplateTimeGranDetailVo("4:00", "8:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("8:00", "12:00", 0));
        intervals.add(new TemplateTimeGranDetailVo("12:00", "16:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("16:00", "19:30", 1));
        intervals.add(new TemplateTimeGranDetailVo("19:30", "24:00", 3));
        templateDetailVo.setDetail(intervals);
        templateDetailVo.setDateRange(Collections.singletonList("12-30,12-31"));
        detailList.add(templateDetailVo);
        strategy.setDetail(detailList);
        strategy.setSeasonStrategyId("testSeasonStrategyId");

        devicePeakCacheInfoBean.setDeviceId("deviceId");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        devicePeakCacheInfoBean.setDeviceName("deviceName");

        try {
            peakShiftBCUAService.processStrategy(strategy, devicePeakCacheInfoBean, new BigDecimal("6"), new BigDecimal("6"));
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // @Test
    public void UEDM_462351_given_策略中相同时段超过4个_when_processOperationStatus_then_生成中英文警告信息() throws Exception {
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        SeasonStrategyForTemplateVo strategy = new SeasonStrategyForTemplateVo();
        List<TemplateDetailVo> detailList = new ArrayList<>();
        TemplateDetailVo templateDetailVo = new TemplateDetailVo();
        List<TemplateTimeGranDetailVo> intervals = new ArrayList<>();
        intervals.add(new TemplateTimeGranDetailVo("0:00", "4:00", 1));
        intervals.add(new TemplateTimeGranDetailVo("4:00", "8:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("8:00", "12:00", 0));
        intervals.add(new TemplateTimeGranDetailVo("12:00", "16:00", 2));
        intervals.add(new TemplateTimeGranDetailVo("16:00", "19:30", 1));
        intervals.add(new TemplateTimeGranDetailVo("19:30", "24:00", 3));
        templateDetailVo.setDetail(intervals);
        templateDetailVo.setDateRange(Collections.singletonList("12-30,12-31"));
        detailList.add(templateDetailVo);
        strategy.setDetail(detailList);
        strategy.setSeasonStrategyId("testSeasonStrategyId");

        devicePeakCacheInfoBean.setDeviceId("deviceId");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        devicePeakCacheInfoBean.setDeviceName("deviceName");

        try {
            peakShiftBCUAService.processStrategy(strategy, devicePeakCacheInfoBean, new BigDecimal("6"), new BigDecimal("6"));
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
    /* Ended by AICoder, pid:gebce8db7eu2bd414c72089071337c8f2e47b76a */

}