package ft.fake.provider;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;
import com.zte.uedm.battery.a_domain.cache.provider.AdapterCacheDataProvider;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class AdapterCacheDataProviderFake extends AdapterCacheDataProvider {
    /* Started by AICoder, pid:nf29bw59ae8ea32145c408fe40503e2405773894 */
    private Map<String, AdapterEntity> adapters = new HashMap<>();

    {
        AdapterEntity a1 = new AdapterEntity();
        a1.setId("id1");
        adapters.put("id1", a1);

        AdapterEntity a2 = new AdapterEntity();
        a2.setId("id2");
        adapters.put("id2", a2);

        AdapterEntity a3 = new AdapterEntity();
        a3.setId("id3");
        adapters.put("id3", a3);
    }

    @Override
    public List<AdapterEntity> getCacheDataForCacheProvider(Set<String> keys) throws UedmException {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>(adapters.values());
        } else {
            return keys.stream()
                    .map(adapters::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }
    /* Ended by AICoder, pid:nf29bw59ae8ea32145c408fe40503e2405773894 */
}
