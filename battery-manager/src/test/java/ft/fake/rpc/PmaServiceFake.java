package ft.fake.rpc;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.pma.service.PmaService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PmaServiceFake extends PmaService {

    @Override
    public List<HistoryAiBean> selectDataByCondition(List<String> moIds, List<String> smpIds, String startTime, String endTime, String dataType, String gr) throws UedmException {
        List<HistoryAiBean> beanList = new ArrayList<>();
        String pvSupplySmpId = "pv.accum.pwr.supply";
        String dcLoadConsumeSmpId = "dcload.accum.pwr.consumption";
        String spuGeneration = "spu.accum.generation";
        smpIds.forEach(smpId->{
            if (smpId.equals(pvSupplySmpId) || smpId.equals(dcLoadConsumeSmpId) || smpId.equals(spuGeneration)) {
                moIds.forEach(moId -> {
                    HistoryAiBean bean1 = new HistoryAiBean();
                    bean1.setCurValueTime(startTime);
                    bean1.setSmpId(smpId);
                    bean1.setResId(moId);

                    HistoryAiBean bean2 = new HistoryAiBean();
                    bean2.setCurValueTime(endTime);
                    bean2.setSmpId(smpId);
                    bean2.setResId(moId);

                    if ("mo-pv-gxu6f5".equals(moId)) {
                        bean1.setIncValue("21.347");
                        bean2.setIncValue("25.347");
                    } else if ("mo-pv-gx3h11".equals(moId)) {
                        bean1.setIncValue("32.5");
                        bean2.setIncValue("30.5");
                    } else if ("mo-pv-gxmzjg".equals(moId)) {
                        bean1.setIncValue("45.234");
                        bean2.setIncValue("49.234");
                    } else if ("mo-pv-gxmgqi".equals(moId)) {
                        bean1.setIncValue("56");
                        bean2.setIncValue("69.1");
                    } else if ("dcload1".equals(moId)) {
                        bean1.setIncValue("23.7");
                        bean2.setIncValue("27.7");
                    } else if ("dcload2".equals(moId)) {
                        bean1.setIncValue("33.7");
                        bean2.setIncValue("27.7");
                    } else if ("dcload3".equals(moId)) {
                        bean1.setIncValue("37.89");
                        bean2.setIncValue("49.70");
                    } else if ("dcload4".equals(moId)) {
                        bean1.setIncValue("43.7");
                        bean2.setIncValue("67.7");
                    }  else if ("dcload5".equals(moId)) {
                        bean1.setIncValue("23.7");
                        bean2.setIncValue("47.09");
                    } else if ("dcload6".equals(moId)) {
                        bean1.setIncValue("63.7");
                        bean2.setIncValue("87.7");
                    }else {
                        bean1.setIncValue("12.1");
                        bean2.setIncValue("35.9");
                    }
                    beanList.add(bean1);
                    beanList.add(bean2);
                });
            }

        });
        return beanList;
    }
}
