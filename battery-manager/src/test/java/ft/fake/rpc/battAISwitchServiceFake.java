package ft.fake.rpc;

import com.zte.uedm.battery.controller.battAiConfig.vo.AiConfigVo;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.common.exception.UedmException;

public class battAISwitchServiceFake implements BattAISwitchService{
    public int branch = 0;
    @Override
    public AiConfigVo selectConfigById(String id, String languageOption) throws UedmException {
        if ("ai.life".equals(id) && branch == 0){
            AiConfigVo aiConfigVo = new AiConfigVo();
            aiConfigVo.setValue("ON");
            aiConfigVo.setFlag(true);
            return aiConfigVo;
        }else {
            AiConfigVo aiConfigVo = new AiConfigVo();
            aiConfigVo.setFlag(false);
            return aiConfigVo;
        }
    }

    @Override
    public Integer updateById(String id, String value) throws UedmException {
        return 0;
    }

    @Override
    public String selectEolById(String id) throws UedmException {
        return "";
    }
}
