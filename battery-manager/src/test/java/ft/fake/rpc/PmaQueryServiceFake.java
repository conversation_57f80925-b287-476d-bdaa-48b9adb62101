package ft.fake.rpc;

import com.zte.uedm.battery.util.FakeBranchFlag;
import com.zte.uedm.service.pma.api.PmaQueryService;
import com.zte.uedm.service.pma.api.dto.*;
import com.zte.uedm.service.pma.api.vo.*;
import com.zte.uedm.service.pma.bean.ResultBean;
import com.zte.uedm.service.pma.bean.cache.MocCache;
import org.jetbrains.annotations.NotNull;

import java.util.*;

public class PmaQueryServiceFake implements PmaQueryService {
    public static String GET_SITES_DS_BEANS_FLAG = FakeBranchFlag.DATA_NULL;

    @Override
    public QueryResponseVo queryPmaDataByPaging(PageQueryDto pageQueryDto) {
        return null;
    }

    @Override
    public BeanResponseVo getBeanByPagingQuery(PageQueryDto pageQueryDto) {
        return null;
    }

    @Override
    public QueryResponseVo queryPmaDataByNoPaging(NoPageQueryDto noPageQueryDto) {
        return null;
    }

    @Override
    /* Started by AICoder, pid:a68e63b66e5e44d140d4083660b7a02c466830a1 */
    public BeanResponseVo getBeanByNoPagingQuery(NoPageQueryDto noPageQueryDto) {
        BeanResponseVo beanResponseVo = new BeanResponseVo();
        beanResponseVo.setCode(0);
        List<ResultBean> resultList = new ArrayList<>();

        ResultBean resultBean1 = createResultBean("aa", "55", "45", "50", "2024-09-10 11:00:00");
        ResultBean resultBean2 = createResultBean("bb", "45", "10", "50", "2024-09-10 11:00:00");
        ResultBean resultBean3 = createResultBean("bb", "45", "10", "50", "2024-09-10 12:00:00");

        if ("15".equals(GET_SITES_DS_BEANS_FLAG)) {
            resultBean1.setMinValue("9");
        }

        resultList.add(resultBean1);
        resultList.add(resultBean2);
        resultList.add(resultBean3);
        Map<Integer, BeanDataVo> data = getIntegerBeanDataVoMap(resultList);
        beanResponseVo.setData(data);
        return beanResponseVo;
    }

    @NotNull
    /* Started by AICoder, pid:ee70da508dxa1d3146f8085b10da431e0001dbee */
    private Map<Integer, BeanDataVo> getIntegerBeanDataVoMap(List<ResultBean> resultList) {
        BeanDataVo beanDataVo = new BeanDataVo();
        beanDataVo.setBeanList(resultList);
        beanDataVo.setTotalCount(10);

        Map<Integer, BeanDataVo> data = new LinkedHashMap<>();
        data.put(5, beanDataVo);
        data.put(1440, beanDataVo);

        return data;
    }
    /* Ended by AICoder, pid:ee70da508dxa1d3146f8085b10da431e0001dbee */

    private ResultBean createResultBean(String resId, String maxValue, String minValue, String avgValue, String beginTime) {
        ResultBean resultBean = new ResultBean();
        resultBean.setResId(resId);
        resultBean.setMaxValue(maxValue);
        resultBean.setMinValue(minValue);
        resultBean.setAvgValue(avgValue);
        resultBean.setBeginTime(beginTime);
        return resultBean;
    }
    /* Ended by AICoder, pid:a68e63b66e5e44d140d4083660b7a02c466830a1 */

    @Override
    public MocQueryVo queryMocInfo(MocQueryDto mocQueryDto) {
        MocQueryVo mocQueryVo = new MocQueryVo();
        Map<String, MocCache> data = new HashMap<>();
        MocCache mocCache = new MocCache();
        mocCache.setGrs(Arrays.asList(5,1440,10080,43200));
        mocCache.setMoc("r32.uedm.device.battery");
        data.put("r32.uedm.device.battery",mocCache);
        mocQueryVo.setData(data);
        return mocQueryVo;
    }

    /* Started by AICoder, pid:nb501gdfe6h6ee114f8508146031321b5479808d */

    @Override
    public QueryResponseVo queryConvOfflineByPaging(PageQueryDto pageQueryDto) {
        return null;
    }

    @Override
    public QueryResponseVo queryConvOfflineByNoPaging(NoPageConvDto noPageConvDto) {
        return null;
    }

    @Override
    public CountQueryRespVo queryPmaDataCount(CountQueryDto countQueryDto) {
        return null;
    }

    @Override
    public QueryResponseVo queryApproximateTimePmaDataByNoPaging(NoPageTimeQueryDto noPageTimeQueryDto) {
        return new QueryResponseVo();
    }

    @Override
    public TimePmaDataResponseVo queryApproximateTimeBeanByNoPaging(NoPageTimeQueryDto noPageTimeQueryDto) {
        return new TimePmaDataResponseVo();
    }

    @Override
    public IncPmaDataResponseVo queryIncPmaDataBeanByNoPaging(NoPageIncQueryDto noPageIncQueryDto) {
        return new IncPmaDataResponseVo();
    }
    /* Ended by AICoder, pid:nb501gdfe6h6ee114f8508146031321b5479808d */
}
