package ft.fake.rpc.gateway;

import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.opti.domain.gateway.ConfigurationServiceInterface;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.logic.group.bean.RealGroupBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorDeviceObjectRelationBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.configuration.opt.logic.group.entity.RealGroupEntity;
import com.zte.uedm.common.configuration.opt.monitordevice.entity.MonitorDeviceBaseEntity;
import com.zte.uedm.common.configuration.opt.monitordevice.entity.MonitorDeviceModuleEntity;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.configuration.opt.real.group.site.entity.SiteEntity;
import com.zte.uedm.common.configuration.opt.relation.objectdevice.entity.MonitorDeviceObjectRelationEntity;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.service.config.optional.MocOptional;
import org.mockito.Mockito;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ConfigurationServiceFake implements ConfigurationServiceInterface {

    public static String BATCH_GET_ALL_OBJECT_BY_MOC = FakeBranchFlag.DATA_NULL;


    @Override
    public List<MonitorObjectEntity> batchGetAllObjectByMoc(List<String> ids, String moc) throws UedmException {
        List<MonitorObjectEntity> monitorObjectBeans = new ArrayList<>();
        if (BATCH_GET_ALL_OBJECT_BY_MOC.equals(FakeBranchFlag.DATA_NORMAL)) {
            if (MocOptional.SPU.getId().equals(moc) || MocOptional.SPCU.getId().equals(moc)) {
                MonitorObjectEntity m1 = new MonitorObjectEntity();
                m1.setId("spu1");
                m1.setName("spu1");
                m1.setIdPath("RealGroup-ctrscu/Site-frs1ba/spu1");
                MonitorObjectEntity m2 = new MonitorObjectEntity();
                m2.setId("spu2");
                m2.setName("spu2");
                m2.setIdPath("RealGroup-ctrscu/Site-frs1ba/spu2");
                MonitorObjectEntity m3 = new MonitorObjectEntity();
                m3.setId("spu3");
                m3.setName("spu3");
                m3.setIdPath("RealGroup-c9i07o/Site-d4m7nl/spu3");
                MonitorObjectEntity m4 = new MonitorObjectEntity();
                m4.setId("spu4");
                m4.setName("spu4");
                m4.setIdPath("RealGroup-ctrscu/Site-g9dbod/spu4");
                monitorObjectBeans.add(m4);
                monitorObjectBeans.add(m3);
                monitorObjectBeans.add(m2);
                monitorObjectBeans.add(m1);
            }
        }
        return monitorObjectBeans;
    }
}
