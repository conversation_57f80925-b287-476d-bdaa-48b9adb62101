package ft.fake.redis;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryOriginalLocationBean;
import com.zte.uedm.common.bean.south.LinkCommStatusDTO;
import com.zte.uedm.common.consts.standardpoint.BattStandardPointConstants;
import com.zte.uedm.component.redis.service.RedisSingleService;

import java.util.*;

public class RedisServiceFake extends RedisSingleService {
    public static String identification = "";

    @Override
    public <T> T delete(String name, String key) throws UedmException {

        if("".equals(identification)) {
            return null;
        }
        return super.delete(name,key);
    }

    @Override
    public void put(String name, String key, Object value) throws UedmException {
        if("".equals(identification)) {
            return;
        }
        super.put(name, key, value);
    }

    @Override
    public void put(String name, String key, Object value, long expireTime) throws UedmException {
        super.put(name, key, value, expireTime);
    }

    @Override
    public void put(String key, String value, long expireTime) throws UedmException {
        super.put(key, value, expireTime);
    }

    @Override
    public <T> T getCache(String name, String key) throws UedmException {
        if("".equals(key) || "r32.uedm.device-511mxbxgwg6".equals(key) || "r32.uedm.device-514mxbxgwg6".equals(key)) {
            return null;
        }
        if("r32.uedm.device-59mxbxgwg6".equals(key) || "r32.uedm.device-510mxbxgwg6".equals(key)) {
            BatteryOriginalLocationBean redisAlarm = new BatteryOriginalLocationBean();
            redisAlarm.setLongitude("66");
            redisAlarm.setLatitude("2");
            redisAlarm.setStatus(1);
            return ((T) redisAlarm);
        }
        if("r32.uedm.device-512mxbxgwg6".equals(key)) {
            BatteryOriginalLocationBean redisAlarm = new BatteryOriginalLocationBean();
            redisAlarm.setLongitude("66");
            redisAlarm.setLatitude("89");
            redisAlarm.setStatus(1);
            return ((T) redisAlarm);
        }
        return super.getCache(name, key);
    }

    @Override
    public Map<String, Object> getCacheMap(String name) throws UedmException {
        if ("equals".equals(identification))
        {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> valueMap = new HashMap<>();
            Map<String, Object> valueMap2 = new HashMap<>();
            dataMap.put(BattStandardPointConstants.BATT_SMPID_LONGITUDE,valueMap);
            dataMap.put(BattStandardPointConstants.BATT_SMPID_LAITUDE,valueMap2);
            valueMap.put("value","0");
            valueMap2.put("value","0");
            valueMap.put("timestamp","1");
            valueMap2.put("timestamp","1");
            return dataMap;
        }
        if (name.equals("standard-data:batt-1"))
        {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> valueMap = new HashMap<>();
            dataMap.put("battery.deploy.state",valueMap);
            valueMap.put("value","1");
            return dataMap;
        }
        if (name.equals("std-data:batt-aaa") || name.equals("std-data:batt-bbb"))
        {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> valueMap = new HashMap<>();
            Map<String, Object> valueMap2 = new HashMap<>();
            valueMap.put("value","1");
            if ("normal".equals(identification))
            {
                valueMap2.put("value","1");
                dataMap.put("batt.lock.alarm",valueMap2);
            }
            else
            {
                valueMap2.put("value","2");
                dataMap.put("batt.lock.alarm",valueMap2);
            }
            dataMap.put("battery.deploy.state",valueMap);
            return dataMap;
        }
        if ("normal".equals(identification))
        {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> valueMap = new HashMap<>();
            List<LinkCommStatusDTO> commStatusDTOS = new ArrayList<>();
            LinkCommStatusDTO commStatusDTO = new LinkCommStatusDTO();
            commStatusDTO.setStatus(0);
            dataMap.put("collector-1",valueMap);
            valueMap.put("deviceId","1");
            valueMap.put("currLink","1");
            valueMap.put("status",0);
            valueMap.put("linkInfo",commStatusDTOS);
            return dataMap;
        }
        if ("exception".equals(identification))
        {
            throw new RuntimeException();
        }
        return super.getCacheMap(name);
    }

    @Override
    public Map<String, Object> getCacheMap(String name, Set<String> set) throws UedmException {
        if ("normal".equals(identification))
        {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> valueMap = new HashMap<>();
            List<LinkCommStatusDTO> commStatusDTOS = new ArrayList<>();
            LinkCommStatusDTO commStatusDTO = new LinkCommStatusDTO();
            commStatusDTO.setStatus(0);
            dataMap.put("collector-1",valueMap);
            valueMap.put("deviceId","1");
            valueMap.put("currLink","1");
            valueMap.put("status",0);
            valueMap.put("linkInfo",commStatusDTOS);
            return dataMap;
        }
        if ("exception".equals(identification))
        {
            throw new RuntimeException();
        }
        return super.getCacheMap(name);
    }
}
