package ft.fake.redis;

import com.zte.uedm.service.config.optional.StandPointOptional;
import com.zte.uedm.service.mp.api.standard.StandardDataService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.uedm.service.config.optional.StandPointOptional.BATTERY_SMPID_KEY_MATCH_STATE;

public class StandardDataServiceFake implements StandardDataService {
    public static String identification = "";

    @Override
    public Map<String, Map<String, String>> getByResourceIdAndStandPointId(String resourceId, List<String> standPoints) {

        Map<String, Map<String, String>> dataMap = new HashMap<>();
        Map<String, String> valueMap = new HashMap<>();
        Map<String, String> valueMap2 = new HashMap<>();
        if ("equals".equals(identification))
        {
            valueMap.put("value","21.22");
            valueMap2.put("value","21.22");
            valueMap.put("timestamp","1702488115020");
            valueMap2.put("timestamp","1702488115020");
        }
        else if ("not equals".equals(identification))
        {
            valueMap.put("value","");
            valueMap2.put("value","");
            valueMap.put("timestamp","1702488115020");
            valueMap2.put("timestamp","1602488115020");
        }
        else
        {
            valueMap.put("value","21.22");
            valueMap2.put("value","23.23");
            valueMap.put("timestamp","1702488115020");
            valueMap2.put("timestamp","1602488115020");
        }
        dataMap.put(StandPointOptional.BATTERY_SMPID_LONGITUDE.getId(),valueMap);
        dataMap.put(StandPointOptional.BATTERY_SMPID_LAITUDE.getId(),valueMap2);
        return dataMap;
    }

    /* Started by AICoder, pid:3a46c163a22bb8e148c70b84a03f08195549f563 */
    @Override
    public Map<String, Map<String, Object>> batchQueryByResourceIdAndStandPointId(List<String> resIds, List<String> standardPoints) {
        Map<String,Map<String, Object>> dataMap = new HashMap<>();
        Map<String, Object> valueMap = new HashMap<>();
        Map<String, Object> valueMap2 = new HashMap<>();
        Map<String, Object> child = new HashMap<>();
        Map<String, Object> child2 = new HashMap<>();
        if ("alarm".equals(identification))
        {
            child.put("value","1");
            child2.put("value","1");
        }
        else if ("none".equals(identification))
        {
            child.put("value","2");
            child2.put("value","2");
        }
        else if ("null".equals(identification))
        {
            child.put("value","");
            child2.put("value","");
        }
        else
        {
            child.put("value","0");
            child2.put("value","0");
        }

        child.put("timestamp","1721791602000");
        child2.put("timestamp","1721791602000");
        Map<String, Object> child3 = new HashMap<>();
        Map<String, Object> child4 = new HashMap<>();
        if ("alarm".equals(identification))
        {
            child3.put("value","1");
            child4.put("value","1");
            Map<String, Object> alarmMap = new HashMap<>();
            Map<String, Object> alarmMap2 = new HashMap<>();
            alarmMap.put("value","1");
            alarmMap2.put("value","1");
            valueMap.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),alarmMap);
            valueMap2.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),alarmMap2);
        }
        else if ("none".equals(identification))
        {
            child3.put("value","2");
            child4.put("value","2");
            Map<String, Object> noneMap = new HashMap<>();
            Map<String, Object> noneMap2 = new HashMap<>();
            noneMap.put("value","0");
            noneMap2.put("value","0");
            valueMap.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),noneMap);
            valueMap2.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),noneMap2);
        }
        else if ("null".equals(identification))
        {
            child3.put("value","");
            child4.put("value","");
            Map<String, Object> normalMap = new HashMap<>();
            Map<String, Object> normalMap2 = new HashMap<>();
            normalMap.put("value","");
            normalMap2.put("value","");
            valueMap.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),normalMap);
            valueMap2.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),normalMap2);
        }
        else
        {
            child3.put("value","0");
            child4.put("value","0");
            Map<String, Object> normalMap = new HashMap<>();
            Map<String, Object> normalMap2 = new HashMap<>();
            normalMap.put("value","1");
            normalMap2.put("value","1");
            valueMap.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),normalMap);
            valueMap2.put(StandPointOptional.BATTERY_SMPID_FORTIFICATION_STATUSE.getId(),normalMap2);
        }
        if ("marchStatus".equals(identification))
        {
            child3.put("value","0");
            child4.put("value","0");
            Map<String, Object> normalMap = new HashMap<>();
            Map<String, Object> normalMap2 = new HashMap<>();
            normalMap.put("value","1");
            normalMap2.put("value","1");
            valueMap.put(BATTERY_SMPID_KEY_MATCH_STATE.getId(),normalMap);
            valueMap2.put(BATTERY_SMPID_KEY_MATCH_STATE.getId(),normalMap2);
        }
        child3.put("timestamp","1621791602000");
        child4.put("timestamp","1621791602000");
        valueMap.put(StandPointOptional.BATTERY_SMPID_LONGITUDE.getId(),child);
        valueMap.put(StandPointOptional.BATTERY_SMPID_LAITUDE.getId(),child2);
        valueMap.put(StandPointOptional.BATTERY_SMPID_SHAKE_ALARM.getId(),child);
        valueMap.put(StandPointOptional.BATTERY_SMPID_LOCK_STATUS.getId(),child2);

        valueMap2.put(StandPointOptional.BATTERY_SMPID_LONGITUDE.getId(),child3);
        valueMap2.put(StandPointOptional.BATTERY_SMPID_LAITUDE.getId(),child4);
        valueMap2.put(StandPointOptional.BATTERY_SMPID_SHAKE_ALARM.getId(),child3);
        valueMap2.put(StandPointOptional.BATTERY_SMPID_LOCK_STATUS.getId(),child4);
        dataMap.put("batt-aaa",valueMap);
        dataMap.put("batt-bbb",valueMap2);
        if (resIds.contains("r32.uedm.device.battery1") && identification.equals("normal")){
            valueMap.put(StandPointOptional.BATTERY_SMPID_SERIAL_NUMBER.getId(), child);
            valueMap.put(StandPointOptional.BATTERY_SMPID_KEY_MATCH_STATE.getId(), child);
            dataMap.put("r32.uedm.device.battery1",valueMap);
        }
        if (resIds.contains("r32.uedm.device.battery1") && identification.equals("empty")){
            valueMap.put(StandPointOptional.BATTERY_SMPID_SERIAL_NUMBER.getId(), new HashMap<>());
            dataMap.put("r32.uedm.device.battery1",valueMap);
        }
        return dataMap;
    }
    /* Ended by AICoder, pid:3a46c163a22bb8e148c70b84a03f08195549f563 */
}
