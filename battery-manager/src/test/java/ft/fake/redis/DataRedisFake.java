package ft.fake.redis;

import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.common.exception.UedmException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* Started by AICoder, pid:5c4b246e2apcc87147090826f0f836192b4208ae */
public class DataRedisFake extends DataRedis {

    public String getSmpValueByKey(String moId, String key) throws UedmException {
        return "0";
    }

    public Map<String, String> batchGetSmpValueByKey(List<String> moIds, String key) throws UedmException {
        Map<String, String> map = new HashMap<>();
        map.put("id", "1");
        return map;
    }
}
/* Ended by AICoder, pid:5c4b246e2apcc87147090826f0f836192b4208ae */

