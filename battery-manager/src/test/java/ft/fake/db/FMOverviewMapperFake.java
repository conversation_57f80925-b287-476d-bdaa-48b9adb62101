package ft.fake.db;

import com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper.FMOverviewMapper;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import com.zte.uedm.common.exception.UedmException;

import java.util.ArrayList;
import java.util.List;

/* Started by AICoder, pid:w38c9re53ct03da14a360a9330b5022300e91d6d */
public class FMOverviewMapperFake implements FMOverviewMapper {

    @Override
    public List<FMOverviewPo> selectFmOverviewBean(String userName) throws UedmException {
        List<FMOverviewPo> list = new ArrayList<>();
        for (int i = 1; i < 10; i++) {
            FMOverviewPo fmOverviewBeanVo = new FMOverviewPo();
            fmOverviewBeanVo.setId("" + i + "");
            fmOverviewBeanVo.setSequence(i);
            fmOverviewBeanVo.setEnable(true);
            list.add(fmOverviewBeanVo);
        }
        return list;
    }

    @Override
    public int deleteFmOverviewBean(String userName) throws UedmException {
        return 0;
    }

    @Override
    public void insertFmOverviewBean(List<FMOverviewPo> list) {
    }

    @Override
    public Integer updateFmOverviewBean(List<FMOverviewPo> beanList) {
        return 0;
    }
}
/* Ended by AICoder, pid:w38c9re53ct03da14a360a9330b5022300e91d6d */
