package ft.fake.db;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.zte.uedm.battery.bean.BatteryOverviewBean;
import com.zte.uedm.battery.bean.BatteryOverviewBeanVo;
import com.zte.uedm.battery.bean.BatteryRealtimeReportBean;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.mapper.BatteryOverviewMapper;
import com.zte.uedm.common.exception.UedmException;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.List;

public class BatteryOverviewMapperFake implements BatteryOverviewMapper {

    private final ObjectMapper objectMapper = new ObjectMapper();

    public BatteryOverviewMapperFake() {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }

    @Override
    public List<BatteryOverviewBean> selectBatteryAssertConfig(String userName) throws UedmException {
        return null;
    }

    @Override
    public void insertBatteryAssertConfig(BatteryOverviewBeanVo bean) {

    }

    @Override
    public Integer updateBatteryAssertConfig(BatteryOverviewBeanVo bean) {
        return null;
    }

    @Override
    public List<BatteryOverviewBean> searchBatteryAssertConfig(BatteryOverviewBeanVo bean) {
        return null;
    }

    @Override
    public BattHealthStatusEvalPo selectBatterySoh(String id) {
        return null;
    }

    @Override
    public List<BatteryRealtimeReportBean> selectBatteryRealtimeReport(List<String> ids) throws SQLException {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("data/db/battery-overview-mapper/select_battery_realtime_report_and_moc.json");
        CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, BatteryRealtimeReportBean.class);
        try {
            List<BatteryRealtimeReportBean> lists= objectMapper.readValue(inputStream, collectionType);
            return lists;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
