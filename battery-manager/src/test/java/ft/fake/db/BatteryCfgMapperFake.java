package ft.fake.db;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryCfgBean;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.BatteryCfgMapper;
import java.util.List;

public class BatteryCfgMapperFake implements BatteryCfgMapper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    public static String identification = "";

    public BatteryCfgMapperFake() {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }

    @Override
    public BatteryCfgBean getBatteryCfg(String item) {
        if ("normal".equals(identification))
        {
            BatteryCfgBean batteryCfgBean = new BatteryCfgBean();
            batteryCfgBean.setItem("item");
            batteryCfgBean.setValue("1");
            return batteryCfgBean;
        }
        if ("exception".equals(identification))
        {
            throw new RuntimeException();
        }
        return null;
    }


    @Override
    public void insertOrUpdateCfg(BatteryCfgBean bean) {}
}
