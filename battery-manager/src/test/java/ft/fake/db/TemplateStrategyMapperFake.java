package ft.fake.db;

import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.mapper.TemplateStrategyMapper;
import com.zte.uedm.common.exception.UedmException;

import java.util.ArrayList;
import java.util.List;

public class TemplateStrategyMapperFake implements TemplateStrategyMapper {
    @Override
    public Integer deleteByIds(List<String> ids) throws UedmException {
        return 1;
    }

    @Override
    public Integer deleteDetailStrategyByIds(List<String> ids)  {
        return null;
    }

    @Override
    public void deleteIssuedTaskStrategyRelationByTaskId(String id) {

    }

    @Override
    public void deleteIssuedHistoryDetail(String templateStrategyId, Double version) {

    }

    @Override
    public List<String> selectNameByIds(List<String> ids)  {
        List<String> names = new ArrayList<>();
        if (ids.contains("a0635594-70eb-4c9d-994e-2a9c1627f314")){
            names.add("错峰-5代");
        }
        return names;
    }

    @Override
    public List<String> selectFileIdByIds(List<String> ids) throws UedmException {
        List<String> list = new ArrayList<>();
        return list;
    }

    @Override
    public Integer countName(String name) {
        if ("错峰-5代".equals(name) || "错峰-4代".equals(name)) {
            return 1;
        }
        return 0;
    }

    @Override
    public List<TemplateStrategyBo> selectBySeasonStrategyId(String seasonStrategyId) {
        return null;
    }

    @Override
    public List<TemplateStrategyBo> selectByConditions(TemplateQueryDto queryBean) {
        List<TemplateStrategyBo> list = new ArrayList<>();
        TemplateStrategyBo templateStrategyBo = new TemplateStrategyBo();
        templateStrategyBo.setId("a0635594-70eb-4c9d-994e-2a9c1627f314");
        templateStrategyBo.setName("错峰-5代");
        templateStrategyBo.setDeviceType("CSU5");
        templateStrategyBo.setSource("web");
        templateStrategyBo.setSeasonStrategyId("b5e9af82-136d-4335-9cdb-1a4aeee283c3");
        templateStrategyBo.setRemark("");
        templateStrategyBo.setVersion("1.01");
        templateStrategyBo.setGmtCreate("2023-12-26 09:14:29");
        templateStrategyBo.setGmtModified("2023-12-26 09:14:48");
        templateStrategyBo.setUserCreate("admin");
        templateStrategyBo.setUserModified("admin");
//      "mode" : "3",
//      "weekend_flag" : true,
        list.add(templateStrategyBo);
        return list;
    }

    @Override
    public Integer insertTemplateStrategy(TemplateStrategyPo templateStrategyPo)  {
        return null;
    }

    @Override
    public Integer insertTemplateStrategyDetail(List<TemplateStrategyDetailBcuaPo> details)  {
        return null;
    }

    @Override
    public Integer updateTemplateStrategy(TemplateStrategyPo templateStrategyPo)  {
        return null;
    }

    @Override
    public Integer updateIssuedTaskStrategyRelation(IssuedTaskStrategyRelationPo issuedTaskStrategyRelationPo) {
        return null;
    }

    @Override
    public IssuedTaskStrategyRelationPo selectIssuedTaskStrategyRelation(String taskId) {
        return null;
    }

    @Override
    public List<IssuedTaskStrategyRelationPo> selectRelationExceptSelf(IssuedTaskStrategyRelationPo bean) {
        return null;
    }

    @Override
    public void updateTemplateStrategyVersion(String id) {

    }

    @Override
    public Integer insertIssuedHistoryDetail(IssuedHistoryPo issuedHistoryDetailPo)  {
        return null;
    }

    @Override
    public List<IssuedHistoryPo> selectIssuedHistoryDetail(IssuedHistoryPo issuedHistoryDetailPo)  {
        return null;
    }

    @Override
    public void insertIssuedTaskStrategyRelation(IssuedTaskStrategyRelationPo issuedTaskStrategyRelationPo) {

    }

    @Override
    public IssuedHistoryPo selectIssueHistoryDetailByTaskId(String task_id)  {
        if ("9e7c822d-3287-4bf7-8766-0a1eb439437b".equals(task_id)) {
            IssuedHistoryPo issuedHistoryPo = new IssuedHistoryPo();
            issuedHistoryPo.setTemplateStrategyId("8ad2c9ae-4d50-4d15-8863-1c03097299cc");
            issuedHistoryPo.setVersion(1.00);
            issuedHistoryPo.setAllDetail("{\"detail\":[{\"detail\":[{\"beginTime\":\"0:00\",\"endTime\":\"6:00\",\"strategyType\":2},{\"beginTime\":\"6:00\",\"endTime\":\"12:00\",\"strategyType\":2},{\"beginTime\":\"12:00\",\"endTime\":\"18:00\",\"strategyType\":1},{\"beginTime\":\"18:00\",\"endTime\":\"24:00\",\"strategyType\":0}],\"timeGran\":[]}],\"deviceType\":\"BCUA\",\"fileId\":\"\",\"gmtCreate\":\"2023-12-27 15:42:19\",\"holiday\":[{\"detail\":[{\"beginTime\":\"0:00\",\"endTime\":\"6:00\",\"strategyType\":2},{\"beginTime\":\"6:00\",\"endTime\":\"12:00\",\"strategyType\":2},{\"beginTime\":\"12:00\",\"endTime\":\"18:00\",\"strategyType\":1},{\"beginTime\":\"18:00\",\"endTime\":\"24:00\",\"strategyType\":0}],\"timeGran\":[{\"begin\":\"01/01\",\"end\":\"01/02\",\"remark\":\"\"}]}],\"id\":\"8ad2c9ae-4d50-4d15-8863-1c03097299cc\",\"mode\":\"0\",\"name\":\"除了分组0之外的\",\"remark\":\"\",\"seasonStrategyId\":\"186df733-5d1a-4090-91f2-c56b48ac9de6\",\"seasonStrategyName\":\"能效电价 > 生效日期 2023-09-23\",\"source\":\"web\",\"userCreate\":\"admin\",\"version\":\"V1.00\"}");
            return issuedHistoryPo;
        }
        return null;
    }

    @Override
    public List<TemplateStrategyDetailBcuaPo> selectDetailByTemplateId(String id)  {
        return null;
    }

    @Override
    public List<TemplateStrategyDetailCsu5Po> selectCsu5DetailByTemplateId(String id)  {
        List<TemplateStrategyDetailCsu5Po> list = new ArrayList<>();
        if ("a0635594-70eb-4c9d-994e-2a9c1627f314".equals(id)) {
            TemplateStrategyDetailCsu5Po templateStrategyDetailCsu5Po = new TemplateStrategyDetailCsu5Po();
            templateStrategyDetailCsu5Po.setId("5eec174f-a2a9-4513-9fb5-a683e8608771");
            templateStrategyDetailCsu5Po.setTemplateIndex(1);
            templateStrategyDetailCsu5Po.setTemplateStrategyId("a0635594-70eb-4c9d-994e-2a9c1627f314");
            templateStrategyDetailCsu5Po.setHolidayFlag(false);
            templateStrategyDetailCsu5Po.setRemark("");
            templateStrategyDetailCsu5Po.setBeginDate("2023-12-12");
            templateStrategyDetailCsu5Po.setEndDate("2024-12-31");
            templateStrategyDetailCsu5Po.setDetail("[{\"beginTime\":\"0:00\",\"endTime\":\"6:00\",\"strategyType\":3},{\"beginTime\":\"6:00\",\"endTime\":\"12:00\",\"strategyType\":2},{\"beginTime\":\"12:00\",\"endTime\":\"18:00\",\"strategyType\":1},{\"beginTime\":\"18:00\",\"endTime\":\"24:00\",\"strategyType\":0}]");
            templateStrategyDetailCsu5Po.setGmtCreate("2023-12-26 09:14:48");
            list.add(templateStrategyDetailCsu5Po);
        }
        return list;
    }

    @Override
    public TemplateStrategyDetailBo selectTemplateById(String id) {
        if ("a0635594-70eb-4c9d-994e-2a9c1627f314".equals(id)) {
            TemplateStrategyDetailBo templateStrategyDetailBo = new TemplateStrategyDetailBo();
            templateStrategyDetailBo.setId("a0635594-70eb-4c9d-994e-2a9c1627f314");
            templateStrategyDetailBo.setName("错峰-5代");
            templateStrategyDetailBo.setDeviceType("CSU5");
            templateStrategyDetailBo.setSource("web");
            templateStrategyDetailBo.setSeasonStrategyId("b5e9af82-136d-4335-9cdb-1a4aeee283c3");
            templateStrategyDetailBo.setRemark("");
            templateStrategyDetailBo.setVersion("1.01");
            templateStrategyDetailBo.setGmtCreate("2023-12-26 09:14:29");
            templateStrategyDetailBo.setGmtModified("2023-12-26 09:14:48");
            templateStrategyDetailBo.setUserCreate("admin");
            templateStrategyDetailBo.setUserModified("admin");
//          "mode" : "3",
//          "weekend_flag" : true,
            return templateStrategyDetailBo;
        }
        return null;
    }

    @Override
    public List<TemplateStrategyDetailBo> selectTemplateByIdForCheckName(String name, String id) {
        List<TemplateStrategyDetailBo> list = new ArrayList<>();
        if ("错峰-5代".equals(name) && (!"a0635594-70eb-4c9d-994e-2a9c1627f314".equals(id))) {
            TemplateStrategyDetailBo templateStrategyDetailBo = new TemplateStrategyDetailBo();
            templateStrategyDetailBo.setId("a0635594-70eb-4c9d-994e-2a9c1627f314");
            templateStrategyDetailBo.setName("错峰-5代");
            list.add(templateStrategyDetailBo);
            return list;
        }
        return list;
    }


    @Override
    public Integer insertTemplateStrategyCsuDetail(List<TemplateStrategyDetailCsuPo> details) {
        return null;
    }

    @Override
    public Integer deleteCsu5DetailStrategyByIds(List<String> ids) {
        return null;
    }

    @Override
    public List<String> selectSeasonStrategyByLogicIds(List<String> logicIds) {
        return null;
    }
}
