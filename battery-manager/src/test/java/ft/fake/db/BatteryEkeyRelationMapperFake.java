package ft.fake.db;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zte.uedm.battery.a_infrastructure.safe.po.BatteryEkeyRelationPo;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.BatteryEkeyRelationMapper;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class BatteryEkeyRelationMapperFake implements BatteryEkeyRelationMapper {
    public static String identification = "";

    @Override
    public int insert(BatteryEkeyRelationPo entity) {
        return 0;
    }

    @Override
    public int deleteById(Serializable id) {
        return 0;
    }

    @Override
    public int deleteById(BatteryEkeyRelationPo entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(BatteryEkeyRelationPo entity) {
        return 0;
    }

    @Override
    public int update(BatteryEkeyRelationPo entity, Wrapper<BatteryEkeyRelationPo> updateWrapper) {
        return 0;
    }

    @Override
    public BatteryEkeyRelationPo selectById(Serializable id) {
        return null;
    }

    @Override
    public BatteryEkeyRelationPo selectOne(Wrapper<BatteryEkeyRelationPo> updateWrapper) {
        return new BatteryEkeyRelationPo();
    }

    @Override
    public List<BatteryEkeyRelationPo> selectBatchIds(Collection<? extends Serializable> idList) {
        return null;
    }

    @Override
    public List<BatteryEkeyRelationPo> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return null;
    }

    /* Started by AICoder, pid:y6038x0376w67971432e082b90dd120af811424f */
    @Override
    public List<BatteryEkeyRelationPo> selectList(Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        if (identification.equals("normal"))
        {
            List<BatteryEkeyRelationPo> batteryEkeyRelationPos = new ArrayList<>();
            BatteryEkeyRelationPo relationPo = new BatteryEkeyRelationPo();
            relationPo.setMoId("r32.uedm.device.battery1");
            relationPo.setEkey("ekey123");
            relationPo.setCreator("admin");
            relationPo.setGmtCreate("2024-10-31 00:00:12");
            batteryEkeyRelationPos.add(relationPo);
            return batteryEkeyRelationPos;
        }
        if (identification.equals("empty"))
        {
            return new ArrayList<>();
        }
        return null;
    }
    /* Ended by AICoder, pid:y6038x0376w67971432e082b90dd120af811424f */

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<BatteryEkeyRelationPo>> P selectPage(P page, Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<BatteryEkeyRelationPo> queryWrapper) {
        return null;
    }

    @Override
    public Integer updateBatch(List<BatteryEkeyRelationPo> updateBatch) {
        return null;
    }
}
