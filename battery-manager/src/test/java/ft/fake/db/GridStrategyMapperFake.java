package ft.fake.db;

import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.StrategyCombinationVo;
import com.zte.uedm.battery.bean.scopeStrategy.PriceIntervalStrategyBean;
import com.zte.uedm.battery.bean.scopeStrategy.PriceStrategySynBatteryBean;
import com.zte.uedm.battery.bean.scopeStrategy.TieredPriceStrategyBean;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.pv.bean.ScopeStrategyDetailBean;

import java.util.ArrayList;
import java.util.List;

public class GridStrategyMapperFake implements GridStrategyMapper {
    @Override
    public List<ScopeStrategyResponseBean> queryList(ScopeStrategyQueryBean queryBean) {
        return null;
    }

    @Override
    public List<ScopeStrategyResponseBean> selectByCondition(List<String> list) {
        return null;
    }

    @Override
    public Integer add(ScopeStrategyRequestBean bean) {
        return null;
    }

    @Override
    public Integer edit(ScopeStrategyRequestBean bean) {
        return null;
    }

    @Override
    public ScopeStrategyResponseBean selectById(String id) {
        ScopeStrategyResponseBean scopeStrategyResponseBean = new ScopeStrategyResponseBean();
        scopeStrategyResponseBean.setId("74c71b26-9983-440d-841d-dacf22a20443");
        scopeStrategyResponseBean.setName("performance-group-0");
        scopeStrategyResponseBean.setLogicGroup("[\"RealGroup-qtm1xi\"]");
        scopeStrategyResponseBean.setGmtCreate("2023-12-25 19:49:06");
        scopeStrategyResponseBean.setEnergyType("0");
        return scopeStrategyResponseBean;
    }

    @Override
    public Integer deleteById(String id) {
        return null;
    }

    @Override
    public List<ScopeStrategyResponseBean> selectScopeStrategyExceptId(String id, String energyType) {
        return null;
    }

    @Override
    public List<SeasonStrategyBean> queryByScopeId(String scopeId) {
        return null;
    }

    @Override
    public Integer addSeasonStrategy(SeasonStrategyBean bean) {
        return null;
    }

    @Override
    public Integer addIntervalStrategy(List<IntervalStrategyBean> list) {
        return null;
    }

    @Override
    public Integer addIntervalStrategyDetail(List<IntervalStrategyDetailBean> list) {
        return null;
    }

    @Override
    public SeasonStrategyBean queryById(String id) {
        if ("b5e9af82-136d-4335-9cdb-1a4aeee283c3".equals(id)) {
            SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
            seasonStrategyBean.setId("b5e9af82-136d-4335-9cdb-1a4aeee283c3");
            seasonStrategyBean.setScopeStrategyId("74c71b26-9983-440d-841d-dacf22a20443");
            seasonStrategyBean.setStatus(1);
            seasonStrategyBean.setEffectiveTime("2023-09-23");
            seasonStrategyBean.setGmtCreate("2023-09-22 17:02:54");
            seasonStrategyBean.setGmtModified("2023-09-23 00:00:00");
            return seasonStrategyBean;
        }
        return null;
    }

    @Override
    public Integer editSeasonStrategy(SeasonStrategyBean bean) {
        return null;
    }

    @Override
    public List<String> queryIntervalStrategyIdSBySeasonId(String seasonId) {
        return null;
    }

    @Override
    public Integer deleteseasonStrategyById(String id) {
        return null;
    }

    @Override
    public Integer deleteIntervalStrategyBySeasonId(String seasonId) {
        return null;
    }

    @Override
    public Integer deleteIntervalStrategyDetailByIntervalIds(List<String> intervalStrategyIds) {
        return null;
    }

    @Override
    public List<IntervalStrategyBean> selectIntervalStrategyBySeasonId(String seasonId) {
        ArrayList<IntervalStrategyBean> intervalStrategyBeans = new ArrayList<>();
        if ("b5e9af82-136d-4335-9cdb-1a4aeee283c3".equals(seasonId)){
            IntervalStrategyBean intervalStrategyBean = new IntervalStrategyBean();
            intervalStrategyBean.setId("fd08ea0d-936d-4aa0-a6c6-b72203b122b3");
            intervalStrategyBean.setSeasonStrategyId("b5e9af82-136d-4335-9cdb-1a4aeee283c3");
            intervalStrategyBean.setMode(0);
            intervalStrategyBean.setStartDate("01-01");
            intervalStrategyBean.setEndDate("12-31");
            intervalStrategyBean.setRemark("");
            intervalStrategyBean.setGmtCreate("2023-09-22 17:02:54");
            intervalStrategyBeans.add(intervalStrategyBean);
        }
        return intervalStrategyBeans;
    }

    @Override
    public List<IntervalStrategyDetailBean> selectIntervalStrategyDetailByIntervalStrategyIds(List<String> intervalStrategyIds) {
        List<IntervalStrategyDetailBean> list = new ArrayList<>();
        if (!intervalStrategyIds.isEmpty()&&intervalStrategyIds.contains("fd08ea0d-936d-4aa0-a6c6-b72203b122b3")){
            IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
            intervalStrategyDetailBean.setId("97224750-27c8-4be8-ac4f-41ec6d37795c");
            intervalStrategyDetailBean.setIntervalStrategyId("fd08ea0d-936d-4aa0-a6c6-b72203b122b3");
            intervalStrategyDetailBean.setWeekStr("[]");
            intervalStrategyDetailBean.setDetail("[{\"beginTime\":\"0:00\",\"endTime\":\"6:00\",\"strategyType\":3},{\"beginTime\":\"6:00\",\"endTime\":\"12:00\",\"strategyType\":2},{\"beginTime\":\"12:00\",\"endTime\":\"18:00\",\"strategyType\":1},{\"beginTime\":\"18:00\",\"endTime\":\"24:00\",\"strategyType\":0}]");
            intervalStrategyDetailBean.setGmtCreate("2023-09-22 17:02:54");
            list.add(intervalStrategyDetailBean);
        }
        return list;
    }

    @Override
    public List<IntervalStrategyDetailBean> selectIntervalStrategyDetailWithSeason(IntervalStrategyQueryBean queryBean) {
        return null;
    }

    @Override
    public List<StrategyCombinationVo> getStrategyCombination(String energyType) {
        List<StrategyCombinationVo> list = new ArrayList<>();
        StrategyCombinationVo strategyCombinationVo = new StrategyCombinationVo();
        strategyCombinationVo.setSeasonStrategyId("b5e9af82-136d-4335-9cdb-1a4aeee283c3");
        list.add(strategyCombinationVo);
        return list;
    }

    @Override
    public List<SeasonStrategyBean> queryIntervalStrategyList(IntervalStrategyQueryBean queryBean) {
        return null;
    }

    @Override
    public List<PendingBean> queryTemplatePriceInterval() {
        return null;
    }

    @Override
    public int batchInsert(List<PeakShiftDeviceStatusBean> list) {
        return 0;
    }

    @Override
    public int batchDelete(List<PeakShiftDeviceStatusBean> list) {
        return 0;
    }

    @Override
    public void updateLogicGroup(SynScopeStrategyBean updateBean) {

    }

    @Override
    public void insertAddBean(SynScopeStrategyBean bean) {

    }

    @Override
    public void batchInsertPriceStrategy(List<PriceStrategySynBatteryBean> list) {

    }

    @Override
    public void batchInsertPriceDetail(List<PriceDetailBean> list) {

    }

    @Override
    public void batchInsertPriceIntervalStrategyList(List<PriceIntervalStrategyBean> list) {

    }

    @Override
    public void batchInsertPriceOfTieredStrategyList(List<TieredPriceStrategyBean> list) {

    }

    @Override
    public void batchInsertSeasonStrategy(List<SeasonStrategySynBatterBean> list) {

    }

    @Override
    public ScopeStrategyResponseBean queryRootInfo(String energyType) {
        return null;
    }

    @Override
    public List<ScopeStrategyResponseBean> queryRootStrategy(List<String> list) {
        return null;
    }

    @Override
    public List<ScopeStrategyDetailBean> selectAllScopeDetail() {
        return null;
    }
}
