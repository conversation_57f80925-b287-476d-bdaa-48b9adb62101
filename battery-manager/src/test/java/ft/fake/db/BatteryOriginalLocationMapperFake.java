package ft.fake.db;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryHistoryLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryOriginalLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryRealTimeLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.BatteryOriginalLocationMapper;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class BatteryOriginalLocationMapperFake implements BatteryOriginalLocationMapper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    public static String identification = "";

    @Override
    public void insertOrUpdateLocation(BatteryOriginalLocationBean bean) {

    }

    @Override
    public BatteryOriginalLocationBean selectById(String monitorObjectId) {
        if ("normal".equals(identification))
        {
            BatteryOriginalLocationBean batteryOriginalLocationBean = new BatteryOriginalLocationBean();
            batteryOriginalLocationBean.setMonitorObjectId("moId");
            batteryOriginalLocationBean.setStatus(1);
            batteryOriginalLocationBean.setUpdateTime("1");
            batteryOriginalLocationBean.setLongitude("111");
            batteryOriginalLocationBean.setLatitude("222");
            return batteryOriginalLocationBean;
        }
        if ("exception".equals(identification))
        {
            throw new RuntimeException();
        }
        return null;
    }

    @Override
    public List<BatteryOriginalLocationBean> selectByIds(List<String> moIdList) {
        List<BatteryOriginalLocationBean> originalLocationBeans = new ArrayList<>();
        BatteryOriginalLocationBean bean = new BatteryOriginalLocationBean();
        bean.setMonitorObjectId("batt-aaa");
        if ("normal".equals(identification))
        {
            bean.setStatus(0);
        }
        else if ("none".equals(identification))
        {
            bean.setStatus(2);
        }
        else if ("null".equals(identification))
        {
            BatteryOriginalLocationBean empty = new BatteryOriginalLocationBean();
            empty.setMonitorObjectId("batt-ccc");
            originalLocationBeans.add(empty);
            bean.setStatus(2);
        }
        else
        {
            bean.setStatus(1);
        }
        bean.setLatitude("22.11");
        bean.setLongitude("41.22");

        BatteryOriginalLocationBean bean2 = new BatteryOriginalLocationBean();
        bean2.setMonitorObjectId("batt-bbb");
        if ("normal".equals(identification))
        {
            bean2.setStatus(0);
        }
        else if ("none".equals(identification))
        {
            bean2.setStatus(2);
        }
        else if ("null".equals(identification))
        {
            BatteryOriginalLocationBean empty = new BatteryOriginalLocationBean();
            empty.setMonitorObjectId("batt-ccc");
            originalLocationBeans.add(empty);
            bean2.setStatus(2);
        }
        else
        {
            bean2.setStatus(1);
        }
        bean2.setLatitude("22.11");
        bean2.setLongitude("41.22");
        originalLocationBeans.add(bean);
        originalLocationBeans.add(bean2);
        return originalLocationBeans;
    }


    @Override
    public void insertOrUpdateLocationBatch(List<BatteryOriginalLocationBean> beanList) {
        if("error".equals(FakeBranchFlag.FLAG)) {
            throw new RuntimeException("");
        }
    }

    @Override
    public void insertOrUpdateHistoryLocation(BatteryHistoryLocationBean bean) {
        if("error".equals(FakeBranchFlag.FLAG)) {
            throw new RuntimeException("");
        }
    }

    @Override
    public void insertOrUpdateRealLocationBatch(List<BatteryRealTimeLocationBean> beanList) throws UedmException {
        if("error".equals(FakeBranchFlag.FLAG)) {
            throw new RuntimeException("");
        }
    }

    @Override
    public void setHistoryTime(BatteryHistoryLocationBean batteryHistoryLocationBeans) {
        if("error".equals(FakeBranchFlag.FLAG)) {
            throw new RuntimeException("");
        }
    }

    @Override
    public BatteryRealTimeLocationBean getRealLocation(String moId) {
        return null;
    }

    @Override
    public List<BatteryRealTimeLocationBean> getAllRealLocation() {
        return null;
    }

    @Override
    public List<BatteryHistoryLocationBean> getAllHistoryLocation(String moId, String startTime, String endTime) {
        return null;
    }
}
