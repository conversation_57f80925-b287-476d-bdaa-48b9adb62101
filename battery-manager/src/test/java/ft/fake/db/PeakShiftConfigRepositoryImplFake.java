package ft.fake.db;

import com.zte.uedm.battery.a_infrastructure.repository.peakshift.persistence.PeakShiftConfigRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftConfigPo;

import java.util.Arrays;
import java.util.List;

public class PeakShiftConfigRepositoryImplFake extends PeakShiftConfigRepositoryImpl {
    public List<PeakShiftConfigPo> selectAllPeakShiftConfig() {
        PeakShiftConfigPo p1 = new PeakShiftConfigPo();
        p1.setDeviceId("d1");
        p1.setStrategyType("1");
        PeakShiftConfigPo p2 = new PeakShiftConfigPo();
        p2.setDeviceId("d2");
        p2.setStrategyType("1");
        return Arrays.asList(p1, p2);
    }
}
