package ft;

import com.alibaba.fastjson.JSON;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.battery.a_infrastructure.kafka.UedmLogMsgHandlerImpl;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.OperationLogMapper;
import com.zte.uedm.common.bean.KafkaBean;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.SpringUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ SpringUtil.class })
public class UedmLogMsgHandlerImplFTest {
    @InjectMocks
    private UedmLogMsgHandlerImpl uedmLogMsgHandler;
    @Mock
    private OperationLogMapper operationLogMapper;

    @Before
    public void setup() throws ClassNotFoundException, IllegalAccessException, InstantiationException, UedmException {
        FtMockitoAnnotations.initMocks(this);
        I18nUtils i18nUtils = Mockito.mock(I18nUtils.class);
        PowerMockito.mockStatic(SpringUtil.class);
        when(SpringUtil.getBean(Mockito.eq(I18nUtils.class))).thenReturn(i18nUtils);
    }

    @Test
    public void UEDM_354291_given_kafka信息中提取logBean失败_when_收到设防结果的kafka消息_then_不调用更新数据库(){
        //
        //given
        //收到设防结果的kafka消息

        //when

        //then
        //
        uedmLogMsgHandler.onMsg("");
        Mockito.verifyZeroInteractions(operationLogMapper);



    }

    @Test
    public void UEDM_354289_given_kafka信息中提取logBean成功_kafka的action为update_日志id不为空_logBean状态为空_when_收到设防结果的kafka消息_then_不调用更新数据库(){
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("update");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verifyZeroInteractions(operationLogMapper);
    }

    @Test
    public void UEDM_354283_given_kafka信息中提取logBean成功_kafka的action为update_日志id为空_when_收到设防结果的kafka消息_then_不调用更新数据库(){
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("update");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verifyZeroInteractions(operationLogMapper);

    }

    @Test
    public void UEDM_354295_given_kafka信息中提取logBean成功_kafka的action为update_日志id不为空_logBean状态不为空_logBean的更新时间为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("update");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).updateOperationLog(Mockito.any());
    }

    @Test
    public void UEDM_354297_given_kafka信息中提取logBean成功_kafka的action为update_日志id不为空_logBean状态不为空_logBean的更新时间不为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        operationLogBean.setUpdateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("update");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).updateOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354315_given_kafka信息中提取logBean成功_kafka的action为create_日志id为空_为日志id生成UUID_logBean的创建时间为空_logBean的更新时间为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setStatus(1);
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());
    }

    @Test
    public void UEDM_354307_given_kafka信息中提取logBean成功_kafka的action为create_日志id为空_为日志id生成UUID_logBean的创建时间为空_logBean的更新时间不为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setStatus(1);
        operationLogBean.setUpdateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354305_given_kafka信息中提取logBean成功_kafka的action为create_日志id为空_为日志id生成UUID_logBean的创建时间不为空_logBean的更新时间为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setStatus(1);
        operationLogBean.setCreateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354303_given_kafka信息中提取logBean成功_kafka的action为create_日志id为空_为日志id生成UUID_logBean的创建时间不为空_logBean的更新时间不为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setStatus(1);
        operationLogBean.setCreateTime("2024-06-07 00:00:00");
        operationLogBean.setUpdateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354300_given_kafka信息中提取logBean成功_kafka的action为create_日志id不为空_logBean的创建时间为空_logBean的更新时间为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354309_given_kafka信息中提取logBean成功_kafka的action为create_日志id不为空_logBean的创建时间为空_logBean的更新时间不为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        operationLogBean.setUpdateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354321_given_kafka信息中提取logBean成功_kafka的action为create_日志id不为空_logBean的创建时间不为空_logBean的更新时间为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        operationLogBean.setCreateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }

    @Test
    public void UEDM_354323_given_kafka信息中提取logBean成功_kafka的action为create_日志id不为空_logBean的创建时间不为空_logBean的更新时间不为空_when_收到设防结果的kafka消息_then_调用更新数据库() throws UedmException {
        //
        //given
        //收到设防结果的kafka消息
        OperationLogBean operationLogBean=new OperationLogBean();
        operationLogBean.setId("log-id");
        operationLogBean.setStatus(1);
        operationLogBean.setCreateTime("2024-06-07 00:00:00");
        operationLogBean.setUpdateTime("2024-06-07 00:00:00");
        KafkaBean kafkaBean=new KafkaBean();
        kafkaBean.setAction("create");
        kafkaBean.setData(operationLogBean);
        String message=JSON.toJSONString(kafkaBean);
        //when
        uedmLogMsgHandler.onMsg(message);
        //then
        //
        Mockito.verify(operationLogMapper,Mockito.atLeastOnce()).addOperationLog(Mockito.any());

    }
}
