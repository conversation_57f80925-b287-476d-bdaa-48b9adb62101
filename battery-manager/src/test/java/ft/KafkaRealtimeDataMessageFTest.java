package ft;
/* Started by AICoder, pid:08e05e92fa89692145aa0b2650c4a070ff246e2f */
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.battery.a_domain.safe.BatteryCfgService;
import com.zte.uedm.battery.a_domain.safe.BatteryLocationService;
import com.zte.uedm.battery.a_domain.safe.OperationLogService;
import com.zte.uedm.battery.a_domain.safe.impl.BatteryCfgServiceImpl;
import com.zte.uedm.battery.a_domain.safe.impl.BatteryLocationServiceImpl;
import com.zte.uedm.battery.a_domain.safe.impl.OperationLogServiceImpl;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.a_infrastructure.kafka.KafkaRealtimeDataMessage;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.*;
import com.zte.uedm.battery.a_infrastructure.kafka.util.UedmScene;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryOriginalLocationBean;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.OperationLogMapper;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.component.redis.service.RedisService;
import com.zte.uedm.service.config.domain.rpc.GroupRpc;
import com.zte.uedm.service.config.infrastructure.rpc.impl.GroupRpcImpl;
import ft.fake.cache.CommonCacheServiceFake;
import ft.fake.cache.ConfigurationServiceFake;
import ft.fake.cache.DeviceCacheManagerFake;
import ft.fake.cache.ResourceBaseCacheManagerFake;
import ft.fake.db.OperationLogMapperFake;
import ft.fake.redis.RedisServiceFake;
import org.ehcache.config.CacheConfiguration;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import com.zte.udem.ft.FtMockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import com.zte.oes.dexcloud.redis.redisson.service.RedissonService;
import javax.annotation.Resource;

import java.lang.reflect.Field;
import java.util.Map;

import static org.mockito.Mockito.mock;

public class KafkaRealtimeDataMessageFTest {

    @InjectMocks
    private KafkaRealtimeDataMessage kafkaRealtimeDataMessage;
    @Resource
    private ResourceBaseCacheManager resourceBaseCacheManager = new ResourceBaseCacheManagerFake();
    @Resource
    private JsonService jsonService = new JsonService();
    @Resource
    private BatteryCfgService batteryCfgService = new BatteryCfgServiceImpl();
    @Resource
    private BatteryLocationService batteryLocationService = new BatteryLocationServiceImpl();
    @Resource
    private OperationLogService operationLogService = new OperationLogServiceImpl();
    @Resource
    private DateTimeService dateTimeService = new DateTimeService();
    @Resource
    private CommonCacheService commonCacheService = new CommonCacheServiceFake();
    @Resource
    private RedisService redisService = new RedisServiceFake();
    //    @Resource
//    private KafkaUtils kafkaUtils = new KafkaUtils();
    @Resource
    private GroupCacheManager groupCacheManager = new GroupCacheManager();
    @Resource
    private FieldCacheManager fieldCacheManager = new FieldCacheManager();
    @Resource
    private DeviceCacheManager deviceCacheManager = new DeviceCacheManagerFake();
    @Resource
    private CollectorCacheManager collectorCacheManager = new CollectorCacheManager();
    @Resource
    private MocCacheManager mocCacheManager = new MocCacheManager();
    @Resource
    private CacheManager commonCaffeineCacheManager = new CaffeineCacheManager();
    @Resource
    private ConfigurationService configurationService = new ConfigurationServiceFake();
    @Resource
    private GroupRpc groupRpc = new GroupRpcImpl();
    @Resource
    private OperationLogMapper operationLogMapper = new OperationLogMapperFake();
    @Mock
    private MessageSenderService msgSenderService;
    @Mock
    private CacheConfiguration cacheConfiguration;
    @Mock
    private RedissonService redissonService;
    @Mock
    private UedmScene uedmScene;


    @Before
    public void setup() throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void UEDM_354944_given_kafka不包含电池数据_when_收到kafka实时数据_then_不调用增加电池实时数据缓存() throws UedmException {
        // 测试逻辑
        String msg = "{\n" +
                "  \"timestamp\": 1716808457137,\n" +
                "  \"items\": [\n" +
                "\t\t{\n" +
                "\t\t  \"moId\": \"r32.uedm.device-54mxbxgwg6\",  \n" +
                "\t\t  \"data\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t  \"curAriseTime\": null,\n" +
                "\t\t\t\t  \"preValue\": \"0\",\n" +
                "\t\t\t\t  \"smpId\": \"supply-duration.week.distmains_001\",\n" +
                "\t\t\t\t  \"preAriseTime\": null,\n" +
                "\t\t\t\t  \"state\": 0,\n" +
                "\t\t\t\t  \"invalidTime\": \"2024-05-15 09:00:43\",\n" +
                "\t\t\t\t  \"value\": \"1\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}";
        ResourceBaseCacheManagerFake.identification = "batteryEmpty";
        kafkaRealtimeDataMessage.onMsg(msg);
        KafkaRealtimeDataMessage mock = mock(kafkaRealtimeDataMessage.getClass());
//        Mockito.verify(mock,Mockito.never()).dealItem(Mockito.any(),Mockito.any());
    }

    @Test
    public void UEDM_354945_given_kafka包含电池数据_查询安全区域为空_when_收到kafka实时数据_then_不调用处理实时数据方法() throws UedmException {
        String msg = "{\n" +
                "  \"timestamp\": 1716808457137,\n" +
                "  \"items\": [\n" +
                "\t\t{\n" +
                "\t\t  \"moId\": \"r32.uedm.device-54mxbxgwg6\",  \n" +
                "\t\t  \"data\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t  \"curAriseTime\": null,\n" +
                "\t\t\t\t  \"preValue\": \"0\",\n" +
                "\t\t\t\t  \"smpId\": \"supply-duration.week.distmains_001\",\n" +
                "\t\t\t\t  \"preAriseTime\": null,\n" +
                "\t\t\t\t  \"state\": 0,\n" +
                "\t\t\t\t  \"invalidTime\": \"2024-05-15 09:00:43\",\n" +
                "\t\t\t\t  \"value\": \"1\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "null";
        kafkaRealtimeDataMessage.onMsg(msg);
        KafkaRealtimeDataMessage mock = mock(kafkaRealtimeDataMessage.getClass());
//        Mockito.verify(mock,Mockito.never()).dealItem(Mockito.any(),Mockito.any());
    }

    @Test
    public void UEDM_354948_given_kafka包含电池数据_查询安全区域不为空_经度为空_when_收到kafka实时数据_then_不调用处理实时数据方法() throws UedmException {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        KafkaRealtimeDataMessage mock = mock(kafkaRealtimeDataMessage.getClass());
//        Mockito.verify(mock,Mockito.never()).dealUnlockRecord(Mockito.any(),Mockito.any());
    }

    @Test
    public void test_msgIsEmpty() throws UedmException {
        String msg = "";
        ResourceBaseCacheManagerFake.identification = "batteryEmpty";
        kafkaRealtimeDataMessage.onMsg(msg);
        KafkaRealtimeDataMessage mock = mock(kafkaRealtimeDataMessage.getClass());
//        Mockito.verify(mock,Mockito.never()).dealItem(Mockito.any(),Mockito.any());
    }

    @Test
    public void UEDM_354949_given_kafka包含电池数据_查询安全区域不为空_纬度为空_when_收到kafka实时数据_then_不调用处理解锁记录方法() throws UedmException {
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        KafkaRealtimeDataMessage mock = mock(kafkaRealtimeDataMessage.getClass());
//        Mockito.verify(mock,Mockito.never()).dealUnlockRecord(Mockito.any(),Mockito.any());
    }

    @Test
    public void UEDM_354952_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在或者实时数据不为解锁_when_收到kafka实时数据_then_不更新解锁操作记录() {
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        OperationLogService mock = mock(operationLogService.getClass());
        Mockito.verify(mock,Mockito.never()).updateLog(Mockito.any());
    }

    @Test
    public void UEDM_354954_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据为空_当前位置数据存入电池位置map_电池位置map不大于1000_when_收到kafka实时数据_then_不调插入方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-51mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryLocationService mock = mock(batteryLocationService.getClass());
        Mockito.verify(mock,Mockito.never()).addOrUpdateLocationBatch(Mockito.any());
    }

    @Test
    public void UEDM_354956_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据为空_当前位置数据存入电池位置map_电池位置map大于1000_when_收到kafka实时数据_then_调插入方法() throws NoSuchFieldException, IllegalAccessException {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        DeviceCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";

        /* Started by AICoder, pid:g1760i8df4s8d2114eda09b50094bc195e263345 */

        // 获取BATT_LOCATION_MAP字段的Field对象
        Field battLocationMapField = kafkaRealtimeDataMessage.getClass().getDeclaredField("BATT_LOCATION_MAP");

        // 确保字段是可访问的
        battLocationMapField.setAccessible(true);

        // 获取当前的BATT_LOCATION_MAP值
        Map<String, BatteryOriginalLocationBean> battLocationMap = (Map<String, BatteryOriginalLocationBean>) battLocationMapField.get(kafkaRealtimeDataMessage);
        battLocationMap.clear();
        // 向HashMap中添加1000条数据
        for (int i = 1; i < 1000; i++) {
            BatteryOriginalLocationBean batteryOriginalLocationBean = new BatteryOriginalLocationBean();
            batteryOriginalLocationBean.setMonitorObjectId(i + "");
            battLocationMap.put("key" + i, batteryOriginalLocationBean);
        }
        /* Ended by AICoder, pid:g1760i8df4s8d2114eda09b50094bc195e263345 */
        kafkaRealtimeDataMessage.onMsg(msg);
        Assert.assertEquals(999,battLocationMap.size());
    }

    @Test
    public void UEDM_354958_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据无效_电池移动距离大于安全距离_状态偷盗未变化_when_收到kafka实时数据_then_不调用更新状态方法(){
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-52mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryLocationService mock = mock(batteryLocationService.getClass());
        Mockito.verify(mock,Mockito.never()).addOrUpdateLocation(Mockito.any());
    }

    @Test
    public void UEDM_355068_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据无效_电池移动距离大于安全距离_状态偷盗未变化_when_收到kafka实时数据_then_不调用更新状态方法() {
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        DeviceCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryOriginalLocationBean cache = commonCaffeineCacheManager.getCache(GlobalConstants.BATT_LOCATION_CACHE_NAME).get("r32.uedm.device-54mxbxgwg6",BatteryOriginalLocationBean.class);
        Assert.assertEquals(Integer.valueOf(2),cache.getStatus());
    }

    @Test
    public void UEDM_354960_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据无效_电池移动距离不大于安全距离_状态偷盗变化_when_收到kafka实时数据_then_调用更新状态方法() throws NoSuchFieldException {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        DeviceCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryOriginalLocationBean cache = commonCaffeineCacheManager.getCache(GlobalConstants.BATT_LOCATION_CACHE_NAME).get("r32.uedm.device-54mxbxgwg6",BatteryOriginalLocationBean.class);
        System.out.println();
        Assert.assertEquals(Integer.valueOf(2),cache.getStatus());
    }

    @Test
    public void UEDM_354962_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据有效_电池移动距离大于安全距离_状态偷盗未变化_when_收到kafka实时数据_then_不调用更新状态方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-52mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryLocationService mock = mock(batteryLocationService.getClass());
        Mockito.verify(mock,Mockito.never()).addOrUpdateLocation(Mockito.any());
    }

    @Test
    public void UEDM_354964_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据有效_电池移动距离大于安全距离_状态偷盗变化_when_收到kafka实时数据_then_调用更新状态方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"80\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"80\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryLocationService mock = mock(batteryLocationService.getClass());
        Mockito.verify(mock,Mockito.never()).addOrUpdateLocation(Mockito.any());
    }

    @Test
    public void UEDM_354965_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据有效_电池移动距离不大于安全距离_状态偷盗未变化_when_收到kafka实时数据_then_不调用更新状态方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"21\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"121\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        DeviceCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryOriginalLocationBean cache = commonCaffeineCacheManager.getCache(GlobalConstants.BATT_LOCATION_CACHE_NAME).get("r32.uedm.device-54mxbxgwg6",BatteryOriginalLocationBean.class);
        Assert.assertEquals(Integer.valueOf(2),cache.getStatus());
    }

    @Test
    public void UEDM_354968_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据有效_电池移动距离不大于安全距离_状态偷盗变化_when_收到kafka实时数据_then_调用更新状态方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-56mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"120\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"-82\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"batt.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryLocationService mock = mock(batteryLocationService.getClass());
        Mockito.verify(mock,Mockito.never()).addOrUpdateLocation(Mockito.any());
    }

    @Test
    public void UEDM_354970_given_kafka包含电池数据_查询安全区域不为空_经纬度不为空_处理中的操作记录不存在且实时数据不为解锁_缓存查询电池位置数据不为空_经纬度数据有效_电池移动距离不大于安全距离_状态偷盗变化_when_收到kafka实时数据_then_调用更新状态方法() {
        // 测试逻辑
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-54mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"120\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"-82\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0.0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        kafkaRealtimeDataMessage.onMsg(msg);
        BatteryOriginalLocationBean cache = commonCaffeineCacheManager.getCache(GlobalConstants.BATT_LOCATION_CACHE_NAME).get("r32.uedm.device-54mxbxgwg6",BatteryOriginalLocationBean.class);
        Assert.assertEquals(Integer.valueOf(0),cache.getStatus());
    }


    @Test
    public void consumeLocationCacheTest_WhenNormal() throws Exception{
        Field batteryMap = KafkaRealtimeDataMessage.class.getDeclaredField("BATT_LOCATION_MAP");
        batteryMap.setAccessible(true);
        Map<String,BatteryOriginalLocationBean> map = (Map<String,BatteryOriginalLocationBean>)batteryMap.get(new KafkaRealtimeDataMessage());
        for (int i = 0; i < 10; i++) {
            map.put(String.valueOf(i),new BatteryOriginalLocationBean());
        }
        FakeBranchFlag.setFLAG("error");
        kafkaRealtimeDataMessage.consumeLocationCache();
        Thread.sleep(7000L);

        Assert.assertEquals(10,map.size());
    }

    @Test
    public void judgeSiteSceneTest() throws NoSuchFieldException, IllegalAccessException {
        String msg = "{\"timestamp\":1716808457137,\"items\":[{\"moId\":\"r32.uedm.device-515mxbxgwg6\",\"data\":[{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"supply-duration.week.distmains_001\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"1\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.longitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"66\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.latitude\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"2\"},{\"curAriseTime\":null,\"preValue\":\"0\",\"smpId\":\"battery.missing.alarm\",\"preAriseTime\":null,\"state\":0,\"invalidTime\":\"2024-05-1509:00:43\",\"value\":\"0\"}]}]}";
        ResourceBaseCacheManagerFake.identification = "battery";
        CommonCacheServiceFake.identification = "area";
        Mockito.doReturn(true).when(uedmScene).judgeSiteScene();
        kafkaRealtimeDataMessage.onMsg(msg);
        Assert.assertEquals(null,commonCaffeineCacheManager.getCache("battery_security_ultra_safe_area").get("r32.uedm.device-515mxbxgwg6"));
    }
    @Test
    public void bean_test() throws Exception {
        PojoTestUtil.TestForPojo(Datum.class);
        PojoTestUtil.TestForPojo(DisplayName.class);
        PojoTestUtil.TestForPojo(ExtendField.class);
        PojoTestUtil.TestForPojo(ImopAlarmClearItem.class);
        PojoTestUtil.TestForPojo(ImopAlarmRaiseItem.class);
        PojoTestUtil.TestForPojo(Item.class);
        PojoTestUtil.TestForPojo(KafkaStandardMpData.class);
    }
}

/* Ended by AICoder, pid:08e05e92fa89692145aa0b2650c4a070ff246e2f */
