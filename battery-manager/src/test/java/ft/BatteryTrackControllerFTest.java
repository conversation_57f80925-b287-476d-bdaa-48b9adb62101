package ft;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.oes.dexcloud.redis.redisson.service.RedissonService;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.battery.a_domain.safe.impl.*;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.a_infrastructure.safe.bean.*;
import com.zte.uedm.battery.a_infrastructure.safe.common.utils.BatteryTrackManager;
import com.zte.uedm.battery.a_infrastructure.safe.repository.mapper.*;
import com.zte.uedm.battery.a_interfaces.safe.web.BatteryTrackController;
import com.zte.uedm.battery.a_interfaces.safe.web.dto.BatteryBatchOperationResponse;
import com.zte.uedm.battery.a_interfaces.safe.web.vo.BatteryUnlockRequest;
import com.zte.uedm.battery.domain.RemoteControlDomain;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.util.FakeBranchFlag;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.SpringUtil;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.service.mp.api.standard.StandardDataService;
import com.zte.uedm.service.pma.api.PmaQueryService;
import ft.fake.BatteryTrackServiceImplFake;
import ft.fake.cache.*;
import ft.fake.db.*;
import ft.fake.redis.BatteryTrackManagerFake;
import ft.fake.redis.RedisServiceFake;
import ft.fake.redis.StandardDataServiceFake;
import ft.fake.rpc.PmaQueryServiceFake;
import ft.fake.rpc.PmaServiceAdFake;
import ft.fake.rpc.gateway.SouthFrameworkRpcFake;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ SpringUtil.class })
public class BatteryTrackControllerFTest {
    /* Started by AICoder, pid:k205e97f5dud39214719087600e1713fb1097506 */
    @InjectMocks
    private BatteryTrackController batteryTrackController;
    @Resource
    private PmaServiceUtils pmaService = new PmaServiceAdFake();
    @Resource
    private CommonCacheService commonCacheService = new CommonCacheServiceFake();
    @Resource
    private CacheManager commonCaffeineCacheManager = new CaffeineCacheManager();
    @Resource
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager = new ResourceCollectorRelationCacheManagerFake();
    @Resource
    private CollectorCacheManager collectorCacheManager = new CollectorCacheManagerFake();
    @Resource
    private BatteryCfgMapper batteryCfgMapper = new BatteryCfgMapperFake();
    @Resource
    private OperationLogMapper operationLogMapper = new OperationLogMapperFake();
    @Resource
    private BatteryOriginalLocationMapper batteryOriginalLocationMapper = new BatteryOriginalLocationMapperFake();
    @Resource
    private ResourceBaseCacheManager resourceBaseCacheManager = new ResourceBaseCacheManagerFake();
    @Resource
    private DeviceCacheManager deviceCacheManager = new DeviceCacheManagerFake();
    @Resource
    private FieldCacheManager fieldCacheManager = new FieldCacheManagerFake();
    @Resource
    private StandardDataService standardDataService = new StandardDataServiceFake();
    @Resource
    private BatteryTrackOverviewMapper batteryTrackOverviewMapper = new BatteryTrackOverviewMapperFake();
    @Resource
    private BatteryTrackStatusMapper batteryTrackStatusMapper = new BatteryTrackStatusMapperFake();
    @Resource
    private BatteryTrackManager batteryTrackManager = new BatteryTrackManagerFake();
    @Mock
    private RemoteControlDomain remoteControlDomain;
    @Resource
    private com.zte.uedm.component.redis.service.RedisService commonRedisService = new RedisServiceFake();
    @Mock
    private RedissonService redissonService;
    @Resource
    private BatteryTrackServiceImpl batteryTrackServiceImpl = new BatteryTrackServiceImplFake();
    @Resource
    private PmaQueryService pmaQueryService = new PmaQueryServiceFake();
    @Mock
    private MessageSenderService messageSenderService;
    @Resource
    private ConfigService configService = new ConfigServiceFake();
    @Resource
    private EkeHistoryRecordMapper ekeHistoryRecordMapper = new EkeHistoryRecordMapperFake();
    @Resource
    private BatteryEkeyRelationMapper batteryEkeyRelationMapper = new BatteryEkeyRelationMapperFake();
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Before
    public void setup() throws ClassNotFoundException, IllegalAccessException, InstantiationException, UedmException {
        FtMockitoAnnotations.initMocks(this);
        I18nUtils i18nUtils = Mockito.mock(I18nUtils.class);
        PowerMockito.mockStatic(SpringUtil.class);
        when(SpringUtil.getBean(Mockito.eq(I18nUtils.class))).thenReturn(i18nUtils);
    }
    /* Ended by AICoder, pid:k205e97f5dud39214719087600e1713fb1097506 */

    @SuppressWarnings("all")
    @Test
    public void UEDM_354974_given_监控对象id不为空_startTime或endTime为空_when_查询电池历史轨迹_then_startTime和endTime为最近三小时时间() throws UedmException {
        List<BatteryTrackBean> result = (List<BatteryTrackBean>) batteryTrackController.gethistoryTrack("mo-batt-123", "",
                "2024-07-15 00:00:00", 0, 0).getData();
        String[] parts = result.get(0).getTime().split(" ~ ");
        String startTimeStr = parts[0];
        String endTimeStr = parts[1];

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, formatter);
        long between = ChronoUnit.HOURS.between(startTime, endTime);
        Assert.assertEquals(3,between);
    }

    @Test
    public void UEDM_354975_given_监控对象id不为空_startTime和endTime不为空_查询经纬度历史数据报错_when_查询电池历史轨迹_then_提示查询历史轨迹失败(){
        try {
            PmaServiceAdFake.identification = "exception";
            batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                    "2024-07-15 00:00:00", 0, 0);
        }
        catch (Exception e)
        {
            Assert.assertEquals("{\"zh_CN\":\"查询历史轨迹失败\",\"en_US\":\"Query history track fail\"}",e.getMessage());
        }
    }

    @Test
    public void UEDM_354978_given_监控对象id不为空_startTime和endTime不为空_查询经纬度历史数据为null_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "null";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354980_given_监控对象id不为空_startTime和endTime不为空_经纬度数据数量不一致_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "abnormal";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354982_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度不为空_电池轨迹数据为空_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "abnormal";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354983_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度不为空_电池轨迹数据不为空_电池轨迹数据为空_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "abnormal";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354986_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度不为空_电池轨迹数据不为空_电池轨迹数据不为空_when_查询电池历史轨迹_then_返回经纬度数据() throws UedmException {
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(1,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354990_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度为空_取轨迹个数不为空_电池轨迹数据不为空_when_查询电池历史轨迹_then_返回经纬度数据() throws UedmException {
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, 0);
        Assert.assertEquals(1,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354992_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度为空_轨迹个数为空_电池轨迹数据为空_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "abnormal";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, null);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354994_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度为空_轨迹个数为空_电池轨迹数据不为空_when_查询电池历史轨迹_then_返回经纬度数据() throws UedmException {
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, null);
        Assert.assertEquals(1,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354987_given_监控对象id不为空_startTime和endTime不为空_历史数据不为null且数量一致_查询粒度为空_取轨迹个数不为空_电池轨迹数据为空_when_查询电池历史轨迹_then_返回空list() throws UedmException {
        PmaServiceAdFake.identification = "abnormal";
        ResponseBean bean = batteryTrackController.gethistoryTrack("mo-batt-123", "2024-07-13 00:00:00",
                "2024-07-15 00:00:00", 0, null);
        Assert.assertEquals(0,bean.getTotal().intValue());
    }

    @Test
    public void UEDM_354972_given_监控对象id为空_when_查询电池历史轨迹_then_返回code负100() throws UedmException {
        ResponseBean bean = batteryTrackController.gethistoryTrack("", "", "", 0, 0);
        Assert.assertEquals(ParameterExceptionEnum.BLANK.getCode(),bean.getCode());
    }

    /* Started by AICoder, pid:caa4e358d7g61b414abd0bf650d55d1c83e05f7c */

    @Test
    public void UEDM_355004_given_监控对象id不为空_标准测点数据包含经纬度数据_查询经纬度成功_经度和纬度时间相等__when_查询电池实时轨迹_then_返回前面查到的经纬度数据() {
        StandardDataServiceFake.identification = "equals";
        ResponseBean result = batteryTrackController.getRealtionTrack("moId");
        Assert.assertEquals(1,result.getTotal().intValue());
    }

    @Test
    public void UEDM_354996_given_监控对象id为空_when_查询电池实时轨迹_then_返回code负100() throws UedmException {
        ResponseBean bean = batteryTrackController.getRealtionTrack("");
        Assert.assertEquals(ParameterExceptionEnum.BLANK.getCode(),bean.getCode());
    }

    @Test
    public void UEDM_354997_given_监控对象id不为空_标准测点数据不包含经纬度数据_when_查询电池实时轨迹_then_返回经纬度数据为空(){
        StandardDataServiceFake.identification = "not equals";
        BatteryTrackBean result = (BatteryTrackBean) batteryTrackController.getRealtionTrack("moId").getData();
        Assert.assertNull(result.getLatitude());
    }

    @Test
    public void UEDM_355000_given_监控对象id不为空_标准测点数据包含经纬度数据_查询经纬度失败_when_查询电池实时轨迹_then_返回经纬度数据为空(){
        StandardDataServiceFake.identification = "not equals";
        BatteryTrackBean result = (BatteryTrackBean) batteryTrackController.getRealtionTrack("moId").getData();
        Assert.assertNull(result.getLatitude());
    }

    @Test
    public void UEDM_355002_given_监控对象id不为空_标准测点数据包含经纬度数据_查询经纬度成功_经度和纬度时间不相等__when_查询电池实时轨迹_then_返回经纬度数据为空(){
        StandardDataServiceFake.identification = "not equals";
        BatteryTrackBean result = (BatteryTrackBean) batteryTrackController.getRealtionTrack("moId").getData();
        Assert.assertNull(result.getLatitude());
    }
    /* Ended by AICoder, pid:caa4e358d7g61b414abd0bf650d55d1c83e05f7c */
    /* Started by AICoder, pid:seb6654f3b1333114ca508e0e0ee7d062455c428 */
    @Test
    public void UEDM_355018_given_轨迹数据查询条件为null_when_电池轨迹数据查询列表_then_返回code为负100() {
        ResponseBean result = batteryTrackController.select(null, null, "zh-CN");
        Assert.assertEquals(ParameterExceptionEnum.BLANK.getCode(),result.getCode());
    }

    @Test
    public void UEDM_355020_given_站点名称为空或者超长_when_电池轨迹数据查询列表_then_返回code为负301() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaabbbbbbbbbbcccccccccccddddddddddddddda");
        ResponseBean result = batteryTrackController.select(requestBean, null, "zh-CN");
        Assert.assertEquals("-301",result.getCode().toString());
    }

    @Test
    public void testSelect_Normal_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        RedisServiceFake.identification = "normal";
        BatteryOriginalLocationMapperFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void testSelect_Filter_Gps_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NULL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        RedisServiceFake.identification = "normal";
        BatteryOriginalLocationMapperFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void testSelect_Alarm_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("1");
        requestBean.setTheftStatus("1");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("1");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("1");
        requestBean.setSort("realGroupName");
        requestBean.setOrder("desc");
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        SouthFrameworkRpcFake.identification = "alarm";
        StandardDataServiceFake.identification = "alarm";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void testSelect_None_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("2");
        requestBean.setTheftStatus("2");
        requestBean.setFortificationState("0");
        requestBean.setMotionState("2");
        requestBean.setAriseTimeEnd("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("2");
        requestBean.setSort("batteryName");
        requestBean.setOrder("asc");
        BatteryOriginalLocationMapperFake.identification = "none";
        RedisServiceFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "none";
        StandardDataServiceFake.identification = "none";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void testSelect_Null_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("2");
        requestBean.setTheftStatus("2");
        requestBean.setFortificationState("2");
        requestBean.setMotionState("2");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("2");
        BatteryOriginalLocationMapperFake.identification = "null";
        SouthFrameworkRpcFake.identification = "none";
        StandardDataServiceFake.identification = "null";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void testSelect_When_Sort_Is_AriseTime_Request() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("2");
        requestBean.setTheftStatus("2");
        requestBean.setFortificationState("2");
        requestBean.setMotionState("2");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("2");
        requestBean.setSort("ariseTime");
        requestBean.setSort("desc");
        BatteryOriginalLocationMapperFake.identification = "null";
        SouthFrameworkRpcFake.identification = "none";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "null";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355022_given_参数校验通过_分组下电池为空__when_电池轨迹数据查询列表_then_返回轨迹数据为空(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355024_given_参数校验通过_分组下电池不为空_电池无轨迹功能_when_电池轨迹数据查询列表_then_结果不包含电池数据(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355026_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池位置bean为空_when_电池轨迹数据查询列表_then_偷盗状态为未知(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355028_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池位置bean不为空_电池位置bean的状态字段为空_when_电池轨迹数据查询列表_then_偷盗状态为未知(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355030_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池位置bean不为空_电池位置bean的状态字段不为空_when_电池轨迹数据查询列表_then_偷盗状态为该状态(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355031_given_参数校验通过_分组下电池不为空_电池有轨迹功能_关联的采集器状态未取到_when_电池轨迹数据查询列表_then_通讯状态为2(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("2");
        requestBean.setTheftStatus("2");
        requestBean.setFortificationState("0");
        requestBean.setMotionState("2");
        requestBean.setAriseTimeEnd("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("2");
        requestBean.setSort("batteryName");
        requestBean.setOrder("asc");
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "none";
        SouthFrameworkRpcFake.identification = "none";
        StandardDataServiceFake.identification = "none";
        RedisServiceFake.identification = "normal";
        BatteryTrackManagerFake.identification = "";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertEquals(0,result.getBatteryList().size());
    }

    @Test
    public void UEDM_355034_given_参数校验通过_分组下电池不为空_电池有轨迹功能_采集器状态为异常或故障_when_电池轨迹数据查询列表_then_通讯状态为1(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("1");
        requestBean.setTheftStatus("1");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("1");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("1");
        requestBean.setSort("realGroupName");
        requestBean.setOrder("desc");
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "";
        SouthFrameworkRpcFake.identification = "alarm";
        StandardDataServiceFake.identification = "alarm";
        BatteryTrackManagerFake.identification = "abnormal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertEquals("1.0",result.getBatteryList().get(0).getCommunicationStatus());
    }

    @Test
    public void UEDM_355036_given_参数校验通过_分组下电池不为空_电池有轨迹功能_采集器状态为正常_when_电池轨迹数据查询列表_then_通讯状态为0(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertEquals("0.0",result.getBatteryList().get(0).getCommunicationStatus());
    }

    @Test
    public void UEDM_355038_given_参数校验通过_分组下电池不为空_电池有轨迹功能__经纬度上报时间为空_when_电池轨迹数据查询列表_then_是否有位置信息为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertFalse(result.getBatteryList().get(0).isCanUnlock());
    }

    @Test
    public void UEDM_355040_given_参数校验通过_分组下电池不为空_电池有轨迹功能_经纬度上报时间不为空_when_电池轨迹数据查询列表_then_是否有位置信息为true(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertTrue(result.getBatteryList().get(0).isHasPositionInfo());
    }

    @Test
    public void UEDM_355042_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池轨迹相关过滤条件为空_when_电池轨迹数据查询列表_then_结果条数加1(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
//        Assert.assertEquals(1,result.getBatteryList().size());
    }

    @Test
    public void UEDM_355076_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池轨迹相关过滤条件满足_when_电池轨迹数据查询列表_then_结果条数加1(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
//        Assert.assertEquals(1,result.getBatteryList().size());
    }

    @Test
    public void UEDM_355044_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池轨迹相关过滤条件存在不满足_when_电池轨迹数据查询列表_then_结果条数不增加(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("2");
        requestBean.setTheftStatus("2");
        requestBean.setFortificationState("2");
        requestBean.setMotionState("2");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("2");
        requestBean.setSort("ariseTime");
        requestBean.setSort("desc");
        BatteryOriginalLocationMapperFake.identification = "null";
        SouthFrameworkRpcFake.identification = "none";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "null";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ResponseBean result = batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN");
        Assert.assertEquals(0,result.getCode().intValue());
    }

    @Test
    public void UEDM_355046_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池关联采集器为空_when_电池轨迹数据查询列表_then_是否具解锁能力为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertFalse(result.getBatteryList().get(0).isCanUnlock());
    }

    @Test
    public void UEDM_355047_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池关联采集器不为空_采集器关联的模型为空_when_电池轨迹数据查询列表_then_是否具解锁能力为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertFalse(result.getBatteryList().get(0).isCanUnlock());
    }

    @Test
    public void UEDM_355050_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池关联采集器不为空_采集器关联的模型不为空_采集器模型包含BCUA或者协议类型包含BMS_when_电池轨迹数据查询列表_then_是否具解锁能力为true(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        RedisServiceFake.identification = "normal";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertTrue(result.getBatteryList().get(0).isHasUnlockAbility());
    }

    @Test
    public void UEDM_355052_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池关联采集器不为空_采集器关联的模型不为空_采集器模型不包含BCUA且协议类型不包含BMS_when_电池轨迹数据查询列表_then_是否具解锁能力为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertEquals(1,result.getBatteryList().size());
    }

    @Test
    public void UEDM_355054_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池关联采集器不为空_电池状态为锁定且状态不为执行中_when_电池轨迹数据查询列表_then_是否能解锁为true(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        RedisServiceFake.identification = "normal";
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        BatteryTrackManagerFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertTrue(result.getBatteryList().get(0).isHasUnlockAbility());
    }

    @Test
    public void UEDM_355056_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池状态不为锁定或者状态在执行中_when_电池轨迹数据查询列表_then_是否能解锁为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
//        Assert.assertEquals(1,result.getBatteryList().size());
    }

    @Test
    public void UEDM_355057_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池状态未设防且状态不为执行中_when_电池轨迹数据查询列表_then_是否能设防为true(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertTrue(result.getBatteryList().get(0).isHasUnlockAbility());
    }

    @Test
    public void UEDM_355060_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池状态不为未设防或者状态在执行中_when_电池轨迹数据查询列表_then_是否能设防为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertFalse(result.getBatteryList().get(0).isCanUnlock());
    }

    @Test
    public void UEDM_355061_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池状态为设防且状态不为执行中_when_电池轨迹数据查询列表_then_是否能撤防为true(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
        Assert.assertTrue(result.getBatteryList().get(0).isHasUnlockAbility());
    }

    @Test
    public void UEDM_355064_given_参数校验通过_分组下电池不为空_电池有轨迹功能_电池状态不为设防或者状态在执行中_when_电池轨迹数据查询列表_then_是否能撤防为false(){
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common","VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa","site-bbb"));
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, mockHttpServletRequest, "zh-CN").getData();
//        Assert.assertEquals(1,result.getBatteryList().size());
    }
    /* Ended by AICoder, pid:seb6654f3b1333114ca508e0e0ee7d062455c428 */

    /* Started by AICoder, pid:x42b911e89vc37914c600bc4b0ea8860af61ac07 */
    private void setupCommonConditions(BatteryTrackSearchRequestBean requestBean) {
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        BatteryOriginalLocationMapperFake.identification = "normal";
        SouthFrameworkRpcFake.identification = "";
        RedisServiceFake.identification = "normal";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    }

    @Test
    public void UEDM_439280_given_电池轨迹电池安防查询_when_状态字段为true_then_支持设防撤防() {
        //
        //given
        //
        ConfigServiceFake.identification = "network";
        StandardDataServiceFake.identification = "marchStatus";
        BatteryTrackSearchRequestBean requestBean = createRequestBean();
        setupCommonConditions(requestBean);
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";

        //when
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, new MockHttpServletRequest(), "zh-CN").getData();

        //then
        Assert.assertTrue(result.getBatteryList().get(0).getSupportNetworkStatus());
    }

    @Test
    public void UEDM_439281_given_电池轨迹电池安防查询_when_状态字段为false_then_不支持设防撤防() {
        //
        //given
        //
        ConfigServiceFake.identification = "network";
        BatteryTrackSearchRequestBean requestBean = createRequestBean();
        setupCommonConditions(requestBean);
        StandardDataServiceFake.identification = "equals";
        BatteryTrackManagerFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";

        //when
        BatteryTrackSearchResponseBean result = (BatteryTrackSearchResponseBean) batteryTrackController.select(requestBean, new MockHttpServletRequest(), "zh-CN").getData();

        //then
        Assert.assertFalse(result.getBatteryList().get(0).getSupportNetworkStatus());
    }

    private BatteryTrackSearchRequestBean createRequestBean() {
        BatteryTrackSearchRequestBean requestBean = new BatteryTrackSearchRequestBean();
        requestBean.setSiteName("site-1");
        requestBean.setSiteLevel(Arrays.asList("common", "VIP"));
        requestBean.setCommunicationStatus("0");
        requestBean.setTheftStatus("0");
        requestBean.setFortificationState("1");
        requestBean.setMotionState("0");
        requestBean.setAriseTimeBegin("2024-06-05 00:11:22");
        requestBean.setPageNo(1);
        requestBean.setPageSize(10);
        requestBean.setLockStatus("0");
        requestBean.setSort("siteName");
        requestBean.setOrder("desc");
        requestBean.setRealGroupIds(Arrays.asList("site-aaa", "site-bbb"));
        return requestBean;
    }

    /* Ended by AICoder, pid:x42b911e89vc37914c600bc4b0ea8860af61ac07 */
    /* Started by AICoder, pid:55540x6b67xc87e14967083e9046662ca888f422 */
    @Test
    public void UEDM_355006_given_监控对象id为空__when_判断电池是否具有轨迹功能_then_返回code为负100() throws UedmException {
        ResponseBean result = batteryTrackController.enableTrackFunction("");
        Assert.assertEquals(ParameterExceptionEnum.BLANK.getCode(),result.getCode());
    }

    @Test
    public void UEDM_355007_given_监控对象id不为空_查询监控对象关联的监控设备为空__when_判断电池是否具有轨迹功能_then_返回result为0() throws UedmException {
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NULL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = "null";
        Integer result = (Integer) batteryTrackController.enableTrackFunction("site-Id").getData();
        Assert.assertEquals("0",result.toString());
    }

    @Test
    public void UEDM_355010_given_监控对象id不为空_查询监控对象关联的监控设备失败__when_判断电池是否具有轨迹功能_then_返回result为0() throws UedmException {
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_EXP;
        Integer result = (Integer) batteryTrackController.enableTrackFunction("site-Id").getData();
        Assert.assertEquals("0",result.toString());
    }

    @Test
    public void UEDM_355012_given_监控对象id不为空_查询监控对象关联的监控设备不为空_设备协议类型为BMS或HCGPS__when_判断电池是否具有轨迹功能_then_返回result为1() throws UedmException {
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        Integer result = (Integer) batteryTrackController.enableTrackFunction("site-Id").getData();
        Assert.assertEquals("1",result.toString());
    }

    @Test
    public void UEDM_355014_given_监控对象id不为空_查询监控对象关联的监控设备不为空_设备协议类型不为BMS或HCGPS__设备模型是BCUA_when_判断电池是否具有轨迹功能_then_返回result为1() throws UedmException {
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NORMAL;
        Integer result = (Integer) batteryTrackController.enableTrackFunction("site-Id").getData();
        Assert.assertEquals("1",result.toString());
    }

    @Test
    public void UEDM_355015_given_监控对象id不为空_查询监控对象关联的监控设备不为空_设备协议类型不为BMS或HCGPS__设备模型不是BCUA_when_判断电池是否具有轨迹功能_then_返回result为0() throws UedmException {
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = FakeBranchFlag.DATA_NULL;
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = "null";
        Integer result = (Integer) batteryTrackController.enableTrackFunction("site-Id").getData();
        Assert.assertEquals("0",result.toString());
    }
    /* Ended by AICoder, pid:55540x6b67xc87e14967083e9046662ca888f422 */

    /* Started by AICoder, pid:m352093cefy3a0e146c20a21207e032432d17ed6 */
    @Test
    public void testSetSafeArea_NullParam() {
        ResponseBean response = batteryTrackController.setSafeArea(null);
        Assert.assertEquals("-1", response.getCode().toString());
    }

    @Test
    public void testSetSafeArea_Success() {
        BatteryCfgMapperFake.identification = "normal";
        ResponseBean response = batteryTrackController.setSafeArea(50);
        Assert.assertEquals(1, response.getTotal().intValue());
    }

    @Test
    public void testGetSafeArea_Exception() {
        try {
            CommonCacheServiceFake.identification = "null";
            BatteryCfgMapperFake.identification = "exception";
            batteryTrackController.getSafeArea();
        }
        catch (Exception e)
        {
            Assert.assertEquals("set safe area error.", e.getMessage());
        }
    }
    /* Ended by AICoder, pid:m352093cefy3a0e146c20a21207e032432d17ed6 */

    /* Started by AICoder, pid:vfe594e67em7a06142be097700586b244cb93abe */
    @Test
    public void testQueryArmRecord() {
        BatteryLogRequestBean logRequestBean = new BatteryLogRequestBean();
        logRequestBean.setId("id");
        logRequestBean.setPageNo(1);
        logRequestBean.setPageSize(10);
        ResponseBean result = batteryTrackController.queryArmRecord(logRequestBean);
        Assert.assertEquals(1,result.getTotal().intValue());
    }

    @Test
    public void testQueryArmRecord_Exception() {
        try {
            batteryTrackController.queryArmRecord(new BatteryLogRequestBean());
        }
        catch (Exception e)
        {
            Assert.assertEquals("query alarm record error.", e.getMessage());
        }
    }

    @Test
    public void testQueryStealRecord_Exception() {
        try {
            batteryTrackController.queryStealRecord(new BatteryLogRequestBean());
        }
        catch (Exception e)
        {
            Assert.assertEquals("query steal record error.", e.getMessage());
        }
    }

    @Test
    public void testQueryStealRecord_Normal() {
        BatteryLogRequestBean logRequestBean = new BatteryLogRequestBean();
        logRequestBean.setId("batt-1");
        logRequestBean.setBeginTime("2024-07-26 00:00:00");
        logRequestBean.setEndTime("2024-07-29 00:00:00");
        logRequestBean.setPageNo(1);
        logRequestBean.setPageSize(10);
        ResponseBean bean = batteryTrackController.queryStealRecord(logRequestBean);
        Assert.assertEquals(1,bean.getTotal().intValue());
    }

    @Test
    public void testSetBatteryLocation() {
        BatteryLocationSetBean locationSetBean = new BatteryLocationSetBean();
        locationSetBean.setId("batt-1");
        locationSetBean.setLatitude("22.11");
        locationSetBean.setLongitude("42.11");
        ResponseBean response = batteryTrackController.setBatteryLocation(locationSetBean);
        Assert.assertEquals(0,response.getTotal().intValue());
    }

    @Test
    public void testSetBatteryLocation_Exception() {
        try {
            batteryTrackController.setBatteryLocation(new BatteryLocationSetBean());
        }
        catch (Exception e)
        {
            Assert.assertEquals("set battery location error.", e.getMessage());
        }
    }

    @Test
    public void testGetBatteryLocation() {
        ResponseBean result = batteryTrackController.getBatteryLocation("batt-1");
        Assert.assertEquals(0,result.getTotal().intValue());
    }

    @Test
    public void testGetUnlockRecord() {
        BatteryLogRequestBean logRequestBean = new BatteryLogRequestBean();
        logRequestBean.setId("batt-1");
        logRequestBean.setBeginTime("2024-07-26 00:00:00");
        logRequestBean.setEndTime("2024-07-29 00:00:00");
        logRequestBean.setPageNo(1);
        logRequestBean.setPageSize(10);
        ResponseBean result = batteryTrackController.getUnlockRecord(logRequestBean,"zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(1,result.getTotal().intValue());
    }

    @Test
    public void testGetUnlockRecord_Exception() {
        try {
            batteryTrackController.getUnlockRecord(new BatteryLogRequestBean(), "zh-CN", new MockHttpServletRequest());
        }
        catch (Exception e)
        {
            Assert.assertEquals("param is blank.", e.getMessage());
        }
    }

    @Test
    public void testUnlock() throws Exception {
        ResponseBean result = batteryTrackController.unlock(new BatteryUnlockRequest(),"zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(0,result.getTotal().intValue());
    }

    @Test
    public void testSetProtectState_Normal() throws Exception {
        BatteryProtectRequestBean protectRequestBean = new BatteryProtectRequestBean();
        List<String> ids = new ArrayList<>();
        ids.add("batt-1");
        ids.add("batt-2");
        protectRequestBean.setProtectState("1");
        protectRequestBean.setIds(ids);
        protectRequestBean.setMsg("success");
        OperationLogMapperFake.identification = "normal";
        ResponseBean bean = batteryTrackController.setProtectState(protectRequestBean,"zh-CN", new MockHttpServletRequest());
        Assert.assertEquals("0", bean.getTotal().toString());
    }

    @Test
    public void testSetProtectState_Abnormal() throws Exception {
        BatteryProtectRequestBean protectRequestBean = new BatteryProtectRequestBean();
        protectRequestBean.setProtectState("2");
        protectRequestBean.setIds(Collections.singletonList("batt-abc"));
        protectRequestBean.setMsg("success");
        ResponseBean bean = batteryTrackController.setProtectState(protectRequestBean,"zh-CN", new MockHttpServletRequest());
        Assert.assertEquals("0", bean.getTotal().toString());
    }

    @Test
    public void testSetProtectState_When_Param_Is_Null() {
        try {
            batteryTrackController.setProtectState(null,"zh-CN", new MockHttpServletRequest());
        }
        catch (Exception e)
        {
            Assert.assertEquals("request body is null", e.getMessage());
        }
    }

    @Test
    public void testSetProtectState_When_Ids_Is_Null() {
        try {
            BatteryProtectRequestBean protectRequestBean = new BatteryProtectRequestBean();
            protectRequestBean.setProtectState("1");
            protectRequestBean.setMsg("success");
            batteryTrackController.setProtectState(protectRequestBean,"zh-CN", new MockHttpServletRequest());
        }
        catch (Exception e)
        {
            Assert.assertEquals("ids is empty", e.getMessage());
        }
    }

    @Test
    public void testSetProtectState_When_ProtectState_Is_Null() {
        try {
            BatteryProtectRequestBean protectRequestBean = new BatteryProtectRequestBean();
            List<String> ids = new ArrayList<>();
            ids.add("batt-1");
            ids.add("batt-2");
            protectRequestBean.setIds(ids);
            protectRequestBean.setMsg("success");
            batteryTrackController.setProtectState(protectRequestBean,"zh-CN", new MockHttpServletRequest());
        }
        catch (Exception e)
        {
            Assert.assertEquals("protectState can not be null", e.getMessage());
        }
    }

    @Test
    public void testSetProtectState_When_Msg_Is_Null() {
        try {
            BatteryProtectRequestBean protectRequestBean = new BatteryProtectRequestBean();
            List<String> ids = new ArrayList<>();
            ids.add("batt-1");
            ids.add("batt-2");
            protectRequestBean.setIds(ids);
            protectRequestBean.setProtectState("0");
            batteryTrackController.setProtectState(protectRequestBean,"zh-CN", new MockHttpServletRequest());
        }
        catch (Exception e)
        {
            Assert.assertEquals("When disarming, the operation information cannot be empty", e.getMessage());
        }
    }
    /* Ended by AICoder, pid:vfe594e67em7a06142be097700586b244cb93abe */

    @Test
    public void UEDM_354273_given_安全区域值为null_when_调用设置安全区域接口_then_设置安全区域失败_返回参数不能为空错误信息(){
        //
        //given
        //安全区域值为null
        Integer safeArea = null;
        //when
        ResponseBean response = batteryTrackController.setSafeArea(safeArea);

        //then
        //
        Assert.assertEquals("-1", response.getCode().toString());
    }

    @Test
    public void UEDM_354351_given_安全区域值不为null_从缓存查出电池安全区域配置为空_从数据库查不到_when_调用设置安全区域接口_then_报错信息电池配置安全区域不存在(){
        //
        //given
        //安全区域值不为null
        Integer safeArea = 2;
        CommonCacheServiceFake.identification = "null";
        BatteryCfgMapperFake.identification = "null";

        //when
        ResponseBean response = batteryTrackController.setSafeArea(safeArea);

        //then
        //
        Assert.assertEquals("-1", response.getCode().toString());
    }

    @Test
    public void UEDM_354268_given_安全区域值不为null_从缓存查出电池安全区域配置为空_从数据库可以查到_when_调用设置安全区域接口_then_返回设置的安全区域值(){
        //
        //given
        //安全区域值不为null
        Integer safeArea = 2;
        CommonCacheServiceFake.identification = "null";
        BatteryCfgMapperFake.identification = "normal";

        //when
        String response = batteryTrackController.setSafeArea(safeArea).getData().toString();

        //then
        //
        Assert.assertEquals("1",  response);
    }

    @Test
    public void UEDM_354361_given_安全区域值不为null_从缓存查出电池安全区域配置不为空_when_调用设置安全区域接口_then_返回设置的安全区域值(){
        //
        //given
        //安全区域值不为null
        Integer safeArea = 2;
        CommonCacheServiceFake.identification = "normal";

        //when
        String response = batteryTrackController.setSafeArea(safeArea).getData().toString();

        //then
        //
        Assert.assertEquals("2",  response);
    }

    @Test
    public void UEDM_354349_given_从缓存查出电池安全区域配置不为空_when_调用查询安全区域接口_then_返回设置的安全区域值(){
        //
        //given
        //从缓存查出电池安全区域配置不为空
        CommonCacheServiceFake.identification = "normal";

        //when
        String response = batteryTrackController.getSafeArea().getData().toString();

        //then
        //
        Assert.assertEquals("2",  response);
    }

    @Test
    public void UEDM_354279_given_从缓存查出电池安全区域配置为空_数据库可以查到_when_调用查询安全区域接口_then_返回设置的安全区域值(){
        //
        //given
        CommonCacheServiceFake.identification = "null";
        BatteryCfgMapperFake.identification = "normal";

        //when
        String response = batteryTrackController.getSafeArea().getData().toString();

        //then
        //
        Assert.assertEquals("1",  response);
    }

    @Test
    public void UEDM_354325_given_从缓存查出电池安全区域配置为空_数据库也查不到_when_调用查询安全区域接口_then_报错信息电池配置安全区域不存在(){
        //
        //given
        //无
        CommonCacheServiceFake.identification = "null";
        BatteryCfgMapperFake.identification = "null";

        //when
        String response = batteryTrackController.getSafeArea().getMessage();

        //then
        //
        Assert.assertEquals("battery cfg safe area does not exists",  response);

    }

    @Test
    public void UEDM_354329_given_参数校验失败_when_查询电池设防记录_then_返回校验失败信息(){
        //
        //given
        //电池设防记录查询id为空
        BatteryLogRequestBean queryBean = new BatteryLogRequestBean();

        //when
        ResponseBean response = batteryTrackController.queryArmRecord(queryBean);

        //then
        //
        Assert.assertEquals(-1,  response.getCode().intValue());

    }

    @Test
    public void UEDM_354345_given_参数校验通过_when_查询电池设防记录_then_返回电池设防记录(){
        //
        //given
        //电池设防记录查询id、pageNo和pageSize都不为空
        OperationLogMapperFake.identification = "normal";
        BatteryLogRequestBean queryBean = new BatteryLogRequestBean();
        queryBean.setId("id");
        queryBean.setPageNo(1);
        queryBean.setPageSize(10);

        //when
        List<OperationLogBean> response = (List<OperationLogBean>) batteryTrackController.queryArmRecord(queryBean).getData();

        //then
        //
        Assert.assertEquals(1,  response.get(0).getStatus().intValue());
    }

    @Test
    public void UEDM_354343_given_参数校验失败_when_查询电池偷盗记录_then_返回校验失败信息(){
        //
        //given
        //电池偷盗记录查询id为空
        BatteryLogRequestBean queryBean = new BatteryLogRequestBean();

        //when
        ResponseBean response = batteryTrackController.queryStealRecord(queryBean);

        //then
        //
        Assert.assertEquals(-1,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354355_given_参数校验通过_when_查询电池偷盗记录_then_返回电池偷盗记录(){
        //
        //given
        //电池偷盗记录查询id、pageNo和pageSize都不为空
        OperationLogMapperFake.identification = "normal";
        BatteryLogRequestBean queryBean = new BatteryLogRequestBean();
        queryBean.setId("id");
        queryBean.setPageNo(1);
        queryBean.setPageSize(10);

        //when
        List<BatteryTheftLogViewBean> response = (List<BatteryTheftLogViewBean>) batteryTrackController.queryStealRecord(queryBean).getData();

        //then
        //
        Assert.assertEquals("123.456",  response.get(0).getOriginalLongitude());
    }

    @Test
    public void UEDM_354353_given_参数校验失败id为空_when_查询电池解锁记录_then_返回校验失败信息参数为空(){
        //
        //given
        //查询条件的id为空
        BatteryLogRequestBean batteryLogRequestBean = new BatteryLogRequestBean();

        //when
        ResponseBean response = batteryTrackController.getUnlockRecord(batteryLogRequestBean,"zh-CN", new MockHttpServletRequest());

        //then
        //
        Assert.assertEquals(-301,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354346_given_参数校验通过_when_查询电池解锁记录_then_返回电池解锁记录(){
        //
        //given
        //查询条件的id、pageNo和pageSize都不为空
        OperationLogMapperFake.identification = "normal";
        BatteryLogRequestBean batteryLogRequestBean = new BatteryLogRequestBean();
        batteryLogRequestBean.setId("1");
        batteryLogRequestBean.setPageNo(1);
        batteryLogRequestBean.setPageSize(10);

        //when
        ResponseBean response = batteryTrackController.getUnlockRecord(batteryLogRequestBean,"zh-CN", new MockHttpServletRequest());

        //then
        //
        Assert.assertEquals(0,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354357_given_参数校验失败_when_设置电池位置_then_返回校验失败信息(){
        //
        //given
        //id为空
        BatteryLocationSetBean queryBean = new BatteryLocationSetBean();

        //when
        ResponseBean response = batteryTrackController.setBatteryLocation(queryBean);

        //then
        //
        Assert.assertEquals(-1,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354299_given_参数校验通过_when_设置电池位置_then_调用插入电池位置到数据库方法(){
        //
        //given
        //id和经纬度不为空
        BatteryLocationSetBean queryBean = new BatteryLocationSetBean();
        queryBean.setId("id");
        queryBean.setLatitude("123");
        queryBean.setLongitude("111");

        //when
        ResponseBean response = batteryTrackController.setBatteryLocation(queryBean);

        //then
        //
        Assert.assertEquals(0,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354310_given_根据id查询数据库的电池位置失败_when_查询电池位置_then_返回code为负1(){
        //
        //given
        //电池id
        BatteryOriginalLocationMapperFake.identification = "exception";
        String id = "id";

        //when
        ResponseBean response = batteryTrackController.getBatteryLocation(id);

        //then
        //
        Assert.assertEquals(-1,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354313_given_根据id查询数据库的电池位置成功_when_查询电池位置_then_返回电池位置信息(){
        //
        //given
        //电池id
        BatteryOriginalLocationMapperFake.identification = "normal";
        String id = "id";

        //when
        ResponseBean response = batteryTrackController.getBatteryLocation(id);

        //then
        //
        Assert.assertEquals(0,  response.getCode().intValue());
    }

    @Test
    public void UEDM_354317_given_从数据库中查询电池轨迹概览列信息为空__when_查询电池轨迹概览维度自定义列_then_返回列信息(){
        //
        //given
        //分页信息和用户名
        BatteryTrackOverviewMapperFake.identification = "null";

        //when
        ResponseBean result = batteryTrackController.selectOverviewConfig(10,1,new MockHttpServletRequest(),"zh-CN");

        //then
        //
        Assert.assertEquals(12,  result.getTotal().intValue());
    }

    @Test
    public void UEDM_354373_given_从数据库中查询电池轨迹概览列信息不为空__when_查询电池轨迹概览维度自定义列_then_返回列信息(){
        //
        //given
        //分页信息和用户名
        BatteryTrackOverviewMapperFake.identification = "normal";

        //when
        ResponseBean result = batteryTrackController.selectOverviewConfig(10,1,new MockHttpServletRequest(),"zh-CN");

        //then
        //
        Assert.assertEquals(1,  result.getTotal().intValue());
    }

    @Test
    public void UEDM_354369_given_从数据库中查询电池轨迹概览列信息查询失败__when_查询电池轨迹概览维度自定义列_then_返回code为负1(){
        //
        //given
        //分页信息和用户名
        BatteryTrackOverviewMapperFake.identification = "exception";

        //when
        ResponseBean result = batteryTrackController.selectOverviewConfig(10,1,new MockHttpServletRequest(),"zh-CN");

        //then
        //
        Assert.assertEquals(-1,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354385_given_更新列为空__when_更新电池轨迹概览维度自定义列_then_返回code为负301(){
        //
        //given
        //更新列信息

        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(new ArrayList<>(),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-301,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354377_given_列排序位置重复__when_更新电池轨迹概览维度自定义列_then_返回code为负302(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:a58aei1d83t4f60146410a4bf0a37b1b98079a13 */
        List<BatteryTrackOverviewUpdateBean> updateBeanList = new ArrayList<>();

        // 创建第一个BatteryTrackOverviewUpdateBean并赋值
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);

        // 创建第二个BatteryTrackOverviewUpdateBean并赋值，sequence字段与bean1相同
        BatteryTrackOverviewUpdateBean bean2 = new BatteryTrackOverviewUpdateBean();
        bean2.setId("bean2");
        bean2.setSequence(1);
        bean2.setEnable(false);

        // 将两个bean添加到List中
        updateBeanList.add(bean1);
        updateBeanList.add(bean2);
        /* Ended by AICoder, pid:a58aei1d83t4f60146410a4bf0a37b1b98079a13 */

        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(updateBeanList, new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354370_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id不包含在枚举列信息里面__when_更新电池轨迹概览维度自定义列_then_返回code为负305(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);
        /* Ended by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewMapperFake.identification = "null";

        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");

        //then
        //
        Assert.assertEquals(-305,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354363_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在枚举列信息里面_更新列位置重复_when_更新电池轨迹概览维度自定义列_then_返回code为负302(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("batteryName");
        bean1.setSequence(13);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "null";
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354375_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_概览纬度校验不可变动失败_when_更新电池轨迹概览维度自定义列_then_返回code为负208(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("batteryName");
        bean1.setSequence(1);
        bean1.setEnable(false);
        BatteryTrackOverviewMapperFake.identification = "null";
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-208,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354359_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_概览纬度校验不可变动通过_更新列到数据库失败_when_更新电池轨迹概览维度自定义列_then_返回code为负200(){
        //
        //given
        //更新列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("batteryName");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "null";
        BatteryTrackOverviewMapperFake.updateIdentification = "exception";
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-200,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354365_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_概览纬度校验不可变动通过_更新列到数据库成功_when_更新电池轨迹概览维度自定义列_then_返回code为0(){
        //
        //given
        //更新列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("batteryName");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "null";
        BatteryTrackOverviewMapperFake.updateIdentification = "";

        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(0,  result.getCode().intValue());

    }

    @Test
    public void UEDM_354383_given_参数校验成功_数据库查询电池轨迹概览列信息不为空_更新列id不包含在查询结果里面__when_更新电池轨迹概览维度自定义列_then_返回code为负305(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "null";
        /* Ended by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-305,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354395_given_参数校验成功_数据库查询电池轨迹概览列信息不为空_更新列id包含在查询结果里面_更新列位置重复_when_更新电池轨迹概览维度自定义列_then_返回code为负302(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(2);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "normal";
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354379_given_参数校验成功_数据库查询电池轨迹概览列信息不为空_更新列id包含在查询结果里面_更新列位置不重复_概览纬度校验不可变动失败_when_更新电池轨迹概览维度自定义列_then_返回code为负208(){
        //
        //given
        //更新列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(false);
        BatteryTrackOverviewMapperFake.identification = "normal";
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-208,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354389_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在查询结果里面_更新列位置不重复_概览纬度校验不可变动通过_更新列到数据库失败_when_更新电池轨迹概览维度自定义列_then_返回code为负200(){
        //
        //given
        //更新列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "normal";
        BatteryTrackOverviewMapperFake.updateIdentification = "exception";
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-200,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354393_given_参数校验成功_数据库查询电池轨迹概览列信息为空_更新列id包含在查询结果里面_更新列位置不重复_概览纬度校验不可变动通过_更新列到数据库成功_when_更新电池轨迹概览维度自定义列_then_返回code为0(){
        //
        //given
        //更新列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackOverviewMapperFake.identification = "normal";
        BatteryTrackOverviewMapperFake.updateIdentification = "";
        //when
        ResponseBean result = batteryTrackController.updateOverviewConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(0,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354381_given_从数据库中查询电池轨迹状态列信息为空__when_查询电池轨迹状态维度自定义列_then_返回列信息(){
        //
        //given
        //分页信息和用户名
        BatteryTrackStatusMapperFake.identification = "null";

        //when
        ResponseBean result = batteryTrackController.selectStatusConfig(10,1,new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(6,  result.getTotal().intValue());
    }

    @Test
    public void UEDM_354399_given_从数据库中查询电池轨迹状态列信息不为空__when_查询电池轨迹状态维度自定义列_then_返回列信息(){
        //
        //given
        //分页信息和用户名
        BatteryTrackStatusMapperFake.identification = "normal";
        //when
        ResponseBean result = batteryTrackController.selectStatusConfig(10,1,new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(1,  result.getTotal().intValue());
    }

    @Test
    public void UEDM_354397_given_从数据库中查询电池轨迹状态列信息查询失败__when_查询电池轨迹状态维度自定义列_then_返回code为负1(){
        //
        //given
        //分页信息和用户名
        BatteryTrackStatusMapperFake.identification = "exception";
        //when
        ResponseBean result = batteryTrackController.selectStatusConfig(10,1,new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-1,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354387_given_更新列为空__when_更新电池轨迹状态维度自定义列_then_返回code为负301(){
        //
        //given
        //更新状态列信息

        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(new ArrayList<>(),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-301,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354249_given_列排序位置重复__when_更新电池轨迹状态维度自定义列_then_返回code为负302(){
        //
        //given
        //更新列状态信息
        /* Started by AICoder, pid:a58aei1d83t4f60146410a4bf0a37b1b98079a13 */
        List<BatteryTrackOverviewUpdateBean> updateBeanList = new ArrayList<>();

        // 创建第一个BatteryTrackOverviewUpdateBean并赋值
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);

        // 创建第二个BatteryTrackOverviewUpdateBean并赋值，sequence字段与bean1相同
        BatteryTrackOverviewUpdateBean bean2 = new BatteryTrackOverviewUpdateBean();
        bean2.setId("bean2");
        bean2.setSequence(1);
        bean2.setEnable(false);

        // 将两个bean添加到List中
        updateBeanList.add(bean1);
        updateBeanList.add(bean2);
        /* Ended by AICoder, pid:a58aei1d83t4f60146410a4bf0a37b1b98079a13 */
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(updateBeanList, new MockHttpServletRequest(),"zh-CN");

        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354254_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id不包含在枚举列信息里面__when_更新电池轨迹状态维度自定义列_then_返回code为负305(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);
        /* Ended by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "";
        BatteryTrackStatusMapperFake.updateIdentification = "";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-305,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354241_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在枚举列信息里面_更新列位置重复_when_更新电池轨迹状态维度自定义列_then_返回code为负302(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("totalNumbers");
        bean1.setSequence(13);
        bean1.setEnable(true);
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "null";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354247_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_状态纬度校验不可变动失败_when_更新电池轨迹状态维度自定义列_then_返回code为负208(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("totalNumbers");
        bean1.setSequence(1);
        bean1.setEnable(false);
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "null";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-208,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354242_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_状态纬度校验不可变动通过_更新列到数据库失败_when_更新电池轨迹状态维度自定义列_then_返回code为负200(){
        //
        //given
        //更新状态列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("totalNumbers");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackStatusMapperFake.identification = "null";
        BatteryTrackStatusMapperFake.updateIdentification = "exception";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-200,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354250_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在枚举列信息里面_更新列位置不重复_状态纬度校验不可变动通过_更新列到数据库成功_when_更新电池轨迹状态维度自定义列_then_返回code为0(){
        //
        //given
        //更新状态列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("totalNumbers");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackStatusMapperFake.identification = "null";
        BatteryTrackStatusMapperFake.updateIdentification = "normal";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(0,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354245_given_参数校验成功_数据库查询电池轨迹状态列信息不为空_更新列id不包含在查询结果里面__when_更新电池轨迹状态维度自定义列_then_返回code为负305(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("bean1");
        bean1.setSequence(1);
        bean1.setEnable(true);
        /* Ended by AICoder, pid:u58aes1d8344f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "null";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-305,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354263_given_参数校验成功_数据库查询电池轨迹状态列信息不为空_更新列id包含在查询结果里面_更新列位置重复_when_更新电池轨迹状态维度自定义列_then_返回code为负302(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(2);
        bean1.setEnable(true);
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "normal";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-302,  result.getCode().intValue());

    }

    @Test
    public void UEDM_354259_given_参数校验成功_数据库查询电池轨迹状态列信息不为空_更新列id包含在查询结果里面_更新列位置不重复_状态纬度校验不可变动失败_when_更新电池轨迹状态维度自定义列_then_返回code为负208(){
        //
        //given
        //更新状态列信息
        /* Started by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(false);
        /* Ended by AICoder, pid:a58ae11d83u4f60146410a4bf0a37b0b98049a13 */
        BatteryTrackStatusMapperFake.identification = "normal";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-208,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354253_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在查询结果里面_更新列位置不重复_状态纬度校验不可变动通过_更新列到数据库失败_when_更新电池轨迹状态维度自定义列_then_返回code为负200(){
        //
        //given
        //更新状态列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackStatusMapperFake.identification = "normal";
        BatteryTrackStatusMapperFake.updateIdentification = "exception";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(-200,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354257_given_参数校验成功_数据库查询电池轨迹状态列信息为空_更新列id包含在查询结果里面_更新列位置不重复_状态纬度校验不可变动通过_更新列到数据库成功_when_更新电池轨迹状态维度自定义列_then_返回code为0(){
        //
        //given
        //更新状态列信息
        BatteryTrackOverviewUpdateBean bean1 = new BatteryTrackOverviewUpdateBean();
        bean1.setId("id");
        bean1.setSequence(1);
        bean1.setEnable(true);
        BatteryTrackStatusMapperFake.identification = "normal";
        BatteryTrackStatusMapperFake.updateIdentification = "normal";
        //when
        ResponseBean result = batteryTrackController.updateStatusConfig(Arrays.asList(bean1),new MockHttpServletRequest(),"zh-CN");
        //then
        //
        Assert.assertEquals(0,  result.getCode().intValue());
    }

    @Test
    public void UEDM_354260_given_设防状态bean为null_when_设置设防状态_then_返回错误信息请求体为空() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = null;
        //when
        ResponseBean responseBean = batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest());
        //then
        //
        Assert.assertEquals("request body is null",  responseBean.getMessage());
    }

    @Test
    public void UEDM_354271_given_设防状态bean不为null_设防的电池监控对象id为空_when_设置设防状态_then_返回错误信息id为空() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        //when
        ResponseBean responseBean = batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest());
        //then
        //
        Assert.assertEquals("ids is empty",  responseBean.getMessage());
    }

    @Test
    public void UEDM_354281_given_设防状态bean不为null_设防的电池监控对象id不为空_设防的设防状态为空_when_设置设防状态_then_返回错误信息设防状态为空() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("batt-aaa"));
        //when
        ResponseBean responseBean = batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest());
        //then
        //
        Assert.assertEquals("protectState can not be null",  responseBean.getMessage());
    }

    @Test
    public void UEDM_354285_given_设防状态bean不为null_设防的电池监控对象id不为空_设防的设防状态不为空_如果为撤防且撤防原因是null_when_设置设防状态_then_返回错误信息撤防必须有原因() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("batt-aaa"));
        queryBean.setProtectState("0.0");
        //when
        ResponseBean responseBean = batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest());
        //then
        //
        Assert.assertEquals("When disarming, the operation information cannot be empty",  responseBean.getMessage());
    }

    @Test
    public void UEDM_354277_given_设防状态bean不为null_设防的电池监控对象id不为空_设防的设防状态不为空_如果不是为撤防或者撤防原因不是null_moId通讯状态查询失败或者不为正常_when_设置设防状态_then_返回电池设防失败信息() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("batt-aaa"));
        queryBean.setProtectState("1.0");
        BatteryTrackManagerFake.identification="abnormal";
        //when
        BatteryBatchOperationResponse responseBean = (BatteryBatchOperationResponse) batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest()).getData();
        //then
        //
        Assert.assertEquals(1,  responseBean.getFailedNum().intValue());
    }

    @Test
    public void UEDM_354293_given_设防状态bean不为null_设防的电池监控对象id不为空_设防的设防状态不为空_如果不是为撤防或者撤防原因不是null_moId通讯状态为正常_设置值与当前值一致_when_设置设防状态_then_返回失败信息() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("batt-aaa"));
        queryBean.setProtectState("1.0");
        BatteryTrackManagerFake.identification="normal";
        //when
        BatteryBatchOperationResponse responseBean = (BatteryBatchOperationResponse) batteryTrackController.setProtectState(queryBean, "aa", new MockHttpServletRequest()).getData();
        //then
        //
        Assert.assertEquals(1,  responseBean.getFailedNum().intValue());
    }

    @Test
    public void UEDM_354287_given_设防状态bean不为null_设防的电池监控对象id不为空_设防的设防状态不为空_如果不是为撤防或者撤防原因不是null_moId通讯状态为正常_设置值与当前值不一致_when_设置设防状态_then_返回成功结果() throws Exception{
        //
        //given
        //设防状态bean
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("batt-aaa"));
        queryBean.setProtectState("0.0");
        queryBean.setMsg("123");
        BatteryTrackManagerFake.identification="normal";
        CollectorCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        ConfigServiceFake.identification = "gps";
        //when
        BatteryBatchOperationResponse responseBean = (BatteryBatchOperationResponse) batteryTrackController.setProtectState(queryBean, "zh", new MockHttpServletRequest()).getData();
        //then
        //
        Assert.assertEquals(1,  responseBean.getSuccessNum().intValue());
    }

    @Test
    public void UEDM_354336_given_电池解锁的监控对象id为空_when_设置电池解锁状态_then_返回总数量为空() throws Exception {
        ResponseBean response = batteryTrackController.unlock(new BatteryUnlockRequest(), "zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(0,response.getTotal().intValue());
    }

    @Test
    public void UEDM_354319_given_电池解锁的监控对象id不为空_电池监控对象通讯状态不为正常_when_设置电池解锁状态_then_失败数量加一() throws Exception {
        BatteryUnlockRequest request = new BatteryUnlockRequest();
        request.setMoIdList(Arrays.asList("batt-123","batt-321"));
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        OperationLogMapperFake.identification = "normal";
        BatteryBatchOperationResponse response = (BatteryBatchOperationResponse) batteryTrackController.unlock(request, "zh-CN", new MockHttpServletRequest()).getData();
        Assert.assertEquals(2,response.getFailedItemList().size());
    }

    @Test
    public void UEDM_354331_given_电池解锁的监控对象id不为空_电池监控对象通讯状态为正常_状态不是锁定_when_设置电池解锁状态_then_失败数量加一() throws Exception {
        BatteryUnlockRequest request = new BatteryUnlockRequest();
        request.setMoIdList(Arrays.asList("batt-aaa","batt-bbb"));
        BatteryTrackManagerFake.identification = "normal";
        OperationLogMapperFake.identification = "normal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        BatteryBatchOperationResponse response = (BatteryBatchOperationResponse) batteryTrackController.unlock(request, "zh-CN", new MockHttpServletRequest()).getData();
        Assert.assertEquals(2,response.getFailedItemList().size());
    }

    @Test
    public void UEDM_354327_given_电池解锁的监控对象id不为空_电池监控对象通讯状态为正常_状态是锁定_已经在执行状态_when_设置电池解锁状态_then_失败数量加一() throws Exception {
        BatteryUnlockRequest request = new BatteryUnlockRequest();
        request.setMoIdList(Arrays.asList("batt-aaa","batt-bbb"));
        BatteryTrackManagerFake.identification = "abnormal";
        DeviceCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        FieldCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        BatteryBatchOperationResponse response = (BatteryBatchOperationResponse) batteryTrackController.unlock(request, "zh-CN", new MockHttpServletRequest()).getData();
        Assert.assertEquals(2,response.getFailedItemList().size());
    }

    @Test
    public void UEDM_354339_given_电池解锁的监控对象id不为空_电池监控对象通讯状态为正常_状态是锁定_不在执行状态_when_设置电池解锁状态_then_成功数量加一() throws Exception {
        BatteryUnlockRequest request = new BatteryUnlockRequest();
        request.setMoIdList(Arrays.asList("batt-aaa","batt-bbb"));
        BatteryTrackManagerFake.identification = "normal";
        RedisServiceFake.identification = "normal";
        OperationLogMapperFake.identification = "";
        BatteryBatchOperationResponse response = (BatteryBatchOperationResponse) batteryTrackController.unlock(request, "zh-CN", new MockHttpServletRequest()).getData();
        Assert.assertEquals(2,response.getSuccessNum().intValue());
    }

    @Test
    public void UEDM_354333_given_电池解锁的监控对象id不为空_电池通讯状态查询异常code为负1_when_设置电池解锁状态_then_返回code为负1(){
        try {
            BatteryUnlockRequest request = new BatteryUnlockRequest();
            request.setMoIdList(Arrays.asList("batt-aaa","batt-bbb"));
            BatteryTrackManagerFake.identification = "abnormal";
            RedisServiceFake.identification = "exception";
            OperationLogMapperFake.identification = "";
            batteryTrackController.unlock(request, "zh-CN", new MockHttpServletRequest()).getData();
        }
        catch (Exception e)
        {
            Assert.assertEquals("-1",e.getMessage());
        }
    }

    @Test
    public void UEDM_354341_given_未读取配置文件中配置的超时时间_when_操作超时监测任务_then_超时时间默认设置48(){
        //
        //given
        //操作超时监测任务

        //when

        //then
        //

    }

    @Test
    public void UEDM_354335_given_读取配置文件中配置的超时时间_状态为处理中的数据为空_when_操作超时监测任务_then_不调用更新操作日志方法(){
        //
        //given
        //操作超时监测任务

        //when

        //then
        //

    }

    @Test
    public void UEDM_354366_given_读取配置文件中配置的超时时间_状态为处理中的数据不为空_更新时间已超时_when_操作超时监测任务_then_调用更新操作日志方法(){
        //
        //given
        //操作超时监测任务

        //when

        //then
        //

    }

    @Test
    public void UEDM_354391_given_读取配置文件中配置的超时时间_状态为处理中的数据不为空_更新时间未超时_when_操作超时监测任务_then_不调用更新操作日志方法(){
        //
        //given
        //操作超时监测任务

        //when

        //then
        //

    }


    @Test
    public void UEDM_438161_given_网络级防盗_设防情况下_when_调用电池撤防及撤防__then_设防数据不为空() throws Exception {
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("r32.uedm.device.battery1","r32.uedm.device.battery2"));
        queryBean.setProtectState("1.0");
        ConfigServiceFake.identification = "network";
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        StandardDataServiceFake.identification = "normal";
        BatteryEkeyRelationMapperFake.identification = "normal";
        batteryTrackController.setProtectState(queryBean, "zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(1,ResourceCollectorRelationCacheManagerFake.collectorResourceMap.size());
    }

    @Test
    public void UEDM_438163_given_网络级防盗_撤防情况下_when_调用电池撤防及撤防__then_撤防数据不为空() throws Exception {
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("r32.uedm.device.battery1","r32.uedm.device.battery2"));
        queryBean.setProtectState("0.0");
        queryBean.setMsg("msg");
        ConfigServiceFake.identification = "network";
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        StandardDataServiceFake.identification = "normal";
        BatteryEkeyRelationMapperFake.identification = "normal";
        batteryTrackController.setProtectState(queryBean, "zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(1,ResourceCollectorRelationCacheManagerFake.collectorResourceMap.size());
    }


    @Test
    public void testSetProtect_When_BatteryEkey_Is_Null() throws Exception {
        BatteryProtectRequestBean queryBean = new BatteryProtectRequestBean();
        queryBean.setIds(Arrays.asList("r32.uedm.device.battery1","r32.uedm.device.battery2"));
        queryBean.setProtectState("0.0");
        queryBean.setMsg("msg");
        ConfigServiceFake.identification = "network";
        ResourceCollectorRelationCacheManagerFake.GET_BY_CONDITION_FLAG = "normal";
        StandardDataServiceFake.identification = "normal";
        BatteryEkeyRelationMapperFake.identification = "empty";
        EkeHistoryRecordMapperFake.identification = "normal";
        batteryTrackController.setProtectState(queryBean, "zh-CN", new MockHttpServletRequest());
        Assert.assertEquals(1,ResourceCollectorRelationCacheManagerFake.collectorResourceMap.size());
    }
}
