/* Started by AICoder, pid:ud73659186abc94147b408bfb108c33629b32194 */
package ft;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.oes.dexcloud.redis.redisson.service.RedissonService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftDistributionService;
import com.zte.uedm.battery.a_domain.cache.provider.FieldCacheDataProvider;
import com.zte.uedm.battery.a_domain.cache.provider.GroupCacheDataProvider;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftDistributionCrudDomainService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.GroupCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.a_interfaces.peakshift.web.PeakShiftStrategyDistributionController;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftDeviceTaskDto;
import com.zte.uedm.battery.bean.PeakShiftGrainQueryBean;
import com.zte.uedm.battery.bean.PeakShiftGrainQueryResultBean;
import com.zte.uedm.battery.controller.PriceStrategyController;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.battery.rpc.impl.PeakShiftConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.PeakShiftGrainQueryService;
import com.zte.uedm.battery.service.PriceStrategyService;
import com.zte.uedm.battery.service.impl.PeakShiftConfigBaseServiceImpl;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.mockito.Mockito.when;

public class PeakShiftGrainQueryServiceImplFTest {
    @InjectMocks
    private PeakShiftStrategyDistributionController peakShiftStrategyDistributionController;
    @Mock
    private HttpServletRequest request;
    @Mock
    private PeakShiftDistributionService peakShiftDistributionService;
    @Mock
    private PeakShiftDistributionCrudDomainService peakShiftDistributionCrudDomainService;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private ConfigService configService;
    @Mock
    private MpRpcImpl mpRpcImpl;
    @Mock
    private AdapterPointDataService adapterPointDataService;
    @Mock
    private RedissonService redissonService;

    @InjectMocks
    private PriceStrategyController priceStrategyController;
    @Mock
    private PriceStrategyService priceStrategyService;
    @Mock
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;
    @Mock
    private GroupCacheManager groupCacheManager;
    @Mock
    private GroupCacheDataProvider groupCacheDataProvider;
    @Mock
    private PeakShiftGrainQueryService peakShiftGrainQueryService;
    @Mock
    private PeakShiftConfigurationManagerRpcImpl peakShiftConfigurationManagerRpcImpl;
    @Mock
    private ResourceBaseCacheManager resourceBaseCacheManager;
    @Mock
    private FieldCacheManager fieldCacheManager;
    @Mock
    private FieldCacheDataProvider fieldCacheDataProvider;
    @Mock
    private CommonCacheService commonCacheService;
    @Mock
    private CacheManager commonCaffeineCacheManager;
    @Mock
    private HttpServletResponse response;

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void UEDM_492403_given_用户没有所有设备权限_when_调用查询错峰收益列表的接口_then_展示站点的错峰收益列表() {
        // given
        PeakShiftDeviceTaskDto peakShiftDeviceTaskDto = new PeakShiftDeviceTaskDto();
        // when
        ResponseBean responseBean = peakShiftStrategyDistributionController.selectDeviceTaskInfoByCondition(peakShiftDeviceTaskDto, 1, 10, request, "zh-CN");
        // then
        Assert.assertNotEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void UEDM_492405_given_用户没有所有设备权限_when_策略下发新增选择设备_then_展示站点的设备() {
        // given
        PeakShiftGrainQueryResultBean page = new PeakShiftGrainQueryResultBean();
        page.setTotal(1L);

        PeakShiftGrainQueryBean peakShiftGrainQueryBean = new PeakShiftGrainQueryBean();
        peakShiftGrainQueryBean.setPeakStrategy("0");
        peakShiftGrainQueryBean.setStartTime("2024-04-01");
        peakShiftGrainQueryBean.setEndTime("2024-04-01");
        peakShiftGrainQueryBean.setTimeGran(null);
        peakShiftGrainQueryBean.setPositionGran(1);

        when(Tools.getUserName(request)).thenReturn("admin");
        when(configService.getGlobalProperty(Mockito.any())).thenReturn("¥");
        // when
        ResponseBean responseBean = priceStrategyController.exportStatistics(peakShiftGrainQueryBean, 1, 10, "zh-CN", request, response);
        // then
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void UEDM_492406_given_用户没有所有设备权限_when_策略下发新增选择模板_then_展示站点的模板() {
        // given
        PeakShiftGrainQueryBean peakShiftGrainQueryBean = new PeakShiftGrainQueryBean();
        // when
        ResponseBean responseBean = priceStrategyController.exportStatistics(peakShiftGrainQueryBean, 1, 10, "zh-CN", request, response);
        // then
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }
}
/* Ended by AICoder, pid:ud73659186abc94147b408bfb108c33629b32194 */