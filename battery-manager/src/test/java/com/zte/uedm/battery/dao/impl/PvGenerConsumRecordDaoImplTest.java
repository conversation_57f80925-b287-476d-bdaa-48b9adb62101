package com.zte.uedm.battery.dao.impl;

import com.zte.uedm.battery.bean.pv.PvCommonQueryMapperBean;
import com.zte.uedm.battery.bean.pv.PvPowerAnalysisResponseBean;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PvGenerConsumRecordDaoImplTest
{
    @InjectMocks
    private PvGenerConsumRecordDaoImpl pvGenerConsumRecordDao;

    @Mock
    private PvGenerConsumRecordMapper pvGenerConsumRecordMapper;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectOverViewRecordByConditionNoGrainTest()
    {
        List<PvPowerAnalysisResponseBean> res = pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(new ArrayList<>(), new PvCommonQueryMapperBean());
        List<PvPowerAnalysisResponseBean> res1 = pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(Arrays.asList("1"), new PvCommonQueryMapperBean());
        Assert.assertEquals(res.size(),0);
    }

    @Test
    public void selectOverViewRecordByConditionWithGrainTest()
    {
        List<PvPowerAnalysisResponseBean> res = pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(new ArrayList<>(), new PvCommonQueryMapperBean());
        List<PvPowerAnalysisResponseBean> res1 = pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(Arrays.asList("1"), new PvCommonQueryMapperBean());
        Assert.assertEquals(res.size(),0);
    }
}
