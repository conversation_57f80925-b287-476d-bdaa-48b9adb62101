package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.controller.backuppower.dto.DeviceBackupReqDto;
import com.zte.uedm.battery.mapper.BattBackupPowerEvalMapper;
import com.zte.uedm.battery.service.po.BattBackupPowerEvalPO;
import java.util.ArrayList;
import java.util.List;

import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

public class MonitorObjectServiceImplTest {

    @InjectMocks
    private MonitorObjectServiceImpl monitorObjectService;

    @Mock
    private BattBackupPowerEvalMapper mapper;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getDeviceBackupDuration() throws Exception {
        Mockito.when(mapper.selectBackupPowerDuration(anyList())).thenReturn(null);
        monitorObjectService.getDeviceBackupDuration(new ArrayList<>());
        List<BattBackupPowerEvalPO> battBackupPowerEvalPOS = new ArrayList<>();
        BattBackupPowerEvalPO battBackupPowerEvalPO = new BattBackupPowerEvalPO();
        battBackupPowerEvalPO.setId("id1");
        battBackupPowerEvalPOS.add(battBackupPowerEvalPO);
        Mockito.when(mapper.selectBackupPowerDuration(anyList())).thenReturn(battBackupPowerEvalPOS);
        monitorObjectService.getDeviceBackupDuration(new ArrayList<>());

        battBackupPowerEvalPO.setBackupPowerDuration(12.3599999999999994);
        battBackupPowerEvalPOS.add(battBackupPowerEvalPO);
        Mockito.when(mapper.selectBackupPowerDuration(anyList())).thenReturn(battBackupPowerEvalPOS);
        monitorObjectService.getDeviceBackupDuration(new ArrayList<>());
    }

    @Test
    public void testGetBackupDurationByCondition() {
        Mockito.when(mapper.selectBackupDurationByCondition(Mockito.any())).thenReturn(null);
        monitorObjectService.selectBackupByCondition(new DeviceBackupReqDto());
        List<BattBackupPowerEvalPO> battBackupPowerEvalPOS = new ArrayList<>();
        BattBackupPowerEvalPO battBackupPowerEvalPO = new BattBackupPowerEvalPO();
        battBackupPowerEvalPO.setId("id1");
        battBackupPowerEvalPOS.add(battBackupPowerEvalPO);
        Mockito.when(mapper.selectBackupDurationByCondition(Mockito.any())).thenReturn(battBackupPowerEvalPOS);
        monitorObjectService.selectBackupByCondition(new DeviceBackupReqDto());

        battBackupPowerEvalPO.setBackupPowerDuration(32.232);
        battBackupPowerEvalPOS.add(battBackupPowerEvalPO);
        Mockito.when(mapper.selectBackupDurationByCondition(Mockito.any())).thenReturn(battBackupPowerEvalPOS);
        monitorObjectService.selectBackupByCondition(new DeviceBackupReqDto());
    }

    @Test
    public void testGetDeviceBackupDuration() throws UedmException {
        Mockito.doThrow(new UedmException(-1, "error")).when(mapper).selectBackupPowerDuration(Mockito.any());
        try {
            monitorObjectService.getDeviceBackupDuration(new ArrayList<>());
        } catch (Exception e) {

        }
    }
}