package com.zte.uedm.battery.controller.backuppowerconfig.enums;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class BackupPowerDeviceTypeEnumTest {
    @Test
    public void test() {
        assertEquals("powerSupplyEquipment", BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getId());
        assertEquals("{\"en_US\":\"Power Supply Equipment\",\"zh_CN\":\"电源设备\"}",BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getName());
        assertEquals((Integer) 1, BackupPowerDeviceTypeEnum.Power_Supply_Equipment.getSequence());


        assertEquals("{\"en_US\":\"Battery Pack(Independent Monitoring)\",\"zh_CN\":\"电池组(独立监控)\"}", BackupPowerDeviceTypeEnum.getNameById("batteryPack"));
        assertEquals("", BackupPowerDeviceTypeEnum.getNameById("id"));
        assertEquals(2, BackupPowerDeviceTypeEnum.getAllIdNameBySequence().size());
        assertEquals(2, BackupPowerDeviceTypeEnum.getAllIds().size());
        List<IdNameBean> dims = BackupPowerDeviceTypeEnum.getAllIdNameBySequence();
//        assertEquals("switchPower", dims.get(0).getId());
    }

}