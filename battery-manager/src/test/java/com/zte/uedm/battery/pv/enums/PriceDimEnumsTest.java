package com.zte.uedm.battery.pv.enums;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.List;

import static org.junit.Assert.*;

public class PriceDimEnumsTest {

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getId() {

        Assert.assertSame(PriceDimEnums.NAME.getId(), "name");
    }

    @Test
    public void getName() {
        Assert.assertSame(PriceDimEnums.PRICETYPE.getName(), "{\"en_US\":\"Mode\",\"zh_CN\":\"类型\"}");
    }

    @Test
    public void getUnit() {
        Assert.assertSame(PriceDimEnums.PRICETYPE.getUnit(), null);
    }

    @Test
    public void getAllPriceDims() {
        List<String> dims = PriceDimEnums.getAllPriceDims();
        Assert.assertSame(dims.size(), 7);
    }

    @Test
    public void getUnitById() {
        String unit = PriceDimEnums.getUnitById("tipPrice");
        Assert.assertSame(unit, "¥");
        String unit1 = PriceDimEnums.getUnitById(null);
        Assert.assertSame(unit1, null);
        String unit2 = PriceDimEnums.getUnitById("111");
        Assert.assertSame(unit2, null);
    }

    @Test
    public void getNameById() {
        String name = PriceDimEnums.getNameById("name");
        Assert.assertSame(name, "{\"en_US\":\"Name\",\"zh_CN\":\"名称\"}");
        String name1 = PriceDimEnums.getNameById(null);
        Assert.assertSame(name1, "");
        String name2 = PriceDimEnums.getNameById("11");
        Assert.assertSame(name2, "");
    }
}