package com.zte.uedm.battery.pv.enums;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2023/10/07
 */
public class SolarSummaryGrainEnumTest {
    @Test
    void testGetCode() {
        assertThat(SolarSummaryGrainEnum.DAY.getCode()).isEqualTo("day");
        assertThat(SolarSummaryGrainEnum.MONTH.getCode()).isEqualTo("month");
        assertThat(SolarSummaryGrainEnum.YEAR.getCode()).isEqualTo("year");
        assertThat(SolarSummaryGrainEnum.ALL.getCode()).isEqualTo("all");
    }

    @Test
    void testGetTableName() {
        assertThat(SolarSummaryGrainEnum.DAY.getTableName()).isEqualTo("hour");
        assertThat(SolarSummaryGrainEnum.MONTH.getTableName()).isEqualTo("day");
        assertThat(SolarSummaryGrainEnum.YEAR.getTableName()).isEqualTo("month");
        assertThat(SolarSummaryGrainEnum.ALL.getTableName()).isEqualTo("year");
    }

    @Test
    void testGetTableNameByCode() {
        assertThat(SolarSummaryGrainEnum.getTableNameByCode("day")).isEqualTo("hour");
    }

    @Test
    void testGetEnum() {
        assertThat(SolarSummaryGrainEnum.getEnum("day")).isEqualTo(SolarSummaryGrainEnum.DAY);
    }
}
