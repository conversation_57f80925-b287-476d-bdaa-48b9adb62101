package com.zte.uedm.battery.controller;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.bean.BatteryCfgBeanVO;
import com.zte.uedm.battery.bean.BatteryManagerCfgBean;
import com.zte.uedm.battery.service.BatteryManagerCfgService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@SpringBootTest
public class BatteryManagerCfgControllerTest {

    @Mock
    private BatteryManagerCfgService batteryManagerCfgService;
    @InjectMocks
    private BatteryManagerCfgController batteryManagerCfgController;

    private BatteryManagerCfgBean batteryManagerCfgBean;

    private BatteryCfgBeanVO batteryCfgBeanVO;

    private ResponseBean responseBean = new ResponseBean();
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        batteryManagerCfgBean = new BatteryManagerCfgBean();
        batteryManagerCfgBean.setCfgValue("1314id");
        batteryManagerCfgBean.setMoOid("oid");
        batteryManagerCfgBean.setCfgType("00001");
        batteryCfgBeanVO = new BatteryCfgBeanVO();
        batteryCfgBeanVO.setMoOid("oid");
        batteryCfgBeanVO.setCfgType("00001");
        responseBean.setCode(0);
    }

    @Test
    public void insert()throws UedmException{
        doNothing().when(batteryManagerCfgService).insert(anyList());
        batteryManagerCfgController.insert(Lists.newArrayList(batteryManagerCfgBean));
        assertEquals("oid", batteryManagerCfgBean.getMoOid());
    }
    
    @Test
    public void insert2()throws UedmException{
        doNothing().when(batteryManagerCfgService).insert(null);
        batteryManagerCfgController.insert(null);
        assertEquals("oid", batteryManagerCfgBean.getMoOid());
    }

    @Test
    public void update() throws UedmException
    {
        doReturn(1).when(batteryManagerCfgService).update(any());
        responseBean = batteryManagerCfgController.update(Lists.newArrayList(batteryManagerCfgBean));
        assertEquals(1,(int)responseBean.getTotal());
    }
    
    @Test
    public void update2() throws UedmException
    {
        doReturn(1).when(batteryManagerCfgService).update(null);
        responseBean = batteryManagerCfgController.update(null);
        assertEquals(null, responseBean.getTotal());
    }

    @Test
    public void select() throws UedmException
    {
        doReturn(Lists.newArrayList(batteryManagerCfgBean)).when(batteryManagerCfgService).select(any());
        responseBean.setData(Lists.newArrayList(batteryManagerCfgBean));
        responseBean = batteryManagerCfgController.select(batteryCfgBeanVO);
        assertSame(null, responseBean.getMessage());
    }

    @Test
    public void selectStandby() throws UedmException
    {
        doReturn(Lists.newArrayList(batteryManagerCfgBean)).when(batteryManagerCfgService).select(any());
        responseBean.setData(batteryManagerCfgBean);
        responseBean = batteryManagerCfgController.selectStandbyTimeGlobal();
        assertSame(null, responseBean.getMessage());
    }
    
    @Test
    public void selectStandby2() throws UedmException
    {
        doReturn(Lists.newArrayList(batteryManagerCfgBean)).when(batteryManagerCfgService).select(null);
        responseBean.setData(batteryManagerCfgBean);
        responseBean = batteryManagerCfgController.selectStandbyTimeGlobal();
        assertSame(null, responseBean.getMessage());
    }
}
