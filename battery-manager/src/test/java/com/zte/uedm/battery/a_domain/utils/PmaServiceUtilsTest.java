package com.zte.uedm.battery.a_domain.utils;

/* Started by AICoder, pid:pe54351e88sdcd3148fe0bb430227f603e27b4d2 */

import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.cache.StandardPointCacheMgr;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.battery.bean.pv.HistoryDataResponseConditionBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.service.pma.api.PmaQueryService;
import com.zte.uedm.service.pma.api.dto.SpIdDto;
import com.zte.uedm.service.pma.api.vo.BeanDataVo;
import com.zte.uedm.service.pma.api.vo.BeanResponseVo;
import com.zte.uedm.service.pma.api.vo.IncPmaDataResponseVo;
import com.zte.uedm.service.pma.bean.IncPmaDataResultBean;
import com.zte.uedm.service.pma.bean.ResultBean;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PmaServiceUtilsTest {

    @Mock
    private PmaQueryService pmaQueryService;

    @Mock
    private StandardPointCacheMgr standardPointCacheMgr;

    @InjectMocks
    private PmaServiceUtils pmaServiceUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectHistoryDataByCondition() throws UedmException, ParseException, com.zte.uedm.basis.exception.UedmException {
        // 初始化测试数据
        List<String> moIds = Arrays.asList("1", "2");
        String moc = "MOC";
        List<SpIdDto> spIdList = Arrays.asList(new SpIdDto("SP1", "inc",5), new SpIdDto("SP2", "inc",5));
        String startTime = "2022-01-01 00:00:00";
        String endTime = "2022-01-02 00:00:00";
        String dataType = "collect";
        String grain = GlobalConstants.TIME_TYPE_DAY_WHOLE;

        StandardPointEntity mocEntity = new StandardPointEntity();
        mocEntity.setId("moc");
        mocEntity.setConvergeGranularity("{\"minute\":{\"flag\":true,\"value\":1},\"day\":{\"flag\":true,\"value\":1},\"week\":{\"flag\":true,\"value\":1},\"month\":{\"flag\":true,\"value\":1}}");
        when(standardPointCacheMgr.getStandardByIdMoc(any(),any())).thenReturn(mocEntity);

        BeanResponseVo beanResponseVo = new BeanResponseVo();
        beanResponseVo.setCode(0);
         List<ResultBean> list = Arrays.asList(new ResultBean());
        Map<Integer, BeanDataVo> data = new HashMap<>();
         BeanDataVo beanDataVo = new BeanDataVo();
         beanDataVo.setBeanList(list);
         beanDataVo.setTotalCount(10);
         data.put(5,beanDataVo);
         data.put(1440,beanDataVo);
         data.put(50,beanDataVo);
        beanResponseVo.setData(data);
        IncPmaDataResponseVo incPmaDataResponseVo = new IncPmaDataResponseVo();
        incPmaDataResponseVo.setCode(0);
        Map<String, List<IncPmaDataResultBean>> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("da",Arrays.asList(new IncPmaDataResultBean()));
        incPmaDataResponseVo.setData(objectObjectHashMap);
        // 模拟返回结果
        when(pmaQueryService.getBeanByPagingQuery(any())).thenReturn(beanResponseVo);
        when(pmaQueryService.getBeanByNoPagingQuery(any())).thenReturn(beanResponseVo);
        when(pmaQueryService.queryIncPmaDataBeanByNoPaging(any())).thenReturn(incPmaDataResponseVo);


        // 调用方法
        List<HistoryDataResponseConditionBean> result = pmaServiceUtils.selectHistoryDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, grain);
        pmaServiceUtils.selectHistoryDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, GlobalConstants.TIME_TYPE_MINUTE_WHOLE);
        pmaServiceUtils.selectHistoryDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, GlobalConstants.TIME_TYPE_HOUR_WHOLE);
        pmaServiceUtils.selectHistoryDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, "");
        pmaServiceUtils.selectDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, grain);
        pmaServiceUtils.selectIncDataByCondition(moIds, moc, "da", startTime, endTime, grain);
        pmaServiceUtils.selectPmaDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, grain);
       // pmaServiceUtils.getResultBeansByPage(moIds, moc, spIdList, startTime, endTime, dataType, grain);
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

/* Ended by AICoder, pid:pe54351e88sdcd3148fe0bb430227f603e27b4d2 */
@Test
public void testSelectHistoryDataByCondition2() throws UedmException, ParseException, com.zte.uedm.basis.exception.UedmException {
    // 初始化测试数据
    List<String> moIds = Arrays.asList("1", "2");
    String moc = "MOC";
    List<SpIdDto> spIdList = Arrays.asList(new SpIdDto("SP1", "inc",5), new SpIdDto("SP2", "inc",5));
    String startTime = "2022-01-01 00:00:00";
    String endTime = "2022-01-02 00:00:00";
    String dataType = "collect1";
    String grain = GlobalConstants.TIME_TYPE_DAY_WHOLE;
    StandardPointEntity mocEntity = new StandardPointEntity();
    mocEntity.setId("moc");
    mocEntity.setConvergeGranularity("{\"minute\":{\"flag\":true,\"value\":1},\"day\":{\"flag\":true,\"value\":1},\"week\":{\"flag\":true,\"value\":1},\"month\":{\"flag\":true,\"value\":1}}");
    when(standardPointCacheMgr.getStandardByIdMoc(any(),any())).thenReturn(mocEntity);

    BeanResponseVo beanResponseVo = new BeanResponseVo();
    beanResponseVo.setCode(0);
    List<ResultBean> list = Arrays.asList(new ResultBean());
    Map<Integer, BeanDataVo> data = new HashMap<>();
    BeanDataVo beanDataVo = new BeanDataVo();
    beanDataVo.setBeanList(list);
    beanDataVo.setTotalCount(10);
    data.put(5,beanDataVo);
    data.put(1440,beanDataVo);
    data.put(60,beanDataVo);
    beanResponseVo.setData(data);

    // 模拟返回结果
    when(pmaQueryService.getBeanByPagingQuery(any())).thenReturn(beanResponseVo);
    when(pmaQueryService.getBeanByNoPagingQuery(any())).thenReturn(beanResponseVo);


    // 调用方法
    List<HistoryDataResponseConditionBean> result = pmaServiceUtils.selectHistoryDataByCondition(moIds, moc, spIdList, startTime, endTime, dataType, grain);
}
}