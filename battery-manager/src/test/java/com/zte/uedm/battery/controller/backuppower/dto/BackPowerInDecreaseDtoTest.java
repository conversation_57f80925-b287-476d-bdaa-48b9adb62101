package com.zte.uedm.battery.controller.backuppower.dto;

import org.junit.Assert;
import org.junit.Test;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class BackPowerInDecreaseDtoTest {
    @Test
    public void test() throws ParseException {
        BackPowerInDecreaseDto backPowerInDecreaseDto = new BackPowerInDecreaseDto();
        String evlTime = "2022/07/11 15:00:00";
        String preEvlTime = "2022/07/10 15:00:00";
        Date evl = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").parse(evlTime);
        Date preEvl = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").parse(preEvlTime);
        backPowerInDecreaseDto.setNorInNumber(1);
        backPowerInDecreaseDto.setNorDeNumber(1);
        backPowerInDecreaseDto.setAbNorInNumber(1);
        backPowerInDecreaseDto.setAbNorDeNumber(1);
        backPowerInDecreaseDto.setUnEvaluateInNumber(1);
        backPowerInDecreaseDto.setUnEvaluateDeNumber(1);
        backPowerInDecreaseDto.setEvlTime(evl);
        backPowerInDecreaseDto.setPreEvalTime(preEvl);
        Assert.assertEquals(1, backPowerInDecreaseDto.getNorDeNumber());
        Assert.assertEquals(1, backPowerInDecreaseDto.getNorInNumber());
        Assert.assertEquals(1, backPowerInDecreaseDto.getAbNorDeNumber());
        Assert.assertEquals(1, backPowerInDecreaseDto.getAbNorInNumber());
        Assert.assertEquals(1, backPowerInDecreaseDto.getUnEvaluateDeNumber());
        Assert.assertEquals(1, backPowerInDecreaseDto.getUnEvaluateInNumber());
        Assert.assertNotNull(backPowerInDecreaseDto.getEvlTime());
        Assert.assertNotNull(backPowerInDecreaseDto.getPreEvalTime());
        Assert.assertNotNull(backPowerInDecreaseDto.toString());
    }

}