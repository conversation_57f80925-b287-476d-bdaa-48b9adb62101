package com.zte.uedm.battery.controller.backuppower.dto;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class BackupPowerStatisticsFilterDtoTest {
    @Test
    public void test() throws Exception
    {
        BackupPowerStatisticsFilterDto bean0 = new BackupPowerStatisticsFilterDto();
        bean0.setBrands(Arrays.asList("brand"));
        bean0.setDims(Arrays.asList("total","normal","deficiency","xxx"));
        bean0.setStatus(Arrays.asList("normal","deficiency","xxx"));
        bean0.setApplicationScene(Arrays.asList("Li","xxx"));
        bean0.setDeviceType(Arrays.asList("switchPower","xxx"));
        bean0.setBackupPowerDurationStart(1.3);
        bean0.setBackupPowerDurationEnd(1.23);
        bean0.checkParamIsValid();

        bean0.setModels(Arrays.asList("switchPower","xxx"));
        bean0.setSeries(Arrays.asList("switchPower","xxx"));
        bean0.setBrands(Arrays.asList("switchPower","xxx"));
        bean0.setManufacturers(Arrays.asList("switchPower","xxx"));
        bean0.setSiteLevel(Arrays.asList("s0001","xxx"));
        bean0.setPowerSupply(Arrays.asList("s0002","xxx"));
        Map<String, Boolean> attributeEnableMap = new HashMap<>();
        bean0.checkAssetAttribute(attributeEnableMap);

        PojoTestUtil.TestForPojo(BackupPowerStatisticsFilterDto.class);
        BackupPowerStatisticsFilterDto bean = new BackupPowerStatisticsFilterDto(new BackupOverviewExportDto());
        bean.setLogicGroupId("id");
        bean.toString();
        Assert.assertEquals("id", bean.getLogicGroupId());
    }
}