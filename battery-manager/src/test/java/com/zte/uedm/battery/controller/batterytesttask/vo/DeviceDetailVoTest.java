package com.zte.uedm.battery.controller.batterytesttask.vo;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class DeviceDetailVoTest
{
    @Test
    public void testSetGetToString()
    {
        DeviceDetailVo bean = new DeviceDetailVo();
        bean.setId("77");
        bean.setName("1");
        bean.setPathName("77");
        bean.setStatus(new IdNameBean());
        bean.setPreTestTimeRange("1");
        bean.setNextTestTimeRange("1");
        bean.setNeedRetry(false);

        bean.getStatus();
        bean.getId();
        bean.getName();
        bean.getNextTestTimeRange();
        bean.getPreTestTimeRange();

        bean.toString();
        assertEquals("77",bean.getId());
    }
}
