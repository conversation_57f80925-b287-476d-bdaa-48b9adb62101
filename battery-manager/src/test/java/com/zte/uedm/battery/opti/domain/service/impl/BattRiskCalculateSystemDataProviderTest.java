package com.zte.uedm.battery.opti.domain.service.impl;

import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class BattRiskCalculateSystemDataProviderTest
{
    @InjectMocks
    private BattRiskCalculateSystemDataProvider battRiskCalculateSystemDataProvider;

    @Mock
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository;

    @Mock
    private DateTimeService dateTimeService;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getDataRealTime_Test() throws Exception
    {
        List<String> moIdList = new ArrayList<>();
        moIdList.add("batt-1");
        Map<String, Map<String, String>> result = battRiskCalculateSystemDataProvider.getDataRealTime(moIdList, new ArrayList<>());
        Assert.assertEquals(result.size(), 0);

        List<String> parameterIds = new ArrayList<>();
        parameterIds.add("batt.system.current.time");

        List<BattRiskRuleSourceEntity> riskRuleSourceEntities = new ArrayList<>();
        BattRiskRuleSourceEntity battRiskRuleSourceEntity = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity.setId("batt.system.current.time");
        battRiskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_SYSTEM);
        riskRuleSourceEntities.add(battRiskRuleSourceEntity);

        PowerMockito.when(battRiskRuleSourceRepository.selectByIds(Mockito.anyList())).thenReturn(riskRuleSourceEntities);
        PowerMockito.when(dateTimeService.getCurrentDateTime()).thenReturn(new Date());

        Map<String, Map<String, String>> result1 = battRiskCalculateSystemDataProvider.getDataRealTime(moIdList, parameterIds);
        Assert.assertEquals(result1.size(), 1);
    }

    @Test
    public void getDataRangeTime_Test() throws Exception
    {
        Assert.assertTrue(battRiskCalculateSystemDataProvider.getDataRangeTime(new ArrayList<>(), new ArrayList<>(), Pair.of(new Date(), new Date())).isEmpty());
    }
}
