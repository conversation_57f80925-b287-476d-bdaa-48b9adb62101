package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyDetailPo;
import com.zte.uedm.battery.bean.pojo.PeakShiftCsu5StrategyPo;
import com.zte.uedm.common.exception.UedmException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PeakShiftMapperFake implements PeakShiftMapper{
    @Override
    public Integer deleteByIdAndStatus(String id) {
        return null;
    }

    @Override
    public Integer deleteDetailStrategyById(String strategyId) {
        return null;
    }

    @Override
    public List<PeakShiftStrategyBean> selectByConditions(PeakShiftStrategyQueryBean queryBean) {
        return null;
    }

    @Override
    public Integer insertPeakShiftStrategy(PeakShiftStrategyBean bean) throws UedmException {
        return null;
    }

    @Override
    public Integer insertPeakShiftStrategyDetail(List<PeakShiftStrategyDetailBean> detailBeans) throws UedmException {
        return null;
    }

    @Override
    public Integer updatePeakShiftStrategy(PeakShiftStrategyBean bean) throws UedmException {
        return null;
    }

    @Override
    public Integer updatePeakShiftStrategyDetail(List<PeakShiftStrategyDetailBean> detailBeans) throws UedmException {
        return null;
    }

    @Override
    public PeakShiftStrategyVo selectPeakShiftStrategyById(String id) throws UedmException {
        return null;
    }

    @Override
    public List<PeakShiftStrategyDetailBean> selectPeakShiftStrategyDetailByStrategyId(String id) throws UedmException {
        return null;
    }

    @Override
    public List<PeakShiftStrategyBean> selectPeakShiftStrategyByStatus(Integer status) throws UedmException {
        return null;
    }

    @Override
    public int duplicateNameCheck(String name, String id) throws UedmException {
        return 0;
    }

    @Override
    public void changeStatus(String id, Integer status) throws UedmException {

    }

    @Override
    public void updateEffectiveTime(String id, String currentTime) throws UedmException {

    }

    @Override
    public void updateExpirationTime(String id, String currentTime) throws UedmException {

    }

    @Override
    public int selectStatusById(String id) {
        return 0;
    }

    @Override
    public PeakShiftStrategyBean selectPeakShiftStrategyBeanById(String id) throws UedmException {
        return null;
    }

    @Override
    public Integer deleteFromDetailByTime(String startTime, String endTime) {
        return null;
    }

    @Override
    public Integer insertPeakShiftBatteryChargeDischargeDetail(List<PeakShiftBatteryDetailDataBean> beans) throws UedmException {
        return null;
    }

    @Override
    public List<PeakShiftStrategyDetailBean> selectStategyDetailByDay(String date) {
        return null;
    }

    @Override
    public List<PeakShiftBatteryDetailDataBean> selectDetailDataByCondition(PeakShiftDetailQueryBean peakShiftDetailQueryBean) {
        return null;
    }

    @Override
    public void insertIntoSPorBattpackMergedData(List<PeakShiftBatteryDataBean> mergedData) {

    }

    @Override
    public void deleteSPorBattpackMergedData(String date) {

    }

    @Override
    public void insertIntoStatisticsData(PeakShiftStatisticsBean statisticsData) {

    }

    @Override
    public void deleteStatisticsData(String date, String id, Integer strategyType, String strategyId, String gmtCreate) {

    }

    @Override
    public List<PeakShiftStatisticsBean> selectStaticsDataByCondition(String start, String end, List<String> siteIds) {
        return null;
    }

    @Override
    public void changeSeasonStrategyStatusInto1() {

    }

    @Override
    public void changeSeasonStrategyStatusInto2() {

    }

    @Override
    public List<ScopeStrategyResponseBean> selectScopeStrategyToChange() {
        return null;
    }

    @Override
    public String selectEffectiveTimeSeasonStrategyToExpirate(String scopeStrategyId) {
        return null;
    }

    @Override
    public List<IntervalStrategyDetailModeBean> selectIntervalStrategyDetailByDateAndStrategyId(String scopeStrategyId, String date) {
        return null;
    }

    @Override
    public List<KwhGainBean> calculateKwhGainByStatisticsDate(String today) {
        List<KwhGainBean> kwhGainBeans = new ArrayList<>();
        KwhGainBean kwhGainBean = new KwhGainBean();
        kwhGainBean.setMonitorDeviceId("111111");
        kwhGainBean.setSiteId("222222");
        kwhGainBean.setCharge("10");
        kwhGainBean.setPowerSystemId("12345");
        kwhGainBeans.add(kwhGainBean);
        return kwhGainBeans;
    }

    @Override
    public List<KwhGainAccumulatedBean> selectKwhAccumulatedGainByDate(String today) {
        List<KwhGainAccumulatedBean> kwhGainAccumulatedBeans = new ArrayList<>();
        KwhGainAccumulatedBean kwhGainAccumulatedBean = new KwhGainAccumulatedBean();
        kwhGainAccumulatedBean.setMonitorDeviceId("123456");
        kwhGainAccumulatedBean.setCharge("10");
        kwhGainAccumulatedBean.setGainTotal("20");
        kwhGainAccumulatedBean.setDischarge("22");
        kwhGainAccumulatedBean.setPowerSystemId("33333");
        kwhGainAccumulatedBeans.add(kwhGainAccumulatedBean);
        return kwhGainAccumulatedBeans;
    }

    @Override
    public void insertAllKwhGainBean(List<KwhGainBean> list) {

    }

    @Override
    public void insertKwhGainAccumulatedBean(List<KwhGainAccumulatedBean> list) {

    }

    @Override
    public void deleteKwhGainBeanByDate(String date) {

    }

    @Override
    public void deleteKwhGainAccumulatedBeanByDate(String date) {

    }

    @Override
    public PeakShiftCsu5StrategyPo selectCsuStrategyByDeviceId(String deviceId) {
        return null;
    }

    @Override
    public List<PeakShiftCsu5StrategyPo> selectCsuAllStrategyByDeviceId(String deviceId) {
        return null;
    }

    @Override
    public List<PeakShiftCsu5StrategyPo> selectCsuAllStrategyByDeviceIds(List<String> deviceId) {
        return new ArrayList<>();
    }

    @Override
    public void updateCsuStrategyEndTime(PeakShiftCsu5StrategyPo bean) {

    }

    @Override
    public void insertCsuStrategy(PeakShiftCsu5StrategyPo bean) {

    }

    @Override
    public void insertCsuStrategyDetails(List<PeakShiftCsu5StrategyDetailPo> beans) {

    }

    @Override
    public List<PeakShiftCsu5StrategyDetailPo> selectCsuStrategyDetailByDeviceId(String deviceId) {
        return null;
    }

    @Override
    public List<PeakShiftCsu5StrategyDetailBean> selectCsuStrategyDetailByDeviceIds(List<String> deviceIds) {
        return new ArrayList<>();
    }

    @Override
    public void deleteKwhGainByDateAndSystemId(List<String> powerSystemIdList, String day) {

    }

    @Override
    public List<KwhGainBean> selectLastKwhGainByDay(String startDay, String endDay, List<String> powerSystemIdList) {
        return Collections.emptyList();
    }

    @Override
    public List<KwhGainBean> selectLastKwhGain(String day) {
        return Collections.emptyList();
    }
}
