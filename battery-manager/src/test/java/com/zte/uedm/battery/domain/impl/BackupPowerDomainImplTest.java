package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.controller.backuppower.dto.*;
import com.zte.uedm.battery.mapper.BattBackupPowerEvalMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static com.zte.uedm.battery.consts.CommonConst.BATT_BACK_POWER;
import static com.zte.uedm.battery.consts.CommonConst.CACHE_NAME_BATT_BACK_POWER_EVAL;
import static org.mockito.Mockito.when;

public class BackupPowerDomainImplTest
{
    @InjectMocks
    private BackupPowerEvalDomainImpl backupPowerDomain;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Mock
    private BattBackupPowerEvalMapper backupPowerMapper;
    @Mock
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private CommonCacheService cacheService;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectByCondition_check_param() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
        powerEvalPojo.setBackupPowerDuration(1.2);
        powerEvalPojo.setThresholdDuration(1.2);
        //powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo.setName("1.2");
        powerEvalPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setEvalTime("2022-07");
        powerEvalPojo.setId("1.2");
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectByCondition(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerEvalPojoPageInfo=backupPowerDomain.selectByCondition(null,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectByCondition_abnormal() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
        powerEvalPojo.setBackupPowerDuration(1.2);
        powerEvalPojo.setThresholdDuration(1.2);
       // powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo.setName("1.2");
        powerEvalPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        //powerEvalPojo.setEvalTime(new Date());
        powerEvalPojo.setId("1.2");
        battBackupPowerEvalPojos.add(powerEvalPojo);
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectByCondition(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerEvalPojoPageInfo=backupPowerDomain.selectByCondition(evalDetailDto,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }
    @Test
    public void selectByCondition_normal() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("r32.uedm.group-global");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
        powerEvalPojo.setBackupPowerDuration(1.2);
        powerEvalPojo.setThresholdDuration(1.2);
        //powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo.setName("1.2");
        powerEvalPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setEvalTime("2022-07");
        powerEvalPojo.setId("1.2");
        powerEvalPojo.setPathIds("RealGroup-Central-1/Site-4005/mo-cabinet-j5fhdc/mo-sp-djuju0");
        battBackupPowerEvalPojos.add(powerEvalPojo);
        BattBackupPowerEvalPojo powerEvalPojo2 = new BattBackupPowerEvalPojo();
        powerEvalPojo2.setBackupPowerDuration(1.2);
        powerEvalPojo2.setThresholdDuration(1.2);
        //powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo2.setName("1.2");
        powerEvalPojo2.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo2.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo2.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo2.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo2.setEvalTime("2022-08");
        powerEvalPojo2.setId("1.2");
        powerEvalPojo2.setPathIds("RealGroup-Central-1/Site-4005/mo-cabinet-j5fhdc/mo-sp-djuju0");
        battBackupPowerEvalPojos.add(powerEvalPojo2);
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerEvalPojos).when(backupPowerMapper).selectByCondition(Mockito.any());
            Mockito.doReturn(Arrays.asList("321","Site-4005","mo-sp-djuju0")).when(configurationManagerRpcImpl).getAuthPositionsByUser(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            pageInfo = backupPowerDomain.selectByCondition(evalDetailDto, serviceBaseInfoBean);
            evalDetailDto.setLogicGroupId("Site-4005");
            pageInfo = backupPowerDomain.selectByCondition(evalDetailDto, serviceBaseInfoBean);
            Mockito.doReturn(new ArrayList<>()).when(configurationManagerRpcImpl).getAuthPositionsByUser(Mockito.any());
            pageInfo = backupPowerDomain.selectByCondition(evalDetailDto, serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(pageInfo.getTotal()));
    }
	
	@Test
    public void selectByLogicGroupId1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        String time = "2022-07-10  15:00:00";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime(time);
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectByLogicGroupId(null,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }


    @Test
    public void selectByLogicGroupId2() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "name";
        String time = "2022-07-10  15:00:00";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime(time);
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectByLogicGroupId(logicGroupId,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectByLogicGroupId3() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "Site-4005";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("Site-4005");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        powerOverviewPojo.setPathIds("RealGroup-Central-1/Site-4005/mo-cabinet-j5fhdc/mo-sp-djuju0");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerOverviewPojos).when(backupPowerMapper).selectByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectByLogicGroupId(logicGroupId, serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(battBackupPowerOverviewPojos.size()));
    }

    @Test
    public void selectByLogicGroupId4() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "r32.uedm.group-global";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("r32.uedm.group-global");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("r32.uedm.group-global");
        evalDetailDto.setStatus(Arrays.asList("r32.uedm.group-global"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerOverviewPojos).when(backupPowerMapper).selectByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectByLogicGroupId(logicGroupId, serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(battBackupPowerOverviewPojos.size()));
    }

    @Test
    public void selectInDesByLogicGroupId1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectInDesByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDesByLogicGroupId(null,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }


    @Test
    public void selectInDesByLogicGroupId2() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "name";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectInDesByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDesByLogicGroupId(logicGroupId,serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectInDesByLogicGroupId3() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "Site-4005";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("Site-4005");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        powerOverviewPojo.setPathIds("RealGroup-Central-1/Site-4005/mo-cabinet-j5fhdc/mo-sp-djuju0");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerOverviewPojos).when(backupPowerMapper).selectInDesByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDesByLogicGroupId(logicGroupId, serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(battBackupPowerOverviewPojos.size()));
    }

    @Test
    public void selectInDesByLogicGroupId4() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        String logicGroupId = "r32.uedm.group-global";
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("r32.uedm.group-global");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerOverviewPojos).when(backupPowerMapper).selectInDesByLogicGroupId(Mockito.any(),Mockito.anyString());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDesByLogicGroupId(logicGroupId, serviceBaseInfoBean);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(battBackupPowerOverviewPojos.size()));
    }


    @Test
    public void selectTrendByCondition() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalTrendPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalTrendPojo powerEvalTrendPojo = new BattBackupPowerEvalTrendPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        PageInfo<BattBackupPowerEvalTrendPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);

        try {

            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectTrendByCondition(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerEvalPojoPageInfo=backupPowerDomain.selectTrendByCondition(evalTrendDto,serviceBaseInfoBean);

        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());

    }
    @Test
    public void selectTrendByCondition1() throws Exception
    {
        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalTrendPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalTrendPojo powerEvalTrendPojo = new BattBackupPowerEvalTrendPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);

        PageInfo<BattBackupPowerEvalTrendPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectTrendByCondition(Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
        pageInfo = backupPowerDomain.selectTrendByCondition(evalTrendDto, serviceBaseInfoBean);
        Assert.assertEquals("1",String.valueOf(pageInfo.getTotal()));

    }
    @Test
    public void selectTrendByCondition2() throws Exception
    {
        UedmException flag=null;

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {

            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectTrendByCondition(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            PageInfo<BattBackupPowerEvalTrendPojo> backupPowerEvalPojoPageInfo=backupPowerDomain.selectTrendByCondition(null,serviceBaseInfoBean);

        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectInDecreaseDetailByLogicGroupId_normal() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        UedmException flag=null;
        ResponseBean re = null;
        BackPowerDetailDto backPowerDetailDto = new BackPowerDetailDto();
        backPowerDetailDto.setLogicGroupId("Site-4005");

        List<BattBackupPowerOverviewPojo> battBackupPowerOverviewPojos = new ArrayList<>();
        BattBackupPowerOverviewPojo powerOverviewPojo = new BattBackupPowerOverviewPojo();
        powerOverviewPojo.setBackupPowerDuration(1.2);
        powerOverviewPojo.setThresholdDuration(1.2);
        powerOverviewPojo.setSurplusDischargeDuration("1.2");
        powerOverviewPojo.setName("1.2");
        powerOverviewPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerOverviewPojo.setEvalTime("2022-07-10  15:00:00");
        powerOverviewPojo.setId("1.2");
        powerOverviewPojo.setPathIds("RealGroup-Central-1/Site-4005/mo-powercabinet-4005/mo-sp-4005");
        battBackupPowerOverviewPojos.add(powerOverviewPojo);

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doReturn(battBackupPowerOverviewPojos).when(backupPowerMapper).selectInDecreaseDetailByLogicGroupId(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDecreaseDetailByLogicGroupId(backPowerDetailDto.getLogicGroupId());
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(battBackupPowerOverviewPojos.size()));
    }

    @Test
    public void selectInDecreaseDetailByLogicGroupId_check_param() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;

        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
        powerEvalPojo.setBackupPowerDuration(1.2);
        powerEvalPojo.setThresholdDuration(1.2);
        //powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo.setName("1.2");
        powerEvalPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        //powerEvalPojo.setEvalTime(new Date());
        powerEvalPojo.setId("1.2");
        battBackupPowerEvalPojos.add(powerEvalPojo);
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectInDecreaseDetailByLogicGroupId(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDecreaseDetailByLogicGroupId(null);
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectInDecreaseDetailByLogicGroupId_abnormal() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        BackPowerDetailDto backPowerDetailDto = new BackPowerDetailDto();
        backPowerDetailDto.setLogicGroupId("Site-4005");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
        powerEvalPojo.setBackupPowerDuration(1.2);
        powerEvalPojo.setThresholdDuration(1.2);
        // powerEvalPojo.setSurplusDischargeDuration(1.2);
        powerEvalPojo.setName("1.2");
        powerEvalPojo.setBrand("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setSeries("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setManufacture("{\"id\":\"321\",\"name\":\"名称32\"}");
        powerEvalPojo.setModel("{\"id\":\"321\",\"name\":\"名称32\"}");
        //powerEvalPojo.setEvalTime(new Date());
        powerEvalPojo.setId("1.2");
        battBackupPowerEvalPojos.add(powerEvalPojo);
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectInDecreaseDetailByLogicGroupId(Mockito.any());
            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerDomain.selectInDecreaseDetailByLogicGroupId(backPowerDetailDto.getLogicGroupId());
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectEvalDByMoIds() throws UedmException
    {
        try {
        UedmException flag=null;
        ResponseBean re = null;
        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);



            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            backupPowerEvalPojoPageInfo=backupPowerDomain.selectEvalDByMoIds(Arrays.asList("1"),serviceBaseInfoBean);

        } catch (UedmException e) {
            Assert.assertNotNull(e);
        }


    }

    @Test
    public void selectEvalDByMoIds11() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);

        BatteryBackupPowerEvalPojo batteryBackupPowerEvalPojos =new BatteryBackupPowerEvalPojo();
        batteryBackupPowerEvalPojos.setId("id");
        batteryBackupPowerEvalPojos.setStatus("status");

        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        Mockito.when(cacheService.getCache(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(null);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDByMoIds(Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());

        pageInfo = backupPowerDomain.selectEvalDByMoIds(Arrays.asList("1"), serviceBaseInfoBean);
        Assert.assertEquals("0",String.valueOf(pageInfo.getTotal()));

    }

    @Test
    public void selectEvalDByMoIds1() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);

        BatteryBackupPowerEvalCachePojo batteryBackupPowerEvalPojos =new BatteryBackupPowerEvalCachePojo();
        batteryBackupPowerEvalPojos.setId("id");
        batteryBackupPowerEvalPojos.setStatus("status");

        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDByMoIds(Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
        Mockito.when(cacheService.getCache(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(Arrays.asList(batteryBackupPowerEvalPojos));
        pageInfo = backupPowerDomain.selectEvalDByMoIds(Arrays.asList("1"), serviceBaseInfoBean);
        Assert.assertEquals("0",String.valueOf(pageInfo.getTotal()));

    }

    @Test
    public void selectEvalDByMoIds12() throws Exception
    {
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("1.2");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());

        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);

        BatteryBackupPowerEvalPojo batteryBackupPowerEvalPojos =new BatteryBackupPowerEvalPojo();
        batteryBackupPowerEvalPojos.setId("id");
        batteryBackupPowerEvalPojos.setStatus("status");

        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDByMoIds(Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
        Mockito.when(cacheService.getCache(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.doReturn(new ArrayList<>()).when(cacheService).getCache(CACHE_NAME_BATT_BACK_POWER_EVAL, BATT_BACK_POWER, List.class);
        List<BattBackupPowerEvalPojo> battBackupPowerEvalPojos = new ArrayList<>();
        BattBackupPowerEvalPojo battBackupPowerEvalPojo = new BattBackupPowerEvalPojo();
        battBackupPowerEvalPojo.setId("id");
        battBackupPowerEvalPojos.add(battBackupPowerEvalPojo);
        when(backupPowerMapper.selectEvalDByMoIds(null)).thenReturn(battBackupPowerEvalPojos);
        pageInfo = backupPowerDomain.selectEvalDByMoIds(Arrays.asList("1"), serviceBaseInfoBean);
        Assert.assertEquals("0",String.valueOf(pageInfo.getTotal()));

    }

    @Test
    public void selectEvalDByMoIds2() throws Exception
    {
        try{
        UedmException flag=null;

        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        try {

            Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.any());
            PageInfo<BattBackupPowerEvalPojo> backupPowerEvalPojoPageInfo=backupPowerDomain.selectEvalDByMoIds(new ArrayList<>(),null);

        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void selectEvalDetailByCondition() throws UedmException
    {

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        TableDetailSelectDto evalDetailSelectDto=new TableDetailSelectDto();
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectEvalDetailByCondition(Mockito.any());
            //pageInfo=backupPowerDomain.selectEvalDetailByCondition(evalDetailSelectDto,serviceBaseInfoBean);
            Assert.assertEquals(1,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }


    @Test
    public void selectEvalDetailByCondition1() throws UedmException
    {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        powerEvalTrendPojo.setPathIds("123/123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        TableDetailSelectDto evalDetailSelectDto=new TableDetailSelectDto();
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        Mockito.doReturn(new ArrayList<>()).when(configurationManagerRpcImpl).getAuthPositionsByUser(Mockito.any());
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("123");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());
        try {
            Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDetailByCondition(Mockito.any());
            pageInfo=backupPowerDomain.selectEvalDetailByCondition(evalDetailSelectDto,new HashMap<>(),new HashMap<>(),new HashMap<>(),serviceBaseInfoBean);
            Assert.assertEquals(0,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("123",e.getMessage());
        }
    }
    @Test
    public void filterDeletedSPDetailAndPageTest() throws UedmException
    {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        powerEvalTrendPojo.setPathIds("123/123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        TableDetailSelectDto evalDetailSelectDto=new TableDetailSelectDto();
        PageInfo<BattBackupPowerEvalPojo> pageInfo;
        Mockito.doReturn(new ArrayList<>()).when(configurationManagerRpcImpl).getAuthPositionsByUser(Mockito.any());
        List<ResourceBaseBean> resourceBeanListByMoc = new ArrayList<>();
        ResourceBaseBean resourceBaseBean = new ResourceBaseBean();
        resourceBaseBean.setId("123");
        resourceBaseBean.setMoc(MocType.SP);
        resourceBeanListByMoc.add(resourceBaseBean);
        Mockito.doReturn(resourceBeanListByMoc).when(configurationManagerRpcImpl).getResourceBeanListByMoc(Mockito.any());
        Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDetailByCondition(Mockito.any());
        /* Started by AICoder, pid:b0f375af5573432bb3267fb4142cc91a */
        Map<String, SiteBean> siteMap=new HashMap<>();
        SiteBean siteBean = new SiteBean();
        siteBean.setSecurityLevel("1");
        siteBean.setPowerSupplyScene("1");
        siteMap.put("123",siteBean);
        try {
            pageInfo=backupPowerDomain.filterDeletedSPDetailAndPage(evalDetailSelectDto,battBackupPowerEvalTrendPojos,siteMap,new HashMap<>(),new HashMap<>(),serviceBaseInfoBean);
            Assert.assertEquals(2,pageInfo.getTotal());
        /* Ended by AICoder, pid:b0f375af5573432bb3267fb4142cc91a */
        } catch (UedmException e) {
            Assert.assertEquals("123",e.getMessage());
        }
    }

    @Test
    public void selectEvalDetailByCondition2() throws UedmException
    {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        TableDetailSelectDto evalDetailSelectDto = new TableDetailSelectDto();
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        try {
            Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectEvalDetailByCondition(Mockito.any());
            //pageInfo=backupPowerDomain.selectEvalDetailByCondition(evalDetailSelectDto,null);
            Assert.assertEquals(1,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("params serviceBean is empty",e.getMessage());
        }
    }


    @Test
    public void selectBackupPowerEvalMByCondition() throws UedmException
    {

        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        DecreaseStatisticsNewDto evalDetailSelectDto=new DecreaseStatisticsNewDto();
        evalDetailSelectDto.setEvalTime("1");
        evalDetailSelectDto.setLogicGroupId("1");
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerMapper).selectBackupPowerEvalMByCondition(Mockito.any());
            pageInfo=backupPowerDomain.selectBackupPowerEvalMByCondition(evalDetailSelectDto,serviceBaseInfoBean);
            Assert.assertEquals(1,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }


    @Test
    public void selectBackupPowerEvalMByCondition1() throws UedmException
    {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);
        DecreaseStatisticsNewDto evalDetailSelectDto=new DecreaseStatisticsNewDto();
        evalDetailSelectDto.setEvalTime("1");
        evalDetailSelectDto.setLogicGroupId("1");
        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        try {
            Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectBackupPowerEvalMByCondition(Mockito.any());
            pageInfo=backupPowerDomain.selectBackupPowerEvalMByCondition(evalDetailSelectDto,serviceBaseInfoBean);
            Assert.assertEquals(1,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("123",e.getMessage());
        }
    }


    @Test
    public void selectBackupPowerEvalMByCondition2() throws UedmException
    {
        List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalPojo powerEvalTrendPojo = new BattBackupPowerEvalPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        battBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        DecreaseStatisticsNewDto evalDetailSelectDto=new DecreaseStatisticsNewDto();

        PageInfo<BattBackupPowerEvalPojo> pageInfo=new PageInfo<>(battBackupPowerEvalTrendPojos);
        try {
            Mockito.doReturn(battBackupPowerEvalTrendPojos).when(backupPowerMapper).selectBackupPowerEvalMByCondition(Mockito.any());
            pageInfo=backupPowerDomain.selectBackupPowerEvalMByCondition(evalDetailSelectDto,null);
            Assert.assertEquals(1,pageInfo.getTotal());
        } catch (UedmException e) {
            Assert.assertEquals("params is empty",e.getMessage());
        }
    }

    @Test
    public void getDeviceTypeByNameTest()  {
        TableDetailSelectDto evalDetailSelectDto1 = new TableDetailSelectDto();
        evalDetailSelectDto1.setName("p");
        ServiceBaseInfoBean serviceBaseInfoBean1 = new ServiceBaseInfoBean("user","en-US");
        backupPowerDomain.getDeviceTypeByName(evalDetailSelectDto1,serviceBaseInfoBean1);
        TableDetailSelectDto evalDetailSelectDto2 = new TableDetailSelectDto();
        evalDetailSelectDto2.setName("电池");
        ServiceBaseInfoBean serviceBaseInfoBean2 = new ServiceBaseInfoBean("user","zh-CN");
        backupPowerDomain.getDeviceTypeByName(evalDetailSelectDto2,serviceBaseInfoBean2);
        TableDetailSelectDto evalDetailSelectDto3 = new TableDetailSelectDto();
        backupPowerDomain.getDeviceTypeByName(evalDetailSelectDto3,serviceBaseInfoBean2);
        TableDetailSelectDto evalDetailSelectDto4 = new TableDetailSelectDto();
        evalDetailSelectDto4.setName("x");
        ServiceBaseInfoBean serviceBaseInfoBean4 = new ServiceBaseInfoBean("user","zh-CN");
        TableDetailSelectDto tableDetailSelectDto = backupPowerDomain.getDeviceTypeByName(evalDetailSelectDto4,serviceBaseInfoBean4);
        Assert.assertEquals("no.absent",tableDetailSelectDto.getType().get(0));
    }

}
