package com.zte.uedm.battery.controller;

import com.zte.uedm.battery.service.DataMigrationService;
import com.zte.uedm.common.bean.ResponseBean;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

public class DataMigrationControllerTest {

    @Mock
    private DataMigrationService dataMigrationService;

    @InjectMocks
    private DataMigrationController dataMigrationController;

    private MockHttpServletRequest request;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        request = new MockHttpServletRequest();
    }

    @Test
    public void testMigrateDataSuccess() throws Exception {
        // Given
        doNothing().when(dataMigrationService).migrateData(request);

        // When
        ResponseBean responseBean = dataMigrationController.migrateData(request);

        // Then
        assertNotNull(responseBean);
    }

    @Test
    public void testMigrateDataFailure() throws Exception {
        // Given
        Exception exception = new RuntimeException("Data migration failed.");
        doThrow(exception).when(dataMigrationService).migrateData(request);

        // When
        ResponseBean responseBean = dataMigrationController.migrateData(request);

        // Then
        assertNotNull(responseBean);
    }
}
