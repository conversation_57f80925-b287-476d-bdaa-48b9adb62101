package com.zte.uedm.battery.opti.infrastructure.enums.battrisk;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.LinkedList;

public class BatterySpIdAlarmCodeOptionalTest
{

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void BattRiskRuleSourceTypeGroupOptional_Test() throws Exception
    {
        BatterySpIdAlarmCodeOptional batterySpIdAlarmCodeOptional = new BatterySpIdAlarmCodeOptional(10L,"da,over");
        Long alarmCode = batterySpIdAlarmCodeOptional.getAlarmCode();
        String spId = batterySpIdAlarmCodeOptional.getSpId();
        BatterySpIdAlarmCodeOptional batterySpIdAlarmCodeOptional1 = new BatterySpIdAlarmCodeOptional(10L,"da,under");
        LinkedList<BatterySpIdAlarmCodeOptional> battCellOverVoltAlarmCell = BatterySpIdAlarmCodeOptional.battCellOverVoltAlarmCell;
        Assert.assertSame(17,battCellOverVoltAlarmCell.size());
    }
}
