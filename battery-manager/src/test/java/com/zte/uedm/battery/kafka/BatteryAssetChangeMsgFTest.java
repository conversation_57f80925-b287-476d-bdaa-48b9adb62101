package com.zte.uedm.battery.kafka;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.domain.impl.BatteryRemainDischargringDurationEvalDomainImpl;
import com.zte.uedm.battery.opti.application.event.kafka.listener.BatteryAssetChangeMsg;
import com.zte.uedm.battery.rpc.AssetRpcFake;
import com.zte.uedm.battery.service.impl.BattOverviewInitDataServiceImpl;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.cache.CacheManager;

import javax.annotation.Resource;


/* Started by AICoder, pid:0b7453f225ca458989f55ec0af38b279 */
public class BatteryAssetChangeMsgFTest {

    @InjectMocks
    private BatteryAssetChangeMsg batteryAssetChangeMsg;

    @Resource
    private BattOverviewInitDataServiceImpl battOverviewInitDataService = new BattOverviewInitDataServiceImpl();
    @Resource
    private AssetRpcFake assetRpc = new AssetRpcFake();

    @Mock
    private JsonService jsonService;
    @Mock
    private RedisService redisService;
    @Mock
    private CacheManager caffeineCacheManager;
    @Mock
    private BatteryRemainDischargringDurationEvalDomainImpl batteryRemainDischargringDurationEvalDomain;


    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    // @Test
    public void UEDM_304128_given_资产模块存在电池资产信息_电池监控对象id和action执行动作_when_调用kafka监听电池资产信息变更方法_then_新增成功() {
        String msg = "{\"model\":\"asset_instance\",\"topic\":\"asset_change\",\"types\":[\"82fafb6d-0419-49e3-b876-e07ca0460f57\"],\"action\":{\"id\":\"create\"},\"data\":[\"ea23c521-7724-4572-8ea3-07377bf2b00d\"]}";
        batteryAssetChangeMsg.onMsg(msg);
    }

    // @Test
    public void UEDM_304133_given_资产模块存在电池资产信息_电池监控对象id和action执行动作_when_调用kafka监听电池资产信息变更方法_then_更新成功() {
        String msg = "{\"model\":\"asset_instance\",\"topic\":\"asset_change\",\"types\":[\"82fafb6d-0419-49e3-b876-e07ca0460f57\"],\"action\":{\"id\":\"update\"},\"data\":[\"ea23c521-7724-4572-8ea3-07377bf2b00d\"]}";
        batteryAssetChangeMsg.onMsg(msg);
    }

    // @Test
    public void UEDM_304135_given_资产模块存在电池资产信息_电池监控对象id和action执行动作_when_调用kafka监听电池资产信息变更方法_then_删除成功() {
        String msg = "{\"model\":\"asset_instance\",\"topic\":\"asset_change\",\"types\":[\"82fafb6d-0419-49e3-b876-e07ca0460f57\"],\"action\":{\"id\":\"delete\"},\"data\":[\"ea23c521-7724-4572-8ea3-07377bf2b00d\"]}";
        batteryAssetChangeMsg.onMsg(msg);
    }

    // @Test
    public void UEDM_304131_given_资产模块存在电池资产信息_无法区分动作类型_when_调用kafka监听电池资产信息变更方法_then_更新成功() {
        String msg = "{\"model\":\"asset_instance\",\"topic\":\"asset_change\",\"types\":[\"82fafb6d-0419-49e3-b876-e07ca0460f57\"],\"action\":{\"id\":\"all\"},\"data\":[\"ea23c521-7724-4572-8ea3-07377bf2b00d\"]}";
        batteryAssetChangeMsg.onMsg(msg);
    }

}
/* Ended by AICoder, pid:0b7453f225ca458989f55ec0af38b279 */
