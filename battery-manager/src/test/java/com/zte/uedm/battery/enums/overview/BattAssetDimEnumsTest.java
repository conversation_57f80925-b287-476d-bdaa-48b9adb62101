package com.zte.uedm.battery.enums.overview;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.scheduling.annotation.Async;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class BattAssetDimEnumsTest {
@Mock
private BattAssetDimEnums battAssetDimEnums;
    @Test
    public void test()
    {
        BattAssetDimEnums.SUPPLIER.getId();
        BattAssetDimEnums.MANU_FACTUER.getId();
        BattAssetDimEnums.BATT_TYPE.getId();


        Assert.assertSame(BattAssetDimEnums.SUPPLIER.getId(), "supplier");
        Assert.assertSame(BattAssetDimEnums.MANU_FACTUER.getId(), "manufactuer");
        Assert.assertSame(BattAssetDimEnums.BATT_TYPE.getId(), "battType");
    }

    @Test
    public void test1()
    {
        BattAssetDimEnums.SUPPLIER.getName();
        BattAssetDimEnums.MANU_FACTUER.getName();
        BattAssetDimEnums.BATT_TYPE.getName();
        BattAssetDimEnums.SUPPLIER.getDefaultIndex();
        BattAssetDimEnums.SUPPLIER.getDefaultEnable();


        Assert.assertSame(BattAssetDimEnums.SUPPLIER.getName(), "{\"en_US\":\"Supplier\",\"zh_CN\":\"供应商\"}");
        Assert.assertSame(BattAssetDimEnums.MANU_FACTUER.getName(), "{\"en_US\":\"Manufacturer\",\"zh_CN\":\"制造商\"}");
        Assert.assertSame(BattAssetDimEnums.BATT_TYPE.getName(), "{\"en_US\":\"Batt Type\",\"zh_CN\":\"类型\"}");
    }

    @Test
    public void test2()
    {
        BattAssetDimEnums.SUPPLIER.getDefaultFixed();
        BattAssetDimEnums.MANU_FACTUER.getDefaultFixed();
        BattAssetDimEnums.BATT_TYPE.getDefaultFixed();


        Assert.assertSame(BattAssetDimEnums.SUPPLIER.getDefaultFixed(), false);
        Assert.assertSame(BattAssetDimEnums.MANU_FACTUER.getDefaultFixed(), false);
        Assert.assertSame(BattAssetDimEnums.BATT_TYPE.getDefaultFixed(), false);
    }




    @Test
    public void testGetAll()
    {
        List<String>  aaa =BattAssetDimEnums.getAllBattStatisticsAssetDim();
        Assert.assertEquals(3,aaa.size());
    }

    @Test
    public void getNameByIdTest()
    {
        Assert.assertSame("{\"en_US\":\"Batt Type\",\"zh_CN\":\"类型\"}", BattAssetDimEnums.getNameById("battType"));
        Assert.assertSame("", BattAssetDimEnums.getNameById("battTypse"));

    }
}
