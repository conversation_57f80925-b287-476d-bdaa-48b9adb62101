package com.zte.uedm.battery.controller.peakshift;

import com.github.pagehelper.PageInfo;
import com.zte.oes.dexcloud.configcenter.commons.event.ConfigChangeEvent;
import com.zte.oes.dexcloud.configcenter.commons.event.ConfigChangeItem;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.peak.PeakShiftDeviceAllConfigDto;
import com.zte.uedm.battery.bean.peak.PeakShiftDeviceConfigBean;
import com.zte.uedm.battery.bean.peak.PeakShiftDeviceEnableDto;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.service.PeakShiftDeviceService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.service.mp.api.adapter.AdapterPointDataService;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class PeakShiftDeviceControllerTest {

    @InjectMocks
    private PeakShiftDeviceController peakShiftDeviceController;

    @Mock
    private PeakShiftDeviceService peakShiftDeviceService;

    @Mock
    private JsonService jsonService;

    @Mock
    private AdapterPointDataService adapterPointDataService;

    @Mock
    private ConfigService configService;

    @Mock
    private CollectorCacheManager collectorCacheManager;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
        when(jsonService.objectToJson(any())).thenReturn("");

        CollectorEntity collector = new CollectorEntity();
        collector.setMoc(CommonConst.MOC_BCUA);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
    }

    @Test
    public void testSelectPeakShiftDeviceType() throws Exception
    {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();

        // Configure PeakShiftDeviceService.getPeakShiftDeviceType(...).
        final IdNameBean normalDto = new IdNameBean();
        normalDto.setName("name");
        normalDto.setId("id");
        final List<IdNameBean> normalDtos = Arrays.asList(normalDto);
        when(peakShiftDeviceService.getPeakShiftDeviceType("languageOption")).thenReturn(normalDtos);

        // Run the test
        final ResponseBean result = peakShiftDeviceController.selectPeakShiftDeviceType("languageOption", request);

        // Verify the results
        Assert.assertEquals(0, result.getCode().intValue());
    }

    @Test
    public void testSelectPeakShiftDeviceType_PeakShiftDeviceServiceReturnsNoItems() throws Exception
    {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        when(peakShiftDeviceService.getPeakShiftDeviceType("languageOption")).thenReturn(Collections.emptyList());

        // Run the test
        final ResponseBean result = peakShiftDeviceController.selectPeakShiftDeviceType("languageOption", request);

        // Verify the results
        Assert.assertEquals(0, result.getCode().intValue());
    }

    @Test
    public void testSelectPeakShiftDeviceType_PeakShiftDeviceServiceThrowsUedmException() throws Exception
    {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        when(peakShiftDeviceService.getPeakShiftDeviceType(anyString())).thenThrow(new UedmException(-1,""));

        // Run the test
        final ResponseBean result = peakShiftDeviceController.selectPeakShiftDeviceType("languageOption", request);

        // Verify the results
        Assert.assertEquals(-1, result.getCode().intValue());
    }

    /* Started by AICoder, pid:e0af2994c61c4c3142d40b9e30213f522230833a */
    @Test
    public void enableBCUADeviceTest() {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceConfigBean deviceConfigBean = new PeakShiftDeviceConfigBean();
        deviceConfigBean.setId("id");
        deviceConfigBean.setDeviceType("CSU5");
        ResponseBean responseBean = peakShiftDeviceController.enablePeakShiftDevice(deviceConfigBean, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void enableBCUADeviceTest_Exp() {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceConfigBean deviceConfigBean = new PeakShiftDeviceConfigBean();
        deviceConfigBean.setId("id");
        deviceConfigBean.setDeviceType("CSU");
        ResponseBean responseBean = peakShiftDeviceController.enablePeakShiftDevice(deviceConfigBean, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), -304);
    }

    @Test
    public void enableBCUADeviceTest1() {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceConfigBean deviceConfigBean = new PeakShiftDeviceConfigBean();
        ResponseBean responseBean = peakShiftDeviceController.enablePeakShiftDevice(deviceConfigBean, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT.intValue());
    }

    @Test
    public void enableBCUADeviceTest2() throws UedmException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceConfigBean deviceConfigBean = new PeakShiftDeviceConfigBean();
        deviceConfigBean.setId("id");
        deviceConfigBean.setDeviceType("CSU5");
        List<String> failedList = Collections.singletonList("ids");
        doReturn(failedList).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(Mockito.anyList(), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.enablePeakShiftDevice(deviceConfigBean, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.OTHER_TEMPORARY_ERROR.intValue());
    }

    @Test
    public void enableBCUADevice_ExcTest3() throws UedmException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceConfigBean deviceConfigBean = new PeakShiftDeviceConfigBean();
        deviceConfigBean.setId("id");
        deviceConfigBean.setDeviceType("CSU5");
        doThrow(new UedmException(-200, "")).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(Mockito.anyList(), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.enablePeakShiftDevice(deviceConfigBean, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), -200);
    }
    /* Ended by AICoder, pid:e0af2994c61c4c3142d40b9e30213f522230833a */

    @Test
    public void batchEnableBCUADevicesTest()
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceEnableDto deviceEnableDto = new PeakShiftDeviceEnableDto();
        deviceEnableDto.setId("id");
        deviceEnableDto.setDeviceType("CSU5");
        ResponseBean responseBean = peakShiftDeviceController.batchEnablePeakShiftDevices(Collections.singletonList(deviceEnableDto), "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), -301);
    }

    @Test
    public void batchEnableDevicesTest_Exp()
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceEnableDto deviceEnableDto = new PeakShiftDeviceEnableDto();
        deviceEnableDto.setId("id");
        deviceEnableDto.setDeviceType("CSU");
        deviceEnableDto.setName("name");
        ResponseBean responseBean2 = peakShiftDeviceController.batchEnablePeakShiftDevices(Collections.singletonList(deviceEnableDto), "", req);
        Assert.assertEquals(responseBean2.getCode().intValue(), -304);
    }

    @Test
    public void batchEnableBCUADevicesTest1() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceEnableDto deviceEnableDto = new PeakShiftDeviceEnableDto();
        deviceEnableDto.setId("id");
        deviceEnableDto.setName("name");
        deviceEnableDto.setDeviceType("CSU5");
        doReturn(Collections.singletonList("sss")).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(anyList(), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.batchEnablePeakShiftDevices(Collections.singletonList(deviceEnableDto), "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.OTHER_TEMPORARY_ERROR.intValue());
    }

    @Test
    public void batchEnableBCUADevicesTest2() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceEnableDto deviceEnableDto = new PeakShiftDeviceEnableDto();
        deviceEnableDto.setId("id");
        deviceEnableDto.setName("name");
        deviceEnableDto.setDeviceType("CSU5");
        doThrow(new UedmException(-200, "")).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(Mockito.anyList(), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.batchEnablePeakShiftDevices(Collections.singletonList(deviceEnableDto), "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), -200);
    }

    @Test
    public void allEnableBCUADevicesTest()
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceAllConfigDto allConfigDto = new PeakShiftDeviceAllConfigDto();
        allConfigDto.setLogicGroupId("groupId");
        ResponseBean responseBean = peakShiftDeviceController.allEnablePeakShiftDevices(allConfigDto, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void allEnableBCUADevicesTest1()
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceAllConfigDto allConfigDto = new PeakShiftDeviceAllConfigDto();
        ResponseBean responseBean = peakShiftDeviceController.allEnablePeakShiftDevices(allConfigDto, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(),-100);
    }

    @Test
    public void allEnableBCUADevicesTest2()
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceAllConfigDto allConfigDto = new PeakShiftDeviceAllConfigDto();
        allConfigDto.setLogicGroupId("groupId");
        ResponseBean responseBean = peakShiftDeviceController.allEnablePeakShiftDevices(allConfigDto, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(),0);
    }

    @Test
    public void allEnableBCUADevicesTest3() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceAllConfigDto allConfigDto = new PeakShiftDeviceAllConfigDto();
        allConfigDto.setLogicGroupId("groupId");
        doReturn(Collections.singletonList("id")).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(any(PeakShiftDeviceAllConfigDto.class), any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.allEnablePeakShiftDevices(allConfigDto, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.OTHER_TEMPORARY_ERROR.intValue());
    }

    @Test
    public void allEnableBCUADevices_ExcTest4() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceAllConfigDto allConfigDto = new PeakShiftDeviceAllConfigDto();
        allConfigDto.setLogicGroupId("groupId");
        doThrow(new UedmException(-200, "")).when(peakShiftDeviceService).setPeakShiftDeviceStrategies(any(PeakShiftDeviceAllConfigDto.class), any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = peakShiftDeviceController.allEnablePeakShiftDevices(allConfigDto, "", req);
        Assert.assertEquals(responseBean.getCode().intValue(), -200);
    }

    /* Started by AICoder, pid:96df643f24xe7f2143af0b56b1235a2b89000038 */
    @Test
    public void detailTest() throws Exception {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> ompMap = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> omPointMap = new HashMap<>();
        Map<String, AdapterPointDataVo> pointValueMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("result");
        pointValueMap.put("1", adapterPointDataVo);
        omPointMap.put("030184", pointValueMap);
        ompMap.put("2233", omPointMap);

        when(adapterPointDataService.getByCollectorIdAndAdapterId(anyList())).thenReturn(ompMap);
        doReturn(peakShiftDeviceBeanVo).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("result").when(configService).getGlobalProperty(Mockito.anyString());

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(1, responseBean.getTotal().intValue());
    }

    @Test
    public void detailTest1() throws UedmException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        ResponseBean responseBean = peakShiftDeviceController.detail("", "zh_CN", req);
        Assert.assertEquals(-301, responseBean.getCode().intValue());
    }

    @Test
    public void detailTest2() throws UedmException, IOException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");

        doReturn(peakShiftDeviceBeanVo).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("").when(configService).getGlobalProperty(Mockito.anyString());

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(-305, responseBean.getCode().intValue());
    }

    @Test
    public void detailTest3() throws Exception {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> ompMap = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> omPointMap = new HashMap<>();
        Map<String, AdapterPointDataVo> pointValueMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("result2");
        pointValueMap.put("1", adapterPointDataVo);
        omPointMap.put("030184", pointValueMap);
        ompMap.put("2233", omPointMap);

        when(adapterPointDataService.getByCollectorIdAndAdapterId(anyList())).thenReturn(ompMap);
        doReturn(peakShiftDeviceBeanVo).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("result_2").when(configService).getGlobalProperty(Mockito.anyString());

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(-306, responseBean.getCode().intValue());
    }

    @Test
    public void detailTest4() throws Exception {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");

        when(adapterPointDataService.getByCollectorIdAndAdapterId(anyList())).thenReturn(null);
        doReturn(peakShiftDeviceBeanVo).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("result_2").when(configService).getGlobalProperty(Mockito.anyString());

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @Test
    public void detailTest_EX1() throws Exception {
        HttpServletRequest req = mock(HttpServletRequest.class);

        doThrow(new UedmException(-200, "error")).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("result").when(configService).getGlobalProperty(Mockito.anyString());

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> ompMap = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> omPointMap = new HashMap<>();
        Map<String, AdapterPointDataVo> pointValueMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("result");
        pointValueMap.put("1", adapterPointDataVo);
        omPointMap.put("030184", pointValueMap);
        ompMap.put("2233", omPointMap);

        when(adapterPointDataService.getByCollectorIdAndAdapterId(anyList())).thenReturn(ompMap);

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(-200, responseBean.getCode().intValue());
    }

    @Test
    public void detailTest_EX2() throws Exception {
        HttpServletRequest req = mock(HttpServletRequest.class);
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");

        doReturn(peakShiftDeviceBeanVo).when(peakShiftDeviceService).detail(Mockito.any(), Mockito.any());
        doReturn("result").when(configService).getGlobalProperty(Mockito.anyString());
        doThrow(Exception.class).when(adapterPointDataService).getByCollectorIdAndAdapterId(anyList());

        ResponseBean responseBean = peakShiftDeviceController.detail("2233", "zh_CN", req);
        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:96df643f24xe7f2143af0b56b1235a2b89000038 */

    /* Started by AICoder, pid:w1602d427agfb26141930ab420b28029941314b1 */
    @Test
    public void selectByLogicAndDeviceTypeTest() throws UedmException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        List<String> logicIds = new ArrayList<>();

        PageInfo<String> pageInfoList = new PageInfo<>();
        pageInfoList.setList(logicIds);
        pageInfoList.setTotal(logicIds.size());

        Mockito.doReturn(pageInfoList).when(peakShiftDeviceService).selectByLogicAndDeviceType(Mockito.any(), Mockito.any(), Mockito.anyString());
        ResponseBean responseBean = peakShiftDeviceController.selectByLogicAndDeviceType(logicIds, "CSU5", 1, 10, "zh_CN", req);
        Assert.assertEquals(0, responseBean.getTotal().intValue());
    }

    @Test
    public void selectByLogicAndDeviceType_EX() throws UedmException {
        HttpServletRequest req = mock(HttpServletRequest.class);
        List<String> logicIds = new ArrayList<>();

        Mockito.doThrow(new UedmException(-200, "error")).when(peakShiftDeviceService).selectByLogicAndDeviceType(Mockito.any(), Mockito.any(), Mockito.anyString());
        ResponseBean responseBean = peakShiftDeviceController.selectByLogicAndDeviceType(logicIds, "CSU5", 1, 10, "zh_CN", req);
        Assert.assertEquals(-200, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:w1602d427agfb26141930ab420b28029941314b1 */

    @Test
    public void listenTest()
    {
        ConfigChangeEvent event = mock(ConfigChangeEvent.class);
        ConfigChangeItem item = new ConfigChangeItem();
        item.setKey("battery.peak.shift.configuration.port");
        item.setNewValue("123");
        item.setOldValue("321");
        Map<String, ConfigChangeItem> chgs = new HashMap<>();
        chgs.put(item.getKey(), item);
        doReturn(chgs).when(event).getChanges();
        peakShiftDeviceController.listen(event);
        assertEquals(item.getOldValue(),"321");
    }

    @Test
    public void listen_ExcTest() throws UedmException
    {
        ConfigChangeEvent event = mock(ConfigChangeEvent.class);
        ConfigChangeItem item = new ConfigChangeItem();
        item.setKey("battery.peak.shift.configuration.port");
        item.setNewValue("123");
        item.setOldValue("321");
        Map<String, ConfigChangeItem> chgs = new HashMap<>();
        chgs.put(item.getKey(), item);
        doReturn(chgs).when(event).getChanges();
        doThrow(new UedmException(-200, "")).when(peakShiftDeviceService).sendPortToAllBCUADevices(Mockito.anyString());
        peakShiftDeviceController.listen(event);
        assertEquals(item.getOldValue(),"321");
    }
}