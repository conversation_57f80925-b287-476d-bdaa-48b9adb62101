package com.zte.uedm.battery.opti.domain.battRiskEval.service.impl;

import com.zte.uedm.battery.bean.BattRiskEvalBean;
import com.zte.uedm.battery.bean.BattRiskEvalVO;
import com.zte.uedm.battery.enums.batteryrisk.BatteryRiskLevelEnums;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskEvalRepository;
import com.zte.uedm.battery.opti.domain.service.impl.BattRiskEvalDomainImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryRiskUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(PowerMockRunner.class)
public class BattRiskEvalDomainImplTest {

    @InjectMocks
    private BattRiskEvalDomainImpl battRiskEvalDomainImpl;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BattRiskEvalRepository riskEvalRepository;
    @Mock
    private BatteryRiskUtils batteryRiskUtils;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
        doReturn("123").when(i18nUtils).getMapFieldByLanguageOption(any(),any());

    }

    @Test
    public void batteryDetailRiskSelect() throws UedmException {
        //PowerMockito.mockStatic(DateUtils.class);
        Mockito.doReturn(new ArrayList<BattRiskEvalVO>()).when(riskEvalRepository).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
        ResponseBean responseBean = battRiskEvalDomainImpl.batteryDetailRiskSelect("1", "Ee", null);
        Mockito.doThrow(new UedmException(-200,"1")).when(riskEvalRepository).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
      //  ResponseBean responseBean1 = battRiskEvalDomainImpl.batteryDetailRiskSelect("1", "Ee", null);
        ArrayList<BattRiskEvalVO> battRiskEvalVOS = new ArrayList<>();
        BattRiskEvalVO battRiskEvalVO = new BattRiskEvalVO();
        battRiskEvalVO.setBattId("1");
        battRiskEvalVO.setName("南孚5号电池");
        battRiskEvalVO.setRiskName("{zh_CN:\"健康\",en_US:\"healthy\"}");
        battRiskEvalVO.setRiskLevel("level_1");
        battRiskEvalVO.setRiskCause("{zh_CN:\"打雷\",en_US:\"pong!pong!\"}");
        battRiskEvalVO.setRiskSuggestion("{zh_CN:\"阿萨德呼呼\",en_US:\"nono!\"}");
        battRiskEvalVO.setEvalTime("1699232047400");
        battRiskEvalVO.setRiskId("root");
        battRiskEvalVOS.add(battRiskEvalVO);
        List<BattRiskEvalBean>  battRiskEvals= new ArrayList<>();
        BattRiskEvalBean bean =new BattRiskEvalBean();
        bean.setRiskLevel("level_1");
        bean.setBattId("1");
        bean.setRiskId("root");
        battRiskEvals.add(bean);
        Mockito.doAnswer(invocation ->{
            List<BattRiskEvalBean> args = invocation.getArgument(0);
              args.stream().forEach(be -> be.setRiskLevel(BatteryRiskLevelEnums.LEVEL_4.getId()));
              return  null;
            }).when(batteryRiskUtils).dealBlank(battRiskEvals);
        Mockito.doReturn(battRiskEvalVOS).when(riskEvalRepository).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
        ResponseBean responseBean2 = battRiskEvalDomainImpl.batteryDetailRiskSelect("1", "en_US", null);
        //1698368969034
    }

    @Test
    public void selectRiskEvalLevel_test() throws UedmException {
        Assert.assertNotNull(battRiskEvalDomainImpl.selectRiskEvalLevel(new ServiceBaseInfoBean("aa","en_CH")));
    }


    @Test
    public void queryByBattIdAndStartEndTime_test() throws UedmException {
        Map<String, String> riskEvalMapHand = new HashMap<>();
        riskEvalMapHand.put("batt-1","level_1");
        riskEvalMapHand.put("batt-2","level_2");
        ArrayList<BattRiskEvalBean> battRiskEvalBeans = new ArrayList<>();
        BattRiskEvalBean battRiskEvalBean = new BattRiskEvalBean();
        battRiskEvalBean.setBattId("batt-1");
        battRiskEvalBean.setRiskLevel("level_2");
        battRiskEvalBeans.add(battRiskEvalBean);
        BattRiskEvalBean battRiskEvalBean2 = new BattRiskEvalBean();
        battRiskEvalBean2.setBattId("batt-1");
        battRiskEvalBean2.setRiskLevel("level_1");
        battRiskEvalBeans.add(battRiskEvalBean2);
        BattRiskEvalBean battRiskEvalBean3 = new BattRiskEvalBean();
        battRiskEvalBean3.setBattId("batt-3");
        battRiskEvalBean3.setRiskLevel("level_3");
        battRiskEvalBeans.add(battRiskEvalBean3);
        BattRiskEvalBean battRiskEvalBean4 = new BattRiskEvalBean();
        battRiskEvalBean4.setBattId("batt-4");
        battRiskEvalBean4.setRiskLevel("level_3");
        battRiskEvalBeans.add(battRiskEvalBean4);
        Assert.assertNotNull(battRiskEvalDomainImpl.queryByBattIdAndStartEndTime(new ArrayList<>(),new ArrayList<>()));
        Mockito.doReturn(battRiskEvalBeans).when(riskEvalRepository).queryByBattIdAndStartEndTime(Mockito.anyList(), Mockito.anyList(),Mockito.anyString());
        List<String> riskLevels = new ArrayList<>();
        riskLevels.add("level_2");
        List<String> ids = new ArrayList<>();
        ids.add("batt-1");
        ids.add("batt-2");
        ids.add("batt-3");
        ids.add("batt-4");
        Assert.assertNotNull(battRiskEvalDomainImpl.queryByBattIdAndStartEndTime(ids,riskLevels));
    }
    @Test
    public void queryByBattIdAndStartEndTime_test2() throws UedmException {
        //有维保的电池id
        final List<String> list = Arrays.asList("111", "115");
        //需要隐藏的风险id
        final List<String> list2 = Arrays.asList("root", "root3");
        //当天风险
        List<BattRiskEvalBean> battRiskEvalBeans = new ArrayList<>();
        BattRiskEvalBean bean =new BattRiskEvalBean();
        bean.setRiskLevel("level_1");
        bean.setBattId("111");
        bean.setRiskId("root");
        BattRiskEvalBean bean2 =new BattRiskEvalBean();
        bean2.setRiskLevel("level_1");
        bean2.setBattId("111");
        bean2.setRiskId("root3");
        BattRiskEvalBean bean3 =new BattRiskEvalBean();
        bean3.setRiskLevel("level_2");
        bean3.setBattId("111");
        bean3.setRiskId("root1");
        BattRiskEvalBean bean4 =new BattRiskEvalBean();
        bean3.setRiskLevel("level_3");
        bean3.setBattId("111");
        bean3.setRiskId("root6");
        battRiskEvalBeans.add(bean2);
        battRiskEvalBeans.add(bean);
        battRiskEvalBeans.add(bean3);
        Mockito.doReturn(battRiskEvalBeans).when(riskEvalRepository).queryByBattIdAndStartEndTime(Mockito.anyList(), Mockito.anyList(),Mockito.anyString());
        //
        Mockito.doAnswer(invocation -> {
            List<BattRiskEvalBean>  riskbeans = invocation.getArgument(0);
            for(BattRiskEvalBean riskbean:riskbeans){
                if(list.contains(riskbean.getBattId())&&list2.contains(riskbean.getRiskId())){
                    //隐藏风风险
                    riskbean.setRiskLevel(BatteryRiskLevelEnums.LEVEL_4.getId());
                }
            }
            return null;
        }).when(batteryRiskUtils).dealBlank(battRiskEvalBeans);
        List<String> riskLevels = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        ids.add("111");
        ids.add("113");
        Assert.assertNotNull(battRiskEvalDomainImpl.queryByBattIdAndStartEndTime(ids,riskLevels));
    }
}