package com.zte.uedm.battery.bean.pv;

import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.battery.bean.ThresholdBean;
import lombok.SneakyThrows;
import org.junit.Test;

public class PvEfficiencyLevelBeanTest {
    @SneakyThrows
    @Test
    public void test(){
        PojoTestUtil.TestForPojo(PvEfficiencyLevelBean.class);
        new PvEfficiencyLevelBean().toString();
        new PvEfficiencyLevelBean(new PvMaintenceReminderPo()).toString();
        new PvEfficiencyLevelBean("",new ThresholdBean()).toString();
    }

}
