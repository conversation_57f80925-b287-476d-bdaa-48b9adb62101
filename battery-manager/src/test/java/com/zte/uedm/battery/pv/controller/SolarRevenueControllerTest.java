package com.zte.uedm.battery.pv.controller;

import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.domain.PriceStrategyDomain;
import com.zte.uedm.battery.pv.bean.SolarRecollectionLogBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueOverviewExportBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueScopeStrategyQueryBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueStatisticsBean;
import com.zte.uedm.battery.pv.dto.ManualSupplementaryDto;
import com.zte.uedm.battery.pv.dto.SiteHistoryScopeIdQueryDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueOverviewDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueScopeStatisticsDto;
import com.zte.uedm.battery.pv.service.impl.SolarRecollectionServiceImpl;
import com.zte.uedm.battery.pv.service.impl.SolarRevenueServiceImpl;
import com.zte.uedm.battery.pv.service.impl.SolarRevenueStatisticsServiceImpl;
import com.zte.uedm.battery.pv.vo.SolarRevenueOverviewVo;
import com.zte.uedm.battery.rpc.vo.CurrRecollectionSiteVo;
import com.zte.uedm.battery.schedule.SolarRevenueStatisticsJob;
import com.zte.uedm.battery.update.v16232009v16232010.UpdateRevenueScopeExecuteImpl;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class})
public class SolarRevenueControllerTest {
    @InjectMocks
    private SolarRevenueController solarRevenueController;
    @Mock
    SolarRevenueServiceImpl solarRevenueServiceImpl;
    @Mock
    JsonService jsonService;
    @Mock
    MessageSenderService msgSenderService;
    @Mock
    private SolarRevenueStatisticsJob solarRevenueStatisticsJob;
    @Mock
    private SolarRevenueStatisticsServiceImpl solarRevenueStatisticsServiceImpl;
    @Mock
    private SolarRecollectionServiceImpl solarRecollectionServiceImpl;

    @Mock
    private PriceStrategyDomain priceStrategyDomain;
    @Mock
    private UpdateRevenueScopeExecuteImpl updateRevenueScopeExecute;

    private HttpServletRequest request;
    private HttpServletResponse response;

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
        PowerMockito.mockStatic(Tools.class);
    }
    @Test
    public void exportOverview() throws UedmException {
        SolarRevenueOverviewExportBean exportBean = new SolarRevenueOverviewExportBean();
        exportBean.setLogicGroupId("abc");
        Mockito.doThrow(new UedmException(-1,"")).when(solarRevenueServiceImpl).exportOverview(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("sdc", "zh-CN");
        when(jsonService.objectToJson(Mockito.any())).thenReturn("jsonString");
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        solarRevenueController.exportOverview(exportBean,request,response,"zh-CN");
    }

    @Test
    public void exportOverview1() throws UedmException {
        SolarRevenueOverviewExportBean exportBean = new SolarRevenueOverviewExportBean();
        exportBean.setLogicGroupId("abc");
        when(solarRevenueServiceImpl.exportOverview(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("file");
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("sdc", "zh-CN");
        when(jsonService.objectToJson(Mockito.any())).thenReturn("jsonString");
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        solarRevenueController.exportOverview(exportBean,request,response,"zh-CN");
        solarRevenueController.exportOverview(null,request,response,"zh-CN");
    }

    @Test
    public void queryStatisticsTest() throws UedmException {
        boolean flag = true;
        try {
            PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("user1");

            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            SolarRevenueOverviewVo result = new SolarRevenueOverviewVo();
            Mockito.doReturn(result).when(solarRevenueServiceImpl).queryStatistics(Mockito.any(),Mockito.any(),Mockito.any());

            solarRevenueController.queryStatistics(new SolarRevenueOverviewDto(), "zh-CN", request);
        } catch (Exception e) {
            Assert.assertEquals(true, flag);
        }
    }

    @Test
    public void queryStatisticsParamNullTest() throws UedmException {
        boolean flag = true;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            SolarRevenueOverviewVo result = new SolarRevenueOverviewVo();
            Mockito.doReturn(null).when(solarRevenueServiceImpl).queryStatistics(Mockito.any(),Mockito.any(),Mockito.any());

            solarRevenueController.queryStatistics(null, "zh-CN", request);
        } catch (Exception e) {
            Assert.assertEquals(true, flag);
            e.printStackTrace();
        }
    }

    @Test
    public void queryStatisticsParamTest_Exc() {
        try {
            Mockito.doThrow(new UedmException(-635,"")).when(solarRevenueServiceImpl).queryStatistics(Mockito.any(),Mockito.any(),Mockito.any());

            solarRevenueController.queryStatistics(null, "zh-CN", request);
        } catch (UedmException e) {
            Assert.assertEquals(-635, e.getErrorId().intValue());
        }
    }

    @Test
    public void solarRevenueStatisticsJobTest() {
        ResponseBean responseBean = solarRevenueController.solarRevenueStatisticsJob(null, null);
        Assert.assertEquals(responseBean.getCode(), new Integer(0));
    }

    @Test
    public void solarRevenueStatisticsManualTest() throws UedmException, ParseException, InterruptedException {
        Mockito.doNothing().when(solarRevenueStatisticsServiceImpl).solarRevenueStatisticsManual(Mockito.any(SolarRevenueScopeStatisticsDto.class));
        ResponseBean responseBean = solarRevenueController.solarRevenueStatisticsManual(new SolarRevenueScopeStatisticsDto(), null, null);
        Assert.assertEquals(responseBean.getCode(), new Integer(0));
    }

    @Test
    public void manualSupplementaryTest() throws UedmException {
        ManualSupplementaryDto dto = new ManualSupplementaryDto();
        try {
            ResponseBean responseBean = solarRevenueController.manualSupplementary(dto, "zh-CN", request);
            Assert.assertEquals(-204,responseBean.getCode().intValue() );
        }catch (Exception e){

        }

        List<String> siteIdList = new ArrayList<>();
        siteIdList.add("aaa");
        dto.setSiteIdList(siteIdList);
        ResponseBean responseBean = solarRevenueController.manualSupplementary(dto, "zh-CN", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());

        doThrow(new UedmException(-301, "站点不支持补采")).when(solarRecollectionServiceImpl).manualSupplementaryByTimeRange(Mockito.any(), Mockito.any(), Mockito.any());
        try {
            solarRevenueController.manualSupplementary(dto, "zh-CN", request);
        }catch (Exception e){
            Assert.assertEquals("站点不支持补采", e.getMessage());
        }
    }

    @Test
    public void getRecollectionLogBySiteTest() throws UedmException {
        List<SolarRecollectionLogBean> list = new ArrayList<>();
        when(solarRecollectionServiceImpl.getRecollectionLogBySite(any())).thenReturn(list);

        List<String> siteIdList = new ArrayList<>();
        siteIdList.add("aaa");
        ResponseBean responseBean = solarRevenueController.getRecollectionLogBySite(siteIdList, "zh_CN", request);
        Assert.assertEquals(0, responseBean.getTotal().intValue());

        doThrow(new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc())).when(solarRecollectionServiceImpl).getRecollectionLogBySite(any());
        try {
            solarRevenueController.getRecollectionLogBySite(siteIdList, "zh_CN", request);
        } catch (Exception e){
            Assert.assertEquals(DatabaseExceptionEnum.OPERATEDB.getDesc(), e.getMessage());
        }

        doThrow(new RuntimeException()).when(solarRecollectionServiceImpl).getRecollectionLogBySite(any());
        try {
            solarRevenueController.getRecollectionLogBySite(siteIdList, "zh_CN", request);
        } catch (Exception e){
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void getCurrentRecollectionSiteNumTest(){
        CurrRecollectionSiteVo recollectionSiteVo = new CurrRecollectionSiteVo();
        recollectionSiteVo.setCurrentRecollectionSiteNum(10);
        recollectionSiteVo.setConcurrentSitesAcrossTheEntireNetwork(20);
        when(solarRecollectionServiceImpl.getCurrentRecollectionSiteNum()).thenReturn(recollectionSiteVo);
        ResponseBean responseBean = solarRevenueController.getCurrentRecollectionSiteNum("zh-CN");
        assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void getSiteHistoryScopeIdsByTimeTest() throws Exception {
        SiteHistoryScopeIdQueryDto dto = new SiteHistoryScopeIdQueryDto();
        dto.setBeginTime("2023-12-01");
        dto.setEndTime("2023-12-01");
        //站点id为空
        try {
            solarRevenueController.getSiteHistoryScopeIdsByTime(dto, "zh-CN", null);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
        //站点id为空
        dto.setSiteId("site1");
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("user1");
        Mockito.doReturn(new ArrayList<>()).when(solarRevenueServiceImpl).siteHistoryScopeIdSelect(Mockito.any(SiteHistoryScopeIdQueryDto.class), Mockito.any(String.class), Mockito.any(String.class));
        ResponseBean responseBean = solarRevenueController.getSiteHistoryScopeIdsByTime(dto, "zh-CN", null);
        Assert.assertEquals(0, responseBean.getCode().intValue());
        //站点id为空
        dto.setSiteId("site1");
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("user1");
        Mockito.doThrow(new UedmException(DatabaseExceptionEnum.OPERATEDB.getCode(), DatabaseExceptionEnum.OPERATEDB.getDesc())).when(solarRevenueServiceImpl).siteHistoryScopeIdSelect(Mockito.any(SiteHistoryScopeIdQueryDto.class), Mockito.any(String.class), Mockito.any(String.class));
        try {
            solarRevenueController.getSiteHistoryScopeIdsByTime(dto, "zh-CN", null);
        } catch (Exception e) {
            Assert.assertEquals(DatabaseExceptionEnum.OPERATEDB.getDesc(), e.getMessage());
        }
    }

    @Test
    public void getSolarRevenuePriceTest() throws Exception {
        SolarRevenueScopeStrategyQueryBean queryBean = new SolarRevenueScopeStrategyQueryBean();
        queryBean.setStartDate("2023-11-23");
        queryBean.setEndDate("2023-11-23");
        queryBean.setMinuteConvergeCycle(5);
        queryBean.setSiteIds(Arrays.asList("Site-gsgmkt"));

        SolarRevenueStatisticsBean vo = new SolarRevenueStatisticsBean();

        when(priceStrategyDomain.getSolarRevenuePriceStrategy(queryBean)).thenReturn(vo);
        ResponseBean responseBean = solarRevenueController.getSolarRevenuePrice(queryBean, request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
    @Test
    public void testUpdateExecuteJob() {
        // Arrange
        String languageOption = "en";

        // Act
        ResponseBean response = solarRevenueController.updateExecuteJob(languageOption);

        // Assert
        verify(updateRevenueScopeExecute, times(1)).updateExecute();
        assertEquals(0, response.getCode().intValue());
        assertNull(response.getData());
        assertNull(response.getMessage());
    }

    @Test
    public void testUpdateExecuteJobWithException() {
        // Arrange
        String languageOption = "en";

        doThrow(new RuntimeException()).when(updateRevenueScopeExecute).updateExecute();

        // Act
        ResponseBean response = solarRevenueController.updateExecuteJob(languageOption);

        // Assert
        verify(updateRevenueScopeExecute, times(1)).updateExecute();
        assertEquals(-1, response.getCode().intValue());
    }
}