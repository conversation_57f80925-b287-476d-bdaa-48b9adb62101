package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;


public class AlarmConfigResponseBeanTest
{
    @Test
    public void test()
    {
        AlarmConfigResponseBean alarmConfigResponseBean = new AlarmConfigResponseBean();
        alarmConfigResponseBean.setAlarmDesc("alarmDesc");
        alarmConfigResponseBean.setAlarmType("3");
        alarmConfigResponseBean.setExpressionDesc("expressionDesc");
        alarmConfigResponseBean.setSeverity(1);
        Assert.assertEquals(""+1,""+alarmConfigResponseBean.getSeverity());
    }
}