package com.zte.uedm.battery.a_domain.service.peakshift.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.*;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.GridStrategyRepository;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.TemplateStrategyRepository;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.UpDownloadFileRepository;
import com.zte.uedm.battery.a_domain.gateway.ConfigurationServiceInterface;
import com.zte.uedm.battery.a_domain.service.peakshift.GridStrategyService;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftTemplateFileService;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.PeakShiftDistributionTaskDetailMapper;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskDetailPo;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.TemplateStrategyDetailBcuaPo;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.TemplateStrategyDetailCsuPo;
import com.zte.uedm.battery.a_interfaces.peakshift.inner.dto.QueryFileIdRpcDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateQueryDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyAddDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyDto;
import com.zte.uedm.battery.bean.UpDownloadFileBean;
import com.zte.uedm.battery.bean.peak.*;
import com.zte.uedm.battery.consts.CommonConst;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.IsKafkaBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class TemplateStrategyServiceImplTest {

    @InjectMocks
    private TemplateStrategyServiceImpl templateStrategyService;

    @Mock
    private ConfigurationServiceInterface configurationServiceInterface;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private JsonService jsonService;

    @Mock
    private GridStrategyRepository gridStrategyRepository;

    @Mock
    private TemplateStrategyRepository templateStrategyRepository;

    @Mock
    private MessageSenderService msgSenderService;

    @Mock
    private PeakShiftDistributionTaskDetailMapper peakShiftDistributionTaskDetailMapper;

    @Mock
    private UpDownloadFileRepository upDownloadFileRepository;

    @Mock
    private PeakShiftTemplateFileService peakShiftTemplateFileService;

    @Mock
    private GridStrategyService gridStrategyService;

    @Mock
    private I18nUtils i18nUtils;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAddTemplateStrategy() throws Exception
    {
        // Setup
        final TemplateStrategyAddDto strategyBaseDto = new TemplateStrategyAddDto();
        strategyBaseDto.setName("name");
        strategyBaseDto.setDeviceType("deviceType");
        strategyBaseDto.setSource("source");
        strategyBaseDto.setSeasonStrategyId("seasonStrategyId");
        strategyBaseDto.setMode("2");
        strategyBaseDto.setRemark("remark");
        final TemplateDetailBaseDto templateDetailBaseDto = new TemplateDetailBaseDto();
        final TemplateTimeGranDetailVo templateTimeGranDetailVo = new TemplateTimeGranDetailVo();
        templateTimeGranDetailVo.setBeginTime("beginTime");
        templateTimeGranDetailVo.setEndTime("endTime");
        templateTimeGranDetailVo.setStrategyType(0);
        templateDetailBaseDto.setDetail(Arrays.asList(templateTimeGranDetailVo));
        strategyBaseDto.setDetail(Arrays.asList(templateDetailBaseDto));
        final TemplateHolidayDto templateDetailBaseDto1 = new TemplateHolidayDto();
        final TemplateTimeGranDetailVo templateTimeGranDetailVo1 = new TemplateTimeGranDetailVo();
        templateTimeGranDetailVo1.setBeginTime("beginTime");
        templateTimeGranDetailVo1.setEndTime("endTime");
        templateTimeGranDetailVo1.setStrategyType(0);
        templateDetailBaseDto1.setDetail(Arrays.asList(templateTimeGranDetailVo1));
        strategyBaseDto.setHoliday(Arrays.asList(templateDetailBaseDto1));

        final StrategyCombinationEntity strategyCombinationVo = new StrategyCombinationEntity();
        strategyCombinationVo.setScopeStrategyName("scopeStrategyName");
        strategyCombinationVo.setSeasonStrategyId("seasonStrategyId");
        strategyCombinationVo.setSeasonStrategyName("seasonStrategyName");
        strategyCombinationVo.setEffectiveTime("effectiveTime");
        strategyCombinationVo.setExpirationTime("expirationTime");
        strategyCombinationVo.setMode(0);
        strategyCombinationVo.setStatus(0);
        final List<StrategyCombinationEntity> strategyCombinationVos = Arrays.asList(strategyCombinationVo);
        when(gridStrategyRepository.getStrategyCombination(Mockito.any())).thenReturn(strategyCombinationVos);

        when(templateStrategyRepository.countName("name")).thenReturn(0);
        when(dateTimeService.getCurrentTime()).thenReturn("gmtCreate");
        when(jsonService.objectToJson(Arrays.asList(new TemplateTimeGranDetailVo()))).thenReturn("detail");
        when(templateStrategyRepository.insertTemplateStrategyCsuDetail(
                Arrays.asList(new TemplateStrategyDetailCsuPo()))).thenReturn(0);
        when(templateStrategyRepository.insertTemplateStrategyDetail(
                Arrays.asList(new TemplateStrategyDetailBcuaPo()))).thenReturn(0);
        when(templateStrategyRepository.insertTemplateStrategy(any(TemplateStrategyDto.class))).thenReturn(0);

        // Run the test
        templateStrategyService.addTemplateStrategy(strategyBaseDto, "user_create");

    }

    @Test
    public void testAddTemplateStrategy1() throws Exception
    {
        final TemplateStrategyAddDto strategyBaseDto = new TemplateStrategyAddDto();
        strategyBaseDto.setName("name");
        strategyBaseDto.setDeviceType("deviceType");
        strategyBaseDto.setSource("source");
        strategyBaseDto.setSeasonStrategyId("seasonStrategyId");
        strategyBaseDto.setMode("3");
        strategyBaseDto.setRemark("remark");
        final TemplateDetailBaseDto templateDetailBaseDto = new TemplateDetailBaseDto();
        final TemplateTimeGranDetailVo templateTimeGranDetailVo = new TemplateTimeGranDetailVo();
        templateTimeGranDetailVo.setBeginTime("beginTime");
        templateTimeGranDetailVo.setEndTime("endTime");
        templateTimeGranDetailVo.setStrategyType(0);
        templateDetailBaseDto.setDetail(Arrays.asList(templateTimeGranDetailVo));
        TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
        templateTimeGranVo.setBegin("2023-05-06");
        templateTimeGranVo.setEnd("2024-08-06");
        templateDetailBaseDto.setTimeRange(templateTimeGranVo);
        strategyBaseDto.setDetail(Arrays.asList(templateDetailBaseDto));
        final TemplateHolidayDto holidayDto = new TemplateHolidayDto();
        final TemplateTimeGranDetailVo templateTimeGranDetailVo1 = new TemplateTimeGranDetailVo();
        templateTimeGranDetailVo1.setBeginTime("beginTime");
        templateTimeGranDetailVo1.setEndTime("endTime");
        templateTimeGranDetailVo1.setStrategyType(0);
        holidayDto.setTimeRange(templateTimeGranVo);
        holidayDto.setDetail(Arrays.asList(templateTimeGranDetailVo1));
        strategyBaseDto.setHoliday(Arrays.asList(holidayDto));

        final StrategyCombinationEntity strategyCombinationVo = new StrategyCombinationEntity();
        strategyCombinationVo.setScopeStrategyName("scopeStrategyName");
        strategyCombinationVo.setSeasonStrategyId("seasonStrategyId");
        strategyCombinationVo.setSeasonStrategyName("seasonStrategyName");
        strategyCombinationVo.setEffectiveTime("effectiveTime");
        strategyCombinationVo.setExpirationTime("expirationTime");
        strategyCombinationVo.setMode(0);
        strategyCombinationVo.setStatus(0);
        final List<StrategyCombinationEntity> strategyCombinationVos = Arrays.asList(strategyCombinationVo);
        when(gridStrategyRepository.getStrategyCombination(Mockito.any())).thenReturn(strategyCombinationVos);

        when(templateStrategyRepository.countName("name")).thenReturn(0);
        when(dateTimeService.getCurrentTime()).thenReturn("gmtCreate");
        when(jsonService.objectToJson(Arrays.asList(new TemplateTimeGranDetailVo()))).thenReturn("detail");
        when(templateStrategyRepository.insertTemplateStrategyCsuDetail(
                Arrays.asList(new TemplateStrategyDetailCsuPo()))).thenReturn(0);
        when(templateStrategyRepository.insertTemplateStrategyDetail(
                Arrays.asList(new TemplateStrategyDetailBcuaPo()))).thenReturn(0);
        when(templateStrategyRepository.insertTemplateStrategy(any(TemplateStrategyDto.class))).thenReturn(0);
        Assertions.assertDoesNotThrow(()->templateStrategyService.addTemplateStrategy(strategyBaseDto, "user_create"));

    }

    @Test
    public void addTemplateStrategyTest1() throws UedmException, com.zte.uedm.common.exception.UedmException {
        TemplateStrategyAddDto templateStrategyAddDto = getTemplateStrategyAddDto();
        templateStrategyAddDto.setMode("0");
        List<TemplateHolidayDto> holiday = templateStrategyAddDto.getHoliday();
        holiday.add(new TemplateHolidayDto());
        holiday.add(new TemplateHolidayDto());
        templateStrategyAddDto.setHoliday(holiday);
        StrategyCombinationEntity vo = new StrategyCombinationEntity();
        vo.setSeasonStrategyId("01-15");
        doReturn(Arrays.asList(vo)).when(gridStrategyRepository).getStrategyCombination(Mockito.any());
        doReturn("2022").when(dateTimeService).getCurrentTime();
        doReturn("2022").when(jsonService).objectToJson(Mockito.any());
        doReturn(1).when(templateStrategyRepository).insertTemplateStrategy(Mockito.any());
        doReturn(1).when(templateStrategyRepository).insertTemplateStrategyDetail(Mockito.any());
        doReturn(0).when(templateStrategyRepository).countName(Mockito.any());
        PowerMockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.anyString(), Mockito.anyString());

        assertThrows(UedmException.class,()->templateStrategyService.addTemplateStrategy(templateStrategyAddDto, "zh_CN"));

        templateStrategyAddDto.setMode("1");
        assertThrows(UedmException.class,()->templateStrategyService.addTemplateStrategy(templateStrategyAddDto, "zh_CN"));
    }

    @Test
    public void addTemplateStrategyTest3() throws UedmException
    {

        TemplateStrategyAddDto templateStrategyAddDto = getTemplateStrategyAddDto();
        StrategyCombinationEntity vo = new StrategyCombinationEntity();
        vo.setSeasonStrategyId("01-19");
        doReturn(Arrays.asList(vo)).when(gridStrategyRepository).getStrategyCombination(Mockito.any());
        assertThrows(UedmException.class,()->templateStrategyService.addTemplateStrategy(templateStrategyAddDto, "zh_CN"));

    }

    private TemplateStrategyAddDto getTemplateStrategyAddDto()
    {
        TemplateStrategyAddDto templateStrategyAddDto = new TemplateStrategyAddDto();
        templateStrategyAddDto.setName("2022");
        templateStrategyAddDto.setSeasonStrategyId("01-15");
        templateStrategyAddDto.setMode("0");
        templateStrategyAddDto.setRemark("2022");
        templateStrategyAddDto.setSource("test");
        templateStrategyAddDto.setDeviceType("test");

        List<TemplateHolidayDto> holiday = new ArrayList<>();
        TemplateHolidayDto holidayVo = new TemplateHolidayDto();
        List<TemplateTimeGranVo> timeGranVos = new ArrayList<>();
        TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
        templateTimeGranVo.setBegin("test");
        templateTimeGranVo.setEnd("test");
        templateTimeGranVo.setRemark("test");
        timeGranVos.add(templateTimeGranVo);
        holidayVo.setTimeGran(timeGranVos);
        List<TemplateDetailBaseDto> detail = new ArrayList<>();
        TemplateDetailBaseDto vo = new TemplateDetailBaseDto();
        List<Integer> times = new ArrayList<>();
        times.add(1);
        vo.setTimeGran(times);
        List<TemplateTimeGranDetailVo> detailVos = new ArrayList<>();
        TemplateTimeGranDetailVo detailVo = new TemplateTimeGranDetailVo();
        detailVo.setStrategyType(1);
        detailVo.setBeginTime("test");
        detailVo.setEndTime("test");
        detailVos.add(detailVo);
        vo.setDetail(detailVos);
        detail.add(vo);
        templateStrategyAddDto.setDetail(detail);
        holidayVo.setDetail(detailVos);
        holiday.add(holidayVo);
        templateStrategyAddDto.setHoliday(holiday);
        return templateStrategyAddDto;
    }

    /* Started by AICoder, pid:e2f5f513ada34527922f59d157321933 */
    @Test
    public void testGetCsu5Detail_Success() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        List<TemplateStrategyDetailCsu5Entity> details = new ArrayList<>();
        TemplateStrategyDetailCsu5Entity detail1 = new TemplateStrategyDetailCsu5Entity();
        detail1.setBeginDate("2022-01-01");
        detail1.setEndDate("2022-01-07");
        detail1.setRemark("Weekend");
        detail1.setDetail("[{\"day\":\"MONDAY\",\"time\":\"08:00-12:00\"},{\"day\":\"TUESDAY\",\"time\":\"14:00-18:00\"}]");
        detail1.setHolidayFlag(false);
        details.add(detail1);

        TemplateStrategyDetailCsu5Entity detail2 = new TemplateStrategyDetailCsu5Entity();
        detail2.setBeginDate("2022-02-01");
        detail2.setEndDate("2022-02-02");
        detail2.setRemark("New Year's Day");
        detail2.setDetail("[{\"day\":\"WEDNESDAY\",\"time\":\"09:00-17:00\"}]");
        detail2.setHolidayFlag(true);
        details.add(detail2);

        when(templateStrategyRepository.selectCsu5DetailByTemplateId(templateId)).thenReturn(details);

        // 执行测试
        TemplateStrategyDetailEntity result = templateStrategyService.getCsu5Detail(templateStrategyDetailBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertEquals(1, result.getDetail().size());
        assertEquals(1, result.getHoliday().size());
        assertEquals("Weekend", result.getDetail().get(0).getTimeRange().getRemark());
        assertEquals("New Year's Day", result.getHoliday().get(0).getTimeRange().getRemark());
    }

    @Test
    public void testGetCsu5Detail_EmptyDetails() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        when(templateStrategyRepository.selectCsu5DetailByTemplateId(templateId)).thenReturn(new ArrayList<>());

        // 执行测试
        TemplateStrategyDetailEntity result = templateStrategyService.getCsu5Detail(templateStrategyDetailBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertTrue(result.getDetail().isEmpty());
        assertTrue(result.getHoliday().isEmpty());
    }

    @Test(expected = UedmException.class)
    public void testGetCsu5Detail_RepositoryThrowsException() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        doThrow(UedmException.class).when(templateStrategyRepository).selectCsu5DetailByTemplateId(templateId);

        // 执行测试，预期抛出异常
        templateStrategyService.getCsu5Detail(templateStrategyDetailBo);
    }
    /* Ended by AICoder, pid:e2f5f513ada34527922f59d157321933 */

    /* Started by AICoder, pid:2f4c67cc518c4d558028a1ddb3700073 */
    @Test
    public void testGetBcuaDetail_Success() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        List<TemplateStrategyDetailBcuaEntity> details = new ArrayList<>();
        TemplateStrategyDetailBcuaEntity detail1 = new TemplateStrategyDetailBcuaEntity();
        detail1.setHolidayFlag(false);
        detail1.setTimeGran("[1,2,3]");
        detail1.setDetail("[{\"day\":\"MONDAY\",\"time\":\"08:00-12:00\"},{\"day\":\"TUESDAY\",\"time\":\"14:00-18:00\"}]");
        details.add(detail1);

        TemplateStrategyDetailBcuaEntity detail2 = new TemplateStrategyDetailBcuaEntity();
        detail2.setHolidayFlag(true);
        detail2.setHolidayTimeGran("[{\"begin\":\"2022-01-01\",\"end\":\"2022-01-07\"}]");
        detail2.setDetail("[{\"day\":\"WEDNESDAY\",\"time\":\"09:00-17:00\"}]");
        details.add(detail2);

        when(templateStrategyRepository.selectDetailByTemplateId(templateId)).thenReturn(details);

        // 执行测试
        TemplateStrategyDetailEntity result = templateStrategyService.getBcuaDetail(templateStrategyDetailBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertEquals(1, result.getDetail().size());
        assertEquals(1, result.getHoliday().size());
        assertEquals(Arrays.asList(1, 2, 3), result.getDetail().get(0).getTimeGran());
        assertEquals("2022-01-01", result.getHoliday().get(0).getTimeGran().get(0).getBegin());
        assertEquals("2022-01-07", result.getHoliday().get(0).getTimeGran().get(0).getEnd());
    }

    @Test
    public void testGetBcuaDetail_EmptyDetails() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        when(templateStrategyRepository.selectDetailByTemplateId(templateId)).thenReturn(new ArrayList<>());

        // 执行测试
        TemplateStrategyDetailEntity result = templateStrategyService.getBcuaDetail(templateStrategyDetailBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertTrue(result.getDetail().isEmpty());
        assertTrue(result.getHoliday().isEmpty());
    }

    @Test(expected = UedmException.class)
    public void testGetBcuaDetail_RepositoryThrowsException() throws UedmException {
        // 准备测试数据
        String templateId = "123";
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId(templateId);

        doThrow(UedmException.class).when(templateStrategyRepository).selectDetailByTemplateId(templateId);

        // 执行测试，预期抛出异常
        templateStrategyService.getBcuaDetail(templateStrategyDetailBo);
    }
    /* Ended by AICoder, pid:2f4c67cc518c4d558028a1ddb3700073 */

    /* Started by AICoder, pid:4aede9369aa042e78749c96fd98943eb */
    @Test
    public void testCheckNameUnique_NullName() throws UedmException {
        String id = "123";
        String name = null;

        try {
            templateStrategyService.checkNameUnique(id, name);
            fail("Expected UedmException");
        } catch (UedmException e) {
            assertEquals(UedmErrorCodeConstants.PARAMETER_NAME_REPEAT, e.getErrorId());
            assertEquals("name is null", e.getMessage());
        }
    }

    @Test
    public void testCheckNameUnique_NameUnique() throws UedmException {
        String id = "123";
        String name = "UniqueName";

        List<TemplateStrategyDetailEntity> allBos = new ArrayList<>();
        when(templateStrategyRepository.selectTemplateByIdForCheckName(name, id)).thenReturn(allBos);

        Boolean result = templateStrategyService.checkNameUnique(id, name);
        assertTrue(result);
    }

    @Test
    public void testCheckNameUnique_NameNotUnique() throws UedmException {
        String id = "123";
        String name = "NonUniqueName";

        List<TemplateStrategyDetailEntity> allBos = new ArrayList<>();
        allBos.add(new TemplateStrategyDetailEntity()); // Add a dummy object to simulate a non-unique name
        when(templateStrategyRepository.selectTemplateByIdForCheckName(name, id)).thenReturn(allBos);

        Boolean result = templateStrategyService.checkNameUnique(id, name);
        assertFalse(result);
    }

    @Test(expected = UedmException.class)
    public void testCheckNameUnique_RepositoryThrowsException() throws UedmException {
        String id = "123";
        String name = "ValidName";

        doThrow(UedmException.class).when(templateStrategyRepository).selectTemplateByIdForCheckName(name, id);

        templateStrategyService.checkNameUnique(id, name);
    }
    /* Ended by AICoder, pid:4aede9369aa042e78749c96fd98943eb */

    /* Started by AICoder, pid:09a06e8fb32f49a4812d7fe96f9c0597 */
    @Test
    public void testSelectHistorySuccess() throws Exception {
        String taskId = "1";
        IssuedHistoryEntity po = new IssuedHistoryEntity();
        po.setAllDetail("{\"version\":\"1.0\",\"templateDetail\":{}}");

        // 模拟templateStrategyRepository.selectIssueHistoryDetailByTaskId(taskId)方法返回po
        when(templateStrategyRepository.selectIssueHistoryDetailByTaskId(taskId)).thenReturn(po);

        IssHistoryEntity result = templateStrategyService.selectHistory(taskId);
        assertEquals("V1.0", result.getVersion());
    }

    @Test
    public void testSelectHistoryWithNullTaskId() throws Exception {
        String taskId = null;
        IssHistoryEntity result = templateStrategyService.selectHistory(taskId);
        assertNull(result.getVersion());
    }
    /* Ended by AICoder, pid:09a06e8fb32f49a4812d7fe96f9c0597 */


    @Test
    public void getFileIdTest() throws UedmException, com.zte.uedm.common.exception.UedmException {
        QueryFileIdRpcDto dto = new QueryFileIdRpcDto();
        dto.setTemplateStrategyId("1");
        dto.setVersion("1");
        templateStrategyService.getFileId(dto);

        when(templateStrategyRepository.getTaskIdByTemplateId(any())).thenReturn("1");
        when(peakShiftDistributionTaskDetailMapper.selectByTaskId(any(), any())).thenReturn(Collections.singletonList(new PeakShiftTaskDetailPo()));
        templateStrategyService.getFileId(dto);
    }

    @Test
    public void given_totalDaysExceeds30_when_checkBCUAHolidayNum_then_throwException() throws UedmException {
        templateStrategyService.checkBCUAHolidayNum(new ArrayList<>());
        List<TemplateHolidayDto> holiday = new ArrayList<>();
        TemplateHolidayDto templateHolidayDto = new TemplateHolidayDto();
        List<TemplateTimeGranVo> timeGran = new ArrayList<>();
        TemplateTimeGranVo vo = new TemplateTimeGranVo();
        vo.setBegin("01/01");
        vo.setEnd("03/01");
        timeGran.add(vo);
        templateHolidayDto.setTimeGran(timeGran);
        holiday.add(templateHolidayDto);
        // when
        try {
            templateStrategyService.checkBCUAHolidayNum(holiday);
        } catch (Exception e) {
        }
        vo.setEnd("01/12");
        try {
            templateStrategyService.checkBCUAHolidayNum(holiday);
        } catch (Exception e) {
        }

        TemplateTimeGranVo vo1 = new TemplateTimeGranVo();
        vo1.setBegin("01/01");
        vo1.setEnd("01/02");
        timeGran.add(vo1);
        try {
            templateStrategyService.checkBCUAHolidayNum(holiday);
        } catch (Exception e) {
        }

    }

    /* Started by AICoder, pid:1b588g1c05mfefc14f3d0a578067703abf76f94d */
    @Test(expected = UedmException.class)
    public void testCheckYearCrossOrOverNums_ExceedsLimit() throws UedmException {
        List<TemplateTimeGranVo> timeGranVoList = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            TemplateTimeGranVo vo = new TemplateTimeGranVo();
            vo.setBegin("2023-01-01");
            vo.setEnd("2023-01-02");
            timeGranVoList.add(vo);
        }
        templateStrategyService.checkYearCrossOrOverNums(timeGranVoList);
    }

    @Test(expected = UedmException.class)
    public void testCheckYearCrossOrOverNums_SpanningYears() throws UedmException {
        List<TemplateTimeGranVo> timeGranVoList = new ArrayList<>();

        TemplateTimeGranVo vo1 = new TemplateTimeGranVo();
        vo1.setBegin("2023-12-31");
        vo1.setEnd("2024-01-01");
        timeGranVoList.add(vo1);

        templateStrategyService.checkYearCrossOrOverNums(timeGranVoList);
    }

    @Test
    public void testCheckYearCrossOrOverNums_SameYear() throws UedmException {
        List<TemplateTimeGranVo> timeGranVoList = new ArrayList<>();

        TemplateTimeGranVo vo1 = new TemplateTimeGranVo();
        vo1.setBegin("2023-01-01");
        vo1.setEnd("2023-12-31");
        timeGranVoList.add(vo1);

        // 这个测试应当正常通过，不抛出异常
        templateStrategyService.checkYearCrossOrOverNums(timeGranVoList);
    }
    /* Ended by AICoder, pid:1b588g1c05mfefc14f3d0a578067703abf76f94d */

    @Test(expected = UedmException.class)
    public void testCheckCsuTemplate_StrategyNumbersExceeds() throws UedmException {
        List<TemplateDetailBaseDto> details = new ArrayList<>();
        TemplateDetailBaseDto baseDto = new TemplateDetailBaseDto();

        List<TemplateTimeGranDetailVo> timeGranDetails = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            TemplateTimeGranDetailVo detailVo = new TemplateTimeGranDetailVo();
            // 假设有需要设置的字段
            detailVo.setStrategyType(i % 4); // 只为示例
            timeGranDetails.add(detailVo);
        }
        baseDto.setDetail(timeGranDetails);
        details.add(baseDto);

        templateStrategyService.checkCsuTemplate(details, new ArrayList<>(), CommonConst.CSU6);
    }

    @Test(expected = UedmException.class)
    public void testCheckCsuTemplate_ExceedingStrategyPeriodGroups() throws UedmException {
        List<TemplateDetailBaseDto> details = new ArrayList<>();
        TemplateDetailBaseDto baseDto = new TemplateDetailBaseDto();

        List<TemplateTimeGranDetailVo> timeGranDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            TemplateTimeGranDetailVo detailVo = new TemplateTimeGranDetailVo();
            detailVo.setStrategyType(1); // 所有的类型都为1
            timeGranDetails.add(detailVo);
        }
        baseDto.setDetail(timeGranDetails);
        details.add(baseDto);

        templateStrategyService.checkCsuTemplate(details, new ArrayList<>(), CommonConst.CSU6);
    }

    @Test(expected = UedmException.class)
    public void testCheckCsuTemplate_HolidayPeriodsExceeds() throws UedmException {
        List<TemplateHolidayDto> holidays = new ArrayList<>();
        for (int i = 0; i < 21; i++) {
            TemplateHolidayDto holidayDto = new TemplateHolidayDto();
            TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
            templateTimeGranVo.setBegin("2023-01-01");
            templateTimeGranVo.setEnd("2023-01-10");
            holidayDto.setTimeRange(templateTimeGranVo); // 示例时间范围
            holidays.add(holidayDto);
        }

        templateStrategyService.checkCsuTemplate(new ArrayList<>(), holidays, CommonConst.CSU6);
    }

    @Test
    public void testCheckCsuTemplate_ValidInput() throws UedmException {
        List<TemplateDetailBaseDto> details = new ArrayList<>();
        TemplateDetailBaseDto baseDto = new TemplateDetailBaseDto();

        List<TemplateTimeGranDetailVo> timeGranDetails = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            TemplateTimeGranDetailVo detailVo = new TemplateTimeGranDetailVo();
            // 合法的策略类型
            detailVo.setStrategyType(i % 4);
            timeGranDetails.add(detailVo);
        }
        baseDto.setDetail(timeGranDetails);
        details.add(baseDto);

        List<TemplateHolidayDto> holidays = new ArrayList<>();
        TemplateHolidayDto holidayDto = new TemplateHolidayDto();
        TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
        templateTimeGranVo.setBegin("2023-01-01");
        templateTimeGranVo.setEnd("2023-01-10");
        holidayDto.setTimeRange(templateTimeGranVo);
        holidays.add(holidayDto);

        // 没有异常应该通过
        try {
            templateStrategyService.checkCsuTemplate(details, holidays, CommonConst.CSU6);
        } catch (UedmException e) {
            assertNotNull(e);
        }
    }

    @Test
    public void editTemplateStrategyTest() throws com.zte.uedm.common.exception.UedmException, UedmException {
        com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto templateStrategyEditDto = getTemplateStrategyDto();
        doReturn(new SeasonStrategyEntity()).when(gridStrategyRepository).queryById(anyString());
        doReturn("2022").when(dateTimeService).getCurrentTime();
        doReturn("2022").when(jsonService).objectToJson(Mockito.any());
        doReturn(1).when(templateStrategyRepository).updateTemplateStrategy(Mockito.any());
        doReturn(1).when(templateStrategyRepository).insertTemplateStrategyDetail(Mockito.any());
        TemplateStrategyDetailEntity strategyDetailBo = new TemplateStrategyDetailEntity();
        strategyDetailBo.setName("test");
        strategyDetailBo.setVersion("1.00");
        doReturn(strategyDetailBo).when(templateStrategyRepository).selectTemplateById(Mockito.anyString());
        List<com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBcuaPo> details = new ArrayList<>();
        doReturn(details).when(templateStrategyRepository).selectDetailByTemplateId(Mockito.anyString());
        doReturn(1).when(templateStrategyRepository).deleteDetailStrategyByIds(Mockito.anyList());
        doReturn(0).when(templateStrategyRepository).countName(Mockito.any());

        PowerMockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.anyString(), Mockito.anyString());

        templateStrategyService.editTemplateStrategy(templateStrategyEditDto, "zh_CN");
        templateStrategyEditDto.setName("");
        try
        {
            templateStrategyService.editTemplateStrategy(templateStrategyEditDto, "zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals("parameter is error", e.getMessage());
        }
        templateStrategyEditDto.setName("test1");
        doReturn(1).when(templateStrategyRepository).countName(Mockito.any());
        assertThrows(com.zte.uedm.basis.exception.UedmException.class, ()->templateStrategyService.editTemplateStrategy(templateStrategyEditDto, "zh_CN"));


        doReturn(null).when(templateStrategyRepository).selectTemplateById(Mockito.anyString());
        try
        {
            templateStrategyService.editTemplateStrategy(templateStrategyEditDto, "zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals("id is not exist", e.getMessage());
        }

        try
        {
            templateStrategyService.editTemplateStrategy(null, "zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void editTemplateStrategyTest1() throws UedmException {
        com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto templateStrategyEditDto = getTemplateStrategyDto();
        doReturn(null).when(gridStrategyRepository).queryById("01-19");
        assertThrows(com.zte.uedm.basis.exception.UedmException.class, ()->templateStrategyService.editTemplateStrategy(templateStrategyEditDto, "zh_CN"));
    }

    private  com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto getTemplateStrategyDto()
    {
        com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto templateStrategyEditDto = new com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateStrategyEditDto();
        templateStrategyEditDto.setId("test");
        templateStrategyEditDto.setName("2022");
        templateStrategyEditDto.setSeasonStrategyId("01-15");
        templateStrategyEditDto.setMode("0");
        templateStrategyEditDto.setRemark("2022");
        List<TemplateHolidayDto> holiday = new ArrayList<>();
        TemplateHolidayDto holidayVo = new TemplateHolidayDto();
        List<TemplateTimeGranVo> timeGranVos = new ArrayList<>();
        TemplateTimeGranVo templateTimeGranVo = new TemplateTimeGranVo();
        templateTimeGranVo.setBegin("10/01");
        templateTimeGranVo.setEnd("10/02");
        templateTimeGranVo.setRemark("test");
        timeGranVos.add(templateTimeGranVo);
        holidayVo.setTimeGran(timeGranVos);
        List<TemplateDetailBaseDto> detail = new ArrayList<>();
        TemplateDetailBaseDto vo = new TemplateDetailBaseDto();
        List<Integer> times = new ArrayList<>();
        times.add(1);
        vo.setTimeGran(times);
        List<TemplateTimeGranDetailVo> detailVos = new ArrayList<>();
        TemplateTimeGranDetailVo detailVo = new TemplateTimeGranDetailVo();
        detailVo.setStrategyType(1);
        detailVo.setBeginTime("test");
        detailVo.setEndTime("test");
        detailVos.add(detailVo);
        vo.setDetail(detailVos);
        detail.add(vo);
        templateStrategyEditDto.setDetail(detail);
        holidayVo.setDetail(detailVos);
        holiday.add(holidayVo);
        templateStrategyEditDto.setHoliday(holiday);
        return templateStrategyEditDto;
    }

    @Test
    public void searchByConditionsTest() throws UedmException {
        List<TemplateStrategyEntity> listResult = new ArrayList<>();
        TemplateStrategyEntity bo = new TemplateStrategyEntity();
        bo.setSeasonStrategyId("test");
        bo.setVersion("V1.00");
        listResult.add(bo);
        TemplateQueryDto queryBean = new TemplateQueryDto();
        queryBean.setName("TestName");
        queryBean.setSeasonStrategyName("test");
        doReturn(listResult).when(templateStrategyRepository).selectByConditions(any(com.zte.uedm.battery.a_interfaces.peakshift.web.dto.TemplateQueryDto.class));
        doReturn("CSU5").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(),Mockito.anyString());
        PowerMockito.when(gridStrategyService.getSeasonStrategyName(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("test");
        PageInfo<TemplateStrategyEntity> list = templateStrategyService.searchByConditions(queryBean, "zh_CN");
        assertEquals(list.getList(), listResult);
        PowerMockito.when(gridStrategyService.getSeasonStrategyName(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("电网");
        PageInfo<TemplateStrategyEntity> list1 = templateStrategyService.searchByConditions(queryBean, "zh_CN");
        assertEquals(list1.getList(), listResult);
        try
        {
            queryBean.setName(
                    "ttetsssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss");
            templateStrategyService.searchByConditions(queryBean, "zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals("name is too long", e.getMessage());
        }
    }

    @Test
    public void selectDetailByTemplateIdTest() throws com.zte.uedm.common.exception.UedmException, UedmException {
        List<com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBcuaPo> listResult = new ArrayList<>();
        com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBcuaPo po = new com.zte.uedm.battery.bean.peak.TemplateStrategyDetailBcuaPo();
        po.setHolidayFlag(true);
        po.setHolidayTimeGran("xx");
        listResult.add(po);
        List<TemplateStrategyDetailCsu5Entity> listResult2 = new ArrayList<>();
        TemplateStrategyDetailCsu5Entity po1 = new TemplateStrategyDetailCsu5Entity();
        po1.setHolidayFlag(true);
        po1.setRemark("xx");
        po1.setDetail(JsonUtils.objectToJson(Arrays.asList(new TemplateTimeGranDetailVo())));
        listResult2.add(po1);
        TemplateStrategyDetailEntity templateStrategyDetailBo = new TemplateStrategyDetailEntity();
        templateStrategyDetailBo.setId("id");
        templateStrategyDetailBo.setName("test");
        templateStrategyDetailBo.setSeasonStrategyId("test");
        templateStrategyDetailBo.setFileId("test");
        templateStrategyDetailBo.setDeviceType("BCUA");
        doReturn(listResult).when(templateStrategyRepository).selectDetailByTemplateId(Mockito.anyString());
        doReturn(listResult2).when(templateStrategyRepository).selectCsu5DetailByTemplateId(Mockito.anyString());
        doReturn(templateStrategyDetailBo).when(templateStrategyRepository).selectTemplateById(Mockito.anyString());
        List<UpDownloadFileBean> upDownloadFileBeans = new ArrayList<>();
        UpDownloadFileBean bean = new UpDownloadFileBean();
        bean.setName("test");
        upDownloadFileBeans.add(bean);
        doReturn(upDownloadFileBeans).when(configurationManagerRpc)
                .selectFileById(Mockito.anyList(), Mockito.anyString());

        TemplateStrategyDetailEntity strategyDetailBo = templateStrategyService.selectDetailByTemplateId("test", "test",
                "test");
        PowerMockito.when(gridStrategyService.getSeasonStrategyName(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("test");
        assertEquals(strategyDetailBo.getName(), templateStrategyDetailBo.getName());
        doThrow(new com.zte.uedm.common.exception.UedmException(-1, "error")).when(configurationManagerRpc)
                .selectFileById(Mockito.anyList(), Mockito.anyString());

        TemplateStrategyDetailEntity bo2 = templateStrategyService.selectDetailByTemplateId("test", "test", "test");
        assertEquals(bo2.getFileName(), "");
        templateStrategyDetailBo.setDeviceType("CSU5");
        templateStrategyService.selectDetailByTemplateId("test", "test", "test");
        po1.setHolidayFlag(false);
        templateStrategyService.selectDetailByTemplateId("test", "test", "test");
        templateStrategyDetailBo.setDeviceType("BCUA");
        po.setHolidayFlag(false);
        po.setTimeGran("xx");
        templateStrategyService.selectDetailByTemplateId("test", "test", "test");
        try {
            doReturn(null).when(templateStrategyRepository).selectTemplateById(Mockito.anyString());
            templateStrategyService.selectDetailByTemplateId("test", "test", "test");
        } catch (Exception e) {
            Assert.assertEquals("id is not exist", e.getMessage());
        }
    }

    @Test
    public void deleteTemplateStrategyTest() throws com.zte.uedm.common.exception.UedmException, UedmException {
        UpDownloadFileEntity upDownloadFileEntity = new UpDownloadFileEntity();
        upDownloadFileEntity.setName("filename");
        when(upDownloadFileRepository.selectById(anyString())).thenReturn(upDownloadFileEntity);
        List<String> names = new ArrayList<>();
        names.add("test");
        PowerMockito.when(templateStrategyRepository.selectNameByIds(Mockito.anyList())).thenReturn(names);
        PowerMockito.when(templateStrategyRepository.selectFileIdByIds(Mockito.anyList())).thenReturn(names);
        PowerMockito.when(templateStrategyRepository.deleteDetailStrategyByIds(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(templateStrategyRepository.deleteByIds(Mockito.anyList())).thenReturn(1);
        OperlogBean operlogBean = new OperlogBean();
        IsKafkaBean isKafkaBean = new IsKafkaBean();
        PowerMockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.anyString(), Mockito.anyString());
        PowerMockito.doReturn(1).when(configurationManagerRpc).deleteFileInfo(Mockito.anyList());
        int count = templateStrategyService.deleteTemplateStrategy(names, operlogBean, isKafkaBean);
        Assert.assertEquals(1, count);
        try
        {
            templateStrategyService.deleteTemplateStrategy(new ArrayList<>(), operlogBean, isKafkaBean);
        }
        catch (Exception e)
        {
            Assert.assertEquals("delete failed!", e.getMessage());
        }
    }

    @Test
    public void getFileNameTest_Except() throws UedmException {
        try {
            when(upDownloadFileRepository.selectById(anyString())).thenThrow(new UedmException(1,"1"));
            templateStrategyService.getFileName("1", "2");
        } catch (UedmException e) {

        }
    }

}