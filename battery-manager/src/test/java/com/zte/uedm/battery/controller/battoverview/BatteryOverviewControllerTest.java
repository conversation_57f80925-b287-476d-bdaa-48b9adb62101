package com.zte.uedm.battery.controller.battoverview;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterAssetRequestDto;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterOverviewRequestDto;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterStatisticsRequestDto;
import com.zte.uedm.battery.controller.battoverview.dto.BatteryOverviewFilterWorkConditionRequestDto;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.opti.domain.service.BattRiskEvalDomain;
import com.zte.uedm.battery.service.BatteryOverviewConfigService;
import com.zte.uedm.battery.service.BatteryOverviewService;
import com.zte.uedm.battery.service.BatteryWorkConditionDimensionsDatesCache;
import com.zte.uedm.battery.service.BatteryWorkConditionDimensionsService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;

import static org.mockito.Mockito.*;

public class BatteryOverviewControllerTest {
    @Mock
    private BatteryWorkConditionDimensionsService batteryWorkConditionDimensionsService;
    @InjectMocks
    private BatteryOverviewController batteryOverviewController;
    @Mock
    private BattAssetDomain battAssetDomain;
    @Mock
    private BatteryWorkConditionDimensionsDatesCache batteryWorkConditionDimensionsDatesCache;
    @Mock
    private BatteryOverviewConfigService battOverviewConfigService;
    @Mock
    private BatteryOverviewService batteryOverviewService;
    @Mock
    private BattSohDomain battSohDomain;
    @Mock
    private BattTypeDomain battTypeDomain;
    @Mock
    private BattSocDomain battSocDomain;
    @Mock
    private BattRatedCapDomain battRatedCapDomain;
    @Mock
    private BattChargeStatusDomain battChargeStatusDomain;
    @Mock
    private BattAlarmDomain battAlarmDomain;
    @Mock
    private BattRiskEvalDomain riskEvalDomain;
    @Mock
    private BattAssetAttributeDomain battAssetAttributeDomain;
    @Mock
    private JsonService jsonService;
    @Mock
    private MessageSenderService msgSenderService;
    @Mock
    private BattLifeDomain battLifeDomain;

    private BatteryOverviewBeanVo batteryOverviewBeanVo;
    private ResponseBean responseBean = new ResponseBean();

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setEnable(true);
        responseBean.setCode(0);
        responseBean.setTotal(5);
        when(battAssetDomain.selectAssetEnable()).thenReturn(false);
    }

@Test
public void selectWorkConditionConfig()
    {
        UedmException flag=null;
        try{
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
            batteryOverviewBeanVo.setUserName("apiuser");
            List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
            batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
            batteryOverviewController.selectWorkConditionConfig(1,1,request,"");
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }
@Test
public void selectWorkConditionConfig01()
    {
        UedmException flag=null;
        try{
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
            batteryOverviewBeanVo.setUserName("apiuser");
            List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
            batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        //    request=null;
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
            batteryOverviewController.selectWorkConditionConfig(1,1,request,"");
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }
@Test
public void searchWorkConditionConfig() {
    UedmException flag=null;
    try{
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsSearchRequestBean batteryWorkConditionDimensionsSearchRequestBean = new BatteryWorkConditionDimensionsSearchRequestBean();
        batteryWorkConditionDimensionsSearchRequestBean.setName("a");
        batteryWorkConditionDimensionsSearchRequestBean.setPageNo(1);
        batteryWorkConditionDimensionsSearchRequestBean.setPageSize(1);
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        batteryOverviewBeanVo.setName("alarm");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryOverviewController.searchWorkConditionConfig(batteryWorkConditionDimensionsSearchRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}



    @Test
    public void searchWorkConditionConfig01() {
        UedmException flag=null;
        try{
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryWorkConditionDimensionsSearchRequestBean batteryWorkConditionDimensionsSearchRequestBean = new BatteryWorkConditionDimensionsSearchRequestBean();
            batteryWorkConditionDimensionsSearchRequestBean.setName("a");
            batteryWorkConditionDimensionsSearchRequestBean.setPageNo(1);
            batteryWorkConditionDimensionsSearchRequestBean.setPageSize(1);
            BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
            batteryOverviewBeanVo.setUserName("apiuser");
            batteryOverviewBeanVo.setName("alarm");
            List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
            batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean2 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean2.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean2.setId("11");
            batteryOverviewWorkConditionDimensionsDatesBean2.setSequence(1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean2);
            when(batteryWorkConditionDimensionsService.searchWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
            batteryOverviewController.searchWorkConditionConfig(batteryWorkConditionDimensionsSearchRequestBean,request,"1");
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }


@Test
public void updateWorkConditionConfig001() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}
@Test
public void updateWorkConditionConfig002() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean.setId("1");
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}

@Test
public void updateWorkConditionConfig003() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
        batteryWorkConditionDimensionsSearchRequestBean02.setId("prstsoc");
        batteryWorkConditionDimensionsSearchRequestBean03.setId("ratedcap");
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}

@Test
public void updateWorkConditionConfig004() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("apiuser");
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("life");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean2 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean2.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean2.setId("prstsoc");
        batteryOverviewWorkConditionDimensionsDatesBean2.setSequence(2);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean3 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean3.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean3.setId("ratedcap");
        batteryOverviewWorkConditionDimensionsDatesBean3.setSequence(3);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean2);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean3);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("ratedcap");
        batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
        batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
        batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
        batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
        batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        Map<String, BatteryWorkConditionDimensionsDatesBean> cache= new HashMap(){
            {
                put("alarm",new BatteryWorkConditionDimensionsDatesBean(true,"{\"en_US\":\"Alarm\",\"zh_CN\":\"告警\"}",1,true,true));
                put("soh",new BatteryWorkConditionDimensionsDatesBean(true,"{\"en_US\":\"SOH\",\"zh_CN\":\"健康状态\"}",2,true,true));
                put("life",new BatteryWorkConditionDimensionsDatesBean(true,"{\"en_US\":\"Life\",\"zh_CN\":\"寿命\"}",3,true,true));
                put("prstsoc",new BatteryWorkConditionDimensionsDatesBean(true,"{\"en_US\":\"Prst Soc\",\"zh_CN\":\"剩余容量\"}",4,true,true));
                put("ratedcap",new BatteryWorkConditionDimensionsDatesBean(true,"{\"en_US\":\"Rated Cap\",\"zh_CN\":\"额定容量\"}",5,true,true));

            }
        };
        when(batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(Mockito.any())).thenReturn(cache.get("alarm"));

        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}

@Test
public void updateWorkConditionConfig005() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("ratedcap");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(11);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean2 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean2.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean2.setId("prstsoc");
        batteryOverviewWorkConditionDimensionsDatesBean2.setSequence(22);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean3 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean3.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean3.setId("life");
        batteryOverviewWorkConditionDimensionsDatesBean3.setSequence(33);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean2);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean3);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("1");
        batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
        batteryWorkConditionDimensionsSearchRequestBean02.setId("1");
        batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
        batteryWorkConditionDimensionsSearchRequestBean03.setId("1");
        batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}


@Test
public void updateWorkConditionConfig006() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("1");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean2 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean2.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean2.setId("2");
        batteryOverviewWorkConditionDimensionsDatesBean2.setSequence(2);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean3 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean3.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean3.setId("3");
        batteryOverviewWorkConditionDimensionsDatesBean3.setSequence(3);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean2);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean3);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
        batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
        batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
        batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
        batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
        batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}

@Test
public void updateWorkConditionConfig007() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
        batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
        batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
        batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
        batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
        batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryGetIdBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryGetIdBean.setId("1");
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfigById(Mockito.any())).thenReturn(batteryGetIdBean);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}

@Test
public void updateWorkConditionConfig008() {
    UedmException flag=null;
    try{
        List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
        batteryOverviewBeanVo.setUserName("apiuser");
        List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
        BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
        batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
        batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
        batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
        when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
        batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
        batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
        batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
        batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
        batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
        batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
        batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
        batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
        BatteryOverviewWorkConditionDimensionsDatesBean batteryGetIdBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
        batteryGetIdBean=null;
        doReturn(batteryGetIdBean).when(batteryWorkConditionDimensionsService).selectWorkConditionConfigById(batteryOverviewBeanVo);
        batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
    }catch (UedmException e){
        Assert.assertEquals("",e.getMessage());
    }
}


    @Test
    public void updateWorkConditionConfig009() {
        UedmException flag=null;
        try{
            List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
            batteryOverviewBeanVo.setUserName("apiuser");
            List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean1.setId("11");
            batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
            batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
            batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean01.setSequence(1);
            batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
            batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
            batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
            batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
            BatteryOverviewWorkConditionDimensionsDatesBean batteryGetIdBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryGetIdBean.setId("1");
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfigById(Mockito.any())).thenReturn(batteryGetIdBean);
//            when(batteryOverviewController.listR(Mockito.any())).thenReturn(false);
            batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean,request,"1");
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void updateWorkConditionConfig010() {
        UedmException flag=null;
        try{
            List<BatteryWorkConditionDimensionsUpdateRequestBeanBean> batteryWorkConditionDimensionsUpdateRequestBean = new ArrayList<>();
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean01 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean02 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryWorkConditionDimensionsUpdateRequestBeanBean batteryWorkConditionDimensionsSearchRequestBean03 = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
            BatteryOverviewBeanVo batteryOverviewBeanVo = new BatteryOverviewBeanVo();
            batteryOverviewBeanVo.setUserName("apiuser");
            List<BatteryOverviewWorkConditionDimensionsDatesBean> batteryOverviewWorkConditionDimensionsDatesBean = new ArrayList<>();
            BatteryOverviewWorkConditionDimensionsDatesBean batteryOverviewWorkConditionDimensionsDatesBean1 = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryOverviewWorkConditionDimensionsDatesBean1.setUserName("apiuser");
            batteryOverviewWorkConditionDimensionsDatesBean1.setId("alarm");
            batteryOverviewWorkConditionDimensionsDatesBean1.setSequence(1);
            batteryOverviewWorkConditionDimensionsDatesBean.add(batteryOverviewWorkConditionDimensionsDatesBean1);
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfig(Mockito.any(),Mockito.anyString())).thenReturn(batteryOverviewWorkConditionDimensionsDatesBean);
            batteryWorkConditionDimensionsSearchRequestBean01.setId("alarm");
            batteryWorkConditionDimensionsSearchRequestBean01.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean01.setSequence(7);
            batteryWorkConditionDimensionsSearchRequestBean02.setId("life");
            batteryWorkConditionDimensionsSearchRequestBean02.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean02.setSequence(2);
            batteryWorkConditionDimensionsSearchRequestBean03.setId("prstsoc");
            batteryWorkConditionDimensionsSearchRequestBean03.setEnable(true);
            batteryWorkConditionDimensionsSearchRequestBean03.setSequence(3);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean01);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean02);
            batteryWorkConditionDimensionsUpdateRequestBean.add(batteryWorkConditionDimensionsSearchRequestBean03);
            BatteryOverviewWorkConditionDimensionsDatesBean batteryGetIdBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
            batteryGetIdBean.setId("1");
            when(batteryWorkConditionDimensionsService.selectWorkConditionConfigById(Mockito.any())).thenReturn(batteryGetIdBean);

            BatteryWorkConditionDimensionsDatesBean bean = new BatteryWorkConditionDimensionsDatesBean(false,"",1,false,false);
            when(batteryWorkConditionDimensionsDatesCache.getBatteryWorkConditionDimensionsDates(Mockito.any())).thenReturn(bean);
//            when(batteryOverviewController.listR(Mockito.any())).thenReturn(false);
            ResponseBean responseBean = batteryOverviewController.updateWorkConditionConfig(batteryWorkConditionDimensionsUpdateRequestBean, request, "1");
        }catch (UedmException e){
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals(responseBean.getClass(),ResponseBean.class);
    }

@Test
public void listRT1() {
    List<String> list = new LinkedList<>();
    list.add("1");
    list.add("1");
    list.add("1");
    batteryOverviewController.listR(list);
    Assert.assertEquals(3,list.size());
}

@Test
public void listRT2() {
    List<String> list = new LinkedList<>();
    list.add("1");
    list.add("12");
    list.add("13");
    batteryOverviewController.listR(list);
    Assert.assertEquals(3,list.size());
}

    @Test
    public void testSelectBatteryAssertConfig() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BatteryOverviewBean> pageList = new PageInfo<>();
        pageList.setTotal(100);

        List<BatteryOverviewBean> beanList = new ArrayList<>();
        BatteryOverviewBean batteryOverviewBean = new BatteryOverviewBean();
        batteryOverviewBean.setUserName("apiuser");
        beanList.add(batteryOverviewBean);
        doReturn(beanList).when(batteryOverviewService).selectBatteryAssertConfig(Mockito.anyObject(), anyObject());
        responseBean = batteryOverviewController.selectBatteryAssertConfig(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void testUpdateBatteryAssertConfig() throws Exception
    {
        UedmException flag=null;
        try{
            List<BatteryAssertConfigUpdateBean> updateBeanList = new ArrayList<>();
            BatteryAssertConfigUpdateBean updateBean = new BatteryAssertConfigUpdateBean();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBeanList.add(updateBean);

            HttpServletRequest request = mock(HttpServletRequest.class);

            doReturn(10).when(batteryOverviewService).updateBatteryAssertConfig(anyObject(),anyString());
            responseBean = batteryOverviewController.updateBatteryAssertConfig(updateBeanList,request,"");

        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void testSearchBatteryAssertConfig() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BatteryOverviewBean> page = new PageInfo<>();
        page.setTotal(5);
        BatteryConfigSearchBean searchBean = new BatteryConfigSearchBean();
        searchBean.setName("类型");
        when(batteryOverviewService.searchBatteryAssertConfig(anyObject(), anyObject(), anyObject())).thenReturn(page);
        responseBean = batteryOverviewController.searchBatteryAssertConfig(searchBean,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void testUpdateBatteryAssertConfig1() throws Exception
    {
        UedmException flag=null;
        try{
            List<BatteryAssertConfigUpdateBean> updateBeanList = new ArrayList<>();
            BatteryAssertConfigUpdateBean updateBean = new BatteryAssertConfigUpdateBean();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBean.setEnable(false);
            updateBeanList.add(updateBean);

            HttpServletRequest request = mock(HttpServletRequest.class);
            List<BatteryOverviewBean> batteryOverviewBeanList = new ArrayList<>();
            BatteryOverviewBean batteryOverviewBean = new BatteryOverviewBean();
            batteryOverviewBean.setId("1");
            batteryOverviewBean.setSequence(1);
            batteryOverviewBean.setDefaultFixed(true);
            batteryOverviewBean.setEnable(true);
            batteryOverviewBeanList.add(batteryOverviewBean);
            doReturn(batteryOverviewBeanList).when(batteryOverviewService).selectBatteryAssertConfig(anyObject(),
                    anyObject());
            responseBean = batteryOverviewController.updateBatteryAssertConfig(updateBeanList,request,"");

        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void testUpdateBatteryAssertConfig2() throws Exception
    {
        UedmException flag=null;
        try{
            List<BatteryAssertConfigUpdateBean> updateBeanList = new ArrayList<>();
            BatteryAssertConfigUpdateBean updateBean = new BatteryAssertConfigUpdateBean();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBean.setEnable(true);
            updateBeanList.add(updateBean);

            HttpServletRequest request = mock(HttpServletRequest.class);
            List<BatteryOverviewBean> batteryOverviewBeanList = new ArrayList<>();
            BatteryOverviewBean batteryOverviewBean = new BatteryOverviewBean();
            batteryOverviewBean.setId("1");
            batteryOverviewBean.setSequence(1);
            batteryOverviewBean.setDefaultFixed(true);
            batteryOverviewBean.setEnable(true);
            batteryOverviewBeanList.add(batteryOverviewBean);
            doReturn(batteryOverviewBeanList).when(batteryOverviewService).selectBatteryAssertConfig(anyObject(), anyObject());
            doReturn(1).when(batteryOverviewService).updateBatteryAssertConfig(anyObject(),anyObject());
            responseBean = batteryOverviewController.updateBatteryAssertConfig(updateBeanList,request,"");

        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void statisticsByDimTest1()
    {
        UedmException flag=null;
        try{
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(999);
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewFilterStatisticsRequestDto inBean =  new BatteryOverviewFilterStatisticsRequestDto();
            inBean.setStartDateBegin("211");
            inBean.setLogicId("121212");
            Integer pageSize = 0;
            Integer pageNo=0;
            String languageOption = "zh-CN";

            when(batteryOverviewService.statisticsByStatisticsDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
            batteryOverviewController.statisticsByStatisticsDim(inBean,pageSize,pageNo,request,languageOption);
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void statisticsByDimTest2Null()
    {
        UedmException flag=null;
        try{
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(999);
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewFilterStatisticsRequestDto inBean =  new BatteryOverviewFilterStatisticsRequestDto();
            inBean.setStartDateBegin("211");
            inBean.setLogicId("121212");
            Integer pageSize = 0;
            Integer pageNo=0;
            String languageOption = "zh-CN";
            inBean=null;
            when(batteryOverviewService.statisticsByStatisticsDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
            batteryOverviewController.statisticsByStatisticsDim(inBean,pageSize,pageNo,request,languageOption);
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void statisticsByDimTest3() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByStatisticsDim(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterStatisticsRequestDto dto = new BatteryOverviewFilterStatisticsRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(page).when(riskEvalDomain).selectRiskEvalLevel(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doReturn(responseBean).when(batteryOverviewService).statisticsByStatisticsDim(Mockito.any(), Mockito.any());
        dto.setTypes(Arrays.asList("2"));
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("total"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("21"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("total"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setTypes(new ArrayList<>());

        dto.setSoh(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setSoh(new ArrayList<>());

        dto.setPrstSocLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setPrstSocLevels(new ArrayList<>());

        dto.setRatedCapLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setRatedCapLevels(new ArrayList<>());


        dto.setChargeStatus(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setChargeStatus(new ArrayList<>());

        dto.setAlarmLevels(Arrays.asList(12));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setAlarmLevels(new ArrayList<>());
        dto.setLifeLevels(Arrays.asList("1"));
        dto.setTypes(Arrays.asList("2"));
        Assert.assertEquals(new Integer(-307), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());

        dto.setRiskLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setRiskLevels(new ArrayList<>());
        dto.setLifeLevels(Arrays.asList("1"));
        Assert.assertEquals(new Integer(-307), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());


    }

    @Test
    public void statisticsByDimTest_exce() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByStatisticsDim(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterStatisticsRequestDto dto = new BatteryOverviewFilterStatisticsRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        dto.setTypes(Arrays.asList("2"));
        Mockito.doThrow(new UedmException(-1,"")).when(batteryOverviewService).statisticsByStatisticsDim(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("total"));
        Assert.assertEquals(new Integer(-1), batteryOverviewController.statisticsByStatisticsDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
    }


    @Test
    public void statisticsByAssetDimTest_3() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByAssetDim(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterAssetRequestDto dto = new BatteryOverviewFilterAssetRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doReturn(responseBean).when(batteryOverviewService).statisticsByAssetDim(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("battType"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("21"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("battType"));
        dto.setTypes(Arrays.asList("12"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setTypes(new ArrayList<>());

        dto.setSoh(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setSoh(new ArrayList<>());

        dto.setPrstSocLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setPrstSocLevels(new ArrayList<>());

        dto.setRatedCapLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setRatedCapLevels(new ArrayList<>());


        dto.setChargeStatus(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setChargeStatus(new ArrayList<>());

        dto.setAlarmLevels(Arrays.asList(12));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setAlarmLevels(new ArrayList<>());

    }

    @Test
    public void statisticsByAssetDimTest_exc() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByAssetDim(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterAssetRequestDto dto = new BatteryOverviewFilterAssetRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doThrow(new UedmException(-1,"")).when(batteryOverviewService).statisticsByAssetDim(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("battType"));
        Assert.assertEquals(new Integer(-1), batteryOverviewController.statisticsByAssetDim(dto, 10,1,httpServletRequest,"zh-CN").getCode());
    }

    @Test
    public void statisticsByWorkCondition_3() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByWorkCondition(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterWorkConditionRequestDto dto = new BatteryOverviewFilterWorkConditionRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        dto.setTypes(Arrays.asList("2"));
        Mockito.doReturn(responseBean).when(batteryOverviewService).statisticsByWorkConditionDim(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("alarm"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("21"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("alarm"));
        dto.setTypes(Arrays.asList("1"));
        Assert.assertEquals(new Integer(0), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setTypes(new ArrayList<>());

        dto.setSoh(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setSoh(new ArrayList<>());

        dto.setPrstSocLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setPrstSocLevels(new ArrayList<>());

        dto.setRatedCapLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setRatedCapLevels(new ArrayList<>());


        dto.setChargeStatus(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setChargeStatus(new ArrayList<>());

        dto.setAlarmLevels(Arrays.asList(12));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setAlarmLevels(new ArrayList<>());

    }

    @Test
    public void statisticsByWorkConditionTest_exc() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByWorkCondition(null, 10,1,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterWorkConditionRequestDto dto = new BatteryOverviewFilterWorkConditionRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doThrow(new UedmException(-1,"")).when(batteryOverviewService).statisticsByWorkConditionDim(Mockito.any(), Mockito.any());
        dto.setTypes(Arrays.asList("2"));
        Assert.assertEquals(new Integer(-301), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("alarm"));
        Assert.assertEquals(new Integer(-1), batteryOverviewController.statisticsByWorkCondition(dto, 10,1,httpServletRequest,"zh-CN").getCode());
    }


    @Test
    public void selectOverviewByCondition_3() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.selectOverviewByCondition(null,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterOverviewRequestDto dto = new BatteryOverviewFilterOverviewRequestDto();
        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("life");
        dto.setBattOverViewdims(Arrays.asList(bean));

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doReturn(responseBean).when(battOverviewConfigService).selectOverviewByCondition(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("alarm"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("21"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setDims(Arrays.asList("alarm"));
        dto.setTypes(Arrays.asList("1"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setTypes(new ArrayList<>());

        dto.setSoh(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setSoh(new ArrayList<>());

        dto.setPrstSocLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setPrstSocLevels(new ArrayList<>());

        dto.setRatedCapLevels(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setRatedCapLevels(new ArrayList<>());


        dto.setChargeStatus(Arrays.asList("12"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setChargeStatus(new ArrayList<>());

        dto.setAlarmLevels(Arrays.asList(12));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setAlarmLevels(new ArrayList<>());

        dto.setOrder("sa");
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setOrder(null);

        dto.setSort("sa");
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        dto.setSort(null);

    }

    @Test
    public void selectOverviewByConditionTest_exc() throws Exception
    {
        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.selectOverviewByCondition(null,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewFilterOverviewRequestDto dto = new BatteryOverviewFilterOverviewRequestDto();

        List<IdNameBean> list = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(list);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());

        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Mockito.doThrow(new UedmException(-1,"")).when(battOverviewConfigService).selectOverviewByCondition(Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(0), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("77");
        dto.setBattOverViewdims(Arrays.asList(bean));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
        BatteryOverviewBean bean1 = new BatteryOverviewBean();
        bean.setId("life");
        dto.setBattOverViewdims(Arrays.asList(bean));
        dto.setLogicId("1");
        dto.setDims(Arrays.asList("alarm"));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.selectOverviewByCondition(dto,httpServletRequest,"zh-CN").getCode());
    }



    @Test
    public void statisticsByAssetDimTest1()
    {
        UedmException flag=null;
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(999);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryOverviewFilterAssetRequestDto inBean =  new BatteryOverviewFilterAssetRequestDto();
        inBean.setStartDateBegin("211");
        inBean.setLogicId("121212");
        Integer pageSize = 0;
        Integer pageNo=0;
        String languageOption = "zh-CN";

        try {
            when(batteryOverviewService.statisticsByStatisticsDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
        } catch (UedmException e) {
            
        }
        ResponseBean responseBean1 = batteryOverviewController.statisticsByAssetDim(inBean, pageSize, pageNo, request, languageOption);
        Assert.assertSame(null,responseBean1.getMessage());
    }

    @Test
    public void statisticsByAssetDimTest2Null()
    {
        UedmException flag=null;
        try{
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(999);
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewFilterAssetRequestDto inBean =  new BatteryOverviewFilterAssetRequestDto();
            inBean.setStartDateBegin("211");
            inBean.setLogicId("121212");
            Integer pageSize = 0;
            Integer pageNo=0;
            String languageOption = "zh-CN";
            inBean=null;
            when(batteryOverviewService.statisticsByStatisticsDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
            batteryOverviewController.statisticsByAssetDim(inBean,pageSize,pageNo,request,languageOption);
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void statisticsByAssetDimTest33()
    {
        UedmException flag=null;
        try{
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(999);
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewFilterAssetRequestDto inBean =  new BatteryOverviewFilterAssetRequestDto();
            inBean.setStartDateBegin("211");
            inBean.setLogicId("121212");
            Integer pageSize = 0;
            Integer pageNo=0;
            String languageOption = "zh-CN";
            inBean=null;
            when(battAssetDomain.selectAssetEnable()).thenReturn(true);
            when(batteryOverviewService.statisticsByStatisticsDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
            batteryOverviewController.statisticsByAssetDim(inBean,pageSize,pageNo,request,languageOption);
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void statisticsByWorkConditionTest1()
    {
        UedmException flag=null;
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(999);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        BatteryOverviewFilterWorkConditionRequestDto inBean =  new BatteryOverviewFilterWorkConditionRequestDto();
        inBean.setStartDateBegin("211");
        inBean.setLogicId("121212");
        Integer pageSize = 0;
        Integer pageNo=0;
        String languageOption = "zh-CN";

        try {
            when(batteryOverviewService.statisticsByWorkConditionDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);
        } catch (UedmException e) {
            
        }
        ResponseBean responseBean1 = batteryOverviewController.statisticsByWorkCondition(inBean, pageSize, pageNo, request, languageOption);
        Assert.assertSame(null,responseBean1.getMessage());
    }

    @Test
    public void statisticsByWorkConditionTest2Null()
    {
        UedmException flag=null;
        try{
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(999);
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            BatteryOverviewFilterWorkConditionRequestDto inBean =  new BatteryOverviewFilterWorkConditionRequestDto();
            inBean.setStartDateBegin("211");
            inBean.setLogicId("121212");
            Integer pageSize = 0;
            Integer pageNo=0;
            String languageOption = "zh-CN";
            inBean=null;
            when(batteryOverviewService.statisticsByWorkConditionDim(Mockito.any(), Mockito.any())).thenReturn(responseBean);

            batteryOverviewController.statisticsByWorkCondition(inBean,pageSize,pageNo,request,languageOption);
        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }


    @Test
    public void GetargsAllSqTests(){
        BatteryOverviewWorkConditionDimensionsDatesBean sBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
        List<BatteryOverviewWorkConditionDimensionsDatesBean> bBean = new ArrayList<>();
        sBean.setName("");sBean.setSequence(999);
        bBean.add(sBean);
        List<Integer> integers = batteryOverviewController.GetargsAllSq(bBean);
        Assert.assertEquals(1,integers.size());
    }

    @Test
    public void GetargsAllIdTests(){
        BatteryOverviewWorkConditionDimensionsDatesBean sBean = new BatteryOverviewWorkConditionDimensionsDatesBean();
        List<BatteryOverviewWorkConditionDimensionsDatesBean> bBean = new ArrayList<>();
        sBean.setName("");sBean.setSequence(999);sBean.setId("666");
        bBean.add(sBean);
        List<String> strings = batteryOverviewController.GetargsAllId(bBean);
        Assert.assertEquals(1,strings.size());
    }

    @Test
    public void exportOverviewTest() throws Exception
    {
        List<IdNameBean> lists = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(lists);
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(),Mockito.any());


        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        List<String> list = Lists.newArrayList();
        list.add("da");

        BatteryOverviewExportBean exportBean = new BatteryOverviewExportBean();
        List<String> types = new ArrayList<>();
        types.add("0");
        ArrayList<ImageBean> list1 = new ArrayList<>();
        list1.add(new ImageBean());
        exportBean.setImages(list1);
        exportBean.setStartDateBegin("2022-05-17");
        exportBean.setStartDateEnd("2022-05-18");
        exportBean.setLogicId("sa");
        ArrayList<String> strings = new ArrayList<>();
        strings.add("alarm");
        exportBean.setStatisticsDims(strings);

        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("name");
        exportBean.setBattOverViewdims(Arrays.asList(bean));
        Mockito.doReturn("").when(battOverviewConfigService).exportOverview(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(0), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());

        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());

        exportBean.setBattOverViewdims(new ArrayList<>());
        Assert.assertEquals(new Integer(0), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());

        BatteryOverviewBean bean77 = new BatteryOverviewBean();
        bean.setId("77");
        exportBean.setBattOverViewdims(Arrays.asList(bean77));
        Assert.assertEquals(new Integer(0), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());

        exportBean.setSort("da");
        Assert.assertEquals(new Integer(0), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());
        exportBean.setSort(null);

        exportBean.setOrder("da");
        Assert.assertEquals(new Integer(0), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());
        exportBean.setOrder(null);

    }
    @Test
    public void exportOverviewTest_null() throws Exception
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        Assert.assertEquals(new Integer(-301), batteryOverviewController.exportOverview(null,request,response, "zh-CN").getCode());
    }
    @Test
    public void exportOverviewTest_out_range() throws Exception
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        BatteryOverviewExportBean exportBean = new BatteryOverviewExportBean();
        List<String> types = new ArrayList<>();
        types.add("0");
        ArrayList<ImageBean> list1 = new ArrayList<>();
        list1.add(new ImageBean());
        exportBean.setImages(list1);
        exportBean.setStartDateBegin("2022-05-17");
        exportBean.setStartDateEnd("2022-05-18");
        exportBean.setLogicId("sa");

        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("77");
        exportBean.setBattOverViewdims(Arrays.asList(bean));

        Assert.assertEquals(new Integer(-301), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());
    }
    @Test
    public void exportOverviewTest_out_range2() throws Exception
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        BatteryOverviewExportBean exportBean = new BatteryOverviewExportBean();
        List<String> types = new ArrayList<>();
        types.add("0");
        ArrayList<ImageBean> list1 = new ArrayList<>();
        list1.add(new ImageBean());
        exportBean.setImages(list1);
        exportBean.setStartDateBegin("2022-05-17");
        exportBean.setStartDateEnd("2022-05-18");
        exportBean.setLogicId("sa");
        ArrayList<String> workCOnditions = new ArrayList<>();
        workCOnditions.add("riskLevel2");
        exportBean.setWorkCoditionDims(workCOnditions);
        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("77");
        exportBean.setBattOverViewdims(Arrays.asList(bean));
        Assert.assertEquals(new Integer(-304), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());
        workCOnditions.remove(0);
        workCOnditions.add("riskLevel");
        ArrayList<String> assetDims = new ArrayList<>();
        assetDims.add("213");
        exportBean.setAssetdims(assetDims);
        Assert.assertEquals(new Integer(-304), batteryOverviewController.exportOverview(exportBean,request,response, "zh-CN").getCode());
    }
    /* Started by AICoder, pid:ab934c16899f4a20be7f04bad941bb28 */
    @Test
    public void testCheckTypes_EmptyTypeList() {
        Class<BatteryOverviewController> clazz = BatteryOverviewController.class;
        try {
            Method method = clazz.getDeclaredMethod("checkTypes", List.class,ResponseBean.class);
            // 抑制访问修饰符，使得私有方法变为可以访问的
            Assert.assertNotNull( method );
            method.setAccessible(true);
            List<String> types = Arrays.asList("type1", "invalidType");
            ResponseBean result = Mockito.mock(ResponseBean.class);

            method.invoke(batteryOverviewController, types,result);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            Assert.fail();
        }
    }
    /* Ended by AICoder, pid:ab934c16899f4a20be7f04bad941bb28 */

    /* Started by AICoder, pid:37fa8af11d4f4ad0b9be2995211fd89b */
    @Test
    public void testCheckSortOrderParam_InvalidSort() {

        Class<BatteryOverviewController> clazz = BatteryOverviewController.class;
        try {
            Method method = clazz.getDeclaredMethod("checkSortOrderParam", BatteryOverviewFilterOverviewRequestDto.class,ResponseBean.class);

            Assert.assertNotNull( method );
            method.setAccessible(true);
            BatteryOverviewFilterOverviewRequestDto dao = new BatteryOverviewFilterOverviewRequestDto();
            //排序方式
            dao.setSort("***");
            ResponseBean result = Mockito.mock(ResponseBean.class);
            method.invoke(batteryOverviewController,dao,result);
            //排序字段
            dao.setSort("");
            dao.setOrder("***");
            method.invoke(batteryOverviewController,dao,result);

        }
        catch (Exception e)
        {
            e.printStackTrace();
            Assert.fail();
        }

    }
    /* Ended by AICoder, pid:37fa8af11d4f4ad0b9be2995211fd89b */
    @Test
    /* Started by AICoder, pid:f0df388090i449414a3709cea0be484805b54432 */
    public void exportOverviewTest2() throws Exception {
        List<IdNameBean> lists = new ArrayList<>();
        PageInfo<IdNameBean> page = new PageInfo<>(lists);

        // 使用Mockito模拟多个方法的返回值
        Mockito.doReturn(page).when(battTypeDomain).selectLevels(Mockito.any());
        Mockito.doReturn(page).when(battSohDomain).selectSohLevels(Mockito.any());
        Mockito.doReturn(page).when(battSocDomain).selectSocLevels(Mockito.any());
        Mockito.doReturn(page).when(battRatedCapDomain).selectBattRatedCapLevels(Mockito.any());
        Mockito.doReturn(page).when(battChargeStatusDomain).selectBattChargeStatusOptions(Mockito.any());
        Mockito.doReturn(page).when(battAlarmDomain).selectAlarmLevels(Mockito.any());
        Mockito.doReturn(null).when(battAssetAttributeDomain).selectAttributeEnable(Mockito.anyList(), Mockito.any());

        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        List<String> list = Lists.newArrayList();
        list.add("da");

        BatteryOverviewExportBean exportBean = new BatteryOverviewExportBean();
        List<String> types = new ArrayList<>();
        types.add("0");
        ArrayList<ImageBean> images = new ArrayList<>();
        images.add(new ImageBean());
        exportBean.setImages(images);
        exportBean.setStartDateBegin("2022-05-17");
        exportBean.setStartDateEnd("2022-05-18");
        exportBean.setLogicId("sa");
        ArrayList<String> statisticsDims = new ArrayList<>();
        statisticsDims.add("alarm");
        exportBean.setStatisticsDims(statisticsDims);

        BatteryOverviewBean bean = new BatteryOverviewBean();
        bean.setId("name");
        exportBean.setBattOverViewdims(Arrays.asList(bean));
        // 模拟jsonService.objectToJson和msgSenderService.sendMsgAsync方法的行为
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(), Mockito.any());
        Mockito.doReturn("1").when(battOverviewConfigService).exportOverview(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        Assert.assertEquals(new Integer(1), batteryOverviewController.exportOverview(exportBean, request, response, "zh-CN").getCode());
        when(battOverviewConfigService.exportOverview(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new UedmException(-1, ""));
       batteryOverviewController.exportOverview(exportBean, request, response, "zh-CN");

    }
    /* Ended by AICoder, pid:f0df388090i449414a3709cea0be484805b54432 */
}