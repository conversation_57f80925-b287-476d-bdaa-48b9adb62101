package com.zte.uedm.battery.controller.batttest;

import com.zte.uedm.battery.service.BatteryTestHistoryService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

/**
 * @ Author     ：10260977
 * @ Date       ：11:13 2022/8/11
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
public class BatteryTestHistoryControllerTest
{
    @InjectMocks
    private BatteryTestHistoryController batteryTestHistoryController;

    @Mock
    private BatteryTestHistoryService batteryTestHistoryService;

    @Mock
    private MessageSenderService msgSenderService;

    private HttpServletRequest request;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void recordIndexDimSelectByUserTest() throws UedmException
    {
        Mockito.doReturn(new ArrayList<>()).when(batteryTestHistoryService).selectByUserName(Mockito.any(),Mockito.any(), Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestHistoryController.recordIndexDimSelectByUser("1","", mock);
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void recordIndexDimSelectByUserTest_Exc() throws UedmException
    {
        try
        {
            Mockito.doThrow(new UedmException(-200,"")).when(batteryTestHistoryService).selectByUserName(Mockito.any(),Mockito.any(), Mockito.any());
            HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
            batteryTestHistoryController.recordIndexDimSelectByUser("1","", mock);
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-200, java.util.Optional.ofNullable(e.getErrorId()));
        }
    }

    @Test
    public void recordIndexDimSaveTest() throws UedmException
    {
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestHistoryController.recordIndexDimSave(new ArrayList<>(), "", mock);
        Assert.assertEquals(null,responseBean.getTotal());
    }

    @Test
    public void recordIndexDimSaveTest_Exc() throws UedmException
    {
        try
        {
            Mockito.doThrow(new UedmException(-200,"")).when(batteryTestHistoryService).updateUserRecordIndexDim(Mockito.any(), Mockito.any());
            HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
            batteryTestHistoryController.recordIndexDimSave(new ArrayList<>(), "", mock);
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-200, java.util.Optional.ofNullable(e.getErrorId()));
        }
    }
}
