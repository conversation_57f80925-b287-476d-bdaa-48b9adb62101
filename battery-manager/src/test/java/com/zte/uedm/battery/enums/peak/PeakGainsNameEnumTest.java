package com.zte.uedm.battery.enums.peak;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class PeakGainsNameEnumTest
{
    @Test
    public void testGetName()
    {
        assertEquals("{\"en_US\":\"Charge Cost\",\"zh_CN\":\"充电费用\"}", PeakGainsNameEnum.CHARGECOST.getName());
        assertEquals("{\"en_US\":\"Discharge Cost\",\"zh_CN\":\"放电价值\"}", PeakGainsNameEnum.DISCHARGECOST.getName());
        assertEquals("{\"en_US\":\"Revenue\",\"zh_CN\":\"收益\"}", PeakGainsNameEnum.PROFIT.getName());
    }
}
