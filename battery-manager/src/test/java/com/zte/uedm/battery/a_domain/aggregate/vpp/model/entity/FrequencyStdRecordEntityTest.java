package com.zte.uedm.battery.a_domain.aggregate.vpp.model.entity;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/* Started by AICoder, pid:4ac015bdd1pae4c1403c080b6094dc0e98091e0f */
public class FrequencyStdRecordEntityTest {

    @Test
    public void FrequencyStdRecordEntityTest() throws Exception {
        FrequencyStdRecordEntity frequencyStdRecordEntity = new FrequencyStdRecordEntity();
        PojoTestUtil.TestForPojo(frequencyStdRecordEntity.getClass());
        Assert.assertEquals(frequencyStdRecordEntity.toString(), new FrequencyStdRecordEntity().toString());
    }
}
/* Ended by AICoder, pid:4ac015bdd1pae4c1403c080b6094dc0e98091e0f */
