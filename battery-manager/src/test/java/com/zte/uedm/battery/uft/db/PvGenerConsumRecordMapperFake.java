package com.zte.uedm.battery.uft.db;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.zte.uedm.battery.bean.pv.*;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import com.zte.uedm.battery.util.FakeBranchFlag;
import com.zte.uedm.common.exception.UedmException;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class PvGenerConsumRecordMapperFake implements PvGenerConsumRecordMapper {

    public static String SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NULL;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public PvGenerConsumRecordMapperFake() {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }
    @Override
    public Integer insert(List<PvGenerConsumRecordBean> list, String grain) {
        return null;
    }

    @Override
    public List<PvGenerConsumRecordBean> selectByCondition(PvGenerConsumRecordBean solarGenerConsumRecordBean) {
        return null;
    }

    @Override
    public List<PvGenerConsumRecordBean> selectByPvIdandTimeRange(String pvId, String beginTime, String endTime) throws UedmException {
        return null;
    }

    @Override
    public List<PvPowerAnalysisResponseBean> selectPvIdRecord(List<String> pvIds, String beginTime, String endTime) {
        return null;
    }

    @Override
    public void updateLowerDay(PvGenerConsumRecordBean solarGenerConsumRecordBean) {

    }

    @Override
    public List<PvGenerConsumRecordBean> selectByPvTypeAndGrainTime(List<String> pvIds, String pvType, String recordTime, String grain) throws UedmException {
        return null;
    }

    @Override
    public List<String> selectPvIdsByCondition(PvGenerConsumRecordBean pvGenerConsumRecordBean) throws UedmException {
        return null;
    }

    @Override
    public List<PvPowerAnalysisResponseBean> selectOverViewRecordByConditionNoGrain(PvCommonQueryMapperBean pvCommonQueryMapperBean) {
        return null;
    }

    @Override
    public List<PvPowerAnalysisResponseBean> selectOverViewRecordByConditionWithGrain(PvCommonQueryMapperBean pvCommonQueryMapperBean) {
        List<PvPowerAnalysisResponseBean> list = new ArrayList<>();
        if (SELECT_OVERVIEW_RECORD_BY_CONDITION.equals(FakeBranchFlag.DATA_NORMAL)) {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("data/db/pv-gener-consum-record-mapper/select_overview_record_by_condition_with_grain.json");
            try {
                list = objectMapper.readValue(inputStream, new TypeReference<List<PvPowerAnalysisResponseBean>>() {
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    @Override
    public List<PvTrendDataBean> selectTrendRecordByCondition(PvTrendQueryRequestBean pvTrendQueryRequestBean) {
        return null;
    }

    @Override
    public HistoryDataResponseConditionBean selectSpcuGenerConsumBySpu(SpcuRelashipSpuMapperBean spcuRelashipSpuMapperBean) {
        return null;
    }
}
