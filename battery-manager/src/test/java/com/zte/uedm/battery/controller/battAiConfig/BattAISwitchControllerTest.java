package com.zte.uedm.battery.controller.battAiConfig;

import com.zte.uedm.battery.controller.battAiConfig.dto.AiConfigDto;
import com.zte.uedm.battery.controller.battAiConfig.vo.AiConfigVo;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import java.util.Optional;

import static org.mockito.Mockito.mock;

public class BattAISwitchControllerTest {
    @InjectMocks
    private BattAISwitchController battAISwitchController;
    @Mock
    private BattAISwitchService battAISwitchService;
    private HttpServletRequest request;
    @Mock
    private JsonService jsonService;
    @Mock
    private MessageSenderService msgSenderService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        request = mock(HttpServletRequest.class);
    }

    @Test
    public void selectByIdTest() throws UedmException {
        AiConfigVo aiConfigVo = new AiConfigVo();
        aiConfigVo.setFlag(true);
        aiConfigVo.setId("ai.life");
        ResponseBean responseBean1 = battAISwitchController.selectById("ai", "zh-CN", request);
        Assert.assertNotNull(responseBean1.getError());

        Mockito.doReturn(aiConfigVo).when(battAISwitchService).selectConfigById("ai.life", "zh-CN");
        ResponseBean responseBean2 = battAISwitchController.selectById("ai.life", "zh-CN", request);
        Assert.assertEquals(0, responseBean2.getCode().intValue());

        Mockito.doThrow(new UedmException(1, "")).when(battAISwitchService).selectConfigById("ai.life", "zh-CN");
        ResponseBean responseBean3 = battAISwitchController.selectById("ai.life", "zh-CN", request);
        Assert.assertNotNull(responseBean3.getMessage());
    }

    @Test
    public void updateByIdTest() throws UedmException {
        AiConfigVo prevAiConfigVo = new AiConfigVo();
        prevAiConfigVo.setId("ai.life");
        prevAiConfigVo.setValue("ON");
        Mockito.doReturn(prevAiConfigVo).when(battAISwitchService).selectConfigById(Mockito.any(),Mockito.any());
        Mockito.doReturn("msg").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        ResponseBean responseBean1 = battAISwitchController.updateById(new AiConfigDto("ai", "on"), "zh-CN", request);
        Assert.assertNotNull(responseBean1.getError());

        Mockito.doReturn(1).when(battAISwitchService).updateById("ai.life","ON");
        ResponseBean responseBean2 = battAISwitchController.updateById(new AiConfigDto("ai.life", "ON"), "zh-CN", request);
        Assert.assertEquals(Optional.of(1), Optional.ofNullable(responseBean2.getTotal()));

        Mockito.doThrow(new UedmException(1, "")).when(battAISwitchService).selectConfigById(Mockito.any(),Mockito.any());
        Mockito.doThrow(new UedmException(201,"")).when(battAISwitchService).updateById("ai.life","ON");
        ResponseBean responseBean3 = battAISwitchController.updateById(new AiConfigDto("ai.life", "ON"), "zh-CN", request);
        Assert.assertNotNull(responseBean3.getMessage());
    }

    @Test
    public void selectEolByIdTest() throws UedmException {
        Assert.assertNotNull(battAISwitchController.selectEolById(null,null,null));
        Mockito.doReturn("1").when(battAISwitchService).selectEolById(Mockito.any());
        Assert.assertNotNull(battAISwitchController.selectEolById("life.eol",null,null));
        Mockito.doThrow(new UedmException(1,"")).when(battAISwitchService).selectEolById(Mockito.any());
        Assert.assertNotNull(battAISwitchController.selectEolById("life.eol",null,null));
    }
    @Test
    public void updateEolByIdTest()throws UedmException{
        Mockito.doReturn("0.6").when(battAISwitchService).selectEolById(Mockito.any());
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battAISwitchController.updateEolById(null,null,null));
        AiConfigDto aiConfigDto=new AiConfigDto("life.eol","1");
        Mockito.doReturn(1).when(battAISwitchService).updateById(Mockito.any(),Mockito.any());
        Assert.assertNotNull(battAISwitchController.updateEolById(aiConfigDto,"zh-CN",request));
        Mockito.doThrow(new UedmException(1, "")).when(battAISwitchService).selectEolById(Mockito.any());
        Mockito.doThrow(new UedmException(1,"")).when(battAISwitchService).updateById(Mockito.any(),Mockito.any());
        Assert.assertNotNull(battAISwitchController.updateEolById(aiConfigDto,"zh-CN",request));
    }
//    @Test
//    public void updateByIdTest() throws UedmException {
//        Mockito.doReturn("msg").when(jsonService).objectToJson(Mockito.any());
//        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
//        ResponseBean responseBean1 = battAISwitchController.updateById("ai", "on", "zh-CN", request);
//        Assert.assertNotNull(responseBean1.getError());
//
//        Mockito.doReturn(1).when(battAISwitchService).updateById("ai.life","ON");
//        ResponseBean responseBean2 = battAISwitchController.updateById("ai.life", "ON", "zh-CN", request);
//        Assert.assertEquals(Optional.of(1), Optional.ofNullable(responseBean2.getTotal()));
//
//        Mockito.doThrow(new UedmException(201,"")).when(battAISwitchService).updateById("ai.life","ON");
//        ResponseBean responseBean3 = battAISwitchController.updateById("ai.life", "ON", "zh-CN", request);
//        Assert.assertNotNull(responseBean3.getMessage());
//
//        ResponseBean responseBean4 = battAISwitchController.updateById("life.eol", "ON", "zh-CN", request);
//        Assert.assertNotNull(responseBean4.getMessage());
//
//        ResponseBean responseBean5 = battAISwitchController.updateById("life.eol", "1.01", "zh-CN", request);
//        Assert.assertNotNull(responseBean5.getMessage());
//
//        ResponseBean responseBean6 = battAISwitchController.updateById("life.eol", "0.1", "zh-CN", request);
//        Assert.assertNull(responseBean6.getMessage());
//    }
}
