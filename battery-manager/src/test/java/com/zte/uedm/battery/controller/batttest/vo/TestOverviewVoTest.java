package com.zte.uedm.battery.controller.batttest.vo;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Assert;
import org.junit.Test;

public class TestOverviewVoTest {

    @Test
    public void test(){
        TestOverviewVo testOverviewVo = new TestOverviewVo();
        testOverviewVo.setTestStatusId("77");
        testOverviewVo.setTestStatus(new IdNameBean());
        testOverviewVo.setPowerSupplyScene(new IdNameBean());
        testOverviewVo.setTestStartTime("77");
        testOverviewVo.toString();

        Assert.assertEquals("77",testOverviewVo.getTestStatusId());
    }

}
