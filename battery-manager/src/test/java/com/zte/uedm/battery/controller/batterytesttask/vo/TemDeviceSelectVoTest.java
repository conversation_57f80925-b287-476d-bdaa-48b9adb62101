package com.zte.uedm.battery.controller.batterytesttask.vo;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class TemDeviceSelectVoTest
{
    @Test
    public void testSetGetToString()
    {
        TemDeviceSelectVo bean = new TemDeviceSelectVo();
        bean.setName("1");
        bean.setId("1");
        bean.setTestStatus(new IdNameBean());
        bean.setHealthStatus(new IdNameBean());
        bean.setBackupPowerStatus(new IdNameBean());
        bean.setPathName("1");


        bean.getName();
        bean.getPathName();
        bean.getHealthStatus();
        bean.getTestStatus();
        bean.getBackupPowerStatus();

        bean.toString();
        assertEquals("1",bean.getPathName());
    }
}
