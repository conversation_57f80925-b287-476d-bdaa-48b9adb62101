package com.zte.uedm.battery.enums.batttest;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2023年03月15日 下午5:19
 * @Version : 1.0
 */
public class BatteryTestResultEnumsTest
{
    @Test
    public void test(){
        BatteryTestResultEnums.NORMAL.getId();
        BatteryTestResultEnums.NORMAL.getName();
        BatteryTestResultEnums.FAULT.getId();
        BatteryTestResultEnums.FAULT.getName();
        BatteryTestResultEnums.FAULT.getSequence();

        String name = BatteryTestResultEnums.NORMAL.getName();
        Assert.assertEquals("{\"zh_CN\":\"正常\",\"en_US\":\"Normal\"}",name);
        List<String> ids = BatteryTestResultEnums.getAllIds();
        Assert.assertEquals(3,ids.size());
        BatteryTestResultEnums.getNameById("1");
        List<Pair<String, String>> allIdName = BatteryTestResultEnums.getAllIdName();
        Assert.assertEquals(3,allIdName.size());
    }
}
