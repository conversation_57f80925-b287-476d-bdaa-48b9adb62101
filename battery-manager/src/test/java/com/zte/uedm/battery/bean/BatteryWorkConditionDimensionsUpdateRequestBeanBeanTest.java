package com.zte.uedm.battery.bean;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class BatteryWorkConditionDimensionsUpdateRequestBeanBeanTest {
    @Test
    public void testSetGetToString()
    {
        BatteryWorkConditionDimensionsUpdateRequestBeanBean bb = new BatteryWorkConditionDimensionsUpdateRequestBeanBean();
        bb.setId("1");
        bb.setSequence(1);
        bb.setEnable(false);

        bb.toString();
        assertEquals(1,(int) bb.getSequence());
    }


}