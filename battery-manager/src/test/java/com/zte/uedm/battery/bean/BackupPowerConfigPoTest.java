package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;

public class BackupPowerConfigPoTest
{
    @Test
    public void test()
    {
        BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
        backupPowerConfigPo.setId("name");
        backupPowerConfigPo.setName("77");
        backupPowerConfigPo.setSequence(7);
        backupPowerConfigPo.setCreator("admin");
        backupPowerConfigPo.toString();
        Assert.assertEquals("admin",backupPowerConfigPo.getCreator());

    }
}
