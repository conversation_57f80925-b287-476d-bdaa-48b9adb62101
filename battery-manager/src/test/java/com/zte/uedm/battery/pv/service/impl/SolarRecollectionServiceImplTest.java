package com.zte.uedm.battery.pv.service.impl;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.opti.domain.service.AuthorizationService;
import com.zte.uedm.battery.pv.bean.SolarRecollectionLogBean;
import com.zte.uedm.battery.pv.dto.ManualSupplementaryDto;
import com.zte.uedm.battery.pv.mapper.SolarRecollectionRecordMapper;
import com.zte.uedm.battery.pv.vo.SiteRecollectionStatusVo;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.CurrRecollectionSiteVo;
import com.zte.uedm.battery.rpc.vo.SiteRecollectionCapabilityBean;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.kafka.producer.bean.SouthOriginalDataRecollectKafkaBean;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.doThrow;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @Description 太阳能补采service测试
 * @Date 11/1/23 3:03 PM
 * @Version 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SolarRecollectionServiceImplTest {
    @InjectMocks
    private SolarRecollectionServiceImpl solarRecollectionService;

    @Mock
    private SolarRecollectionRecordMapper solarRecollectionRecordMapper;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Mock
    private ConfigurationService configurationService;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private ConfigService configService;

    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private LogUtils logUtils;
    @Mock
    private MessageSenderService msgSenderService;

    @Mock
    private JsonService jsonService;

    private HttpServletRequest request;
    @Mock
    private FieldCacheManager fieldCacheManager;
    @Mock
    private AuthorizationService authorizationService;

    @Test
    public void manualSupplementaryByTimeRangeTest() throws UedmException, com.zte.uedm.basis.exception.UedmException {
        ManualSupplementaryDto manualSupplementaryDto = new ManualSupplementaryDto();
        manualSupplementaryDto.setStartTime("2023-10-20 10:10:30");
        manualSupplementaryDto.setEndTime("2023-10-20 10:20:30");
        List<String> siteIdList = new ArrayList<>();
        siteIdList.add("aaa");
        manualSupplementaryDto.setSiteIdList(siteIdList);
        List<String> pointIdList = new ArrayList<>();
        manualSupplementaryDto.setPointList(pointIdList);

        HttpServletRequest request = mock(HttpServletRequest.class);

        // 补采能力false &&  测点为空
        List<SiteRecollectionCapabilityBean> siteRecollectionCapabilityBeanList = new ArrayList<>();
        when(configurationManagerRpcImpl.queryRecollectionCapabilityWithSite(any())).thenReturn(siteRecollectionCapabilityBeanList);
        when(configService.getGlobalProperty(any())).thenReturn("2");
        Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(Arrays.asList("aaa"));
        when(dateTimeService.getCurrentDateTime()).thenReturn(new Date(2023,10,20,10,10,10));
        try{
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"zh-CN");
        }catch (UedmException e){
            Assert.assertEquals(-301, e.getErrorId().intValue());
        }

        // 测点不为空
        SiteRecollectionCapabilityBean capabilityBean = new SiteRecollectionCapabilityBean();
        capabilityBean.setRecollectionAble(true);
        siteRecollectionCapabilityBeanList.add(capabilityBean);
        when(configurationManagerRpcImpl.queryRecollectionCapabilityWithSite(any())).thenReturn(siteRecollectionCapabilityBeanList);
        List<String> logSiteIdList = new ArrayList<>();
        logSiteIdList.add("log1");
        try{
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"");
        }catch (UedmException e){
            Assert.assertEquals(-305, e.getErrorId().intValue());
        }

        List<MonitorDeviceBaseBean> deviceBaseBeanList = new ArrayList<>();
        MonitorDeviceBaseBean deviceBaseBean = new MonitorDeviceBaseBean();
        deviceBaseBean.setId("801E");
        deviceBaseBean.setProtocolType("SF");
        deviceBaseBeanList.add(deviceBaseBean);
        when(configurationManagerRpcImpl.queryDeviceBySiteList(any(), any())).thenReturn(deviceBaseBeanList);
        when(i18nUtils.getMapFieldByLanguageOption(any(), any())).thenReturn("补采");
        List<FieldEntity> list = new ArrayList<>();
        FieldEntity fieldEntity = new FieldEntity();
        fieldEntity.setId("aaa");
        fieldEntity.setName("2");
        list.add(fieldEntity);
        when(fieldCacheManager.selectByIds(anySet())).thenReturn(list);
        try{
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"");
        }catch (UedmException e){
            Assert.assertEquals(-304, e.getErrorId().intValue());
        }

        // 补采中……
        List<String> siteIds = new ArrayList<>();
        siteIds.add("aaa");
        when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(),any())).thenReturn(siteIds);
        try{
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"");
        }catch (UedmException e){
            Assert.assertEquals(-302, e.getErrorId().intValue());
        }

        logSiteIdList.add("log2");
        when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(), any())).thenReturn(logSiteIdList);
        pointIdList.add("aaa-1");
        manualSupplementaryDto.setPointList(pointIdList);
        try{
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"");
        }catch (UedmException e){
            Assert.assertEquals(-304, e.getErrorId().intValue());
        }

        try{
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(new ArrayList<>());
            solarRecollectionService.manualSupplementaryByTimeRange(manualSupplementaryDto, request,"");
        }catch (UedmException e){
            Assert.assertEquals(-635, e.getErrorId().intValue());
        }
    }

    @Test
    public void querySiteRecollectionStatusTest() throws com.zte.uedm.basis.exception.UedmException {
        List<String> siteIdList = new ArrayList<>();
        when(dateTimeService.getCurrentDateTime()).thenReturn(new Date());
        // 站点为空
        List<SiteRecollectionStatusVo> res1 = solarRecollectionService.querySiteRecollectionStatus(siteIdList);
        assertEquals(0, res1.size());

        siteIdList.add("aaa");
        siteIdList.add("bbb");
        List<String> recollectionSiteList = new ArrayList<>();
        recollectionSiteList.add("aaa");
        Mockito.when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(), any())).thenReturn(recollectionSiteList);
        Mockito.when(solarRecollectionRecordMapper.querySiteRecollectionLog(Mockito.any(), Mockito.any())).thenReturn(recollectionSiteList);
        List<SiteRecollectionStatusVo> res2 = solarRecollectionService.querySiteRecollectionStatus(siteIdList);
        assertEquals(2, res2.size());

        /* Started by AICoder, pid:48354sb7d8e2aea1475e08e760cb890e05353499 */
        when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(), any())).thenThrow(new RuntimeException());
        try {
            solarRecollectionService.querySiteRecollectionStatus(siteIdList);
        } catch (Exception e) {
        }
        /* Ended by AICoder, pid:48354sb7d8e2aea1475e08e760cb890e05353499 */
    }
    @Test
    public void getCurrentRecollectionSiteListTest() throws com.zte.uedm.basis.exception.UedmException {
        when(dateTimeService.getCurrentDateTime()).thenReturn(new Date());
        List<String> recollectionSiteList = new ArrayList<>();
        recollectionSiteList.add("aaa");
        when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(), any())).thenReturn(recollectionSiteList);
        List<String> res = solarRecollectionService.getCurrentRecollectionSiteList();
        assertEquals(1, res.size());

        doThrow(new RuntimeException()).when(solarRecollectionRecordMapper).querySiteRecollectionLog(any(),any());
        List<String> res1 = solarRecollectionService.getCurrentRecollectionSiteList();
        assertEquals(0, res1.size());
    }

    @Test
    public void getCurrentRecollectionSiteNumTest() throws com.zte.uedm.basis.exception.UedmException {
        when(dateTimeService.getCurrentDateTime()).thenReturn(new Date());
        when(configService.getGlobalProperty(any())).thenReturn("30");
        CurrRecollectionSiteVo res1 = solarRecollectionService.getCurrentRecollectionSiteNum();
        assertEquals(0, res1.getCurrentRecollectionSiteNum().intValue());
        // 数据查询异常
        Mockito.when(solarRecollectionRecordMapper.querySiteRecollectionLog(any(), any())).thenThrow(new RuntimeException());
        CurrRecollectionSiteVo res2 = solarRecollectionService.getCurrentRecollectionSiteNum();
        Assert.assertNull(res2.getCurrentRecollectionSiteNum());
    }

    @Test
    public void getRecollectionLogBySiteTest() throws UedmException, com.zte.uedm.basis.exception.UedmException {
        try {
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(Arrays.asList("11"));
            solarRecollectionService.getRecollectionLogBySite(null);
        }catch (UedmException e){
            Assert.assertEquals(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, e.getErrorId());
        }

        List<SolarRecollectionLogBean> beanList = new ArrayList<>();
        when(solarRecollectionRecordMapper.querySiteRecollectionLogWithLimit(any(),any())).thenReturn(beanList);
        List<SolarRecollectionLogBean> res = solarRecollectionService.getRecollectionLogBySite("aaa");
        Assert.assertEquals(0, res.size());

        doThrow(new RuntimeException()).when(solarRecollectionRecordMapper).querySiteRecollectionLogWithLimit(any(),any());
        try {
            solarRecollectionService.getRecollectionLogBySite("null");
        }catch (UedmException e){
            Assert.assertEquals(DatabaseExceptionEnum.OPERATEDB.getCode(), e.getErrorId());
        }

        try {
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(new ArrayList<>());
            solarRecollectionService.getRecollectionLogBySite("null");
        }catch (UedmException e){
            Assert.assertEquals(-635, e.getErrorId().intValue());
        }

    }

    @Test
    public void sendOriginalDataRecollectMsgToDeviceTest() throws UedmException {
        //call OK
        List<SouthOriginalDataRecollectKafkaBean> list = new ArrayList<>();
        SouthOriginalDataRecollectKafkaBean bean = new SouthOriginalDataRecollectKafkaBean();
        bean.setProtocolType("ZTE_SF");
        list.add(bean);
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(), Mockito.any());
        Mockito.doReturn("test").when(jsonService).objectToJson(Mockito.any());
        Method method = PowerMockito.method(SolarRecollectionServiceImpl.class, "sendOriginalDataRecollectMsgToDevice", List.class);
        try {
            method.invoke(solarRecollectionService, list);
        } catch (Exception e) {
            Assert.fail();
        }
        //call exception
        Mockito.doThrow(new RuntimeException("xxx")).when(msgSenderService).sendMsgAsync(Mockito.any(), Mockito.any());
        try {
            method.invoke(solarRecollectionService, list);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }
}
