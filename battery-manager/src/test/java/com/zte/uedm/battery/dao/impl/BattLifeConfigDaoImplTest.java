/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattLifeConfigDaoImplTest
 * 文件作者：00248587
 * 开发时间：2023/3/2
 */
package com.zte.uedm.battery.dao.impl;

import com.zte.uedm.battery.bean.BattLifeConfigPo;
import com.zte.uedm.battery.mapper.BattLifeConfigMapper;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class BattLifeConfigDaoImplTest
{
    @InjectMocks
    private BattLifeConfigDaoImpl battLifeConfigDao;

    @Mock
    private BattLifeConfigMapper battLifeConfigMapper;

    @Before
    public void init()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectBattLifeConfig_normal() throws Exception {
        Mockito.doReturn(Arrays.asList(new BattLifeConfigPo())).when(battLifeConfigMapper).selectBattLifeConfig(Mockito.any());
        List<BattLifeConfigPo> battLifeConfigPos = battLifeConfigDao.selectBattLifeConfig("77");
        Assert.assertEquals(1, battLifeConfigPos.size());
    }

    @Test
    public void selectBattLifeConfig_ex()
    {
        try {
            Mockito.doThrow(new RuntimeException()).when(battLifeConfigMapper).selectBattLifeConfig(Mockito.any());
            battLifeConfigDao.selectBattLifeConfig("77");
        }catch (Exception e)
        {
            Assert.assertEquals("Database select failed",e.getMessage());
        }
    }

    @Test
    public void insertBattLifeConfigByBeans_normal() throws UedmException {
        Exception flag = null;
        try
        {
            Mockito.doNothing().when(battLifeConfigMapper).insertBattLifeConfigByBeans(Mockito.any());
            battLifeConfigDao.insertBattLifeConfigByBeans(Arrays.asList(new BattLifeConfigPo()));
        }
        catch (Exception e)
        {
            flag = new Exception();
        }
        Assert.assertTrue(flag== null);
    }

    @Test
    public void insertBattLifeConfigByBeans_ex() throws UedmException
    {
        try {
            Mockito.doThrow(new RuntimeException()).when(battLifeConfigMapper).insertBattLifeConfigByBeans(Mockito.any());
            battLifeConfigDao.insertBattLifeConfigByBeans(new ArrayList<>());
        }catch (Exception e)
        {
            Assert.assertEquals("Database add failed",e.getMessage());
        }
    }

    @Test
    public void updateBattLifeConfigByBeans_normal() throws Exception {
        Mockito.doReturn(2).when(battLifeConfigMapper).updateBattLifeConfigByBeans(Mockito.any());
        Integer num = battLifeConfigDao.updateBattLifeConfigByBeans(Arrays.asList(new BattLifeConfigPo()));
        Assert.assertEquals(2, num.intValue());
    }

    @Test
    public void updateBattLifeConfigByBeans_ex()
    {
        try {
            Mockito.doThrow(new RuntimeException()).when(battLifeConfigMapper).updateBattLifeConfigByBeans(Mockito.any());
            battLifeConfigDao.updateBattLifeConfigByBeans(new ArrayList<>());
        }catch (Exception e)
        {
            Assert.assertEquals("Database update failed",e.getMessage());
        }
    }

    @Test
    public void testUpdateMultiLifeCfgNameByIdAndUser() throws Exception {

        try {
            Mockito.when(battLifeConfigMapper.updateMultiLifeCfgNameByIdAndUser(Mockito.any())).thenThrow(new SQLException());
            battLifeConfigDao.updateMultiLifeCfgNameByIdAndUser(Collections.singletonList(new BattLifeConfigPo()));
        }catch (Exception e)
        {
            Assert.assertEquals("Database update failed",e.getMessage());
        }

        battLifeConfigDao.updateMultiLifeCfgNameByIdAndUser(new ArrayList<>());

        Mockito.doReturn(2).when(battLifeConfigMapper).updateMultiLifeCfgNameByIdAndUser(Mockito.any());
        Integer num = battLifeConfigDao.updateMultiLifeCfgNameByIdAndUser(Collections.singletonList(new BattLifeConfigPo()));

        Assert.assertEquals(2, num.intValue());

    }



    @Test
    public void searchBattLifeConfig_normal() throws Exception {
        Mockito.doReturn(Arrays.asList(new BattLifeConfigPo())).when(battLifeConfigMapper).searchBattLifeConfig(Mockito.any(), Mockito.any());
        List<BattLifeConfigPo> battLifeConfigPos = battLifeConfigDao.searchBattLifeConfig("12", "12");
        Assert.assertEquals(1, battLifeConfigPos.size());
    }

    @Test
    public void searchBattLifeConfig_ex()
    {
        try {
            Mockito.doThrow(new RuntimeException()).when(battLifeConfigMapper).searchBattLifeConfig(Mockito.any(), Mockito.any());
            List<BattLifeConfigPo> battLifeConfigPos = battLifeConfigDao.searchBattLifeConfig("1", "2");
        }catch (Exception e)
        {
            Assert.assertEquals("Database select failed",e.getMessage());
        }
    }
}
