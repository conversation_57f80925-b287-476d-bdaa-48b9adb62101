package com.zte.uedm.battery.aop;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import javax.validation.metadata.ConstraintDescriptor;

public class ConstraintViolationImpl<T> implements ConstraintViolation<T>
{

    @Override
    public String getMessage() {
        // TODO Auto-generated method stub
        return "aaaaa";
    }

    @Override
    public String getMessageTemplate() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public T getRootBean() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Class<T> getRootBeanClass() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object getLeafBean() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object[] getExecutableParameters() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object getExecutableReturnValue() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Path getPropertyPath() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object getInvalidValue() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public ConstraintDescriptor<?> getConstraintDescriptor() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public <U> U unwrap(Class<U> type) {
        // TODO Auto-generated method stub
        return null;
    }

}