package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;
import java.util.Date;

public class BattBackupPowerThresholdDetailPojoTest
{
    @Test
    public void Test()
    {
        try {
            PojoTestUtil.TestForPojo(BattBackupPowerThresholdDetailPojo.class);
            BattBackupPowerThresholdDetailPojo detailPojo = new BattBackupPowerThresholdDetailPojo("username", "id", 12, "category_id");
            BattBackupPowerThresholdDetailPojo detailPojo2 = new BattBackupPowerThresholdDetailPojo(12, "id", "username");
            BattBackupPowerThresholdDetailPojo detailPojo3 = new BattBackupPowerThresholdDetailPojo("username", "id", 12, "category_id","type");
            BattBackupPowerThresholdDetailPojo detailPojo1 = new BattBackupPowerThresholdDetailPojo("username", "id", 12);
            Assert.assertNotEquals(detailPojo, detailPojo2);
            Assert.assertEquals(detailPojo1.getType(), "default");
            Assert.assertNotEquals(detailPojo2, detailPojo3);
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(BattBackupPowerThresholdDetailPojo.class);
        BattBackupPowerThresholdDetailPojo battBackupPowerThresholdDetailPojo = new BattBackupPowerThresholdDetailPojo();
        battBackupPowerThresholdDetailPojo.setId("2");
        battBackupPowerThresholdDetailPojo.setThreshold(1);
        battBackupPowerThresholdDetailPojo.setCreator("2");
        battBackupPowerThresholdDetailPojo.setGmtCreate(new Date());
        battBackupPowerThresholdDetailPojo.setCategoryId("2");

        battBackupPowerThresholdDetailPojo.getThreshold();
        battBackupPowerThresholdDetailPojo.getCreator();
        battBackupPowerThresholdDetailPojo.getCategoryId();
        battBackupPowerThresholdDetailPojo.getType();
        battBackupPowerThresholdDetailPojo.getGmtModified();
        BattBackupPowerThresholdDetailPojo bean = new BattBackupPowerThresholdDetailPojo(1,"1","2");
        String s = battBackupPowerThresholdDetailPojo.toString();
        Assert.assertEquals("2", battBackupPowerThresholdDetailPojo.getCreator());
    }

    @Test
    public void test1() throws Exception
    {
        String type = ConfigSourceClassify.SPECIAL.getType();
        String typeZhEn = ConfigSourceClassify.SPECIAL.getTypeZhEn();
        Assert.assertEquals("special", type);
    }
}
