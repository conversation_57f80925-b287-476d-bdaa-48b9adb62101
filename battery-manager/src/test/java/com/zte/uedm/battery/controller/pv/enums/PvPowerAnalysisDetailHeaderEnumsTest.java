package com.zte.uedm.battery.controller.pv.enums;

import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;

public class PvPowerAnalysisDetailHeaderEnumsTest
{

    @Test
    public void test()
    {
        String siteId = PvPowerAnalysisDetailHeaderEnums.SITE.getId();
        Assert.assertEquals(siteId,"site");

        String siteName = PvPowerAnalysisDetailHeaderEnums.SITE.getName();
        Assert.assertEquals(siteName,"{\"en_US\":\"Site\",\"zh_CN\":\"站点名称\"}");

        String powerGenerationName = PvPowerAnalysisDetailHeaderEnums.POWER_GENERATION.getName();
        Assert.assertEquals(powerGenerationName,"{\"en_US\":\"Energy Generation(kWh)\",\"zh_CN\":\"发电量(kWh)\"}");

        String solarRevenueName = PvPowerAnalysisDetailHeaderEnums.SOLAR_REVENUE.getName();
        Assert.assertEquals(solarRevenueName,"{\"en_US\":\"Solar Revenue\",\"zh_CN\":\"太阳能收益\"}");

        String gridFeeName = PvPowerAnalysisDetailHeaderEnums.GRID_FEE.getName();
        Assert.assertEquals(gridFeeName,"{\"en_US\":\"Grid Fee\",\"zh_CN\":\"市电电费\"}");

        Map<String, String> initHeader = PvPowerAnalysisDetailHeaderEnums.initHeader();
        Assert.assertEquals(7,initHeader.size());

        List<String> allEnumsIds = PvPowerAnalysisDetailHeaderEnums.getAllEnumsIds();
        Assert.assertEquals(7,allEnumsIds.size());
    }

}
