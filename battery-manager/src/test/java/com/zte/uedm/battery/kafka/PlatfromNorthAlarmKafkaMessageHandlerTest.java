package com.zte.uedm.battery.kafka;

import com.zte.uedm.battery.bean.alarm.AlarmMsg;
import com.zte.uedm.battery.bean.alarm.AlarmTitle;
import com.zte.uedm.battery.bean.alarm.DisplayName;
import com.zte.uedm.battery.bean.alarm.FmAlarmBean;
import com.zte.uedm.battery.bean.pv.PvExceptionRecordBean;
import com.zte.uedm.battery.mapper.PvAlarmRecordMapper;
import com.zte.uedm.battery.mapper.PvExceptionRecordMapper;
import com.zte.uedm.battery.opti.application.event.kafka.listener.PlatfromNorthAlarmKafkaMessageHandler;
import com.zte.uedm.battery.opti.infrastructure.kafka.PlatformHandler;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.JsonUtil;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * @ Author     ：10260977
 * @ Date       ：15:07 2021/4/9
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
public class PlatfromNorthAlarmKafkaMessageHandlerTest
{
    @InjectMocks
    private PlatfromNorthAlarmKafkaMessageHandler platfromNorthAlarmKafkaMessageHandler;

    @Mock
    private JsonService jsonService;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private PvExceptionRecordMapper pvExceptionRecordMapper;

    @Mock
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Mock
    private BlankService blankService;

    @Mock
    private PvAlarmRecordMapper pvAlarmRecordMapper;

    @Mock
    private PlatformHandler platformHandler;

    @Before
    public void setup()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void onMsgTest_PVids_Null() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(000123L);
            DisplayName displayname = new DisplayName();
            displayname.setEn("sh");
            displayname.setZh("中");
            AlarmTitle alarmTitle = new AlarmTitle();
            alarmTitle.setDisplayname(displayname);
            alarmmsg.setAlarmtitle(alarmTitle);
            //产生
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
            //清除
            alarmUpwardKafka.setMessagetype(0);
            s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11"));
            //产生
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
            //清除
            alarmUpwardKafka.setMessagetype(0);
            s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }


    @Test
    public void onMsgTest_Exc() throws UedmException
    {
        try
        {
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenThrow(new UedmException(-200,""));
            platfromNorthAlarmKafkaMessageHandler.onMsg("s");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Alarm_Map_Null() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            DisplayName displayname = new DisplayName();
            displayname.setEn("sh");
            displayname.setZh("中");
            AlarmTitle alarmTitle = new AlarmTitle();
            alarmTitle.setDisplayname(displayname);
            alarmmsg.setAlarmtitle(alarmTitle);
            //产生
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
            //清除
            alarmUpwardKafka.setMessagetype(0);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            s = JsonUtil.objectToString(alarmUpwardKafka);
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Alarm_Clear_Map_Null() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(0);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(null);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }


    @Test
    public void onMsgTest_Clear_2() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(0);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            List<PvExceptionRecordBean> pvExceptionRecordBeans = new ArrayList<>();
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBean.setState("1");
            Mockito.when(pvExceptionRecordMapper.selectById(Mockito.anyString()))
                    .thenReturn(Lists.newArrayList(pvExceptionRecordBean));
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Clear_3() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(0);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("");
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Miss_Not_Empty() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            //不为空
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBean.setId("11");
            pvExceptionRecordBean.setPvId("11");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(Lists.newArrayList(pvExceptionRecordBean));
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Miss_Empty() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            //不为空
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBean.setId("11");
            pvExceptionRecordBean.setPvId("11");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_AlarmList_Empty() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            //不为空
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBean.setId("11");
            pvExceptionRecordBean.setPvId("11");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(Lists.newArrayList(pvExceptionRecordBean));
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Maintain() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000002L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000002", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Maintain_Not_Empty() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000002L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(1);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);
            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBean.setId("11");
            pvExceptionRecordBean.setPvId("11");
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(Lists.newArrayList(pvExceptionRecordBean));
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000002", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void onMsgTest_Clear() throws UedmException
    {
        try
        {
            FmAlarmBean alarmUpwardKafka = new FmAlarmBean();
            AlarmMsg alarmmsg = new AlarmMsg();
            alarmmsg.setAlarmkey("1");
            alarmmsg.setAlarmcode(1000001L);
            alarmmsg.setMe("11");
            AlarmTitle alarmtitle = new AlarmTitle();
            alarmtitle.setDisplayname(new DisplayName());
            alarmmsg.setAlarmtitle(alarmtitle);
            alarmUpwardKafka.setMessagetype(0);
            alarmUpwardKafka.setAlarmmsg(alarmmsg);
            String s = JsonUtil.objectToString(alarmUpwardKafka);

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.onMsg(s);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void dealPvExceptionAlarm_Exc() throws UedmException
    {
        try
        {
            platfromNorthAlarmKafkaMessageHandler.handlerInitialAlarmClear();
            Mockito.when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                    .thenThrow(new RuntimeException());
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.dealPvExceptionAlarm("s", "2", "3", "1");

        }
        catch (Exception e)
        {
            Assert.assertSame(null,e.getMessage());
        }
    }

    @Test
    public void handlerInitialAlarmClearTest() throws UedmException
    {
        try
        {

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.setAlarmIdWaitClean(maps);
            platfromNorthAlarmKafkaMessageHandler.handlerInitialAlarmClear();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void handlerInitialAlarmClearTest_Empty() throws UedmException
    {
        try
        {

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(new ArrayList<>());
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.setAlarmIdWaitClean(maps);
            platfromNorthAlarmKafkaMessageHandler.handlerInitialAlarmClear();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void handlerInitialAlarmClearTest_Empty2() throws UedmException
    {
        try
        {

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11", "12"));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(null);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.setAlarmIdWaitClean(maps);
            platfromNorthAlarmKafkaMessageHandler.handlerInitialAlarmClear();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void handlerInitialAlarmClearTest_Exc() throws UedmException
    {
        try
        {

            Mockito.when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenThrow(new UedmException(-200, ""));
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-04-10 12:00:00");
            Map<String, Map<String, String>> maps = new HashMap<>();
            Map<String, String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001", "11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            Mockito.when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(maps);
            Mockito.when(pvAlarmRecordMapper.selectExcepionIdById(Mockito.anyString())).thenReturn("11");
            platfromNorthAlarmKafkaMessageHandler.setAlarmIdWaitClean(maps);
            platfromNorthAlarmKafkaMessageHandler.handlerInitialAlarmClear();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    /* Started by AICoder, pid:k30cb21267l6ac1143df095470d358785a62098c */

    @Test
    public void dealPvExceptionAlarm()
    {
        try {
            Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2024-04-10 12:00:00");
            platfromNorthAlarmKafkaMessageHandler.dealPvExceptionAlarm("id", "moid", "name",  "1");
            Map<String, Map<String, String>> pvAlarmCodeMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            map.put("123", "ok");
            pvAlarmCodeMap.put("pv.comp.missing", map);
            pvAlarmCodeMap.put("pv.comp.maintain.alarm", map);
            List<PvExceptionRecordBean> pvExceptionRecordBeans = new ArrayList<>();
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBeans.add(pvExceptionRecordBean);
            when(pvExceptionRecordMapper.selectExceptionAlarmInfoById(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                    .thenReturn(pvExceptionRecordBeans);
            platfromNorthAlarmKafkaMessageHandler.dealPvExceptionAlarm("id", "moid", "name",  "1");
        }
        catch (Exception e)
        {
            Assert.assertEquals(null, e);
        }
    }
    /* Ended by AICoder, pid:k30cb21267l6ac1143df095470d358785a62098c */
}
