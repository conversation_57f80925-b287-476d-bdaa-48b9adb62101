package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.StandardPointCacheManager;
import com.zte.uedm.battery.api.bean.AiBean;
import com.zte.uedm.battery.api.bean.RealtimeDataBean;
import com.zte.uedm.battery.api.service.DataService;
import com.zte.uedm.battery.bean.BattCellQueryBean;
import com.zte.uedm.battery.bean.BattCellVO;
import com.zte.uedm.battery.bean.ImageBean;
import com.zte.uedm.battery.export.manage.ExcelFileWriter;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.service.AlarmService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@SpringBootTest
public class BatteryCellServiceImplTest
{

    @InjectMocks
    private BatteryCellServiceImpl batteryCellServiceImpl;

    @Mock
    DataService dataService;
    @Mock
    AlarmService alarmService;
    @Mock
    I18nUtils i18nUtils;
    @Mock
    private JsonService jsonService;
    @Mock
    private WriterExportFactory wf;

    @Mock
    private StandardPointCacheManager standardPointCacheManager;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getBattCellData() throws UedmException, com.zte.uedm.basis.exception.UedmException {
        batteryCellServiceImpl.getBattCellData(new BattCellQueryBean(),null,"");
        List<String> smpIds = new ArrayList<>();
        smpIds.add("1");
        smpIds.add("2");
        BattCellQueryBean battCellQueryBean = new BattCellQueryBean();
        battCellQueryBean.setSmpIds(smpIds);
        battCellQueryBean.setBattId("1");
        Map<String, Map<String, String>> map1 = getStringMapMap();
        when(dataService.getRealtimeDataByIdAndType(any(), any())).thenReturn(map1);

        StandardPointEntity standardPointEntity = new StandardPointEntity();
        standardPointEntity.setBelongTo("1");
        standardPointEntity.setId("1");
        standardPointEntity.setMoc("1");
        StandardPointEntity standardPointEntity2 = new StandardPointEntity();
        standardPointEntity2.setBelongTo("2");
        standardPointEntity2.setId("2");
        List<StandardPointEntity> list = new ArrayList<>();
        list.add(standardPointEntity);
        list.add(standardPointEntity2);
        when(standardPointCacheManager.getStandardByIsExpand(any(),any())).thenReturn(list);

        when(jsonService.jsonToObject(any(), any())).thenReturn(new HashMap<>());
        batteryCellServiceImpl.getBattCellData(battCellQueryBean,null,"");
    }

    @Test
    public void getBattCellData1() throws UedmException
    {
        batteryCellServiceImpl.getBattCellData(new BattCellQueryBean(),null,"");
        List<String> smpIds = new ArrayList<>();
        smpIds.add("1");
        BattCellQueryBean battCellQueryBean = new BattCellQueryBean();
        battCellQueryBean.setSmpIds(smpIds);
        battCellQueryBean.setBattId("1");
        Map<String, Map<String, String>> map1 = getStringMapMap();
        when(dataService.getRealtimeDataByIdAndType(any(), any())).thenReturn(map1);
        when(jsonService.jsonToObject(any(), any())).thenReturn(new HashMap<>());
        List<BattCellVO> battCellData = batteryCellServiceImpl.getBattCellData(battCellQueryBean, null, "");
        Assert.assertEquals(1,battCellData.size());
    }

    /* Started by AICoder, pid:tf1c89f673d7e6b14fec0914d038d90c6ec9fbc9 */
    @NotNull
    private Map<String, Map<String, String>> getStringMapMap() {
        Map<String, Map<String, String>> map1 = new HashMap<>();
        Map<String, String> map2 = new HashMap<>();
        map2.put("value", "1");
        map1.put("1", map2);
        map1.put("2", null);
        return map1;
    }
    /* Ended by AICoder, pid:tf1c89f673d7e6b14fec0914d038d90c6ec9fbc9 */

    @Test
    public void getExpandStandardDataByTypeTest() throws com.zte.uedm.basis.exception.UedmException {
        doThrow(com.zte.uedm.basis.exception.UedmException.class).when(standardPointCacheManager).getStandardByIsExpand(any(), any());

        try {
            batteryCellServiceImpl.getExpandStandardDataByType("1", "1");
        } catch (Exception e) {

        }
    }

    @Test
    public void getCellAlarmcodemap() throws UedmException
    {
        when(jsonService.jsonToObject(any(), any())).thenThrow(UedmException.class);
        batteryCellServiceImpl.getCellAlarmcodemap();

        String smpId1 = "1";
        Map<Long, Integer> alarmByOidMap = new HashMap<>();
        Map<String, Integer> alarmLevelMap = new HashMap<>();
        Map<String, List<Long>> cellAlarmCodemap = new HashMap<>();
        List<Long> longs = new ArrayList<>();
        longs.add(1L);
        cellAlarmCodemap.put("1",longs);
        alarmByOidMap.put(1L,1);
        batteryCellServiceImpl.dealAlarmLevel(smpId1,alarmByOidMap,alarmLevelMap,cellAlarmCodemap);
        Assert.assertEquals(1,longs.size());
    }


    @Test
    public void exportBattCellData() throws UedmException
    {
        List<String> smpIds = new ArrayList<>();
        smpIds.add("1");
        BattCellQueryBean battCellQueryBean = new BattCellQueryBean();
        battCellQueryBean.setSmpIds(smpIds);
        battCellQueryBean.setBattId("1");
        RealtimeDataBean realtimeDataBean = new RealtimeDataBean();
        AiBean aiBean = new AiBean();
        aiBean.setSmpId("1");
        List<AiBean> aiBeans = new ArrayList<>();
        aiBeans.add(aiBean);
        realtimeDataBean.setAiList(aiBeans);
        Map<String, Map<String, String>> map1 = getStringMapMap();
        when(dataService.getRealtimeDataByIdAndType(any(), any())).thenReturn(map1);
        when(jsonService.jsonToObject(any(), any())).thenReturn(new HashMap<>());

        batteryCellServiceImpl.exportBattCellData(battCellQueryBean,null,null,"");
        Assert.assertEquals(1,aiBeans.size());
    }

    @Test
    public void testClose() {
        FileExportWriter wb = Mockito.mock(FileExportWriter.class);

        try {
            Mockito.doThrow(IOException.class).when(wb).closeFile();
            batteryCellServiceImpl.closeFile(wb);
        } catch (IOException e) {
            assertEquals("", e.getMessage());
        }
    }

    @Test
    public void exportBattCellData2() throws UedmException
    {
        List<String> smpIds = new ArrayList<>();
        smpIds.add("1");
        BattCellQueryBean battCellQueryBean = new BattCellQueryBean();
        battCellQueryBean.setSmpIds(smpIds);
        battCellQueryBean.setBattId("1");
        RealtimeDataBean realtimeDataBean = new RealtimeDataBean();
        AiBean aiBean = new AiBean();
        aiBean.setSmpId("1");
        List<AiBean> aiBeans = new ArrayList<>();
        aiBeans.add(aiBean);
        realtimeDataBean.setAiList(aiBeans);
        List<ImageBean> imageBeans = new ArrayList<>();
        ImageBean imageBean = new ImageBean();
        imageBean.setXLine(1);
        imageBean.setImageName("xgsg");
        imageBean.setYLine(2);
        imageBean.setBase64Str("gsdgsdgsgwsg");
        imageBean.setDim("bbbb");
        imageBeans.add(imageBean);
        battCellQueryBean.setImages(imageBeans);
        Map<String, Map<String, String>> map1 = getStringMapMap();
        when(dataService.getRealtimeDataByIdAndType(any(), any())).thenReturn(map1);
        when(jsonService.jsonToObject(any(), any())).thenReturn(new HashMap<>());

        FileExportWriter fw = new ExcelFileWriter();
        when(wf.getWriter(any())).thenReturn(fw);
        try {

            batteryCellServiceImpl.exportBattCellData(battCellQueryBean,null,null,"");
        }catch (UedmException e){
            Assert.assertEquals(""+e.getErrorId(),"-1");
        }
    }

}
