package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.BattTestTaskDeviceBean;
import com.zte.uedm.battery.bean.BattTestTaskPeriodBean;
import com.zte.uedm.battery.controller.batterytest.vo.BattTestCheckBeforeVo;
import com.zte.uedm.battery.domain.BattTestTaskDeviceDomain;
import com.zte.uedm.battery.domain.BattTestTaskDomain;
import com.zte.uedm.battery.domain.BatteryTestDomain;
import com.zte.uedm.battery.enums.BattTestTaskStatusEnums;
import com.zte.uedm.battery.service.BatteryTemporaryTestService;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.Mockito.when;

public class BattPeriodTestServiceImplTest
{
    @InjectMocks
    private BattPeriodTestServiceImpl battPeriodTestService;
    @Mock
    private BattTestTaskDomain battTestTaskDomain;
    @Mock
    private BattTestTaskDeviceDomain battTestTaskDeviceDomain;
    @Mock
    private BatteryTestDomain batteryTestDomain;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private JsonService jsonService;
    @Mock
    private RedisService redisService;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private CollectorCacheManager collectorCacheManager;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private MsgSenderService msgSenderService;
    @Mock
    private BatteryTemporaryTestService batteryTemporaryTestService;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        Map<String, List<String>> map = new HashMap<>();
        map.put("collectorId", Arrays.asList("sp-2", "sp-3"));
        Mockito.doReturn(map).when(resourceCollectorRelationCacheManager).queryCollectorIdWithResourceMap(Mockito.any());
        BattTestCheckBeforeVo battTestCheckBeforeVo = new BattTestCheckBeforeVo();
        battTestCheckBeforeVo.setId("sp-2");
        battTestCheckBeforeVo.setResult(true);
        BattTestCheckBeforeVo battTestCheckBeforeVo1 = new BattTestCheckBeforeVo();
        battTestCheckBeforeVo1.setId("sp-3");
        battTestCheckBeforeVo1.setResult(false);
        Mockito.doReturn(Arrays.asList(battTestCheckBeforeVo, battTestCheckBeforeVo1)).when(batteryTemporaryTestService).checkBatterySoc(Mockito.any(), Mockito.any());
    }

    @Test
    public void battPeriodTest_normal() throws UedmException
    {
        try
        {
            List<BattTestTaskPeriodBean> tasks = new ArrayList<>();
            BattTestTaskPeriodBean taskPeriodBean = new BattTestTaskPeriodBean();
            taskPeriodBean.setPeriod(1);
            taskPeriodBean.setId("task-1");
            taskPeriodBean.setStartTime("2022-08-14 11:00:00");
            taskPeriodBean.setStatus(BattTestTaskStatusEnums.RUNNING.getId());
            BattTestTaskPeriodBean taskPeriodBean1 = new BattTestTaskPeriodBean();
            taskPeriodBean1.setPeriod(2);
            taskPeriodBean1.setId("task-2");
            taskPeriodBean1.setStartTime("2022-08-14 11:00:00");
            taskPeriodBean1.setStatus(BattTestTaskStatusEnums.RUNNING.getId());
            BattTestTaskPeriodBean taskPeriodBean2 = new BattTestTaskPeriodBean();
            taskPeriodBean2.setPeriod(2);
            taskPeriodBean2.setId("task-3");
            taskPeriodBean2.setStartTime("2022-08-14 11:00:00");
            taskPeriodBean2.setStatus(BattTestTaskStatusEnums.RUNNING.getId());
            tasks.add(taskPeriodBean);
            tasks.add(taskPeriodBean1);
            tasks.add(taskPeriodBean2);
            Mockito.doReturn(tasks).when(battTestTaskDomain).selectTaskByStatus(Mockito.anyString());

            List<BattTestTaskDeviceBean> testTaskDeviceBeans = new ArrayList<>();
            BattTestTaskDeviceBean taskDeviceBean = new BattTestTaskDeviceBean();
            taskDeviceBean.setTaskId("task-1");
            taskDeviceBean.setId("sp-1");
            BattTestTaskDeviceBean taskDeviceBean1 = new BattTestTaskDeviceBean();
            taskDeviceBean1.setTaskId("task-2");
            taskDeviceBean1.setId("sp-2");
            BattTestTaskDeviceBean taskDeviceBean2 = new BattTestTaskDeviceBean();
            taskDeviceBean2.setTaskId("task-3");
            taskDeviceBean2.setId("sp-3");
            testTaskDeviceBeans.add(taskDeviceBean);
            testTaskDeviceBeans.add(taskDeviceBean1);
            testTaskDeviceBeans.add(taskDeviceBean2);
            Mockito.doReturn(testTaskDeviceBeans).when(battTestTaskDeviceDomain).selectTaskByIds(Mockito.any());

            Mockito.doReturn("2022-08-16 11:04:10").when(dateTimeService).getCurrentTime();

            Map<Integer, String> result = new HashMap<>();
            result.put(0, "success");
            Mockito.doReturn(result).when(batteryTestDomain).startBatteryTest(Mockito.any(), Mockito.any());
            battPeriodTestService.battPeriodTest("auto");
        }
        catch (Exception e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void battPeriodTest_Empty() throws UedmException
    {
        try
        {
            Mockito.doReturn(new ArrayList<>()).when(battTestTaskDomain).selectTaskByStatus(Mockito.anyString());
            battPeriodTestService.battPeriodTest("auto");
        }
        catch (Exception e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void battPeriodTest_Ex1()
    {
        try
        {
            List<BattTestTaskPeriodBean> tasks = new ArrayList<>();
            BattTestTaskPeriodBean taskPeriodBean = new BattTestTaskPeriodBean();
            taskPeriodBean.setPeriod(1);
            taskPeriodBean.setId("task-1");
            taskPeriodBean.setStartTime("2022-08-14 11:00:00");
            taskPeriodBean.setStatus(BattTestTaskStatusEnums.RUNNING.getId());
            BattTestTaskPeriodBean taskPeriodBean1 = new BattTestTaskPeriodBean();
            taskPeriodBean1.setPeriod(2);
            taskPeriodBean1.setId("task-2");
            taskPeriodBean1.setStartTime("2022-08-14 11:00:00");
            taskPeriodBean1.setStatus(BattTestTaskStatusEnums.RUNNING.getId());
            tasks.add(taskPeriodBean);
            tasks.add(taskPeriodBean1);
            Mockito.doReturn(tasks).when(battTestTaskDomain).selectTaskByStatus(Mockito.anyString());

            List<BattTestTaskDeviceBean> testTaskDeviceBeans = new ArrayList<>();
            BattTestTaskDeviceBean taskDeviceBean = new BattTestTaskDeviceBean();
            taskDeviceBean.setTaskId("task-1");
            taskDeviceBean.setId("sp-1");
            BattTestTaskDeviceBean taskDeviceBean1 = new BattTestTaskDeviceBean();
            taskDeviceBean1.setTaskId("task-2");
            taskDeviceBean1.setId("sp-2");
            testTaskDeviceBeans.add(taskDeviceBean);
            testTaskDeviceBeans.add(taskDeviceBean1);
            Mockito.doReturn(testTaskDeviceBeans).when(battTestTaskDeviceDomain).selectTaskByIds(Mockito.any());

            Mockito.doReturn("2022-08-16 11:03:10").when(dateTimeService).getCurrentTime();

            Map<Integer, String> result = new HashMap<>();
            result.put(0, "success");
            result.put(-205, "error");
            result.put(-206, "error");
            result.put(-207, "error");
            when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(Arrays.asList("sp-1"));
            Mockito.doReturn(result).when(batteryTestDomain).startBatteryTest(Mockito.any(), Mockito.any());
            battPeriodTestService.battPeriodTest("auto");
        }
        catch (Exception e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

}
