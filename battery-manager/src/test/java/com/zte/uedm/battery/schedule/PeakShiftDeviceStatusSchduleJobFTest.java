package com.zte.uedm.battery.schedule;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.rpc.ConfigurationRpcFake;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.service.DateTimeService;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


public class PeakShiftDeviceStatusSchduleJobFTest {

    @InjectMocks
    private PeakShiftDeviceStatusSchduleJob peakShiftDeviceStatusSchduleJob;

    @Mock
    private GridStrategyMapper gridStrategyMapper;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Resource
    private ConfigurationRpcFake configurationRpcs = new ConfigurationRpcFake();
    @Resource
    private DateTimeService dateTimeService = new DateTimeService();


    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    @SneakyThrows
    @Test
    public void UEDM_284179_given_存在错峰设备缓存信息_when_调用批量更新错峰设备运行状态方法_then_更新成功() {
        List<DevicePeakCacheInfoBean> beanList = new ArrayList<>();
        DevicePeakCacheInfoBean cacheInfoBean = new DevicePeakCacheInfoBean();
        cacheInfoBean.setDeviceId("123");
        cacheInfoBean.setPosition("test");
        beanList.add(cacheInfoBean);
        Mockito.when(configurationManagerRpc.queryAllList()).thenReturn(beanList);
        peakShiftDeviceStatusSchduleJob.execute();
        Mockito.verify(gridStrategyMapper).batchInsert(Mockito.any());
    }

}
