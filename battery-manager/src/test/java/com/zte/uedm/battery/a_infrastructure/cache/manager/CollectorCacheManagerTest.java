package com.zte.uedm.battery.a_infrastructure.cache.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.cache.provider.CollectorCacheDataProvider;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.caffeine.service.impl.CacheBaseManager;
import com.zte.uedm.service.config.optional.GlobalOptional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CollectorCacheManagerTest {

    @InjectMocks
    private CollectorCacheManager collectorCacheManager;

    @Mock
    private CollectorCacheDataProvider cacheDataProvider;

    @Mock
    private CommonCacheService cacheService;

    private static final ConcurrentMap<String, List<CollectorEntity>> map = new ConcurrentHashMap<>();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void mockParam() {
        map.clear();
        CollectorEntity collectorEntity = new CollectorEntity();
        collectorEntity.setId("id");
        collectorEntity.setPathId(new String[]{"id"});
        CollectorEntity collectorEntity1 = new CollectorEntity();
        collectorEntity1.setId("id");
        collectorEntity1.setPathId(new String[]{"id"});
        List<CollectorEntity> collectorEntityList = new ArrayList<>();
        collectorEntityList.add(collectorEntity);
        collectorEntityList.add(collectorEntity1);
        map.put("test", collectorEntityList);
        map.put("test1", collectorEntityList);
        Cache<Object, Object> cache = mock(Cache.class);
        doReturn(map).when(cache).asMap();
        doReturn(cache).when(cacheService).getAllCache(any());
        doReturn(false).when(cacheService).isKeyExist(any(), any());
        doReturn(collectorEntityList).when(cacheService).getCache(any(), any(), any());
    }

    @Test
    public void testGetCacheName() {
        String cacheName = collectorCacheManager.getCacheName();
        assertEquals("CACHE_NAME_COLLECTOR_INSTANCE", cacheName);
    }

    @Test
    public void testGetCacheDataProvider() {
        CacheDataProvider cacheDataProvider = collectorCacheManager.getCacheDataProvider();
        assertEquals(this.cacheDataProvider, cacheDataProvider);
    }

    @Test
    public void getAllCollectorId(){
        mockParam();
        List<String> allCollectorId = collectorCacheManager.getAllCollectorId();
        Assert.assertNotNull(allCollectorId);
    }

    @Test
    public void testInit() throws UedmException {
        Set<String> init = collectorCacheManager.init();
        when(cacheDataProvider.getCacheDataForCacheProvider(new HashSet<>())).thenThrow(new RuntimeException());
        collectorCacheManager.init();
        Assert.assertNotNull(init);

    }

    @Test
    public void getCollectorById() throws UedmException {
        mockParam();
        List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(new ArrayList<>());
        Assert.assertNotNull(collectorById);
    }

    @Test
    public void getCollectorByIdExp() throws UedmException {
        List<String> arrayList = new ArrayList<>();
        arrayList.add("123");
        when(cacheService.getCache(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new RuntimeException());
        List<CollectorEntity> collectorById = collectorCacheManager.getCollectorById(arrayList);
    }

    @Test
    public void getAllCollectorTest() throws UedmException {
        mockParam();
        List<CollectorEntity> collectorById = collectorCacheManager.getAllCollector();
        Assert.assertNotNull(collectorById);
    }

    @Test
    public void getAllCollectorTestExp() throws UedmException {
        when(cacheService.getAllCache(Mockito.any())).thenThrow(new RuntimeException());
        List<CollectorEntity> collectorById = collectorCacheManager.getAllCollector();
    }

    @Test
    public void testGetResourceBeansInPathWithNullCollectorEntity() {
        collectorCacheManager.getResourceBeansInPath(Collections.singleton("id"), null);
        Map<String, ResourceBaseEntity> map = new HashMap<>();
        map.put("sitId", null);
        collectorCacheManager.getResourceBeansInPath(Collections.singleton("id"), map);
        CollectorEntity collectorEntity = new CollectorEntity();
        collectorEntity.setId("id");
        collectorEntity.setPathId(new String[]{"id", "sitId"});
        List<BaseCacheBean> list = new ArrayList<>();
        list.add(collectorEntity);
        when(cacheService.getCache(any(), any(), any())).thenReturn(list);
        collectorCacheManager.getResourceBeansInPath(Collections.singleton("id"), map);
    }

    @Test
    public void getCollectorsType() {
        collectorCacheManager.getCollectorsType(null);
        collectorCacheManager.getCollectorsType(Collections.singleton("id"));

        CollectorEntity collectorEntity = new CollectorEntity();
        collectorEntity.setId("id");
        collectorEntity.setMoc("moc");
        collectorEntity.setProtocolAttribute(new HashMap<>());
        List<BaseCacheBean> list = new ArrayList<>();
        list.add(collectorEntity);
        when(cacheService.getCache(any(), any(), any())).thenReturn(list);
        collectorCacheManager.getCollectorsType(Collections.singleton("id"));
    }

    /* Started by AICoder, pid:i799dh059cl01491486d08d7208b781654f37c06 */
    @Test
    public void selectCollectorByIds() throws UedmException {
        Set<String> set = new HashSet<>();
        set.add("1");
        List<CollectorEntity> list = collectorCacheManager.selectCollectorByIds(set);
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void selectCollectorByIds1() throws UedmException {
        List<CollectorEntity> list = collectorCacheManager.selectCollectorByIds(null);
        Assert.assertEquals(0, list.size());
    }
    /* Ended by AICoder, pid:i799dh059cl01491486d08d7208b781654f37c06 */

    /* Started by AICoder, pid:qa1c6fee15l64e81477c0a606020393331d0e29f */
    @Test
    public void selectByIdsAndAdapterIds() throws UedmException {
        CollectorEntity collectorEntity = new CollectorEntity();
        collectorEntity.setId("id");
        collectorEntity.setMoc("moc");
        collectorEntity.setProtocolAttribute(new HashMap<>());
        collectorEntity.setAdapterId("adapterId");
        List<BaseCacheBean> list = new ArrayList<>();
        list.add(collectorEntity);
        when(cacheService.getCache(any(), any(), any())).thenReturn(list);
        collectorCacheManager.selectByIdsAndAdapterIds(Collections.singletonList("id"), Collections.singletonList("adapterId"));
    }

    @Test
    public void getCollectorBypathId() throws UedmException {
        mockParam();
        collectorCacheManager.getCollectorBypathId("id");

        // Testing exception handling
        when(cacheService.getAllCache(Mockito.any())).thenThrow(new RuntimeException());
        collectorCacheManager.getCollectorBypathId("id");
    }
    /* Ended by AICoder, pid:qa1c6fee15l64e81477c0a606020393331d0e29f */

    /* Started by AICoder, pid:h6357rd9b2e592714272085bb007b21a07093379 */
    @Test
    public void getCollectorIdsByPathAndType() throws UedmException {
        mockParam();
        collectorCacheManager.getCollectorIdsByPathAndType(new HashSet<>());

        when(cacheService.getAllCache(Mockito.any())).thenThrow(new RuntimeException());
        collectorCacheManager.getCollectorIdsByPathAndType(new HashSet<>());
    }
    /* Ended by AICoder, pid:h6357rd9b2e592714272085bb007b21a07093379 */

    @Test
    public void getCollectorsByType() throws UedmException {
        mockParam();
        collectorCacheManager.getCollectorsByType(new HashSet<>());

        when(cacheService.getAllCache(Mockito.any())).thenThrow(new RuntimeException());
        collectorCacheManager.getCollectorsByType(new HashSet<>());
    }

}