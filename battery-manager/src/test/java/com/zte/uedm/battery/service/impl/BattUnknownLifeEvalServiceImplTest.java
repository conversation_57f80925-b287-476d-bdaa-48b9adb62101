package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BatteryHisDataDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.battlife.BattLifeAIConfigService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

public class BattUnknownLifeEvalServiceImplTest
{

    @InjectMocks
    private BattUnknownLifeEvalServiceImpl battUnknownLifeEvalService;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;


    @Mock
    protected BatteryHisDataDomain batteryHisDataDomain;

    @Mock
    protected BattLifeEvalDomain battLifeEvalDomain;

    @Mock
    protected BattLifeAIConfigService battLifeAIConfigService;

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void dispatchHandleTest_empty() throws UedmException {
        Map<String, BattTypeEnum> battTypeEnumMap = new HashMap<>();
        battTypeEnumMap.put("Batt1", BattTypeEnum.UNKNOWN);
        List<MoObjectConfiguration> moObjectConfigurationList1 = new ArrayList<>();
        battUnknownLifeEvalService.dispatchHandle(moObjectConfigurationList1, battTypeEnumMap);
        Assert.assertEquals(0,moObjectConfigurationList1.size());
    }

    @Test
    public void dispatchHandleTest() throws UedmException {
        List<MoObjectConfiguration> moObjectConfigurationList = new ArrayList<>();
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("Batt1");
        moObjectConfigurationList.add(moObjectConfiguration);
        Map<String, BattTypeEnum> battTypeEnumMap         = new HashMap<>();
        battTypeEnumMap.put("Batt1", BattTypeEnum.UNKNOWN);

        Mockito.doReturn(new ArrayList<>()).when(batteryHisDataDomain).getAllStandardPointBean();
        Mockito.doReturn(new ArrayList<>()).when(configurationManagerRpcImpl).getStandardPointByIds(any(), any());

        Mockito.doReturn(1).when(battLifeEvalDomain).getBattRunDays(any(), any());
//        Mockito.doReturn(1).when(battLifeEvalDomain).recordEvalByDay(any());
        Mockito.doReturn(1).when(battLifeEvalDomain).recordEvalByMonth(any());

        battUnknownLifeEvalService.dispatchHandle(moObjectConfigurationList, battTypeEnumMap);
        Assert.assertEquals(1,moObjectConfigurationList.size());
    }

}
