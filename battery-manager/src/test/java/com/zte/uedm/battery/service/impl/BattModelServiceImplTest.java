package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;

public class BattModelServiceImplTest
{

    @InjectMocks
    private BattModelServiceImpl battModelService;
    @Mock
    private BattAssetDomain battAssetDomain;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void searchTest_normal() throws Exception
    {
        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battAssetDomain).searchModelInfos(Mockito.any(), Mockito.any());
        Assert.assertSame(0l, battModelService.search("name", new ServiceBaseInfoBean("","")).getTotal());
    }
}
