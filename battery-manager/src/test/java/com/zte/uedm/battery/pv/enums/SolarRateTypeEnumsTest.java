package com.zte.uedm.battery.pv.enums;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.io.IOException;



public class SolarRateTypeEnumsTest {

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getType() {
        Assert.assertSame(SolarRateTypeEnums.TOU.getType(), 1);
    }

    @Test
    public void getName() {
        Assert.assertSame(SolarRateTypeEnums.TOU.getName(), "{\"en_US\":\"TOU\",\"zh_CN\":\"峰谷\"}");
    }

    @Test
    public void getNameByType_null() {
        Assert.assertSame(SolarRateTypeEnums.getNameByType(null),"{\"en_US\":\"None\",\"zh_CN\":\"无\"}");
    }

    @Test
    public void getNameByType() {
        Assert.assertSame(SolarRateTypeEnums.getNameByType(SolarRateTypeEnums.TOU.getType()),"{\"en_US\":\"TOU\",\"zh_CN\":\"峰谷\"}");
    }
}