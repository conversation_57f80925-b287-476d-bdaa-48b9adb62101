package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.pv.service.SolarRevenueStatisticsService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

public class StopPeakShiftingStrategyJobTest {
    @InjectMocks
    private StopPeakShiftingStrategyJob stopPeakShiftingStrategyJob;
    

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void executeTest() {
        stopPeakShiftingStrategyJob.execute();
    }
}
