package com.zte.uedm.battery.enums.batttest;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class AcInputStatusEnumsTest {

    @Test
    public void test(){
        AcInputStatusEnums.NORMAL.getId();
        AcInputStatusEnums.NORMAL.getName();
        AcInputStatusEnums.ABNORMAL.getId();
        AcInputStatusEnums.ABNORMAL.getName();

        String name = AcInputStatusEnums.NORMAL.getName();
        Assert.assertEquals("{\"zh_CN\":\"正常\",\"en_US\":\"Normal\"}",name);

        List<Pair<String, String>> allIdName = AcInputStatusEnums.getAllIdName();
        List<String> list = AcInputStatusEnums.getAllId();
        Assert.assertEquals(2,list.size());
        Assert.assertEquals(2,allIdName.size());
    }

}
