package com.zte.uedm.battery.pv.service.impl;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

public class AbstractSolarRevenueStatisticsStrategyTest {

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void calcSolarRevenueHours() {
    }

    @Test
    public void calcHistoryAiBeanIncValue() {
    }

    @Test
    public void validHistoryAiBean() {
    }

    @Test
    public void getStatisticsCode() {
    }

    @Test
    public void supplementSolarRevenueBean() {
    }

    @Test
    public void solarGridScopeStrategyMatch() {
    }

    @Test
    public void calcTotalPowerGenerationPerHour() {
    }
}