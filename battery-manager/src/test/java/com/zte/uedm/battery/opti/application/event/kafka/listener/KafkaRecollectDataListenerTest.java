package com.zte.uedm.battery.opti.application.event.kafka.listener;

import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.pv.service.SolarRevenueStatisticsService;
import com.zte.uedm.battery.service.PeakShiftService;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.service.config.optional.MocOptional;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

public class KafkaRecollectDataListenerTest
{
    @InjectMocks
    private kafkaRecollectDataListener kafkaRecollectDataListener;
    @Mock
    private JsonService jsonService;

    @Mock
    private SolarRevenueStatisticsService solarRevenueStatisticsService;

    @Mock
    private PeakShiftService peakShiftService;

    @Mock
    private ResourceBaseCacheManager resourceBaseCacheManager;

    @Before
    public void setup()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void onMsgTest()
    {
        kafkaRecollectDataListener.onMsg("");
    }

    @Test
    public void onMsgTestPv() throws UedmException
    {
        Map<String, List<String>> map = new HashMap<>();
        map.put("pvId", Arrays.asList("2023-09-09"));
        Mockito.when(jsonService.jsonToObject(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        ResourceBaseEntity entity = new ResourceBaseEntity();
        entity.setMoc(MocOptional.PV.getId());
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(entity);
        kafkaRecollectDataListener.onMsg("");
    }

    @Test
    public void onMsgTestPv1() throws UedmException
    {
        Map<String, List<String>> map = new HashMap<>();
        map.put("pvId", Arrays.asList("2023-09-09"));
        Mockito.when(jsonService.jsonToObject(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(null);
        kafkaRecollectDataListener.onMsg("");
    }

    @Test
    public void onMsgTestPv2() throws UedmException
    {
        Map<String, List<String>> map = new HashMap<>();
        map.put("pvId", Arrays.asList("2023-09-09"));
        Mockito.when(jsonService.jsonToObject(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        ResourceBaseEntity entity = new ResourceBaseEntity();
        entity.setMoc(MocOptional.PV.getId());
        entity.setId("pvId");
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(entity);
        kafkaRecollectDataListener.onMsg("");
    }

    @Test
    public void onMsgTestBatt() throws UedmException
    {
        Map<String, List<String>> map = new HashMap<>();
        map.put("battId", Arrays.asList("2024-12-11"));
        Mockito.when(jsonService.jsonToObject(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        ResourceBaseEntity entity = new ResourceBaseEntity();
        entity.setMoc(MocOptional.BATTERY.getId());
        entity.setId("battId");
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(entity);
        kafkaRecollectDataListener.onMsg("");
    }

    @Test
    public void onMsgTest_exc() throws UedmException
    {
        Mockito.when(jsonService.jsonToObject(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new UedmException(-1,"1"));
        kafkaRecollectDataListener.onMsg("");
    }

    /* Started by AICoder, pid:o0ff7bcd9cye14814b6309e59028032da0e9349d */
    @Test
    public void testReCollectBatteryValue_Success() {
        // 准备测试数据
        Map<String, List<String>> recollectData = new HashMap<>();
        // 假设添加了一些值
        // recollectData.put("key", Arrays.asList("value1", "value2"));

        // 调用方法
        kafkaRecollectDataListener.reCollectBatteryValue(recollectData);

        // 验证peakShiftService的reStatisticsCSU6方法被调用
        verify(peakShiftService, times(1)).reStatisticsCSU6(recollectData);
    }

    @Test
    public void testReCollectBatteryValue_Exception() {
        // 准备测试数据
        Map<String, List<String>> recollectData = new HashMap<>();
        // 添加直接触发异常的行为
        doThrow(new RuntimeException("Test exception")).when(peakShiftService).reStatisticsCSU6(recollectData);

        // 执行方法
        kafkaRecollectDataListener.reCollectBatteryValue(recollectData);

        // 验证peakShiftService的reStatisticsCSU6方法被调用
        verify(peakShiftService, times(1)).reStatisticsCSU6(recollectData);

        // 此处可以通过mock库来验证日志是否记录了错误，这里不涉及具体实现
    }
    /* Ended by AICoder, pid:o0ff7bcd9cye14814b6309e59028032da0e9349d */
}
