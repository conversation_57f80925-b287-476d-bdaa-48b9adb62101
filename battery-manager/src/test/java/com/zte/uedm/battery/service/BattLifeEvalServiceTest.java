package com.zte.uedm.battery.service;

import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.service.battlife.impl.BattLifeAIModeServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

public class BattLifeEvalServiceTest {

    @InjectMocks
    private BattLifeEvalService battLifeEvalService=new BattLifeAIModeServiceImpl();

    @Mock
    private DataRedis dataRedis;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getStandardPointValue() {
        battLifeEvalService.getStandardPointValue(null, null, null);
    }
}