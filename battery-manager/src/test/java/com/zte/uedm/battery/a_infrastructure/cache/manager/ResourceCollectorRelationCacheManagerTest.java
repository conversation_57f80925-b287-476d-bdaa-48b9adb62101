package com.zte.uedm.battery.a_infrastructure.cache.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.MocEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_domain.cache.provider.ResourceCollectorRelationCacheDataProvider;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.configuration.opt.relation.objectdevice.entity.MonitorDeviceObjectRelationEntity;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.caffeine.service.impl.CacheBaseManager;
import com.zte.uedm.service.config.optional.MocOptional;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class ResourceCollectorRelationCacheManagerTest {

    @InjectMocks
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private ResourceCollectorRelationCacheDataProvider cacheDataProvider;
    @Mock
    private DeviceCacheManager deviceCacheManager;

    @Mock
    private CommonCacheService cacheService;
    @Mock
    private CacheBaseManager cacheBaseManager;
    private static final Map<String, List<ResourceCollectorRelationEntity>> map = new ConcurrentHashMap<>();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(resourceCollectorRelationCacheManager, "rCollectorRelationCacheDataProvider", cacheDataProvider);
    }

    private void mockParam() {
        map.clear();
        ResourceCollectorRelationEntity resourceCollectorRelationEntity = new ResourceCollectorRelationEntity();
        resourceCollectorRelationEntity.setResourceId("id");
        resourceCollectorRelationEntity.setCollectorId("id");
        ResourceCollectorRelationEntity resourceCollectorRelationEntity1 = new ResourceCollectorRelationEntity();
        resourceCollectorRelationEntity1.setResourceId("id");
        resourceCollectorRelationEntity1.setCollectorId("id2");
        ResourceCollectorRelationEntity resourceCollectorRelationEntity2 = new ResourceCollectorRelationEntity();
        resourceCollectorRelationEntity2.setResourceId("id1");
        resourceCollectorRelationEntity2.setCollectorId("id2");
        List<ResourceCollectorRelationEntity> resourceCollectorRelationEntities = new ArrayList<>();
        resourceCollectorRelationEntities.add(resourceCollectorRelationEntity);
        resourceCollectorRelationEntities.add(resourceCollectorRelationEntity1);
        resourceCollectorRelationEntities.add(resourceCollectorRelationEntity2);
        map.put("test", resourceCollectorRelationEntities);
        map.put("test1", resourceCollectorRelationEntities);
        Cache<Object, Object> cache = mock(Cache.class);
        doReturn(map).when(cache).asMap();
        doReturn(cache).when(cacheService).getAllCache(any());
        doReturn(false).when(cacheService).isKeyExist(any(), any());
        doReturn(resourceCollectorRelationEntities).when(cacheService).getCache(any(), any(), any());
    }

    @Test
    public void getCacheNameTest() {
        String result = resourceCollectorRelationCacheManager.getCacheName();
        assertEquals("CACHE_NAME_RESOURCE_COLLECTOR_RELATION_INSTANCE", result);
    }

    @Test
    public void getCacheDataProviderTest() {
        CacheDataProvider result = resourceCollectorRelationCacheManager.getCacheDataProvider();
        Assert.assertNotNull(result);
    }

    @Test
    public void getIsMultiTest() {
        boolean result = resourceCollectorRelationCacheManager.getIsMulti();
        Assert.assertTrue(result);
    }

    @Test
    public void getCollectorIdByDeviceIdTest() throws UedmException {
        mockParam();
        Set<String> collectorIdByDeviceId = resourceCollectorRelationCacheManager.getCollectorIdByResourceId("a");
        Assert.assertNotNull(collectorIdByDeviceId);
    }
    @Test
    public void getCollectorIdByDeviceIdTest_null() throws UedmException {
        map.clear();
        map.put("test1", new ArrayList<>());
        Cache<Object, Object> cache = mock(Cache.class);
        doReturn(map).when(cache).asMap();
        doReturn(cache).when(cacheService).getAllCache(any());
        Set<String> collectorIdByDeviceId = resourceCollectorRelationCacheManager.getCollectorIdByResourceId("a");
        Assert.assertNotNull(collectorIdByDeviceId);
    }

    @Test
    public void testInit() throws UedmException {
        Set<String> init = resourceCollectorRelationCacheManager.init();
        when(cacheDataProvider.getCacheDataForCacheProvider(new HashSet<>())).thenThrow(new RuntimeException());
        Set<String> init1 = resourceCollectorRelationCacheManager.init();
        Assert.assertNotNull(init);
    }


    /* Started by AICoder, pid:e23265bc5ca14456a68d39b74862ecae */
    @Test
    public void testGetResourceIdByCollectorIds_Empty() {
        List<String> collectorIds = new ArrayList<>();
        List<String> result = resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collectorIds);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:e23265bc5ca14456a68d39b74862ecae */

    /* Started by AICoder, pid:a3158094f75946ca8343d5d339d490ed */
    @Test
    public void testGetResourceIdByCollectorIds_SingleElement() {
        List<String> collectorIds = Arrays.asList("1");
        // 创建一个模拟的返回值，这里可以根据实际情况进行修改
        List<ResourceCollectorRelationEntity> mockReturnValue = Arrays.asList(new ResourceCollectorRelationEntity());

        // 设置commonCacheService.getCache方法的返回值
        when(cacheService.getCache(anyString(), anyString(), eq(List.class)))
                .thenReturn(mockReturnValue);

        List<String> result = resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collectorIds);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:a3158094f75946ca8343d5d339d490ed */
    @Test
    public void testGetResourceIdByCollectorIds_SingleElement2() {
        List<String> collectorIds = Arrays.asList("id");
        mockParam();
        List<String> result = resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collectorIds);
        assertFalse(result.isEmpty());



    }
    @Test
    public void testGetResourceIdsByCollectorIds_ReturnsEmptySet_WhenNoData() throws UedmException {
        // 设置模拟行为
        mockParam();

        // 调用方法
        Set<String> result = resourceCollectorRelationCacheManager.getResourceIdsByCollectorId("collector123");


        // 验证结果
        assertTrue(!result.isEmpty());
    }
    @Test
    public void testGetResourceIdsByCollectorIds_ReturnsNoEmptySet_WhenData() throws UedmException {
        // 设置模拟行为

        mockParam();

        // 调用方法
        Set<String> result = resourceCollectorRelationCacheManager.getResourceIdsByCollectorId("id");

        // 验证结果
        assertTrue(!result.isEmpty());
    }



    @Test
    public void testGetResourceIdsByCollectorIds_HandlesException() throws UedmException {
        // 设置模拟行为以抛出异常
        when(cacheBaseManager.selectByKey(anyString())).thenThrow(new UedmException(-1, "Database error"));

        // 调用方法
        Set<String> result = resourceCollectorRelationCacheManager.getResourceIdsByCollectorId("collector123");

        // 验证结果
        assertTrue(result.isEmpty());
    }
    @Test
    public void testGetAllEntity(){
        mockParam();
        List<ResourceCollectorRelationEntity> result = resourceCollectorRelationCacheManager.getAllEntity();
        assertFalse(result.isEmpty());
    }
    /* Started by AICoder, pid:t58113f488q09f414c690961d0ddcf1e4ff16a81 */
    @Test
    public void testQueryAllMap_ReturnsCorrectMap_WhenDataExists() throws UedmException {
        // 设置模拟行为
        mockParam();

        // 调用方法
        Map<String, List<ResourceCollectorRelationEntity>> result = resourceCollectorRelationCacheManager.queryCollectorWithResourceMap( Arrays.asList("id"));

        // 验证结果
        assertTrue(!result.isEmpty());
    }
    /* Ended by AICoder, pid:t58113f488q09f414c690961d0ddcf1e4ff16a81 */


    /* Started by AICoder, pid:e1723e9251d1003143ac0bcd200c7b12022970b0 */
    @Test
    public void testGetRelatedDeviceIdbyDeviceIdsAndMoc_ReturnsEmptyMap_WhenNoData() throws UedmException {
        // 设置模拟行为
        mockParam();
        List<DeviceEntity> entityList = new ArrayList<>();
        DeviceEntity entity = new DeviceEntity();
        entity.setId("id1");
        entityList.add(entity);
        DeviceEntity entity1 = new DeviceEntity();
        entity1.setId("id2");
        entityList.add(entity1);
        when(deviceCacheManager.getDevicesByMoc(anyString())).thenReturn(entityList);

        // 调用方法
        Map<String, List<String>> result = resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Arrays.asList("id"), "MOC1");
        Map<String, List<ResourceCollectorRelationEntity>> result1 = resourceCollectorRelationCacheManager.getCollectorRelatedDevice(Collections.singleton("id"), "MOC1");
        resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Arrays.asList("id"), "");
        resourceCollectorRelationCacheManager.getCollectorRelatedDevice(Collections.singleton("id"), "");
        resourceCollectorRelationCacheManager.queryResourceWithCollectorMap(new ArrayList<>());
        resourceCollectorRelationCacheManager.queryCollectorWithResourceMap(new ArrayList<>());
        // 验证结果
        assertTrue(!result.isEmpty());
        assertTrue(!result1.isEmpty());
    }
    /* Ended by AICoder, pid:e1723e9251d1003143ac0bcd200c7b12022970b0 */


    @Test
    public void getSpRelatedBatterylistMap()
    {
        Map<String, List<MonitorObjectBean>> spRelatedBatterylistMap = resourceCollectorRelationCacheManager.getSpRelatedBatterylistMap(null, null);
        Assert.assertEquals(0,spRelatedBatterylistMap.size());
    }

    @SneakyThrows
    @Test
    public void getSpRelatedBatterylistMapTest()
    {
        mockParam();
        DeviceEntity monitorObjectEntity = new DeviceEntity();
        monitorObjectEntity.setId("id");
        monitorObjectEntity.setExattribute("{\"is_loop\": \"2\"}");
        Map<String, List<MonitorObjectBean>> spRelatedBatterylistMap = resourceCollectorRelationCacheManager.getSpRelatedBatterylistMap(Arrays.asList("id"), Arrays.asList(monitorObjectEntity));

        Assert.assertEquals(1,spRelatedBatterylistMap.size());
    }


    @Test
    public void getRelationsByCollectors() throws UedmException {
        resourceCollectorRelationCacheManager.getRelationsByCollectors(null);

        resourceCollectorRelationCacheManager.getRelationsByCollectors(Collections.singleton("id"));
    }

    /* Started by AICoder, pid:u14a3e2e08i3aee14c090ad1608acf2556d5f71e */

    @Test
    public void testGetRelationResourceBeanInfo_NormalCase() throws UedmException {
        mockParam();
        Set<String> collectors = new HashSet<>(Arrays.asList("id1", "id2"));
        String moc = "moc1";

        List<DeviceEntity> deviceEntityList = new ArrayList<>();
        DeviceEntity entity = new DeviceEntity();
        entity.setMoc(MocOptional.BATTERY.getId());
        entity.setId("id1");
        DeviceEntity entity2 = new DeviceEntity();
        entity2.setMoc(MocOptional.BATTERY.getId());
        entity2.setId("id2");
        deviceEntityList.add(entity);
        deviceEntityList.add(entity2);
        when(deviceCacheManager.getDevicesByMoc(moc)).thenReturn(deviceEntityList);
        Set<String> resourceBeanInfo = resourceCollectorRelationCacheManager.getRelationResourceBeanInfo(collectors, moc);
        assertEquals(1, resourceBeanInfo.size());
    }
    /* Ended by AICoder, pid:u14a3e2e08i3aee14c090ad1608acf2556d5f71e */

    /* Started by AICoder, pid:o7451l0691mbbee14bfb0a5930354e24ac69de3b */
    @Test
    public void testQueryResourceMapWithMoc_MatchingDevices() throws UedmException {
        List<String> collectIds = new ArrayList<>();
        collectIds.add("collector1");
        collectIds.add("collector2");
        String moc = "moc1";

        List<DeviceEntity> deviceEntityList = new ArrayList<>();
        DeviceEntity entity = new DeviceEntity();
        entity.setMoc(MocOptional.BATTERY.getId());
        entity.setId("id1");
        DeviceEntity entity2 = new DeviceEntity();
        entity2.setMoc(MocOptional.BATTERY.getId());
        entity2.setId("id2");
        deviceEntityList.add(entity);
        deviceEntityList.add(entity2);
        when(deviceCacheManager.getDevicesByMoc(moc)).thenReturn(deviceEntityList);
        Map<String, List<String>> resourceBeanInfo = resourceCollectorRelationCacheManager.queryResourceMapWithMoc(collectIds, moc);
        assertEquals(2, resourceBeanInfo.size());
    }
    /* Ended by AICoder, pid:o7451l0691mbbee14bfb0a5930354e24ac69de3b */

    /* Started by AICoder, pid:r75b7i7818c49b1146e8083150b82d0c4645fff7 */
    @Test
    public void getAllRelations() throws UedmException {
        List<ResourceCollectorRelationEntity> list = resourceCollectorRelationCacheManager.getAllRelations();
        assertEquals(0, list.size());
    }
    /* Ended by AICoder, pid:r75b7i7818c49b1146e8083150b82d0c4645fff7 */
}
