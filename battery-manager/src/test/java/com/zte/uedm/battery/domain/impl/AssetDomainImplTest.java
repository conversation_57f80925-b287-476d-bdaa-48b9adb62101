package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

public class AssetDomainImplTest
{
    @InjectMocks
    private AssetDomainImpl assetDomain;

    @Mock
    private AssetRpcImpl assetRpcImpl;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectBattTypeByMoIdsTest()
    {
        doReturn(new PageInfo<>(new ArrayList<>())).when(assetRpcImpl).selectBattTypeByMoIds(anyList(), anyString(), Mockito.anyInt(),Mockito.anyInt());
        List<IdNameBean> list= assetDomain.selectBattTypeByMoIds(Arrays.asList("id"));
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void selectBattTypeByMoIdsTest_idEmpty()
    {
        doReturn(new PageInfo<>(new ArrayList<>())).when(assetRpcImpl).selectBattTypeByMoIds(anyList(), anyString(), Mockito.anyInt(),Mockito.anyInt());
        List<IdNameBean> list= assetDomain.selectBattTypeByMoIds(new ArrayList<>());
        Assert.assertEquals(0, list.size());
    }
}
