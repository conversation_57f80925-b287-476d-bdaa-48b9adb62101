package com.zte.uedm.battery.opti.domain.service.impl;

import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.domain.service.CommonConditionPmaDataQueryDomain;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceValueTypeOptional;
import com.zte.uedm.battery.opti.infrastructure.pma.bean.PmaDataBean;
import com.zte.uedm.battery.opti.infrastructure.pma.bean.PmaDataQueryRequest;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.pma.service.PmaService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.*;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class BattRiskCalculatePointDataProviderTest {
    @InjectMocks
    private BattRiskCalculatePointDataProvider battRiskCalculatePointDataProvider;
    @Mock
    private DataRedis dataRedis;
    @Mock
    private PmaService pmaService;
    @Mock
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Mock
    private CommonConditionPmaDataQueryDomain commonConditionPmaDataQueryDomain;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getDataRealTimeNormal_Test() throws Exception
    {
        List<BattRiskRuleSourceEntity> ruleSourceEntities = new ArrayList<>();
        BattRiskRuleSourceEntity battRiskRuleSourceEntity = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity.setMappingId("batt.cells.volt");
        battRiskRuleSourceEntity.setId("batt.cells.volt.id");
        battRiskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity);
        BattRiskRuleSourceEntity battRiskRuleSourceEntity2 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity2.setMappingId("batt.volt");
        battRiskRuleSourceEntity2.setId("batt.volt.id");
        battRiskRuleSourceEntity2.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity2.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity2);
        when(battRiskRuleSourceRepository.selectByIds(Mockito.anyList())).thenReturn(ruleSourceEntities);


        Map<String, Map<String, String>> resultMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value",null);
        resultMap.put("batt.cells.volt",map);
        map = new HashMap<>();
        map.put("value","2");
        resultMap.put("batt.volt",map);
        when(dataRedis.selectRealData(Mockito.anyString())).thenReturn(resultMap);

        ArrayList<String> moids = new ArrayList<>();
        moids.add("batt-1");
        moids.add("batt-2");
        ArrayList<String> parameterIds = new ArrayList<>();
        parameterIds.add("batt.cells.volt");
        parameterIds.add("batt.volt");
        Assert.assertNotNull(battRiskCalculatePointDataProvider.getDataRealTime(moids, parameterIds).isEmpty());


    }
    @Test
    public void getDataRealTimeNoData_Test() throws Exception
    {
        when(dataRedis.selectRealData(Mockito.anyString())).thenReturn(new HashMap<>());
        Assert.assertTrue(battRiskCalculatePointDataProvider.getDataRealTime(new ArrayList<>(), new ArrayList<>()).isEmpty());
    }

    @Test
    public void getDataRangeTimeNormal_Test() throws Exception
    {
        List<PmaDataBean> result = new ArrayList<>();
        PmaDataBean historyAiBean = new PmaDataBean();
        historyAiBean.setResId("Batt-1");
        historyAiBean.setSmpId("volt");
        historyAiBean.setCurrValue("5V");
        historyAiBean.setCurValueTime("2020-11-11 11:11:11");
        result.add(historyAiBean);
        historyAiBean = new PmaDataBean();
        historyAiBean.setResId("Batt-1");
        historyAiBean.setSmpId("volt");
        historyAiBean.setCurrValue("6V");
        historyAiBean.setCurValueTime("2020-11-11 11:11:11");
        result.add(historyAiBean);
        historyAiBean = new PmaDataBean();
        historyAiBean.setResId("Batt-2");
        historyAiBean.setSmpId("temp");
        historyAiBean.setCurrValue("35度");
        historyAiBean.setCurValueTime("2020-11-11 11:11:11");
        result.add(historyAiBean);
        historyAiBean = new PmaDataBean();
        historyAiBean.setResId("Batt-2");
        historyAiBean.setSmpId("curr");
        historyAiBean.setCurrValue(null);
        historyAiBean.setCurValueTime("2020-11-11 11:11:11");
        result.add(historyAiBean);



        when(commonConditionPmaDataQueryDomain.queryPmaDataBatch(Mockito.any())).thenReturn(result);

        List<BattRiskRuleSourceEntity> ruleSourceEntities = new ArrayList<>();
        BattRiskRuleSourceEntity battRiskRuleSourceEntity = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity.setMappingId("volt");
        battRiskRuleSourceEntity.setId("batt.volt.id");
        battRiskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity);
        BattRiskRuleSourceEntity battRiskRuleSourceEntity2 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity2.setMappingId("temp");
        battRiskRuleSourceEntity2.setId("batt.temp.id");
        battRiskRuleSourceEntity2.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity2.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity2);
        BattRiskRuleSourceEntity battRiskRuleSourceEntity3 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity3.setMappingId("curr");
        battRiskRuleSourceEntity3.setId("batt.curr.id");
        battRiskRuleSourceEntity3.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity3.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity3);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity4 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity4.setMappingId("curr");
        battRiskRuleSourceEntity4.setId("batt.curr.id");
        battRiskRuleSourceEntity4.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity4.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity4);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity5 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity5.setMappingId("curr");
        battRiskRuleSourceEntity5.setId("batt.curr.id");
        battRiskRuleSourceEntity5.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity5.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity5);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity6 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity6.setMappingId("curr");
        battRiskRuleSourceEntity6.setId("batt.curr.id");
        battRiskRuleSourceEntity6.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity6.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity6);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity7 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity7.setMappingId("curr");
        battRiskRuleSourceEntity7.setId("batt.curr.id");
        battRiskRuleSourceEntity7.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity7.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity7);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity8 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity8.setMappingId("curr");
        battRiskRuleSourceEntity8.setId("batt.curr.id");
        battRiskRuleSourceEntity8.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity8.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity8);

        BattRiskRuleSourceEntity battRiskRuleSourceEntity9 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity9.setMappingId("curr");
        battRiskRuleSourceEntity9.setId("batt.curr.id");
        battRiskRuleSourceEntity9.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT);
        battRiskRuleSourceEntity9.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity9);

        when(battRiskRuleSourceRepository.selectByIds(Mockito.anyList())).thenReturn(ruleSourceEntities);


        ArrayList<String> moids = new ArrayList<>();
        moids.add("Batt-2");
        moids.add("Batt-1");
        ArrayList<String> parmIds = new ArrayList<>();
        parmIds.add("volt");
        parmIds.add("temp");
        parmIds.add("curr");
        for (int i = 0; i < 9400; i++) {
            moids.add(String.valueOf(i));
        }
        Assert.assertNotNull(battRiskCalculatePointDataProvider.getDataRangeTime(moids, parmIds, Pair.of(new Date(), new Date())).isEmpty());
    }

    @Test
    public void getDataRangeTimeNodata_Test() throws Exception
    {

        when(pmaService.selectDataByCondition(Mockito.anyList()
                ,Mockito.anyList()
                ,Mockito.anyString()
                ,Mockito.anyString()
                ,Mockito.anyString()
                ,Mockito.anyString())).thenReturn(new ArrayList<>());
        ArrayList<String> strings = new ArrayList<>();
        strings.add("A");
        Assert.assertTrue(battRiskCalculatePointDataProvider.getDataRangeTime( new ArrayList<>(), new ArrayList<>(), Pair.of(new Date(), new Date())).isEmpty());
    }
}
