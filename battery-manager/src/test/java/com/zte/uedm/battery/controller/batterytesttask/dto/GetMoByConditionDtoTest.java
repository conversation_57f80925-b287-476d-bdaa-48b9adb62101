package com.zte.uedm.battery.controller.batterytesttask.dto;

import org.junit.Test;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;

public class GetMoByConditionDtoTest
{
    @Test
    public void testSetGetToString()
    {
        GetMoByConditionDto bean = new GetMoByConditionDto();
        bean.setName("1");
        bean.setMoc("77");
        bean.setLogroupId("77");
        bean.setPageNo(1);
        bean.setPageSize(11);

        bean.getLogroupId();
        bean.getName();
        bean.getPageNo();
        bean.getPageSize();

        bean.toString();
        assertEquals("77",bean.getLogroupId());
    }
}
