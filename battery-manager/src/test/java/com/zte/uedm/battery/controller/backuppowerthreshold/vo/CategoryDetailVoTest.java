package com.zte.uedm.battery.controller.backuppowerthreshold.vo;

import com.zte.uedm.battery.bean.CategoryDetailPojo;
import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

public class CategoryDetailVoTest {

    @Test
    public void testSuccessfully()throws Exception
    {
        PojoTestUtil.TestForPojo(CategoryDetailVo.class);
        CategoryDetailVo categoryDetailVo = new CategoryDetailVo();
        categoryDetailVo.setId("1");
        categoryDetailVo.setThreshold(7);
        categoryDetailVo.setUpdater("77");

        categoryDetailVo.getId();
        categoryDetailVo.getThreshold();
        categoryDetailVo.getUpdater();

        categoryDetailVo.toString();
        Assert.assertEquals("1",categoryDetailVo.getId());
    }

    @Test
    public void testTransform() throws Exception
    {
        CategoryDetailPojo categoryDetailPojo = new CategoryDetailPojo();
        categoryDetailPojo.setUpdater("77");
        categoryDetailPojo.setId("7");

        CategoryDetailVo categoryDetailVo = new CategoryDetailVo(categoryDetailPojo);
        Assert.assertEquals("7",categoryDetailVo.getId());
    }

    @Test
    public void testGmtModifiedNotNull() throws Exception
    {
        CategoryDetailPojo categoryDetailPojo = new CategoryDetailPojo();
        Date date = new Date();
        categoryDetailPojo.setGmtModified(date);
        categoryDetailPojo.setSiteLevel("siteLevel");

        CategoryDetailVo categoryDetailVo = new CategoryDetailVo(categoryDetailPojo);
        Assert.assertNotNull(categoryDetailVo.getGmtModified());
    }

}
