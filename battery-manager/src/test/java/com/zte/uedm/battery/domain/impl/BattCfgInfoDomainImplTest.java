package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.mapper.BattCfgInfoMapper;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;

import static org.mockito.Mockito.when;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2022年05月17日 下午2:57
 * @Version : 1.0
 */
public class BattCfgInfoDomainImplTest
{
    @InjectMocks
    private BattCfgInfoDomainImpl battCfgInfoDomainImpl;
    @Mock
    private BattCfgInfoMapper battCfgInfoMapper;

    @Before
    public void before() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void findBattCfgInfoByMoId_Normal() throws Exception
    {
        try
        {
            when(battCfgInfoMapper.findBattCfgInfoByMoId(Mockito.any())).thenReturn(new ArrayList<>());
            battCfgInfoDomainImpl.findBattCfgInfoByMoId("");
        }
        catch (Exception e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void findBattCfgInfoByMoId_EX()
    {
        try
        {
            when(battCfgInfoMapper.findBattCfgInfoByMoId(Mockito.any())).thenThrow(new UedmException(-403, "xxx"));
            battCfgInfoDomainImpl.findBattCfgInfoByMoId("");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(new Integer(-200),e.getErrorId());
        }
    }
}
