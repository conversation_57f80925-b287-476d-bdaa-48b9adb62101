package com.zte.uedm.battery.domain.impl;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.LoopDataBean;
import com.zte.uedm.battery.bean.MoBasicInfoVo;
import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPo;
import com.zte.uedm.battery.controller.batttest.bo.TestImpactTrendSelectBo;
import com.zte.uedm.battery.domain.BattLoopDomain;
import com.zte.uedm.battery.domain.RemoteControlDomain;
import com.zte.uedm.battery.domain.SpHealthEvalDomain;
import com.zte.uedm.battery.enums.batttest.BattTestTypeEnums;
import com.zte.uedm.battery.mapper.BattTestMapper;
import com.zte.uedm.battery.mapper.BatteryBackupPowerEvalDMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.zte.uedm.battery.util.constant.BatteryConstant.*;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class BatteryTestDomainImplTest
{
    @InjectMocks
    private BatteryTestDomainImpl batteryTestDomain;
    @Mock
    private RemoteControlDomain remoteControlDomain;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private BattTestMapper battTestMapper;
    @Mock
    private BatteryBackupPowerEvalDMapper batteryBackupPowerEvalDMapper;
    @Mock
    private SpHealthEvalDomain spHealthEvalDomain;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private RedisService redisService;
    @Mock
    private JsonService jsonService;
    @Mock
    private BattLoopDomain battLoopDomain;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private CollectorCacheManager collectorCacheManager;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private MsgSenderService msgSenderService;

    @Mock
    private ConfigService configService;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void startBatteryTestTest_normal() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
        List<MoBasicInfoVo> list = new ArrayList<>();
        MoBasicInfoVo batt1 = new MoBasicInfoVo();
        batt1.setId("batt1");
        list.add(batt1);
        spRelationBatt.put("sp-1", list);
        List<IdNameBean> battHealthStatusList = new ArrayList<>();
        IdNameBean batt1HealthStatus = new IdNameBean();
        batt1HealthStatus.setName("test_end");
        batt1HealthStatus.setId("Ended");
        battHealthStatusList.add(batt1HealthStatus);
        Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());

        Map<String, Object> allValueMap = new HashMap<>();
        Map<String, String> value = new HashMap<>();
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME, "2022-08-18 12:00:00");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, "command_issuing");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME, "admin");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID, "taskId");
        allValueMap.put("sp-1", value);
        Mockito.doReturn(allValueMap).when(redisService).getCacheMap(Mockito.anyString());
        Map<Integer, String> result = batteryTestDomain.startBatteryTest(Arrays.asList("sp-1"), serviceBean);
        Assert.assertEquals(2l, result.size());
    }
    @Test
    public void startBatteryTestTest_normal2() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
        List<MoBasicInfoVo> list = new ArrayList<>();
        MoBasicInfoVo batt1 = new MoBasicInfoVo();
        batt1.setId("batt1");
        list.add(batt1);
        spRelationBatt.put("sp-1", list);
        List<IdNameBean> battHealthStatusList = new ArrayList<>();
        IdNameBean batt1HealthStatus = new IdNameBean();
        batt1HealthStatus.setName("test_end");
        batt1HealthStatus.setId("Ended");
        battHealthStatusList.add(batt1HealthStatus);
        Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());

        Map<String, Object> allValueMap = new HashMap<>();

        Mockito.doReturn(allValueMap).when(redisService).getCacheMap(Mockito.anyString());
        Map<Integer, String> result = batteryTestDomain.startBatteryTest(Arrays.asList("sp-1"), serviceBean);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void startBatteryTestTest_noBattpack() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
        List<MoBasicInfoVo> list = new ArrayList<>();
        spRelationBatt.put("sp-1", list);
        List<IdNameBean> battHealthStatusList = new ArrayList<>();
        Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());
        Map<Integer, String> result = batteryTestDomain.startBatteryTest(Arrays.asList("sp-1"), serviceBean);
        Assert.assertEquals(2l, result.size());
    }

    @Test
    public void startBatteryTestTest_notTest() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
        List<MoBasicInfoVo> list = new ArrayList<>();
        MoBasicInfoVo batt1 = new MoBasicInfoVo();
        batt1.setId("batt1");
        list.add(batt1);
        spRelationBatt.put("sp-1", list);
        List<IdNameBean> battHealthStatusList = new ArrayList<>();
        IdNameBean batt1HealthStatus = new IdNameBean();
        batt1HealthStatus.setName("test");
        batt1HealthStatus.setId("sp-1");
        battHealthStatusList.add(batt1HealthStatus);
        Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());
        Map<Integer, String> result = batteryTestDomain.startBatteryTest(Arrays.asList("sp-1"), serviceBean);
        Assert.assertEquals(2l, result.size());
    }

    @Test
    public void startBatteryTestTest_Ex() throws UedmException
    {
        try
        {
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
            Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
            List<MoBasicInfoVo> list = new ArrayList<>();
            MoBasicInfoVo batt1 = new MoBasicInfoVo();
            batt1.setId("batt1");
            list.add(batt1);
            spRelationBatt.put("sp-1", list);
            List<IdNameBean> battHealthStatusList = new ArrayList<>();
            IdNameBean batt1HealthStatus = new IdNameBean();
            batt1HealthStatus.setName("test_end");
            batt1HealthStatus.setId("sp-1");
            battHealthStatusList.add(batt1HealthStatus);
            Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
            Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());
            Mockito.doThrow(new UedmException(-1, "")).when(remoteControlDomain).remoteControl(Mockito.any(), Mockito.any());
            batteryTestDomain.startBatteryTest(Arrays.asList("sp-1"), serviceBean);
        }
        catch (Exception e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void insertTestRecordInfoTest_normal()
    {
        try
        {
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
            Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
            List<MoBasicInfoVo> list = new ArrayList<>();
            MoBasicInfoVo batt1 = new MoBasicInfoVo();
            batt1.setId("batt1");
            list.add(batt1);
            spRelationBatt.put("sp-1", list);
            List<IdNameBean> battHealthStatusList = new ArrayList<>();
            IdNameBean batt1HealthStatus = new IdNameBean();
            batt1HealthStatus.setName("test_end");
            batt1HealthStatus.setId("sp-1");
            battHealthStatusList.add(batt1HealthStatus);
            Mockito.doReturn(spRelationBatt).when(battLoopDomain).getRelationLoops(Mockito.any());
            Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());
            Mockito.doReturn("2022-07-01 12:12:10").when(dateTimeService).getCurrentTime();
            CollectorEntity collectorEntity = new CollectorEntity();
            collectorEntity.setMoc("r32.uedm.collector.bcua");
            Mockito.when(collectorCacheManager.getCollectorById(Mockito.any())).thenReturn(Arrays.asList(collectorEntity));
            batteryTestDomain.insertTestRecordInfo(Arrays.asList("sp-1"), "temp", "task_id", serviceBean, "2024-06-19 18:21:29");
            collectorEntity.setMoc("r32.uedm.collector.csu5");
            Mockito.when(collectorCacheManager.getCollectorById(Mockito.any())).thenReturn(Arrays.asList(collectorEntity));
            batteryTestDomain.insertTestRecordInfo(Arrays.asList("sp-1"), "temp", "task_id", serviceBean, "2024-06-19 18:21:29");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }


    @Test
    public void test()
    {
        try {
            List<BattTestRelationDataRecordPo> list = new ArrayList<>();
            BattTestRelationDataRecordPo battTestRelationDataRecordPo = new BattTestRelationDataRecordPo();
            list.add(battTestRelationDataRecordPo);

            Mockito.doReturn(list).when(battTestMapper).selectByRecordPoId(Mockito.any());
            List<BattTestRelationDataRecordPo> battTestRelationDataRecordPos = batteryTestDomain.selectByRecordPoId(null);
            Assert.assertEquals(0,battTestRelationDataRecordPos.size());
        }catch (Exception e)
        {
            Assert.assertEquals("param recordPoId is empty",e.getMessage());
        }
    }

    @Test
    public void test1()
    {
        try {
            List<BattTestRelationDataRecordPo> list = new ArrayList<>();
            BattTestRelationDataRecordPo battTestRelationDataRecordPo = new BattTestRelationDataRecordPo();
            list.add(battTestRelationDataRecordPo);

            Mockito.doReturn(list).when(battTestMapper).selectByRecordPoId(Mockito.any());
            List<BattTestRelationDataRecordPo> battTestRelationDataRecordPos = batteryTestDomain.selectByRecordPoId("77");
            Assert.assertEquals(1,battTestRelationDataRecordPos.size());
        }catch (Exception e)
        {
            Assert.assertEquals("param recordPoId is empty",e.getMessage());
        }
    }

    @Test
    public void insertTestRecordDetailTest_normal()
    {
        try
        {
            List<LoopDataBean> spLoopDataBeans = new ArrayList<>();
            LoopDataBean loopDataBean = new LoopDataBean();
            loopDataBean.setId("loopId");
            loopDataBean.setTestStartCause("3");
            loopDataBean.setTestStartTime("2022-08-18 12:00:00");
            loopDataBean.setTestEndTime("2022-08-18 12:00:00");
            LoopDataBean loopDataBean1 = new LoopDataBean();
            loopDataBean1.setTestStartCause("3");
            loopDataBean1.setId("loopId-1");
            loopDataBean1.setTestStartTime("2022-08-18 11:00:00");
            loopDataBean1.setTestEndTime("2022-08-18 13:00:00");
            loopDataBean1.setTestStopCause("exit");
            spLoopDataBeans.add(loopDataBean);
            spLoopDataBeans.add(loopDataBean1);
            batteryTestDomain.insertTestRecordDetail("sp", spLoopDataBeans, "2022-08-20 12:00:00");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void insertTestRecordDetailTest_blank()
    {
        try
        {
            List<LoopDataBean> spLoopDataBeans = new ArrayList<>();
            LoopDataBean loopDataBean = new LoopDataBean();
            loopDataBean.setId("loopId");
            loopDataBean.setTestStartCause("3");
            loopDataBean.setTestStartTime("2022-08-18 12:00:00");
            loopDataBean.setTestEndTime("2022-08-18 12:00:00");
            LoopDataBean loopDataBean1 = new LoopDataBean();
            loopDataBean1.setTestStartCause("3");
            loopDataBean1.setId("loopId-1");
            loopDataBean1.setTestStartTime("2022-08-18 11:00:00");
            loopDataBean1.setTestEndTime("2022-08-18 13:00:00");
            loopDataBean1.setTestStopCause("exit");
            spLoopDataBeans.add(loopDataBean);
            spLoopDataBeans.add(loopDataBean1);
            batteryTestDomain.insertTestRecordDetail("", spLoopDataBeans, "2022-08-20 12:00:00");
            batteryTestDomain.insertTestRecordDetail("sp", null, "2022-08-20 12:00:00");
            batteryTestDomain.insertTestRecordDetail("sp", spLoopDataBeans, "");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("param is blank!",e.getMessage());
        }
    }


    @Test
    public void selectByCondition()
    {
        try
        {
            TestImpactTrendSelectBo testImpactTrendSelectBo=new TestImpactTrendSelectBo();
            testImpactTrendSelectBo.setSpIds(Arrays.asList("1"));
            batteryTestDomain.selectByCondition(testImpactTrendSelectBo, null);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("selectByCondition  params is empty",e.getMessage());
        }
    }

    @Test
    public void selectByCondition1()
    {
        try
        {
            TestImpactTrendSelectBo testImpactTrendSelectBo=new TestImpactTrendSelectBo();
            testImpactTrendSelectBo.setSpIds(Arrays.asList("1"));
            ServiceBaseInfoBean serviceBean=new ServiceBaseInfoBean("1","2","3");
            batteryTestDomain.selectByCondition(null, serviceBean);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("selectByCondition  params is empty",e.getMessage());
        }
    }

    @Test
    public void selectByCondition2()
    {
        try
        {
            TestImpactTrendSelectBo testImpactTrendSelectBo=new TestImpactTrendSelectBo();
            testImpactTrendSelectBo.setSpIds(Arrays.asList("1"));
            ServiceBaseInfoBean serviceBean=new ServiceBaseInfoBean("1","2","3",1,2);
            Mockito.doThrow(new RuntimeException("12")).when(battTestMapper).selectByCondition(Mockito.any());
            batteryTestDomain.selectByCondition(testImpactTrendSelectBo, serviceBean);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }
    @Test
    public void selectByCondition3()
    {
        try
        {
            TestImpactTrendSelectBo testImpactTrendSelectBo=new TestImpactTrendSelectBo();
            testImpactTrendSelectBo.setSpIds(Arrays.asList("1"));
            batteryTestDomain.selectByCondition(testImpactTrendSelectBo, new ServiceBaseInfoBean("xx","xx"));
        }
        catch (UedmException e)
        {
            Assert.assertEquals("selectByCondition  params is empty",e.getMessage());
        }
    }

    @Test
    public void testGetNotTestSpId() throws UedmException {
        // 准备测试数据
        List<String> ids = Arrays.asList("id1", "id2", "id3");
        Set<String> idSet = new HashSet<>(ids);

        // 模拟 battTestMapper.selectBattTestStatus 的行为
        List<IdNameBean> battTestStatus = Arrays.asList(
                new IdNameBean("id1", "TEST_START"),
                new IdNameBean("id2", "TEST_END"),
                new IdNameBean("id3", "TEST_EXIT")
        );
        when(battTestMapper.selectBattTestStatus(ids)).thenReturn(battTestStatus);

        // 调用被测试的方法
        batteryTestDomain.getNotTestSpId(ids, idSet);

        // 验证结果
        assertTrue(idSet.contains("id1"));   // id1 的状态是 TEST_START，应该保留

        // 验证 battTestMapper.selectBattTestStatus 被调用了一次
        verify(battTestMapper, times(1)).selectBattTestStatus(ids);
    }
    @Test
    public void startBatterySocTest() throws com.zte.uedm.basis.exception.UedmException, UedmException {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<ResourceCollectorRelationEntity>> relationMap = new HashMap<>();
        List<ResourceCollectorRelationEntity> entityList = new ArrayList<>();
        ResourceCollectorRelationEntity entity = new ResourceCollectorRelationEntity();
        entity.setCollectorId("id");
        entityList.add(entity);
        relationMap.put("1", entityList);
        when(resourceCollectorRelationCacheManager.queryResourceWithCollectorMap(Mockito.any())).thenReturn(relationMap);
        List<CollectorEntity> collector = new ArrayList<>();
        CollectorEntity c1 = new CollectorEntity();
        collector.add(c1);
        when(collectorCacheManager.getCollectorById(Mockito.any())).thenReturn(collector);
        when(configService.getGlobalProperty(Mockito.any())).thenReturn("30");
        Map<Integer, String> startBatterySoc = batteryTestDomain.startBatterySoc(Arrays.asList("1"),serviceBean, BattTestTypeEnums.TEMPORARY.getId(), new HashMap<>());
    }
    @Test
    public void startBatterySocTest_null() throws com.zte.uedm.basis.exception.UedmException, UedmException {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<ResourceCollectorRelationEntity>> relationMap = new HashMap<>();
        List<ResourceCollectorRelationEntity> entityList = new ArrayList<>();
        ResourceCollectorRelationEntity entity = new ResourceCollectorRelationEntity();
        entity.setCollectorId("id");
        entityList.add(entity);
        relationMap.put("1", entityList);
        when(resourceCollectorRelationCacheManager.queryResourceWithCollectorMap(Mockito.any())).thenReturn(relationMap);
        List<CollectorEntity> collector = new ArrayList<>();
        CollectorEntity c1 = new CollectorEntity();
        collector.add(c1);
        when(collectorCacheManager.getCollectorById(Mockito.any())).thenReturn(collector);
        when(configService.getGlobalProperty(Mockito.any())).thenReturn("30");
        Map<Integer, String> startBatterySoc = batteryTestDomain.startBatterySoc(Arrays.asList("2"),serviceBean, BattTestTypeEnums.TEMPORARY.getId(), new HashMap<>());
    }
    @Test
    public void startBatterySocTest_null1() throws com.zte.uedm.basis.exception.UedmException, UedmException {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<ResourceCollectorRelationEntity>> relationMap = new HashMap<>();
        List<ResourceCollectorRelationEntity> entityList = new ArrayList<>();
        ResourceCollectorRelationEntity entity = new ResourceCollectorRelationEntity();
        entity.setCollectorId("id");
        entityList.add(entity);
        relationMap.put("1", entityList);
        when(resourceCollectorRelationCacheManager.queryResourceWithCollectorMap(Mockito.any())).thenReturn(relationMap);
        List<CollectorEntity> collector = new ArrayList<>();
        CollectorEntity c1 = new CollectorEntity();
        when(collectorCacheManager.getCollectorById(Mockito.any())).thenReturn(collector);
        when(configService.getGlobalProperty(Mockito.any())).thenReturn("30");
        Map<Integer, String> startBatterySoc = batteryTestDomain.startBatterySoc(Arrays.asList("1"),serviceBean, BattTestTypeEnums.TEMPORARY.getId(), new HashMap<>());
    }
    @Test
    public void startBatterySocTest_normal() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<String, List<MoBasicInfoVo>> spRelationBatt = new HashMap<>();
        List<MoBasicInfoVo> list = new ArrayList<>();
        MoBasicInfoVo batt1 = new MoBasicInfoVo();
        batt1.setId("batt1");
        list.add(batt1);
        spRelationBatt.put("sp-1", list);
        List<IdNameBean> battHealthStatusList = new ArrayList<>();
        IdNameBean batt1HealthStatus = new IdNameBean();
        batt1HealthStatus.setName("test_end");
        batt1HealthStatus.setId("Ended");
        battHealthStatusList.add(batt1HealthStatus);
        Mockito.doReturn(spRelationBatt).when(configurationManagerRpc).getChildMonitorObjects(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(battHealthStatusList).when(battTestMapper).selectBattTestStatus(Mockito.any());

        Map<String, Object> allValueMap = new HashMap<>();
        Map<String, String> value = new HashMap<>();
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_CAUSE_TIME, "2022-08-18 12:00:00");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_STATE, "command_issuing");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_USER_NAME, "admin");
        value.put(REDIS_CACHE_NAME_BATT_TEST_CACHE_KEY_TASK_ID, "taskId");
        allValueMap.put("sp-1", value);
        Mockito.doReturn(allValueMap).when(redisService).getCacheMap(Mockito.anyString());
        Map<Integer, String> result = batteryTestDomain.startBatterySoc(Arrays.asList("sp-1"), serviceBean, BattTestTypeEnums.TEMPORARY.getId(), new HashMap<>());
        Assert.assertEquals(2l, result.size());
    }
    @Test(expected = UedmException.class)
    public void testGetNotTestSpIdWithException() throws UedmException {
        // 准备测试数据
        List<String> ids = Arrays.asList("id1", "id2", "id3");
        Set<String> idSet = new HashSet<>(ids);

        // 模拟 battTestMapper.selectBattTestStatus 抛出异常
        when(battTestMapper.selectBattTestStatus(ids)).thenThrow(new RuntimeException());

        // 调用被测试的方法，预期抛出 UedmException
        batteryTestDomain.getNotTestSpId(ids, idSet);
    }
}

