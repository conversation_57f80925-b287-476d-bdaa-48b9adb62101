package com.zte.uedm.battery.pv.enums;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.List;

public class PriceTypeEnumTest {
    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getId() {
        Assert.assertSame(PriceTypeEnum.FLAT.getId(), "Flat");
    }

    @Test
    public void getName() {
        Assert.assertSame(PriceTypeEnum.FLAT.getName(), "{\"en_US\":\"Flat\",\"zh_CN\":\"单一\"}");
    }


    @Test
    public void getNameById() {
        String name = PriceTypeEnum.getNameById("Flat");
        Assert.assertSame(name, "{\"en_US\":\"Flat\",\"zh_CN\":\"单一\"}");
        String name1 = PriceTypeEnum.getNameById(null);
        Assert.assertSame(name1, "");
        String name2 = PriceTypeEnum.getNameById("11");
        Assert.assertSame(name2, "");
    }

    @Test
    public void getNameByIdAndTiered(){
        String name = PriceTypeEnum.getNameByIdAndTiered("Tiered 2-Critical Peak","2");
        Assert.assertEquals(name, "{\"en_US\":\"Tiered 2-Critical Peak\",\"zh_CN\":\"第2档-尖峰\"}");
        String name1 = PriceTypeEnum.getNameByIdAndTiered("Tiered 2-Critical Peak",null);
        Assert.assertSame(name1, "");
        String name2 = PriceTypeEnum.getNameByIdAndTiered("","2");
        Assert.assertSame(name2, "");
        String name3 = PriceTypeEnum.getNameByIdAndTiered("","");
        Assert.assertSame(name3, "");
        String name4 = PriceTypeEnum.getNameByIdAndTiered("Tiered 2-Critical Peak","3");
        Assert.assertSame(name4, "");
    }
}
