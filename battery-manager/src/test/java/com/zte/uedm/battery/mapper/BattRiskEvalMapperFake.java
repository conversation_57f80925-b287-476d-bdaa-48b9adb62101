package com.zte.uedm.battery.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zte.uedm.battery.bean.BattRiskEvalBean;
import com.zte.uedm.battery.bean.BattRiskEvalPojo;
import com.zte.uedm.battery.bean.BattRiskEvalVO;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskEvalMapper;
import com.zte.uedm.common.exception.UedmException;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class BattRiskEvalMapperFake implements BattRiskEvalMapper {
    @Override
    public List<BattRiskEvalVO> batteryDetailRiskSelectByid(String id, String currentDay) throws UedmException {
        /* Started by AICoder, pid:a0165ae806774565bf31817b817f72db */
        List<BattRiskEvalVO> list = new ArrayList<>();

        BattRiskEvalVO battRiskEvalVO1 = new BattRiskEvalVO();
        battRiskEvalVO1.setBattId("1");
        battRiskEvalVO1.setRiskId("101");
        battRiskEvalVO1.setRiskLevel("1");

        BattRiskEvalVO battRiskEvalVO2 = new BattRiskEvalVO();
        battRiskEvalVO2.setBattId("2");
        battRiskEvalVO2.setRiskId("102");
        battRiskEvalVO2.setRiskLevel("2");

        list.add(battRiskEvalVO1);
        list.add(battRiskEvalVO2);
        /* Ended by AICoder, pid:a0165ae806774565bf31817b817f72db */
        return list;
    }

    @Override
    public List<BattRiskEvalBean> queryByBattIdAndStartEndTime(List<String> ids, List<String> riskLevels, String currentDay) {
        List<BattRiskEvalBean> list = new ArrayList<>();
        BattRiskEvalBean bean = new BattRiskEvalBean();
        bean.setRiskId("1");
        bean.setBattId("11");
        bean.setRiskLevel("1");
        list.add(bean);
        return list;
    }

    @Override
    public List<BattRiskEvalPojo> selectAll() {
        List<BattRiskEvalPojo> list = new ArrayList<>();
        BattRiskEvalPojo bean = new BattRiskEvalPojo();
        bean.setRiskId("1");
        bean.setBattId("11");
        bean.setRiskLevel("1");
        list.add(bean);
        return list;
    }

    @Override
    public void batchInsert(List<BattRiskEvalPojo> list) {

    }

    @Override
    public int insert(BattRiskEvalBean entity) {
        return 0;
    }

    @Override
    public int deleteById(Serializable id) {
        return 0;
    }

    @Override
    public int deleteById(BattRiskEvalBean entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<BattRiskEvalBean> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(BattRiskEvalBean entity) {
        return 0;
    }

    @Override
    public int update(BattRiskEvalBean entity, Wrapper<BattRiskEvalBean> updateWrapper) {
        return 0;
    }

    @Override
    public BattRiskEvalBean selectById(Serializable id) {
        return null;
    }

    @Override
    public List<BattRiskEvalBean> selectBatchIds(Collection<? extends Serializable> idList) {
        return null;
    }

    @Override
    public List<BattRiskEvalBean> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }

    @Override
    public List<BattRiskEvalBean> selectList(Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<BattRiskEvalBean>> P selectPage(P page, Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<BattRiskEvalBean> queryWrapper) {
        return null;
    }
}
