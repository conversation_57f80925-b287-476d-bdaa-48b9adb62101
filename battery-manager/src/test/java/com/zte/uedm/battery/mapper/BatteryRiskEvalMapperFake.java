package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.service.po.BattRiskEvalPO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BatteryRiskEvalMapperFake implements BatteryRiskEvalMapper{
    @Override
    public List<BattRiskEvalPO> singleBatteryHistoryQry(String batteryId, String evaluateTimeStart, String evaluateTimeEnd) {
        List<BattRiskEvalPO> resultList = new ArrayList<>();
        // 创建数据
        BattRiskEvalPO po = new BattRiskEvalPO();
        po.setBattId("12345");
        po.setRiskId("67890");
        po.setEvalTime("2022-01-01");
        po.setName("Mock Battery");
        po.setPathIds("1,2,3");
        po.setPathNames("Path1,Path2,Path3");
        po.setBattType("Lithium-ion");
        po.setRiskName("Overheating");
        po.setRiskLevel("1");
        po.setRiskCause("{\"en_US\":\"Battery temperature too high\",\"zh_CN\":\"电池温度过高\"}");
        po.setRiskSuggestion("{\"en_US\":\"nosuggestion\",\"zh_CN\":\"建议无\"}");
        po.setRiskDetail("The battery temperature exceeded the safe limit.");
        po.setCreator("TestUser");
        po.setGmtCreate(new Date());
        resultList.add(po);
        return resultList;
    }
}
