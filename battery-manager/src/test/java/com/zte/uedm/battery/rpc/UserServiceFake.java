package com.zte.uedm.battery.rpc;

import com.zte.uedm.battery.util.FakeBranchFlag;
import com.zte.uedm.function.sm.api.user.impl.UserServiceImpl;
import com.zte.uedm.function.sm.api.user.vo.UserDetailVo;
import com.zte.uedm.function.sm.exception.AuthorityException;
import com.zte.uedm.function.sm.optional.AuthorizationStatusOptional;

import java.util.ArrayList;
import java.util.List;

public class UserServiceFake extends UserServiceImpl {
    /* Started by AICoder, pid:p8d1d7404589c4a148640a9590c6f55361c39b1c */
    /**
     * 用于获取MO的标志。
     */
    public static String GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NULL;

    /**
     * 角色列表。
     */
    private List<String> roles;

    /**
     * 默认角色名称。
     */
    private String defaultRoleName = "all";

    /**
     * 获取用户详细信息。
     *
     * @param userName 用户名
     * @return 用户详细信息对象
     */
    @Override
    public UserDetailVo getUserDetail(String userName) {
        UserDetailVo userDetailVo = new UserDetailVo();

        if (userName.equals(defaultRoleName)) {
            roles = new ArrayList<>();
            roles.add("Test");
        }

        userDetailVo.setRoleNames(roles);
        return userDetailVo;
    }

    /**
     * 获取授权状态。
     *
     * @param userRoles          用户角色列表
     * @param authorizedRoles    授权角色列表
     * @param authorizedAllRoles 所有授权角色列表
     * @return 授权状态
     * @throws AuthorityException 权限异常
     */
    public AuthorizationStatusOptional getAuthorizationStatus(List<String> userRoles,
                                                              List<String> authorizedRoles,
                                                              List<String> authorizedAllRoles) throws AuthorityException {
        if (GET_MO_BY_MOC_AND_POSITION.equals(FakeBranchFlag.DATA_NULL)) {
            return AuthorizationStatusOptional.NO_PERMISSION;
        }
        //模拟部分权限
        if (GET_MO_BY_MOC_AND_POSITION.equals(FakeBranchFlag.DATA_EXP)) {
            return AuthorizationStatusOptional.PARTIAL_PERMISSION;
        }
        // 完全权限
        return AuthorizationStatusOptional.FULL_PERMISSION;
    }
    /* Ended by AICoder, pid:p8d1d7404589c4a148640a9590c6f55361c39b1c */
}