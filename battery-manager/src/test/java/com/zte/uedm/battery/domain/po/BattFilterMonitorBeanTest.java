package com.zte.uedm.battery.domain.po;

import org.junit.Assert;
import org.junit.Test;

public class BattFilterMonitorBeanTest
{
    @Test
    public void test()
    {
        BattFilterMonitorBean bean = new BattFilterMonitorBean();
        bean.setName("77");
        bean.setId("77");
        bean.setLeftLifeInteger(7);
        bean.setLeftLife("77");
        bean.setLeftLifeSource("AI");
        bean.setLeftLifeEvalInfo("General");
        bean.toString();
        Assert.assertEquals("77",bean.getId());
        BattFilterMonitorBean bean1 = new BattFilterMonitorBean("77");
        Assert.assertEquals("77",bean1.getId());

    }
}
