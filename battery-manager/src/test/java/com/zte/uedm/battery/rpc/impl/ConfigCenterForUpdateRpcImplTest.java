/* Started by AICoder, pid:xa504o43ea6dce71468a0bfc50a4bb51e97622df */
package com.zte.uedm.battery.rpc.impl;

import com.zte.uedm.battery.a_infrastructure.rpc.bean.ConfigGroup;
import com.zte.uedm.battery.a_infrastructure.rpc.ConfigCenterForUpdateRpc;
import com.zte.uedm.battery.a_infrastructure.rpc.impl.ConfigCenterForUpdateRpcImpl;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

public class ConfigCenterForUpdateRpcImplTest {

    @InjectMocks
    private ConfigCenterForUpdateRpcImpl configCenterRpcImpl;

    @Mock
    private ConfigCenterForUpdateRpc configCenterRpc;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void updateExecute() throws IOException, UedmException {
        Call<ResponseBean> call = mock(Call.class);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Response<ResponseBean> response = Response.success(responseBean);

        doReturn(call).when(configCenterRpc).editConfigCenterInfo(any());
        doReturn(response).when(call).execute();

        configCenterRpcImpl.updateConfigCenterInfo(new ConfigGroup());

        Mockito.when(configCenterRpc.editConfigCenterInfo(any())).thenThrow(new RuntimeException());

        try {
            configCenterRpcImpl.updateConfigCenterInfo(new ConfigGroup());
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId().toString(), "-502");
        }
    }
}
/* Ended by AICoder, pid:xa504o43ea6dce71468a0bfc50a4bb51e97622df */