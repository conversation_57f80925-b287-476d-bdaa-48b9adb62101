package com.zte.uedm.battery.controller.backuppower;

import com.zte.uedm.battery.bean.BattBackupPowerThresholdDetailPojo;
import com.zte.uedm.battery.controller.backuppower.dto.DeviceBackupReqDto;
import com.zte.uedm.battery.domain.BackupPowerThresholdDetailDomain;
import com.zte.uedm.battery.service.MonitorObjectService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import java.util.ArrayList;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

public class MonitorObjectControllerTest {

    @InjectMocks
    private MonitorObjectController monitorObjectController;

    @Mock
    private MonitorObjectService monitorObjectService;

    @Mock
    private BackupPowerThresholdDetailDomain backupPowerThresholdDetailDomain;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectObjectPowerDuration_normal() {
        DeviceBackupReqDto voIdBlank = DeviceBackupReqDto.builder().ids(new ArrayList<>()).build();
        DeviceBackupReqDto voGrainNormal = DeviceBackupReqDto.builder().ids(new ArrayList<String>(){{add("id1");}}).build();
        ResponseBean voIdBlankRes =  monitorObjectController.selectObjectPowerDuration(voIdBlank, "zh-CN");

        ResponseBean voGrainNormalRes = monitorObjectController.selectObjectPowerDuration(voGrainNormal, "zh-CN");
        Assert.assertEquals("param is valid", voIdBlankRes.getMessage());
        Assert.assertSame(1, voGrainNormalRes.getTotal());
    }

    @Test
    public void selectObjectPowerDuration_exception() throws UedmException {
        DeviceBackupReqDto voGrainNormal = DeviceBackupReqDto.builder().ids(new ArrayList<String>(){{add("uid");}}).build();
        Mockito.when(monitorObjectService.getDeviceBackupDuration(anyList())).thenThrow(new UedmException(0, "error"));
        ResponseBean voGrainNormalRes = monitorObjectController.selectObjectPowerDuration(voGrainNormal, "zh-CN");
        Assert.assertSame(0, voGrainNormalRes.getCode());
    }

    @Test
    public void selectDesignedPowerDuration() throws UedmException {
        Mockito.when(backupPowerThresholdDetailDomain.selectById(anyString())).thenReturn(new BattBackupPowerThresholdDetailPojo());
        ResponseBean voGrainNormalRes = monitorObjectController.selectDesignedPowerDuration("id");
        Assert.assertSame(0, voGrainNormalRes.getCode());
    }

    @Test
    public void selectDesignedPowerDuration_exception() throws UedmException {
        Mockito.when(backupPowerThresholdDetailDomain.selectById(anyString())).thenThrow(new UedmException(-1, "error"));
        ResponseBean voGrainNormalRes = monitorObjectController.selectDesignedPowerDuration("id");
        Assert.assertSame(-1, voGrainNormalRes.getCode());
    }

    @Test
    public void selectBackupByCondition_exception() throws UedmException {
        Mockito.when(monitorObjectController.selectBackupByCondition(Mockito.any())).thenThrow(new UedmException(-1, "error"));
        ResponseBean voGrainNormalRes = monitorObjectController.selectBackupByCondition(new DeviceBackupReqDto());
        Assert.assertSame(-1, voGrainNormalRes.getCode());
    }
}