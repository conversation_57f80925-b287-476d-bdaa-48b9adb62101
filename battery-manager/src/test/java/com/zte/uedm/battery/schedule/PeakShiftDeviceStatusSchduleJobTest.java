package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.PeakShiftDeviceStatusBean;
import com.zte.uedm.battery.mapper.GridStrategyMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PeakShiftDeviceStatusSchduleJobTest
{
    @InjectMocks
    private PeakShiftDeviceStatusSchduleJob peakShiftDeviceStatusSchduleJob;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Mock
    private GridStrategyMapper gridStrategyMapper;

    @Mock
    private DateTimeService dateTimeService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute() throws Exception
    {
        Exception ex = null;
        try
        {
            final DevicePeakCacheInfoBean bean = new DevicePeakCacheInfoBean();
            bean.setDeviceId("deviceId");
            bean.setDeviceName("deviceName");
            bean.setParentId("parentId");
            bean.setSiteId("siteId");
            bean.setSiteName("siteName");
            bean.setPosition("position");
            bean.setElectricBenefit("electricBenefit");
            bean.setTotalCharge("totalCharge");
            bean.setTotalDischarge("totalDischarge");
            bean.setBattCapacity("battCapacity");
            bean.setExecStatus("execStatus");
            bean.setRunningStatus("runningStatus");
            bean.setRunningStatusMap(new HashMap<>());
            bean.setEnablePeak(false);
            bean.setCurrStrategy("currStrategy");
            final List<DevicePeakCacheInfoBean> list = Arrays.asList(bean);
            when(configurationManagerRpc.queryAllList()).thenReturn(list);

            when(gridStrategyMapper.batchInsert(Arrays.asList(new PeakShiftDeviceStatusBean()))).thenReturn(0);

            peakShiftDeviceStatusSchduleJob.execute();
        }
        catch (UedmException e)
        {
            ex = e;
        }
        assertEquals(ex,null);
    }

    @Test
    public void testExecute_ConfigurationManagerRpcImplReturnsNoItems() throws Exception
    {
        Exception ex = null;
        try
        {
            when(configurationManagerRpc.queryAllList()).thenReturn(Collections.emptyList());
            when(gridStrategyMapper.batchInsert(Arrays.asList(new PeakShiftDeviceStatusBean()))).thenReturn(0);

            peakShiftDeviceStatusSchduleJob.execute();
        }
        catch (UedmException e)
        {
            ex = e;
        }
        assertEquals(null,ex);
    }

    @Test
    public void testExecute_ConfigurationManagerRpcImplThrowsUedmException() throws Exception
    {
        Exception ex = null;
        try
        {
            when(configurationManagerRpc.queryAllList()).thenThrow(UedmException.class);
            peakShiftDeviceStatusSchduleJob.execute();
        }
        catch (UedmException e)
        {
            ex = e;
        }
        assertEquals(null,ex);
    }
}
