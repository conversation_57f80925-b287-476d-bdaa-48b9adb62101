package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class BattHealthStatusEvalBeanTest {
    @Test
    public void testSetGetToString()
    {
        BattHealthStatusEvalPo bean = new BattHealthStatusEvalPo();
        bean.setId("77");
        bean.toString();
        assertEquals("77",bean.getId());
    }
}
