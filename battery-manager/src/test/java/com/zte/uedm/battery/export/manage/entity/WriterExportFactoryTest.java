package com.zte.uedm.battery.export.manage.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.HashMap;
import java.util.Map;

import com.zte.uedm.battery.export.manage.ExcelFileWriter;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.ObjectFactory;


public class WriterExportFactoryTest
{
    
    @InjectMocks
    private WriterExportFactory writerExportFactory;
    
    @Mock
    private ObjectFactory<Map<String, FileExportWriter>> f;
    
    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        
    }
    
    @Test
    public void ExcelFileWriterTest() throws Exception
    {
        ExcelFileWriter ExcelFileWriter = new ExcelFileWriter();
        Map<String, FileExportWriter> sMap = new HashMap<String, FileExportWriter>();
        sMap.put("file_writer_1", ExcelFileWriter);
        
        Mockito.doReturn(sMap).when(f).getObject();
        ExportType e = ExportType.valueOf("EXCEL");
        writerExportFactory.getWriter(e);
        assertEquals(1,sMap.size());
        
    }
}
