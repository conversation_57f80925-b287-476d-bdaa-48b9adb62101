package com.zte.uedm.battery.pv.vo;

import org.junit.Test;

import java.math.BigDecimal;

public class MaintenanceRemindVOTest {

    @Test
    public void test() throws Exception {
        MaintenanceRemindVO maintenanceRemindVO = new MaintenanceRemindVO();
        maintenanceRemindVO.setMaintenance(true);
        maintenanceRemindVO.setPowerRating("");
        maintenanceRemindVO.setSiteId("");
        maintenanceRemindVO.setPosition("");
        maintenanceRemindVO.setName("");
        maintenanceRemindVO.setEfficiency("");
        maintenanceRemindVO.setRatioSupply("");
        maintenanceRemindVO.setLoad("");
        maintenanceRemindVO.setLevel("");
        maintenanceRemindVO.setRemark("");
        maintenanceRemindVO.setGeneration("");
        maintenanceRemindVO.setSupplyMode("");
        String efficiency = maintenanceRemindVO.getEfficiency();
        String load = maintenanceRemindVO.getLoad();
        String generation = maintenanceRemindVO.getGeneration();
        String name = maintenanceRemindVO.getName();
        String level = maintenanceRemindVO.getLevel();
        String powerRating = maintenanceRemindVO.getPowerRating();
        String ratioSupply = maintenanceRemindVO.getRatioSupply();
        String siteId = maintenanceRemindVO.getSiteId();
        String position = maintenanceRemindVO.getPosition();
        String remark = maintenanceRemindVO.getRemark();
        boolean maintenance = maintenanceRemindVO.isMaintenance();
        String supplyMode = maintenanceRemindVO.getSupplyMode();
    }

    @Test
    public void setEfficiencyFromValue() {
        MaintenanceRemindVO maintenanceRemindVO = new MaintenanceRemindVO();
        maintenanceRemindVO.setEfficiencyFromValue(null, BigDecimal.ZERO);
        maintenanceRemindVO.setGenerationFromValue(null, BigDecimal.ZERO);
        maintenanceRemindVO.setLoadFromValue(null, BigDecimal.ZERO);
        maintenanceRemindVO.setPowerRatingFromValue(null, BigDecimal.ZERO);
        maintenanceRemindVO.setRatioSupplyFromValue(null, BigDecimal.ZERO);
        maintenanceRemindVO.setEfficiencyFromValue(BigDecimal.ONE, BigDecimal.ZERO);
        maintenanceRemindVO.setGenerationFromValue(BigDecimal.ONE, BigDecimal.ZERO);
        maintenanceRemindVO.setLoadFromValue(BigDecimal.ONE, BigDecimal.ZERO);
        maintenanceRemindVO.setRatioSupplyFromValue(BigDecimal.ONE, BigDecimal.ZERO);
        maintenanceRemindVO.setPowerRatingFromValue(BigDecimal.ONE,BigDecimal.ZERO);
        maintenanceRemindVO.setPathId("/id/id");
    }

}