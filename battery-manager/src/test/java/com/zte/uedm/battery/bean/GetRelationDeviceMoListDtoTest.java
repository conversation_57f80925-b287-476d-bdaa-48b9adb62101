package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

public class GetRelationDeviceMoListDtoTest
{
    @Test
    public void Test() throws Exception
    {
        PojoTestUtil.TestForPojo(GetRelationDeviceMoListDto.class);
        GetRelationDeviceMoListDto getRelationDeviceMoListDto = new GetRelationDeviceMoListDto();
        getRelationDeviceMoListDto.setMocList(Arrays.asList("1"));
        getRelationDeviceMoListDto.setMoId("1");
        String str = getRelationDeviceMoListDto.toString();
        Assert.assertEquals("1", getRelationDeviceMoListDto.getMoId());
    }
}
