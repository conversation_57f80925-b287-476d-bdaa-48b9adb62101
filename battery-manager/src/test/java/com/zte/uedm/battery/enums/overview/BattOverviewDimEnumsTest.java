package com.zte.uedm.battery.enums.overview;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;

import java.util.List;
import java.util.Map;

public class BattOverviewDimEnumsTest {
@Mock
private BattOverViewDimEnums battOverviewDimEnums;
    @Test
    public void test()
    {
        String id = BattOverViewDimEnums.NAME.getId();
        String id1 = BattOverViewDimEnums.POSITION.getId();
        String id2 = BattOverViewDimEnums.TYPE.getId();
        String id3 = BattOverViewDimEnums.VOLT.getId();
        String id4 = BattOverViewDimEnums.CURR.getId();
        String id5 = BattOverViewDimEnums.TEMP.getId();
        String id6 = BattOverViewDimEnums.SOC.getId();
        String id7 = BattOverViewDimEnums.SOH.getId();
        String id8 = BattOverViewDimEnums.RATED_CAP.getId();
        String id9 = BattOverViewDimEnums.CHARGE_STATUS.getId();
        String id10 = BattOverViewDimEnums.HEALTH.getId();
        String id11 = BattOverViewDimEnums.LEFT_LIFE.getId();
        String id19 = BattOverViewDimEnums.REMAIN_DISCHARGE_DURATION.getId();
        String id12 = BattOverViewDimEnums.ALARM.getId();
        String id13 = BattOverViewDimEnums.SUPPLIER.getId();
        String id14 = BattOverViewDimEnums.MANU_FACTUER.getId();
        String id15 = BattOverViewDimEnums.BRAND.getId();
        String id16 = BattOverViewDimEnums.SERIES.getId();
        String id17 = BattOverViewDimEnums.MODEL.getId();
        String id18 = BattOverViewDimEnums.START_DATE.getId();
        String id21 = BattOverViewDimEnums.RISK_LEVEL.getId();


        Assert.assertSame(id, "name");
        Assert.assertSame(id1, "position");
        Assert.assertSame(id2, "type");
        Assert.assertSame(id3, "volt");
        Assert.assertSame(id4, "curr");
        Assert.assertSame(id5, "temp");
        Assert.assertSame(id6, "soc");
        Assert.assertSame(id7, "soh");
        Assert.assertSame(id8, "ratedCap");
        Assert.assertSame(id9, "chargeStatus");
        Assert.assertSame(id10, "health");
        Assert.assertSame(id11, "leftLife");
        Assert.assertSame(id12, "alarm");
        Assert.assertSame(id13, "supplier");
        Assert.assertSame(id14, "manufacturer");
        Assert.assertSame(id15, "brand");
        Assert.assertSame(id16, "series");
        Assert.assertSame(id17, "model");
        Assert.assertSame(id18, "startDate");
        Assert.assertSame(id19, "remainDischargeDuration");
        Assert.assertSame(id21, "riskLevel");
    }

    @Test
    public void test1()
    {
        String healthName = BattOverViewDimEnums.HEALTH.getName();
        Assert.assertSame(healthName, "{\"en_US\":\"SOH\",\"zh_CN\":\"健康度\"}");

        String ratedCapName = BattOverViewDimEnums.RATED_CAP.getName();
        Assert.assertSame(ratedCapName, "{\"en_US\":\"Rated Capacity\",\"zh_CN\":\"额定容量\"}");

        String socName = BattOverViewDimEnums.SOC.getName();
        Assert.assertSame(socName, "{\"en_US\":\"SOC\",\"zh_CN\":\"剩余容量\"}");

        String chargeStatusName = BattOverViewDimEnums.CHARGE_STATUS.getName();
        Assert.assertSame(chargeStatusName, "{\"en_US\":\"Charge Status\",\"zh_CN\":\"运行状态\"}");

        String modelName = BattOverViewDimEnums.MODEL.getName();
        Assert.assertSame(modelName, "{\"en_US\":\"Model\",\"zh_CN\":\"型号\"}");

        String brandName = BattOverViewDimEnums.BRAND.getName();
        Assert.assertSame(brandName, "{\"en_US\":\"Brand\",\"zh_CN\":\"品牌\"}");

        String seriesName = BattOverViewDimEnums.SERIES.getName();
        Assert.assertSame(seriesName, "{\"en_US\":\"Series\",\"zh_CN\":\"系列\"}");

        String name1 = BattOverViewDimEnums.MANU_FACTUER.getName();
        Assert.assertSame(name1, "{\"en_US\":\"Manufacturer\",\"zh_CN\":\"制造商\"}");

        String riskLevelName = BattOverViewDimEnums.RISK_LEVEL.getName();
        Assert.assertSame(riskLevelName, "{\"en_US\":\"Risk Level\",\"zh_CN\":\"风险等级\"}");

        List<String> battOverviewSelectOrderOptionalList = BattOverViewDimEnums.getBattOverviewSelectOrderOptionalList();
        Assert.assertEquals(12,battOverviewSelectOrderOptionalList.size());

        String unit = BattOverViewDimEnums.MANU_FACTUER.getUnit();
        Assert.assertSame(unit, null);
    }

    @Test
    public void test2()
    {
        Boolean defaultFixed = BattOverViewDimEnums.SOH.getDefaultFixed();
        Boolean defaultFixed1 = BattOverViewDimEnums.MANU_FACTUER.getDefaultFixed();

        Assert.assertSame(defaultFixed, false);
        Assert.assertSame(defaultFixed1, false);

        String volt = BattOverViewDimEnums.getUnitById("volt");
        Assert.assertEquals("V",volt);

        String volt1 = BattOverViewDimEnums.getUnitById(null);
        Assert.assertSame(null,volt1);

        String volt3 = BattOverViewDimEnums.getUnitById("77");
        Assert.assertSame(null,volt3);
    }

    @Test
    public void test3()
    {
        String nameById = BattOverViewDimEnums.getNameById("startDate");
        Assert.assertEquals("{\"en_US\":\"Date of Activation\",\"zh_CN\":\"启用日期\"}",nameById);

        String alarmNameById = BattOverViewDimEnums.getNameById("alarm");
        Assert.assertEquals("{\"en_US\":\"Alarm\",\"zh_CN\":\"告警\"}",alarmNameById);
    }


    @Test
    public void testGetAll()
    {
        String id = BattOverViewDimEnums.getMatchName("name");
        Assert.assertEquals("{\"en_US\":\"Name\",\"zh_CN\":\"名称\"}",id);
    }

    @Test
    public void testGetAll2()
    {
        Assert.assertTrue(BattOverViewDimEnums.matchTypeById(BattOverViewDimEnums.TYPE.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchSocById(BattOverViewDimEnums.SOC.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchRatedCapById(BattOverViewDimEnums.RATED_CAP.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchSohById(BattOverViewDimEnums.SOH.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchChargeStatusById(BattOverViewDimEnums.CHARGE_STATUS.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchLifeById(BattOverViewDimEnums.LEFT_LIFE.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchRemainingDurationById(BattOverViewDimEnums.REMAIN_DISCHARGE_DURATION.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchAlarmById(BattOverViewDimEnums.ALARM.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchManufacturById(BattOverViewDimEnums.MANU_FACTUER.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchBrandById(BattOverViewDimEnums.BRAND.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchSeriesById(BattOverViewDimEnums.SERIES.getId()));
        Assert.assertTrue(BattOverViewDimEnums.matchModelById(BattOverViewDimEnums.MODEL.getId()));

    }

    @Test
    public void getAllBattStatisticsOverviewDimTest()
    {
        Assert.assertSame(21, BattOverViewDimEnums.getAllBattStatisticsOverviewDim().size());
    }

    @Test
    public void getNameByIdTest()
    {
        Assert.assertSame("", BattOverViewDimEnums.getNameById(null));
        Assert.assertSame("{\"en_US\":\"Name\",\"zh_CN\":\"名称\"}", BattOverViewDimEnums.getNameById("name"));
        Assert.assertSame("", BattOverViewDimEnums.getNameById("sa"));

    }

    @Test
    public void getAllOverviewDimMapTest()
    {
        Assert.assertSame(21, BattOverViewDimEnums.getAllOverviewDimMap().size());
    }

    @Test
    public void sortable_test()
    {
        Boolean sortable = BattOverViewDimEnums.NAME.getSortable();
        Boolean sortable1 = BattOverViewDimEnums.POSITION.getSortable();
        Boolean sortable2 = BattOverViewDimEnums.TYPE.getSortable();
        Boolean sortable3 = BattOverViewDimEnums.VOLT.getSortable();
        Boolean sortable4 = BattOverViewDimEnums.CURR.getSortable();
        Boolean sortable5 = BattOverViewDimEnums.TEMP.getSortable();
        Boolean sortable6 = BattOverViewDimEnums.SOC.getSortable();

        Assert.assertSame(sortable, true);
        Assert.assertSame(sortable1, true);
        Assert.assertSame(sortable2, false);
        Assert.assertSame(sortable3, true);
        Assert.assertSame(sortable4, true);
        Assert.assertSame(sortable5, true);
        Assert.assertSame(sortable6, true);

        Map<String, Boolean> idSortableMap = BattOverViewDimEnums.getIdSortableMap();
        Assert.assertEquals(21,idSortableMap.size());
    }
}
