package com.zte.uedm.battery.enums.backuppower;

import org.junit.Assert;
import org.junit.Test;

public class BackupOverviewExportColumnEnumTest
{
    @Test
    public void test()
    {
        BackupOverviewExportColumnEnum.POSITION.getColumnId();
        BackupOverviewExportColumnEnum.NORMAL.getColumnId();
        BackupOverviewExportColumnEnum.NORMAL_INC.getColumnId();
        BackupOverviewExportColumnEnum.NORMAL_DEC.getColumnName();
        BackupOverviewExportColumnEnum.DEFICIENCY.getColumnName();
        BackupOverviewExportColumnEnum.DEFICIENCY_INC.getColumnName();


        Assert.assertSame(BackupOverviewExportColumnEnum.POSITION.getColumnId(), "position");
        Assert.assertSame(BackupOverviewExportColumnEnum.NORMAL.getColumnId(), "normal");
        Assert.assertSame(BackupOverviewExportColumnEnum.DEFICIENCY.getColumnId(), "deficiency");

        Assert.assertEquals(10,BackupOverviewExportColumnEnum.getAllColumnIdList().size());
        Assert.assertEquals("{\"en_US\":\"position\",\"zh_CN\":\"位置\"}",BackupOverviewExportColumnEnum.getNameById("position"));
        Assert.assertEquals("",BackupOverviewExportColumnEnum.getNameById("xxx"));
        Assert.assertEquals("",BackupOverviewExportColumnEnum.getNameById(null));

    }
}
