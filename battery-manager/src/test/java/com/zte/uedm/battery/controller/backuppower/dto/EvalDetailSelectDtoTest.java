package com.zte.uedm.battery.controller.backuppower.dto;

import com.zte.uedm.battery.bean.BatteryOverviewBean;
import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.battery.enums.backuppower.BackupPowerApplicationSceneEnum;
import com.zte.uedm.battery.enums.backuppower.BackupPowerStateEnum;
import com.zte.uedm.common.enums.SortEnum;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class EvalDetailSelectDtoTest
{
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(EvalDetailSelectDto.class);
        EvalDetailSelectDto bean = new EvalDetailSelectDto();
        bean.checkNotEmpty();
        bean.checkParamsRange();
        bean.setLogicGroupId("123");
        List<BatteryOverviewBean> dims=new ArrayList<>();
        BatteryOverviewBean batteryOverviewBean = new BatteryOverviewBean();
        batteryOverviewBean.setId("name");
        dims.add(batteryOverviewBean);
        bean.setDims(dims);
        bean.setOrder("name");
        bean.setSort("asc");
        bean.setOrder("name");
        bean.setSort("desc");
        bean.setLogicGroupId("1");
        bean.setPageNo(1);
        bean.setPageSize(1);
        bean.checkParamsRange();
        List<String> allackupPowerStateIds = BackupPowerStateEnum.getAllackupPowerStateIds();
        List<String> allIds = BackupPowerApplicationSceneEnum.getAllIds();
        List<String> allNames = EvalDetailSelectEnum.getAllNames();
        List<String> sortList =SortEnum.getSortIdList();
        allackupPowerStateIds.addAll(allIds);
        allackupPowerStateIds.addAll(allNames);
        allackupPowerStateIds.addAll(sortList);
        bean.setApplicationScene(allackupPowerStateIds);
        bean.setSeries(allackupPowerStateIds);
        bean.setBrands(allackupPowerStateIds);
        bean.setManufacturers(allackupPowerStateIds);
        bean.setModels(allackupPowerStateIds);
        bean.setOrder("1");
        bean.setSort("2");
        bean.checkNotEmpty();
        bean.checkParamsRange();
        bean.getLogicGroupId();
        bean.getOrder();
        bean.getSort();
        bean.toString();
        Assert.assertEquals("1", bean.getLogicGroupId());
    }
}
