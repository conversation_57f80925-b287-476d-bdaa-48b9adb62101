package com.zte.uedm.battery.service.impl;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.bean.pv.PvCommonQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvPowerAnalysisResponseBean;
import com.zte.uedm.battery.bean.pv.PvTrendDataBean;
import com.zte.uedm.battery.bean.pv.PvTrendQueryRequestBean;
import com.zte.uedm.battery.dao.PvGenerConsumRecordDao;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import com.zte.uedm.battery.opti.domain.service.AuthorizationService;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.PvGenerConsumRecordService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.constant.PvConstant;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.service.config.optional.MocOptional;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

/**
 * @ Author     ：10260977
 * @ Date       ：17:39 2021/3/16
 * @ Description：测试类
 * @ Modified By：
 * @ Version: 1.0
 */
public class PvPowerAnalysisServiceImplTest
{
    @InjectMocks
    private PvPowerAnalysisServiceImpl pvPowerAnalysisServiceImpl;

    @Mock
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;

    @Mock
    private PvGenerConsumRecordMapper pvGenerConsumRecordMapper;


    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private PvGenerConsumRecordService pvGenerConsumRecordService;

    @Mock
    private I18nUtils i18nUtilsService;
    
    @Mock
    private PvGenerConsumRecordDao pvGenerConsumRecordDao;
    @Mock
    private DeviceCacheManager deviceCacheManager;

    @Mock
    private AuthorizationService authorizationService;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void pvPowerAnalysisOverviewTest() throws UedmException
    {
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = buildMonitorObjectDsBeans();
            List<DeviceDSEntity> monitorObjectDsBeans2 = buildMonitorObjectDsBeans2();
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(monitorObjectDsBeans);
            List<PvPowerAnalysisResponseBean> beans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvId("2");
            pvPowerAnalysisResponseBean.setPvName("1");
            pvPowerAnalysisResponseBean.setPvarrayName("2");
            pvPowerAnalysisResponseBean.setPvmoduleName("3");
            pvPowerAnalysisResponseBean.setPvGeneration("0.00");
            pvPowerAnalysisResponseBean.setDcloadConsumption("0.00");
            beans.add(pvPowerAnalysisResponseBean);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            List<String> siteIds = new ArrayList<>();
            siteIds.add("site-1");
            siteIds.add("site-2");
            Mockito.doReturn(siteIds).when(realGroupRelationSiteUtils).getSiteIds(Mockito.anyList());

            /* Started by AICoder, pid:7809c2c0e1nd32e148b00b9e6091350e78f4ca41 */
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = new HashMap<>();
            FieldEntity siteDsBean = new FieldEntity();
            siteDsBean.setId("site-1");
            siteDsBean.setName("2");
            /* Ended by AICoder, pid:7809c2c0e1nd32e148b00b9e6091350e78f4ca41 */
            siteIdRelationSiteDsBeansMap.put("site-1", siteDsBean);
            Mockito.doReturn(siteIdRelationSiteDsBeansMap).when(realGroupRelationSiteUtils).getAllSiteDsBeanMap();
            List<DeviceEntity> topoEntity = new ArrayList<>();
            DeviceEntity deviceEntity1 = new DeviceEntity();
            deviceEntity1.setMoc(MocOptional.PV.getId());
            deviceEntity1.setName("name");

            topoEntity.add(deviceEntity1);
            DeviceEntity deviceEntity2 = new DeviceEntity();
            deviceEntity2.setMoc(MocOptional.SPCU.getId());
            deviceEntity2.setName("name");
            topoEntity.add(deviceEntity2);

            Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);
            
            Mockito.when(i18nUtilsService.getMapFieldByLanguageOption(Mockito.any(), Mockito.any())).thenReturn("11");
            Map<String, List<DeviceDSEntity>> siteRelatedPvMaps = new HashMap<>();
            siteRelatedPvMaps.put("site-1", monitorObjectDsBeans2);
            Mockito.doReturn(siteRelatedPvMaps).when(realGroupRelationSiteUtils).getsiteRelatedPvMap_new();
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setGrain("11");
            pvCommonQueryRequestBean.setName("1");
            pvCommonQueryRequestBean.setRatioBegin(0.00);
            pvCommonQueryRequestBean.setRatioEnd(80.0);
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("spcu"));
            pvCommonQueryRequestBean.setName("2");
            pvCommonQueryRequestBean.setRatioBegin(null);
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("spu"));
            pvCommonQueryRequestBean.setName("3");
            pvCommonQueryRequestBean.setRatioBegin(0.00);
            pvCommonQueryRequestBean.setRatioEnd(null);
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
            Assert.assertEquals(1,list.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void pvPowerAnalysisOverviewTest_Normal_spcu() throws UedmException
    {
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = buildMonitorObjectDsBeans();
            List<DeviceDSEntity> monitorObjectDsBeans2 = buildMonitorObjectDsBeans2();
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(monitorObjectDsBeans);
            List<PvPowerAnalysisResponseBean> beans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvId("1");
            pvPowerAnalysisResponseBean.setPvName("1");
            pvPowerAnalysisResponseBean.setPvarrayName("2");
            pvPowerAnalysisResponseBean.setPvmoduleName("3");
            pvPowerAnalysisResponseBean.setPvGeneration("0.00");
            pvPowerAnalysisResponseBean.setDcloadConsumption("0.00");
            beans.add(pvPowerAnalysisResponseBean);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            List<String> siteIds = new ArrayList<>();
            siteIds.add("site-1");
            siteIds.add("site-2");
            Mockito.doReturn(siteIds).when(realGroupRelationSiteUtils).getSiteIds(Mockito.anyList());
            List<DeviceEntity> topoEntity = new ArrayList<>();
            DeviceEntity deviceEntity1 = new DeviceEntity();
            deviceEntity1.setMoc(MocOptional.PV.getId());
            deviceEntity1.setName("name");

            topoEntity.add(deviceEntity1);
            DeviceEntity deviceEntity2 = new DeviceEntity();
            deviceEntity2.setMoc(MocOptional.SPCU.getId());
            deviceEntity2.setName("name");
            topoEntity.add(deviceEntity2);

            Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);
            /* Started by AICoder, pid:63d58s0310y7e14141b20930709e47096a94ce9c */
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = new HashMap<>();
            FieldEntity siteDsBean = new FieldEntity();
            siteDsBean.setId("2");
            siteDsBean.setName("2");
            /* Ended by AICoder, pid:63d58s0310y7e14141b20930709e47096a94ce9c */
            siteIdRelationSiteDsBeansMap.put("2", siteDsBean);
            Mockito.doReturn(siteIdRelationSiteDsBeansMap).when(realGroupRelationSiteUtils).getAllSiteDsBeanMap();
            Mockito.when(i18nUtilsService.getMapFieldByLanguageOption(Mockito.any(), Mockito.any())).thenReturn("11");
            Map<String, List<DeviceDSEntity>> siteRelatedPvMaps = new HashMap<>();
            siteRelatedPvMaps.put("site-1", monitorObjectDsBeans2);
            Mockito.doReturn(siteRelatedPvMaps).when(realGroupRelationSiteUtils).getsiteRelatedPvMap_new();
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setName("1");
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            pvCommonQueryRequestBean.setGrain("11");
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("spcu"));
            pvCommonQueryRequestBean.setName("2");
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
            Assert.assertEquals(1,list.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void pvPowerAnalysisOverviewTest_Normal_Spu() throws UedmException
    {
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = buildMonitorObjectDsBeans();
            List<DeviceDSEntity> monitorObjectDsBeans2 = buildMonitorObjectDsBeans2();
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(monitorObjectDsBeans);
            List<PvPowerAnalysisResponseBean> beans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvId("1");
            pvPowerAnalysisResponseBean.setPvName("1");
            pvPowerAnalysisResponseBean.setPvarrayName("2");
            pvPowerAnalysisResponseBean.setPvmoduleName("3");
            pvPowerAnalysisResponseBean.setPvGeneration("0.00");
            pvPowerAnalysisResponseBean.setDcloadConsumption("0.00");
            beans.add(pvPowerAnalysisResponseBean);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            List<String> siteIds = new ArrayList<>();
            siteIds.add("1");
            siteIds.add("2");
            Mockito.doReturn(siteIds).when(realGroupRelationSiteUtils).getSiteIds(Mockito.anyList());
            List<DeviceEntity> topoEntity = new ArrayList<>();
            DeviceEntity deviceEntity1 = new DeviceEntity();
            deviceEntity1.setMoc(MocOptional.PV.getId());
            deviceEntity1.setName("name");

            topoEntity.add(deviceEntity1);
            DeviceEntity deviceEntity2 = new DeviceEntity();
            deviceEntity2.setMoc(MocOptional.SPCU.getId());
            deviceEntity2.setName("name");
            topoEntity.add(deviceEntity2);

            Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);
            /* Started by AICoder, pid:63d58s0310y7e14141b20930709e47096a94ce9c */
            Map<String, FieldEntity> siteIdRelationSiteDsBeansMap = new HashMap<>();
            FieldEntity siteDsBean = new FieldEntity();
            siteDsBean.setId("2");
            siteDsBean.setName("2");
            /* Ended by AICoder, pid:63d58s0310y7e14141b20930709e47096a94ce9c */
            Mockito.doReturn(siteIdRelationSiteDsBeansMap).when(realGroupRelationSiteUtils).getAllSiteDsBeanMap();
            Mockito.when(i18nUtilsService.getMapFieldByLanguageOption(Mockito.any(), Mockito.any())).thenReturn("11");
            Map<String, List<DeviceDSEntity>> siteRelatedPvMaps = new HashMap<>();
            siteRelatedPvMaps.put("1", monitorObjectDsBeans2);
            Mockito.doReturn(siteRelatedPvMaps).when(realGroupRelationSiteUtils).getsiteRelatedPvMap_new();
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("spu"));
            pvCommonQueryRequestBean.setName("3");
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
            Assert.assertEquals(0,list.size());

        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void pvPowerAnalysisOverviewTest_Empty() throws UedmException
    {
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = buildMonitorObjectDsBeans();
            List<DeviceDSEntity> monitorObjectDsBeans2 = buildMonitorObjectDsBeans2();
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setName("1");
            pvCommonQueryRequestBean.setGrain("11");
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(monitorObjectDsBeans);
            //数据库查询为空
            List<DeviceEntity> topoEntity = new ArrayList<>();
            DeviceEntity deviceEntity1 = new DeviceEntity();
            deviceEntity1.setMoc(MocOptional.PV.getId());
            deviceEntity1.setName("name");

            topoEntity.add(deviceEntity1);
            DeviceEntity deviceEntity2 = new DeviceEntity();
            deviceEntity2.setMoc(MocOptional.SPCU.getId());
            deviceEntity2.setName("name");
            topoEntity.add(deviceEntity2);

            Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");
            List<PvPowerAnalysisResponseBean> beans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvId("1");
            pvPowerAnalysisResponseBean.setPvName("1");
            pvPowerAnalysisResponseBean.setPvarrayName("2");
            pvPowerAnalysisResponseBean.setPvmoduleName("3");
            pvPowerAnalysisResponseBean.setPvGeneration("0.00");
            pvPowerAnalysisResponseBean.setDcloadConsumption("0.00");
            beans.add(pvPowerAnalysisResponseBean);
            Mockito.when(i18nUtilsService.getMapFieldByLanguageOption(Mockito.any(), Mockito.any())).thenReturn("11");
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionWithGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            Mockito.when(pvGenerConsumRecordDao.selectOverViewRecordByConditionNoGrain(Mockito.any(),Mockito.any())).thenReturn(beans);
            List<String> siteIds = new ArrayList<>();
            siteIds.add("site-1");
            siteIds.add("site-2");
            Mockito.doReturn(siteIds).when(realGroupRelationSiteUtils).getSiteIds(Mockito.anyList());
            Mockito.doReturn(new HashMap<>()).when(realGroupRelationSiteUtils).getAllSiteDsBeanMap();
            Map<String, List<DeviceDSEntity>> siteRelatedPvMaps = new HashMap<>();
            siteRelatedPvMaps.put("site-1", monitorObjectDsBeans2);
            Mockito.doReturn(siteRelatedPvMaps).when(realGroupRelationSiteUtils).getsiteRelatedPvMap_new();
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
            Assert.assertEquals(0,list.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void pvPowerAnalysisOverviewTest_Exc() throws UedmException
    {
        try
        {
            List<MonitorObjectDsBean> monitorObjectDsBeans = buildMonitorObjectDsBeans();
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200,""));
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setName("1");
            pvCommonQueryRequestBean.setGrain("11");
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
            Assert.assertEquals(0,list.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void pvPowerAnalysisOverviewTest_NoAuth()
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setName("1");
            pvCommonQueryRequestBean.setGrain("11");
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(new ArrayList<>());
            List<PvPowerAnalysisResponseBean> list = pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10, "");
        } catch (UedmException e)
        {
            Assert.assertEquals(-635, e.getErrorId().intValue());
        }
    }

    @Test
    public void computeGenerConsumRatioTest() throws UedmException
    {
        try
        {
            Mockito.when(configurationManagerRpcImpl.selectMoByMocAndPosition(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvCommonQueryRequestBean.setPvTypes(Lists.newArrayList("pv"));
            pvCommonQueryRequestBean.setName("1");
            pvCommonQueryRequestBean.setGrain("11");
            pvCommonQueryRequestBean.setRatioEnd(30.00);
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(Arrays.asList("11"));
            pvPowerAnalysisServiceImpl.pvPowerAnalysisOverview(pvCommonQueryRequestBean, "11", 1, 10,"");

            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvId("11");
            pvPowerAnalysisResponseBean.setPvName("1");
            pvPowerAnalysisResponseBean.setPvarrayName("2");
            pvPowerAnalysisResponseBean.setPvmoduleName("3");
            pvPowerAnalysisResponseBean.setPvGeneration("2.00");
            pvPowerAnalysisResponseBean.setDcloadConsumption("9.00");

            pvPowerAnalysisServiceImpl.computeGenerConsumRatio(pvPowerAnalysisResponseBean, pvCommonQueryRequestBean);
            pvCommonQueryRequestBean.setRatioBegin(1.00);
            pvPowerAnalysisServiceImpl.computeGenerConsumRatio(pvPowerAnalysisResponseBean, pvCommonQueryRequestBean);
            pvCommonQueryRequestBean.setRatioEnd(null);
            pvPowerAnalysisServiceImpl.computeGenerConsumRatio(pvPowerAnalysisResponseBean, pvCommonQueryRequestBean);
            pvCommonQueryRequestBean.setRatioBegin(30.00);
            pvPowerAnalysisServiceImpl.computeGenerConsumRatio(pvPowerAnalysisResponseBean, pvCommonQueryRequestBean);
            pvPowerAnalysisServiceImpl.computeGenerConsumRatio(pvPowerAnalysisResponseBean, new PvCommonQueryRequestBean());

        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @NotNull
    private List<MonitorObjectDsBean> buildMonitorObjectDsBeans()
    {
        List<MonitorObjectDsBean> monitorObjectDsBeans = new ArrayList<>();
        MonitorObjectDsBean pv1 = new MonitorObjectDsBean();
        pv1.setId("1");
        pv1.setMoc("r32.uedm.device.solar");
        pv1.setPath("1/2/3/4");
        pv1.setPathId("1/2/3/4");
        pv1.setName("11");
        MonitorObjectDsBean pv2 = new MonitorObjectDsBean();
        pv2.setId("2");
        pv2.setPath("1/2/3/4");
        pv2.setPathId("1/2/3/4");
        pv2.setName("22");
        pv2.setMoc("r32.uedm.device.solar");
        monitorObjectDsBeans.add(pv1);
        monitorObjectDsBeans.add(pv2);
        return monitorObjectDsBeans;
    }
    /* Started by AICoder, pid:f2f76c2509ac1b0140bf0aff30b9c41359d77964 */
    private List<DeviceDSEntity> buildMonitorObjectDsBeans2()
    {
        List<DeviceDSEntity> monitorObjectDsBeans = new ArrayList<>();
        DeviceDSEntity pv1 = new DeviceDSEntity();
        pv1.setId("1");
        pv1.setPathId("1/2/3/4".split("/"));
        pv1.setName("11");
        DeviceDSEntity pv2 = new DeviceDSEntity();
        pv2.setId("2");
        pv2.setPathId("1/2/3/4".split("/"));
        pv2.setName("22");
        monitorObjectDsBeans.add(pv1);
        monitorObjectDsBeans.add(pv2);
        return monitorObjectDsBeans;
    }
    /* Ended by AICoder, pid:f2f76c2509ac1b0140bf0aff30b9c41359d77964 */
    @Test
    public void buildSpuInfo_test(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setMoc(MocOptional.PV.getId());
        deviceEntity1.setName("name");

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setTopo(topo);
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpcuInfo(MocOptional.SPCU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test1(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setMoc(MocOptional.PV.getId());
        deviceEntity1.setName("name");
        String[] topo2 = {"id"};
        deviceEntity1.setTopo(topo2);

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        String[] topo1 = {"id"};
        deviceEntity2.setTopo(topo1);
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setTopo(topo);
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpuInfo(MocOptional.SPU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test7(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setName("name");
        String[] topo2 = {"id"};
        deviceEntity1.setTopo(topo2);
        deviceEntity1.setId("id1");

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        String[] topo1 = {"id"};
        deviceEntity2.setTopo(topo1);
        deviceEntity2.setId("id");
        topoEntity.add(deviceEntity2);
        DeviceEntity deviceEntity3 = new DeviceEntity();
        deviceEntity3.setMoc(MocOptional.PV.getId());
        deviceEntity3.setName("name");
        String[] topo3 = {"id2"};
        deviceEntity3.setTopo(topo3);
        deviceEntity3.setId("ID");
        topoEntity.add(deviceEntity3);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);
        Mockito.when(deviceCacheManager.getDeviceByMoc(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setTopo(topo);
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpuInfo(MocOptional.SPU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test8(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setName("name");
        String[] topo2 = {"id"};
        deviceEntity1.setTopo(topo2);

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        String[] topo1 = {"id"};
        deviceEntity2.setTopo(topo1);
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setTopo(topo);
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpuInfo(MocOptional.SPU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test3(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setMoc(MocOptional.PV.getId());
        deviceEntity1.setName("name");

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpcuInfo(MocOptional.SPCU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test2(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setMoc(MocOptional.PV.getId());
        deviceEntity1.setName("name");
        String[] topo2 = {"id"};

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setMoc(MocOptional.SPCU.getId());
        deviceEntity2.setName("name");
        String[] topo1 = {"id"};
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpuInfo(MocOptional.SPU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void buildSpuInfo_test9(){
        List<DeviceEntity> topoEntity = new ArrayList<>();
        DeviceEntity deviceEntity1 = new DeviceEntity();
        deviceEntity1.setName("name");
        String[] topo2 = {"id"};

        topoEntity.add(deviceEntity1);
        DeviceEntity deviceEntity2 = new DeviceEntity();
        deviceEntity2.setName("name");
        String[] topo1 = {"id"};
        topoEntity.add(deviceEntity2);

        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(topoEntity);

        List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
        PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
        pvPowerAnalysisResponseBean.setPvId("ID");
        pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
        Map<String, MonitorObjectDsBean> idBeanMap = new HashMap<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        String[] topo = {"id"};
        monitorObjectDsBean.setName("name");
        monitorObjectDsBean.setPath("site/pv");
        monitorObjectDsBean.setPathId("siteId/pvID");
        idBeanMap.put("ID",monitorObjectDsBean);
        Map<String, String> pvIdSiteNameMap = new HashMap<>();
        List<PvPowerAnalysisResponseBean> result = pvPowerAnalysisServiceImpl.buildSpuInfo(MocOptional.SPU.getId(),"name",idBeanMap,pvPowerAnalysisResponseBeans,pvIdSiteNameMap,"ch-ZH");
        Assert.assertTrue(result != null);
    }
    @Test
    public void pvPowerAnalysisTrendTest_Normal() throws UedmException
    {
        try
        {
            MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
            monitorObjectBean.setId("11");
            monitorObjectBean.setName("11");
            monitorObjectBean.setMoc("r32.uedm.device.solar");
            Mockito.when(configurationService.getMonitorObjectById(Mockito.any())).thenReturn(monitorObjectBean);
            //请求bean
            PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
            pvTrendQueryRequestBean.setPvId("11");
            pvTrendQueryRequestBean.setCompareYear("2021");
            pvTrendQueryRequestBean.setGrain("d");
            pvTrendQueryRequestBean.setEndTime("2022-03-10");
            pvTrendQueryRequestBean.setStartTime("2022-01-10");
            List<PvTrendDataBean> beans = new ArrayList<>();
            List<PvTrendDataBean> beancurrs = new ArrayList<>();
            PvTrendDataBean pvTrendDataBean = new PvTrendDataBean();
            pvTrendDataBean.setRecordDate("2022-03-10");
            pvTrendDataBean.setPvGeneration("0.00");
            pvTrendDataBean.setDcloadConsumption("0.00");
            beans.add(pvTrendDataBean);
            PvTrendDataBean pvTrendDataBean2 = new PvTrendDataBean();
            pvTrendDataBean2.setRecordDate("2022-03-11");
            pvTrendDataBean2.setPvGeneration("0.00");
            pvTrendDataBean2.setDcloadConsumption("0.00");
            beancurrs.add(pvTrendDataBean);
            beancurrs.add(pvTrendDataBean2);
            //空测试
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(Arrays.asList("11"));
            pvPowerAnalysisServiceImpl.pvPowerAnalysisTrend(pvTrendQueryRequestBean, "11", 1, 10,"");
            Mockito.when(pvGenerConsumRecordMapper.selectTrendRecordByCondition(Mockito.any())).thenReturn(beancurrs).thenReturn(beans);
            pvPowerAnalysisServiceImpl.pvPowerAnalysisTrend(pvTrendQueryRequestBean, "11", 1, 10,"");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void pvPowerAnalysisTrendTest_Year() throws UedmException
    {
        try
        {
            MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
            monitorObjectBean.setId("11");
            monitorObjectBean.setName("11");
            monitorObjectBean.setMoc(MocOptional.PV.getId());
            Mockito.when(configurationService.getMonitorObjectById(Mockito.any())).thenReturn(monitorObjectBean);
            //请求bean
            PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
            pvTrendQueryRequestBean.setPvId("11");
            pvTrendQueryRequestBean.setCompareYear("2021");
            pvTrendQueryRequestBean.setGrain("y");
            pvTrendQueryRequestBean.setEndTime("2022-03-10");
            pvTrendQueryRequestBean.setStartTime("2022-01-10");
            List<PvTrendDataBean> beans = new ArrayList<>();
            List<PvTrendDataBean> beancurrs = new ArrayList<>();
            PvTrendDataBean pvTrendDataBean = new PvTrendDataBean();
            pvTrendDataBean.setRecordDate("2022-03-10");
            pvTrendDataBean.setPvGeneration("0.00");
            pvTrendDataBean.setDcloadConsumption("0.00");
            beans.add(pvTrendDataBean);
            PvTrendDataBean pvTrendDataBean2 = new PvTrendDataBean();
            pvTrendDataBean2.setRecordDate("2022-03-11");
            pvTrendDataBean2.setPvGeneration("0.00");
            pvTrendDataBean2.setDcloadConsumption("0.00");
            beancurrs.add(pvTrendDataBean2);
            //空测试
            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(Arrays.asList("11"));
            Mockito.when(pvGenerConsumRecordMapper.selectTrendRecordByCondition(Mockito.any())).thenReturn(beancurrs).thenReturn(new ArrayList<>());
            pvPowerAnalysisServiceImpl.pvPowerAnalysisTrend(pvTrendQueryRequestBean, "11", 1, 10,"");

            Mockito.when(pvGenerConsumRecordMapper.selectTrendRecordByCondition(Mockito.any())).thenReturn(beancurrs).thenReturn(beans);
            pvPowerAnalysisServiceImpl.pvPowerAnalysisTrend(pvTrendQueryRequestBean, "11", 1, 10,"");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void pvPowerAnalysisTrendTest_NoAuth()
    {
        try
        {
            //请求bean
            PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
            pvTrendQueryRequestBean.setPvId("11");
            pvTrendQueryRequestBean.setCompareYear("2021");
            pvTrendQueryRequestBean.setGrain("y");
            pvTrendQueryRequestBean.setEndTime("2022-03-10");
            pvTrendQueryRequestBean.setStartTime("2022-01-10");

            Mockito.when(authorizationService.getFullPermissionByResIds(Mockito.any())).thenReturn(new ArrayList<>());
            pvPowerAnalysisServiceImpl.pvPowerAnalysisTrend(pvTrendQueryRequestBean, "11", 1, 10,"");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-635,e.getErrorId().intValue());
        }
    }

    @Test
    public void sortByGenerConsumRatioTest_Normal() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.asc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.GENER_CONSUM_RATIO);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);

            pvCommonQueryRequestBean.setSort(SortEnum.desc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.GENER_CONSUM_RATIO);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void sortByGenerConsumRatioTest_Normal1() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.desc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.GENER_CONSUM_RATIO);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setGenerConsumRatio("100.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean1 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean1.setGenerConsumRatio("113.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean2 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean2.setGenerConsumRatio("62.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean3 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean3.setGenerConsumRatio("88.00");
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean1);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean2);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean3);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            Assert.assertEquals("113.00",pvPowerAnalysisResponseBeans.get(0).getGenerConsumRatio());
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void sortByGenerConsumRatioTest_Normal2() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.desc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.SOLAR_GENERATION);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvGeneration("100.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean1 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean1.setPvGeneration("113.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean2 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean2.setPvGeneration("62.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean3 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean3.setPvGeneration("88.00");
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean1);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean2);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean3);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            Assert.assertEquals("113.00",pvPowerAnalysisResponseBeans.get(0).getPvGeneration());
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void sortByGenerConsumRatioTest_Normal3() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.desc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.DCLOAD_CONSUMPTION);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setDcloadConsumption("100.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean1 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean1.setDcloadConsumption("113.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean2 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean2.setDcloadConsumption("62.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean3 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean3.setDcloadConsumption("88.00");
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean1);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean2);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean3);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            Assert.assertEquals("113.00",pvPowerAnalysisResponseBeans.get(0).getDcloadConsumption());
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void sortByGenerConsumRatioTest_Normal4() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.asc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.SOLAR_GENERATION);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setPvGeneration("100.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean1 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean1.setPvGeneration("113.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean2 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean2.setPvGeneration("62.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean3 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean3.setPvGeneration("88.00");
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean1);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean2);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean3);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            Assert.assertEquals("62.00",pvPowerAnalysisResponseBeans.get(0).getPvGeneration());
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void sortByGenerConsumRatioTest_Normal5() throws UedmException
    {
        try
        {
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setSort(SortEnum.asc.getId());
            pvCommonQueryRequestBean.setOrder(PvConstant.DCLOAD_CONSUMPTION);
            List<PvPowerAnalysisResponseBean> pvPowerAnalysisResponseBeans = new ArrayList<>();
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean.setDcloadConsumption("100.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean1 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean1.setDcloadConsumption("113.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean2 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean2.setDcloadConsumption("62.00");
            PvPowerAnalysisResponseBean pvPowerAnalysisResponseBean3 = new PvPowerAnalysisResponseBean();
            pvPowerAnalysisResponseBean3.setDcloadConsumption("88.00");
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean1);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean2);
            pvPowerAnalysisResponseBeans.add(pvPowerAnalysisResponseBean3);
            pvPowerAnalysisServiceImpl.sortByGenerConsumRatio(pvCommonQueryRequestBean, pvPowerAnalysisResponseBeans);
            Assert.assertEquals("62.00",pvPowerAnalysisResponseBeans.get(0).getDcloadConsumption());
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void getMoPositionByPathTest() throws UedmException
    {
        try
        {
            MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
            monitorObjectDsBean.setPath("");
            pvPowerAnalysisServiceImpl.getMoPositionByPath(monitorObjectDsBean, "2");
            monitorObjectDsBean.setMoc(MocType.SPU);
            monitorObjectDsBean.setPath("1/2/3/4");
            pvPowerAnalysisServiceImpl.getMoPositionByPath(monitorObjectDsBean,"2");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

}
