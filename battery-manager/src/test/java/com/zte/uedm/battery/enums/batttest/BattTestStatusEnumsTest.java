package com.zte.uedm.battery.enums.batttest;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class BattTestStatusEnumsTest {

    @Test
    public void test()
    {
        List<IdNameBean> allIdName = BattTestStatusEnums.getAllIdName();
        List<String> allId = BattTestStatusEnums.getAllId();
        BattTestStatusEnums.COMMAND_ISSUING.getId();
        BattTestStatusEnums.TEST_END.getId();
        BattTestStatusEnums.TEST_EXIT.getId();
        BattTestStatusEnums.COMMAND_ISSUING.getName();
        BattTestStatusEnums.TEST_END.getName();
        BattTestStatusEnums.TEST_EXIT.getName();
        String Delivered = BattTestStatusEnums.getNameById("Delivered");
        Assert.assertEquals("{\"zh_CN\":\"手工测试任务已下发给电池，待电池完成测试后，会在测试历史中呈现测试结果\",\"en_US\":\"The manual test task has been Delivered to the battery, and after the battery completes the test, the test results will be presented in the test history.\"}",Delivered);
    }

    @Test
    public void test1()
    {
        List<String> list = BattTestStatusEnums.getAllId();
        List<BattTestStatusEnums> battTestStatusEnums = Arrays.asList(BattTestStatusEnums.values());

        Assert.assertEquals(4,battTestStatusEnums.size());
    }

}
