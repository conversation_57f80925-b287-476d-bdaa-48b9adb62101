package com.zte.uedm.battery.a_interfaces.safe.web.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class VOTest {

    @Test
    public void BatteryUnlockRecordVoTest() throws Exception {
        BatteryUnlockRecordVo vo = new BatteryUnlockRecordVo();
        PojoTestUtil.TestForPojo(BatteryUnlockRecordVo.class);
        Assert.assertNotEquals("",vo.toString());
    }

    @Test
    public void BatteryUnlockRequestTest() throws Exception {
        BatteryUnlockRequest request = new BatteryUnlockRequest();
        PojoTestUtil.TestForPojo(BatteryUnlockRequest.class);
        Assert.assertNotEquals("",request.toString());
    }
}
