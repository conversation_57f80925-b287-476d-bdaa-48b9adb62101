package com.zte.uedm.battery.enums;

import org.junit.Assert;
import org.junit.Test;


public class TieredModeEnumTest {

    @Test
    public void tieredModeTest() {
        TieredModeEnum tieredModeEnum = TieredModeEnum.MONTH;
        Assert.assertEquals("month", tieredModeEnum.getTieredMode());
        Assert.assertEquals(Integer.valueOf(0), tieredModeEnum.getTieredModeCode());

        TieredModeEnum tieredModeEnumYear = TieredModeEnum.getByTieredMode("year");
        Assert.assertEquals("year", tieredModeEnumYear.getTieredMode());
        Assert.assertEquals(Integer.valueOf(1), tieredModeEnumYear.getTieredModeCode());
    }
}