package com.zte.uedm.battery.opti.infrastructure.repository.persistence;

import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskRuleSourceMapper;
import com.zte.uedm.battery.opti.infrastructure.repository.po.BattRiskRuleSourcePo;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BattRiskRuleSourceRepositoryImplTest
{
    @InjectMocks
    private BattRiskRuleSourceRepositoryImpl battRiskRuleSourceRepository;

    @Mock
    private BattRiskRuleSourceMapper battRiskRuleSourceMapper;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectByIds_ids_is_empty_Test() throws Exception
    {
        Assert.assertTrue(battRiskRuleSourceRepository.selectByIds(new ArrayList<>()).isEmpty());
    }

    @Test
    public void selectByIds_normal_Test() throws Exception
    {
        List<String> ids = Arrays.asList("id");

        List<BattRiskRuleSourcePo> pos = Arrays.asList(new BattRiskRuleSourcePo());
        Mockito.doReturn(pos).when(battRiskRuleSourceMapper).selectByIds(Mockito.anyList());

        Assert.assertTrue(battRiskRuleSourceRepository.selectByIds(ids).size() == 1);
    }

    @Test
    public void selectByIds_sql_exception_Test() throws Exception
    {
        try {
            List<String> ids = Arrays.asList("id");

            Mockito.doThrow(new RuntimeException("sql exception")).when(battRiskRuleSourceMapper).selectByIds(Mockito.anyList());

            Assert.assertTrue(battRiskRuleSourceRepository.selectByIds(ids).size() == 1);
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-1), e.getErrorId());
        }
    }


    @Test
    public void selectAll_normal_Test() throws Exception
    {

        List<BattRiskRuleSourcePo> pos = Arrays.asList(new BattRiskRuleSourcePo());
        Mockito.doReturn(pos).when(battRiskRuleSourceMapper).selectAllResouce();

        Assert.assertTrue(battRiskRuleSourceRepository.selectAll().size() == 1);
    }

    @Test
    public void selectAll_sql_exception_Test()
    {
        try {

            Mockito.doThrow(new RuntimeException("sql exception")).when(battRiskRuleSourceMapper).selectAllResouce();

            Assert.assertTrue(battRiskRuleSourceRepository.selectAll().size() == 1);
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-1), e.getErrorId());
        }
    }

}
