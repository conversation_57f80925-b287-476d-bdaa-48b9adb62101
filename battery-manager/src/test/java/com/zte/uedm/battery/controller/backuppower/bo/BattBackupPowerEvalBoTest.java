package com.zte.uedm.battery.controller.backuppower.bo;

import com.zte.uedm.battery.bean.BattBackupPowerEvalPojo;
import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class BattBackupPowerEvalBoTest
{
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(BattBackupPowerEvalBo.class);
        BattBackupPowerEvalBo bean = new BattBackupPowerEvalBo(new BattBackupPowerEvalPojo());
        bean.setStatus("1");
        bean.toString();
        Assert.assertEquals("1", bean.getStatus());
    }
}
