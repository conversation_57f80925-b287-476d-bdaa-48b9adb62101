package com.zte.uedm.battery.controller.battHealth;

import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.BatteryConfigSearchBean;
import com.zte.uedm.battery.bean.BatteryHealthDimensionsBean;
import com.zte.uedm.battery.controller.batthealth.BatteryHealthConfigController;
import com.zte.uedm.battery.controller.batthealth.dto.BatteryHealthDimensionsUpdateDto;
import com.zte.uedm.battery.service.BattHealthConfigService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @FileDesc :
 * <AUTHOR> 00253634
 * @date Date : 2023年03月02日 下午5:51
 * @Version : 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ Tools.class})
public class BatteryHealthConfigControllerTest
{
    @InjectMocks
    private BatteryHealthConfigController batteryHealthConfigController;
    @Mock
    private BattHealthConfigService battHealthConfigService;
    @Mock
    private JsonService jsonService;
    @Mock
    private MessageSenderService msgSenderService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectHealthListConfig() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BatteryHealthDimensionsBean> pageList = new PageInfo<>();
        pageList.setTotal(100);
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("admin");
        List<BatteryHealthDimensionsBean> beanList = new ArrayList<>();
        BatteryHealthDimensionsBean bean = new BatteryHealthDimensionsBean();
        bean.setUserName("apiuser");
        beanList.add(bean);
        doReturn(beanList).when(battHealthConfigService).selectHealthListConfig(Mockito.anyObject(), anyObject());
        ResponseBean responseBean = batteryHealthConfigController.selectHealthListConfig(1,2,request,"");
        assertSame(0, responseBean.getCode());
    }

    @Test
    public void test_updateBatteryHealthConfig() throws Exception
    {
        UedmException flag=null;
        try{
            List<BatteryHealthDimensionsUpdateDto> updateBeanList = new ArrayList<>();
            BatteryHealthDimensionsUpdateDto updateBean = new BatteryHealthDimensionsUpdateDto();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBeanList.add(updateBean);
            HttpServletRequest request = mock(HttpServletRequest.class);
            PowerMockito.mockStatic(Tools.class);
            PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("admin");
            doReturn(10).when(battHealthConfigService).updateBatteryHealthConfig(anyObject(),anyString());
            ResponseBean responseBean = batteryHealthConfigController.updateBatteryHealthConfig(updateBeanList,request,"");

        }catch (UedmException e){
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void test_updateBatteryHealthConfig_userName_null() throws Exception
    {
        UedmException flag=null;
        try{
            List<BatteryHealthDimensionsUpdateDto> updateBeanList = new ArrayList<>();
            BatteryHealthDimensionsUpdateDto updateBean = new BatteryHealthDimensionsUpdateDto();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBeanList.add(updateBean);
            HttpServletRequest request = mock(HttpServletRequest.class);
            PowerMockito.mockStatic(Tools.class);
            PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("");
            doReturn(10).when(battHealthConfigService).updateBatteryHealthConfig(anyObject(),anyString());
            ResponseBean responseBean = batteryHealthConfigController.updateBatteryHealthConfig(updateBeanList,request,"");

        }catch (UedmException e){
            Assert.assertEquals("userName is null",e.getMessage());
        }
    }

    @Test
    public void test_searchHealthConfig() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("admin");
        PageInfo<BatteryHealthDimensionsBean> page = new PageInfo<>();
        page.setTotal(5);
        BatteryConfigSearchBean searchBean = new BatteryConfigSearchBean();
        searchBean.setName("类型");
        when(battHealthConfigService.searchHealthConfig(anyObject(), anyObject(), anyObject())).thenReturn(page);
        ResponseBean responseBean = batteryHealthConfigController.searchHealthConfig(searchBean,request,"");
        assertSame(null, responseBean.getMessage());
    }

    @Test
    public void test_searchHealthConfig_EXC() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("admin");
        PageInfo<BatteryHealthDimensionsBean> page = new PageInfo<>();
        page.setTotal(5);
        BatteryConfigSearchBean searchBean = new BatteryConfigSearchBean();
        searchBean.setName("类型");
        when(battHealthConfigService.searchHealthConfig(anyObject(), anyObject(), anyObject())).thenThrow(new UedmException(-1,"xxx"));
        ResponseBean responseBean = batteryHealthConfigController.searchHealthConfig(searchBean,request,"");
        assertSame("xxx", responseBean.getMessage());
    }

    @Test
    public void test_updateBatteryHealthConfig1() throws Exception
    {
        UedmException flag = null;
        try
        {
            List<BatteryHealthDimensionsUpdateDto> updateBeanList = new ArrayList<>();
            BatteryHealthDimensionsUpdateDto updateBean = new BatteryHealthDimensionsUpdateDto();
            updateBean.setId("1");
            updateBean.setSequence(1);
            updateBean.setEnable(false);
            updateBeanList.add(updateBean);

            HttpServletRequest request = mock(HttpServletRequest.class);
            PowerMockito.mockStatic(Tools.class);
            PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("admin");
            List<BatteryHealthDimensionsBean> batteryHealthList = new ArrayList<>();
            BatteryHealthDimensionsBean bean = new BatteryHealthDimensionsBean();
            bean.setId("1");
            bean.setSequence(1);
            bean.setDefaultFixed(true);
            bean.setEnable(true);
            batteryHealthList.add(bean);
            doReturn(batteryHealthList).when(battHealthConfigService).selectHealthListConfig(anyObject(), anyObject());
            doReturn(1).when(battHealthConfigService).updateBatteryHealthConfig(anyObject(), anyObject());
            ResponseBean responseBean = batteryHealthConfigController.updateBatteryHealthConfig(updateBeanList, request,
                    "");

        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }
}
