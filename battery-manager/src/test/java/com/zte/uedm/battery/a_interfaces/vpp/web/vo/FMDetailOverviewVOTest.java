package com.zte.uedm.battery.a_interfaces.vpp.web.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/* Started by AICoder, pid:e649aea06f766c9145b80bd260f5dc0fe78988c0 */
public class FMDetailOverviewVOTest {

    @Test
    public void FMDetailOverviewVOTest() throws Exception {
        FMDetailOverviewVO fmDetailOverviewVO = new FMDetailOverviewVO();
        PojoTestUtil.TestForPojo(fmDetailOverviewVO.getClass());
        Assert.assertEquals(fmDetailOverviewVO.toString(), new FMDetailOverviewVO().toString());
    }
}
/* Ended by AICoder, pid:e649aea06f766c9145b80bd260f5dc0fe78988c0 */
