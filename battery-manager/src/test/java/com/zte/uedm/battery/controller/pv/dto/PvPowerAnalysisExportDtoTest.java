package com.zte.uedm.battery.controller.pv.dto;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;

public class PvPowerAnalysisExportDtoTest
{
    @Test
    public void test()
    {
        PvPowerAnalysisExportDto dto = new PvPowerAnalysisExportDto();
        dto.setGrain("day");
        dto.setEndTime("77");
        dto.setPositions(new ArrayList<>());
        dto.setStartTime("fk");
        dto.toString();

        Assert.assertEquals("day",dto.getGrain());
    }
}
