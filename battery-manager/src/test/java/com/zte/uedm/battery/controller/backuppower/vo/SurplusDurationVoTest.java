package com.zte.uedm.battery.controller.backuppower.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;


public class SurplusDurationVoTest
{
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(SurplusDurationVo.class);
        Map<String, Double> value = new HashMap<>();
        value.put("Li",2.5);
        SurplusDurationVo bean = new SurplusDurationVo(value);
        bean.setUnit("1");
        bean.setValue(value);

        bean.toString();
        Assert.assertEquals("1", bean.getUnit());
    }
}
