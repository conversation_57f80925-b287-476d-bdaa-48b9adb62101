package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

/* Started by AICoder, pid:f835f958fc6a4639ae62dce310e2c031 */
import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Test;
import static org.junit.Assert.*;

public class SeasonStrategyForTemplateEntityTest {
    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(SeasonStrategyForTemplateEntity.class);
    }
}
/* Ended by AICoder, pid:f835f958fc6a4639ae62dce310e2c031 */