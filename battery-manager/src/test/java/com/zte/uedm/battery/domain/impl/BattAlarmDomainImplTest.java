package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.AlarmRpc;
import com.zte.uedm.battery.rpc.impl.ActiveAlarmServiceV2RpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.AlarmPgCacheService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BattAlarmDomainImplTest
{

    @InjectMocks
    private BattAlarmDomainImpl battAlarmDomain;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private AlarmRpc alarmRpc;
    @Mock
    private DataRedis dataRedis;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private ActiveAlarmServiceV2RpcImpl activeAlarmServiceV2RpcImpl;

    @Mock
    private AlarmPgCacheService alarmPgCacheService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectLevelTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battAlarmDomain.selectAlarmLevels(null).getTotal());
    }

    @Test
    public void selectLevelTest_normal() throws Exception
    {
        Mockito.doReturn("d").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(), Mockito.anyString());
        Assert.assertSame(5l, battAlarmDomain.selectAlarmLevels(new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void selectAlarmsByMoIdTest_param_blank() throws Exception
    {
        Assert.assertSame(0, battAlarmDomain.selectAlarmsByMoId(null, "").size());
    }

    @Test
    public void selectAlarmsByMoIdsTest_param_blank() throws Exception
    {
        Assert.assertSame(0, battAlarmDomain.selectAlarmsByMoIds(null, "").size());
    }

    @Test
    public void selectAlarmsByMoIdTest_normal() throws Exception
    {


        AlarmResponse alarmResponse = new AlarmResponse();
        List<Alarm> alarms = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setMe("11");
        alarm.setAlarmraisedtime(1618984688621L);
        alarm.setId(100L);
        alarm.setAlarmcode(123L);
        alarms.add(alarm);

        alarmResponse.setAlarms(alarms);
        alarmResponse.setTotalcount(1);
        Response<AlarmResponse> response = Response.success(alarmResponse);
        Call<AlarmResponse> responseBeanCall = mock(Call.class);
        when(alarmRpc.getActiveAlarm(Mockito.any(), Mockito.anyString())).thenReturn(responseBeanCall);

        Assert.assertSame(0, battAlarmDomain.selectAlarmsByMoId("moId", "").size());
    }

    @Test
    public void selectAlarmsByMoIdsTest_normal() throws Exception
    {
        Assert.assertSame(0, battAlarmDomain.selectAlarmsByMoIds(Arrays.asList("11"), "").size());
    }


    @Test
    public void selectMostAlarmLevelInListTest_param_blank() throws Exception
    {
        Assert.assertSame(-1, battAlarmDomain.selectMostAlarmLevelInList(null));
        AlarmDTO alarm = new AlarmDTO();
        alarm.setPerceivedseverity(3);
        Assert.assertSame(3, battAlarmDomain.selectMostAlarmLevelInList(Arrays.asList(alarm)));
    }

    @Test
    public void selectAlarmsByMoIdsTest() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "1");
        map.put("timestamp","1702488115020");
        batteryStdRealData.put( "batteryset.batt.curr.imbl.alarm", map);
        batteryStdRealData.put( "batteryset.batt.detect.abnormal.alarm", map);
        batteryStdRealData.put( "battery.cell.poor.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.anyList())).thenReturn(batteryStdRealData);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        nameMap.put("batt.cell.poor.alarm",stringHashMap);
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(1, result.size());
    }
    /* Started by AICoder, pid:84687x7fd2sf704140f40b62b0202e2979733c55 */
    @Test
    public void selectAlarmsByMoIdsTest1() throws UedmException {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "1");
        map.put("timestamp","1702488115020");
        batteryStdRealData.put( "batteryset.batt.curr.imbl.alarm", map);
        batteryStdRealData.put( "batteryset.batt.detect.abnormal.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.anyList())).thenReturn(batteryStdRealData);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        nameMap.put("batt.cell.poor.alarm",stringHashMap);
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(1, result.size());
    }
    /* Ended by AICoder, pid:84687x7fd2sf704140f40b62b0202e2979733c55 */

    @Test
    public void selectAlarmsByMoIdsTestEmpty() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "1");
        map.put("timestamp","1702488115020");
        batteryStdRealData.put( "batteryset.batt.curr.imbl.alarm", map);
        batteryStdRealData.put( "batteryset.batt.detect.abnormal.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.any())).thenReturn(batteryStdRealData);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(0, result.size());
    }

    @Test
    public void selectAlarmsByMoIdsTestFalse() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "0");
        map.put("timestamp","1702488115020");
        batteryStdRealData.put( "batt.cell.poor.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.any())).thenReturn(batteryStdRealData);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Assert.assertSame(1, result.size());
    }

    @Test
    public void selectAlarmsByMoIdsTestNull() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "3");
        batteryStdRealData.put( "battpack.batt.curr.imbl.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.any())).thenReturn(batteryStdRealData);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void selectAlarmsByMoIdsTestNull1() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "0");
        batteryStdRealData.put( "batt", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.any())).thenReturn(batteryStdRealData);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void selectAlarmsByMoIdsTestNull2() throws UedmException
    {
        List<String> moIds = new ArrayList<>();
        moIds.add("id");
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", "3");
        map.put("timestamp","1702488115020");
        batteryStdRealData.put( "battpack.batt.detect.abnormal.alarm", map);
        when(dataRedis.selectRealData(Mockito.anyString(), Mockito.any())).thenReturn(batteryStdRealData);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("zh_CN","1111");
        stringHashMap.put("en_US","1111");
        BatteryEvalDTO dto = new BatteryEvalDTO();
        HashMap<String, BatteryEvalDTO> hashMap = new HashMap<>();
        hashMap.put("id",dto);
        when(configurationManagerRpc.selectBattPackObject(Mockito.any(), Mockito.any())).thenReturn("a");
        Map<String, Boolean> result = battAlarmDomain.selectHealthAlarmByMoIds(moIds,hashMap,nameMap);
        Assert.assertSame(1, result.size());
    }
}
