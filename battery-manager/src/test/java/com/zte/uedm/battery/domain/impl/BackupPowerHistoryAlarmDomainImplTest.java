package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.api.BattConst;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.po.BackupPowerBean;
import com.zte.uedm.battery.domain.po.BatteryBackupPowerBean;
import com.zte.uedm.battery.mapper.BattAlarmMapper;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.AlarmService;
import com.zte.uedm.battery.service.SystemConfigService;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorDeviceObjectRelationBean;
import com.zte.uedm.common.consts.standardpoint.BattStandardPointConstants;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.service.config.optional.StandPointOptional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ DateUtils.class })
public class BackupPowerHistoryAlarmDomainImplTest
{

    private static final Object ERROR_CODE_1 = "-3";
    private static final String BATTPACK_CHG_COEFF = "batteryset.chg.coeff";
    @InjectMocks
    private BackupPowerHistoryAlarmDomainImpl backupPowerHistoryAlarmDomain;

    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private BattAlarmMapper battAlarmMapper;
    @Mock
    private ConfigurationManagerRpcImpl configurationRpcImpl;
    @Mock
    private BatteryMaximumChargingCapacityDomainImpl batteryMaximumChargingCapacityDomainImpl;
    @Mock
    private DataRedis dataRedis;
    @Mock
    private PmaServiceUtils pmaService;
    @Mock
    private BattLifeEvalDomain battLifeEvalDomain;
    @Mock
    private JsonService jsonService;

    @Mock
    private AlarmService alarmService;

    @Mock
    private SystemConfigService configService;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private CollectorCacheManager collectorCacheManager;

    @Before
    public void setUp() throws IOException, UedmException, com.zte.uedm.basis.exception.UedmException {
        MockitoAnnotations.initMocks(this);
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(anyString())).thenReturn(new HashSet<>(Arrays.asList("id")));
        CollectorEntity entity = new CollectorEntity();
        entity.setId("collector");
        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(Arrays.asList(entity));
    }
    @Test
    public void getSiteAlrmTimeByMoListTest(){

        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setMoc("acdp");
        moList.add(deviceEntity);
        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = new ArrayList<>();
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorDeviceId("1");
        monitorDeviceObjectRelationBean.setMonitorObjectId("1");;
        monitorRelatedDeviceList.add(monitorDeviceObjectRelationBean);

        Map<String, Object> map = new HashMap<>();
        List<String> acdpList = new ArrayList<>();
        List<String> distmainsList = new ArrayList<>();
        List<String> dcdpList = new ArrayList<>();
        List<String> battPackList = new ArrayList<>();
        // 交流电停电和市电告警list
        List<Alarm> acdpAndDistmainsAlarmList = new ArrayList<>();
        // 一次下电告警 list
        List<Alarm> dcdpAndBattPackList = new ArrayList<>();
        List<String> acdp_distmains_List = new ArrayList<>();
        List<String> dcdp_battPack_List = new ArrayList<>();
        List<String> dcdp_branch_List1 = new ArrayList<>();
        List<AlarmCode> alarm_acdp_distmains_codes = new ArrayList();
        List<AlarmCode> alarm_dcdp_battPack_codes = new ArrayList();
        List<AlarmCode> alarm_dcdp_branch_codes = new ArrayList();
        List<AlarmCode> alarm_dcdp_branch_all_codes = new ArrayList();
        Map<String, Object> map3 = new HashMap<>();
        List<String> reasons = new ArrayList<>();
        BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
        BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
        Map<String, Map<String, String>> smpDataMapOfBatt = new HashMap<>();
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        Map<String,String> map4 = new HashMap<>();
        map4.put("value","-96");
        Map<String,String> map1 = new HashMap<>();
        map1.put("value","0.3");
        Map<String,String> map2 = new HashMap<>();
        map2.put("value","0");
        smpDataMapOfBatt.put("batt.prst.soc",map4);
        smpDataMapOfBatt.put("battpack.chg.coeff",map1);
        smpDataMapOfBatt.put("batt.charge.discharge.status",map2);
        Alarm alarm = new Alarm();
        alarm.setAlarmraisedtime(1689593169L);
        acdpAndDistmainsAlarmList.add(alarm);
        battPackList.add("r32.uedm.battpack");
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);

        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();
        BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();

        map3.put("code",ERROR_CODE_1);
        try {
            when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);
            when(dataRedis.selectRealData(Mockito.any())).thenReturn(smpDataMapOfBatt);
            when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
            when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
            when(dataRedis.getSmpValueFromMap(smpDataMapOfBatt, BattStandardPointConstants.BATT_SMPID_CHARGE_DISCHARGE_STATUS)).thenReturn("0");
            when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
            when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
            when(dateTimeService.getCurrentTime()).thenReturn("2023-07-06 14:47:36");
            map = backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (UedmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assert.assertSame("-7",map.get("code"));
    }

    @Test
    public void getSiteAlrmTimeByMoListTest_NoLlvdAlarm(){

        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity acdp = new DeviceEntity();
        acdp.setMoc("acdp");
        acdp.setId("acdp-1");
        moList.add(acdp);
        DeviceEntity dcdp = new DeviceEntity();
        dcdp.setMoc("dcdp");
        dcdp.setId("dcdp-1");
        moList.add(dcdp);
        DeviceEntity battPack = new DeviceEntity();
        battPack.setMoc("battpack");
        battPack.setId("battpack-1");
        moList.add(battPack);

        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = new ArrayList<>();
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorDeviceId("1");
        monitorDeviceObjectRelationBean.setMonitorObjectId("1");;
        monitorRelatedDeviceList.add(monitorDeviceObjectRelationBean);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        List<String> reasons = new ArrayList<>();
        BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
        BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);

        BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();

        map3.put("code",ERROR_CODE_1);
        try {
            when(dateTimeService.getCurrentTime()).thenReturn("2023-07-06 14:47:36");
            map = backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (UedmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assert.assertSame("-7",map.get("code"));
    }

    @Test
    public void getSiteAlrmTimeByMoListTest_NoAcAlarm(){

        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity acdp = new DeviceEntity();
        acdp.setMoc("acdp");
        acdp.setId("acdp-1");
        moList.add(acdp);
        DeviceEntity dcdp = new DeviceEntity();
        dcdp.setMoc("dcdp");
        dcdp.setId("dcdp-1");
        moList.add(dcdp);
        DeviceEntity battPack = new DeviceEntity();
        battPack.setMoc("battpack");
        battPack.setId("battpack-1");
        moList.add(battPack);

        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = new ArrayList<>();
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorDeviceId("1");
        monitorDeviceObjectRelationBean.setMonitorObjectId("1");;
        monitorRelatedDeviceList.add(monitorDeviceObjectRelationBean);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        List<String> reasons = new ArrayList<>();
        BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
        BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);

        List<Alarm> llvdAlarms = new ArrayList<>();
        Alarm alarm = new Alarm();
        // 2023-09-20 00:00:00
        alarm.setAlarmraisedtime(1695139200L);
        alarm.setAlarmcode(50008008L);
        llvdAlarms.add(alarm);
        List<Alarm> smartLlvdAlarms = new ArrayList<>();
        Alarm alarm1 = new Alarm();
        // 2023-09-20 01:00:00
        alarm1.setAlarmraisedtime(1695142800L);
        alarm1.setAlarmcode(5001702104L);
        smartLlvdAlarms.add(alarm1);
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);

        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();

        BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();
        map3.put("code",ERROR_CODE_1);
        try {
            when(dateTimeService.getStrMillisecondTime(any())).thenReturn("2023-09-20 00:00:00.000");
            when(configService.getAcPowerOffTimeRange()).thenReturn(3);
            when(alarmService.getAlarmDataBatch(any())).thenReturn(llvdAlarms).thenReturn(smartLlvdAlarms);
            when(dateTimeService.getCurrentTime()).thenReturn("2023-07-06 14:47:36");
            map = backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (UedmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assert.assertSame(0,battBackupPowerEvalResultBean.getMax());
    }

    @Test
    public void getSiteAlrmTimeByMoListTest_NotMax(){

        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity acdp = new DeviceEntity();
        acdp.setMoc("acdp");
        acdp.setId("acdp-1");
        moList.add(acdp);
        DeviceEntity dcdp = new DeviceEntity();
        dcdp.setMoc("dcdp");
        dcdp.setId("dcdp-1");
        moList.add(dcdp);
        DeviceEntity battPack = new DeviceEntity();
        battPack.setMoc("battpack");
        battPack.setId("battpack-1");
        moList.add(battPack);

        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = new ArrayList<>();
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorDeviceId("1");
        monitorDeviceObjectRelationBean.setMonitorObjectId("1");;
        monitorRelatedDeviceList.add(monitorDeviceObjectRelationBean);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        List<String> reasons = new ArrayList<>();
        BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
        BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);

        List<Alarm> llvdAlarms = new ArrayList<>();
        Alarm alarm = new Alarm();
        // 2023-09-20 00:00:00
        alarm.setAlarmraisedtime(1695139200L);
        alarm.setAlarmcode(50008008L);
        llvdAlarms.add(alarm);
        List<Alarm> smartLlvdAlarms = new ArrayList<>();
        Alarm alarm1 = new Alarm();
        // 2023-09-20 01:00:00
        alarm1.setAlarmraisedtime(1695142800L);
        alarm1.setAlarmcode(5001702104L);
        smartLlvdAlarms.add(alarm1);

        List<Alarm> acAlarms = new ArrayList<>();
        Alarm alarm2 = new Alarm();
        // 2023-09-19 20:00:00
        alarm2.setAlarmraisedtime(1695124800L);
        alarm2.setAlarmcode(50031026L);
        acAlarms.add(alarm2);
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);

        map3.put("code",ERROR_CODE_1);
        BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();
        try {
            when(dateTimeService.getStrMillisecondTime(any())).thenReturn("2023-09-20 00:00:00.000");
            when(configService.getAcPowerOffTimeRange()).thenReturn(3);
            when(alarmService.getAlarmDataBatch(any())).thenReturn(llvdAlarms).thenReturn(smartLlvdAlarms).thenReturn(acAlarms);
            when(dateTimeService.getCurrentTime()).thenReturn("2023-07-06 14:47:36");
            map = backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (UedmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assert.assertSame(0,battBackupPowerEvalResultBean.getMax());
    }

    @Test
    public void getSiteAlrmTimeByMoListTest_Normal(){

        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity acdp = new DeviceEntity();
        acdp.setMoc("acdp");
        acdp.setId("acdp-1");
        moList.add(acdp);
        DeviceEntity dcdp = new DeviceEntity();
        dcdp.setMoc("dcdp");
        dcdp.setId("dcdp-1");
        moList.add(dcdp);
        DeviceEntity battPack = new DeviceEntity();
        battPack.setMoc("battpack");
        battPack.setId("battpack-1");
        moList.add(battPack);

        List<MonitorDeviceObjectRelationBean> monitorRelatedDeviceList = new ArrayList<>();
        MonitorDeviceObjectRelationBean monitorDeviceObjectRelationBean = new MonitorDeviceObjectRelationBean();
        monitorDeviceObjectRelationBean.setMonitorDeviceId("1");
        monitorDeviceObjectRelationBean.setMonitorObjectId("1");;
        monitorRelatedDeviceList.add(monitorDeviceObjectRelationBean);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        List<String> reasons = new ArrayList<>();
        BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
        BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
        Map<String, Map<String, String>> smpDataMapOfBatt = new HashMap<>();
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        Map<String,String> map4 = new HashMap<>();
        map4.put("value","100");
        Map<String,String> map1 = new HashMap<>();
        map1.put("value","99");
        Map<String,String> map2 = new HashMap<>();
        map2.put("value","100");
        smpDataMapOfBatt.put("batt.prst.soc",map4);
        smpDataMapOfBatt.put("battpack.chg.coeff",map1);
        smpDataMapOfBatt.put("batt.charge.discharge.status",map2);

        List<Alarm> llvdAlarms = new ArrayList<>();
        Alarm alarm = new Alarm();
        // 2023-09-20 00:00:00
        alarm.setAlarmraisedtime(1695139200L);
        alarm.setAlarmcode(50008008L);
        llvdAlarms.add(alarm);
        Alarm alarm12 = new Alarm();
        // 2023-09-21 00:00:00
        alarm12.setAlarmraisedtime(1695225600L);
        alarm12.setAlarmcode(50008008L);
        llvdAlarms.add(alarm12);
        List<Alarm> smartLlvdAlarms = new ArrayList<>();
        Alarm alarm1 = new Alarm();
        // 2023-09-20 01:00:00
        alarm1.setAlarmraisedtime(1695142800L);
        alarm1.setAlarmcode(5001702104L);
        smartLlvdAlarms.add(alarm1);

        List<Alarm> acAlarms = new ArrayList<>();
        Alarm alarm2 = new Alarm();
        // 2023-09-19 20:00:00
        alarm2.setAlarmraisedtime(1695124800L);
        alarm2.setAlarmcode(50031026L);
        acAlarms.add(alarm2);
        Alarm alarm21 = new Alarm();
        // 2023-09-21 00:00:00
        alarm21.setAlarmraisedtime(1695222000L);
        alarm21.setAlarmcode(50031026L);
        llvdAlarms.add(alarm21);
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);

        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();

        List<HistoryAiBean> historyAiBeans  =  new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setCurValue("100");
        historyAiBean.setResId("id");
        historyAiBeans.add(historyAiBean);
        BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();

        map3.put("code",ERROR_CODE_1);
        try {
            when(dateTimeService.getStrMillisecondTime(any())).thenReturn("2023-09-20 00:00:00.000");
            when(configService.getAcPowerOffTimeRange()).thenReturn(3);
            when(alarmService.getAlarmDataBatch(any())).thenReturn(llvdAlarms).thenReturn(smartLlvdAlarms).thenReturn(acAlarms);
            when(dateTimeService.getCurrentTime()).thenReturn("2023-07-06 14:47:36");
            when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);
            when(dataRedis.selectRealData(Mockito.any())).thenReturn(smpDataMapOfBatt);
            when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
            when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
            when(dataRedis.getSmpValueFromMap(smpDataMapOfBatt, BattStandardPointConstants.BATT_SMPID_CHARGE_DISCHARGE_STATUS)).thenReturn("0");
            when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
            when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
            when(pmaService.selectDataByCondition(any(),any(), any(), any(), any(), any(),any())).thenReturn(historyAiBeans);
            map = backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (UedmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assert.assertSame(1,battBackupPowerEvalResultBean.getMax());
    }

    @Test
    public void selectByIdsAndType1() throws UedmException
    {
        Exception flag =null;
        try
        {
            List<DeviceEntity> moList = new ArrayList<>();
//            MonitorObjectBean bean = new MonitorObjectBean();
//            bean.setMoc("acdp");
//            moList.add(bean);
//            MonitorObjectBean bean1 = new MonitorObjectBean();
//            bean1.setMoc("distmains");
//            moList.add(bean1);
            DeviceEntity bean2 = new DeviceEntity();
            bean2.setMoc("dcdp");
            bean2.setId("da");
            moList.add(bean2);
            DeviceEntity bean3 = new DeviceEntity();
            bean3.setMoc("batt");
            bean3.setId("da");
            moList.add(bean3);
            DeviceEntity bean4 = new DeviceEntity();
            bean4.setMoc("battpack");
            bean4.setId("da");
            moList.add(bean4);
            DeviceEntity bean5 = new DeviceEntity();
            bean5.setMoc("dcdp");
            bean5.setId("da");
            moList.add(bean5);
            List<String> reasons = new ArrayList<>();
            BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
            BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();
            when(dateTimeService.getCurrentTime()).thenReturn("da");
            BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
            backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        }catch (Exception e) {
            flag =new Exception();
        }
        Assert.assertSame(false,flag == null);
    }

    @Test
    public void selectByIdsAndType2() throws UedmException
    {
        Exception flag =null;
        try
        {
            List<DeviceEntity> moList = new ArrayList<>();
            DeviceEntity bean = new DeviceEntity();
            bean.setMoc("acdp");
            bean.setId("da");
            moList.add(bean);
            DeviceEntity bean1 = new DeviceEntity();
            bean1.setMoc("distmains");
            bean1.setId("da");
            moList.add(bean1);
            DeviceEntity bean2 = new DeviceEntity();
            bean2.setMoc("dcdp");
            bean2.setId("da");
            moList.add(bean2);
            DeviceEntity bean3 = new DeviceEntity();
            bean3.setMoc("batt");
            bean3.setId("da");
            moList.add(bean3);
            DeviceEntity bean4 = new DeviceEntity();
            bean4.setMoc("battpack");
            bean4.setId("da");
            moList.add(bean4);
            DeviceEntity bean5 = new DeviceEntity();
            bean5.setMoc("dcdp");
            bean5.setId("da");
            moList.add(bean5);
            when(dateTimeService.getCurrentTime()).thenReturn("2022-07-01 12:12:10");
            List<Alarm> alarms = new ArrayList<>();
            List<String> reasons = new ArrayList<>();
            BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
            BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();
            BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
            when(alarmService.getAlarmData(Mockito.anyObject())).thenReturn(alarms);
            backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        }catch (Exception e) {
            flag =new Exception();
        }
        Assert.assertSame(false,flag != null);
    }

    @Test
    public void selectByIdsAndType3() throws UedmException
    {
        Exception flag =null;
        try
        {
            List<DeviceEntity> moList = new ArrayList<>();
            DeviceEntity bean = new DeviceEntity();
            bean.setMoc("acdp");
            bean.setId("da");
            moList.add(bean);
            DeviceEntity bean1 = new DeviceEntity();
            bean1.setMoc("distmains");
            bean1.setId("da");
            moList.add(bean1);
            DeviceEntity bean2 = new DeviceEntity();
            bean2.setMoc("dcdp");
            bean2.setId("da");
            moList.add(bean2);
            DeviceEntity bean3 = new DeviceEntity();
            bean3.setMoc("batt");
            bean3.setId("da");
            moList.add(bean3);
            DeviceEntity bean4 = new DeviceEntity();
            bean4.setMoc("battpack");
            bean4.setId("da");
            moList.add(bean4);
            DeviceEntity bean5 = new DeviceEntity();
            bean5.setMoc("dcdp");
            bean5.setId("da");
            moList.add(bean5);
            when(dateTimeService.getCurrentTime()).thenReturn("da");
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm = new Alarm();
            alarm.setAlarmraisedtime(20L);
            alarms.add(alarm);
            when(alarmService.getAlarmData(Mockito.anyObject())).thenReturn(alarms);
            List<String> reasons = new ArrayList<>();
            BattBackupPowerEvalResultBean battBackupPowerEvalResultBean = new BattBackupPowerEvalResultBean();
            BattBackupPowerEvalConditionBean battBackupPowerEvalConditionBean = new BattBackupPowerEvalConditionBean();
            BackupPowerBean backupPowerBean = new BackupPowerBean("1","1");
            backupPowerHistoryAlarmDomain.getSiteAlrmTimeByMoList(backupPowerBean,moList,"1",reasons,battBackupPowerEvalResultBean,battBackupPowerEvalConditionBean);
        }catch (Exception e) {
            flag =new Exception();
        }
        Assert.assertSame(true,flag != null);
    }

    @Test
    public void getCoefficient() throws UedmException
    {
        List<Alarm> dcdpAndBattAndattPackList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(50031030L);
        dcdpAndBattAndattPackList.add(alarm);
        Double coefficient = backupPowerHistoryAlarmDomain.getCoefficient(dcdpAndBattAndattPackList);
        Assert.assertSame(true,coefficient > 0);
    }

    @Test
    public void getCoefficient1() throws UedmException
    {
        List<Alarm> dcdpAndBattAndattPackList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(50003005L);
        dcdpAndBattAndattPackList.add(alarm);
        Double coefficient = backupPowerHistoryAlarmDomain.getCoefficient(dcdpAndBattAndattPackList);
        Assert.assertSame(true,coefficient > 0);
    }

    @Test
    public void getCoefficient2() throws UedmException
    {
        List<Alarm> dcdpAndBattAndattPackList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(50008008L);
        dcdpAndBattAndattPackList.add(alarm);
        Double coefficient = backupPowerHistoryAlarmDomain.getCoefficient(dcdpAndBattAndattPackList);
        Assert.assertSame(true,coefficient > 0);
    }

    @Test
    public void getCoefficient3() throws UedmException
    {
        List<Alarm> dcdpAndBattAndattPackList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(50031034L);
        dcdpAndBattAndattPackList.add(alarm);
        Double coefficient = backupPowerHistoryAlarmDomain.getCoefficient(dcdpAndBattAndattPackList);
        Assert.assertSame(true,coefficient > 0);
    }


    @Test
    public void checkAlarmTest(){
        List<BattAlarmBean> alarmBeanRemote = new ArrayList<>();
        BattAlarmBean remote1 = new BattAlarmBean();
        remote1.setOmpIndex("ompIndex1");
        remote1.setOmpId(BattConst.REMOTE_SWITCH);
        remote1.setOmpValue("1");
        remote1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanRemote.add(remote1);

        BattAlarmBean remote2 = new BattAlarmBean();
        remote2.setOmpIndex("ompIndex1");
        remote2.setOmpId(BattConst.REMOTE_STATUS);
        remote2.setOmpValue("1");
        remote2.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanRemote.add(remote2);

        List<BattAlarmBean> alarmBeanTime = new ArrayList<>();
        BattAlarmBean time1 = new BattAlarmBean();
        time1.setOmpIndex("ompIndex1");
        time1.setOmpId("805800281101");
        time1.setOmpValue("1");
        time1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanTime.add(time1);

        BattAlarmBean timeStart1 = new BattAlarmBean();
        timeStart1.setOmpIndex("ompIndex1");
        timeStart1.setOmpId("805800282501");
        timeStart1.setOmpValue("21:00");
        timeStart1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanTime.add(timeStart1);

        BattAlarmBean timeEnd1 = new BattAlarmBean();
        timeEnd1.setOmpIndex("ompIndex1");
        timeEnd1.setOmpId("805800282601");
        timeEnd1.setOmpValue("23:00");
        timeEnd1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanTime.add(timeEnd1);

        List<BattAlarmBean> alarmBeanExemption = new ArrayList<>();
        BattAlarmBean exemption1 = new BattAlarmBean();
        exemption1.setOmpIndex("ompIndex1");
        exemption1.setOmpId("805800281601");
        exemption1.setOmpValue("1");
        exemption1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanExemption.add(exemption1);

        BattAlarmBean exemptionStart1 = new BattAlarmBean();
        exemptionStart1.setOmpIndex("ompIndex1");
        exemptionStart1.setOmpId("805800282F01");
        exemptionStart1.setOmpValue("22:00");
        exemptionStart1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanExemption.add(exemptionStart1);

        BattAlarmBean exemptionEnd1 = new BattAlarmBean();
        exemptionEnd1.setOmpIndex("ompIndex1");
        exemptionEnd1.setOmpId("805800283001");
        exemptionEnd1.setOmpValue("23:30");
        exemptionEnd1.setStartTime(new Date(System.currentTimeMillis() - 120000));
        alarmBeanExemption.add(exemptionEnd1);

        boolean result = backupPowerHistoryAlarmDomain.checkAlarm(alarmBeanRemote,alarmBeanTime,alarmBeanExemption,"ompIndex1",System.currentTimeMillis());
        assertTrue(result);
    }
    @Test
    public void testCheckTimeInCommonUse(){
        //测试时间段开关未打开
        List<BattAlarmBean> alarmBeanTime1 = new ArrayList<>();
        alarmBeanTime1.add(new BattAlarmBean("ALARM_ENABLED_FLAG", "0", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime1, System.currentTimeMillis()));

        //测试时间段开关打开，但无对应时间段
        List<BattAlarmBean> alarmBeanTime2 = new ArrayList<>();
        alarmBeanTime2.add(new BattAlarmBean("ALARM_ENABLED_FLAG", "1", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime2, System.currentTimeMillis()));
        alarmBeanTime2.add(new BattAlarmBean("SWITCH_TIME_1_START", "08:00", new Date()));
        alarmBeanTime2.add(new BattAlarmBean("SWITCH_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime2, System.currentTimeMillis()));

        //测试时间段开关打开，且落在对应时间段内
        List<BattAlarmBean> alarmBeanTime3 = new ArrayList<>();
        alarmBeanTime3.add(new BattAlarmBean("ALARM_ENABLED_FLAG", "1", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("SWITCH_TIME_1_START", "08:00", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("SWITCH_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime3, getTimestampByTime("09:00")));

        //测试多个时间段的情况
        List<BattAlarmBean> alarmBeanTime4 = new ArrayList<>();
        alarmBeanTime4.add(new BattAlarmBean("ALARM_ENABLED_FLAG", "1", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("SWITCH_TIME_1_START", "02:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("SWITCH_TIME_1_END", "06:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("SWITCH_TIME_2_START", "08:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("SWITCH_TIME_2_END", "12:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime4, getTimestampByTime("03:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime4, getTimestampByTime("10:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime4, getTimestampByTime("07:00")));

        //测试时间跨天的情况
        List<BattAlarmBean> alarmBeanTime5 = new ArrayList<>();
        alarmBeanTime5.add(new BattAlarmBean("ALARM_ENABLED_FLAG", "1", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("SWITCH_TIME_1_START", "22:00", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("SWITCH_TIME_1_END", "06:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime5, getTimestampByTime("23:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime5, getTimestampByTime("05:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime5, getTimestampByTime("07:00")));
    }

    @Test
    public void testCheckExemptionInCommonUse(){
        //测试免检开关未打开
        List<BattAlarmBean> alarmBeanTime1 = new ArrayList<>();
        alarmBeanTime1.add(new BattAlarmBean("EX_ENABLE_FLAG", "0", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime1, System.currentTimeMillis()));

        //测试免检开关打开，但无对应时间段
        List<BattAlarmBean> alarmBeanTime2 = new ArrayList<>();
        alarmBeanTime2.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime2, System.currentTimeMillis()));
        alarmBeanTime2.add(new BattAlarmBean("EX_TIME_1_START", "08:00", new Date()));
        alarmBeanTime2.add(new BattAlarmBean("EX_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime2, System.currentTimeMillis()));

        //测试免检开关打开，且落在对应时间段内
        List<BattAlarmBean> alarmBeanTime3 = new ArrayList<>();
        alarmBeanTime3.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("EX_TIME_1_START", "08:00", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("EX_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime3, getTimestampByTime("09:00")));

        //测试多个时间段的情况
        List<BattAlarmBean> alarmBeanTime4 = new ArrayList<>();
        alarmBeanTime4.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_1_START", "02:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_1_END", "06:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_2_START", "08:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_2_END", "12:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("03:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("10:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("07:00")));

        //测试时间跨天的情况
        List<BattAlarmBean> alarmBeanTime5 = new ArrayList<>();
        alarmBeanTime5.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("EX_TIME_1_START", "22:00", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("EX_TIME_1_END", "06:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime5, getTimestampByTime("23:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime5, getTimestampByTime("05:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkTimeInCommonUse(alarmBeanTime5, getTimestampByTime("07:00")));
    }

    @Test
    public void testCheckExemptionInCommonUse1(){
        //测试免检开关未打开
        List<BattAlarmBean> alarmBeanTime1 = new ArrayList<>();
        alarmBeanTime1.add(new BattAlarmBean("EX_ENABLE_FLAG", "0", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime1, System.currentTimeMillis()));

        //测试免检开关打开，但无对应时间段
        List<BattAlarmBean> alarmBeanTime2 = new ArrayList<>();
        alarmBeanTime2.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime2, System.currentTimeMillis()));
        alarmBeanTime2.add(new BattAlarmBean("EX_TIME_1_START", "08:00", new Date()));
        alarmBeanTime2.add(new BattAlarmBean("EX_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime2, System.currentTimeMillis()));

        //测试免检开关打开，且落在对应时间段内
        List<BattAlarmBean> alarmBeanTime3 = new ArrayList<>();
        alarmBeanTime3.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("EX_TIME_1_START", "08:00", new Date()));
        alarmBeanTime3.add(new BattAlarmBean("EX_TIME_1_END", "10:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime3, getTimestampByTime("09:00")));

        //测试多个时间段的情况
        List<BattAlarmBean> alarmBeanTime4 = new ArrayList<>();
        alarmBeanTime4.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_1_START", "02:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_1_END", "06:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_2_START", "08:00", new Date()));
        alarmBeanTime4.add(new BattAlarmBean("EX_TIME_2_END", "12:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("03:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("10:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime4, getTimestampByTime("07:00")));

        //测试时间跨天的情况
        List<BattAlarmBean> alarmBeanTime5 = new ArrayList<>();
        alarmBeanTime5.add(new BattAlarmBean("EX_ENABLE_FLAG", "1", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("EX_TIME_1_START", "22:00", new Date()));
        alarmBeanTime5.add(new BattAlarmBean("EX_TIME_1_END", "06:00", new Date()));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime5, getTimestampByTime("23:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime5, getTimestampByTime("05:00")));
        assertFalse(backupPowerHistoryAlarmDomain.checkExemptionInCommonUse(alarmBeanTime5, getTimestampByTime("07:00")));
    }

    private long getTimestampByTime(String time){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = "2021-01-01 " + time + ":00";
        try {
            Date date = format.parse(dateStr);
            return date.getTime();
        } catch (ParseException e) {

            return 0;
        }
    }
    @Test
    public void isMaxCapacityTest() throws UedmException {
        Map<String, Map<String, String>> smpDataMapOfBatt = new HashMap<>();
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        Map<String,String> map = new HashMap<>();
        map.put("batt.prst.soc","96");
        smpDataMapOfBatt.put("id",map);
        List<Alarm> acdpAndDistmainsAlarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmraisedtime(1689593169L);
        acdpAndDistmainsAlarmList.add(alarm);
        List<String> battPackList = new ArrayList<>();
        battPackList.add("r32.uedm.battpack");
        List<String> reasons = new ArrayList<>();
        BackupPowerBean backupPowerBean = new BackupPowerBean("id","name");
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);
        List<HistoryAiBean> historyAiBeans  =  new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setCurValue("96");
        historyAiBean.setResId("id");
        historyAiBeans.add(historyAiBean);
        when(pmaService.selectDataByCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(historyAiBeans);
        when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);
        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();
        when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
        when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(dataRedis.selectRealData(Mockito.any())).thenReturn(smpDataMapOfBatt);
        when( dataRedis.getSmpValueFromMap(Mockito.any(),Mockito.any())).thenReturn("96");

        boolean isMaxCapacity = backupPowerHistoryAlarmDomain.isMaxCapacity(backupPowerBean, alarm, battPackList, reasons);
        assertTrue(isMaxCapacity);


    }
    @Test
    public void isMaxCapacityTest1() throws UedmException {
        Map<String, Map<String, String>> smpDataMapOfBatt = new HashMap<>();
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        Map<String,String> map = new HashMap<>();
        map.put("value","-96");
        Map<String,String> map1 = new HashMap<>();
        map1.put("value","0.3");
        Map<String,String> map2 = new HashMap<>();
        map2.put("value","0");
        smpDataMapOfBatt.put("batt.prst.soc",map);
        smpDataMapOfBatt.put("battpack.chg.coeff",map1);
        smpDataMapOfBatt.put("batt.charge.discharge.status",map2);
        List<Alarm> acdpAndDistmainsAlarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmraisedtime(1689593169L);
        acdpAndDistmainsAlarmList.add(alarm);
        List<String> battPackList = new ArrayList<>();
        battPackList.add("r32.uedm.battpack");
        List<String> reasons = new ArrayList<>();
        BackupPowerBean backupPowerBean = new BackupPowerBean("id","name");
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);
        List<HistoryAiBean> historyAiBeans  =  new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setCurValue("96");
        historyAiBean.setResId("id");
        historyAiBeans.add(historyAiBean);
        when(pmaService.selectDataByCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(historyAiBeans);
        when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);
        when(dataRedis.selectRealData(Mockito.any())).thenReturn(smpDataMapOfBatt);
        when(dataRedis.getSmpValueFromMap(smpDataMapOfBatt, BattStandardPointConstants.BATT_SMPID_CHARGE_DISCHARGE_STATUS)).thenReturn("0");
        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();

        when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
        when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);

        boolean isMaxCapacity = backupPowerHistoryAlarmDomain.isMaxCapacity(backupPowerBean, alarm, battPackList, reasons);
        assertTrue(isMaxCapacity);
    }
    @Test
    public void isMaxCapacityTest2() throws UedmException {
        Map<String, Map<String, String>> smpDataMapOfBatt = new HashMap<>();
        List<BatteryBackupPowerBean> meetConditionsBattery = new ArrayList<>();
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        meetConditionsBattery.add(batteryBackupPowerBean);
        Map<String,String> map = new HashMap<>();
        map.put("value","0");
        Map<String,String> map1 = new HashMap<>();
        map1.put("value","0.1");
        Map<String,String> map2 = new HashMap<>();
        map2.put("value","0");
        smpDataMapOfBatt.put("batt.prst.soc",map);
        smpDataMapOfBatt.put("battpack.chg.coeff",map1);
        smpDataMapOfBatt.put("batt.charge.discharge.status",map2);
        List<Alarm> acdpAndDistmainsAlarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmraisedtime(1689593169L);
        acdpAndDistmainsAlarmList.add(alarm);
        List<String> battPackList = new ArrayList<>();
        battPackList.add("r32.uedm.battpack");
        List<String> reasons = new ArrayList<>();
        BackupPowerBean backupPowerBean = new BackupPowerBean("id","name");
        backupPowerBean.setLiBatteryBackupPowerBeans(meetConditionsBattery);
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("id",100);
        List<HistoryAiBean> historyAiBeans  =  new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setCurValue("-96");
        historyAiBean.setResId("id");
        historyAiBeans.add(historyAiBean);
        when(pmaService.selectDataByCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(historyAiBeans);
        when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);
        when(dataRedis.selectRealData(Mockito.any())).thenReturn(smpDataMapOfBatt);
        when(dataRedis.getSmpValueFromMap(smpDataMapOfBatt, StandPointOptional.BATTERY_SMPID_CHARGE_DISCHARGE_STATUS.getId())).thenReturn("0");
        when(dataRedis.getSmpValueFromMap(smpDataMapOfBatt, BATTPACK_CHG_COEFF)).thenReturn("0.1");
        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();

        when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
        when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);

        boolean isMaxCapacity = backupPowerHistoryAlarmDomain.isMaxCapacity(backupPowerBean, alarm, battPackList, reasons);
        assertTrue(isMaxCapacity);
    }
}
