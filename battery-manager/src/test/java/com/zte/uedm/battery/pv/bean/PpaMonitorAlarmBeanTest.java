package com.zte.uedm.battery.pv.bean;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/8
 **/
public class PpaMonitorAlarmBeanTest {
    @Test
    public void test() throws Exception
    {
        try {
            PojoTestUtil.TestForPojo(PpaMonitorAlarmBean.class);
            PojoTestUtil.TestForPojo(SitePpaMonitorAlarmBean.class);
            PojoTestUtil.TestForPojo(SitePvOutTotalPowerBean.class);
        } catch (Exception e) {
            Assert.assertEquals("", e.getMessage());
        }
    }

}
