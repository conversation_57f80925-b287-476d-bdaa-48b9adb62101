package com.zte.uedm.battery.a_domain.service.peakshift.impl;

import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.factory.PeakShiftFactory;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.PeakShiftTaskWithDevicePo;
import com.zte.uedm.battery.bean.LogInputBean;
import com.zte.uedm.battery.mapper.PeakShiftTaskMapper;
import com.zte.uedm.battery.service.impl.DevicePeakCacheInfoServiceImpl;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.component.redis.service.RedisService;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.zte.uedm.battery.consts.CommonConst.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/* Started by AICoder, pid:h9868u997030ffe14fbf094234e81b8c98484a64 */
public class StopPeakShiftingStrategyServiceImplTest {

    @InjectMocks
    private StopPeakShiftingStrategyServiceImpl stopPeakShiftingStrategyService;

    @Mock
    private PeakShiftFactory peakShiftFactory;

    @Mock
    private PeakShiftTaskMapper peakShiftTaskMapper;

    @Mock
    private CollectorCacheManager collectorCacheManager;

    @Mock
    private RedisService commonRedisService;

    @Mock
    private DevicePeakCacheInfoServiceImpl devicePeakCacheInfoService;

    @Mock
    private LogUtils logUtils;

    private DateTimeFormatter formatter;
    private LocalDateTime nowDate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        nowDate = LocalDateTime.now();
    }

    // 测试正常流程
    @Test
    void testStopPeakShiftingStrategy_HappyPath() throws Exception {
        try{
            // 模拟数据
            List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
            PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
            task.setDeviceId("device1");
            task.setExpirationDate(nowDate.plusHours(1).format(formatter));
            dbPos.add(task);

            CollectorEntity collector = new CollectorEntity();
            collector.setId("device1");
            collector.setMoc("moc");
            collector.setProtocolAttribute("SNMP");
            collector.setProtocolId("protocol1");

            when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
            when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
            when(commonRedisService.getCacheMap(anyString())).thenReturn(new HashMap<>());
            when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

            // 执行方法
            stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);

        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // 测试无设备任务的情况
    @Test
    void testStopPeakShiftingStrategy_NoTasks() throws Exception {
        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(new ArrayList<>());
        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);
        verify(collectorCacheManager, never()).getCollectorById(any());
        verify(peakShiftFactory, never()).generateByDeviceType(anyString());
    }

    // 测试缓存为空的情况
    @Test
    void testStopPeakShiftingStrategy_EmptyCache() throws Exception {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        dbPos.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(commonRedisService.getCacheMap(anyString())).thenReturn(new HashMap<>());
        when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);

    }

    // 测试缓存中status为0的情况
    @Test
    void testStopPeakShiftingStrategy_CacheStatus0() throws Exception {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        dbPos.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 0);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 0);
        cacheMap.put("device1", infoMap);

        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);

        verify(peakShiftFactory, never()).generateByDeviceType("SNMP");
    }

    // 测试缓存中status为1且retryTimes小于3的情况
    @Test
    void testStopPeakShiftingStrategy_CacheStatus1RetryTimesLessThan3() throws Exception {
        try {
            List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
            PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
            task.setDeviceId("device1");
            task.setExpirationDate(nowDate.minusHours(1).format(formatter));
            dbPos.add(task);

            CollectorEntity collector = new CollectorEntity();
            collector.setId("device1");
            collector.setMoc("moc");
            collector.setProtocolAttribute("SNMP");
            collector.setProtocolId("protocol1");

            Map<String, Object> cacheMap = new HashMap<>();
            Map<String, Object> infoMap = new HashMap<>();
            infoMap.put(PEAK_SHIFT_STOP_STATUS, 1);
            infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 2);
            cacheMap.put("device1", infoMap);

            when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
            when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
            when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
            when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

            stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);

        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    // 测试缓存中status为1且retryTimes大于3的情况
    @Test
    void testStopPeakShiftingStrategy_CacheStatus1RetryTimesGreaterThan3() throws Exception {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        dbPos.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 1);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 4);
        cacheMap.put("device1", infoMap);

        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(null);

        verify(commonRedisService, never()).putAll(anyString(), any());
    }

    // 测试传入的deviceIds为空的情况
    @Test
    void testStopPeakShiftingStrategy_EmptyDeviceIds() throws Exception {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        dbPos.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(commonRedisService.getCacheMap(anyString())).thenReturn(new HashMap<>());
        when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(Collections.emptyList());

    }

    // 测试传入的deviceIds包含在缓存中的情况
    @Test
    void testStopPeakShiftingStrategy_DeviceIdsInCache() throws Exception {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        dbPos.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 0);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 0);
        cacheMap.put("device1", infoMap);

        when(peakShiftTaskMapper.selectDeviceLatestTaskWithDevice()).thenReturn(dbPos);
        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(peakShiftFactory.generateByDeviceType("SNMP")).thenReturn(mock(PeakShiftCommonService.class));

        stopPeakShiftingStrategyService.stopPeakShiftingStrategy(Collections.singletonList("device1"));

    }

    // 测试OMP数据为空的情况
    @Test
    void testCheckSNMPStatus_EmptyOMPData() throws Exception {
        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 2);

        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(devicePeakCacheInfoService.dealSNMPOmp(any())).thenReturn(new HashMap<>());

        stopPeakShiftingStrategyService.checkSNMPStatus(collector,new ArrayList<>(), "aa");

    }

    // 测试SNMP启用状态为0的情况
    @Test
    void testCheckSNMPStatus_SNMPDisabled() throws Exception {
        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 1);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 1);
        cacheMap.put("device1", infoMap);

        Map<String, Map<String, AdapterPointDataVo>> ompMap = new HashMap<>();
        Map<String, AdapterPointDataVo> adapterPointDataMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("0");
        adapterPointDataMap.put(SNMP_ENABLE_POINT, adapterPointDataVo);
        ompMap.put("device1", adapterPointDataMap);

        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(devicePeakCacheInfoService.dealSNMPOmp(any())).thenReturn(ompMap);

        stopPeakShiftingStrategyService.checkSNMPStatus(collector,new ArrayList<>(), "aa");

        verify(commonRedisService, times(1)).putAll(anyString(), anyMap());
        verify(logUtils, times(1)).generalLogSender(any(LogInputBean.class));
    }

    // 测试SNMP启用状态为1且retryTimes小于3的情况
    @Test
    void testCheckSNMPStatus_SNMPEnabledRetryTimesLessThan3() throws Exception {
        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 1);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 1);
        cacheMap.put("device1", infoMap);

        Map<String, Map<String, AdapterPointDataVo>> ompMap = new HashMap<>();
        Map<String, AdapterPointDataVo> adapterPointDataMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("1");
        adapterPointDataMap.put(SNMP_ENABLE_POINT, adapterPointDataVo);
        ompMap.put("device1", adapterPointDataMap);

        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(devicePeakCacheInfoService.dealSNMPOmp(any())).thenReturn(ompMap);

        stopPeakShiftingStrategyService.checkSNMPStatus(collector,new ArrayList<>(), "aa");

        verify(logUtils, never()).generalLogSender(any(LogInputBean.class));
    }

    // 测试SNMP启用状态无法解析的情况
    @Test
    void testCheckSNMPStatus_UnparseableSNMPStatus() throws Exception {
        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("SNMP");
        collector.setProtocolId("protocol1");

        Map<String, Object> cacheMap = new HashMap<>();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put(PEAK_SHIFT_STOP_STATUS, 1);
        infoMap.put(PEAK_SHIFT_STOP_RETRY_TIMES, 2);
        cacheMap.put("device1", infoMap);

        Map<String, Map<String, AdapterPointDataVo>> ompMap = new HashMap<>();
        Map<String, AdapterPointDataVo> adapterPointDataMap = new HashMap<>();
        AdapterPointDataVo adapterPointDataVo = new AdapterPointDataVo();
        adapterPointDataVo.setValue("3");
        adapterPointDataMap.put(SNMP_ENABLE_POINT, adapterPointDataVo);
        ompMap.put("330079", adapterPointDataMap);

        when(commonRedisService.getCacheMap(anyString())).thenReturn(cacheMap);
        when(devicePeakCacheInfoService.dealSNMPOmp(any())).thenReturn(ompMap);

        stopPeakShiftingStrategyService.checkSNMPStatus(collector,new ArrayList<>(), "aa");

        verify(commonRedisService, times(1)).putAll(anyString(), anyMap());
        verify(logUtils, never()).generalLogSender(any(LogInputBean.class));
    }

    // 测试设备类型非SNMP的情况
    @Test
    void testProcessFilteredTasks_DeviceTypeNotSNMP() throws Exception {
        List<PeakShiftTaskWithDevicePo> tasks = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        tasks.add(task);

        CollectorEntity collector = new CollectorEntity();
        collector.setId("device1");
        collector.setMoc("moc");
        collector.setProtocolAttribute("non-SNMP");

        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.singletonList(collector));
        when(peakShiftFactory.generateByDeviceType("non-SNMP")).thenReturn(null);

        stopPeakShiftingStrategyService.processFilteredTasks(tasks,new ArrayList<>());

    }

    // 测试设备采集器不存在的情况
    @Test
    void testProcessFilteredTasks_CollectorNotFound() throws Exception {
        List<PeakShiftTaskWithDevicePo> tasks = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        task.setExpirationDate(nowDate.minusHours(1).format(formatter));
        tasks.add(task);

        when(collectorCacheManager.getCollectorById(any())).thenReturn(Collections.emptyList());

        stopPeakShiftingStrategyService.processFilteredTasks(tasks,new ArrayList<>());

    }

    // 测试数据库任务列表为null的情况
    @Test
    void testFilterDbTasksByCache_DbPosNull() {
        List<PeakShiftTaskWithDevicePo> result = stopPeakShiftingStrategyService.filterDbTasksByCache(null, new HashSet<>());
        assertNull(result);
    }

    // 测试缓存ID集合为空的情况
    @Test
    void testFilterDbTasksByCache_CollectIdsEmpty() {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        dbPos.add(task);

        List<PeakShiftTaskWithDevicePo> result = stopPeakShiftingStrategyService.filterDbTasksByCache(dbPos, new HashSet<>());
        assertEquals(dbPos, result);
    }

    // 测试缓存ID集合中包含所有设备的情况
    @Test
    void testFilterDbTasksByCache_AllCollectIdsInCache() {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task = new PeakShiftTaskWithDevicePo();
        task.setDeviceId("device1");
        dbPos.add(task);

        Set<String> collectIds = new HashSet<>();
        collectIds.add("device1");

        List<PeakShiftTaskWithDevicePo> result = stopPeakShiftingStrategyService.filterDbTasksByCache(dbPos, collectIds);
        assertTrue(result.isEmpty());
    }

    // 测试缓存ID集合中部分设备的情况
    @Test
    void testFilterDbTasksByCache_PartCollectIdsInCache() {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();
        PeakShiftTaskWithDevicePo task1 = new PeakShiftTaskWithDevicePo();
        task1.setDeviceId("device1");
        dbPos.add(task1);
        PeakShiftTaskWithDevicePo task2 = new PeakShiftTaskWithDevicePo();
        task2.setDeviceId("device2");
        dbPos.add(task2);

        Set<String> collectIds = new HashSet<>();
        collectIds.add("device1");

        List<PeakShiftTaskWithDevicePo> result = stopPeakShiftingStrategyService.filterDbTasksByCache(dbPos, collectIds);
        assertEquals(1, result.size());
        assertEquals("device2", result.get(0).getDeviceId());
    }


    // 测试更新Redis中的峰值转移状态的情况
    @Test
    void testUpdatePeakShiftStatus_UpdateCache() throws Exception {
        String collectorId = "device1";

        stopPeakShiftingStrategyService.updatePeakShiftStatus(collectorId, 1, 2, "aa");

        verify(commonRedisService, times(1)).putAll(anyString(), anyMap());
    }

    // 测试发送日志的情况
    @Test
    void testSendLog_SendSuccessLog() throws com.zte.uedm.common.exception.UedmException {
        String collectId = "device1";
        String result = SUCCESS;

        stopPeakShiftingStrategyService.sendLog(collectId, result);

        verify(logUtils, times(1)).generalLogSender(any(LogInputBean.class));
    }

    // 测试发送失败日志的情况
    @Test
    void testSendLog_SendFailedLog() throws com.zte.uedm.common.exception.UedmException {
        String collectId = "device1";
        String result = FAILED;

        stopPeakShiftingStrategyService.sendLog(collectId, result);

        verify(logUtils, times(1)).generalLogSender(any(LogInputBean.class));
    }

    /* Started by AICoder, pid:1920f82578fe6be1471a0aca1146786fcd26a687 */
    @Test
    public void testCheckCollectId_withMatchAndSNMP() {
        // 准备测试数据：dbPos
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();

        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId("device001");
        po.setId("task001");
        po.setExpirationDate("2025-12-31");

        dbPos.add(po);

        // 参数设置
        String collectId = "device001";
        String taskInfo = "2025-12-31"; // 匹配 expirationDate
        Set<String> collectIds = new HashSet<>(Arrays.asList("device001", "device002"));
        String deviceType = "SNMP";

        // 执行方法
        stopPeakShiftingStrategyService.checkCollectId(dbPos, collectId, taskInfo, collectIds);

        // 断言：collectId 没有被删除
        assertTrue(collectIds.contains("device001"));
    }

    @Test
    public void testCheckCollectId_withMismatchAndSNMP() {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();

        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId("device001");
        po.setId("task001");
        po.setExpirationDate("2025-12-31");

        dbPos.add(po);

        String collectId = "device001";
        String taskInfo = "wrong_date";
        Set<String> collectIds = new HashSet<>(Arrays.asList("device001", "device002"));
        String deviceType = "SNMP";

        stopPeakShiftingStrategyService.checkCollectId(dbPos, collectId, taskInfo, collectIds);

        assertFalse(collectIds.contains("device001")); // 应该被移除了
    }

    @Test
    public void testCheckCollectId_withNonSNMP() {
        List<PeakShiftTaskWithDevicePo> dbPos = new ArrayList<>();

        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId("device001");
        po.setId("task001");

        dbPos.add(po);

        String collectId = "device001";
        String taskInfo = "task01";
        Set<String> collectIds = new HashSet<>(Arrays.asList("device001", "device002"));
        String deviceType = "MODBUS"; // 非 SNMP

        stopPeakShiftingStrategyService.checkCollectId(dbPos, collectId, taskInfo, collectIds);

        assertFalse(collectIds.contains("device001"));
    }

    @Test
    public void testCheckCollectId_withNoMatchingDevice() {
        List<PeakShiftTaskWithDevicePo> dbPos = Collections.emptyList();

        String collectId = "device001";
        String taskInfo = "task001";
        Set<String> collectIds = new HashSet<>(Arrays.asList("device001", "device002"));
        String deviceType = "MODBUS";

        stopPeakShiftingStrategyService.checkCollectId(dbPos, collectId, taskInfo, collectIds);

        // 没找到 dbPo，不会执行 remove
        assertTrue(collectIds.contains("device001"));
    }

    @Test
    public void testExtracted_CollectorNotFound() {
        String collectId = "123";
        List<PeakShiftTaskWithDevicePo> dbPos = Collections.emptyList();
        Object taskInfo = "task1";
        Set<String> collectIds = new HashSet<>(Arrays.asList(collectId));

        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(null);

        stopPeakShiftingStrategyService.extracted(dbPos, collectId, taskInfo, collectIds);

        assertTrue(collectIds.contains(collectId)); // 不移除，因为日志只 warn
    }

    @Test
    public void testExtracted_NonSNMP_DeviceIdMatch() {
        String collectId = "123";
        Set<String> collectIds = new HashSet<>(Collections.singletonList(collectId));

        // 准备 CollectorEntity
        CollectorEntity entity = new CollectorEntity();
        entity.setMoc("moc");
        entity.setProtocolAttribute("protocol");

        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(Collections.singletonList(entity));

        // 准备 dbPo
        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId(collectId);
        po.setId("task1");

        List<PeakShiftTaskWithDevicePo> dbPos = Collections.singletonList(po);

        stopPeakShiftingStrategyService.extracted(dbPos, collectId, "task1", collectIds);

        assertTrue(collectIds.contains(collectId)); // 相等，不移除
    }

    @Test
    public void testExtracted_SNMP_ExpirationMatch() {
        String collectId = "123";
        Set<String> collectIds = new HashSet<>(Collections.singletonList(collectId));

        // SNMP 设备
        CollectorEntity entity = new CollectorEntity();
        entity.setMoc("moc");
        entity.setProtocolAttribute("protocol");

        when(collectorCacheManager.getCollectorById(anyList()))
                .thenReturn(Collections.singletonList(entity));

        // dbPo with expirationDate
        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId(collectId);
        po.setExpirationDate("2025-01-01");

        List<PeakShiftTaskWithDevicePo> dbPos = Collections.singletonList(po);

        stopPeakShiftingStrategyService.extracted(dbPos, collectId, "2025-01-01", collectIds);

        assertFalse(collectIds.contains(collectId)); // 匹配，不移除
    }

    @Test
    public void testExtracted_SNMP_ExpirationMismatch() {
        String collectId = "123";
        Set<String> collectIds = new HashSet<>(Collections.singletonList(collectId));

        CollectorEntity entity = new CollectorEntity();
        entity.setMoc("moc");
        entity.setProtocolAttribute("protocol");

        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(Collections.singletonList(entity));


        PeakShiftTaskWithDevicePo po = new PeakShiftTaskWithDevicePo();
        po.setDeviceId(collectId);
        po.setExpirationDate("2024-01-01");

        List<PeakShiftTaskWithDevicePo> dbPos = Collections.singletonList(po);

        stopPeakShiftingStrategyService.extracted(dbPos, collectId, "2025-01-01", collectIds);

        assertFalse(collectIds.contains(collectId)); // 不匹配，被移除
    }
    /* Ended by AICoder, pid:1920f82578fe6be1471a0aca1146786fcd26a687 */
}
/* Ended by AICoder, pid:h9868u997030ffe14fbf094234e81b8c98484a64 */

