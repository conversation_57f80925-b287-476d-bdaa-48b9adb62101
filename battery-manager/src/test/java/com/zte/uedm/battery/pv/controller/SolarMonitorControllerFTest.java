package com.zte.uedm.battery.pv.controller;

import com.zte.log.filter.UserThreadLocal;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.FieldCacheManager;
import com.zte.uedm.battery.cache.DeviceCacheFake;
import com.zte.uedm.battery.cache.FieldCacheFake;
import com.zte.uedm.battery.export.manage.FileExportWriter;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.pv.bean.SolarMonitorStatisticsQueryBean;
import com.zte.uedm.battery.pv.bean.SolarRevenueOverviewExportBean;
import com.zte.uedm.battery.pv.dto.SolarMonitorQueryDto;
import com.zte.uedm.battery.pv.dto.SolarRevenueOverviewDto;
import com.zte.uedm.battery.pv.enums.SolarSummaryGrainEnum;
import com.zte.uedm.battery.pv.mapper.SolarMaxPowerMapper;
import com.zte.uedm.battery.pv.mapper.SolarMaxPowerMapperFake;
import com.zte.uedm.battery.pv.mapper.SolarRevenueMapper;
import com.zte.uedm.battery.pv.mapper.SolarRevenueMapperFake;
import com.zte.uedm.battery.pv.service.CarbonReductionService;
import com.zte.uedm.battery.pv.service.SolarMonitorService;
import com.zte.uedm.battery.pv.service.SolarRevenueService;
import com.zte.uedm.battery.pv.service.impl.SolarMonitorServiceImpl;
import com.zte.uedm.battery.pv.service.impl.SolarRecollectionServiceImpl;
import com.zte.uedm.battery.pv.service.impl.SolarRevenueServiceImpl;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.ConfigurationRpcFake;
import com.zte.uedm.battery.rpc.MpRpc;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.battery.service.impl.AlarmServiceImpl;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.ImageBean;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.I18nUtilService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.HeaderUtils;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.pma.service.PmaService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class, HeaderUtils.class, SolarSummaryGrainEnum.class, UserThreadLocal.class,CompletableFuture.class})
public class SolarMonitorControllerFTest {
    @InjectMocks
    private SolarMonitorController solarMonitorController;
    @Resource
    private SolarMonitorService solarMonitorService = new SolarMonitorServiceImpl() ;
    @Mock
    private CarbonReductionService carbonReductionService;
    @Resource
    private SolarRevenueService solarRevenueService = new SolarRevenueServiceImpl();
    @Mock
    private SolarRevenueMapper solarRevenueMapper = new SolarRevenueMapperFake();
    @Resource
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils = new RealGroupRelationSiteUtils();
    @Autowired
    private MonitorManagerRpcImpl monitorManagerRpc = new MonitorManagerRpcImpl();
    @Autowired
    private JsonService jsonService;
    @Mock
    private DataRedis dataRedis;
    @Autowired
    private I18nUtilService i18nUtils;
    @Mock
    private WriterExportFactory wf;
    @Resource
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl = new ConfigurationManagerRpcImpl();
    @Mock
    private ConfigService configService;
    @Resource
    private AlarmServiceImpl alarmServiceImpl = new AlarmServiceImpl();
    @Mock
    private AssetRpcImpl assetRpcImpl ;
    @Mock
    private SolarRecollectionServiceImpl solarRecollectionServiceImpl;
    @Mock
    private PmaService pmaService;
    @Resource
    private DateTimeService dateTimeService = new DateTimeService();
    @Mock
    private SolarMaxPowerMapper solarMaxPowerMapper = new SolarMaxPowerMapperFake();
    @Mock
    private RedisService redisService;

    @Resource
    private MpRpcImpl mpRpcImpl = new MpRpcImpl();
    @Mock
    private MpRpc mpRpc;
//    @Mock
//    private OriginalPointRpc originalPointRpc;
    @Mock
    private ConfigurationRpc configurationRpc = new ConfigurationRpcFake();
    @Mock
    private MessageSenderService msgSenderService;
    @Mock
    private FileExportWriter exportWriter;
    @Mock
    private CommonCacheService commonCacheService;
    @Mock
    private CacheManager caffeineCacheManager;
    @Mock
    private FieldCacheManager fieldCacheManager = new FieldCacheFake();
    @Mock
    private DeviceCacheManager deviceCacheManager = new DeviceCacheFake();
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private com.zte.uedm.component.redis.service.RedisService redissonService;
    @Mock
    private LicenseSwitchService licenseMgr;


    @Before
    public void before() throws ClassNotFoundException, IllegalAccessException, InstantiationException, NoSuchFieldException, NoSuchMethodException, InvocationTargetException, UedmException {
        FtMockitoAnnotations.initMocks(this);
        request = Mockito.mock(HttpServletRequest.class);
        response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.getUserName(Mockito.any())).thenReturn("username");
        PowerMockito.when(Tools.getRemoteHost(Mockito.any())).thenReturn("************");
        PowerMockito.mockStatic(HeaderUtils.class);
        PowerMockito.when(HeaderUtils.buildUserNameHeaders(Mockito.anyString())).thenReturn(new HashMap<>());
        PowerMockito.mockStatic(SolarSummaryGrainEnum.class);
        PowerMockito.when(SolarSummaryGrainEnum.getTableNameByCode("month")).thenReturn("month");
        PowerMockito.when(SolarSummaryGrainEnum.getTableNameByCode("year")).thenReturn("year");
        PowerMockito.when(SolarSummaryGrainEnum.getTableNameByCode("all")).thenReturn("all");
        PowerMockito.mockStatic(UserThreadLocal.class);
        PowerMockito.when(UserThreadLocal.getLoginType()).thenReturn("0");
        Mockito.doReturn(new HashMap<>()).when(dataRedis).batchGetSmpValueByKey(Mockito.anyList(),Mockito.anyString());
        Mockito.doReturn(new HashMap<>()).when(assetRpcImpl).getSiteInstallationCapacityByMoIds(Mockito.anyList(),Mockito.anyString());
        Mockito.doReturn(exportWriter).when(wf).getWriter(Mockito.any());
        Mockito.doReturn(new ArrayList<>()).when(solarRecollectionServiceImpl).getCurrentRecollectionSiteList();
    }

    @Test
    public void UEDM_289218_given_存在减碳量系数_存在站点与监控对象映射关系_存在有权限的监控对象id_when_调用太阳能监控统计接口_then_返回太阳能站点监控分页数据() throws UedmException, ExecutionException, InterruptedException {
        SolarMonitorQueryDto dto = new SolarMonitorQueryDto();
        dto.setLogicGroupId("r32.uedm.group-global");
        dto.setOrder("totalSolarRevenue");
        dto.setSort("desc");
        dto.setPageNo(1);
        dto.setPageSize(10);

        ResponseBean responseBean = solarMonitorController.solarRevenueStatisticsSelect(dto, "zh_CN", request);
        Assert.assertEquals(-1, responseBean.getCode().intValue());
    }


    @Test
    public void UEDM_289220_given_存在有权限的监控对象_when_调用太能综合监控告警等汇总查询接口_then_返回太阳能综合监控状态汇总数据(){
        SolarMonitorStatisticsQueryBean queryBean = new SolarMonitorStatisticsQueryBean();
        queryBean.setLogicGroupId("r32.uedm.group-global");
        ResponseBean responseBean = solarMonitorController.queryAlarmAndOtherStatistics(queryBean, "zh_CN", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289216_given_有权限的太阳能监控对象不存在_when_调用太能综合监控告警等汇总查询接口_then_抛出异常(){
        SolarMonitorStatisticsQueryBean queryBean = new SolarMonitorStatisticsQueryBean();
        queryBean.setLogicGroupId("admin");
        ResponseBean responseBean = solarMonitorController.queryAlarmAndOtherStatistics(queryBean, "zh_CN", request);
        Assert.assertEquals(-602, responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289214_given_存在太阳能监控对象_传入累计时间粒度_when_调用太阳能综合监控发电及收益汇总查询接口_then_返回太阳能综合监控发电及收益汇总数据(){
        SolarRevenueOverviewDto dto = new SolarRevenueOverviewDto();
        dto.setGrain("all");
        dto.setLogicGroupId("r32.uedm.group-global");
        ResponseBean responseBean = solarMonitorController.querySolarRevenueStatistics(dto, "zh_CN", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289208_given_收益统计查询参数为空_when_调用太阳能综合监控发电及收益汇总查询接口_then_抛出异常(){
        ResponseBean responseBean = solarMonitorController.querySolarRevenueStatistics(null, "zh_CN", request);
        Assert.assertEquals("The parameter is blank", responseBean.getMessage());
    }

    @Test
    public void UEDM_289212_given_存在太阳能监控对象_时间粒度为空_when_调用太阳能综合监控发电及收益汇总查询接口_then_抛出异常(){
        SolarRevenueOverviewDto dto = new SolarRevenueOverviewDto();
        ResponseBean responseBean = solarMonitorController.querySolarRevenueStatistics(dto, "zh_CN", request);
        Assert.assertEquals("The parameter is blank", responseBean.getMessage());
    }

    @Test
    public void UEDM_289210_given_存在太阳能监控对象_传入非累计时间粒度_传入开始时间及结束时间_when_调用太阳能综合监控发电及收益汇总查询接口_then_返回太阳能综合监控发电及收益汇总数据(){
        SolarRevenueOverviewDto dto = new SolarRevenueOverviewDto();
        dto.setGrain("year");
        dto.setBeginTime("2023");
        dto.setEndTime("2024");
        ResponseBean responseBean = solarMonitorController.querySolarRevenueStatistics(dto, "zh_CN", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289235_given_存在太阳能监控对象_传入开始时间及结束时间_传入时间粒度_when_调用太阳能发电及收益统计详情接口_then_返回太阳能发电及收益统计详情数据(){
        SolarRevenueOverviewDto dto = new SolarRevenueOverviewDto();
        dto.setGrain("month");
        dto.setLogicGroupId("r32.uedm.group-global");
        dto.setBeginTime("2024-03");
        dto.setEndTime("2024-04");
        ResponseBean responseBean = solarMonitorController.getSolarSummaryStatisticsDetail(dto, "zh_CN", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289224_given_收益统计查询参数为空_when_调用太阳能发电及收益统计详情接口_then_抛出异常(){
        ResponseBean responseBean = solarMonitorController.getSolarSummaryStatisticsDetail(null, "zh_CN", request);
        Assert.assertEquals(-101 , responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289233_given_存在太阳能监控对象_传入开始时间及结束时间_根据时间粒度获取表名为空_when_调用太阳能发电及收益统计详情接口_then_抛出异常(){
        SolarRevenueOverviewDto dto = new SolarRevenueOverviewDto();
        dto.setGrain("123");
        dto.setBeginTime("2023");
        dto.setEndTime("2024");
        ResponseBean responseBean = solarMonitorController.getSolarSummaryStatisticsDetail(dto, "zh_CN", request);
        Assert.assertEquals(-101 , responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289222_given_导出太阳能收益总览参数合法_when_调用导出太阳能产出和收益接口_then_导出太阳能产出和收益数据_向IMOP_LOG_MANAGE_TOPIC主题发送日志导出消息() throws UedmException {
        SolarRevenueOverviewExportBean exportBean = new SolarRevenueOverviewExportBean();
        exportBean.setLogicGroupId("r32.uedm.group-global");
        exportBean.setGrain("year");
        exportBean.setBeginTime("2024");
        exportBean.setEndTime("2024");
        List<ImageBean> beans = new ArrayList<>();
        ImageBean bean = new ImageBean();
        bean.setBase64Str("testBase64Str");
        bean.setImageName("testbane");
        bean.setXLine(20);
        bean.setYLine(10);
        bean.setDim("testDim");
        beans.add(bean);
        exportBean.setImages(beans);
        exportBean.setIsHistory("false");
        exportBean.setOrder("1");
        exportBean.setSort("desc");
        ResponseBean responseBean = solarMonitorController.exportGenerationAndRevenue(exportBean, request, response, "zh_CN");
        Assert.assertEquals(0,responseBean.getCode().intValue());
        Mockito.verify(msgSenderService, Mockito.atLeastOnce()).sendMsgAsync(Mockito.eq(OperlogBean.IMOP_LOG_MANAGE_TOPIC),Mockito.anyString());
    }

    @Test
    public void UEDM_289229_given_导出太阳能收益总览参数为空_when_调用导出太阳能产出和收益接口_then_抛出异常() throws UedmException {
        ResponseBean responseBean = solarMonitorController.exportGenerationAndRevenue(null, request, response, "zh_CN");
        Assert.assertEquals(-301,responseBean.getCode().intValue());
    }

    @Test
    public void UEDM_289231_given_导出太阳能监控详情参数合法_when_调用太阳能监控详情接口_then_导出太阳能监控详情_向IMOP_LOG_MANAGE_TOPIC主题发送日志导出消息() throws UedmException {
        SolarRevenueOverviewExportBean exportBean = new SolarRevenueOverviewExportBean();
        exportBean.setLogicGroupId("r32.uedm.group-global");
        exportBean.setGrain("all");
        exportBean.setBeginTime("2024-03");
        exportBean.setEndTime("2024-04");
        List<ImageBean> beans = new ArrayList<>();
        ImageBean bean = new ImageBean();
        bean.setBase64Str("testBase64Str");
        bean.setImageName("testbane");
        bean.setXLine(20);
        bean.setYLine(10);
        bean.setDim("testDim");
        beans.add(bean);
        exportBean.setImages(beans);
        exportBean.setIsHistory("false");
        exportBean.setOrder("1");
        exportBean.setSort("desc");
        try{
            ResponseBean responseBean = solarMonitorController.exportDetail(exportBean, request, response, "zh_CN");
            Assert.assertEquals(0,responseBean.getCode().intValue());

        }catch (Exception ignored) {
        }

        Mockito.verify(msgSenderService, Mockito.atLeastOnce()).sendMsgAsync(Mockito.eq(OperlogBean.IMOP_LOG_MANAGE_TOPIC),Mockito.anyString());
    }

    @Test
    public void UEDM_289227_given_导出太阳能监控详情参数为空_when_调用太阳能监控详情接口_then_抛出异常() throws UedmException {
        ResponseBean responseBean = solarMonitorController.exportDetail(null, request, response, "zh_CN");
        Assert.assertEquals(-301,responseBean.getCode().intValue());
    }
}