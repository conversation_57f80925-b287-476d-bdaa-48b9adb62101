/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：VoTest
 * 文件作者：00248587
 * 开发时间：2023/3/9
 */
package com.zte.uedm.battery.rpc.impl.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.battery.rpc.vo.MoAssetInstanceVo;
import org.junit.Assert;
import org.junit.Test;

public class VoTest
{
    @Test
    public void test() throws Exception {
        MoAssetInstanceVo moAssetInstanceVo = new MoAssetInstanceVo();
        PojoTestUtil.TestForPojo(moAssetInstanceVo.getClass());
        Assert.assertEquals(moAssetInstanceVo.toString(), new MoAssetInstanceVo().toString());
    }
}
