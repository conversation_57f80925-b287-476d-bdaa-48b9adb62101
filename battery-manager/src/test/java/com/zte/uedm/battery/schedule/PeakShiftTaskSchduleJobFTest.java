package com.zte.uedm.battery.schedule;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskPo;
import com.zte.uedm.battery.mapper.PeakShiftMapperFake;
import com.zte.uedm.battery.mapper.PeakShiftTaskDetailMapper;
import com.zte.uedm.battery.mapper.PeakShiftTaskMapper;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.SiteSpBatteryRelatedRealGroupRpc;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.service.SystemConfigService;
import com.zte.uedm.battery.service.impl.*;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.redis.service.RedisService;
import ft.fake.redis.CaffeineCacheManagerFake;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mock;


public class PeakShiftTaskSchduleJobFTest {
    @InjectMocks
    private PeakShiftTaskSchduleJob peakShiftTaskSchduleJob;

    @Mock
    private RedisService redisService;
    @Mock
    private SiteSpBatteryRelatedRealGroupRpc siteSpBatteryRelatedRealGroupRpc;
    @Mock
    private CommonCacheService cacheService;
    @Mock
    private SystemConfigService systemConfigService;
    @Mock
    private PeakShiftTaskMapper peakShiftTaskMapper;
    @Mock
    private PeakShiftTaskDetailMapper peakShiftTaskDetailMapper;
    @Mock
    private ConfigurationRpc configurationRpc;
    @Mock
    private CacheManager caffeineCacheManager;

    @Resource
    private PeakShiftTaskSchduleJobServiceImpl peakShiftTaskSchduleJobService = new PeakShiftTaskSchduleJobServiceImpl();
    @Resource
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseService = new PeakShiftConfigBaseServiceImpl();
    @Resource
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl = new SiteSpBatteryRelatedRpcImpl();
    @Resource
    private PeakShiftMapperFake peakShiftMapper = new PeakShiftMapperFake();
    @Resource
    private TemplateStrategyServiceImpl templateStrategyService = new TemplateStrategyServiceImpl();
    @Resource
    private GridStrategyServiceImpl strategyService = new GridStrategyServiceImpl();
    @Resource
    private PeakShiftIssuedTaskServiceImpl peakShiftIssuedTaskService = new PeakShiftIssuedTaskServiceImpl();
    @Resource
    private ConfigService configService;



    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
        Mockito.doReturn(0).when(systemConfigService).getTimeOut();
    }



}
