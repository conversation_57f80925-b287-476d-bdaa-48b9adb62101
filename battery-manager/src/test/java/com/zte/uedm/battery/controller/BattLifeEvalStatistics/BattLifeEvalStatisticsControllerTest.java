package com.zte.uedm.battery.controller.BattLifeEvalStatistics;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.BattLifeEvalStatisticsDto;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.BattLifeEvalStatisticsDtoTest;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.vo.BattLifeEvalStatisticsVoTest;
import com.zte.uedm.battery.service.BattLifeEvalStatisticsService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.Mockito.*;


public class BattLifeEvalStatisticsControllerTest {

    @InjectMocks
    private BattLifeEvalStatisticsController battLifeEvalStatisticsController;

    @Mock
    private BattLifeEvalStatisticsService service;

    private HttpServletRequest request;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        request = mock(HttpServletRequest.class);
    }

    @Test
    public void selectByConditionParamIsNull() throws Exception
    {
        try {
            battLifeEvalStatisticsController.selectByCondition(null,null,null);
        }catch (Exception e){
            Assert.assertEquals("123", e.getMessage());
        }
    }

    @Test
    public void selectByConditionTimeIsError() throws Exception
    {
            BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
            battLifeEvalStatisticsDto.setLogicGroupId("77");
            battLifeEvalStatisticsDto.setEvalTimeBegin("2062-06");
            battLifeEvalStatisticsDto.setEvalTimeEnd("2022-03");
            battLifeEvalStatisticsDto.checkTime();
            ResponseBean responseBean = battLifeEvalStatisticsController.selectByCondition(battLifeEvalStatisticsDto, "zh_CN", request);
            Assert.assertEquals(-304,(int) responseBean.getCode());
    }

    @Test
    public void selectByConditionSuccessfully(){
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        battLifeEvalStatisticsDto.setPageNo(1);
        PageInfo<BattLifeEvalStatisticsVoTest> pageInfo = new PageInfo<>();
        try {
            doReturn(pageInfo).when(service).selectByCondition(Mockito.any(),Mockito.any());
            ResponseBean responseBean = battLifeEvalStatisticsController.selectByCondition(battLifeEvalStatisticsDto, "zh_CN", request);
            Assert.assertEquals(0,(int) responseBean.getCode());
        } catch (UedmException e) {
            
        }
    }

    @Test
    public void selectByConditionFail(){
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        battLifeEvalStatisticsDto.setPageNo(1);
        PageInfo<BattLifeEvalStatisticsVoTest> pageInfo = new PageInfo<>();
        try {
            doThrow(new UedmException(-100,"123")).when(service).selectByCondition(Mockito.any(),Mockito.any());
            ResponseBean responseBean = battLifeEvalStatisticsController.selectByCondition(battLifeEvalStatisticsDto, "zh_CN", request);
            Assert.assertEquals(-1,(int) responseBean.getCode());
        } catch (UedmException e) {
            Assert.assertEquals("123",e.getMessage());
        }
    }

}
