package com.zte.uedm.battery.rpc.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftFileService;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterEntity;
import com.zte.uedm.battery.a_domain.aggregate.adapter.model.entity.AdapterPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldDSEntity;
import com.zte.uedm.battery.a_domain.aggregate.field.model.entity.FieldEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.MocEntity;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.UpDownloadFileRepository;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftTemplateFileService;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.DetailHistoryVo;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.peak.DetailHistoryResposeBean;
import com.zte.uedm.battery.bean.pv.PvNumberBean;
import com.zte.uedm.battery.controller.batterytesttask.dto.GetMoByConditionDto;
import com.zte.uedm.battery.domain.ConfigurationDataDomain;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.dto.FieldRecursiveParamsDto;
import com.zte.uedm.battery.rpc.dto.RecollectionCapabilityQueryDto;
import com.zte.uedm.battery.rpc.vo.SiteRecollectionCapabilityBean;
import com.zte.uedm.battery.service.DevicePeakCacheInfoService;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceSfBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.configuration.point.bean.RecordIndexBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.uedm.function.license.exception.LicenseException;
import com.zte.uedm.service.config.api.configuraiton.impl.AlarmCodeCustomServiceImpl;
import com.zte.uedm.service.config.api.configuraiton.vo.AlarmFunctionParameterVo;
import com.zte.uedm.service.config.api.configuraiton.vo.CustomAlarmCodeMappingVo;
import com.zte.uedm.service.config.api.configuraiton.vo.ResourceAlarmVo;
import com.zte.uedm.service.config.api.model.AlarmCodeService;
import com.zte.uedm.service.config.api.model.vo.AlarmCodeVo;
import com.zte.uedm.service.config.optional.MocOptional;
import com.zte.uedm.service.config.optional.ModelOptional;
import okhttp3.internal.http.RealResponseBody;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.util.reflection.FieldSetter;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@PrepareForTest(ConfigurationManagerRpcImpl.class)
public class ConfigurationManagerRpcImplTest
{
    @InjectMocks
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Mock
    private ConfigurationRpc configurationRpc;

    @Mock
    private PeakShiftFileService peakShiftFileService;
    @Mock
    private FieldCacheManager fieldCacheManager;
    @Mock
    private AlarmCodeService alarmCodeService;
    @Mock
    private AlarmCodeCustomServiceImpl alarmCodeCustomService;
    @Mock
    private StandardPointCacheManager standardPointCacheManager;

    @Mock
    private JsonService jsonService;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private CollectorCacheManager collectorCacheManager;
    @Mock
    private ResourceBaseCacheManager resourceBaseCacheManager;

    @Mock
    private AdapterCacheManager adapterCacheManager;
    @Mock
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils;
    @Mock
    private BatteryAttributeUtils batteryAttributeUtils;

    @Mock
    private LicenseSwitchService licenseMgr;

    @Mock
    private MocCacheManager mocCacheManager;
    @Mock
    private ConfigurationDataDomain configurationDataDomain;
    @Mock
    private DevicePeakCacheInfoService devicePeakCacheInfoService;

    @Mock
    private UpDownloadFileRepository upDownloadFileRepository;

    @Mock
    private PeakShiftTemplateFileService peakShiftTemplateFileService;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void getMonitorObjectListWhenIdsBigByAuthTest() throws Exception {
        List<String> ids = new ArrayList<>();
        String moc = "";
        List<DeviceEntity> list = new ArrayList<>();
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("id");
        String[] path = new String[]{"1","2","3"};
        deviceEntity.setPathId(path);
        list.add(deviceEntity);
        Mockito.when(deviceCacheManager.getDeviceByLogicGroupIdsAndMoc(ids,moc)).thenReturn(list);

        ConfigurationManagerRpcImpl spy = PowerMockito.spy(configurationManagerRpcImpl);
        PowerMockito.doReturn(Arrays.asList("id")).when(spy).getAuthPositionsByUser(Mockito.any());

        List<MoObjectConfiguration> result = spy.getMonitorObjectListWhenIdsBigByAuth(ids, moc, "admin");
//        assertEquals(1, result.size());
    }

    /* Started by AICoder, pid:z2b7dn4365xbf0c1474509376031a3931540e6ca */
    @Test
    public void testGetBpMonitorObjectListWhenIdsBigByAuth_Filtering() throws UedmException {
        try {
            MoObjectConfiguration mo1 = new MoObjectConfiguration();
            mo1.setId("1");
            MoObjectConfiguration mo2 = new MoObjectConfiguration();
            mo2.setId("2");
            MoObjectConfiguration mo3 = new MoObjectConfiguration();
            mo3.setId("3");
            List<MoObjectConfiguration> monitorObjectList = Arrays.asList(mo1, mo2, mo3);

            ResourceCollectorRelationEntity re1 = new ResourceCollectorRelationEntity();
            re1.setResourceId("1");
            re1.setCollectorId("c1");
            ResourceCollectorRelationEntity re2 = new ResourceCollectorRelationEntity();
            re2.setResourceId("2");
            re2.setCollectorId("c2");
            List<ResourceCollectorRelationEntity> relationEntities = Arrays.asList(re1, re2);

            DeviceEntity de1 = new DeviceEntity();
            de1.setId("1");
            de1.setMoc("SP");
            DeviceEntity de2 = new DeviceEntity();
            de2.setId("2");
            de2.setMoc("DCDP");
            List<DeviceEntity> deviceEntities = Arrays.asList(de1, de2);

            CollectorEntity ce1 = new CollectorEntity();
            ce1.setId("c1");
            ce1.setModel("MonitorDeviceBms");
            CollectorEntity ce2 = new CollectorEntity();
            ce2.setId("c2");
            ce2.setModel("MonitorDevicePad");
            List<CollectorEntity> collectorEntities = Arrays.asList(ce1, ce2);

            when(deviceCacheManager.selectAllDevice()).thenReturn(deviceEntities);
            when(collectorCacheManager.getAllCollector()).thenReturn(collectorEntities);
            when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(relationEntities);

            ConfigurationManagerRpcImpl spy = PowerMockito.spy(configurationManagerRpcImpl);
            PowerMockito.doReturn(monitorObjectList).when(spy).getMonitorObjectListWhenIdsBig(Mockito.anyList(), Mockito.anyString());
            PowerMockito.doReturn(Arrays.asList("1","2","3","c1","c2")).when(spy).getAuthPositionsByUser(Mockito.anyString());
            List<MoObjectConfiguration> result = spy.getBpMonitorObjectListWhenIdsBigByAuth(Arrays.asList("1", "2","3"), "moc","userName");
            assertEquals(1, result.size());
            assertEquals("3", result.get(0).getId());
            PowerMockito.doReturn(new ArrayList<>()).when(spy).getAuthPositionsByUser(Mockito.anyString());
            assertEquals(1, spy.getBpMonitorObjectListWhenIdsBigByAuth(Arrays.asList("1", "2","3"), "moc","userName").size());
        } catch (Exception e) {
//            assertEquals("Rpc interface call failed", e.getMessage());
        }
    }
    /* Ended by AICoder, pid:z2b7dn4365xbf0c1474509376031a3931540e6ca */

    @Test
    public void getSpMonitorObjectListWhenIdsBigByAuthTest() throws Exception {
        Mockito.when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(new ArrayList<>());
        ConfigurationManagerRpcImpl spy = PowerMockito.spy(configurationManagerRpcImpl);
        PowerMockito.doReturn(new ArrayList<>()).when(spy).getMonitorObjectListWhenIdsBigByAuth(Mockito.anyList(), Mockito.anyString(), Mockito.anyString());
        List<MoObjectConfiguration> result = spy.getSpMonitorObjectListWhenIdsBigByAuth(new ArrayList<>(), "moc", "admin");
        assertEquals(0, result.size());
    }



    @Test
    public void selectAllPvDcloadRelationTest() throws IOException, UedmException
    {
        try
        {
            List<DeviceEntity> allPvs = new ArrayList<>();
            DeviceEntity deviceEntity = new DeviceEntity();
            deviceEntity.setId("Id");
            deviceEntity.setMoc(MocOptional.PV.getId());
            allPvs.add(deviceEntity);
            when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenReturn(allPvs);
            Map<String, List<String>> result = new HashMap<>();
            when(resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Mockito.any(),Mockito.any())).thenReturn(result);
            configurationManagerRpcImpl.selectAllPvDcloadRelation();
        }
        catch (Exception e)
        {
            assertNotEquals("",e.getMessage());
        }
    }
    @Test
    public void selectAllPvDcloadRelationTest1() throws IOException, UedmException
    {
        try
        {
            List<DeviceEntity> allPvs = new ArrayList<>();
            DeviceEntity deviceEntity = new DeviceEntity();
            deviceEntity.setId("Id");
            deviceEntity.setMoc(MocOptional.PV.getId());
            allPvs.add(deviceEntity);
            when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenThrow(new Exception(""));
            Map<String, List<String>> result = new HashMap<>();
            when(resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Mockito.any(),Mockito.any())).thenReturn(result);
            configurationManagerRpcImpl.selectAllPvDcloadRelation();
        }
        catch (Exception e)
        {
            assertNotEquals("",e.getMessage());
        }
    }






    @Test
    public void getDcloadListByPvIdTest() throws IOException, UedmException
    {
        try
        {

            configurationManagerRpcImpl.getDcloadListByPvId("11");
        }
        catch (Exception e)
        {
            assertEquals("ConfigurationManagerRpcImpl pvId or pvDcloadRelationMap is null",e.getMessage());
        }
    }

    @Test
    public void getDcloadListByPvIdTestExc() throws IOException, UedmException
    {
        try
        {

            configurationManagerRpcImpl.getDcloadListByPvId("");
        }
        catch (Exception e)
        {
            Assert.assertEquals("ConfigurationManagerRpcImpl pvId or pvDcloadRelationMap is null",e.getMessage());
        }
    }


    /* Started by AICoder, pid:0d9afdb207becaf14623084d6146e4199c8177d2 */
    @Test
    public void testGetMonitorObjectById_ReturnsEmptyBean_WhenNoMatchingId() throws UedmException {
        // 设置模拟行为
        when(deviceCacheManager.selectDeviceById(anySet())).thenReturn(Collections.emptyList());

        // 调用方法
        MonitorObjectBean result = configurationManagerRpcImpl.selectById("nonexistentId");

        // 验证结果
        assertNotNull(result);
        assertNull(result.getId());
        assertNull(result.getName());
    }

    @Test
    public void testGetMonitorObjectById_ReturnsCorrectBean_WhenMatchingId() throws UedmException {
        // 创建模拟数据
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("1");

        // 设置模拟行为
        when(deviceCacheManager.selectDeviceById(anySet())).thenReturn(Arrays.asList(deviceEntity));

        // 调用方法
        MonitorObjectBean result = configurationManagerRpcImpl.selectById("1");

        // 验证结果
        assertNotNull(result);
        assertEquals("1", result.getId());
    }

    @Test
    public void testGetMonitorObjectById_HandlesException() throws UedmException {
        // 设置模拟行为以抛出异常
        when(deviceCacheManager.selectDeviceById(anySet())).thenThrow(new RuntimeException("Database error"));

        // 调用方法并验证异常
        assertThrows(UedmException.class, () -> configurationManagerRpcImpl.selectById("1"));
    }
    /* Ended by AICoder, pid:0d9afdb207becaf14623084d6146e4199c8177d2 */

    @Test
    public void getPvAlarmCodeTest() throws IOException, UedmException
    {
        try
        {
            Map<String, Map<String,String>> maps = new HashMap<>();
            Map<String,String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001","11");
            maps.put("pv.comp.missing", alarmCodeMap);
            maps.put("pv.comp.maintain.alarm", alarmCodeMap);
            configurationManagerRpcImpl.setPvAlarmCodeRelationMap(maps);
            configurationManagerRpcImpl.getPvAlarmCode();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void getPvAlarmCodeTest_null() throws IOException, UedmException
    {
        try
        {
            configurationManagerRpcImpl.getPvAlarmCode();
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }



    @Test
    public void selectMoByMocAndPositionTest_Exc() throws IOException, UedmException
    {
        try
        {
            Map<String,String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001","11");


            LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(new ArrayList<>(), "11");
            when(deviceCacheManager.getDeviceByLogicGroupIdsAndMoc(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException("Database error"));
            when(jsonService.objectToJson(Mockito.any())).thenReturn("");
            configurationManagerRpcImpl.selectMoByMocAndPosition(logicIdAndMocQueryBean, alarmCodeMap);
        }
        catch (Exception e)
        {
            Assert.assertEquals("Failed to select monitor objects",e.getMessage());
        }
    }

    @Test
    public void selectMoByMocAndPositionTest_Exc2() throws IOException, UedmException
    {
        try
        {
            Map<String,String> alarmCodeMap = new HashMap<>();
            alarmCodeMap.put("1000001","11");
            List<DeviceEntity> deviceEntities = new ArrayList<>();
            DeviceEntity deviceEntity = new DeviceEntity();
            deviceEntity.setId("id");
            deviceEntity.setName("name");
            deviceEntity.setParentId("parentId");
            deviceEntity.setPathName("pathName");
            deviceEntity.setTopo(new String[]{"topo"});
            deviceEntity.setPathId(new String[]{"topo"});
            deviceEntities.add(deviceEntity);
            LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(new ArrayList<>(), "11");
            when(deviceCacheManager.getDeviceByLogicGroupIdsAndMoc(Mockito.any(), Mockito.any())).thenReturn(deviceEntities);
            when(jsonService.objectToJson(Mockito.any())).thenReturn("");
            configurationManagerRpcImpl.selectMoByMocAndPosition(logicIdAndMocQueryBean, alarmCodeMap);
        }
        catch (Exception e)
        {
            Assert.assertEquals("Failed to select monitor objects",e.getMessage());
        }
    }

    @Test
    public void testCachePvAlarmCode(){
        List<AlarmCodeVo> alarmConfigBeans = new ArrayList<>();
        List<ResourceAlarmVo> resourceAlarmVos = new ArrayList<>();
        List<StandardPointEntity> pointListByMoc = new ArrayList<>();


        AlarmCodeVo alarmCodeVo = new AlarmCodeVo();
        alarmCodeVo.setCode("22222");
        alarmCodeVo.setMoc(MocOptional.PV.getId());
        List<AlarmFunctionParameterVo> parameters = new ArrayList<>();
        AlarmFunctionParameterVo alarmFunctionParameterVo = new AlarmFunctionParameterVo();
        alarmFunctionParameterVo.setPointId("pv.comp.missing");
        parameters.add(alarmFunctionParameterVo);
        alarmCodeVo.setParameters(parameters);
        alarmConfigBeans.add(alarmCodeVo);

        ResourceAlarmVo resourceAlarmVo = new ResourceAlarmVo();
        List<CustomAlarmCodeMappingVo> customAlarmCodeMappings = new ArrayList<>();
        CustomAlarmCodeMappingVo customAlarmCodeMappingVo = new CustomAlarmCodeMappingVo();
        customAlarmCodeMappingVo.setCode("2222");
        customAlarmCodeMappingVo.setParameters(parameters);
        resourceAlarmVo.setCustomAlarmCodeMappings(customAlarmCodeMappings);
        resourceAlarmVos.add(resourceAlarmVo);

        StandardPointEntity standardPointEntity = new StandardPointEntity();
        standardPointEntity.setId("pv.comp.missing");
        pointListByMoc.add(standardPointEntity);

        try {
            when(alarmCodeService.getAllAlarmCode()).thenReturn(alarmConfigBeans);
            when(alarmCodeCustomService.queryAll()).thenReturn(resourceAlarmVos);
            when(standardPointCacheManager.getStandardByMocs(Mockito.any())).thenReturn(pointListByMoc);
            configurationManagerRpcImpl.cachePvAlarmCode();
            Assert.assertEquals(1,alarmConfigBeans.size());

        } catch (Exception e) {
            throw new RuntimeException(e);

        }


    }

    @Test
    public void testCachePvAlarmCode1(){
        List<AlarmCodeVo> alarmConfigBeans = new ArrayList<>();
        List<ResourceAlarmVo> resourceAlarmVos = new ArrayList<>();
        List<StandardPointEntity> pointListByMoc = new ArrayList<>();


        AlarmCodeVo alarmCodeVo = new AlarmCodeVo();
        alarmCodeVo.setCode("22222");
        alarmCodeVo.setMoc(MocOptional.PV.getId());
        List<AlarmFunctionParameterVo> parameters = new ArrayList<>();
        AlarmFunctionParameterVo alarmFunctionParameterVo = new AlarmFunctionParameterVo();
        alarmFunctionParameterVo.setPointId("solar.pvmissing.alarm");
        alarmFunctionParameterVo.setMocId("da");
        parameters.add(alarmFunctionParameterVo);
        alarmCodeVo.setParameters(parameters);
        alarmConfigBeans.add(alarmCodeVo);

        ResourceAlarmVo resourceAlarmVo = new ResourceAlarmVo();
        List<CustomAlarmCodeMappingVo> customAlarmCodeMappings = new ArrayList<>();
        CustomAlarmCodeMappingVo customAlarmCodeMappingVo = new CustomAlarmCodeMappingVo();
        customAlarmCodeMappingVo.setCode("2222");
        customAlarmCodeMappingVo.setParameters(parameters);
        customAlarmCodeMappings.add(customAlarmCodeMappingVo);
        resourceAlarmVo.setCustomAlarmCodeMappings(customAlarmCodeMappings);
        resourceAlarmVos.add(resourceAlarmVo);

        StandardPointEntity standardPointEntity = new StandardPointEntity();
        standardPointEntity.setId("solar.pvmissing.alarm");
        pointListByMoc.add(standardPointEntity);

        try {
            when(alarmCodeService.getAllAlarmCode()).thenReturn(alarmConfigBeans);
            when(alarmCodeCustomService.queryAll()).thenReturn(resourceAlarmVos);
            when(standardPointCacheManager.getStandardByMocs(Mockito.any())).thenReturn(pointListByMoc);
            configurationManagerRpcImpl.cachePvAlarmCode();
            Assert.assertEquals(1,alarmConfigBeans.size());

        } catch (Exception e) {
            throw new RuntimeException(e);

        }


    }



    /* Started by AICoder, pid:c5652p2ca2ycc7f143c808b120842852c646b5ff */
    @Test
    public void testSelectSiteByPosition_ReturnsEmptyList_WhenNoMatchingSites() throws UedmException {
        // 设置模拟行为
        when(fieldCacheManager.getSiteByLogicGroupId(anyString())).thenReturn(Collections.emptyList());

        // 创建测试数据
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(Collections.singletonList("nonexistentId"),"testMOC");


        // 调用方法
        List<SiteDsBean> result = configurationManagerRpcImpl.selectSiteByPosition(logicIdAndMocQueryBean, new HashMap<>());

        // 验证结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void testSelectSiteByPosition_ReturnsCorrectList_WhenMatchingSites() throws UedmException {
        // 创建模拟数据
        FieldEntity fieldEntity = new FieldEntity();
        fieldEntity.setMoc("testMOC");

        // 设置模拟行为
        when(fieldCacheManager.getSiteByLogicGroupId(anyString())).thenReturn(Arrays.asList(fieldEntity));

        // 创建测试数据
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(Collections.singletonList("nonexistentId"),"testMOC");


        // 调用方法
        List<SiteDsBean> result = configurationManagerRpcImpl.selectSiteByPosition(logicIdAndMocQueryBean, new HashMap<>());

        // 验证结果
        assertEquals(1, result.size());

    }

    @Test
    public void testSelectSiteByPosition_HandlesException() throws UedmException {
        // 设置模拟行为以抛出异常
        when(fieldCacheManager.getSiteByLogicGroupId(anyString())).thenThrow(new RuntimeException("Database error"));

        // 创建测试数据
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(Collections.singletonList("nonexistentId"),"testMOC");


        // 调用方法并验证异常
        assertThrows(UedmException.class, () -> configurationManagerRpcImpl.selectSiteByPosition(logicIdAndMocQueryBean, new HashMap<>()));
    }
    /* Ended by AICoder, pid:c5652p2ca2ycc7f143c808b120842852c646b5ff */

    @Test
    public void getPvSpcuSpuNumberTest()
    {
        try
        {

            when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenReturn(new ArrayList<>());
            PvNumberBean pvNumberBean = configurationManagerRpcImpl.getPvSpcuSpuNumber();
            assertNotNull(pvNumberBean);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void test_buildResponseMap() throws UedmException {
        List<AlarmFunctionParameterVo> parameters = new ArrayList<>();
        AlarmFunctionParameterVo alarmFunctionParameterVo = new AlarmFunctionParameterVo();
        alarmFunctionParameterVo.setPointId("pointID");
        alarmFunctionParameterVo.setMocId("da");
        parameters.add(alarmFunctionParameterVo);
        Map<String, Map<String, String>> alarmCodeMapPoint = new HashMap<>();
        String id = "id";
        StandardPointEntity bean = new StandardPointEntity();
        bean.setId("pointID");
        CustomAlarmCodeMappingVo tempAlarmCodeMappingBean = new CustomAlarmCodeMappingVo();
        tempAlarmCodeMappingBean.setCode("2222");
        tempAlarmCodeMappingBean.setParameters(parameters);
        configurationManagerRpcImpl.buildResponseMap(alarmCodeMapPoint,id,bean,tempAlarmCodeMappingBean,"da");
        Assert.assertTrue(true);
    }

    @Test
    public void getPvSpcuSpuNumberTest_Exc()
    {
        try
        {

            when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenThrow(new Exception(""));
            configurationManagerRpcImpl.getPvSpcuSpuNumber();
        }
        catch (Exception e)
        {
            Assert.assertEquals(1,1);
        }
    }


    @Test
    public void getPathByIdList() throws IOException, UedmException
    {
        List<ResourceBaseEntity> entityList = new ArrayList<>();
        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setId("id");
        resourceBaseEntity.setPathName("pathName");
        resourceBaseEntity.setPathId(new String[]{"pathId"});
        entityList.add(resourceBaseEntity);
        Mockito.when(resourceBaseCacheManager.getEntityByIds(Mockito.anySet())).thenReturn(entityList);
        List<PathInfoBean> pathByIdList = configurationManagerRpcImpl.getPathByIdList(new ArrayList<>(), "1");
        assertEquals(1, pathByIdList.size());
    }

    @Test
    public void testDeleteFileInfo() throws Exception
    {
        // Setup
        // Configure ConfigurationRpc.deleteFileInfo(...).
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        responseBean.setTotal(1);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> value = mock(Call.class);
        when(value.execute()).thenReturn(response);

        when(jsonService.objectToJson(1)).thenReturn("1");

        // Run the test
        final Integer result = configurationManagerRpcImpl.deleteFileInfo(Arrays.asList("value"));

        // Verify the results
        assertEquals(0, result.intValue());
    }

    @Test
    public void testDeleteFileInfo_JsonServiceThrowsUedmException() throws Exception
    {
        // Setup
        // Configure ConfigurationRpc.deleteFileInfo(...).
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(1);
        responseBean.setTotal(1);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> value = mock(Call.class);
        when(value.execute()).thenReturn(response);
        when(jsonService.objectToJson(1)).thenReturn("1");
        UedmException flag=null;
        try
        {
            doThrow(new com.zte.uedm.basis.exception.UedmException(-1,"")).when(peakShiftTemplateFileService).deleteFiles(any());
            final Integer result = configurationManagerRpcImpl.deleteFileInfo(Arrays.asList("value"));
        }catch (UedmException e)
        {
            flag = new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectFileById() throws IOException, UedmException
    {
//        UedmException flag=null;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectFileById(new ArrayList<>(),"1");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
//            flag=new UedmException(-100,"123");
        }
//        Assert.assertNull(flag);
    }
    @Test
    public void selectFileByIdCode1() throws IOException, UedmException
    {
        UedmException flag=null;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            doThrow(new com.zte.uedm.basis.exception.UedmException(-1, "")).when(upDownloadFileRepository).selectByIds(any());
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectFileById(new ArrayList<>(),"1");
        }
        catch (Exception e)
        {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }


    @Test
    public void selectBattExtendAttributeTest() throws UedmException
    {
        Assert.assertEquals(0, configurationManagerRpcImpl.selectBattExtendAttribute(null).getTotal());
        Map<String, Object> attribute = new HashMap<>();
        attribute.put("is_loop","1");
        attribute.put("type", "1");
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setExattribute(attribute);
        Mockito.when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenReturn(Collections.singletonList(deviceEntity));
        Assert.assertEquals(1, configurationManagerRpcImpl.selectBattExtendAttribute(new ServiceBaseInfoBean("userName","lang")).getTotal());
    }

    /* Started by AICoder, pid:l3e3160c94xbb87147270a1de092634df313ac93 */
    @Test
    public void given_resource_base_list_when_get_children_logic_group_id_then_return_filtered_list() {
        // Given
        ResourceBaseEntity entity1 = new ResourceBaseEntity();
        entity1.setId("1");
        entity1.setParentId("r32.uedm.group-global");
        entity1.setModel(ModelOptional.FIELD.getId());
        ResourceBaseEntity entity2 = new ResourceBaseEntity();
        entity2.setId("2");
        entity2.setParentId("1");
        entity2.setModel(ModelOptional.GROUP.getId());
        ResourceBaseEntity entity3 = new ResourceBaseEntity();
        entity3.setId("3");
        entity3.setParentId("2");
        entity3.setModel(ModelOptional.DEVICE.getId());
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(Arrays.asList(entity1, entity2, entity3));

        // When
        List<ResourceBaseBean> result = configurationManagerRpcImpl.getChildrenLogicGroupId("1", "siteLevel","powerSupplyScene", "userName","languageOption");

        // Then
        assertEquals(3, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("2", result.get(1).getId());

        // Given
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(new ArrayList<>());

        // When
        List<ResourceBaseBean> emptyResult = configurationManagerRpcImpl.getChildrenLogicGroupId("1", "siteLevel","powerSupplyScene", "userName","languageOption");

        // Then
        assertEquals(0, emptyResult.size());

        // Given
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(new ArrayList<>());

        // When
        List<ResourceBaseBean> nullResult = configurationManagerRpcImpl.getChildrenLogicGroupId(null, "siteLevel","powerSupplyScene", "userName","languageOption");

        // Then
        assertEquals(0, nullResult.size());
    }
    /* Ended by AICoder, pid:l3e3160c94xbb87147270a1de092634df313ac93 */

    /* Started by AICoder, pid:y9d871794efd59d1437309da402d07253331cc3b */
    @Test
    public void given_queryFieldAndFilter_when_validParameters_then_filterAndSetIds() {
        // 准备测试数据
        FieldRecursiveParamsDto recursiveDto = new FieldRecursiveParamsDto();
        recursiveDto.setSiteLevel("siteLevel");
        recursiveDto.setPowerSupplyScene("powerSupplyScene");
        List<BaseCacheBean> fieldCacheBeans = Collections.singletonList(new FieldEntity());
        try {
            when(fieldCacheManager.queryAll()).thenReturn(fieldCacheBeans);
        } catch (com.zte.uedm.basis.exception.UedmException ignored) {

        }
        List<FieldEntity> fieldEntities = Collections.singletonList(new FieldEntity());
        when(realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(fieldEntities)).thenReturn(Collections.singletonList(new FieldDSEntity()));

        // 执行测试方法
        configurationManagerRpcImpl.queryFieldAndFilter(recursiveDto);

        try {
            // 验证调用次数和返回值
            verify(fieldCacheManager, times(1)).queryAll();
        } catch (com.zte.uedm.basis.exception.UedmException ignored) {

        }
        verify(realGroupRelationSiteUtils, times(1)).fieldEntityConvertFieldDSEntity(Mockito.any());
        verifyNoMoreInteractions(fieldCacheManager);
        verifyNoMoreInteractions(realGroupRelationSiteUtils);
        // 这里可以添加更多的验证来确保递归DTO中的ID集合是正确的
    }
    /* Ended by AICoder, pid:y9d871794efd59d1437309da402d07253331cc3b */

    @Test
    public void getSiteChildrenLogicGroupIdTest() throws UedmException {
        Assert.assertEquals(configurationManagerRpcImpl.getSiteChildrenLogicGroupId("","","","","").size(),0);
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(null);
        Assert.assertEquals(configurationManagerRpcImpl.getSiteChildrenLogicGroupId("id","","","","").size(),0);

        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setId("id");
        Map<String, FieldEntity> fieldEntities = new HashMap<>();
        FieldEntity field = new FieldEntity();
        field.setMoc("r32.uedm.field.site");
        field.setParentId("id");
        field.setId("siteId");
        fieldEntities.put("siteId", field);
        List<ResourceBaseEntity> result = Collections.singletonList(field);
        Mockito.when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(resourceBaseEntity);
        Mockito.when(fieldCacheManager.getFieldMapBeans()).thenReturn(fieldEntities);
        ConfigurationManagerRpcImpl spy = PowerMockito.spy(configurationManagerRpcImpl);
        Mockito.doReturn(result).when(spy).filterByPriorityAndPowerSupply(Mockito.any(), Mockito.any(), Mockito.anyString());
        DeviceEntity device = new DeviceEntity();
        device.setMoc("r32.uedm.device");
        device.setParentId("siteId");
        device.setId("deviceId");
        device.setPathId(new String[]{"siteId","deviceId"});
        Mockito.when(deviceCacheManager.selectAllDevice()).thenReturn(Collections.singletonList(device));
        Mockito.doReturn(new ArrayList<>()).when(spy).getAuthPositionsByUser(Mockito.anyString());
        Assert.assertEquals(spy.getSiteChildrenLogicGroupId("id","","","","").size(),3);
        Mockito.doReturn(Collections.singletonList("deviceId")).when(spy).getAuthPositionsByUser(Mockito.anyString());
        Assert.assertEquals(spy.getSiteChildrenLogicGroupId("id","","","","").size(),1);
        Mockito.doThrow(new UedmException(1,"")).when(spy).getAuthPositionsByUser(Mockito.anyString());
        Assert.assertEquals(spy.getSiteChildrenLogicGroupId("id","","","","").size(),3);
    }
    @Test
    public void filterByPriorityAndPowerSupplyTest(){
        FieldEntity fieldEntity = new FieldEntity();
        Map<String,Object> attribute = new HashMap<>();
        attribute.put("priority","1");
        attribute.put("power_supply", Arrays.asList("1","2"));
        fieldEntity.setExattribute(attribute);
        List<FieldEntity> fieldEntities = Collections.singletonList(fieldEntity);
        Assert.assertEquals(configurationManagerRpcImpl.filterByPriorityAndPowerSupply(fieldEntities,"","").size(),1);
        Assert.assertEquals(configurationManagerRpcImpl.filterByPriorityAndPowerSupply(fieldEntities,"1","").size(),1);
        Assert.assertEquals(configurationManagerRpcImpl.filterByPriorityAndPowerSupply(fieldEntities,"1","1-2").size(),1);
        fieldEntity.setExattribute("1");
        Assert.assertEquals(configurationManagerRpcImpl.filterByPriorityAndPowerSupply(fieldEntities,"1","1-2").size(),0);
        Assert.assertEquals(configurationManagerRpcImpl.filterByPriorityAndPowerSupply(fieldEntities,"","1-2").size(),0);
    }

    /* Started by AICoder, pid:d45ca5ceffa42be14cf108c870d2cc968cd2c133 */
    @Test
    public void getSiteByMoIds04() throws UedmException {
        // given
        List<String> moIdList = new ArrayList<>();
        String userName = "testUser";

        // when
        Map<String, SiteBean> result = configurationManagerRpcImpl.getSiteByMoIds(moIdList, userName);

        // then
        assertEquals(0, result.size());
    }

    @Test
    public void getSiteByMoIds05() throws UedmException {
        // given
        List<String> moIdList = new ArrayList<>();
        String userName = "testUser";
        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setId("1");
        resourceBaseEntity.setMoc("1");
        resourceBaseEntity.setName("1");
        String[] arr1 = {"A", "B", "测试"};
        resourceBaseEntity.setPathId(arr1);
        FieldEntity fieldEntity = new FieldEntity();
        fieldEntity.setId("1");
        when(fieldCacheManager.selectByIds(anySet())).thenReturn(Collections.singletonList(fieldEntity));
        FieldDSEntity fieldDSEntity = new FieldDSEntity();
        fieldDSEntity.setId("1");
        fieldDSEntity.setMoc("r32.uedm.field.site");
        when(realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(anyList())).thenReturn(Collections.singletonList(fieldDSEntity));
        when(resourceBaseCacheManager.getResourceBaseByIds(anySet())).thenReturn(Collections.singletonList(resourceBaseEntity));
        // when
        Map<String, SiteBean> result = configurationManagerRpcImpl.getSiteByMoIds(moIdList, userName);

        // then
        assertEquals(1, result.size());
    }

    /* Started by AICoder, pid:s49d1e34a9mbad5143b0093f10c0487599f141c2 */
    @Test
    public void given_valid_moId_when_getSiteInfoByMoId_then_return_SiteBean() throws UedmException {
        // given
        String moId = "1";
        ResourceBaseEntity monitorObject = new ResourceBaseEntity();
        monitorObject.setPathId(new String[]{"1"});
        FieldDSEntity fieldDSEntity = new FieldDSEntity();
        fieldDSEntity.setMoc(MocOptional.SITE.getId());
        fieldDSEntity.setId("1");
        fieldDSEntity.setName("Site 1");
        fieldDSEntity.setSiteLevel("1");
        fieldDSEntity.setFieldPowerSupply(Arrays.asList("1","2","3","4"));


        when(resourceBaseCacheManager.getResourceBaseById(Mockito.any())).thenReturn(monitorObject);

        when(fieldCacheManager.selectByIds(Mockito.any())).thenReturn(Collections.singletonList(new FieldEntity()));

        when(realGroupRelationSiteUtils.fieldEntityConvertFieldDSEntity(Mockito.any()))
                .thenReturn(Collections.singletonList(fieldDSEntity));
        // when
        SiteBean result = configurationManagerRpcImpl.getSiteInfoByMoId(moId);

        // then
        assertEquals("1", result.getId());
        assertEquals(MocOptional.SITE.getId(), result.getMoc());
        assertEquals("Site 1", result.getName());
        assertEquals("1", result.getSiteLevel());
        assertEquals("1-2-3-4", result.getPowerSupplyScene());
    }

    @Test
    public void given_invalid_moId_when_getSiteInfoByMoId_then_return_null() throws UedmException {
        // given
        String moId = "1";
        ResourceBaseEntity monitorObject = new ResourceBaseEntity();
        monitorObject.setPathId(new String[]{"1"});
        List<FieldEntity> fieldEntities = new ArrayList<>();

        when(resourceBaseCacheManager.getResourceBaseById(moId)).thenReturn(monitorObject);
        when(fieldCacheManager.selectByIds(new HashSet<>(Arrays.asList(monitorObject.getPathId())))).thenReturn(fieldEntities);

        // when
        SiteBean result = configurationManagerRpcImpl.getSiteInfoByMoId(moId);

        // then
        assertNull(result);
    }
    /* Ended by AICoder, pid:s49d1e34a9mbad5143b0093f10c0487599f141c2 */


    /* Started by AICoder, pid:ka4be2329e048451484e08129052354128912dac */
    @Test
    public void testGetMoListBySiteId() throws UedmException {
        // 测试空的siteId
        SelectMoListBySiteIdVo selectMoListBySiteIdVo = new SelectMoListBySiteIdVo();
        selectMoListBySiteIdVo.setSiteId("");
        List<MonitorObjectBean> result = configurationManagerRpcImpl.getMoListBySiteId(selectMoListBySiteIdVo, "userName");
        assertTrue(result.isEmpty());

        // 测试null的siteId
        selectMoListBySiteIdVo.setSiteId(null);
        result = configurationManagerRpcImpl.getMoListBySiteId(selectMoListBySiteIdVo, "userName");
        assertTrue(result.isEmpty());

        // 测试有效的siteId和moList
        selectMoListBySiteIdVo.setSiteId("1");
        List<String> moList = new ArrayList<>();
        moList.add("moc1");
        selectMoListBySiteIdVo.setMoList(moList);
        List<ResourceBaseEntity> allResourceBase = new ArrayList<>();
        ResourceBaseEntity entity1 = new ResourceBaseEntity();
        entity1.setId("11");
        entity1.setMoc("moc1");
        entity1.setPathId(new String[]{"1","2","4"});
        allResourceBase.add(entity1);
        ResourceBaseEntity entity2 = new ResourceBaseEntity();
        entity2.setId("2");
        entity2.setMoc("moc2");
        entity2.setPathId(new String[]{"1","2","4"});
        allResourceBase.add(entity2);
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);
        result = configurationManagerRpcImpl.getMoListBySiteId(selectMoListBySiteIdVo, "userName");
        assertEquals(1, result.size());
        assertEquals("11", result.get(0).getId());

    }
    /* Ended by AICoder, pid:ka4be2329e048451484e08129052354128912dac */

    @Test
    public void getAllSiteTest()
    {
        /* Started by AICoder, pid:ef409m03c5p247814d0e0a25205e7b0496970630 */
        FieldEntity fieldEntity = new FieldEntity();
        fieldEntity.setId("fieldId");
        when(fieldCacheManager.queryAllField()).thenReturn(Arrays.asList(fieldEntity));

        Map<String, FieldEntity> result = configurationManagerRpcImpl.getAllSite();

        Assert.assertEquals("The size of the result map should be 1", 1, result.size());
        /* Ended by AICoder, pid:ef409m03c5p247814d0e0a25205e7b0496970630 */
    }

    @Test
    public void getAllSite_ExcTest2()
    {
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
//            doReturn(value).when(configurationRpc).getAllSiteByBeanMoc(any(), any(), any());
            doReturn(response).when(value).execute();

            List<SiteBean> list = new ArrayList<>();
            doReturn(list).when(jsonService).jsonToObject(any(), any(), any());
            Map<String, FieldEntity> result = configurationManagerRpcImpl.getAllSite();
            Assert.assertEquals(result.size(), 0);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    /* Started by AICoder, pid:w11e2s998ddd90d1481c08e5a0c1c73c7247922e */
    @Test
    public void testGetRootAllChildren() {
        // given
        List<ResourceBaseEntity> allResourceBase = new ArrayList<>();
        /* Started by AICoder, pid:he23e8790cj0d7a14daf0b5e40c70b04e008df17 */
        ResourceBaseEntity entity1 = new ResourceBaseEntity();
        entity1.setId("1");
        entity1.setParentId("r32.uedm.group-global");
        entity1.setModel("r32.uedm.device");
        entity1.setPathId(new String[]{"r32.uedm.group-global"});
        ResourceBaseEntity entity2 = new ResourceBaseEntity();
        entity2.setId("2");
        entity2.setParentId("1");
        entity2.setMoc("r32.uedm.field.site");
        entity2.setModel("r32.uedm.field");
        entity2.setPathId(new String[]{"r32.uedm.group-global"});
        /* Ended by AICoder, pid:he23e8790cj0d7a14daf0b5e40c70b04e008df17 */
        allResourceBase.add(entity1);
        allResourceBase.add(entity2);
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);

        // when
        List<ResourceBaseBean> result = configurationManagerRpcImpl.getRootAllChildren("user", "en");

        // then
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getId());

        // given
        allResourceBase = new ArrayList<>();
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);

        // when
        result = configurationManagerRpcImpl.getRootAllChildren("user", "en");

        // then
        assertTrue(result.isEmpty());

    }
    /* Ended by AICoder, pid:w11e2s998ddd90d1481c08e5a0c1c73c7247922e */

    @Test
    public void getRelationDeviceMoListByMoId()
    {
        GetRelationDeviceMoListDto getRelationDeviceMoListDto = new GetRelationDeviceMoListDto();
        List<DeviceEntity> list = configurationManagerRpcImpl.getRelationDeviceMoListByMoId(getRelationDeviceMoListDto);
        assertEquals(0 ,list.size());
    }

    @Test
    public void getRelationDeviceMoListByMoId1()
    {
        /* Started by AICoder, pid:dd413867eap28c314192088940a6940c0ad4a191 */
        GetRelationDeviceMoListDto getRelationDeviceMoListDto = new GetRelationDeviceMoListDto();
        List<DeviceEntity> list = configurationManagerRpcImpl.getRelationDeviceMoListByMoId(getRelationDeviceMoListDto);

        assertEquals(0, list.size());
        /* Ended by AICoder, pid:dd413867eap28c314192088940a6940c0ad4a191 */
    }

    @Test
    public void selectBattPackObjectTest() throws UedmException {
        String s = configurationManagerRpcImpl.selectBattPackObject(null, null);
        assertEquals(null ,s);
    }

    @Test
    public void selectBattPackObjectTest2() throws UedmException
    {
        /* Started by AICoder, pid:b0500u90d1j6e6914afc09b5f03383025e990498 */
        DeviceEntity entity = new DeviceEntity();
        entity.setId("id");
        entity.setMoc("r32.uedm.device.batteryset");
        entity.setParentId("parentId");
        when(deviceCacheManager.getDeviceById(anyString())).thenReturn(entity);

        String id = configurationManagerRpcImpl.selectBattPackObject("id", "admin");

        assertEquals("id", id);
        /* Ended by AICoder, pid:b0500u90d1j6e6914afc09b5f03383025e990498 */
    }

    @Test
    public void selectBattPackObjectTest3() throws IOException, UedmException, com.zte.uedm.basis.exception.UedmException {
        /* Started by AICoder, pid:m46e6j16a9cf9841409a0b1870720c1593b8d24d */
        Map<String, List<String>> relationMap = new HashMap<>();
        relationMap.put("id", Arrays.asList("id"));
        when(resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Mockito.anyList(), any()))
                .thenReturn(relationMap);

        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("id");
        deviceEntity.setMoc("r32.uedm.device.batteryset");
        when(deviceCacheManager.getDeviceListByIds(Mockito.anyList())).thenReturn(Arrays.asList(deviceEntity));

        DeviceEntity entity = new DeviceEntity();
        entity.setId("id");
        entity.setMoc("r32.uedm.device.battery");
        entity.setParentId("parentId");
        when(deviceCacheManager.getDeviceById(anyString())).thenReturn(entity);

        String id = configurationManagerRpcImpl.selectBattPackObject("id", "admin");
        assertEquals("id", id);
        /* Ended by AICoder, pid:m46e6j16a9cf9841409a0b1870720c1593b8d24d */
    }


    @Test
    public void getPathsByIds() throws IOException, UedmException
    {
        List<String> ids = new ArrayList<>();
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.getPathsByIds(Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.getPathsByIds(ids);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void getPathsByIds1() throws IOException, UedmException
    {
        List<String> ids = new ArrayList<>();
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.getPathsByIds(Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.getPathsByIds(ids);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }
    }
    @Test
    public void getPathsByIds2() throws IOException, UedmException
    {
        List<String> ids = new ArrayList<>();
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(-1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.getPathsByIds(Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.getPathsByIds(ids);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }

    @Test
    public void getPathsByIds3() throws IOException, UedmException
    {
        List<String> ids = new ArrayList<>();
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.getPathsByIds(Mockito.any())).thenThrow(new RuntimeException());
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.getPathsByIds(ids);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }

    @Test
    public void selectRecordIndexByConditionTest() throws IOException, UedmException
    {
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.selectRecordIndexByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectRecordIndexByCondition(new RecordIndexBean(), 1, 10, "");
            when(configurationRpc.selectRecordIndexByConditionAllName(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(value);
            configurationManagerRpcImpl.selectRecordIndexByConditionAllName(new RecordIndexBean(), 1, 10);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getErrorId());
        }
    }
    @Test
    public void selectRecordIndexByConditionTest_Exc() throws IOException, UedmException
    {
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.selectRecordIndexByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectRecordIndexByCondition(new RecordIndexBean(), 1, 10, "");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }

    @Test
    public void selectRecordIndexByConditionAllNameTest_Exc() throws IOException, UedmException
    {
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.selectRecordIndexByConditionAllName(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectRecordIndexByConditionAllName(new RecordIndexBean(), 1, 10);
        }catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }

    @Test
    public void getChildByLogicGroupId() throws IOException
    {
        try {
            List<ResourceBaseBean> resourceBaseBeans  = new ArrayList<>();
            Call<ResponseBean> value = mock(Call.class);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);

            when(configurationRpc.getChildByLogicGroupId(Mockito.any(),Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            Mockito.doReturn(resourceBaseBeans).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any());
            configurationManagerRpcImpl.getChildByLogicGroupId("77","77");
        }catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }
    @Test
    public void getChildByLogicGroupId1() throws IOException
    {
        try {
            List<ResourceBaseBean> resourceBaseBeans  = new ArrayList<>();
            Call<ResponseBean> value = mock(Call.class);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            responseBean.setTotal(7);
            Response<ResponseBean> response = Response.success(responseBean);

            when(configurationRpc.getChildByLogicGroupId(Mockito.any(),Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            Mockito.doReturn(resourceBaseBeans).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any());
            configurationManagerRpcImpl.getChildByLogicGroupId("77","77");
        }catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }
    @Test
    public void getChildByLogicGroupId2() throws IOException
    {
        try {
            List<ResourceBaseBean> resourceBaseBeans  = new ArrayList<>();
            Call<ResponseBean> value = mock(Call.class);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            responseBean.setTotal(7);
            Response<ResponseBean> response = Response.success(responseBean);

            List<TreeBean> treeList = new ArrayList<>();
            TreeBean treeBean = new TreeBean();
            treeBean.setId("77");
            treeList.add(treeBean);

            when(configurationRpc.getChildByLogicGroupId(Mockito.any(),Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            Mockito.doReturn("treeList").when(jsonService).objectToJson(Mockito.any());
            Mockito.doReturn(treeList).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            configurationManagerRpcImpl.getChildByLogicGroupId("77","77");
        }catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        }
    }


    @Test
    public void getChildMonitorObjectsNormal() {
        try {
            setRelationBefore();
            Map<String, List<MoBasicInfoVo>> result = configurationManagerRpcImpl.getChildMonitorObjects(Arrays.asList("id"), "r32.uedm.device.battery");
            assertEquals(1 ,result.size());
        }
        catch (Exception e)
        {
            assertEquals("", e.getMessage());
        }
    }

    @Test
    public void getMoByLogIdAndName() throws UedmException {
        ResourceBaseEntity entity = new ResourceBaseEntity();
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(Arrays.asList(entity));
        GetMoByConditionDto getMoByConditionDto=new GetMoByConditionDto();
        Pair<List<PathInfoBean>, Integer> moByLogIdAndName = configurationManagerRpcImpl.getMoByLogIdAndName(getMoByConditionDto, "2");
        assertEquals(moByLogIdAndName.getLeft().size(),0);
    }

    @Test
    public void getMoByLogIdAndName2() throws UedmException
    {
        ResourceBaseEntity entity = new ResourceBaseEntity();
        entity.setName("entityname");
        entity.setMoc("moc");
        entity.setPathId(new String[]{"1","2"});
        entity.setPathName("1/2");
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(Arrays.asList(entity));
        GetMoByConditionDto dto=new GetMoByConditionDto();
        dto.setLogroupId(MocOptional.GLOBAL.getId());
        dto.setMoc("moc");
        dto.setName("name");
        Pair<List<PathInfoBean>, Integer> moByLogIdAndName = configurationManagerRpcImpl.getMoByLogIdAndName(dto, "2");
        assertEquals(moByLogIdAndName.getLeft().size(),1);
    }
    @Test
    public void getMoByLogIdAndName3() throws IOException, UedmException
    {
        ResourceBaseEntity entity = new ResourceBaseEntity();
        entity.setName("entityname");
        entity.setMoc("moc");
        entity.setPathId(new String[]{"1","2"});
        entity.setPathName("1/2");
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(Arrays.asList(entity));
        GetMoByConditionDto dto=new GetMoByConditionDto();
        dto.setLogroupId("1");
        dto.setMoc("moc");
        dto.setName("name");
        Pair<List<PathInfoBean>, Integer> moByLogIdAndName = configurationManagerRpcImpl.getMoByLogIdAndName(dto, "2");
        assertEquals(moByLogIdAndName.getLeft().size(),1);
    }

    @Test
    public void getChildMonitorObjectsEx()
    {
        try
        {
            Map<String, List<MoBasicInfoVo>> result = configurationManagerRpcImpl.getChildMonitorObjects(null, null);
            assertEquals(0 ,result.size());
        }
        catch (Exception e)
        {
            assertEquals("", e.getMessage());
        }
    }

    @Test
    public void getChildBattLoopsNormal() {
        try {
            /* Started by AICoder, pid:5a5ad25a28q43f714b050b74b0784508c895d3c8 */
            setRelationBefore();
            when(batteryAttributeUtils.getIsLoop(Mockito.any())).thenReturn(true);

            Map<String, List<MoBasicInfoVo>> result = configurationManagerRpcImpl.getChildBattLoops(Arrays.asList("id"));
            assertEquals(1, result.size());
            /* Ended by AICoder, pid:5a5ad25a28q43f714b050b74b0784508c895d3c8 */
        } catch (Exception e) {
            assertEquals("", e.getMessage());
        }
    }

    @Test
    public void getChildBattLoopsEx()
    {
        try
        {
            Map<String, List<MoBasicInfoVo>> result = configurationManagerRpcImpl.getChildBattLoops(null);
            assertEquals(0,result.size());
        }
        catch (Exception e)
        {
            assertEquals("", e.getMessage());
        }
    }

    /* Started by AICoder, pid:rfbd2r0535zeece146fa0b7bb0390b3dfab021a7 */
    @Test
    public void testSelectAcdpIdsByDeviceIds() throws UedmException {
        // given
        List<String> moIdList = Arrays.asList("1", "2");
        String moc = "test";
        List<ResourceCollectorRelationEntity> allEntity = new ArrayList<>();
        List<ResourceBaseEntity> allResourceBase = new ArrayList<>();
        when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(allEntity);
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);

        // when
        Map<String, List<String>> result = configurationManagerRpcImpl.selectAcdpIdsByDeviceIds(moIdList, moc);

        // then
        assertNotNull(result);

        // 添加额外的测试用例
        moIdList = new ArrayList<>();
        result = configurationManagerRpcImpl.selectAcdpIdsByDeviceIds(moIdList, moc);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:rfbd2r0535zeece146fa0b7bb0390b3dfab021a7 */

    /* Started by AICoder, pid:sf5eaoe434239ca1413f09c7115bf51b0ce01e9b */
    @Test
    public void given_moIdList_is_empty_when_selectAcdpIdsByDeviceIds_then_return_empty_map() throws UedmException {
        // given
        List<String> moIdList = new ArrayList<>();
        String moc = "moc";

        // when
        Map<String, List<String>> result = configurationManagerRpcImpl.selectAcdpIdsByDeviceIds(moIdList, moc);

        // then
        assertEquals(0, result.size());
    }

    /* Started by AICoder, pid:5e32d3955aa195d142970942507e41378968d35b */
    @Test
    public void given_moIdList_is_not_empty_and_moc_is_match_when_selectAcdpIdsByDeviceIds_then_return_map() throws UedmException {
        // given
        List<String> moIdList = Arrays.asList("resourceId1", "resourceId2");
        String moc = "moc";
        List<ResourceCollectorRelationEntity> allEntity = new ArrayList<>();
        ResourceCollectorRelationEntity entity1 = new ResourceCollectorRelationEntity();
        entity1.setCollectorId("collectorId1");
        entity1.setResourceId("resourceId1");
        ResourceCollectorRelationEntity entity2 = new ResourceCollectorRelationEntity();
        entity2.setCollectorId("collectorId2");
        entity2.setResourceId("resourceId2");
        allEntity.add(entity1);
        allEntity.add(entity2);

        List<ResourceBaseEntity> allResourceBase = new ArrayList<>();
        ResourceBaseEntity baseEntity1 = new ResourceBaseEntity();
        baseEntity1.setId("resourceId1");
        baseEntity1.setMoc(moc);
        ResourceBaseEntity baseEntity2 = new ResourceBaseEntity();
        baseEntity2.setId("resourceId2");
        baseEntity2.setMoc(moc);
        allResourceBase.add(baseEntity1);
        allResourceBase.add(baseEntity2);

        when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(allEntity);
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);

        // when
        Map<String, List<String>> result = configurationManagerRpcImpl.selectAcdpIdsByDeviceIds(moIdList, moc);

        // then
        assertEquals(2, result.size());
        assertEquals(Arrays.asList("resourceId1"), result.get("resourceId1"));
        assertEquals(Arrays.asList("resourceId2"), result.get("resourceId2"));
    }
    /* Ended by AICoder, pid:5e32d3955aa195d142970942507e41378968d35b */
    /* Ended by AICoder, pid:sf5eaoe434239ca1413f09c7115bf51b0ce01e9b */


    /* Started by AICoder, pid:25871b4d0ao022514e4408a810f5a90a52062843 */
    @Test
    public void selectMonitorObjectCorrelativeObjectsTest() throws UedmException {
        setRelationBefore();
        List<MoBasicInfoVo> moBasicInfoVos = configurationManagerRpcImpl.selectMonitorObjectCorrelativeObjects("id", "r32.uedm.device.battery");
        assertEquals(1, moBasicInfoVos.size());
    }
    /* Ended by AICoder, pid:25871b4d0ao022514e4408a810f5a90a52062843 */

    /* Started by AICoder, pid:0da2bab6c0m8e6e14e6109dae0accb08b0a5ccf9 */
    @Test
    public void selectMonitorObjectCorrelativeObjectsTest_Exception1() throws UedmException {
        List<MoBasicInfoVo> moBasicInfoVos = configurationManagerRpcImpl.selectMonitorObjectCorrelativeObjects(null, null);
        assertEquals(0, moBasicInfoVos.size());
    }
    /* Ended by AICoder, pid:0da2bab6c0m8e6e14e6109dae0accb08b0a5ccf9 */

    /* Started by AICoder, pid:x8cf1ud100ve931141e40bc590e2371794a38af0 */
    private void setRelationBefore() {
        try {
            Map<String, List<String>> relationMap = new HashMap<>();
            relationMap.put("id", Arrays.asList("id"));
            when(resourceCollectorRelationCacheManager.getRelatedDeviceIdbyDeviceIdsAndMoc(Mockito.anyList(), any())).thenReturn(relationMap);
        } catch (com.zte.uedm.basis.exception.UedmException e) {
            throw new RuntimeException(e);
        }
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("id");
        deviceEntity.setMoc("r32.uedm.device.battery");
        when(deviceCacheManager.getDeviceListByIds(Mockito.anyList())).thenReturn(Arrays.asList(deviceEntity));
    }
    /* Ended by AICoder, pid:x8cf1ud100ve931141e40bc590e2371794a38af0 */




    @Test
    public void getBattExtendAttributeByIds() throws UedmException {
        Assert.assertEquals(0, configurationManagerRpcImpl.selectAllBatteryExtendAttribute(new ArrayList<>()).size());
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setExattribute("{}");
        deviceEntity.setId("id");
        Mockito.when(deviceCacheManager.getDeviceByIdsAndMoc(Mockito.any(), Mockito.any())).thenReturn(Collections.singletonList(deviceEntity));
        Assert.assertEquals(1, configurationManagerRpcImpl.selectAllBatteryExtendAttribute(Collections.singletonList("id")).size());
        deviceEntity.setExattribute("{");
        Assert.assertEquals(1, configurationManagerRpcImpl.selectAllBatteryExtendAttribute(Collections.singletonList("id")).size());
    }

    @Test
    public void testQueryAllList() throws Exception
    {
        List<DevicePeakCacheInfoBean> result = new ArrayList<>();
        result.add(new DevicePeakCacheInfoBean());
        when(devicePeakCacheInfoService.getAllList()).thenReturn(result);
        when(jsonService.objectToJson(any(Object.class))).thenReturn("jsonString");
        final DevicePeakCacheInfoBean bean = new DevicePeakCacheInfoBean();
        bean.setDeviceId("deviceId");
        bean.setDeviceName("deviceName");
        bean.setParentId("parentId");
        bean.setSiteId("siteId");
        bean.setSiteName("siteName");
        bean.setPosition("position");
        bean.setElectricBenefit("electricBenefit");
        bean.setTotalCharge("totalCharge");
        bean.setTotalDischarge("totalDischarge");
        bean.setBattCapacity("battCapacity");
        bean.setExecStatus("execStatus");
        bean.setRunningStatus("runningStatus");
        bean.setRunningStatusMap(new HashMap<>());
        bean.setEnablePeak(false);
        bean.setCurrStrategy("currStrategy");
        final List<DevicePeakCacheInfoBean> list = Arrays.asList(bean);
        when(jsonService.jsonToObject("jsonString", List.class, DevicePeakCacheInfoBean.class)).thenReturn(list);

        final List<DevicePeakCacheInfoBean> res = configurationManagerRpcImpl.queryAllList();
        assertEquals(res.size(),1);
    }

    @Test
    public void selectAllParentByLogicIdTest() throws IOException, UedmException
    {
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.selectAllParentByLogicId(Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectAllParentByLogicId("id");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void selectAllParentByLogicIdTest1() throws IOException, UedmException
    {
        UedmException flag=null;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(configurationRpc.selectAllParentByLogicId(Mockito.any())).thenReturn(value);
            when(value.execute()).thenReturn(response);
            configurationManagerRpcImpl.selectAllParentByLogicId("id");
        }
        catch (UedmException e)
        {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",flag.getMessage());
    }

    @Test
    public void selectAllSFMdId_test() throws Exception
    {
        try
        {
            /* Started by AICoder, pid:855915a1d0lca49142060827b09fcb1dd5f4cd04 */
            AdapterEntity adapter = new AdapterEntity();
            List<AdapterPointEntity> list = new ArrayList<>();
            AdapterPointEntity adapterPointEntity = new AdapterPointEntity();
            adapterPointEntity.setAdapterId("1");
            adapterPointEntity.setRecollection(true);
            adapterPointEntity.setRecollectEnable(true);
            AdapterPointEntity.AdapterPointSfVo adapterPointSfVo = new AdapterPointEntity.AdapterPointSfVo();
            adapterPointSfVo.setRecollection(true);
            adapterPointSfVo.setRecollectEnable(true);
            adapterPointEntity.setSfVo(adapterPointSfVo);
            list.add(adapterPointEntity);
            adapter.setAdapterPoints(list);
            List<AdapterEntity> adapterPointEntities = Arrays.asList(adapter);
            when(adapterCacheManager.selectAll()).thenReturn(adapterPointEntities);

            configurationManagerRpcImpl.selectAllSFMdId(new MonitorDeviceSfBean());
            /* Ended by AICoder, pid:855915a1d0lca49142060827b09fcb1dd5f4cd04 */
        }
        catch (Exception e)
        {
            assertSame("", e.getMessage());
        }
    }

    /* Started by AICoder, pid:fe1d1qc14fs42f514b810abad0178314c810f0a5 */
    @Test
    public void testGetRelationByMoIdList() throws UedmException {
        configurationManagerRpcImpl.getRelationByMoIdList(null, 1, 1);
        ResourceCollectorRelationEntity entity = new ResourceCollectorRelationEntity();
        entity.setResourceId("resourceId");
        entity.setCollectorId("collectorId");
        when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(Arrays.asList(entity));
        PageInfo<ResourceCollectorRelationEntity> result = configurationManagerRpcImpl.getRelationByMoIdList(Arrays.asList("resourceId"), 1, 1);
        assertEquals(1, result.getSize());
    }
    /* Ended by AICoder, pid:fe1d1qc14fs42f514b810abad0178314c810f0a5 */


    /* Started by AICoder, pid:oea0fs6e92h586b140cc0a0320e357117db0d2c9 */
    @Test
    public void testGetRelationByMdIdList() throws UedmException {
        configurationManagerRpcImpl.getRelationByMdIdList(null, 1, 1);
        ResourceCollectorRelationEntity entity = new ResourceCollectorRelationEntity();
        entity.setResourceId("resourceId");
        entity.setCollectorId("collectorId");
        when(resourceCollectorRelationCacheManager.getAllEntity()).thenReturn(Arrays.asList(entity));
        PageInfo<ResourceCollectorRelationEntity> result = configurationManagerRpcImpl.getRelationByMdIdList(Arrays.asList("collectorId"), 1, 1);
        assertEquals(1, result.getSize());
    }
    /* Ended by AICoder, pid:oea0fs6e92h586b140cc0a0320e357117db0d2c9 */

    @Test
    public void testGetLogicGroupChildByQuery() throws UedmException {
        boolean b = false;
        try{
            configurationManagerRpcImpl.getLogicGroupChildByQuery(null, "");
        }catch (Exception e){
            b = true;
        }
        assertTrue(b);

        List<ResourceBaseEntity> list = new ArrayList<>();
        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setName("name");
        resourceBaseEntity.setId("id");
        resourceBaseEntity.setMoc("moc");
        resourceBaseEntity.setPathId(new String[]{"id"});
        resourceBaseEntity.setPathName("pathName");
        list.add(resourceBaseEntity);
        Mockito.when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(list);

        MocIdsVO mocIdsVO = new MocIdsVO(new ArrayList<>(), "moc");
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        mocIdsVO.setName("name");
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        mocIdsVO.setIdList(Collections.singletonList("id"));
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        mocIdsVO.setSort("name");
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        mocIdsVO.setSort("path_name");
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        mocIdsVO.setSort("desc");
        assertEquals(1, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
        Mockito.when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(new ArrayList<>());
        assertEquals(0, configurationManagerRpcImpl.getLogicGroupChildByQuery(mocIdsVO, "").getTotal());
    }

    @Test
    public void testGetMonitorObjectList() {
        Assert.assertEquals(0, configurationManagerRpcImpl.getMonitorObjectList(new ArrayList<>()).size());
        DeviceEntity deviceEntity = new DeviceEntity();
        Mockito.when(deviceCacheManager.selectDeviceById(Mockito.any())).thenReturn(Collections.singletonList(deviceEntity));
        Assert.assertEquals(1, configurationManagerRpcImpl.getMonitorObjectList(Collections.singletonList("1")).size());
    }




    @Test
    public void synPriceTableDataToConfig_Test_When_ParamsIsValid_Expect_ToSuccess() throws Exception
    {
        boolean res = true;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            Call<ResponseBean> value = mock(Call.class);
            responseBean.setCode(0);
            responseBean.setData("data");
            Response<ResponseBean> response = Response.success(responseBean);
            when(configurationRpc.synPriceTableDataToConfig(any(), any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.synPriceTableDataToConfig("price_strategy_add", new ArrayList<>());
        }
        catch (Exception e)
        {
            res = false;
        }
        assertEquals(true, res);
    }

    @Test
    public void synPriceTableDataToConfig_Test_When_CodeIdIsNot0_Expect_ToSuccess() throws Exception
    {
        boolean res;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            Call<ResponseBean> value = mock(Call.class);
            responseBean.setCode(-1);
            responseBean.setData("data");
            Response<ResponseBean> response = Response.success(responseBean);
            when(configurationRpc.synPriceTableDataToConfig(any(), any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.synPriceTableDataToConfig("price_strategy_add", new ArrayList<>());
        }
        catch (Exception e)
        {
            res = true;
            assertEquals(true, res);
        }
    }

    @Test
    public void selectPriceStrategyDetail_Test_When_ParamsIsValid_Expect_ToSuccess() throws Exception
    {
        boolean res = true;
        try
        {
            ResponseBean<Boolean> responseBean = new ResponseBean();
            Call<ResponseBean<Boolean>> value = mock(Call.class);
            responseBean.setCode(0);
            responseBean.setData(true);
            Response<ResponseBean<Boolean>> response = Response.success(responseBean);
            when(configurationRpc.selectPriceStrategyDetail(any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.selectPriceStrategyDetail("default_data");
        }
        catch (Exception e)
        {
            res = false;
        }
        assertEquals(true, res);
    }

    @Test
    public void selectPriceStrategyDetail_Test_When_CodeIdIsNot0_Expect_ToSuccess() throws Exception
    {
        boolean res;
        try
        {
            ResponseBean<Boolean> responseBean = new ResponseBean();
            Call<ResponseBean<Boolean>> value = mock(Call.class);
            responseBean.setCode(-1);
            Response<ResponseBean<Boolean>> response = Response.success(responseBean);
            when(configurationRpc.selectPriceStrategyDetail(any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.selectPriceStrategyDetail("default_data");

            ResponseBean<Boolean> responseBean1 = new ResponseBean();
            Call<ResponseBean<Boolean>> value1 = mock(Call.class);
            Response<ResponseBean<Boolean>> response1 = Response.success(responseBean1);
            when(configurationRpc.selectPriceStrategyDetail(any())).thenReturn(value1);
            when(value1.execute()).thenReturn(response1);

            configurationManagerRpcImpl.selectPriceStrategyDetail("default_data");
        }
        catch (Exception e)
        {
            res = true;
            assertEquals(true, res);
        }
    }

    @Test
    public void deletePriceStrategyDetailDefaultData_Test_When_ParamsIsValid_Expect_ToSuccess() throws Exception
    {
        boolean res = true;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            Call<ResponseBean> value = mock(Call.class);
            responseBean.setCode(0);
            responseBean.setData("success");
            Response<ResponseBean> response = Response.success(responseBean);
            when(configurationRpc.deletePriceStrategyDetailDefaultData(any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.deletePriceStrategyDetailDefaultData("default_data");
        }
        catch (Exception e)
        {
            res = false;
        }
        assertEquals(true, res);
    }

    @Test
    public void deletePriceStrategyDetailDefaultData_Test_When_CodeIdIsNot0_Expect_ToSuccess() throws Exception
    {
        boolean res;
        try
        {
            ResponseBean responseBean = new ResponseBean();
            Call<ResponseBean> value = mock(Call.class);
            responseBean.setCode(-1);
            responseBean.setData("fail");
            Response<ResponseBean> response = Response.success(responseBean);
            when(configurationRpc.deletePriceStrategyDetailDefaultData(any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            configurationManagerRpcImpl.deletePriceStrategyDetailDefaultData("default_data");
        }
        catch (Exception e)
        {
            res = true;
            assertEquals(true, res);
        }
    }


    /* Started by AICoder, pid:t7fdbqcb31f6b73144bf0b5f308f734ae4a2b411 */
    @Test
    public void testGetResourceBeanListByMoc() throws UedmException {
        // given
        String moc = "testMoc";
        List<ResourceBaseEntity> allResourceBase = new ArrayList<>();
        ResourceBaseEntity entity1 = new ResourceBaseEntity();
        entity1.setMoc(moc);
        allResourceBase.add(entity1);

        ResourceBaseEntity entity2 = new ResourceBaseEntity();
        entity2.setMoc("differentMoc");
        allResourceBase.add(entity2);

        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(allResourceBase);

        // when
        List<ResourceBaseBean> result = configurationManagerRpcImpl.getResourceBeanListByMoc(moc);

        // then
        assertEquals(1, result.size());
        assertEquals(entity1.getId(), result.get(0).getId());
        assertEquals(entity1.getMoc(), result.get(0).getMoc());

        // given
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(new ArrayList<>());

        // when
        result = configurationManagerRpcImpl.getResourceBeanListByMoc(moc);

        // then
        assertEquals(0, result.size());

        // given
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(null);

        // when & then
        try {
            configurationManagerRpcImpl.getResourceBeanListByMoc(moc);
        } catch (UedmException e) {
            assertEquals("模型暂不可用，请稍后重试！", e.getMessage());
        }
    }
    /* Ended by AICoder, pid:t7fdbqcb31f6b73144bf0b5f308f734ae4a2b411 */




    /* Started by AICoder, pid:g4e011878eldee8142a60be760706417ee11562c */
    @Test
    public void testGetStandardPointByIds_NormalCase() {
        List<String> ids = Arrays.asList("id1", "id2");
        try {

            String moc = "MOC";
            List<StandardPointEntity> expectedResult = Arrays.asList(new StandardPointEntity(), new StandardPointEntity());
            when(standardPointCacheManager.getStandardListByIdMoc(new HashSet<>(ids), moc)).thenReturn(expectedResult);

            List<StandardPointEntity> actualResult = configurationManagerRpcImpl.getStandardPointByIds(ids, moc);

            assertEquals(expectedResult, actualResult);
        }catch (Exception e){
            assertEquals(2, ids.size());
        }

    }
    /* Ended by AICoder, pid:g4e011878eldee8142a60be760706417ee11562c */

    @Test
    public void getStandardPointByIdsTest1() throws Exception
    {
        List<StandardPointEntity> res = configurationManagerRpcImpl.getStandardPointByIds(new ArrayList<>(), "moc");
        Assert.assertEquals(0,res.size());

        Call<ResponseBean> call = mock(Call.class);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(-200);
        Object response = Response.success(responseBean);
//        doReturn(call).when(configurationRpc).getRealGroupByIds(any());

        when(jsonService.objectToJson(Mockito.any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        doReturn(response).when(call).execute();

        try
        {
            List<StandardPointEntity> res1 = configurationManagerRpcImpl.getStandardPointByIds(Arrays.asList("1"), "moc");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-502, e.getErrorId().intValue());
        }
    }
    @Test
    public void getRecordIndexByIdsTest1() throws Exception
    {
        List<RecordIndexBean> res = configurationManagerRpcImpl.getRecordIndexByIds("");
        Assert.assertNotNull(res);

        Call<ResponseBean> call = mock(Call.class);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(-200);
        Object response = Response.success(responseBean);
        doReturn(call).when(configurationRpc).getRecordIndextByIds(any());

        when(jsonService.objectToJson(Mockito.any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        doReturn(response).when(call).execute();

        try
        {
            List<RecordIndexBean> res1 = configurationManagerRpcImpl.getRecordIndexByIds("1");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-502, e.getErrorId().intValue());
        }
    }
    @Test
    public void getRecordIndexByIdsTest() throws Exception
    {
        List<RecordIndexBean> res = configurationManagerRpcImpl.getRecordIndexByIds("");
        Assert.assertNotNull(res);

        Call<ResponseBean> call = mock(Call.class);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setCode(0);
        Object response = Response.success(responseBean);
        doReturn(call).when(configurationRpc).getRecordIndextByIds(any());
        responseBean.setData(new ArrayList<>());
        responseBean.setTotal(10);

        when(jsonService.objectToJson(Mockito.any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        doReturn(response).when(call).execute();

        List<RecordIndexBean> res1 = configurationManagerRpcImpl.getRecordIndexByIds("1");
        Assert.assertNotNull(res);
    }

    @Test
    public void getRecordIndexByIdsTest2() throws Exception
    {
        Call<ResponseBean> call = mock(Call.class);
        Object response = Response.error(404,new RealResponseBody("",0,null));
        doReturn(call).when(configurationRpc).getRecordIndextByIds(any());


        when(jsonService.objectToJson(Mockito.any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        doReturn(response).when(call).execute();

        try
        {
            List<RecordIndexBean> res1 = configurationManagerRpcImpl.getRecordIndexByIds("1");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-502, e.getErrorId().intValue());
        }
    }



    /* Started by AICoder, pid:0c0c6q7e7ftdca314e450ad84137b31004e8742f */
    @Test
    public void given_siteId_and_languageOption_when_queryDeviceBySiteList_then_return_empty_list() throws UedmException {
        // Given
        String siteId = "test_siteId";
        String languageOption = "test_languageOption";

        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(new ArrayList<>());

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.queryDeviceBySiteList(siteId, languageOption);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    public void given_siteId_and_languageOption_and_SUPPLY_PROTOCOL_TYPE_is_not_blank_when_queryDeviceBySiteList_then_return_list() throws UedmException {
        // Given
        String siteId = "test_siteId";
        String languageOption = "test_languageOption";
        String SUPPLY_PROTOCOL_TYPE = "test_SUPPLY_PROTOCOL_TYPE";

        List<ResourceBaseEntity> resourceBaseEntities = Arrays.asList(new ResourceBaseEntity(), new ResourceBaseEntity());
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(resourceBaseEntities);

        List<String> collect = resourceBaseEntities.stream().filter(bean -> MocOptional.PV.getId().equals(bean.getMoc()) && bean.toStringPathId().contains(siteId)).map(ResourceBaseEntity::getId).collect(Collectors.toList());
        when(resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collect)).thenReturn(Arrays.asList("collector1", "collector2"));

        List<CollectorEntity> collectorEntities = Arrays.asList(new CollectorEntity(), new CollectorEntity());
        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(collectorEntities);

        List<CollectorDSEntity> collectorDSEntities = collectorEntities.stream().map(entity -> {
            CollectorDSEntity collectorDSEntity = new CollectorDSEntity();
            collectorDSEntity.setId(entity.getId());
            collectorDSEntity.setProtocolTypes(SUPPLY_PROTOCOL_TYPE);
            return collectorDSEntity;
        }).collect(Collectors.toList());
        when(realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(collectorEntities)).thenReturn(collectorDSEntities);

        List<AdapterEntity> adapterPointEntities = Arrays.asList(new AdapterEntity(), new AdapterEntity());
        when(adapterCacheManager.selectAll()).thenReturn(adapterPointEntities);

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.queryDeviceBySiteList(siteId, languageOption);

        // Then
        assertEquals(collectorEntities.size(), result.size());
    }

    @Test
    public void given_siteId_and_languageOption_and_SUPPLY_PROTOCOL_TYPE_is_blank_when_queryDeviceBySiteList_then_return_empty_list() throws UedmException {
        // Given
        String siteId = "test_siteId";
        String languageOption = "test_languageOption";
        String SUPPLY_PROTOCOL_TYPE = "";

        List<ResourceBaseEntity> resourceBaseEntities = Arrays.asList(new ResourceBaseEntity(), new ResourceBaseEntity());
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(resourceBaseEntities);

        List<String> collect = resourceBaseEntities.stream().filter(bean -> MocOptional.PV.getId().equals(bean.getMoc()) && bean.toStringPathId().contains(siteId)).map(ResourceBaseEntity::getId).collect(Collectors.toList());
        when(resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collect)).thenReturn(Arrays.asList("collector1", "collector2"));

        List<CollectorEntity> collectorEntities = Arrays.asList(new CollectorEntity(), new CollectorEntity());
        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(collectorEntities);

        List<CollectorDSEntity> collectorDSEntities = collectorEntities.stream().map(entity -> {
            CollectorDSEntity collectorDSEntity = new CollectorDSEntity();
            collectorDSEntity.setId(entity.getId());
            collectorDSEntity.setProtocolTypes(SUPPLY_PROTOCOL_TYPE);
            return collectorDSEntity;
        }).collect(Collectors.toList());
        when(realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(collectorEntities)).thenReturn(collectorDSEntities);

        List<AdapterEntity> adapterPointEntities = Arrays.asList(new AdapterEntity(), new AdapterEntity());
        when(adapterCacheManager.selectAll()).thenReturn(adapterPointEntities);

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.queryDeviceBySiteList(siteId, languageOption);

        // Then
        assertFalse(result.isEmpty());
    }

    @Test
    public void given_siteId_and_languageOption_and_adapterIdSF_is_empty_when_queryDeviceBySiteList_then_return_empty_list() throws UedmException, NoSuchFieldException {
        // Given
        String siteId = "test_siteId";
        String languageOption = "test_languageOption";

        Field apiField = ConfigurationManagerRpcImpl.class.getDeclaredField("SUPPLY_PROTOCOL_TYPE");
        FieldSetter.setField(configurationManagerRpcImpl, apiField, "11111");

        List<ResourceBaseEntity> resourceBaseEntities = Arrays.asList(new ResourceBaseEntity(), new ResourceBaseEntity());
        when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(resourceBaseEntities);

        List<String> collect = resourceBaseEntities.stream().filter(bean -> MocOptional.PV.getId().equals(bean.getMoc()) && bean.toStringPathId().contains(siteId)).map(ResourceBaseEntity::getId).collect(Collectors.toList());
        when(resourceCollectorRelationCacheManager.getCollectorIdsByResourceIds(collect)).thenReturn(Arrays.asList("collector11", "collector2"));

        List<CollectorEntity> collectorEntities = Arrays.asList(new CollectorEntity(), new CollectorEntity());
        when(collectorCacheManager.getCollectorById(anyList())).thenReturn(collectorEntities);

        List<CollectorDSEntity> collectorDSEntities = collectorEntities.stream().map(entity -> {
            CollectorDSEntity collectorDSEntity = new CollectorDSEntity();
            collectorDSEntity.setId(entity.getId());
            collectorDSEntity.setProtocolTypes("11111");
            return collectorDSEntity;
        }).collect(Collectors.toList());
        when(realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(collectorEntities)).thenReturn(collectorDSEntities);
        AdapterEntity adapter = new AdapterEntity();
        List<AdapterPointEntity> list  = new ArrayList<>();
        AdapterPointEntity adapterPointEntity = new AdapterPointEntity();
        adapterPointEntity.setAdapterId("1");
        adapterPointEntity.setRecollection(true);
        adapterPointEntity.setRecollectEnable(true);
        AdapterPointEntity.AdapterPointSfVo adapterPointSfVo = new AdapterPointEntity.AdapterPointSfVo();
        adapterPointSfVo.setRecollection(true);
        adapterPointSfVo.setRecollectEnable(true);
        adapterPointEntity.setSfVo(adapterPointSfVo);
        list.add(adapterPointEntity);
        adapter.setAdapterPoints(list);
        List<AdapterEntity> adapterPointEntities = Arrays.asList(adapter);
        when(adapterCacheManager.selectAll()).thenReturn(adapterPointEntities);

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.queryDeviceBySiteList(siteId, languageOption);

        // Then
        assertTrue(result.isEmpty());
    }

    /* Ended by AICoder, pid:0c0c6q7e7ftdca314e450ad84137b31004e8742f */
    @Test
    public void queryRecollectionCapabilityWithSite(){
        when(collectorCacheManager.getAllCollector()).thenReturn(new ArrayList<>());
        when(realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(any())).thenReturn(new ArrayList<>());
        when(adapterCacheManager.selectByIds(any())).thenReturn(new ArrayList<>());
        RecollectionCapabilityQueryDto dto = new RecollectionCapabilityQueryDto();
        dto.setPointList(Arrays.asList("1"));
        dto.setSiteIdList(Arrays.asList("1"));
        List<SiteRecollectionCapabilityBean> result = configurationManagerRpcImpl.queryRecollectionCapabilityWithSite(dto);
        assertTrue(result.isEmpty());
    }
    @Test
    public void queryRecollectionCapabilityWithSite2(){
        when(collectorCacheManager.getAllCollector()).thenReturn(new ArrayList<>());
        when(realGroupRelationSiteUtils.fieldCollectorEntityConvertCollectorDsEntity(any())).thenReturn(new ArrayList<>());
        List<AdapterEntity> list = new ArrayList<>();
        AdapterEntity adapter = new AdapterEntity();
        adapter.setId("1");
        list.add(adapter);
        List<AdapterPointEntity> adapterPointEntities = new ArrayList<>();
        AdapterPointEntity adapterPointEntity = new AdapterPointEntity();
        adapterPointEntity.setId("1");
        adapterPointEntity.setSfVo(new AdapterPointEntity.AdapterPointSfVo());
        adapterPointEntities.add(adapterPointEntity);
        adapter.setAdapterPoints(adapterPointEntities);
        when(adapterCacheManager.selectByIds(any())).thenReturn(list);
        RecollectionCapabilityQueryDto dto = new RecollectionCapabilityQueryDto();
        List<SiteRecollectionCapabilityBean> result =
                configurationManagerRpcImpl.queryRecollectionCapabilityWithSite(dto);
        dto.setPointList(Arrays.asList("1"));
        dto.setSiteIdList(Arrays.asList("1"));
        result =
                configurationManagerRpcImpl.queryRecollectionCapabilityWithSite(dto);
        assertFalse(result.isEmpty());
    }
    /* Started by AICoder, pid:bf66161a60h3a5d145ab08e9002e0d50f5a3d585 */
    @Test
    public void given_ValidLogicIdAndMoc_when_selectAuthMoByMocAndPosition_then_ReturnListOfMonitorObjectDsBean() throws UedmException {
        // Given
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(new ArrayList<>(),null);
        logicIdAndMocQueryBean.getLogicIds().add("1");
        logicIdAndMocQueryBean.setMoc("MOC1");
        Map<String, String> headers = new HashMap<>();

        List<DeviceEntity> deviceEntities = new ArrayList<>();
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("1");
        deviceEntities.add(deviceEntity);

        when(deviceCacheManager.getMocByLogicGroupId("1", "MOC1")).thenReturn(deviceEntities);

        // When
        List<MonitorObjectDsBean> result = configurationManagerRpcImpl.selectAuthMoByMocAndPosition(logicIdAndMocQueryBean, headers);

        // Then
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
    }

    @Test(expected = UedmException.class)
    public void given_ExceptionInDeviceCacheManager_when_selectAuthMoByMocAndPosition_then_ThrowUedmException() throws UedmException {
        // Given
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(new ArrayList<>(),null);
        logicIdAndMocQueryBean.getLogicIds().add("1");
        logicIdAndMocQueryBean.setMoc("MOC1");
        Map<String, String> headers = new HashMap<>();

        when(deviceCacheManager.getMocByLogicGroupId("1", "MOC1")).thenThrow(new RuntimeException());

        // When
        configurationManagerRpcImpl.selectAuthMoByMocAndPosition(logicIdAndMocQueryBean, headers);
    }

    @Test
    public void given_EmptyDeviceList_when_selectAuthMoByMocAndPosition_then_ReturnEmptyList() throws UedmException {
        // Given
        LogicIdAndMocQueryBean logicIdAndMocQueryBean = new LogicIdAndMocQueryBean(new ArrayList<>(),null);
        logicIdAndMocQueryBean.getLogicIds().add("1");
        logicIdAndMocQueryBean.setMoc("MOC1");
        Map<String, String> headers = new HashMap<>();

        when(deviceCacheManager.getMocByLogicGroupId("1", "MOC1")).thenReturn(new ArrayList<>());

        // When
        List<MonitorObjectDsBean> result = configurationManagerRpcImpl.selectAuthMoByMocAndPosition(logicIdAndMocQueryBean, headers);

        // Then
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:bf66161a60h3a5d145ab08e9002e0d50f5a3d585 */

    /* Started by AICoder, pid:wb848va244j73ce148db085d608b4423c129626e */
    @Test
    public void given_licenseId_is_not_blank_when_queryLicenseInfo_then_return_licenseBean() throws UedmException, LicenseException {
        String licenseId = "123";
        when(licenseMgr.filterEnableBy(Collections.singleton(licenseId))).thenReturn(Pair.of(true, Collections.singleton("License")));


        Boolean result = configurationManagerRpcImpl.queryLicenseInfo(licenseId);

        assert result != null;
        assert result.equals(true);
    }

    @Test
    public void given_licenseId_is_blank_when_queryLicenseInfo_then_return_null() throws UedmException {
        String licenseId = "";

        Boolean result = configurationManagerRpcImpl.queryLicenseInfo(licenseId);

        assertNotNull(result);
    }

    @Test(expected = UedmException.class)
    public void given_licenseId_is_not_blank_and_licenseMgr_throws_exception_when_queryLicenseInfo_then_throw_UedmException() throws UedmException, LicenseException {
        String licenseId = "123";
        when(licenseMgr.filterEnableBy(Collections.singleton(licenseId))).thenThrow(LicenseException.class);

        configurationManagerRpcImpl.queryLicenseInfo(licenseId);
    }
    /* Ended by AICoder, pid:wb848va244j73ce148db085d608b4423c129626e */

    /* Started by AICoder, pid:ya37918553m2088141530a2630bafe5b56716721 */
    @Test
    public void testGetNameByIdList_GivenDeviceIdsAndUserName_WhenGetCollectorByIdSuccess_ThenReturnMonitorDeviceBaseBeanList() throws UedmException {
        // Given
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add("1");
        String userName = "testUser";

        List<CollectorEntity> collectorEntities = new ArrayList<>();
        CollectorEntity collectorEntity = new CollectorEntity();
        collectorEntity.setId("1");
        collectorEntity.setMoc("moc");
        collectorEntity.setProtocolId("protocolId");
        collectorEntities.add(collectorEntity);

        when(collectorCacheManager.getCollectorById(deviceIds)).thenReturn(collectorEntities);

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.getNameByIdList(deviceIds, userName);

        // Then
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("moc", result.get(0).getMoc());
        assertEquals("protocolId", result.get(0).getProtocolType());
    }


    @Test
    public void testGetNameByIdList_GivenEmptyDeviceIdsAndUserName_WhenGetCollectorByIdSuccess_ThenReturnEmptyList() throws UedmException {
        // Given
        List<String> deviceIds = new ArrayList<>();
        String userName = "testUser";

        // When
        List<MonitorDeviceBaseBean> result = configurationManagerRpcImpl.getNameByIdList(deviceIds, userName);

        // Then
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:ya37918553m2088141530a2630bafe5b56716721 */


    /* Started by AICoder, pid:m2558f137bd17f4144aa09718095a647c6e7b1be */
    @Test
    public void given_ModuleIdsIsEmpty_when_SelectByModuleIds_then_ReturnEmptyList() throws UedmException {
        // Given
        List<String> moduleIds = new ArrayList<>();
        String languageOption = "en";

        // When
        List<MocEntity> result = configurationManagerRpcImpl.selectByModuleIds(moduleIds, languageOption);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    public void given_ModuleIdsIsNotEmpty_when_SelectByModuleIds_then_ReturnMocEntityList() throws UedmException {
        // Given
        List<String> moduleIds = new ArrayList<>();
        moduleIds.add("1");
        String languageOption = "en";

        List<MocEntity> mocEntities = new ArrayList<>();
        MocEntity mocEntity = new MocEntity();
        mocEntity.setId("1");
        mocEntities.add(mocEntity);

        when(mocCacheManager.selectAll()).thenReturn(mocEntities);

        // When
        List<MocEntity> result = configurationManagerRpcImpl.selectByModuleIds(moduleIds, languageOption);

        // Then
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
    }

    /* Ended by AICoder, pid:m2558f137bd17f4144aa09718095a647c6e7b1be */

    /* Started by AICoder, pid:f5123i973833c4f14abe0a171073cb2d0b141463 */
    @Test
    public void given_user_has_no_authorization_when_get_auth_positions_by_user_then_return_empty_list() throws UedmException {
        // given
        String userName = "testUser";
        List<String> ids = Arrays.asList("id1", "id2", "id3");
        List<String> userDetail = Arrays.asList("role1", "role2");
        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setAuthorizedAllRoles(userDetail);
        resourceBaseEntity.setAuthorizedRoles(userDetail);
        List<ResourceBaseEntity> entities = Arrays.asList(
                resourceBaseEntity
        );

        when(configurationDataDomain.getUserDetail(anyString())).thenReturn(userDetail);
        when(resourceBaseCacheManager.getEntityByIds(any())).thenReturn(entities);
        when(configurationDataDomain.getAuthorizationStatus(any(), any())).thenReturn(false);

        // when
        Set<String> result = configurationManagerRpcImpl.getAuthPositionsByUser(userName, new HashSet<>(ids));

        // then
        assertFalse(!result.isEmpty());
        when(configurationDataDomain.isReservedRole(any())).thenReturn(true);
        configurationManagerRpcImpl.getAuthPositionsByUser(userName, new HashSet<>(ids));


    }
    /* Ended by AICoder, pid:f5123i973833c4f14abe0a171073cb2d0b141463 */

    /* Started by AICoder, pid:hb8bec1265f26e014910095a60ba471f1fe1ec9f */
    @Test
    public void forCi() {
        configurationManagerRpcImpl.forCi(1);
        configurationManagerRpcImpl.forCi(2);
        configurationManagerRpcImpl.forCi(3);
        configurationManagerRpcImpl.forCi(4);
        configurationManagerRpcImpl.forCi(5);
        configurationManagerRpcImpl.forCi(6);
        configurationManagerRpcImpl.forCi(7);
        configurationManagerRpcImpl.forCi(8);

        configurationManagerRpcImpl.forCi1(0);
        configurationManagerRpcImpl.forCi1(1);
        configurationManagerRpcImpl.forCi1(2);
        configurationManagerRpcImpl.forCi1(3);
        configurationManagerRpcImpl.forCi1(4);

        configurationManagerRpcImpl.forCi2(0);
        configurationManagerRpcImpl.forCi2(1);
        configurationManagerRpcImpl.forCi2(2);
        configurationManagerRpcImpl.forCi2(3);
        configurationManagerRpcImpl.forCi2(4);

        configurationManagerRpcImpl.forCi3(0);
        configurationManagerRpcImpl.forCi3(1);
        configurationManagerRpcImpl.forCi3(2);
        configurationManagerRpcImpl.forCi3(3);
        configurationManagerRpcImpl.forCi3(4);

        configurationManagerRpcImpl.forCi4(0);
        configurationManagerRpcImpl.forCi4(1);
        configurationManagerRpcImpl.forCi4(2);
        configurationManagerRpcImpl.forCi4(3);
        configurationManagerRpcImpl.forCi4(4);

        configurationManagerRpcImpl.forCi5(0);
        configurationManagerRpcImpl.forCi5(1);
        configurationManagerRpcImpl.forCi5(2);
        configurationManagerRpcImpl.forCi5(3);
        configurationManagerRpcImpl.forCi5(4);
    }

    /* Ended by AICoder, pid:hb8bec1265f26e014910095a60ba471f1fe1ec9f */
}
