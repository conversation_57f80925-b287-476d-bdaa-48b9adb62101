package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Test;

import static org.junit.Assert.*;

/* Started by AICoder, pid:72a2db0d735f4a69be27c9c6136110af */
import org.junit.Test;
import static org.junit.Assert.*;

public class IntervalStrategyDetailEntityTest {
    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(IntervalStrategyDetailEntity.class);
    }

    public void testBin() {
        byte[] bytes = new byte[1640];

        // 日模版数量:8
        bytes[0] = 0x08;
        // 电价模式:日模式
        bytes[1] = 0x00;
        // 节假日模版序号
        bytes[2] = 0x08;
        // 节假日配置：未配置节假日
        for (int i = 3; i <= 62; i++) {
            bytes[i] = 0x00;
        }
        // 模版关系配置：日模式模版序号
        bytes[63] = 0x01;
        for (int i = 64; i <= 93; i++) {
            bytes[i] = 0x00;
        }
        // 日模版
        // 日模版1的时间段数量:5
        bytes[94] = 0x05;
        // 日模版1的时段1：起始小时：0b00000,起始分钟：0b000000，结束小时：0b00101，结束分钟：0b111011，峰平谷：谷0b0000000001
        bytes[95]=0b00000000;
        bytes[96]=0b00000101;
        bytes[97]= (byte) 0b11101100;

    }

    @Test
    public void test2() {
        byte number = (byte) 0b11101100;
        System.out.println("======================output:" + number);
    }


}
/* Ended by AICoder, pid:72a2db0d735f4a69be27c9c6136110af */