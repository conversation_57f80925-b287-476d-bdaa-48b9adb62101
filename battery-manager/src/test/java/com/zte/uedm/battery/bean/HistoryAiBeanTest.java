package com.zte.uedm.battery.bean;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.junit.Test;

/**
 * gis地图中图标点击展示原子类
 * <AUTHOR>
 */
public class HistoryAiBeanTest {

    @Test
    public void testSetGetToString()
    {
    	HistoryAiBean bean = new HistoryAiBean();
    	bean.setAvgValue("");
    	bean.setAvgValueTime("");
    	bean.setCurValue("");
    	bean.setCurValueTime("");
    	bean.setMaxValue("");
    	bean.setMaxValueTime("");
    	bean.setMinValue("");
    	bean.setMinValueTime("");
    	bean.setTimeRange("");
    	bean.setTimeRange("");
		bean.toString();
		assertEquals("",bean.getAvgValue());
    }
}
