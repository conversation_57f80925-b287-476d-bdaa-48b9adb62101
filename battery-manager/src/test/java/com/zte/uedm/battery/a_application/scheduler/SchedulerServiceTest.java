package com.zte.uedm.battery.a_application.scheduler;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.quartz.Job;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
/* Started by AICoder, pid:of989sc0203b3921430e0ad10072b682ba476f4d */
public class SchedulerServiceTest {
    @InjectMocks
    private SchedulerService schedulerService;
    @Mock
    private Scheduler scheduler;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void startJobNormal() throws SchedulerException
    {
        Job job = mock(Job.class);
        doReturn(new Date()).when(scheduler).scheduleJob(any(), any());
        boolean exception = true;
        try
        {
            schedulerService.startJob("oil", job.getClass(),"0 0/5 * * * ?");
        }
        catch (SchedulerException exp)
        {
            exception = false;
        }
        //断言没有异常产生
        Assert.assertTrue(exception);
    }

    @Test
    public void startJobNameErr() throws SchedulerException
    {
        Job job = mock(Job.class);
        doReturn(new Date()).when(scheduler).scheduleJob(any(), any());

        boolean exception = true;
        try
        {
            schedulerService.startJob("battery_manager", job.getClass(), "0 0/5 * * * ?");
        }
        catch (SchedulerException exp)
        {
            exception = false;
        }
        //断言没有异常产生
        Assert.assertTrue(exception);
    }
    @Test
    public void deleteJob() throws SchedulerException
    {
        doReturn(true).when(scheduler).deleteJob(any());

        boolean exception = true;
        try
        {
            schedulerService.deleteJob("jobName");
        }
        catch (SchedulerException exp)
        {
            exception = false;
        }
        //断言没有异常产生
        Assert.assertTrue(exception);
    }
    @Test
    public void restartJob() throws SchedulerException
    {
        doReturn(true).when(scheduler).deleteJob(any());
        when(scheduler.checkExists((JobKey) any())).thenReturn(true);

        boolean exception = true;
        try
        {
            schedulerService.restartJob("jobName","0 0/5 * * * ?");
            schedulerService.restartJob("AutoPeakShiftStrategy","0 0/5 * * * ?");
        }
        catch (SchedulerException exp)
        {
            exception = false;
        }
        //断言没有异常产生
        Assert.assertTrue(exception);
    }



}



/* Ended by AICoder, pid:of989sc0203b3921430e0ad10072b682ba476f4d */