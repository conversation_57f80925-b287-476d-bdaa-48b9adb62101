package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.bean.BattRiskEvalVO;
import com.zte.uedm.battery.opti.domain.service.BattRiskEvalDomain;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskEvalMapper;
import com.zte.uedm.battery.opti.application.executor.impl.BattRiskEvalServiceImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
@RunWith(PowerMockRunner.class)
@PrepareForTest({ DateUtils.class})
public class BattRiskEvalServiceImplTest
{
    @InjectMocks
    private BattRiskEvalServiceImpl battRiskEvalService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BattRiskEvalDomain battRiskEvalDomain;


    @Mock
    private BattRiskEvalMapper riskEvalMapper;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        doReturn("123").when(i18nUtils).getMapFieldByLanguageOption(any(),any());
    }



    @Test
    public void batteryDetailRiskSelect() throws UedmException {
        PowerMockito.mockStatic(DateUtils.class);
        Mockito.doReturn(new ArrayList<BattRiskEvalVO>()).when(riskEvalMapper).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
        ResponseBean responseBean = battRiskEvalService.batteryDetailRiskSelect("1", "Ee", null);
        Mockito.doThrow(new UedmException(-200,"1")).when(riskEvalMapper).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
        ResponseBean responseBean1 = battRiskEvalService.batteryDetailRiskSelect("1", "Ee", null);
        ArrayList<BattRiskEvalVO> battRiskEvalVOS = new ArrayList<>();
        BattRiskEvalVO battRiskEvalVO = new BattRiskEvalVO();
        battRiskEvalVO.setBattId("1");
        battRiskEvalVO.setName("南孚5号电池");
        battRiskEvalVO.setRiskName("{zh_CN:\"健康\",en_US:\"healthy\"}");
        battRiskEvalVO.setRiskLevel("level_1");
        battRiskEvalVO.setRiskCause("{zh_CN:\"打雷\",en_US:\"pong!pong!\"}");
        battRiskEvalVO.setRiskSuggestion("{zh_CN:\"阿萨德呼呼\",en_US:\"nono!\"}");
        battRiskEvalVO.setEvalTime("1699232047400");
        battRiskEvalVOS.add(battRiskEvalVO);
        Mockito.doReturn(battRiskEvalVOS).when(riskEvalMapper).batteryDetailRiskSelectByid(Mockito.anyString(),Mockito.anyString());
        ResponseBean responseBean2 = battRiskEvalService.batteryDetailRiskSelect("1", "en_US", null);
        //1698368969034
    }
}
