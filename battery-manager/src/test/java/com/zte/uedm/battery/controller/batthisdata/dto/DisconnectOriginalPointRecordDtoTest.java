package com.zte.uedm.battery.controller.batthisdata.dto;

import com.zte.uedm.common.configuration.point.bean.OriginalDataBean;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

public class DisconnectOriginalPointRecordDtoTest
{

    @Test
    public void test()
    {
        DisconnectOriginalPointRecordDto disconnectOriginalPointRecordDto = new DisconnectOriginalPointRecordDto();
        disconnectOriginalPointRecordDto.setMdId("77");
        OriginalDataBean originalDataBean = new OriginalDataBean();
        originalDataBean.setUpdateTime(7L);
        originalDataBean.setIndex("7");
        originalDataBean.setOmpId("805800283801");
        originalDataBean.setValue("77");
        disconnectOriginalPointRecordDto.setOriginalDatas(Arrays.asList(originalDataBean));
        disconnectOriginalPointRecordDto.toString();

        Assert.assertTrue(disconnectOriginalPointRecordDto.checkNotEmpty().getLeft());
        Assert.assertTrue(disconnectOriginalPointRecordDto.checkInRange().getLeft());
        Assert.assertEquals("77",disconnectOriginalPointRecordDto.getMdId());

        DisconnectOriginalPointRecordDto empty = new DisconnectOriginalPointRecordDto();
        Assert.assertFalse(empty.checkNotEmpty().getLeft());

        DisconnectOriginalPointRecordDto outRange = new DisconnectOriginalPointRecordDto();
        disconnectOriginalPointRecordDto.setMdId("77");
        OriginalDataBean outRangeBean = new OriginalDataBean();
        outRangeBean.setUpdateTime(7L);
        outRangeBean.setIndex("7");
        outRangeBean.setOmpId("77");
        outRangeBean.setValue("77");
        outRange.setOriginalDatas(Arrays.asList(outRangeBean));
        Assert.assertFalse(outRange.checkInRange().getLeft());
    }

}
