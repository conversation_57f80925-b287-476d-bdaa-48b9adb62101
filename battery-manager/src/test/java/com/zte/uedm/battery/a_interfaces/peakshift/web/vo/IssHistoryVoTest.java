package com.zte.uedm.battery.a_interfaces.peakshift.web.vo;

import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.IssHistoryEntity;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.po.IssuedHistoryPo;
import com.zte.uedm.battery.bean.PojoTestUtil;
/* Started by AICoder, pid:a1717e43f525423ea4f11a75c6bd79eb */
import org.junit.Test;
import static org.junit.Assert.*;

public class IssHistoryVoTest {
    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(IssHistoryVo.class);
        PojoTestUtil.TestForPojo(IssuedHistoryPo.class);
        PojoTestUtil.TestForPojo(IssHistoryEntity.class);
    }
}
/* Ended by AICoder, pid:a1717e43f525423ea4f11a75c6bd79eb */