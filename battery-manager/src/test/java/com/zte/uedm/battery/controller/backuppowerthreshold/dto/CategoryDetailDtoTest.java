package com.zte.uedm.battery.controller.backuppowerthreshold.dto;

import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.common.enums.SortEnum;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Test;

import java.util.Set;

public class CategoryDetailDtoTest
{
    @Test
    public void testSuccessfully() throws Exception
    {
        PojoTestUtil.TestForPojo(CategoryDetailDto.class);
        CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
        categoryDetailDto.setLogicGroupId("7");
        categoryDetailDto.setPageSize(10);
        categoryDetailDto.setPageNo(1);
        categoryDetailDto.setPageSize(5);
        categoryDetailDto.getLogicGroupId();
        categoryDetailDto.toString();
        Assert.assertEquals("7",categoryDetailDto.getLogicGroupId());
    }

    @Test
    public void testOrderOutOfRange() throws Exception
    {
        UedmException uedmException =null;
        try {
            PojoTestUtil.TestForPojo(CategoryDetailDto.class);
            CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
            categoryDetailDto.setOrder("77");

            categoryDetailDto.getOrder();

            categoryDetailDto.toString();
            categoryDetailDto.checkOrderAndSortAvailable();
        }catch (UedmException e)
        {
            uedmException = new UedmException(-304,"77");
        }
        Assert.assertEquals("77",String.valueOf(uedmException.getMessage()));
    }

    @Test
    public void testOrderSuccessfully() throws Exception
    {
            PojoTestUtil.TestForPojo(CategoryDetailDto.class);
            CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
            categoryDetailDto.setOrder("siteLevel");
            String order = categoryDetailDto.getOrder();
            String orderValue = CategoryDetailDto.BackupPowerThresholdOrderEnum.getOrderValue(order);
            Assert.assertEquals("site_level",orderValue);
    }

    @Test
    public void testSortOutOfRange() throws Exception
    {
        UedmException uedmException =null;
        try {
            PojoTestUtil.TestForPojo(CategoryDetailDto.class);
            CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
            categoryDetailDto.setSort("77");

            categoryDetailDto.getSort();

            categoryDetailDto.toString();
            categoryDetailDto.checkOrderAndSortAvailable();
        }catch (UedmException e)
        {
            uedmException = new UedmException(-304,"77");
        }
        Assert.assertEquals("77",String.valueOf(uedmException.getMessage()));
    }

}
