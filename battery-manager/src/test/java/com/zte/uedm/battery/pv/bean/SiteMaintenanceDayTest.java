package com.zte.uedm.battery.pv.bean;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

public class SiteMaintenanceDayTest {

    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(SiteMaintenanceDay.class);
    }

    @Test
    public void setFromValue() {
        SiteMaintenanceDay siteMaintenanceDay = new SiteMaintenanceDay();
        siteMaintenanceDay.setEfficiencyFromValue(BigDecimal.valueOf(12));
        Assert.assertEquals(siteMaintenanceDay.getEfficiency(), 12f, 0);
        siteMaintenanceDay.setEfficiencyFromValue(null);
        Assert.assertNull(siteMaintenanceDay.getEfficiency());

        siteMaintenanceDay.setRatioSupplyFormValue(null);
        Assert.assertNull(siteMaintenanceDay.getRatioSupply());
        siteMaintenanceDay.setRatioSupplyFormValue(BigDecimal.valueOf(12));
        Assert.assertEquals(siteMaintenanceDay.getRatioSupply(), 12f, 0);

        siteMaintenanceDay.setGenerationFromValue(null);
        Assert.assertNull(siteMaintenanceDay.getGeneration());
        siteMaintenanceDay.setGenerationFromValue(BigDecimal.valueOf(12));
        Assert.assertEquals(siteMaintenanceDay.getGeneration(), 12f, 0);

        siteMaintenanceDay.setLoadFormValue(null);
        Assert.assertNull(siteMaintenanceDay.getLoad());
        siteMaintenanceDay.setLoadFormValue(BigDecimal.valueOf(12));
        Assert.assertEquals(siteMaintenanceDay.getLoad(), 12f, 0);
    }
}