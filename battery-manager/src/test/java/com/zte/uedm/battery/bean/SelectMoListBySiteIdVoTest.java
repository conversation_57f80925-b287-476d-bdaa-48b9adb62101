package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryConfigAddDto;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

public class SelectMoListBySiteIdVoTest
{

    @Test
    public void Test() throws Exception
    {
        PojoTestUtil.TestForPojo(SelectMoListBySiteIdVo.class);
        SelectMoListBySiteIdVo selectMoListBySiteIdVo = new SelectMoListBySiteIdVo();
        selectMoListBySiteIdVo.setMoList(Arrays.asList("1"));
        selectMoListBySiteIdVo.setSiteId("1");
        String str = selectMoListBySiteIdVo.toString();
        Assert.assertEquals("1", selectMoListBySiteIdVo.getSiteId());
    }
}
