package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.bean.pojo.BattTestRecordPojo;
import org.junit.Test;

import java.util.Date;

import static org.junit.Assert.assertEquals;

public class BattTestRecordPojoTest
{
    @Test
    public void testSetGetToString()
    {
        BattTestRecordPojo bean = new BattTestRecordPojo();
        bean.setId("77");
        bean.setDeviceId("77");
        bean.setTestTimeStart("77");
        bean.setTestTimeEnd("77");
        bean.setCauseTime("77");
        bean.setCreator("77");
        bean.setAftBackupPowerStatus("77");
        bean.setAftHealthStatus("77");
        bean.setPreBackupPowerStatus("77");
        bean.setPreHealthStatus("77");
        bean.setUpdater("77");
        bean.setGmtCreate(new Date());
        bean.setGmtModified(new Date());

        bean.getAftBackupPowerStatus();
        bean.getId();
        bean.getAftHealthStatus();
        bean.getTaskId();
        bean.getDeviceId();
        bean.getPreBackupPowerStatus();
        bean.getPreHealthStatus();
        bean.getUpdater();
        bean.getCreator();
        bean.getCauseTime();
        bean.getTestCause();
        bean.getGmtCreate();
        bean.getGmtModified();
        bean.toString();
        assertEquals("77",bean.getId());
    }
}
