package com.zte.uedm.battery.a_interfaces.peakshift.web;

/* Started by AICoder, pid:01d780c9ace5d7a14f3309f121bc341e77107b26 */

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftConfigService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftBCUAServiceImpl;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftConfigQueryDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftConfigUpdateDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.PeakShiftUpdateBean;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.PeakShiftConfigVo;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PeakShiftConfigControllerTest {

    @InjectMocks
    private PeakShiftConfigController controller;

    @Mock
    private PeakShiftConfigService peakShiftConfigService;

    @Mock
    private PeakShiftBCUAServiceImpl peakShiftBCUAService;

    @Mock
    private HttpServletRequest request;
    @Mock
    private MsgSenderService msgSenderService;
    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() {
        // Ensure mocks are injected into the controller
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(controller, "peakShiftConfigService", peakShiftConfigService);
        ReflectionTestUtils.setField(controller, "peakShiftBCUAService", peakShiftBCUAService);
    }

    @Test
    public void testSelectPeakShiftConfig() {
        PeakShiftConfigQueryDto dto = new PeakShiftConfigQueryDto();
        dto.setPageNo(1);
        dto.setPageSize(10);

        PageInfo<PeakShiftConfigVo> pageInfo = new PageInfo<>(Arrays.asList(new PeakShiftConfigVo()));
        when(peakShiftConfigService.selectBatchPeakShiftConfig(dto, "en",null)).thenReturn(pageInfo);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean response = controller.selectPeakShiftConfig(dto, "en",request);

        assertEquals(Optional.of(0), Optional.of(response.getCode()));
        assertEquals(1, ((List<?>) response.getData()).size());
    }



    @Test
    public void testUpdatePeakShiftConfig() {

        PeakShiftConfigUpdateDto dto = new PeakShiftConfigUpdateDto();
        PeakShiftUpdateBean bean1 = new PeakShiftUpdateBean();
        bean1.setDeviceType("type1");
        PeakShiftUpdateBean bean2 = new PeakShiftUpdateBean();
        bean2.setDeviceType("type2");
        List<PeakShiftUpdateBean> deviceList = new ArrayList<>();
        deviceList.add(bean1);
        deviceList.add(bean2);
        dto.setDeviceList(deviceList);
        when(request.getHeader("username")).thenReturn("user1");

        ResponseBean response = controller.updatePeakShiftConfig(dto, request, "en");

        verify(peakShiftConfigService).updatePeakShiftConfig(dto, "user1");
        assertEquals(Optional.of(0), Optional.of(response.getCode()));
    }

    @Test
    public void testUpdatePeakShiftConfigWithInvalidDeviceTypes() {
        PeakShiftConfigUpdateDto dto = new PeakShiftConfigUpdateDto();
        PeakShiftUpdateBean bean1 = new PeakShiftUpdateBean();
        bean1.setDeviceType("CSU6");
        PeakShiftUpdateBean bean2 = new PeakShiftUpdateBean();
        bean2.setDeviceType("SNMP");
        List<PeakShiftUpdateBean> deviceList = new ArrayList<>();
        deviceList.add(bean1);
        deviceList.add(bean2);
        dto.setDeviceList(deviceList);
        when(request.getHeader("username")).thenReturn("user1");

        ResponseBean response = controller.updatePeakShiftConfig(dto, request, "en");

        assertEquals(Optional.of(-2), Optional.of(response.getCode()));
        assertEquals("\"数据更新失败.选中的设备存在CSU6和SNMP\"", response.getMessage());
    }
}

/* Ended by AICoder, pid:01d780c9ace5d7a14f3309f121bc341e77107b26 */