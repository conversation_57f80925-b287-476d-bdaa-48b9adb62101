package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattBackupPowerEvalTrendPojo;
import com.zte.uedm.battery.controller.backuppower.dto.EvalTrendDto;
import com.zte.uedm.battery.controller.backuppower.vo.EvalTrendVo;
import com.zte.uedm.battery.domain.BackupPowerEvalDomain;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.export.manage.WriterExportFactory;
import com.zte.uedm.battery.service.BackupPowerEvalService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class BackupPowerEvalServiceImplTest
{

    @InjectMocks
    private BackupPowerEvalServiceImpl backupPowerEvalService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BackupPowerEvalDomain backupPowerDomain;
    @Mock
    private JsonService jsonService;
    @Mock
    private WriterExportFactory wf;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectTrendByConditionTest() throws UedmException, ParseException {
        EvalTrendDto evalTrendDto=new EvalTrendDto();
        evalTrendDto.setId("123");

        PageInfo<BattBackupPowerEvalTrendPojo> backupPowerPageInfo = new PageInfo<>();
        List<BattBackupPowerEvalTrendPojo> BattBackupPowerEvalTrendPojos = new ArrayList<>();
        BattBackupPowerEvalTrendPojo powerEvalTrendPojo = new BattBackupPowerEvalTrendPojo();
        powerEvalTrendPojo.setId("123");
        powerEvalTrendPojo.setName("123");
        //powerEvalTrendPojo.setEvalTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-07-08 00:00:00"));
        BattBackupPowerEvalTrendPojos.add(powerEvalTrendPojo);
        backupPowerPageInfo.setList(BattBackupPowerEvalTrendPojos);

        PageInfo<EvalTrendVo> evalDetailVoPageInfo=new PageInfo<>();
        List<EvalTrendVo> EvalTrendVos = new ArrayList<>();
        evalDetailVoPageInfo.setList(EvalTrendVos);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "3", 1, 10);

        Mockito.doReturn(backupPowerPageInfo).when(backupPowerDomain).selectTrendByCondition(Mockito.any(),
                Mockito.any());
        evalDetailVoPageInfo = backupPowerEvalService.selectTrendByCondition(evalTrendDto, serviceBaseInfoBean);
        Assert.assertEquals("0",String.valueOf(evalDetailVoPageInfo.getTotal()));

    }
}
