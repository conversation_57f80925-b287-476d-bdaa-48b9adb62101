package com.zte.uedm.battery.controller.batterytesttask;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.controller.batterytesttask.dto.DeviceSelectedDto;
import com.zte.uedm.battery.controller.batterytesttask.vo.DeviceSelectedVo;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.impl.BattTestTaskTemDevicesDomainImpl;
import com.zte.uedm.battery.rpc.ConfigurationRpcFake;
import com.zte.uedm.battery.rpc.MpRpcFake;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.uedm.function.sm.api.user.UserService;
import com.zte.uedm.redis.service.RedisService;
import ft.fake.redis.CaffeineCacheManagerFake;
import org.ehcache.config.CacheConfiguration;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

public class BatteryTestTaskControllerFTest
{
    @InjectMocks
    private BatteryTestTaskController batteryTestTaskController;

    @Mock
    private BattSohDomain battSohDomain;

    @Mock
    private MessageSenderService msgSenderService;
    @Mock
    private JsonService jsonService =new JsonService();
    @Resource
    private MpRpcFake mpRpc = new MpRpcFake();

    @Resource
    private ConfigurationRpcFake configurationRpcs = new ConfigurationRpcFake();

    @Mock
    private RedisService redisService;

    @Mock
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpc;

    @Mock
    HttpServletRequest httpServletRequest;

    @Mock
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;

    @Resource
    private CacheManager caffeineCacheManager = new CaffeineCacheManagerFake();

    @Mock
    private CacheConfiguration cacheConfiguration;
    @Mock
    private LicenseSwitchService licenseMgr;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private UserService userService;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private BackupPowerEvalDomain backupPowerEvalDomain;
    @Mock
    private BattTestRecordDomain battTestRecordDomain;
    @Mock
    private BattTestTaskTemDevicesDomainImpl battTestTaskTemDevicesDomain;
    @Mock
    private BattTestTaskDeviceDomain battTestTaskDeviceDomain;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BattTestTaskDomain battTestTaskDomain;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private BatteryTestDomain batteryTestDomain;
    @Before
    public void setUp() throws IOException, UedmException, ClassNotFoundException, IllegalAccessException, InstantiationException {
        httpServletRequest = Mockito.mock(HttpServletRequest.class);
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void UEDM_304162_given_设备选定参数不在可选值范围内_when_调用selectByCondition_then_抛出UedmException异常() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void UEDM_304163_given_所有测试任务设备为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals(null,responseBean.getData());    }

    @Test
    public void UEDM_304164_given_根据逻辑组id和监控对象name查询的SP监控对象为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setLogicGroupId("1");
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);
        try {
            ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void UEDM_304161_given_电池健康状态为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals(null,responseBean.getData());    }

    @Test
    public void UEDM_304168_given_测试状态为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals(null,responseBean.getData());    }

    @Test
    public void UEDM_304166_given_已选设备名称为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals(null,responseBean.getData());    }

    @Test
    public void UEDM_304167_given_设备信息为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);
        ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",httpServletRequest);
        Assert.assertEquals(null,responseBean.getData());
    }


    /* Started by AICoder, pid:y1d28jc12aj0f94142620b87e08250175407dbd7 */
    @Test
    public void UEDM_304165_given_电池备电状态为空_when_调用selectByCondition_then_返回空的设备筛选结果() throws UedmException {
        // 创建DeviceSelectedDto对象并设置条件，其中备份电源状态为"1"，测试状态为"1"，健康状态为"unEvaluate"
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));

        // 创建ServiceBaseInfoBean对象，模拟服务基本信息
        ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

        // 调用batteryTestTaskController的selectByCondition方法，并传入deviceSelectedDto、分页信息和语言信息
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);

        // 断言响应的数据部分应为null，表示没有找到符合条件的设备
        Assert.assertEquals(null, responseBean.getData());
    }
    /* Ended by AICoder, pid:y1d28jc12aj0f94142620b87e08250175407dbd7 */

    /* Started by AICoder, pid:32975m05df5c95714c79092700a55777f732cd0f */
    @Test
    public void UEDM_478228_given_用户具有所有直流电源的完全权限_when_过滤计划_then_展示计划() throws UedmException {
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);
        Assert.assertEquals(null, responseBean.getData());
    }

    @Test
    public void UEDM_478226_given_测试计划没有关联任何直流电源_when_过滤计划_then_不展示计划() throws UedmException {
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);
        Assert.assertEquals(null, responseBean.getData());
    }

    @Test
    public void UEDM_478224_given_用户没有直流电源的权限_when_过滤计划_then_不展示计划() throws UedmException {
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);
        Assert.assertEquals(null, responseBean.getData());
    }

    @Test
    public void UEDM_478220_given_用户具有部分直流电源的权限_when_过滤计划_then_不展示计划() throws UedmException {
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);
        ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);
        Assert.assertEquals(null, responseBean.getData());
    }

    @Test
    public void UEDM_478222_given_用户不具有某个直流电源的完全权限_when_过滤计划_then_不展示计划() throws UedmException {
        // 创建DeviceSelectedDto对象并设置条件，其中备份电源状态为"1"，测试状态为"1"，健康状态为"unEvaluate"
        DeviceSelectedDto deviceSelectedDto = new DeviceSelectedDto();
        deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));

        // 创建ServiceBaseInfoBean对象，模拟服务基本信息
        ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

        // 调用batteryTestTaskController的selectByCondition方法，并传入deviceSelectedDto、分页信息和语言信息
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1, 10, "zh_CN", httpServletRequest);

        // 断言响应的数据部分应为null，表示没有找到符合条件的设备
        Assert.assertEquals(null, responseBean.getData());
    }
    /* Ended by AICoder, pid:32975m05df5c95714c79092700a55777f732cd0f */
}