package com.zte.uedm.battery.enums.peak;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ErrorCodeTypeEnumTest
{
    @Test public void testGetCode()
    {
        assertEquals(-102, ErrorCodeTypeEnum.INTERVAL.getCode().intValue());
        assertEquals(-103, ErrorCodeTypeEnum.PRICE.getCode().intValue());
    }

    @Test public void testGetMessage()
    {
        assertEquals(ErrorCodeTypeEnum.INTERVAL.getZh(), ErrorCodeTypeEnum.INTERVAL.getMessage("zh-cn"));
        assertEquals(ErrorCodeTypeEnum.PRICE.getEn(), ErrorCodeTypeEnum.PRICE.getMessage("en-us"));
    }

    @Test public void testGeteMessage()
    {
        assertEquals("The power grid has a time period policy and cannot be deleted", ErrorCodeTypeEnum.INTERVAL.getEn());
        assertEquals("The power grid has electricity price policy and cannot be deleted", ErrorCodeTypeEnum.PRICE.getEn());
    }

    @Test public void testGetzMessage()
    {
        assertEquals("该电网已有时段政策，不能删除", ErrorCodeTypeEnum.INTERVAL.getZh());
        assertEquals("该电网已有电价政策，不能删除", ErrorCodeTypeEnum.PRICE.getZh());
    }
}
