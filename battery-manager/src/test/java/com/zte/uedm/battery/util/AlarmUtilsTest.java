package com.zte.uedm.battery.util;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.uedm.battery.bean.pv.PvExceptionRecordBean;
import com.zte.uedm.battery.cache.DeviceCacheFake;
import com.zte.uedm.battery.domain.AlarmPvDomain;
import com.zte.uedm.battery.mapper.PvAlarmRecordMapper;
import com.zte.uedm.battery.mapper.PvExceptionRecordMapper;
import com.zte.uedm.battery.opti.domain.gateway.ConfigurationServiceInterface;
import com.zte.uedm.battery.rpc.AlarmRpc;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import retrofit2.Call;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AlarmUtilsTest
{
    @InjectMocks
    private AlarmPvUtils alarmUtils;

    @Mock
    private AlarmRpc alarmRpc;

    @Mock
    private JsonService jsonService;

    @Mock
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpcImpl;

    @Mock
    private PvExceptionRecordMapper pvExceptionRecordMapper;

    @Mock
    private BlankService blankService;

    @Mock
    private PvAlarmRecordMapper pvAlarmRecordMapper;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Mock
    private ConfigurationServiceInterface configureServiceInterface;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private AlarmPvDomain alarmPvDomain;

    @Mock
    private DeviceCacheManager deviceCacheManager = new DeviceCacheFake();

    @Before
    public void init()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getAlarmByOidsTest() throws Exception
    {
        try
        {
            List<String> oids = new ArrayList<>();
            oids.add("object-1");

            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm = new Alarm();
            alarm.setMe("11");
            alarm.setAlarmraisedtime(1618984688621L);
            alarm.setId(100L);
            alarm.setAlarmcode(123L);
            alarms.add(alarm);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(1);
            Response<AlarmResponse> response = Response.success(alarmResponse);
            Call<AlarmResponse> responseBeanCall = mock(Call.class);
            when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11"));
            when(alarmRpc.getActiveAlarm(Mockito.any(), Mockito.anyString())).thenReturn(responseBeanCall);

            Map<String, Map<String, String>> pvAlarmCodeMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            map.put("123", "ok");
            pvAlarmCodeMap.put("pv.comp.missing", map);
            pvAlarmCodeMap.put("pv.comp.maintain.alarm", map);
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(pvAlarmCodeMap);
            when(responseBeanCall.execute()).thenReturn(response);
            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.when(dateTimeService.getStrTime(Mockito.any())).thenReturn("2021-04-21 10:20:00");
            List<PvExceptionRecordBean> pvExceptionRecordBeans = new ArrayList<>();
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBeans.add(pvExceptionRecordBean);
            when(pvExceptionRecordMapper.selectMissAlarmInfoById(Mockito.anyString()))
                    .thenReturn(pvExceptionRecordBeans);
            when(pvExceptionRecordMapper.selectMaintainAlarmInfoById(Mockito.anyString()))
                    .thenReturn(pvExceptionRecordBeans);
            alarmUtils.initialAlarmInfo("");
            Assert.assertEquals(1,alarms.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void getAlarmByOidsTest_PvId_Empty() throws Exception
    {
        try
        {
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(new HashMap<>());
            alarmUtils.initialAlarmInfo("");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void initialAlarmInfoTest_PvId_Exc() throws Exception
    {
        try
        {
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenThrow(new UedmException(-200, ""));
            alarmUtils.initialAlarmInfo("");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void getAlarmByOidsTest_Not_Empty() throws Exception
    {
        try
        {
            List<String> oids = new ArrayList<>();
            oids.add("object-1");

            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm = new Alarm();
            alarm.setMe("11");
            alarm.setAlarmraisedtime(1618984688621L);
            alarm.setId(100L);
            alarm.setAlarmcode(123L);
            alarms.add(alarm);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(1);
            Response<AlarmResponse> response = Response.success(alarmResponse);
            Call<AlarmResponse> responseBeanCall = mock(Call.class);
            when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11"));
            when(alarmRpc.getActiveAlarm(Mockito.any(), Mockito.anyString())).thenReturn(responseBeanCall);
            Mockito.when(dateTimeService.getStrTime(Mockito.any())).thenReturn("2021-04-21 10:20:00");
            Map<String, Map<String, String>> pvAlarmCodeMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            map.put("123", "ok");
            pvAlarmCodeMap.put("pv.comp.missing", map);
            pvAlarmCodeMap.put("pv.comp.maintain.alarm", map);
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(pvAlarmCodeMap);
            when(responseBeanCall.execute()).thenReturn(response);
            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            alarmUtils.initialAlarmInfo("");
            Assert.assertEquals(1,alarms.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void getAlarmByOidsTest_Exc() throws Exception
    {
        try
        {
            List<String> oids = new ArrayList<>();
            oids.add("object-1");

            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm = new Alarm();
            alarm.setMe("11");
            alarm.setAlarmraisedtime(1618984688621L);
            alarm.setId(100L);
            alarm.setAlarmcode(123L);
            alarms.add(alarm);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(1);
            Response<AlarmResponse> response = Response.success(alarmResponse);
            Call<AlarmResponse> responseBeanCall = mock(Call.class);
            when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11"));
            when(alarmRpc.getActiveAlarm(Mockito.any(), Mockito.anyString())).thenReturn(responseBeanCall);
//            Mockito.when(dateTimeService.getStrTime(Mockito.any())).thenReturn("2021-04-21 10:20:00");
            Map<String, Map<String, String>> pvAlarmCodeMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            map.put("123", "ok");
            pvAlarmCodeMap.put("pv.comp.missing", map);
            pvAlarmCodeMap.put("pv.comp.maintain.alarm", map);
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(pvAlarmCodeMap);
            when(responseBeanCall.execute()).thenReturn(response);
            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            alarmUtils.initialAlarmInfo("");
            Assert.assertEquals(1,alarms.size());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void getAlarmByOidsTest_Maintain_Empty() throws Exception
    {
        try
        {
            List<String> oids = new ArrayList<>();
            oids.add("object-1");

            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm = new Alarm();
            alarm.setMe("11");
            alarm.setAlarmraisedtime(1618984688621L);
            alarm.setId(100L);
            alarm.setAlarmcode(123L);
            alarms.add(alarm);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(1);
            Response<AlarmResponse> response = Response.success(alarmResponse);
            Call<AlarmResponse> responseBeanCall = mock(Call.class);
            when(siteSpBatteryRelatedRpcImpl.getAllPvIds()).thenReturn(Lists.newArrayList("11"));
            when(alarmRpc.getActiveAlarm(Mockito.any(), Mockito.anyString())).thenReturn(responseBeanCall);
            Mockito.when(dateTimeService.getStrTime(Mockito.any())).thenReturn("2021-04-21 10:20:00");
            Map<String, Map<String, String>> pvAlarmCodeMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            map.put("123", "ok");
            pvAlarmCodeMap.put("pv.comp.missing", map);
            pvAlarmCodeMap.put("pv.comp.maintain.alarm", map);
            when(configurationManagerRpcImpl.getPvAlarmCode()).thenReturn(pvAlarmCodeMap);
            when(responseBeanCall.execute()).thenReturn(response);
            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());

            List<PvExceptionRecordBean> pvExceptionRecordBeans = new ArrayList<>();
            PvExceptionRecordBean pvExceptionRecordBean = new PvExceptionRecordBean();
            pvExceptionRecordBeans.add(pvExceptionRecordBean);
            when(pvExceptionRecordMapper.selectMissAlarmInfoById(Mockito.anyString()))
                    .thenReturn(pvExceptionRecordBeans);
            when(pvExceptionRecordMapper.selectMaintainAlarmInfoById(Mockito.anyString()))
                    .thenReturn(new ArrayList<>());
            alarmUtils.initialAlarmInfo("");
            Assert.assertEquals(1,alarms.size());
        }
        catch (Exception e)
        {

        }
    }

}
