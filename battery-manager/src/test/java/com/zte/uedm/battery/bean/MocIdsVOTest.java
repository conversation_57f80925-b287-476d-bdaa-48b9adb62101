package com.zte.uedm.battery.bean;
import com.google.common.collect.Lists;
import java.util.List;

import com.zte.uedm.common.consts.MocType;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

public class MocIdsVOTest {

    @Test
    public void testForMocIdsVO(){
        MocIdsVO mocIdsVO = new MocIdsVO(Collections.singletonList("id-xxl"), MocType.BATT);
        mocIdsVO.setIdList(Lists.newArrayList());
        mocIdsVO.setScenarioType("");
        mocIdsVO.setMoc("");
        mocIdsVO.setName("");
        mocIdsVO.setPageNo(0);
        mocIdsVO.setPageSize(0);
        mocIdsVO.setSort("");
        mocIdsVO.setOrder("");

        List<String> idList = mocIdsVO.getIdList();
        String scenarioType = mocIdsVO.getScenarioType();
        String moc = mocIdsVO.getMoc();
        String name = mocIdsVO.getName();
        Integer pageNo = mocIdsVO.getPageNo();
        Integer pageSize = mocIdsVO.getPageSize();
        String sort = mocIdsVO.getSort();
        String order = mocIdsVO.getOrder();

        Assert.assertNotNull(mocIdsVO.toString());
    }
}
