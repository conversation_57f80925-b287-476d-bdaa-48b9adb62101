package com.zte.uedm.battery.pv.service.impl;

import com.zte.uedm.battery.pv.bean.CarbonReductionBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

public class CarbonReductionServiceImplTest {
    @InjectMocks
    private CarbonReductionServiceImpl carbonReductionServiceImpl;
    @Mock
    private RedisService redisService;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void queryCarbonReductionCoefficient() throws UedmException {
        Double a=0.12345;
        when(redisService.getCache(any(), any()))
                .thenReturn(a);
        Double result = carbonReductionServiceImpl.queryCarbonReductionCoefficient();
        Assert.assertEquals("0.12345", result.toString());
    }

    @Test
    public void carbonReductionCoefficientSetting() throws UedmException {
        doNothing().when(redisService).put(any(), any(), any());
        try {
           carbonReductionServiceImpl.carbonReductionCoefficientSetting(new CarbonReductionBean());
        } catch (UedmException e) {
            Assert.assertEquals(-100, (int)e.getErrorId());
        }
        CarbonReductionBean crb = new CarbonReductionBean();
        crb.setCarbonReductionCoefficient(0.12345);
        carbonReductionServiceImpl.carbonReductionCoefficientSetting(crb);
        assertEquals("0.12345",crb.getCarbonReductionCoefficient().toString());
    }
}