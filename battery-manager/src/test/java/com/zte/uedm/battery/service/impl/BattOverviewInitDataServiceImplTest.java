package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.domain.impl.BattTypeDomainImpl;
import com.zte.uedm.battery.domain.impl.BatteryRemainDischargringDurationEvalDomainImpl;
import com.zte.uedm.battery.mapper.BatteryBaseInfoMapper;
import com.zte.uedm.battery.rpc.AssetRpc;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.BattOverviewAssetConditionVo;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;


@SpringBootTest
public class BattOverviewInitDataServiceImplTest {

    @InjectMocks
    private BattOverviewInitDataServiceImpl battOverviewInitDataService;

    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private BatteryAttributeUtils batteryAttributeUtils;

    @Mock
    private BatteryBaseInfoMapper batteryBaseInfoMapper;

    @Mock
    private AssetRpc assetRpc;

    @Mock
    private JsonService jsonService;

    @Mock
    private I18nUtils i18nUtils;

    @Mock
    private BatteryRemainDischargringDurationEvalDomainImpl batteryRemainDischargringDurationEvalDomain;

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn("List").when(i18nUtils).getMapFieldByLanguageOption(any(), any());
        Mockito.doReturn(0).when(batteryBaseInfoMapper).insertBatteryInfoAndAssetInfo(any());

    }

    @Test
    public void initBatteryInfoTest() throws IOException,UedmException {
        List<DeviceEntity> batts = new ArrayList<>();
        DeviceEntity batteryBaseInfoPo = new DeviceEntity();
        batteryBaseInfoPo.setId("123");
        batteryBaseInfoPo.setPathId(new String[]{"11111"});
        batteryBaseInfoPo.setPathName("test");
        batteryBaseInfoPo.setExattribute("[{\"name\":\"type\",\"value\":\"0\"}]");
        batts.add(batteryBaseInfoPo);
        when(deviceCacheManager.getDeviceByIdsAndMoc(any(), any())).thenReturn(batts);
        when(batteryAttributeUtils.getBatteryTypeCode(any())).thenReturn("1");

        ResponseBean responseBean = mock(ResponseBean.class);
        Response<ResponseBean> response = Response.success(responseBean);
        response.body().setCode(0);
        response.body().setData("123");
        Call<ResponseBean> value = mock(Call.class);
        Mockito.doReturn(value).when(assetRpc).getAssetInfoByAssetNumber(any());

        List<BattOverviewAssetConditionVo> list = new ArrayList<>();
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("123");
        battOverviewAssetConditionVo.setBattType("1111");
        battOverviewAssetConditionVo.setBrand("2222");
        battOverviewAssetConditionVo.setManufacture("3333");
        list.add(battOverviewAssetConditionVo);

        Mockito.doReturn(list).when(jsonService).jsonToObject(any(), any(), any());
        Mockito.doReturn("aaa").when(jsonService).objectToJson(any());
        Mockito.when(value.execute()).thenReturn(response);

        List<IdNameBean> idNameResult = new ArrayList<>();
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setName("111");
        idNameBean.setId("123");
        idNameResult.add(idNameBean);

        battOverviewInitDataService.initBatteryInfoAndAssetInfo();
    }


    @Test
    public void insertBatteryInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.insertBatteryInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.insertBatteryInfo(list);
        Assert.assertEquals("0", integer.toString());
    }

    @Test
    public void updateBatteryInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.updateBatteryInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.updateBatteryInfo(list);
        Assert.assertEquals("0", integer.toString());
    }

    @Test
    public void deleteBatteryInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.deleteBatteryInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.deleteBatteryInfo(list);
        Assert.assertEquals("0", integer.toString());
    }

    @Test
    public void insertAssetInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.insertAssetInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.insertAssetInfo(list);
        Assert.assertEquals("0", integer.toString());
    }

    @Test
    public void updateAssetInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.updateAssetInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.updateAssetInfo(list);
        Assert.assertEquals("0", integer.toString());
    }

    @Test
    public void deleteAssetInfo() throws Exception {
        List<BatteryBaseInfoBean> list = new ArrayList<>();
        BatteryBaseInfoBean batteryBaseInfoPo = new BatteryBaseInfoBean();
        batteryBaseInfoPo.setId("123");
        list.add(batteryBaseInfoPo);

        Mockito.when(batteryBaseInfoMapper.deleteAssetInfo(Mockito.any())).thenReturn(0);
        Integer integer = battOverviewInitDataService.deleteAssetInfo(list);
        Assert.assertEquals("0", integer.toString());
    }


}
