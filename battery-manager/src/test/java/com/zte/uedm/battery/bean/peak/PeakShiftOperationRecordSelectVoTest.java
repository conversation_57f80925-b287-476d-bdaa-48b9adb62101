package com.zte.uedm.battery.bean.peak;

import org.junit.Assert;
import org.junit.Test;

public class PeakShiftOperationRecordSelectVoTest
{
    @Test
    public void test()
    {
        PeakShiftOperationRecordSelectInfoVo peakShiftOperationRecordSelectInfoVo = new PeakShiftOperationRecordSelectInfoVo();
        PeakShiftOperationRecordSelectVo peakShiftOperationRecordSelectVo = new PeakShiftOperationRecordSelectVo();
        peakShiftOperationRecordSelectVo.setId("2233");
        peakShiftOperationRecordSelectVo.setOperationType(peakShiftOperationRecordSelectInfoVo);
        peakShiftOperationRecordSelectVo.setStyle(peakShiftOperationRecordSelectInfoVo);
        peakShiftOperationRecordSelectVo.setFile(peakShiftOperationRecordSelectInfoVo);
        peakShiftOperationRecordSelectVo.toString();
        Assert.assertEquals("2233", peakShiftOperationRecordSelectVo.getId());
    }
}