package com.zte.uedm.battery.a_domain.safe.task;/* Started by AICoder, pid:j6eadvcf47r758714dfd095da060e52bd839e422 */
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.zte.uedm.battery.a_domain.safe.OperationLogService;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.common.service.DateTimeService;
import java.util.*;
import org.junit.*;
import org.mockito.*;

public class OperatorLogTaskTest {

    @InjectMocks private OperatorLogTask operatorLogTask;
    @Mock private OperationLogService operationLogService;
    @Mock private DateTimeService dateTimeService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testTask_NoTimeoutLogs() throws Exception {
        List<OperationLogBean> list=new ArrayList<>();
        OperationLogBean bean = new OperationLogBean();
        bean.setUpdateTime("2020-11-11 11:11:11");
        list.add(bean);
        long l = System.currentTimeMillis();
        when(dateTimeService.getLongTime(any())).thenReturn(l - 48 * 60 * 60 * 1002);
        when(operationLogService.selectByCondition(any())).thenReturn(list);
        operatorLogTask.task();
    }
}
/* Ended by AICoder, pid:j6eadvcf47r758714dfd095da060e52bd839e422 */