package com.zte.uedm.battery.a_infrastructure.repository.vpp.persistence;

import com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper.FrequencyRecordMapper;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyRecordBeanPo;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyRecordPo;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FrequencyStdRecordPo;
import com.zte.uedm.battery.a_interfaces.vpp.web.dto.FrequencyModulationDetailDto;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;

/* Started by AICoder, pid:sbe3fa549a1989c14a0908d751a4079058d5cc7c */
public class FrequencyRecordRepositoryImplTest {

    @InjectMocks
    private FrequencyRecordRepositoryImpl frequencyRecordRepositoryImpl;

    @Mock
    private FrequencyRecordMapper frequencyRecordMapper;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void insertFrequencyStartRecordTest() throws UedmException {
        Mockito.doNothing().when(frequencyRecordMapper).insertFrequencyStartRecord(Mockito.any());
        frequencyRecordRepositoryImpl.insertFrequencyStartRecord(new FrequencyRecordBeanPo());

        Mockito.doThrow(new RuntimeException()).when(frequencyRecordMapper).insertFrequencyStartRecord(Mockito.any());
        try {
            frequencyRecordRepositoryImpl.insertFrequencyStartRecord(new FrequencyRecordBeanPo());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void insertFrequencyStdRecordTest() throws UedmException {
        Mockito.doNothing().when(frequencyRecordMapper).insertFrequencyStdRecord(Mockito.any());
        frequencyRecordRepositoryImpl.insertFrequencyStdRecord(new FrequencyStdRecordPo());

        Mockito.doThrow(new RuntimeException()).when(frequencyRecordMapper).insertFrequencyStdRecord(Mockito.any());
        try {
            frequencyRecordRepositoryImpl.insertFrequencyStdRecord(new FrequencyStdRecordPo());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void updateFrequencyEndRecordTest() throws UedmException {
        Mockito.doNothing().when(frequencyRecordMapper).updateFrequencyEndRecord(Mockito.any());
        frequencyRecordRepositoryImpl.updateFrequencyEndRecord(new FrequencyRecordBeanPo());

        Mockito.doThrow(new RuntimeException()).when(frequencyRecordMapper).updateFrequencyEndRecord(Mockito.any());
        try {
            frequencyRecordRepositoryImpl.updateFrequencyEndRecord(new FrequencyRecordBeanPo());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectTargetPowerListTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectTargetPowerList(Mockito.any())).thenReturn("");
        frequencyRecordRepositoryImpl.selectTargetPowerList("");

        Mockito.when(frequencyRecordMapper.selectTargetPowerList(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectTargetPowerList("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void updateFrequencyTargetPowerRecordTest() throws UedmException {
        Mockito.doNothing().when(frequencyRecordMapper).updateFrequencyTargetPowerRecord(Mockito.any());
        frequencyRecordRepositoryImpl.updateFrequencyTargetPowerRecord(new FrequencyRecordBeanPo());

        Mockito.doThrow(new RuntimeException()).when(frequencyRecordMapper).updateFrequencyTargetPowerRecord(Mockito.any());
        try {
            frequencyRecordRepositoryImpl.updateFrequencyTargetPowerRecord(new FrequencyRecordBeanPo());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void updateSouthApplyRecordTest() throws UedmException {
        Mockito.doNothing().when(frequencyRecordMapper).updateSouthApplyRecord(Mockito.any());
        frequencyRecordRepositoryImpl.updateSouthApplyRecord(new FrequencyRecordBeanPo());

        Mockito.doThrow(new RuntimeException()).when(frequencyRecordMapper).updateSouthApplyRecord(Mockito.any());
        try {
            frequencyRecordRepositoryImpl.updateSouthApplyRecord(new FrequencyRecordBeanPo());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectFrequencyStartTimeTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectFrequencyStartTime(Mockito.any())).thenReturn(new Date());
        frequencyRecordRepositoryImpl.selectFrequencyStartTime("");

        Mockito.when(frequencyRecordMapper.selectFrequencyStartTime(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectFrequencyStartTime("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectFrequencyMoIdTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectFrequencyMoId(Mockito.any())).thenReturn("");
        frequencyRecordRepositoryImpl.selectFrequencyMoId("");

        Mockito.when(frequencyRecordMapper.selectFrequencyMoId(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectFrequencyMoId("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectByMoIdTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectByMoId(Mockito.any())).thenReturn(new ArrayList<>());
        frequencyRecordRepositoryImpl.selectByMoId("");

        Mockito.when(frequencyRecordMapper.selectByMoId(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectByMoId("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void getDeviceFmRecordTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.getDeviceFmRecord(Mockito.any())).thenReturn(new ArrayList<>());
        frequencyRecordRepositoryImpl.getDeviceFmRecord(new FrequencyModulationDetailDto());

        Mockito.when(frequencyRecordMapper.getDeviceFmRecord(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.getDeviceFmRecord(new FrequencyModulationDetailDto());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void getDeviceFmOverviewTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.getDeviceFmOverview(Mockito.any())).thenReturn(new FrequencyRecordPo());
        frequencyRecordRepositoryImpl.getDeviceFmOverview("");

        Mockito.when(frequencyRecordMapper.getDeviceFmOverview(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.getDeviceFmOverview("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void getDetailDiagramTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.getDetailDiagram(Mockito.any())).thenReturn(new FrequencyRecordPo());
        frequencyRecordRepositoryImpl.getDetailDiagram(new FrequencyModulationDetailDto());

        Mockito.when(frequencyRecordMapper.getDetailDiagram(Mockito.any())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.getDetailDiagram(new FrequencyModulationDetailDto());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectFrequencyStartRecordTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectFrequencyStartRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        frequencyRecordRepositoryImpl.selectFrequencyStartRecord("", "", "");

        Mockito.when(frequencyRecordMapper.selectFrequencyStartRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectFrequencyStartRecord("", "", "");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void selectFrequencyModulationMonitorTest() throws UedmException {
        Mockito.when(frequencyRecordMapper.selectFrequencyModulationMonitor()).thenReturn(new ArrayList<>());
        frequencyRecordRepositoryImpl.selectFrequencyModulationMonitor();

        Mockito.when(frequencyRecordMapper.selectFrequencyModulationMonitor()).thenThrow(new RuntimeException());
        try {
            frequencyRecordRepositoryImpl.selectFrequencyModulationMonitor();
        } catch (Exception e) {
            // Exception handling block
        }
    }
}
/* Ended by AICoder, pid:sbe3fa549a1989c14a0908d751a4079058d5cc7c */
