package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

/* Started by AICoder, pid:54cf8w650147ff1142a50832a09c9758008563bb */
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.OriginalDataBean;
import static org.junit.jupiter.api.Assertions.*;
import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

public class OriginalDataBeanTest {

    @Test
    public void testOriginalDataBeanFields() {
        OriginalDataBean bean = new OriginalDataBean();

        // 测试默认值
        assertNull(bean.getDeviceId());
        assertNull(bean.getOmpId());
        assertNull(bean.getRelationPosition());
        assertNull(bean.getIndex());
        assertNull(bean.getValue());
        assertNull(bean.getInvalidTime());
        assertNull(bean.getUpdateTime());

        // 设置字段值
        bean.setDeviceId("device1");
        bean.setOmpId("omp1");
        bean.setRelationPosition("position1");
        bean.setIndex("index1");
        bean.setValue("value1");
        bean.setInvalidTime(1640995200L); // 2022-01-01T00:00:00Z
        bean.setUpdateTime(1640995200L);  // 2022-01-01T00:00:00Z

        // 验证字段值
        assertEquals("device1", bean.getDeviceId());
        assertEquals("omp1", bean.getOmpId());
        assertEquals("position1", bean.getRelationPosition());
        assertEquals("index1", bean.getIndex());
        assertEquals("value1", bean.getValue());
        assertEquals(1640995200L, bean.getInvalidTime());
        assertEquals(1640995200L, bean.getUpdateTime());
    }

    @Test
    public void testToStringMethod() {
        OriginalDataBean bean = new OriginalDataBean();
        bean.setDeviceId("device1");
        bean.setOmpId("omp1");
        bean.setRelationPosition("position1");
        bean.setIndex("index1");
        bean.setValue("value1");
        bean.setInvalidTime(1640995200L);
        bean.setUpdateTime(1640995200L);

        String expectedToString = "OriginalDataBean(adapterPointId=null, deviceId=device1, ompId=omp1, relationPosition=position1, index=index1, value=value1, invalidTime=1640995200, updateTime=1640995200)";
        assertThat(bean.toString()).isEqualTo(expectedToString);
    }
}

/* Ended by AICoder, pid:54cf8w650147ff1142a50832a09c9758008563bb */
