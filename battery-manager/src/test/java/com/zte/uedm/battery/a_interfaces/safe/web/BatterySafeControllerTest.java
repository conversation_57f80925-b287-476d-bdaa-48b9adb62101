package com.zte.uedm.battery.a_interfaces.safe.web;

import com.zte.ums.zenap.sm.agent.api.BrowserCipherService;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.battery.a_application.safe.executor.EkeHistoryRecordCommdService;
import com.zte.uedm.battery.a_interfaces.safe.web.dto.BatteryEkeyExportDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BrowserCipherService.class})
public class BatterySafeControllerTest
{
    @InjectMocks
    private BatterySafeController batterySafeController;

    @Mock
    private EkeHistoryRecordCommdService ekeHistoryRecordCommdService;

    @Mock
    HttpServletRequest httpServletRequest;

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
        httpServletRequest = Mockito.mock(HttpServletRequest.class);
    }

    @Test
    public void exportEkeyTest() throws Exception
    {
        BatteryEkeyExportDto dto = new BatteryEkeyExportDto();
        dto.setPasswd("test");

        PowerMockito.mockStatic(BrowserCipherService.class);
        when(BrowserCipherService.decryptFromBrowser(any(), any())).thenReturn("adssda");
        ResponseBean responseBean = batterySafeController.exportEkey(httpServletRequest, new MockHttpServletResponse(), dto);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }
}
