package com.zte.uedm.battery.controller.battlife.dto;

import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.battery.controller.battlife.po.BattLifeRatedCfgPo;
import com.zte.uedm.battery.controller.battlife.po.BattLifeSpecificCfgPo;
import org.junit.Assert;
import org.junit.Test;

public class BeanTest {
    @Test
    public void test() {
        Exception exception = null;
        try {
            PojoTestUtil.TestForPojo(BattInfoBean.class);
            PojoTestUtil.TestForPojo(SpecificCfgInsertDto.class);
            PojoTestUtil.TestForPojo(SpecificCfgDeleteDto.class);
            PojoTestUtil.TestForPojo(SpecificCfgUpdateDto.class);
            PojoTestUtil.TestForPojo(SpecificCfgQueryDto.class);
            PojoTestUtil.TestForPojo(BattLifeRatedCfgPo.class);
            PojoTestUtil.TestForPojo(BattLifeSpecificCfgPo.class);
            PojoTestUtil.TestForPojo(BattLifeRatedCfgDto.class);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertEquals(null, exception);
    }
}
