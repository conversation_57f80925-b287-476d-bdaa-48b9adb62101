package com.zte.uedm.battery.domain.po;

import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;

import static org.junit.Assert.*;

public class BackupPowerBeanTest {

    private BackupPowerBean backupPowerBeanUnderTest;

    @Before
    public void setUp() throws Exception {
        backupPowerBeanUnderTest = new BackupPowerBean("id", "name");
    }

    @Test
    public void Test()
    {
        backupPowerBeanUnderTest.setId("");
        backupPowerBeanUnderTest.setName("");
        backupPowerBeanUnderTest.setLiBatteryBackupPowerBeans(new ArrayList<>());
        backupPowerBeanUnderTest.setLeadAcidBatteryBackupPowerBeans(new ArrayList<>());
        String id = backupPowerBeanUnderTest.getId();
        backupPowerBeanUnderTest.getName();
        backupPowerBeanUnderTest.getLiBatteryBackupPowerBeans();
        backupPowerBeanUnderTest.getLeadAcidBatteryBackupPowerBeans();

        assertEquals ( "", id);
    }

    @Test
    public void testAddLiBattery() {
        // Setup
        final BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("0.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));

        // Run the test
        backupPowerBeanUnderTest.addLiBattery(batteryBackupPowerBean);

        // Verify the results
        assertEquals ( 1, backupPowerBeanUnderTest.getLiBatteryBackupPowerBeans().size());
    }

    @Test
    public void testAddLeadAcidBattery() {
        // Setup
        final BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("0.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));

        // Run the test
        backupPowerBeanUnderTest.addLeadAcidBattery(batteryBackupPowerBean);

        // Verify the results
        assertEquals ( 1, backupPowerBeanUnderTest.getLeadAcidBatteryBackupPowerBeans().size());

    }

    @Test
    public void testCheckBatteryPackParamIsNull() {
        // Setup
        // Run the test
        final boolean result = backupPowerBeanUnderTest.checkLiBatteryPackParamIsNull(new ArrayList<>());

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testCheckBatteryPackParamIsNotNull() {
        // Setup
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("0.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));
        backupPowerBeanUnderTest.addLiBattery(batteryBackupPowerBean);

        // Run the test
        final boolean result = backupPowerBeanUnderTest.checkLiBatteryPackParamIsNull(new ArrayList<>());

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckBatteryPackSocParamIsNull() {
        // Setup
        // Run the test
        final boolean result = backupPowerBeanUnderTest.checkLiBatteryPackSocParamIsNull();

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testCheckBatteryPackSocParamIsNotNull() {
        // Setup
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("0.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));
        backupPowerBeanUnderTest.addLiBattery(batteryBackupPowerBean);

        // Run the test
        final boolean result = backupPowerBeanUnderTest.checkLiBatteryPackSocParamIsNull();

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCalculateActualLiBatteryPackBackupDuration() {
        // Setup
        // Run the test
        final BigDecimal result = backupPowerBeanUnderTest.calculateActualLiBatteryPackBackupDuration();

        // Verify the results
        assertEquals(null, result);
    }

    @Test
    public void testCalculateLiBatteryPackBackupDuration1() throws Exception {
        // Setup
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("2.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));
        backupPowerBeanUnderTest.addLiBattery(batteryBackupPowerBean);

        // Run the test
        final BigDecimal result = backupPowerBeanUnderTest.calculateActualLiBatteryPackBackupDuration();

        // Verify the results
        assertEquals(0, BigDecimal.ZERO.compareTo(result));
    }

    @Test
    public void testCalculateLiBatteryPackBackupDuration2() throws Exception {
        // Setup
        BatteryBackupPowerBean batteryBackupPowerBean = new BatteryBackupPowerBean();
        batteryBackupPowerBean.setId("id");
        batteryBackupPowerBean.setName("name");
        batteryBackupPowerBean.setCapacity(new BigDecimal("0.00"));
        batteryBackupPowerBean.setHealth(new BigDecimal("0.00"));
        batteryBackupPowerBean.setCurr(new BigDecimal("0.00"));
        batteryBackupPowerBean.setSoc(new BigDecimal("0.00"));
        backupPowerBeanUnderTest.addLiBattery(batteryBackupPowerBean);

        // Run the test
        assertEquals(null, backupPowerBeanUnderTest.calculateActualLiBatteryPackBackupDuration());
    }
}
