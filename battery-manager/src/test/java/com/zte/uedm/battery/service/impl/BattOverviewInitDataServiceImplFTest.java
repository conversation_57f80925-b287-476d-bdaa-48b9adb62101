package com.zte.uedm.battery.service.impl;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.domain.impl.BatteryRemainDischargringDurationEvalDomainImpl;
import com.zte.uedm.battery.rpc.AssetRpcFake;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.redis.service.RedisService;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/* Started by AICoder, pid:0b7453f225ca458989f55ec0af38b279 */
public class BattOverviewInitDataServiceImplFTest {

    @InjectMocks
    private BattOverviewInitDataServiceImpl battOverviewInitDataService;

    @Mock
    private BatteryRemainDischargringDurationEvalDomainImpl batteryRemainDischargringDurationEvalDomain;


    @Mock
    private RedisService redisService;

    @Resource
    private AssetRpcFake assetRpc = new AssetRpcFake();


    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    @SneakyThrows
    // @Test
    public void UEDM_304142_given_电池缓存中存在电池基础信息_when_调用电池缓存更新数据库方法_then_数据库更新成功() {
        List<String> arrayList = new ArrayList<>();
        arrayList.add("123");
        Mockito.doReturn(arrayList).when(batteryRemainDischargringDurationEvalDomain).getFilteredBatteryMoIdList(Mockito.any());

        List<MonitorObjectEntity> filterMonitorObject = new ArrayList<>();
        MonitorObjectEntity monitorObject1 = new MonitorObjectEntity();
        monitorObject1.setId("123");
        monitorObject1.setIdPath("test123456");
        monitorObject1.setPathName("测试");
        monitorObject1.setExtendAttribute("PbAc");
        filterMonitorObject.add(monitorObject1);

        battOverviewInitDataService.initBatteryInfoAndAssetInfo();
    }


    @SneakyThrows
    // @Test
    public void UEDM_304144_given_资产管理asset存在电池资产信息_电池监控对象id_when_调用获取资产管理更新数据库方法_then_数据库更新成功() {
        List<String> arrayList = new ArrayList<>();
        arrayList.add("123");
        Mockito.doReturn(arrayList).when(batteryRemainDischargringDurationEvalDomain).getFilteredBatteryMoIdList(Mockito.any());

        List<MonitorObjectEntity> filterMonitorObject = new ArrayList<>();
        MonitorObjectEntity monitorObject1 = new MonitorObjectEntity();
        monitorObject1.setId("1");
        monitorObject1.setIdPath("test123456");
        monitorObject1.setPathName("测试");
        monitorObject1.setExtendAttribute("PbAc");
        filterMonitorObject.add(monitorObject1);

        battOverviewInitDataService.initBatteryInfoAndAssetInfo();
    }


    @SneakyThrows
    // @Test
    public void UEDM_304143_given_电池缓存不存在电池基础信息_资产管理asset不存在电池资产信息_when_调用更新数据库方法_then_数据库不更新() {
        List<MonitorObjectEntity> monitorObjectEntitys = new ArrayList<>();
        MonitorObjectEntity monitorObject = new MonitorObjectEntity();
        monitorObjectEntitys.add(monitorObject);

        battOverviewInitDataService.initBatteryInfoAndAssetInfo();
    }

}
/* Ended by AICoder, pid:0b7453f225ca458989f55ec0af38b279 */
