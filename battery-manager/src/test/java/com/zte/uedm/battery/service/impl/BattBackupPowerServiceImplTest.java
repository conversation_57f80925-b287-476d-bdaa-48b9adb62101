package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.domain.BattBackupPowerDomain;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

public class BattBackupPowerServiceImplTest
{
    @InjectMocks
    private BattBackupPowerServiceImpl battBackupPowerService;
    @Mock
    private BattBackupPowerDomain battBackupPowerDomain;
    @Mock
    private BattAssetDomain battAssetDomain;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testselectBackupPowerStatusLevels_normal() throws Exception
    {
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("user","ip","zh");
        Mockito.doReturn(new ArrayList<>()).when(battBackupPowerDomain).getBackupPowerStatusLevels(Mockito.anyString());
        Assert.assertSame(0,battBackupPowerService.selectBackupPowerStatusLevels(serviceBaseInfoBean).size());
    }

    @Test
    public void testselectBackupPowerStatusLevels_exception() throws Exception
    {
        try {
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("user","ip","zh");
            Mockito.doThrow(new RuntimeException("")).when(battBackupPowerDomain).getBackupPowerStatusLevels(Mockito.anyString());
            battBackupPowerService.selectBackupPowerStatusLevels(serviceBaseInfoBean);
        } catch (UedmException e) {
            Assert.assertSame(-1,e.getErrorId());
        }
    }

    @Test
    public void selectApplicatonScenes_normal() throws Exception
    {
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("user","ip","zh");
        Mockito.doReturn(new ArrayList<>()).when(battBackupPowerDomain).selectApplicatonScenes(Mockito.anyString());
        Assert.assertEquals(0, battBackupPowerService.selectApplicatonScenes(serviceBaseInfoBean).size());
    }

    @Test
    public void selectApplicatonScenes_exception() throws Exception
    {
        try {
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("user","ip","zh");
            Mockito.doThrow(new RuntimeException("")).when(battBackupPowerDomain).selectApplicatonScenes(Mockito.anyString());
            battBackupPowerService.selectApplicatonScenes(serviceBaseInfoBean);
        } catch (UedmException e) {
            Assert.assertEquals(-1, e.getErrorId().intValue());
        }
    }

    @Test
    public void searchSPManufacturer_normal() throws Exception
    {
        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battAssetDomain).searchManufacturerInfos(Mockito.any(), Mockito.any());
        Assert.assertEquals(0, battBackupPowerService.searchSPManufacturer("name", new ServiceBaseInfoBean("","")).getTotal());
    }

    @Test
    public void searchSPBrand_normal() throws Exception
    {
        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battAssetDomain).searchBrandInfos(Mockito.any(), Mockito.any());
        Assert.assertEquals(0, battBackupPowerService.searchSPBrand("name", new ServiceBaseInfoBean("","")).getTotal());
    }

    @Test
    public void searchSPSeries_normal() throws Exception
    {
        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battAssetDomain).searchSeriesInfos(Mockito.any(), Mockito.any());
        Assert.assertEquals(0, battBackupPowerService.searchSPSeries("name", new ServiceBaseInfoBean("","")).getTotal());
    }

    @Test
    public void searchSPModel_normal() throws Exception
    {
        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battAssetDomain).searchModelInfos(Mockito.any(), Mockito.any());
        Assert.assertEquals(0, battBackupPowerService.searchSPModel("name", new ServiceBaseInfoBean("","")).getTotal());
    }
    @Test
    public void  getSwitchPowerAndBattery(){
        PageInfo<IdNameBean> pageSp = new PageInfo<>();
        List<IdNameBean> list = new ArrayList<>();
        IdNameBean bean = new IdNameBean();
        bean.setId("unknown");
        bean.setName("未知");
        list.add(bean);
        pageSp.setList(list);
        pageSp.setTotal(1l);
        PageInfo<IdNameBean> pageBatt = new PageInfo<>();
        pageBatt.setList(list);
        pageBatt.setTotal(1l);
        PageInfo<IdNameBean> page = battBackupPowerService.getSwitchPowerAndBattery(pageSp,pageBatt);
        Assert.assertEquals(1l,page.getTotal());
    }
}
