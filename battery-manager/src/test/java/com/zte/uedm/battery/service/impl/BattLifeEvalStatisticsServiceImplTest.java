package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattLifeEvalMBean;
import com.zte.uedm.battery.bean.BattLifeLevelsPojo;
import com.zte.uedm.battery.bean.ThresholdBean;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.dto.BattLifeEvalStatisticsDto;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.vo.BattLifeEvalStatisticsVo;
import com.zte.uedm.battery.controller.BattLifeEvalStatistics.vo.StatisticsResultVo;
import com.zte.uedm.battery.domain.impl.BattLifeDomainImpl;
import com.zte.uedm.battery.domain.impl.BattLifeEvalStatisticsDomainImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BattLifeEvalStatisticsServiceImplTest {

    @InjectMocks
    private BattLifeEvalStatisticsServiceImpl battLifeEvalStatisticsService;

    @Mock
    private BattLifeEvalStatisticsDomainImpl battLifeEvalStatisticsDomain;

    @Mock
    private BattLifeDomainImpl battLifeDomain;

    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSuccessfully()throws Exception
    {
        PageInfo<BattLifeEvalMBean> pageInfo = new PageInfo<>();
        List<BattLifeEvalMBean> battLifeEvalMBeans = pageInfo.getList();
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","12",1,10);
        PageInfo<BattLifeEvalStatisticsVo> pageInfo1 = new PageInfo<>();
        try {
            Mockito.doReturn(pageInfo).when(battLifeEvalStatisticsDomain).selectEvalTimeAndLife(Mockito.any(), Mockito.any());
            battLifeEvalStatisticsService.selectByCondition(battLifeEvalStatisticsDto, serviceBaseInfoBean);
            Assert.assertEquals(1, battLifeEvalMBeans.size());
        }catch (Exception e){
            
        }

    }

    @Test
    public void test()throws Exception
    {
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","12",1,10);

        List<BattLifeEvalMBean> battLifeEvalMBeans =new ArrayList<>();
        BattLifeEvalMBean bean1 = new BattLifeEvalMBean();
        bean1.setLife(1);
        bean1.setEvalTime("2022-02");
        battLifeEvalMBeans.add(bean1);
        PageInfo<BattLifeEvalMBean> pageInfo = new PageInfo<>(battLifeEvalMBeans);
        List<BattLifeLevelsPojo> battLifeLevelsPojoPageInfo = new ArrayList<>();
        BattLifeEvalMBean bean = new BattLifeEvalMBean();
        bean.setLife(1);

        String lifeStr = "unknown";

        try {
            Mockito.doReturn(pageInfo).when(battLifeEvalStatisticsDomain).selectEvalTimeAndLife(Mockito.any(), Mockito.any());
            Mockito.doReturn(battLifeLevelsPojoPageInfo).when(battLifeDomain).getAlls();

            if (null!= bean.getLife()){
                battLifeEvalStatisticsService.selectByCondition(battLifeEvalStatisticsDto, serviceBaseInfoBean);
                Assert.assertEquals(1,(int)bean.getLife());
            }

        }catch (Exception e)
        {
         
        }
    }

    @Test
    public void test1()throws Exception
    {
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","12",1,10);

        List<BattLifeEvalMBean> battLifeEvalMBeans =new ArrayList<>();
        BattLifeEvalMBean bean1 = new BattLifeEvalMBean();
        bean1.setLife(1);
        bean1.setEvalTime("2022-02");
        battLifeEvalMBeans.add(bean1);
        PageInfo<BattLifeEvalMBean> pageInfo = new PageInfo<>(battLifeEvalMBeans);
        List<BattLifeLevelsPojo> battLifeLevelsPojoPageInfo = new ArrayList<>();
        BattLifeLevelsPojo pojo = new BattLifeLevelsPojo();
        pojo.setId("1");
        pojo.setName("77");
        battLifeLevelsPojoPageInfo.add(pojo);
        BattLifeEvalMBean bean = new BattLifeEvalMBean();
        bean.setLife(1);

        String lifeStr = "unknown";

        try {
            Mockito.doReturn(pageInfo).when(battLifeEvalStatisticsDomain).selectEvalTimeAndLife(Mockito.any(), Mockito.any());
            Mockito.doReturn(battLifeLevelsPojoPageInfo).when(battLifeDomain).getAlls();

            if (null!= bean.getLife()){
                battLifeEvalStatisticsService.selectByCondition(battLifeEvalStatisticsDto, serviceBaseInfoBean);
                Assert.assertEquals(1,(int)bean.getLife());
            }

        }catch (Exception e)
        {
            
        }
    }

    @Test
    public void test2()throws Exception
    {
        BattLifeEvalStatisticsDto battLifeEvalStatisticsDto = new BattLifeEvalStatisticsDto();
        battLifeEvalStatisticsDto.setLogicGroupId("77");
        ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","12",1,10);

        List<BattLifeEvalMBean> battLifeEvalMBeans =new ArrayList<>();
        BattLifeEvalMBean bean1 = new BattLifeEvalMBean();
        bean1.setLife(3);
        bean1.setEvalTime("2022-02");
        battLifeEvalMBeans.add(bean1);
        PageInfo<BattLifeEvalMBean> pageInfo = new PageInfo<>(battLifeEvalMBeans);
        List<BattLifeLevelsPojo> battLifeLevelsPojoPageInfo = new ArrayList<>();
        BattLifeLevelsPojo pojo = new BattLifeLevelsPojo();
        pojo.setId("1");
        pojo.setName("77");
        battLifeLevelsPojoPageInfo.add(pojo);
        BattLifeEvalMBean bean = new BattLifeEvalMBean();
        bean.setLife(1);

        ThresholdBean thresholdBean = new ThresholdBean();
        thresholdBean.setMin(1.0);
        thresholdBean.setMax(7.0);
        try {
            Mockito.doReturn(pageInfo).when(battLifeEvalStatisticsDomain).selectEvalTimeAndLife(Mockito.any(), Mockito.any());
            Mockito.doReturn(battLifeLevelsPojoPageInfo).when(battLifeDomain).getAlls();
            Mockito.doReturn(thresholdBean).when(jsonService).jsonToObject(Mockito.any(), Mockito.any());

            if (null!= bean.getLife()){
                battLifeEvalStatisticsService.selectByCondition(battLifeEvalStatisticsDto, serviceBaseInfoBean);
                Assert.assertEquals(1,(int)bean.getLife());
            }

        }catch (Exception e)
        {
            
        }
    }

}
