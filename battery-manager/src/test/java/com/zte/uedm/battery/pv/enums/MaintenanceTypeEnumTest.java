package com.zte.uedm.battery.pv.enums;

import org.junit.Test;

import static org.junit.Assert.*;

public class MaintenanceTypeEnumTest {

    @Test
    public void getRemarkByCode() {
        for (MaintenanceTypeEnum value : MaintenanceTypeEnum.values()) {
            String remark = value.getRemark();
            String code = value.getCode();
            String remarkByCode = MaintenanceTypeEnum.getRemarkByCode(code);
        }
        String remarkByCode = MaintenanceTypeEnum.getRemarkByCode("123");

    }
}