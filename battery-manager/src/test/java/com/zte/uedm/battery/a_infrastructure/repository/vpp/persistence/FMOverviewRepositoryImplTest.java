package com.zte.uedm.battery.a_infrastructure.repository.vpp.persistence;

import com.zte.uedm.battery.a_infrastructure.repository.vpp.mapper.FMOverviewMapper;
import com.zte.uedm.battery.a_infrastructure.repository.vpp.po.FMOverviewPo;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

/* Started by AICoder, pid:o7261dbe28pef5814e5809dc505f406262d69a8f */
public class FMOverviewRepositoryImplTest {

    @InjectMocks
    private FMOverviewRepositoryImpl fmOverviewRepositoryImpl;

    @Mock
    private FMOverviewMapper fmOverviewMapper;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectFmOverviewBeanTest() throws UedmException {
        List<FMOverviewPo> fmOverviewPos = new ArrayList<>();
        Mockito.when(fmOverviewMapper.selectFmOverviewBean(Mockito.any())).thenReturn(fmOverviewPos);
        fmOverviewRepositoryImpl.selectFmOverviewBean("");

        Mockito.when(fmOverviewMapper.selectFmOverviewBean(Mockito.any())).thenThrow(new RuntimeException());
        try {
            fmOverviewRepositoryImpl.selectFmOverviewBean("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void deleteFmOverviewBeanTest() throws UedmException {
        Mockito.when(fmOverviewMapper.deleteFmOverviewBean(Mockito.any())).thenReturn(1);
        fmOverviewRepositoryImpl.deleteFmOverviewBean("");

        Mockito.when(fmOverviewMapper.deleteFmOverviewBean(Mockito.any())).thenThrow(new RuntimeException());
        try {
            fmOverviewRepositoryImpl.deleteFmOverviewBean("");
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void insertFmOverviewBeanTest() throws UedmException {
        Mockito.doNothing().when(fmOverviewMapper).insertFmOverviewBean(Mockito.any());
        fmOverviewRepositoryImpl.insertFmOverviewBean(new ArrayList<>());

        Mockito.doThrow(new RuntimeException()).when(fmOverviewMapper).insertFmOverviewBean(Mockito.any());
        try {
            fmOverviewRepositoryImpl.insertFmOverviewBean(new ArrayList<>());
        } catch (Exception e) {
            // Exception handling block
        }
    }

    @Test
    public void updateFmOverviewBeanTest() throws UedmException {
        Mockito.when(fmOverviewMapper.updateFmOverviewBean(Mockito.any())).thenReturn(1);
        fmOverviewRepositoryImpl.updateFmOverviewBean(new ArrayList<>());

        Mockito.when(fmOverviewMapper.updateFmOverviewBean(Mockito.any())).thenThrow(new RuntimeException());
        try {
            fmOverviewRepositoryImpl.updateFmOverviewBean(new ArrayList<>());
        } catch (Exception e) {
            // Exception handling block
        }
    }
}
/* Ended by AICoder, pid:o7261dbe28pef5814e5809dc505f406262d69a8f */
