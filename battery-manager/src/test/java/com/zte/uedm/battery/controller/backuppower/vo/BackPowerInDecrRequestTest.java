package com.zte.uedm.battery.controller.backuppower.vo;

import org.junit.Assert;
import org.junit.Test;


public class BackPowerInDecrRequestTest {
    @Test
    public void test() {
        BackPowerInDecrRequest request = new BackPowerInDecrRequest();
        request.setLogicGroupId("batt1");
        request.setOrder("position");
        request.setSort("asc");
        request.setPageNo(1);
        request.setPageSize(1);
        Assert.assertEquals("batt1", request.getLogicGroupId());
        Assert.assertEquals("position", request.getOrder());
        Assert.assertEquals("asc", request.getSort());
        Assert.assertNotNull(request.getPageNo());
        Assert.assertNotNull(request.getPageSize());
        Assert.assertNotNull(request.toString());
    }

}