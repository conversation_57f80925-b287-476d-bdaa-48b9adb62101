package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.bean.pojo.AddPricePolicyDetailPO;
import org.junit.Assert;
import org.junit.Test;

public class AddPricePolicyDetailPOTest {
    @Test
    public void addPricePolicyDetailPOTest() throws Exception{
        AddPricePolicyDetailPO addPricePolicyDetailPO = new AddPricePolicyDetailPO();
        PojoTestUtil.TestForPojo(addPricePolicyDetailPO.getClass());
        Assert.assertEquals(addPricePolicyDetailPO.toString(),new AddPricePolicyDetailPO().toString());
    }
}
