package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.pojo.*;
import com.zte.uedm.battery.controller.batterytesttask.bo.BattTestTaskBo;
import com.zte.uedm.battery.controller.batterytesttask.bo.BattTestTaskStatusEditBo;
import com.zte.uedm.battery.controller.batterytesttask.dto.*;
import com.zte.uedm.battery.controller.batterytesttask.vo.*;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.domain.impl.BattTestTaskTemDevicesDomainImpl;
import com.zte.uedm.battery.mapper.BattTestTaskDevicesMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.SiteSpBatteryRelatedRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.function.sm.api.user.UserService;
import com.zte.uedm.function.sm.exception.AuthorityException;
import com.zte.uedm.function.sm.optional.AuthorizationStatusOptional;
import com.zte.uedm.redis.service.RedisService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class BatteryTestTaskServiceImplTest
{
    @InjectMocks
    private BatteryTestTaskServiceImpl batteryTestTaskService;
    @Mock
    private BattSohDomain battSohDomain;
    @Mock
    private BattTestTaskDomain battTestTaskDomain;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private SiteSpBatteryRelatedRpcImpl siteSpBatteryRelatedRpc;
    @Mock
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;
    @Mock
    private BackupPowerEvalDomain backupPowerEvalDomain;
    @Mock
    private BattTestRecordDomain battTestRecordDomain;
    @Mock
    private BattTestTaskTemDevicesDomainImpl battTestTaskTemDevicesDomain;
    @Mock
    private BattTestTaskDeviceDomain battTestTaskDeviceDomain;
    @Mock
    private JsonService jsonService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BatteryTestDomain batteryTestDomain;
    @Mock
    private RedisService redisService;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private UserService userService;
    @Mock
    private BattTestTaskDevicesMapper battTestTaskDevicesMapper;
    @Mock
    private BatteryTestServiceImpl batteryTestService;

    private ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("user","ip", "zh-CN");

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectAllStatusLevelsTest1() throws UedmException {
        Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
        List<IdNameBean> result = batteryTestTaskService.selectAllStatusLevels(new ServiceBaseInfoBean("1","1","2"));
        assertEquals(3,result.size());
    }

    @Test
    public void test_editTaskStatus_normal() throws Exception
    {
        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();
        dto.setId("id");
        dto.setStatus("running");

        Mockito.doReturn(new BattTestTaskStatusEditBo()).when(battTestTaskDomain).editTaskStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        assertEquals("", batteryTestTaskService.editTaskStatus(dto, serviceBean));
    }

    @Test
    public void test_editTaskStatus_exception() throws Exception
    {
        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();
        dto.setId("id");
        dto.setStatus("running");

        Mockito.doThrow(new UedmException(-1, "")).when(battTestTaskDomain).editTaskStatus(Mockito.anyString(), Mockito.anyString(), Mockito.any());
        try {
            batteryTestTaskService.editTaskStatus(dto, serviceBean);
        } catch (UedmException e) {
            assertEquals(new Integer(-1), e.getErrorId());
        }
    }

    @Test
    public void test_editTask_normal() throws Exception
    {
        TaskEditRequestDto dto = new TaskEditRequestDto();
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("user","ip","zh-CN");

        BattTestTaskBo bo = new BattTestTaskBo();
        bo.setId("id-1");
        Mockito.doReturn(bo).when(battTestTaskDomain).editTask(Mockito.any(),Mockito.anyString());

        assertEquals("id-1", batteryTestTaskService.editTask(dto, serviceBean).getLeft());
    }

    @Test
    public void test_editTask_exception() throws Exception
    {
        TaskEditRequestDto dto = new TaskEditRequestDto();
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("user","ip","zh-CN");

        Mockito.doThrow(new UedmException(-1,"")).when(battTestTaskDomain).editTask(Mockito.any(),Mockito.anyString());

        try {
            batteryTestTaskService.editTask(dto, serviceBean).getLeft();
        } catch (UedmException e) {
            assertEquals(new Integer(-1), e.getErrorId());
        }
    }

    @Test
    public void test_addTask_normal() throws Exception
    {
        TaskEditRequestDto dto = new TaskEditRequestDto();
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("user","ip","zh-CN");

        Mockito.doReturn(Lists.newArrayList("11")).when(battTestTaskDomain).addTask(Mockito.any(),Mockito.anyString());

        assertEquals(new Integer (1) , batteryTestTaskService.addTask(dto, serviceBean));
    }

    @Test
    public void test_addTask_exception() throws Exception
    {
        TaskEditRequestDto dto = new TaskEditRequestDto();
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("user","ip","zh-CN");

        Mockito.doThrow(new UedmException(-1,"")).when(battTestTaskDomain).addTask(Mockito.any(),Mockito.anyString());
        try
        {
            batteryTestTaskService.addTask(dto, serviceBean);
        } catch (UedmException e)
        {
            assertEquals(new Integer(-1), e.getErrorId());
        }
    }

    @Test
    public void test_selectTestTaskByCondition_normal() throws Exception
    {
        TaskQueryRequestDto dto = new TaskQueryRequestDto();
        dto.setSort("asc");
        dto.setOrder("name");
        BattTestTaskPo battTestTaskPo = new BattTestTaskPo();
        battTestTaskPo.setId("11");
        battTestTaskPo.setStatus("running");
        Mockito.doReturn(Lists.newArrayList(battTestTaskPo)).when(battTestTaskDomain).selectTestTaskByCondition(Mockito.any());
        try {
            Integer total = batteryTestTaskService.selectTestTaskByCondition(dto, serviceBean).getLeft();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void test_selectTestTaskByCondition() throws Exception
    {
        TaskQueryRequestDto dto = new TaskQueryRequestDto();
        dto.setSort("asc");
        dto.setOrder("name");
        dto.setPageNo(1);
        dto.setPageSize(2);
        BattTestTaskPo battTestTaskPo = new BattTestTaskPo();
        battTestTaskPo.setId("11");
        battTestTaskPo.setStatus("running");
        BattTestTaskDeviceBean battTestTaskDeviceBean = new BattTestTaskDeviceBean();
        battTestTaskDeviceBean.setId("id");
        battTestTaskDeviceBean.setTaskId("taskId");
        List<BattTestTaskDeviceBean> battTestTaskDeviceBeans = Arrays.asList(battTestTaskDeviceBean);
        Mockito.doReturn(battTestTaskDeviceBeans).when(battTestTaskDeviceDomain).selectTaskByIds(Mockito.any());

        Mockito.doReturn(Lists.newArrayList(battTestTaskPo)).when(battTestTaskDomain).selectTestTaskByCondition(Mockito.any());
        try {
            Integer total = batteryTestTaskService.selectTestTaskByCondition(dto, serviceBean).getLeft();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }


    @Test
    public void selectByIdTest() throws UedmException
    {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        battTestTaskBo.setStatus("aa");
        Mockito.doReturn(battTestTaskBo).when(battTestTaskDomain).selectById(Mockito.any());
        Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add("a");
        Mockito.doReturn(1).when(battTestTaskDomain).insertTemByTaskId(Mockito.any(),Mockito.any());
        BattTestTaskVo result = batteryTestTaskService.selectById("aa",serviceBean);
        assertEquals("1", result.getStatus().getName());
    }

    @Test
    public void  test()
    {
        Date date =new Date();

        System.out.println(date);
    }

    @Test
    public void selectByIdExceptionTest() throws UedmException
    {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        battTestTaskBo.setStatus("aa");
        Mockito.doThrow(new UedmException(-1,"")).when(battTestTaskDomain).selectById(Mockito.any());
        try {
            BattTestTaskVo result = batteryTestTaskService.selectById("aa",serviceBean);
        } catch (UedmException e)
        {
            assertEquals(new Integer(-200), e.getErrorId());
        }
    }

    @Test
    public void selectByCondition() throws Exception
    {
        try
        {
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("2"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            Mockito.doReturn(new ArrayList<>()).when(battTestTaskDeviceDomain).selectAll();
            List<BattHealthStatusBean> battHealthStatusBeans=new ArrayList<>();
            BattHealthStatusBean battHealthStatusBean = new BattHealthStatusBean();
            battHealthStatusBean.setId("1");
            battHealthStatusBean.setName("1");
            battHealthStatusBeans.add(battHealthStatusBean);
            Mockito.doReturn(battHealthStatusBeans).when(battSohDomain).selectAllBattHealthLevels();
            Set<String> battPackIds = new HashSet<>();
            battPackIds.add("batt");

            Mockito.doReturn(battPackIds).when(batteryTestService).getCacheBattPackid(Mockito.any());
            DeviceSelectedVo deviceSelectedVo = batteryTestTaskService.selectByCondition(deviceSelectedDto, baseInfoBean);
        }
        catch (UedmException | AuthorityException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void selectByCondition1() throws Exception
    {
        try
        {
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setBackupPowerStatus(Arrays.asList("1"));
            deviceSelectedDto.setTestStatus(Arrays.asList("1"));
            deviceSelectedDto.setHealthStatus(Arrays.asList("unEvaluate"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

            List<BattHealthStatusBean> battHealthStatusBeans=new ArrayList<>();
            BattHealthStatusBean battHealthStatusBean = new BattHealthStatusBean();
            battHealthStatusBean.setId("unEvaluate");
            battHealthStatusBean.setName("1");
            battHealthStatusBeans.add(battHealthStatusBean);
            Mockito.doReturn(battHealthStatusBeans).when(battSohDomain).selectAllBattHealthLevels();

            List<BattTestTaskDevicesPo> allTestTaskDevicesPos =new ArrayList<>();
            BattTestTaskDevicesPo battTestTaskDevicesPo = new BattTestTaskDevicesPo();
            battTestTaskDevicesPo.setId("1");
            battTestTaskDevicesPo.setTaskId("1");
            allTestTaskDevicesPos.add(battTestTaskDevicesPo);
            BattTestTaskDevicesPo battTestTaskDevicesPo1 = new BattTestTaskDevicesPo();
            battTestTaskDevicesPo1.setId("1");
            battTestTaskDevicesPo1.setTaskId("1");
            allTestTaskDevicesPos.add(battTestTaskDevicesPo1);
            Mockito.doReturn(allTestTaskDevicesPos).when(battTestTaskDeviceDomain).selectAll();

            List<PathInfoBean> pathInfoBeans=new ArrayList<>();
            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setId("1");
            pathInfoBean.setNamePath("1/2");
            pathInfoBeans.add(pathInfoBean);
            Mockito.doReturn(Pair.of(pathInfoBeans,1)).when(configurationManagerRpc).getMoByLogIdAndName(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());

            List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos=new ArrayList<>();
            BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
            powerEvalPojo.setId("1");
            powerEvalPojo.setName("1");
            powerEvalPojo.setStatus("1");
            battBackupPowerEvalTrendPojos.add(powerEvalPojo);
            Mockito.doReturn(new PageInfo<>(battBackupPowerEvalTrendPojos)).when(backupPowerEvalDomain).selectEvalDByMoIds(Mockito.any(),Mockito.any());

            List<BattHealthStatusEvalPo> battHealthStatusEvalPos=new ArrayList<>();
            BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
            battHealthStatusEvalPo.setStatusId("1");
            battHealthStatusEvalPo.setId("1");
            battHealthStatusEvalPos.add(battHealthStatusEvalPo);
            Mockito.doReturn(battHealthStatusEvalPos).when(battHealthStatusEvalDomain).selectLastByMoIds(Mockito.any());


            List<BattTestRecordPojo> battTestRecordPojos = new ArrayList<>();
            BattTestRecordPojo battTestRecordPojo = new BattTestRecordPojo();
            battTestRecordPojo.setId("1");
            battTestRecordPojo.setDeviceId("1");
            battTestRecordPojo.setTestStatus("1");
            BattTestRecordPojo battTestRecordPojo1= new BattTestRecordPojo();
            battTestRecordPojo1.setDeviceId("2");
            battTestRecordPojos.add(battTestRecordPojo);

            PageInfo<BattTestRecordPojo> devicePage=new PageInfo<>(battTestRecordPojos);
            Mockito.doReturn(devicePage).when(battTestRecordDomain).selectByDeviceIds(Mockito.any(),Mockito.any());

            List<MonitorObjectBean> battInfosByLogic=new ArrayList<>();

            MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
            monitorObjectBean.setId("1");
            monitorObjectBean.setName("1");
            monitorObjectBean.setNamePath("1/2");
            battInfosByLogic.add(monitorObjectBean);
            MonitorObjectBean monitorObjectBean2 = new MonitorObjectBean();
            monitorObjectBean2.setId("2");
            monitorObjectBean2.setName("2");
            monitorObjectBean2.setNamePath("2/3");
            battInfosByLogic.add(monitorObjectBean2);
            Mockito.doReturn(battInfosByLogic).when(siteSpBatteryRelatedRpc).getMonitorObjectList(Mockito.any());
            Set<String> battPackIds = new HashSet<>();
            battPackIds.add("batt");

            Mockito.doReturn(battPackIds).when(batteryTestService).getCacheBattPackid(Mockito.any());
            DeviceSelectedVo deviceSelectedVo = batteryTestTaskService.selectByCondition(deviceSelectedDto, baseInfoBean);
            assertEquals("0",String.valueOf(deviceSelectedVo.getDevices().size()));
        }
        catch (UedmException | AuthorityException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }



    @Test
    public void selectById() throws UedmException
    {
        try
        {
            DeviceDetailDto detailDto=new DeviceDetailDto();
            detailDto.setTaskId("1");
            detailDto.setSort("statusId");
            detailDto.setSort("desc");
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("2"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

            List<BattTestTaskDevicesPo> battTestPeriodicTaskTemDevicesPojos=new ArrayList<>();
            BattTestTaskDevicesPo battTestTaskTemDevicesPojo = new BattTestTaskDevicesPo();
            battTestTaskTemDevicesPojo.setId("1");
            battTestTaskTemDevicesPojo.setTaskId("1");
            battTestTaskTemDevicesPojo.setNeedRetry(false);
            battTestPeriodicTaskTemDevicesPojos.add(battTestTaskTemDevicesPojo);
            Mockito.doReturn(new PageInfo<>(battTestPeriodicTaskTemDevicesPojos)).when(battTestTaskDeviceDomain).selectByTaskId(Mockito.any(),Mockito.any());

            List<PathInfoBean> pathByIdList=new ArrayList<>();
            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setIdPath("1/2");
            pathInfoBean.setId("1");
            pathInfoBean.setNamePath("1/2");
            pathByIdList.add(pathInfoBean);
            Mockito.doReturn(pathByIdList).when(configurationManagerRpc).getPathByIdList(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            BattTestRecordPojo battTestRecordPojo=new BattTestRecordPojo();
            battTestRecordPojo.setId("1");
            battTestRecordPojo.setTestTimeStart("2022-12-12 12:12:12");
            battTestRecordPojo.setTestTimeEnd("2022-12-12 12:12:12");
            battTestRecordPojo.setCauseTime("2022-12-12 12:12:12");
            battTestRecordPojo.setTestStatus("test_exit");
            Mockito.doReturn(new PageInfo<>(Arrays.asList(battTestRecordPojo))).when(battTestRecordDomain).selectByDeviceIds(Mockito.any(),Mockito.any());

            List<BattHealthStatusBean> battHealthStatusBeans=new ArrayList<>();
            BattHealthStatusBean battHealthStatusBean = new BattHealthStatusBean();
            battHealthStatusBean.setId("1");
            battHealthStatusBean.setName("1");
            battHealthStatusBeans.add(battHealthStatusBean);
            Mockito.doReturn(battHealthStatusBeans).when(battSohDomain).selectAllBattHealthLevels();
            Pair<GetTaskRelationDeviceVo, Integer> getTaskRelationDeviceVoIntegerPair = batteryTestTaskService.selectByIdAndStatus(detailDto, baseInfoBean);
            assertEquals("1",String.valueOf(getTaskRelationDeviceVoIntegerPair.getRight()));
        }
        catch (UedmException | ParseException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void selectById1() throws UedmException
    {
        try
        {
            DeviceDetailDto detailDto=new DeviceDetailDto();
            detailDto.setTaskId("1");
            detailDto.setSort("statusId");
            detailDto.setSort("desc");
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("1"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

            List<BattTestTaskDevicesPo> battTestPeriodicTaskTemDevicesPojos=new ArrayList<>();
            BattTestTaskDevicesPo battTestTaskTemDevicesPojo = new BattTestTaskDevicesPo();
            battTestTaskTemDevicesPojo.setId("1");
            battTestTaskTemDevicesPojo.setTaskId("1");
            battTestTaskTemDevicesPojo.setNeedRetry(false);
            battTestPeriodicTaskTemDevicesPojos.add(battTestTaskTemDevicesPojo);
            Mockito.doReturn(new PageInfo<>(battTestPeriodicTaskTemDevicesPojos)).when(battTestTaskDeviceDomain).selectByTaskId(Mockito.any(),Mockito.any());

            List<PathInfoBean> pathByIdList=new ArrayList<>();
            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setIdPath("1/2");
            pathInfoBean.setId("1");
            pathInfoBean.setNamePath("1/2");
            pathByIdList.add(pathInfoBean);
            Mockito.doReturn(pathByIdList).when(configurationManagerRpc).getPathByIdList(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            BattTestTaskBo battTestTaskPojo=new BattTestTaskBo();
            battTestTaskPojo.setId("1");
            battTestTaskPojo.setPeriod(1);
            battTestTaskPojo.setStartTime("2022-08-12 12:12:12");
            Mockito.doReturn(battTestTaskPojo).when(battTestTaskDomain).selectById(Mockito.any());

            List<BattTestRecordPojo> battTestRecordPojos = new ArrayList<>();
            BattTestRecordPojo battTestRecordPojo=new BattTestRecordPojo();
            battTestRecordPojo.setId("1");
            battTestRecordPojo.setTestTimeStart("2022-12-12 12:12:12");
            battTestRecordPojo.setTestTimeEnd("2022-12-12 12:12:12");
            battTestRecordPojo.setCauseTime("2022-12-12 12:12:12");
            battTestRecordPojo.setTestStatus("test_end");
            battTestRecordPojos.add(battTestRecordPojo);
            PageInfo<BattTestRecordPojo> battTestRecordPojoPage=new PageInfo<>(battTestRecordPojos);
            Mockito.doReturn(battTestRecordPojoPage).when(battTestRecordDomain).selectByDeviceIds(Mockito.any(),Mockito.any());

            Pair<GetTaskRelationDeviceVo, Integer> getTaskRelationDeviceVoIntegerPair = batteryTestTaskService.selectByIdAndStatus(detailDto, baseInfoBean);
            assertEquals("1",String.valueOf(getTaskRelationDeviceVoIntegerPair.getRight()));
        }
        catch (UedmException | ParseException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }


    @Test
    public void selectById2() throws UedmException
    {
        try
        {
            DeviceDetailDto detailDto=new DeviceDetailDto();
            detailDto.setTaskId("1");
            detailDto.setSort("statusId");
            detailDto.setSort("desc");
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("1"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

            List<BattTestTaskDevicesPo> battTestPeriodicTaskTemDevicesPojos=new ArrayList<>();
            Mockito.doReturn(new PageInfo<>(battTestPeriodicTaskTemDevicesPojos)).when(battTestTaskDeviceDomain).selectByTaskId(Mockito.any(),Mockito.any());

            List<PathInfoBean> pathByIdList=new ArrayList<>();
            Mockito.doReturn(pathByIdList).when(configurationManagerRpc).getPathByIdList(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            BattTestTaskBo battTestTaskPojo=new BattTestTaskBo();
            battTestTaskPojo.setId("1");
            battTestTaskPojo.setPeriod(1);
            battTestTaskPojo.setStartTime("2022-12-12 12:12:12");
            Mockito.doReturn(battTestTaskPojo).when(battTestTaskDomain).selectById(Mockito.any());

            Pair<GetTaskRelationDeviceVo, Integer> getTaskRelationDeviceVoIntegerPair = batteryTestTaskService.selectByIdAndStatus(detailDto, baseInfoBean);
            assertEquals("0",String.valueOf(getTaskRelationDeviceVoIntegerPair.getRight()));
        }
        catch (UedmException | ParseException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }



    @Test
    public void selectTemByTaskId() throws UedmException
    {
        try
        {
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("2"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            List<BattTestTaskTemDevicesPo> battTestPeriodicTaskTemDevicesPojos=new ArrayList<>();

            Mockito.doReturn(new PageInfo<>(battTestPeriodicTaskTemDevicesPojos)).when(battTestTaskTemDevicesDomain).selectByTaskId(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos=new ArrayList<>();
            Mockito.doReturn(new PageInfo<>(battBackupPowerEvalTrendPojos)).when(backupPowerEvalDomain).selectEvalDByMoIds(Mockito.any(),Mockito.any());
            List<BattTestRecordPojo> battTestRecordPojos = new ArrayList<>();
            PageInfo<BattTestRecordPojo> devicePage=new PageInfo<>(battTestRecordPojos);
            Mockito.doReturn(devicePage).when(battTestRecordDomain).selectByDeviceIds(Mockito.any(),Mockito.any());
            batteryTestTaskService.selectTemByTaskId("deviceSelectedDto", baseInfoBean);
        }
        catch (UedmException | AuthorityException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void selectShowDetailByIdTest() throws UedmException
    {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        battTestTaskBo.setStatus("aa");
        Mockito.doReturn(battTestTaskBo).when(battTestTaskDomain).selectById(Mockito.any());
        Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());

        BattTestTaskVo result = batteryTestTaskService.selectShowDetailById("aa",serviceBean);
        assertEquals("1", result.getStatus().getName());
    }

    @Test
    public void selectShowDetailByIdExceptionTest() throws UedmException
    {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        battTestTaskBo.setStatus("aa");
        Mockito.doThrow(new UedmException(-1,"")).when(battTestTaskDomain).selectById(Mockito.any());
        try {
            BattTestTaskVo result = batteryTestTaskService.selectShowDetailById("aa",serviceBean);
        } catch (UedmException e)
        {
            assertEquals(new Integer(-200), e.getErrorId());
        }
    }

    @Test
    public void startBatteryTemporaryTest_normal() throws UedmException
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
        Map<Integer, String> result = new HashMap<>();
        result.put(0, "success");
        Mockito.doReturn(result).when(batteryTestDomain).startBatteryTest(Mockito.any(), Mockito.any());
        BatteryTestRetryVo vo = new BatteryTestRetryVo();
        vo.setTaskId("task-1");
        vo.setDeviceIds(Arrays.asList("sp-1"));
        MonitorObjectBean sp = new MonitorObjectBean();
        sp.setId("sp-1");
        sp.setName("sp");
        Mockito.doReturn(Arrays.asList(sp)).when(siteSpBatteryRelatedRpc).getMonitorObjectList(Mockito.any());
        List<String> result1 = batteryTestTaskService.retryBatteryTest(vo, serviceBean);
        assertEquals(1l, result1.size());
    }

    @Test
    public void startBatteryTemporaryTest_Empty() throws UedmException {
        try {
            BatteryTestRetryVo vo = new BatteryTestRetryVo();
            vo.setTaskId("task-1");
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
            List<String> result1 = batteryTestTaskService.retryBatteryTest(vo, serviceBean);
        } catch (UedmException e) {
            assertEquals("params is blank.", e.getMessage());
        }
    }
    @Test
    public void selectTemByTaskId1() throws UedmException
    {
        try
        {
            Mockito.doReturn("1").when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
            deviceSelectedDto.setHealthStatus(Arrays.asList("1"));
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");

            List<BattTestTaskTemDevicesPo> battTestPeriodicTaskTemDevicesPojos=new ArrayList<>();
            BattTestTaskTemDevicesPo battTestTaskTemDevicesPojo = new BattTestTaskTemDevicesPo();
            battTestTaskTemDevicesPojo.setId("1");
            battTestTaskTemDevicesPojo.setTaskId("1");
            battTestPeriodicTaskTemDevicesPojos.add(battTestTaskTemDevicesPojo);
            Mockito.doReturn(new PageInfo<>(battTestPeriodicTaskTemDevicesPojos)).when(battTestTaskTemDevicesDomain).selectByTaskId(Mockito.any(),Mockito.any());
            Map<String, List<String>> battMap = new HashMap<>();
            battMap.put("1",Arrays.asList("1"));
            Mockito.doReturn(battMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            List<PathInfoBean> pathInfoBeans=new ArrayList<>();
            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setId("1");
            pathInfoBean.setNamePath("1/2");
            pathInfoBeans.add(pathInfoBean);
            Mockito.doReturn(Pair.of(pathInfoBeans,1)).when(configurationManagerRpc).getMoByLogIdAndName(Mockito.any(),Mockito.any());

            List<BattBackupPowerEvalPojo> battBackupPowerEvalTrendPojos=new ArrayList<>();
            BattBackupPowerEvalPojo powerEvalPojo = new BattBackupPowerEvalPojo();
            powerEvalPojo.setId("1");
            powerEvalPojo.setName("1");
            powerEvalPojo.setStatus("1");
            battBackupPowerEvalTrendPojos.add(powerEvalPojo);
            Mockito.doReturn(new PageInfo<>(battBackupPowerEvalTrendPojos)).when(backupPowerEvalDomain).selectEvalDByMoIds(Mockito.any(),Mockito.any());

            List<BattHealthStatusEvalPo> battHealthStatusEvalPos=new ArrayList<>();
            BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
            battHealthStatusEvalPo.setStatusId("1");
            battHealthStatusEvalPo.setId("1");
            battHealthStatusEvalPos.add(battHealthStatusEvalPo);
            Mockito.doReturn(battHealthStatusEvalPos).when(battHealthStatusEvalDomain).selectLastByMoIds(Mockito.any());

            List<BattTestRecordPojo> battTestRecordPojos = new ArrayList<>();
            BattTestRecordPojo battTestRecordPojo = new BattTestRecordPojo();
            battTestRecordPojo.setId("1");
            battTestRecordPojo.setTestStatus("1");
            battTestRecordPojos.add(battTestRecordPojo);
            PageInfo<BattTestRecordPojo> devicePage=new PageInfo<>(battTestRecordPojos);
            Mockito.doReturn(devicePage).when(battTestRecordDomain).selectByDeviceIds(Mockito.any(),Mockito.any());

            PageInfo<TemDeviceSelectVo> deviceSelectedDto1 = batteryTestTaskService.selectTemByTaskId("deviceSelectedDto", baseInfoBean);
            assertEquals("1",String.valueOf(deviceSelectedDto1.getTotal()));
        }
        catch (UedmException | AuthorityException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void deleteTemDevice() throws UedmException
    {
        try
        {
            DeleteTemDeviceDto deleteTemDeviceDto=new DeleteTemDeviceDto();
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).deleteTemDevice(Mockito.any(),Mockito.any());
            int total = batteryTestTaskService.deleteTemDevice(deleteTemDeviceDto,new ServiceBaseInfoBean("1","2","3"));
            assertEquals("1",String.valueOf(total));
        }
        catch (UedmException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void saveTemDevice() throws UedmException
    {
        try
        {
            DeleteTemDeviceSaveDto deleteTemDeviceDto=new DeleteTemDeviceSaveDto();
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).saveTemDevice(Mockito.any());
            BattTestTaskTemDevicesPo battTestTaskTemDevicesPo = new BattTestTaskTemDevicesPo();
            battTestTaskTemDevicesPo.setId("1");
            PageInfo<BattTestTaskTemDevicesPo> battTestTaskTemDevicesPoPageInfo=new PageInfo<>(Arrays.asList(battTestTaskTemDevicesPo));
            Mockito.doReturn(battTestTaskTemDevicesPoPageInfo).when(battTestTaskTemDevicesDomain).selectByTaskId(Mockito.any(),Mockito.any());
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).saveTemDevice(Mockito.any());
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).deleteTemDevice(Mockito.any(),Mockito.any());
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            int total = batteryTestTaskService.saveTemDevice(deleteTemDeviceDto,baseInfoBean);
            assertEquals("1",String.valueOf(total));
        }
        catch (UedmException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void startBatteryTemporaryTest_Empty2() throws UedmException
    {
        try
        {
            BatteryTestRetryVo vo = new BatteryTestRetryVo();
            vo.setDeviceIds(Arrays.asList("sp-1"));
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
            List<String> result1 = batteryTestTaskService.retryBatteryTest(vo, serviceBean);
        }
        catch (UedmException e)
        {
            assertEquals("params is blank.", e.getMessage());
        }
    }
    public void saveTemDevice1() throws UedmException
    {
        try
        {
            DeleteTemDeviceSaveDto deleteTemDeviceDto=new DeleteTemDeviceSaveDto();
            deleteTemDeviceDto.setDeviceIds(Arrays.asList("1"));
            BattTestTaskTemDevicesPo battTestTaskTemDevicesPo = new BattTestTaskTemDevicesPo();
            battTestTaskTemDevicesPo.setId("1");
            PageInfo<BattTestTaskTemDevicesPo> battTestTaskTemDevicesPoPageInfo=new PageInfo<>(Arrays.asList(battTestTaskTemDevicesPo));
            Mockito.doReturn(battTestTaskTemDevicesPoPageInfo).when(battTestTaskTemDevicesDomain).selectByTaskId(Mockito.any(),Mockito.any());
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).saveTemDevice(Mockito.any());
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).saveTemDevice(Mockito.any());
            int total = batteryTestTaskService.saveTemDevice(deleteTemDeviceDto,baseInfoBean);
            assertEquals("1",String.valueOf(total));
        }
        catch (UedmException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void startBatteryTemporaryTest_Ex()
    {
        try
        {
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("admin", "zh-CN");
            Map<Integer, String> result = new HashMap<>();
            result.put(0, "success");
            result.put(-205, "error");
            result.put(-206, "error");
            result.put(-207, "error");
            Mockito.doReturn(result).when(batteryTestDomain).startBatteryTest(Mockito.any(), Mockito.any());
            BatteryTestRetryVo vo = new BatteryTestRetryVo();
            vo.setTaskId("task-1");
            vo.setDeviceIds(Arrays.asList("sp-1"));
            List<String> result1 = batteryTestTaskService.retryBatteryTest(vo, serviceBean);
        }
        catch (UedmException e)
        {
            assertEquals("error", e.getMessage());
        }
    }
    @Test
    public void saveTemDevice2() throws UedmException
    {
        try
        {
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            BattTestTaskTemDevicesPo battTestTaskTemDevicesPo = new BattTestTaskTemDevicesPo();
            battTestTaskTemDevicesPo.setId("1");
            PageInfo<BattTestTaskTemDevicesPo> battTestTaskTemDevicesPoPageInfo=new PageInfo<>(Arrays.asList(battTestTaskTemDevicesPo));
            Mockito.doReturn(battTestTaskTemDevicesPoPageInfo).when(battTestTaskTemDevicesDomain).selectByTaskId(Mockito.any(),Mockito.any());
            DeleteTemDeviceSaveDto deleteTemDeviceDto=new DeleteTemDeviceSaveDto();
            Mockito.doReturn(1).when(battTestTaskTemDevicesDomain).saveTemDevice(Mockito.any());
            int total = batteryTestTaskService.saveTemDevice(deleteTemDeviceDto,baseInfoBean);
            assertEquals("1",String.valueOf(total));
        }
        catch (UedmException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void searchDevicesByConditionTest() throws UedmException
    {
        try
        {
            //空测试
            DeviceSearchDto deleteTemDeviceDto=new DeviceSearchDto();
            deleteTemDeviceDto.setName("11");
            ServiceBaseInfoBean baseInfoBean = new ServiceBaseInfoBean("1", "1", "2");
            batteryTestTaskService.searchDevicesByCondition(deleteTemDeviceDto, baseInfoBean);
            //rpc空
            Mockito.doReturn(Lists.newArrayList("11")).when(battTestTaskDeviceDomain).selectAllDevicesIds();
            batteryTestTaskService.searchDevicesByCondition(deleteTemDeviceDto, baseInfoBean);

            PathInfoBean pathInfoBean = new PathInfoBean();
            pathInfoBean.setId("11");
            pathInfoBean.setNamePath("11/22/33");
            Mockito.doReturn(Lists.newArrayList(pathInfoBean)).when(configurationManagerRpc).getPathByIdList(Mockito.any(), Mockito.any());
            Pair<List<DeviceSearchDetailVo>, Integer> pair = batteryTestTaskService
                    .searchDevicesByCondition(deleteTemDeviceDto, baseInfoBean);
            assertEquals("0",String.valueOf(pair.getRight()));
        }
        catch (UedmException | AuthorityException e)
        {
            assertEquals("Parameter is not in the range of optional values: null",e.getMessage());
        }
    }

    @Test
    public void test_deleteTasks_noamrl() throws Exception
    {
        Mockito.doReturn(1).when(battTestTaskDomain).deleteTasks(Mockito.anyList());
        assertEquals(new Integer (1) , batteryTestTaskService.deleteTasks(Arrays.asList("da"), serviceBean));
    }

    @Test
    public void test_deleteTasks_exception() throws Exception
    {
        try {
            Mockito.doThrow(new UedmException(-1, "")).when(battTestTaskDomain).deleteTasks(Mockito.anyList());
            assertEquals(new Integer (1) , batteryTestTaskService.deleteTasks(Arrays.asList("da"), serviceBean));
        } catch (UedmException e) {
            assertEquals(new Integer(-1), e.getErrorId());
        }
    }

    @Test
    public void selectBattTestTaskStatisticsTest() throws UedmException
    {
        BattTestTaskStatisticDto dto = new BattTestTaskStatisticDto();
        dto.setAutoStatisticsDims(Arrays.asList("siteNumber"));
        Map<String, BattTestTaskStatisticVo> res = batteryTestTaskService.selectBattTestTaskStatistics(dto, new ServiceBaseInfoBean("1","1"));
        assertEquals(0, res.get("siteNumber").getNumber().intValue());
    }

    @Test
    public void selectBattTestTaskStatisticsTest1() throws UedmException
    {
        BattTestTaskStatisticDto dto = new BattTestTaskStatisticDto();
        dto.setAutoStatisticsDims(Arrays.asList("switchPowerNumber"));
        when(battTestTaskDomain.selectSpRunningByCondition(Mockito.any(), Mockito.anyString())).thenReturn(Arrays.asList("a"));
        Map<String, BattTestTaskStatisticVo> res = batteryTestTaskService.selectBattTestTaskStatistics(dto, new ServiceBaseInfoBean("1","1"));
        assertEquals(1, res.get("switchPowerNumber").getNumber().intValue());
    }

    /* Started by AICoder, pid:db0c33eb94v53bd144c90b1e22af1f6e21b0c2d4 */
    @Test
    public void testExtracted() throws AuthorityException {
        // 构造测试数据
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("System", "127.0.0.1", "");
        List<IdNameBean> selectedDevices = new ArrayList<>();
        IdNameBean device1 = new IdNameBean();
        device1.setId("device1");
        selectedDevices.add(device1);

        IdNameBean device2 = new IdNameBean();
        device2.setId("device2");
        selectedDevices.add(device2);

        List<DeviceSelectedInfoVo> deviceSelectedInfoVos = new ArrayList<>();
        DeviceSelectedInfoVo device3 = new DeviceSelectedInfoVo();
        device3.setId("device3");
        deviceSelectedInfoVos.add(device3);

        DeviceSelectedInfoVo device4 = new DeviceSelectedInfoVo();
        device4.setId("device4");
        deviceSelectedInfoVos.add(device4);

        List<DeviceEntity> deviceEntities = new ArrayList<>();
        DeviceEntity entity1 = new DeviceEntity();
        entity1.setId("device1");
        entity1.setAuthorizedRoles(Arrays.asList("role1"));
        entity1.setAuthorizedAllRoles(Arrays.asList("role1"));
        deviceEntities.add(entity1);

        DeviceEntity entity2 = new DeviceEntity();
        entity2.setId("device2");
        entity2.setAuthorizedRoles(Arrays.asList("role2"));
        entity2.setAuthorizedAllRoles(Arrays.asList("role2"));
        deviceEntities.add(entity2);

        DeviceEntity entity3 = new DeviceEntity();
        entity3.setId("device3");
        entity3.setAuthorizedRoles(Arrays.asList("role3"));
        entity3.setAuthorizedAllRoles(Arrays.asList("role3"));
        deviceEntities.add(entity3);

        // Mock deviceCacheManager
        List<String> selectedDeviceIds = Stream.concat(
                selectedDevices.stream().map(IdNameBean::getId),
                deviceSelectedInfoVos.stream().map(DeviceSelectedInfoVo::getId)
        ).distinct().collect(Collectors.toList());

        when(deviceCacheManager.getDeviceListByIds(selectedDeviceIds)).thenReturn(deviceEntities);

        // Mock userService
        AuthorizationStatusOptional mockStatus1 = mock(AuthorizationStatusOptional.class);
        when(mockStatus1.getId()).thenReturn(2);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role1")), eq(Arrays.asList("role1"))))
                .thenReturn(mockStatus1); // Authorized

        AuthorizationStatusOptional mockStatus2 = mock(AuthorizationStatusOptional.class);
        when(mockStatus2.getId()).thenReturn(1);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role2")), eq(Arrays.asList("role2"))))
                .thenReturn(mockStatus2); // Not authorized

        AuthorizationStatusOptional mockStatus3 = mock(AuthorizationStatusOptional.class);
        when(mockStatus3.getId()).thenReturn(2);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role3")), eq(Arrays.asList("role3"))))
                .thenReturn(mockStatus3); // Authorized

        // Call the method under test
        batteryTestTaskService.extracted(serviceBaseInfoBean, selectedDevices, deviceSelectedInfoVos);

        // Verify the result
        assertEquals(1, selectedDevices.size());
        assertEquals("device1", selectedDevices.get(0).getId());

        assertEquals(1, deviceSelectedInfoVos.size());
        assertEquals("device3", deviceSelectedInfoVos.get(0).getId());

        // Verify interactions
        verify(deviceCacheManager, times(1)).getDeviceListByIds(selectedDeviceIds);
        verify(userService, times(3)).getAuthorizationStatus(anyString(), anyList(), anyList());
    }

    @Test
    public void testExtracted2() throws AuthorityException {
        // 构造测试数据
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("System", "127.0.0.1", "");
        List<TemDeviceSelectVo> result = new ArrayList<>();
        TemDeviceSelectVo device1 = new TemDeviceSelectVo();
        device1.setId("device1");
        result.add(device1);

        TemDeviceSelectVo device2 = new TemDeviceSelectVo();
        device2.setId("device2");
        result.add(device2);

        TemDeviceSelectVo device3 = new TemDeviceSelectVo();
        device3.setId("device3");
        result.add(device3);

        TemDeviceSelectVo device4 = new TemDeviceSelectVo();
        device4.setId("device4"); // Corrected from device3 to device4
        result.add(device4);

        List<DeviceEntity> deviceEntities = new ArrayList<>();
        DeviceEntity entity1 = new DeviceEntity();
        entity1.setId("device1");
        entity1.setAuthorizedRoles(Arrays.asList("role1"));
        entity1.setAuthorizedAllRoles(Arrays.asList("role1"));
        deviceEntities.add(entity1);

        DeviceEntity entity2 = new DeviceEntity();
        entity2.setId("device2");
        entity2.setAuthorizedRoles(Arrays.asList("role2"));
        entity2.setAuthorizedAllRoles(Arrays.asList("role2"));
        deviceEntities.add(entity2);

        DeviceEntity entity3 = new DeviceEntity();
        entity3.setId("device3");
        entity3.setAuthorizedRoles(Arrays.asList("role3"));
        entity3.setAuthorizedAllRoles(Arrays.asList("role3"));
        deviceEntities.add(entity3);

        // Mock deviceCacheManager
        List<String> deviceIds = result.stream()
                .map(TemDeviceSelectVo::getId)
                .collect(Collectors.toList());

        when(deviceCacheManager.getDeviceListByIds(deviceIds)).thenReturn(deviceEntities);

        // Mock userService
        AuthorizationStatusOptional mockStatus1 = mock(AuthorizationStatusOptional.class);
        when(mockStatus1.getId()).thenReturn(2);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role1")), eq(Arrays.asList("role1"))))
                .thenReturn(mockStatus1); // Authorized

        AuthorizationStatusOptional mockStatus2 = mock(AuthorizationStatusOptional.class);
        when(mockStatus2.getId()).thenReturn(1);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role2")), eq(Arrays.asList("role2"))))
                .thenReturn(mockStatus2); // Not authorized

        AuthorizationStatusOptional mockStatus3 = mock(AuthorizationStatusOptional.class);
        when(mockStatus3.getId()).thenReturn(2);
        when(userService.getAuthorizationStatus(eq("System"), eq(Arrays.asList("role3")), eq(Arrays.asList("role3"))))
                .thenReturn(mockStatus3); // Authorized

        // Call the method under test
        batteryTestTaskService.extracted(serviceBaseInfoBean, result);

        // Verify the result
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(device -> device.getId().equals("device1")));

        // Verify interactions
        verify(deviceCacheManager, times(1)).getDeviceListByIds(deviceIds);
    }

    @Test
    public void testGetBattTestTaskPos_withValidData() throws Exception {
        // 准备测试数据
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("System", "127.0.0.1", "");
        // 构造 TaskQueryRequestDto 对象
        TaskQueryRequestDto taskQueryRequestDto = new TaskQueryRequestDto();

        // 创建 BattTestTaskPo 列表
        List<BattTestTaskPo> battTestTaskPos = new ArrayList<>();
        BattTestTaskPo battTestTaskPo = new BattTestTaskPo();
        battTestTaskPo.setId("task1");
        battTestTaskPos.add(battTestTaskPo);

        // 模拟查询任务
        when(battTestTaskDomain.selectTestTaskByCondition(taskQueryRequestDto)).thenReturn(battTestTaskPos);

        // 模拟任务关联设备 ID
        when(battTestTaskDevicesMapper.selectIdByTaskId("task1")).thenReturn(Arrays.asList("device1", "device2"));

        // 创建 DeviceEntity 列表
        List<DeviceEntity> deviceList = new ArrayList<>();
        DeviceEntity device1 = new DeviceEntity();
        device1.setId("device1");
        device1.setAuthorizedRoles(Arrays.asList("admin"));
        device1.setAuthorizedAllRoles(Arrays.asList("user"));
        deviceList.add(device1);

        DeviceEntity device2 = new DeviceEntity();
        device2.setId("device2");
        device2.setAuthorizedRoles(Arrays.asList("user"));
        device2.setAuthorizedAllRoles(Arrays.asList("admin"));
        deviceList.add(device2);

        // 模拟批量查询设备信息
        when(deviceCacheManager.getDeviceListByIds(Arrays.asList("device1", "device2"))).thenReturn(deviceList);

        // 模拟用户授权状态
        AuthorizationStatusOptional authorizationStatus = mock(AuthorizationStatusOptional.class);
        when(authorizationStatus.getId()).thenReturn(2); // 假设 2 代表授权通过
        when(userService.getAuthorizationStatus(anyString(), anyList(), anyList())).thenReturn(authorizationStatus);

        // 执行方法
        List<BattTestTaskPo> result = batteryTestTaskService.getBattTestTaskPos(taskQueryRequestDto, serviceBaseInfoBean);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getId());

        // 验证调用
        verify(battTestTaskDomain).selectTestTaskByCondition(taskQueryRequestDto);
    }

    @Test
    public void testGetBattTestTaskPos_withNoAuthorizedRole() throws Exception {
        // 准备数据
        TaskQueryRequestDto taskQueryRequestDto = new TaskQueryRequestDto();
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("System", "127.0.0.1", "");
        // 创建 BattTestTaskPo 列表
        List<BattTestTaskPo> battTestTaskPos = new ArrayList<>();
        BattTestTaskPo battTestTaskPo = new BattTestTaskPo();
        battTestTaskPo.setId("task1");
        battTestTaskPos.add(battTestTaskPo);

        // 模拟查询任务
        when(battTestTaskDomain.selectTestTaskByCondition(taskQueryRequestDto)).thenReturn(battTestTaskPos);

        // 模拟任务关联设备 ID
        when(battTestTaskDevicesMapper.selectIdByTaskId("task1")).thenReturn(Arrays.asList("device1", "device2"));

        // 创建 DeviceEntity 列表
        List<DeviceEntity> deviceList = new ArrayList<>();
        DeviceEntity device1 = new DeviceEntity();
        device1.setId("device1");
        device1.setAuthorizedRoles(Arrays.asList("guest")); // 无授权角色
        device1.setAuthorizedAllRoles(Arrays.asList("guest"));
        deviceList.add(device1);

        DeviceEntity device2 = new DeviceEntity();
        device2.setId("device2");
        device2.setAuthorizedRoles(Arrays.asList("guest")); // 无授权角色
        device2.setAuthorizedAllRoles(Arrays.asList("guest"));
        deviceList.add(device2);

        // 模拟批量查询设备信息
        when(deviceCacheManager.getDeviceListByIds(Arrays.asList("device1", "device2"))).thenReturn(deviceList);

        // 模拟用户授权状态
        AuthorizationStatusOptional authorizationStatus = mock(AuthorizationStatusOptional.class);
        when(authorizationStatus.getId()).thenReturn(1); // 1 代表没有授权
        when(userService.getAuthorizationStatus(anyString(), anyList(), anyList())).thenReturn(authorizationStatus);

        // 执行方法
        List<BattTestTaskPo> result = batteryTestTaskService.getBattTestTaskPos(taskQueryRequestDto, serviceBaseInfoBean);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size()); // 由于没有权限，应该返回空列表

        // 验证调用
        verify(battTestTaskDomain).selectTestTaskByCondition(taskQueryRequestDto);
        verify(userService).getAuthorizationStatus(anyString(), anyList(), anyList());
    }
    /* Ended by AICoder, pid:db0c33eb94v53bd144c90b1e22af1f6e21b0c2d4 */
}
