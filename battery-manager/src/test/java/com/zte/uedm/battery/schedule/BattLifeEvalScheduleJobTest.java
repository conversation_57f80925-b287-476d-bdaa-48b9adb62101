package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.controller.battAiConfig.vo.AiConfigVo;
import com.zte.uedm.battery.opti.application.scheduler.BattRiskCalculateScheduler;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.battery.service.battlife.impl.BattLifeAIModeServiceImpl;
import com.zte.uedm.battery.service.impl.BattIronLifeEvalServiceImpl;
import com.zte.uedm.battery.service.impl.BattLeadLifeEvalServiceImpl;
import com.zte.uedm.battery.service.impl.BattUnknownLifeEvalServiceImpl;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class BattLifeEvalScheduleJobTest {

    @InjectMocks
    private BattLifeEvalScheduleJob battLifeEvalScheduleJob;

    @Mock
    private BattIronLifeEvalServiceImpl battIronLifeEvalService;

    @Mock
    private BattLeadLifeEvalServiceImpl battLeadLifeEvalService;

    @Mock
    private BattUnknownLifeEvalServiceImpl battUnknownLifeEvalService;

    @Mock
    private BattRiskCalculateScheduler battRiskCalculateScheduler;

    @Mock
    private BattLifeAIModeServiceImpl battLifeAIModeService;

    @Mock
    private BattAISwitchService battAISwitchService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectValueInfoByKeyTest() throws Exception {
        try {
            AiConfigVo aiConfigVo = new AiConfigVo();
            aiConfigVo.setFlag(false);
            when(battAISwitchService.selectConfigById(anyString(), anyString())).thenReturn(aiConfigVo);

            battLifeEvalScheduleJob.execute();
            doThrow(new UedmException(-1, "test")).when(battIronLifeEvalService).evalBattLife();
            doThrow(new UedmException(-1, "test")).when(battLeadLifeEvalService).evalBattLife();
            doThrow(new UedmException(-1, "test")).when(battUnknownLifeEvalService).evalBattLife();
            battLifeEvalScheduleJob.execute();
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-1), e.getErrorId());
        }
    }

    @Test
    public void selectValueInfoByKeyTest1() throws Exception {
        try {
            AiConfigVo aiConfigVo = new AiConfigVo();
            aiConfigVo.setFlag(true);
            aiConfigVo.setValue("ON");
            when(battAISwitchService.selectConfigById(anyString(), anyString())).thenReturn(aiConfigVo);
            battLifeEvalScheduleJob.execute();
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-1), e.getErrorId());
        }
    }
}
