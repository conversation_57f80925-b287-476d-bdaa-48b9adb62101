package com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity;

/* Started by AICoder, pid:5ace268042424b92abcb4561132c93c2 */
import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Test;
import static org.junit.Assert.*;

public class StrategyCombinationEntityTest {
    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(StrategyCombinationEntity.class);
    }
}
/* Ended by AICoder, pid:5ace268042424b92abcb4561132c93c2 */
