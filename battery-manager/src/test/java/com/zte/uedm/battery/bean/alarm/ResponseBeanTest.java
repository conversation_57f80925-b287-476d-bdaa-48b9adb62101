package com.zte.uedm.battery.bean.alarm;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class ResponseBeanTest
{
    @SuppressWarnings("unchecked")
    @Test
    public void testSetGetToString()
    {
        ResponseBean<Alarm> res1 = new ResponseBean();
        res1.setCode(0);
        res1.setMessage("mess");
        res1.setError("err");
        res1.setData(new Alarm[] {});
        res1.setTotal(0);
        assertEquals(0, res1.getCode());
        assertEquals("mess", res1.getMessage());
        assertEquals("err", res1.getError());
        assertEquals(0, res1.getData().length);
        assertEquals(0, res1.getTotal());
        assertNotNull(res1.toString());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testConstruction()
    {
        Alarm alarm = new Alarm();
        alarm.setMe("alarm.dc");
        Alarm[] data = new Alarm[] { alarm };
        ResponseBean<Alarm> res1 = new ResponseBean(alarm);
        assertEquals(0,(int)res1.getCode());

        ResponseBean<Alarm> res2 = new ResponseBean();
        res2.setData(data);
        assertEquals(0,(int)res2.getCode());

        ResponseBean<Alarm> res3 = new ResponseBean();
        res3.setData(data);
        res3.setTotal(1);
        assertEquals(0,(int)res3.getCode());

        ResponseBean<Alarm> res4 = new ResponseBean(-1, "mess", "err");
        assertEquals(-1,(int)res4.getCode());

        ResponseBean<Alarm> res5 = new ResponseBean(-1, new Throwable());
        assertEquals(-1,(int)res5.getCode());
    }
    
    @SuppressWarnings("unchecked")
    @Test
    public void testConstruction2()
    {
        ResponseBean<Alarm> res2 = new ResponseBean(null);
        assertEquals(0,(int)res2.getCode());
    }
}
