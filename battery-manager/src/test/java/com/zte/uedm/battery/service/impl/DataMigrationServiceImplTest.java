package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.mapper.DataMigrationMapper;
import com.zte.uedm.battery.service.DataMigrationService;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.service.config.api.configuraiton.DeviceService;
import com.zte.uedm.service.config.api.configuraiton.vo.DeviceVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

public class DataMigrationServiceImplTest {

    private DataMigrationService dataMigrationService;
    private DataMigrationMapper dataMigrationMapper;
    private DeviceService deviceService;
    private HttpServletRequest request;
    private LogUtils logUtils;

    @BeforeEach
    void setUp() {
        dataMigrationMapper = Mockito.mock(DataMigrationMapper.class);
        deviceService = Mockito.mock(DeviceService.class);
        request = mock(HttpServletRequest.class);
        logUtils = mock(LogUtils.class);
        dataMigrationService = new DataMigrationServiceImpl();

        // 设置模拟依赖
        ((DataMigrationServiceImpl) dataMigrationService).dataMigrationMapper = dataMigrationMapper;
        ((DataMigrationServiceImpl) dataMigrationService).deviceService = deviceService;
        ((DataMigrationServiceImpl) dataMigrationService).logUtils = logUtils;
    }

    /* Started by AICoder, pid:c2a49p32daif2c3141b80955202b287553e41b09 */
    // 测试正常流程
    @Test
    void testMigrateData_HappyPath() throws Exception {
        // 准备测试数据
        List<DeviceVo> resourceList = new ArrayList<>();

        DeviceVo entity1 = new DeviceVo();
        entity1.setId("1");
        entity1.setPathId(new String[]{"path1"});
        entity1.setMoc("r32.uedm.device.battery");
        resourceList.add(entity1);

        DeviceVo entity2 = new DeviceVo();
        entity2.setId("2");
        entity2.setPathId(new String[]{"path2"});
        entity2.setMoc("r32.uedm.device.dcpower");
        resourceList.add(entity2);

        // 模拟返回数据
        when(deviceService.queryAll()).thenReturn(resourceList);

        // 调用方法
        dataMigrationService.migrateData(request);

        // 验证行为
        verify(dataMigrationMapper, times(1)).batchUpdateAllTables(anyList());
    }

    // 测试边界情况：空数据
    @Test
    void testMigrateData_EmptyList() throws Exception {
        when(deviceService.queryAll()).thenReturn(Collections.emptyList());

        dataMigrationService.migrateData(request);

        verify(dataMigrationMapper, never()).batchUpdateAllTables(anyList());
    }

    // 测试边界情况：仅有不匹配的数据
    @Test
    void testMigrateData_NoMatchingData() throws Exception {
        List<DeviceVo> resourceList = new ArrayList<>();
        DeviceVo entity = new DeviceVo();
        entity.setId("3");
        entity.setPathId(new String[]{"path3"});
        entity.setMoc("r32.uedm.device.unknown");
        resourceList.add(entity);

        when(deviceService.queryAll()).thenReturn(resourceList);

        dataMigrationService.migrateData(request);

        verify(dataMigrationMapper, never()).batchUpdateAllTables(anyList());
    }
    /* Ended by AICoder, pid:c2a49p32daif2c3141b80955202b287553e41b09 */
}

