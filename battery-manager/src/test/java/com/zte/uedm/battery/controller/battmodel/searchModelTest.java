package com.zte.uedm.battery.controller.battmodel;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.controller.battbrand.BattBrandController;
import com.zte.uedm.battery.controller.battbrand.dto.BattBrandSearchDto;
import com.zte.uedm.battery.controller.battmodel.dto.BattModelSearchDto;
import com.zte.uedm.battery.service.BattBrandService;
import com.zte.uedm.battery.service.BattModelService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

import static org.mockito.Mockito.mock;

public class searchModelTest
{

    @InjectMocks
    private BattModelController battModelController;
    @Mock
    private BattModelService battModelService;

    private HttpServletRequest req;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        req = mock(HttpServletRequest.class);
    }

    @Test
    public void searchModelTest_param_blank() throws Exception
    {
        ResponseBean re = battModelController.searchModel(null,req,"zh-CN");
        Assert.assertSame(-100,re.getCode());
    }

    @Test
    public void searchModelTest_normal() throws Exception
    {
        BattModelSearchDto dto = new BattModelSearchDto();
        dto.setName("d");

        Mockito.doReturn(new PageInfo<>(new ArrayList<>())).when(battModelService).search(Mockito.anyString(), Mockito.any());
        ResponseBean re = battModelController.searchModel(dto,req,"zh-CN");
        Assert.assertSame(0,re.getCode());
    }

    @Test
    public void searchModelTest_exception() throws Exception
    {
        BattModelSearchDto dto = new BattModelSearchDto();
        dto.setName("d");
        ResponseBean re = null;
        try {
            Mockito.doThrow(new UedmException(-1, "")).when(battModelService).search(Mockito.anyString(), Mockito.any());
            re = battModelController.searchModel(dto,req,"zh-CN");
        } catch (UedmException e) {
            Assert.assertSame(-1, e.getErrorId());
        }
    }
}
