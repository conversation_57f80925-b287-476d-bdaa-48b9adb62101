package com.zte.uedm.battery.enums.overview;

import org.junit.Assert;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class BattStatisticsPrstSocLevelsEnumTest {
    @Test
    public void testGetAll()
    {
        List<String> aaa =BattStatisticsPrstSocLevelsEnum.getAllBattStatisticsPrstSocLevelsIds();
        Assert.assertEquals(4,aaa.size());
    }

    @Test
    public void testGet()
    {
        String aaa =BattStatisticsPrstSocLevelsEnum.getNameById("1");
        Assert.assertEquals("0~30%",aaa);
    }

}