package com.zte.uedm.battery.controller.battlife;

import com.zte.uedm.battery.controller.battlife.dto.*;
import com.zte.uedm.battery.controller.battlife.po.BattLifeRatedCfgPo;
import com.zte.uedm.battery.controller.battlife.po.BattLifeSpecificCfgPo;
import com.zte.uedm.battery.service.battlife.BattLifeAIConfigService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mock;

public class BattLifeAIConfigControllerTest {
    @InjectMocks
    private BattLifeAIConfigController battLifeAIConfigController;
    @Mock
    private BattLifeAIConfigService battLifeAIConfigService;
    @Mock
    private JsonService jsonService;
    @Mock
    private MessageSenderService msgSenderService;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectRatedCfgTest() throws UedmException {
        Mockito.doReturn(new BattLifeRatedCfgPo()).when(battLifeAIConfigService).selectRatedCfg();
        Assert.assertNotNull(battLifeAIConfigController.selectRatedCfg());
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).selectRatedCfg();
        Assert.assertNotNull(battLifeAIConfigController.selectRatedCfg());
    }
    @Test
    public void updateRatedCfgTest()throws UedmException{
        BattLifeRatedCfgPo preBattLifeRatedCfgPo=new BattLifeRatedCfgPo();
        preBattLifeRatedCfgPo.setRatedLiIonLife(1);
        preBattLifeRatedCfgPo.setRatedLeadAcidLife(1);
        preBattLifeRatedCfgPo.setRatedLeadAcidCycleTimes(1);
        preBattLifeRatedCfgPo.setRatedLiIonCycleTimes(1);
        Mockito.doReturn(preBattLifeRatedCfgPo).when(battLifeAIConfigService).selectRatedCfg();
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battLifeAIConfigController.updateRatedCfg(null,null,null));
        BattLifeRatedCfgDto battLifeRatedCfgDto=new BattLifeRatedCfgDto();
        battLifeRatedCfgDto.setRatedLeadAcidLife("1");
        battLifeRatedCfgDto.setRatedLiIonLife("1");
        battLifeRatedCfgDto.setRatedLeadAcidCycleTimes("1");
        battLifeRatedCfgDto.setRatedLiIonCycleTimes("1");
        Mockito.doReturn(1).when(battLifeAIConfigService).updateRatedCfg(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.updateRatedCfg(battLifeRatedCfgDto,"zh-CN", request));
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).selectRatedCfg();
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).updateRatedCfg(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.updateRatedCfg(battLifeRatedCfgDto,"zh-CN",request));
    }
    @Test
    public void selectSpecificCfgTest()throws UedmException{
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battLifeAIConfigController.selectSpecificCfg(null,null,request));
        Mockito.doReturn(new ResponseBean(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT, "parameter is null")).when(battLifeAIConfigService).selectByCondition(Mockito.any(),Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.selectSpecificCfg(new SpecificCfgQueryDto(),null,request));
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).selectByCondition(Mockito.any(),Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.selectSpecificCfg(new SpecificCfgQueryDto(),null,request));
    }

    @Test
    public void insertSpecificCfgTest()throws UedmException{
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battLifeAIConfigController.insertSpecificCfg(null,null,null));
        SpecificCfgInsertDto specificCfgInsertDto=new SpecificCfgInsertDto();
        specificCfgInsertDto.setRatedLiIonLife("1");
        specificCfgInsertDto.setRatedLeadAcidCycleTimes("1");
        specificCfgInsertDto.setRatedLeadAcidLife("1");
        specificCfgInsertDto.setRatedLiIonCycleTimes("1");
        List<BattInfoBean> infoBeans=new ArrayList<>();
        BattInfoBean battInfoBean1 = new BattInfoBean();
        battInfoBean1.setBattId("1");
        battInfoBean1.setBattType("铅酸");
        battInfoBean1.setBattName("1");
        battInfoBean1.setBattPath("1");
        infoBeans.add(battInfoBean1);
        BattInfoBean battInfoBean2 = new BattInfoBean();
        battInfoBean2.setBattId("1");
        battInfoBean2.setBattType("锂电");
        battInfoBean2.setBattName("1");
        battInfoBean2.setBattPath("1");
        infoBeans.add(battInfoBean2);
        specificCfgInsertDto.setBattDetail(infoBeans);
        Mockito.doReturn(1).when(battLifeAIConfigService).insertBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.insertSpecificCfg(specificCfgInsertDto,"zh-CN", request));
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).insertBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.insertSpecificCfg(specificCfgInsertDto,"zh-CN",request));
    }

    @Test
    public void deleteSpecificCfgTest()throws UedmException{
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battLifeAIConfigController.deleteSpecificCfg(null,null,null));
        SpecificCfgDeleteDto specificCfgDeleteDto = new SpecificCfgDeleteDto();
        List<String> ids=new ArrayList<>();
        ids.add("1");
        ids.add("2");
        ids.add("3");
        specificCfgDeleteDto.setIds(ids);
        BattLifeSpecificCfgPo battLifeSpecificCfgPo1=new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo1.setBattType("铅酸");
        BattLifeSpecificCfgPo battLifeSpecificCfgPo2=new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo2.setBattType("铁锂");
        Mockito.doReturn(battLifeSpecificCfgPo1).when(battLifeAIConfigService).selectById("1");
        Mockito.doReturn(battLifeSpecificCfgPo2).when(battLifeAIConfigService).selectById("2");
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).selectById("3");
        Mockito.doReturn(1).when(battLifeAIConfigService).deleteBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.deleteSpecificCfg(specificCfgDeleteDto,"zh-CN", request));
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).deleteBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.deleteSpecificCfg(specificCfgDeleteDto,"zh-CN",request));
    }

    @Test
    public void updateSpecificCfgTest()throws UedmException{
        HttpServletRequest request = mock(HttpServletRequest.class);
        Assert.assertNotNull(battLifeAIConfigController.updateSpecificCfg(null,null,null));
        SpecificCfgUpdateDto specificCfgUpdateDto=new SpecificCfgUpdateDto();
        specificCfgUpdateDto.setRatedLiIonLife("1");
        specificCfgUpdateDto.setRatedLeadAcidCycleTimes("1");
        specificCfgUpdateDto.setRatedLeadAcidLife("1");
        specificCfgUpdateDto.setRatedLiIonCycleTimes("1");
        List<String> ids=new ArrayList<>();
        ids.add("1");
        ids.add("2");
        ids.add("3");
        ids.add("4");
        specificCfgUpdateDto.setIds(ids);
        BattLifeSpecificCfgPo battLifeSpecificCfgPo1=new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo1.setBattType("铅酸");
        BattLifeSpecificCfgPo battLifeSpecificCfgPo2=new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo2.setBattType("锂电");
        Mockito.doReturn(battLifeSpecificCfgPo1).when(battLifeAIConfigService).selectById("1");
        Mockito.doReturn(battLifeSpecificCfgPo2).when(battLifeAIConfigService).selectById("2");
        Mockito.doReturn(null).when(battLifeAIConfigService).selectById("3");
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).selectById("4");
        Mockito.doReturn(1).when(battLifeAIConfigService).updateBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.updateSpecificCfg(specificCfgUpdateDto,"zh-CN", request));
        Mockito.doThrow(new UedmException(1,"")).when(battLifeAIConfigService).updateBatch(Mockito.any());
        Assert.assertNotNull(battLifeAIConfigController.updateSpecificCfg(specificCfgUpdateDto,"zh-CN",request));
    }
}