package com.zte.uedm.battery.bean;

import com.zte.uedm.common.bean.ResponseBean;
import org.junit.Test;

import static org.junit.Assert.*;

public class BatteryWorkConditionDimensionsUpdateResponseBeanTest {
    @Test
    public void testSetGetToString()
    {
        ResponseBean bb = new ResponseBean();
        bb.setCode(1);
        bb.setData("1111");
        bb.setError("11111");
        bb.setMessage("222");
        bb.setTotal(2);
        bb.toString();
        assertEquals(1,(int) bb.getCode());
    }

}