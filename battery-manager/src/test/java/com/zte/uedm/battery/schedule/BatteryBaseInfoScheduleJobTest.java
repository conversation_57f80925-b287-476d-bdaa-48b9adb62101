package com.zte.uedm.battery.schedule;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BatteryBaseInfoScheduleJobTest {

    @InjectMocks
    private BatteryBaseInfoScheduleJob batteryBaseInfoScheduleJob;

    @Mock
    private BatteryRemainDischargringDurationEvalSchduleJob batteryRemainDischargringDurationEvalSchduleJob;


    @Test
    public void testExecute() throws Exception {
        batteryBaseInfoScheduleJob.execute();
        Mockito.when(batteryRemainDischargringDurationEvalSchduleJob.execute()).thenThrow(new RuntimeException());
        batteryBaseInfoScheduleJob.execute();
        verify(batteryRemainDischargringDurationEvalSchduleJob, atLeastOnce()).execute();
    }

    /* Started by AICoder, pid:1a7beb66e8ub426141b80923a0412237d9b9f722 */
    @Test
    public void given_executeClear_when_success_then_noExceptionThrown() throws UedmException {
        // given
        // when
        batteryBaseInfoScheduleJob.executeClear();
        // then
        verify(batteryRemainDischargringDurationEvalSchduleJob, times(1)).clear();
    }

    @Test
    public void given_executeClear_when_exceptionOccurs_then_logError() throws UedmException {
        // given
        doThrow(new RuntimeException("An error occurred")).when(batteryRemainDischargringDurationEvalSchduleJob).clear();
        // when
        batteryBaseInfoScheduleJob.executeClear();
        // then
        verify(batteryRemainDischargringDurationEvalSchduleJob, times(1)).clear();
    }

    /* Ended by AICoder, pid:1a7beb66e8ub426141b80923a0412237d9b9f722 */
}