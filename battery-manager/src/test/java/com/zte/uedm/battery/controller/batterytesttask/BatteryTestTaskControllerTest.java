package com.zte.uedm.battery.controller.batterytesttask;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.uedm.battery.bean.BatteryTestRetryVo;
import com.zte.uedm.battery.controller.batterytesttask.bo.BattTestTaskBo;
import com.zte.uedm.battery.controller.batterytesttask.dto.*;
import com.zte.uedm.battery.controller.batterytesttask.vo.BattTestTaskVo;
import com.zte.uedm.battery.controller.batterytesttask.vo.DeviceSelectedVo;
import com.zte.uedm.battery.controller.batterytesttask.vo.GetTaskRelationDeviceVo;
import com.zte.uedm.battery.service.BatteryTestTaskService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.function.sm.exception.AuthorityException;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class BatteryTestTaskControllerTest
{
    @InjectMocks
    private BatteryTestTaskController batteryTestTaskController;

    @Mock
    private BatteryTestTaskService batteryTestTaskService;
    @Mock
    private MessageSenderService msgSenderService;


    private final HttpServletRequest request = new MockHttpServletRequest();
    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectAllStatusLevelsTest() throws UedmException {
        Mockito.doReturn(new ArrayList<>()).when(batteryTestTaskService).selectAllStatusLevels(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectAllStatusLevels(mock, "1");
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void selectAllStatusLevelsTest1() throws UedmException {
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).selectAllStatusLevels(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectAllStatusLevels(mock, "1");
        Assert.assertEquals("123",responseBean.getMessage());
    }


    @Test
    public void test_editTaskStatus_blank() throws UedmException {

        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();

        ResponseBean res = batteryTestTaskController.editTaskStatus(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-301), res.getCode());
    }

    @Test
    public void test_editTaskStatus_status_error() throws UedmException {

        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();
        dto.setId("task-1");
        dto.setStatus("error-status");

        ResponseBean res = batteryTestTaskController.editTaskStatus(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-3041), res.getCode());
    }

    @Test
    public void test_editTaskStatus_normal() throws UedmException {

        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();
        dto.setId("task-1");
        dto.setStatus("running");

        ResponseBean res = batteryTestTaskController.editTaskStatus(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(0), res.getCode());
    }

    @Test
    public void test_editTaskStatus_exception() throws UedmException {

        TaskStatusEditRequestDto dto = new TaskStatusEditRequestDto();
        dto.setId("task-1");
        dto.setStatus("running");
        Mockito.doThrow(new UedmException(-1,"")).when(batteryTestTaskService).editTaskStatus(Mockito.any(),Mockito.any());

        ResponseBean res = batteryTestTaskController.editTaskStatus(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-1), res.getCode());
    }

    @Test
    public void test_editTask_blank() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("");
        dto.setName("running");
        dto.setPeriod(2);
        dto.setStartTime("2022-08-15 00:00:00");

        ResponseBean res = batteryTestTaskController.editTask(null, request, "zh-CN");
        Assert.assertEquals(new Integer(-301), res.getCode());
        ResponseBean res2 = batteryTestTaskController.editTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-301), res.getCode());
    }

    @Test
    public void test_editTask_length_limit() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111");
        dto.setPeriod(2);
        dto.setStartTime("2022-08-15 00:00:00");

        ResponseBean res = batteryTestTaskController.editTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-303), res.getCode());
    }

    @Test
    public void test_editTask_period_range() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running");
        dto.setPeriod(900);
        dto.setStartTime("2022-08-15 00:00:00");

        ResponseBean res = batteryTestTaskController.editTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-304), res.getCode());
    }

    @Test
    public void test_editTask_period_normal() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running");
        dto.setPeriod(400);
        dto.setStartTime("2022-08-15 00:00:00");

        Mockito.doReturn(Pair.of("task-1", 1)).when(batteryTestTaskService).editTask(Mockito.any(),Mockito.any());

        ResponseBean res = batteryTestTaskController.editTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(0), res.getCode());
    }

    @Test
    public void test_editTask_period_uedmexception() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running");
        dto.setPeriod(400);
        dto.setStartTime("2022-08-15 00:00:00");

        Mockito.doThrow(new UedmException(-1, "")).when(batteryTestTaskService).editTask(Mockito.any(),Mockito.any());

        ResponseBean res = batteryTestTaskController.editTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-1), res.getCode());
    }
    @Test
    public void selectByCondition() throws UedmException
    {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",mock);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void selectByCondition1() throws Exception {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setLogicGroupId("1");
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).selectByCondition(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",mock);
        Assert.assertEquals("123",responseBean.getMessage());
    }

    @Test
    public void selectByCondition2() throws Exception {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setLogicGroupId("1");
        deviceSelectedDto.setTestStatus(Arrays.asList("1"));
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        Mockito.doReturn(deviceSelectedVo).when(batteryTestTaskService).selectByCondition(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",mock);
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }
    @Test
    public void selectByCondition3() throws Exception {
        DeviceSelectedDto deviceSelectedDto=new DeviceSelectedDto();
        deviceSelectedDto.setLogicGroupId("1");
        DeviceSelectedVo deviceSelectedVo = new DeviceSelectedVo();
        deviceSelectedVo.setDevices(new ArrayList<>());
        deviceSelectedVo.setTotal(0);
        Mockito.doReturn(deviceSelectedVo).when(batteryTestTaskService).selectByCondition(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectByCondition(deviceSelectedDto, 1,10,"zh_CN",mock);
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }


    @Test
    public void selectById() throws UedmException
    {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectById(detailDto, "zh_CN",mock);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void selectById1() throws UedmException, ParseException
    {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        detailDto.setTaskId("1");
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).selectByIdAndStatus(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectById(detailDto, "zh_CN",mock);
        Assert.assertEquals("123",responseBean.getMessage());
    }

    @Test
    public void selectById2() throws UedmException, ParseException
    {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        detailDto.setTaskId("1");
        detailDto.setStatus(Arrays.asList("1"));
        Mockito.doReturn(Pair.of(new PageInfo<>(),0)).when(batteryTestTaskService).selectByIdAndStatus(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectById(detailDto, "zh_CN",mock);
        Assert.assertEquals("Parameter is not in the range of optional values",responseBean.getMessage());
    }
    @Test
    public void selectById3() throws UedmException, ParseException {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        detailDto.setTaskId("1");
        Mockito.doReturn(Pair.of(new GetTaskRelationDeviceVo(),0)).when(batteryTestTaskService).selectByIdAndStatus(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectById(detailDto, "zh_CN",mock);
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }
    @Test
    public void selectById4() throws UedmException, ParseException {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        detailDto.setTaskId("1");
        detailDto.setOrder("1");
        detailDto.setSort("1");
        Mockito.doReturn(Pair.of(new GetTaskRelationDeviceVo(),0)).when(batteryTestTaskService).selectByIdAndStatus(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectById(detailDto, "zh_CN",mock);
        Assert.assertEquals("sort or order is not in the range of optional values",responseBean.getMessage());
    }



    @Test
    public void selectTemDeviceByTaskid() throws UedmException
    {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectTemDeviceByTaskid("",1,1, "zh_CN",mock);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void selectTemDeviceByTaskid1() throws UedmException, ParseException, AuthorityException {
        DeviceDetailDto detailDto=new DeviceDetailDto();
        detailDto.setTaskId("1");
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).selectTemByTaskId(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectTemDeviceByTaskid("detailDto",1,1, "zh_CN",mock);
        Assert.assertEquals("123",responseBean.getMessage());
    }

    @Test
    public void selectTemDeviceByTaskid2() throws UedmException, ParseException, AuthorityException {

        Mockito.doReturn(new PageInfo<>()).when(batteryTestTaskService).selectTemByTaskId(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectTemDeviceByTaskid("detailDto",1,1,  "zh_CN",mock);
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }


    @Test
    public void deleteTemDevice() throws UedmException
    {
        DeleteTemDeviceDto detailDto=new DeleteTemDeviceDto();
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.deleteTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void deleteTemDevice1() throws UedmException, ParseException
    {
        DeleteTemDeviceDto detailDto=new DeleteTemDeviceDto();
        detailDto.setTaskId("1");
        detailDto.setDeviceIds(Arrays.asList("1"));
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).deleteTemDevice(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.deleteTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("123",responseBean.getMessage());
    }

    @Test
    public void deleteTemDevice3() throws UedmException, ParseException {
        DeleteTemDeviceDto detailDto=new DeleteTemDeviceDto();
        detailDto.setTaskId("1");
        detailDto.setDeviceIds(Arrays.asList("1"));
        Mockito.doReturn(1).when(batteryTestTaskService).deleteTemDevice(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.deleteTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("1",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void test_selectTestTaskByCondition_normal() throws UedmException, AuthorityException {

        TaskQueryRequestDto dto = new TaskQueryRequestDto();
        dto.setSort("asc");
        dto.setStatus(Lists.newArrayList("running"));
        when(batteryTestTaskService.selectTestTaskByCondition(Mockito.any(), Mockito.any())).thenReturn(Pair.of(1, new ArrayList<>()));
        ResponseBean res = batteryTestTaskController.selectTestTaskByCondition(dto, request, "zh-CN");

        Assert.assertEquals(new Integer(0), res.getCode());
    }

    @Test
    public void test_selectTestTaskByCondition_exc() throws UedmException {

        try
        {
            TaskQueryRequestDto dto = new TaskQueryRequestDto();
            dto.setSort("asc");
            dto.setStatus(Lists.newArrayList("running"));
            when( batteryTestTaskService.selectTestTaskByCondition(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200,""));
            ResponseBean res = batteryTestTaskController.selectTestTaskByCondition(dto, request, "zh-CN");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        } catch (AuthorityException e) {
            assertNull(e);
        }
    }

    @Test
    public void test_selectTestTaskByCondition_exc2() throws UedmException {

        try
        {
            TaskQueryRequestDto dto = new TaskQueryRequestDto();
            dto.setSort("asc");
            dto.setStatus(Lists.newArrayList("running"));
            when( batteryTestTaskService.selectTestTaskByCondition(Mockito.any(), Mockito.any())).thenThrow(new AuthorityException(-200,""));
            ResponseBean res = batteryTestTaskController.selectTestTaskByCondition(dto, request, "zh-CN");
        }
        catch (AuthorityException e)
        {
            Assert.assertEquals("-200", e.getErrorId().toString());
        } catch (UedmException e) {
            assertNull(e);
        }
    }

    @Test
    public void selectEditDetailByIdTest() throws UedmException {
        BattTestTaskVo battTestTaskVo = new BattTestTaskVo();
        Mockito.doReturn(battTestTaskVo).when(batteryTestTaskService).selectById(Mockito.any(),Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectEditDetailById("id", request, "1");
        Assert.assertEquals("1",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void selectEditDetailByIdEmptyTest() throws UedmException {
        ResponseBean responseBean = batteryTestTaskController.selectEditDetailById("", request, "1");
        Assert.assertEquals(new Integer(-301),responseBean.getCode());
    }

    @Test
    public void selectEditDetailByIdExceptionTest() throws UedmException {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        Mockito.doThrow(new UedmException(-1,"")).when(batteryTestTaskService).selectById(Mockito.any(),Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectEditDetailById("id", request, "1");
        Assert.assertEquals(new Integer(-1), responseBean.getCode());
    }

    @Test
    public void selectShowDetailByIdTest() throws UedmException {
        BattTestTaskVo battTestTaskVo = new BattTestTaskVo();
        Mockito.doReturn(battTestTaskVo).when(batteryTestTaskService).selectShowDetailById(Mockito.any(),Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectShowDetailById("id", request, "1");
        Assert.assertEquals("1",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void selectShowDetailByIdEmptyTest() throws UedmException {
        ResponseBean responseBean = batteryTestTaskController.selectShowDetailById("", request, "1");
        Assert.assertEquals(new Integer(-301),responseBean.getCode());
    }

    @Test
    public void selectShowDetailByIdExceptionTest() throws UedmException {
        BattTestTaskBo battTestTaskBo = new BattTestTaskBo();
        Mockito.doThrow(new UedmException(-1,"")).when(batteryTestTaskService).selectShowDetailById(Mockito.any(),Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.selectShowDetailById("id", request, "1");
        Assert.assertEquals(new Integer(-1), responseBean.getCode());
    }

    @Test
    public void retryBatteryTestTest_normal() throws UedmException
    {
        BatteryTestRetryVo vo = new BatteryTestRetryVo();
        Mockito.doReturn(new ArrayList<>()).when(batteryTestTaskService).retryBatteryTest(Mockito.any(), Mockito.any());
        ResponseBean responseBean = batteryTestTaskController.retryBatteryTest(vo, request, "zh_CN");
        Assert.assertEquals("0",String.valueOf(responseBean.getTotal()));
    }
    @Test
    public void retryBatteryTestTest_Ex() throws UedmException
    {
        BatteryTestRetryVo vo = new BatteryTestRetryVo();
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).retryBatteryTest(Mockito.any(), Mockito.any());
        ResponseBean responseBean = batteryTestTaskController.retryBatteryTest(vo, request, "zh_CN");
        Assert.assertEquals("retry battery test fail", String.valueOf(responseBean.getMessage()));
    }
    @Test
    public void saveTemDevice() throws UedmException
    {
        DeleteTemDeviceSaveDto detailDto=new DeleteTemDeviceSaveDto();
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.saveTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("param is blank.",responseBean.getMessage());
    }

    @Test
    public void saveTemDevice1() throws UedmException
    {
        DeleteTemDeviceSaveDto detailDto=new DeleteTemDeviceSaveDto();
        detailDto.setTaskId("1");
        detailDto.setDeviceIds(Arrays.asList("1"));
        Mockito.doThrow(new UedmException(-100,"123")).when(batteryTestTaskService).saveTemDevice(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.saveTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("123",responseBean.getMessage());
    }

    @Test
    public void saveTemDevice2() throws UedmException {
        DeleteTemDeviceSaveDto detailDto=new DeleteTemDeviceSaveDto();
        detailDto.setTaskId("1");
        detailDto.setDeviceIds(Arrays.asList("1"));
        Mockito.doReturn(1).when(batteryTestTaskService).saveTemDevice(Mockito.any(),Mockito.any());
        Mockito.doReturn("1").when(jsonService).objectToJson(Mockito.any());
        HttpServletRequest mock = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = batteryTestTaskController.saveTemDevice(detailDto, "zh_CN",mock);
        Assert.assertEquals("1",String.valueOf(responseBean.getTotal()));
    }
    @Test
    public void test_addTask_period_normal() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running");
        dto.setPeriod(400);
        dto.setStartTime("2022-08-15 00:00:00");

        Mockito.doReturn(1).when(batteryTestTaskService).addTask(Mockito.any(),Mockito.any());

        ResponseBean res = batteryTestTaskController.addTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(0), res.getCode());
    }

    @Test
    public void test_addTask_period_exception() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running");
        dto.setPeriod(400);
        dto.setStartTime("2022-08-15 00:00:00");
        Mockito.doThrow(new UedmException(-1, "")).when(batteryTestTaskService).addTask(Mockito.any(),Mockito.any());
        ResponseBean res = batteryTestTaskController.addTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-1), res.getCode());
        try
        {
            //周期超限
            dto.setPeriod(800);
            ResponseBean responseBean = batteryTestTaskController.addTask(dto, request, "zh-CN");
            Assert.assertEquals(new Integer(-304), responseBean.getCode());
        }
        catch (Exception e)
        {

        }
    }

    @Test
    public void test_addTask_blank() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("");
        dto.setName("running");
        dto.setPeriod(2);
        dto.setStartTime("2022-08-15 00:00:00");

        ResponseBean res = batteryTestTaskController.addTask(null, request, "zh-CN");
        Assert.assertEquals(new Integer(-301), res.getCode());
        ResponseBean res2 = batteryTestTaskController.addTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-301), res.getCode());
    }

    @Test
    public void test_addTask_length_limit() throws UedmException {

        TaskEditRequestDto dto = new TaskEditRequestDto();
        dto.setId("1");
        dto.setName("running111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111");
        dto.setPeriod(2);
        dto.setStartTime("2022-08-15 00:00:00");

        ResponseBean res = batteryTestTaskController.addTask(dto, request, "zh-CN");
        Assert.assertEquals(new Integer(-303), res.getCode());
    }
    @Test
    public void test_searchDevicesByName() throws UedmException, AuthorityException {

        DeviceSearchDto dto = new DeviceSearchDto();
        dto.setName("11");
        when(batteryTestTaskService.searchDevicesByCondition(Mockito.any(), Mockito.any())).thenReturn(Pair.of(new ArrayList<>(), 0));
        ResponseBean responseBean = batteryTestTaskController.searchDevicesByName(dto, "11", request);
        Assert.assertEquals(new Integer(0), responseBean.getCode());
    }

    @Test
    public void test_searchDevicesByName_Exc() throws UedmException
    {

        try
        {
            DeviceSearchDto dto = new DeviceSearchDto();
            dto.setName("11");

            when(batteryTestTaskService.searchDevicesByCondition(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200, ""));
            batteryTestTaskController.searchDevicesByName(dto,"11", request);
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-200, (int) e.getErrorId());

        } catch (AuthorityException e) {
           assertNull(e);
        }
    }

    @Test
    public void test_searchDevicesByName_Exc2() throws UedmException
    {

        try
        {
            DeviceSearchDto dto = new DeviceSearchDto();
            dto.setName("11");

            when(batteryTestTaskService.searchDevicesByCondition(Mockito.any(), Mockito.any())).thenThrow(new AuthorityException(-200, ""));
            batteryTestTaskController.searchDevicesByName(dto,"11", request);
        }
        catch (AuthorityException e)
        {
            Assert.assertEquals(-200, (int) e.getErrorId());

        } catch (UedmException e) {
            assertNull(e);
        }
    }

    @Test
    public void test_deleteTask_normal() throws Exception
    {
        Assert.assertEquals(new Integer(-301), batteryTestTaskController.deleteTask(new ArrayList<>(), request, "zh-CN").getCode());
        Assert.assertEquals(new Integer(0), batteryTestTaskController.deleteTask(Arrays.asList("sa"), request, "zh-CN").getCode());
    }

    @Test
    public void test_deleteTask_exception() throws Exception
    {
        Mockito.doThrow(new UedmException(-1, "")).when(batteryTestTaskService).deleteTasks(Mockito.anyList(),Mockito.any());
        Assert.assertEquals(new Integer(-1), batteryTestTaskController.deleteTask(Arrays.asList("sa"), request, "zh-CN").getCode());
    }

    @Test
    public void selectBattTestTaskStatisticsTest_empty()
    {
        ResponseBean responseBean = batteryTestTaskController.selectBattTestTaskStatistics(new BattTestTaskStatisticDto(), "lang", request);
        Assert.assertEquals(-301, responseBean.getCode().intValue());
    }

    @Test
    public void selectBattTestTaskStatisticsTest_param()
    {
        BattTestTaskStatisticDto battTestTaskStatisticDto = new BattTestTaskStatisticDto();
        battTestTaskStatisticDto.setAutoStatisticsDims(Arrays.asList("siteNumber"));
        battTestTaskStatisticDto.setStatus(Arrays.asList("aa"));
        ResponseBean responseBean = batteryTestTaskController.selectBattTestTaskStatistics(battTestTaskStatisticDto, "lang", request);
        Assert.assertEquals(-304, responseBean.getCode().intValue());
    }

    @Test
    public void selectBattTestTaskStatisticsTest_normal()
    {
        BattTestTaskStatisticDto battTestTaskStatisticDto = new BattTestTaskStatisticDto();
        battTestTaskStatisticDto.setAutoStatisticsDims(Arrays.asList("siteNumber"));
        ResponseBean responseBean = batteryTestTaskController.selectBattTestTaskStatistics(battTestTaskStatisticDto, "lang", request);
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void selectBattTestTaskStatisticsTest_Exc() throws UedmException
    {
        BattTestTaskStatisticDto battTestTaskStatisticDto = new BattTestTaskStatisticDto();
        battTestTaskStatisticDto.setAutoStatisticsDims(Arrays.asList("siteNumber"));
        Mockito.when(batteryTestTaskService.selectBattTestTaskStatistics(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-1, "1"));
        ResponseBean responseBean = batteryTestTaskController.selectBattTestTaskStatistics(battTestTaskStatisticDto, "lang", request);
        Assert.assertEquals(-1, responseBean.getCode().intValue());
    }
}
