package com.zte.uedm.battery.a_infrastructure.common;

/* Started by AICoder, pid:v19f15d86bz0d5f14c160aa5b0781049f6324dfd */
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class GlobalConstantsTest {
    @InjectMocks
    private GlobalConstants globalConstants;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testStandardPrefix() {
        Assertions.assertEquals("std-data:", GlobalConstants.STANDARD_PREFIX);
    }

    @Test
    public void testSingleQueryNum() {
        Assertions.assertEquals(30000, GlobalConstants.SINGLE_QUERY_NUM);
    }

    @Test
    public void testDisarm() {
        Assertions.assertEquals("0.0", GlobalConstants.DISARM);
    }

    @Test
    public void testSafeArea() {
        Assertions.assertEquals("batt_security_area", GlobalConstants.SAFE_AREA);
    }

    @Test
    public void testBattCfgCacheName() {
        Assertions.assertEquals("BATTERY_CFG", GlobalConstants.BATT_CFG_CACHE_NAME);
    }

    @Test
    public void testBattLocationCacheName() {
        Assertions.assertEquals("BATTERY_ORIGINAL_LOCATION", GlobalConstants.BATT_LOCATION_CACHE_NAME);
    }
}

/* Ended by AICoder, pid:v19f15d86bz0d5f14c160aa5b0781049f6324dfd */