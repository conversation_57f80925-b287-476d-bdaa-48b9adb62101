package com.zte.uedm.battery.enums.batttest;

import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

public class BatteryOverviewDimsEnumsTest {

    @Test
    public void test() {
        BatteryOverviewDimsEnums.NAME.getId();
        BatteryOverviewDimsEnums.NAME.getName();
        BatteryOverviewDimsEnums.NAME.getDefaultIndex();
        BatteryOverviewDimsEnums.NAME.getDefaultEnable();
        BatteryOverviewDimsEnums.NAME.getDefaultFixed();
        BatteryOverviewDimsEnums.NAME.getUnit();
        BatteryOverviewDimsEnums.NAME.getSortable();

        String name = BatteryOverviewDimsEnums.getNameById(BatteryOverviewDimsEnums.NAME.getId());
        Assert.assertEquals(name, BatteryOverviewDimsEnums.NAME.getName());

        Integer defaultIndex = BatteryOverviewDimsEnums.getDefaultIndexById(BatteryOverviewDimsEnums.NAME.getId());
        Assert.assertEquals(defaultIndex, BatteryOverviewDimsEnums.NAME.getDefaultIndex());

        Boolean defaultFixed = BatteryOverviewDimsEnums.getDefaultFixedById(BatteryOverviewDimsEnums.NAME.getId());
        Assert.assertEquals(defaultFixed, BatteryOverviewDimsEnums.NAME.getDefaultFixed());

        Boolean sortable = BatteryOverviewDimsEnums.getSortableById(BatteryOverviewDimsEnums.NAME.getId());
        Assert.assertEquals(sortable, BatteryOverviewDimsEnums.NAME.getSortable());

        Boolean enable = BatteryOverviewDimsEnums.getDefaultEnableById(BatteryOverviewDimsEnums.NAME.getId());
        Assert.assertEquals(enable, BatteryOverviewDimsEnums.NAME.getSortable());

        BatteryOverviewDimsEnums.getAllIds();
        BatteryOverviewDimsEnums.getAllCannotModifyEnums();
        BatteryOverviewDimsEnums.getIdsWithIdName();
        String unit = BatteryOverviewDimsEnums.getUnitById("name");
        Assert.assertEquals(null, unit);
        try {
            BatteryOverviewDimsEnums.checkIdIsNeeded(Collections.emptyList());
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-200));
        }
        try {
            BatteryOverviewDimsEnums.checkIdIsNeeded(Collections.singletonList("11"));
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-302));
        }
    }

}
