package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.export.manage.entity.ExportTaskPO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class ExportTaskMapperFake implements ExportTaskMapper{
    /* Started by AICoder, pid:00e57q94c085c2c144a40ab780a63b4cb5194268 */
    @Override
    public List<ExportTaskPO> selectExport(List<Integer> statusList) {
        return null;
    }

    @Override
    public int updateById(ExportTaskPO exportTaskPO) {
        return 1;
    }

    @Override
    public int insertTask(ExportTaskPO exportTaskPO) {
        return 1;
    }

    @Override
    public List<ExportTaskPO> selectByCondition(String exportKey, String user) {
        return getDB().stream()
                .filter(bean -> bean.getExportKey().equals(exportKey)
                        && bean.getCreateUser().equals(user)
                        && bean.getStatus() != 3)
                .collect(Collectors.toList());
    }

    @Override
    public ExportTaskPO selectById(String taskId) {
        return getDB().stream()
                .filter(bean -> bean.getId().equals(taskId))
                .findFirst()
                .orElse(null);
    }

    @Override
    public int deleteById(String taskId) {
        return 0;
    }

    @Override
    public int removeBatchByIds(List<String> idList) {
        return 0;
    }

    @Override
    public List<ExportTaskPO> selectByCreateUserAndExportKey(String createUser, String exportKey) {
        return getDB().stream()
                .filter(bean -> bean.getExportKey().equals(exportKey)
                        && bean.getCreateUser().equals(createUser))
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:00e57q94c085c2c144a40ab780a63b4cb5194268 */

    private List<ExportTaskPO>  getDB(){
        /* Started by AICoder, pid:fd952h61843ca26142ab0b8dd0a9513475746b93 */
        // 创建一个空的列表来存储 ExportTaskPO 对象
        List<ExportTaskPO> exportTaskList = new ArrayList<>();

        // 创建并添加第一个 ExportTaskPO 对象到列表中
        ExportTaskPO task1 = new ExportTaskPO();
        task1.setId("1");
        task1.setExportKey("key1");
        task1.setParams("{\"param1\":\"value1\"}");
        task1.setStatus(2);
        task1.setProgress("0%");
        task1.setCreateUser("admin");
        task1.setGmtCreate(new Date());
        task1.setFileName("file1.xlsx");
        task1.setFilePath("/path/to/file1.xlsx");
        task1.setCompleteTime(new Date());
        task1.setServiceBean("serviceBean1");

        exportTaskList.add(task1);

        // 创建并添加第二个 ExportTaskPO 对象到列表中
        ExportTaskPO task2 = new ExportTaskPO();
        task2.setId("2");
        task2.setExportKey("key2");
        task2.setParams("{\"param2\":\"value2\"}");
        task2.setStatus(1);
        task2.setProgress("50%");
        task2.setCreateUser("admin");
        task2.setGmtCreate(new Date());
        task2.setFileName("file2.xlsx");
        task2.setFilePath("/path/to/file2.xlsx");
        task2.setCompleteTime(new Date());
        task2.setServiceBean("serviceBean2");

        exportTaskList.add(task2);
        return  exportTaskList;
        /* Ended by AICoder, pid:fd952h61843ca26142ab0b8dd0a9513475746b93 */
    }
}
