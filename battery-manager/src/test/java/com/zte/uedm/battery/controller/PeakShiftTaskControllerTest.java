package com.zte.uedm.battery.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.StopPeakShiftingStrategyServiceImpl;
import com.zte.uedm.battery.bean.PeakShiftDeviceChildBeanVo;
import com.zte.uedm.battery.bean.PeakShiftDeviceTaskBean;
import com.zte.uedm.battery.bean.PeakShiftDeviceTaskVo;
import com.zte.uedm.battery.bean.peak.DeviceLatestPeakShiftTaskVo;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskDetailQueryDTO;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskRetryBean;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.battery.service.PeakShiftTaskDeviceService;
import com.zte.uedm.battery.service.PeakShiftTaskService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class PeakShiftTaskControllerTest
{

    @InjectMocks
    private PeakShiftTaskController peakShiftTaskController;

    @Mock
    private PeakShiftTaskService peakShiftTaskService;

    @Mock
    private PeakShiftTaskDeviceService peakShiftTaskDeviceService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private ConfigService configService;

    @Mock
    private MpRpcImpl mpRpc;

    @Mock
    private JsonService jsonService;

    @Mock
    private MessageSenderService msgSenderService;

    @Mock
    private StopPeakShiftingStrategyServiceImpl stopPeakShiftingStrategyService;

    public static final String NAME = "name";

    public static final String FILEID = "fileId";

    public static final String DEVICEIDS = "deviceIds";

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void getFileInfoTest() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        List<PeakShiftDeviceChildBeanVo> beanVoList = new ArrayList<>();
        PeakShiftDeviceChildBeanVo peakShiftDeviceBeanVo = new PeakShiftDeviceChildBeanVo();
        peakShiftDeviceBeanVo.setId("2233");
        peakShiftDeviceBeanVo.setName("2233");
        beanVoList.add(peakShiftDeviceBeanVo);

        doReturn(beanVoList).when(peakShiftTaskService).getFileInfo(Mockito.any());
        ResponseBean fileInfo = peakShiftTaskController.getFileInfo(Lists.newArrayList("2233"), 1, 10, "zh_CN", req);
        assertEquals(1, fileInfo.getTotal().intValue());
    }

    @Test
    public void getFileInfoTest1() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);

        ResponseBean fileInfo = peakShiftTaskController.getFileInfo(null, 1, 10, "zh_CN", req);
        assertEquals(0, fileInfo.getTotal().intValue());
    }

    @Test
    public void getFileInfoTest_EX() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        doThrow(new UedmException(-200, "error")).when(peakShiftTaskService).getFileInfo(Mockito.any());
        ResponseBean fileInfo = peakShiftTaskController.getFileInfo(Lists.newArrayList("2233"), 1, 10, "zh_CN", req);
        assertEquals(-200, fileInfo.getCode().intValue());
    }

    @Test
    public void getOptionalDeviceStatusTest() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        Map<String, Boolean> optionalDeviceStatusMap = new HashMap<>();
        optionalDeviceStatusMap.put("2233", true);

        doReturn(optionalDeviceStatusMap).when(peakShiftTaskService).getOptionalDeviceStatus(Mockito.any());
        ResponseBean fileInfo = peakShiftTaskController.getOptionalDeviceStatus(Lists.newArrayList("2233"), 1, 10, "zh_CN", req);
        assertEquals(1, fileInfo.getTotal().intValue());
    }

    @Test
    public void getOptionalDeviceStatusTest1() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);

        ResponseBean fileInfo = peakShiftTaskController.getOptionalDeviceStatus(null, 1, 10, "zh_CN", req);
        assertEquals(0, fileInfo.getTotal().intValue());
    }

    @Test
    public void getOptionalDeviceStatusTest_EX() throws UedmException
    {
        HttpServletRequest req = mock(HttpServletRequest.class);
        doThrow(new UedmException(-200, "error")).when(peakShiftTaskService).getOptionalDeviceStatus(Mockito.any());
        ResponseBean fileInfo = peakShiftTaskController.getOptionalDeviceStatus(Lists.newArrayList("2233"), 1, 10, "zh_CN", req);
        assertEquals(-200, fileInfo.getCode().intValue());
    }

    @Test
    public void setStatusByDeviceIdTest1()
    {
        PeakShiftTaskDetailQueryDTO queryDTO = new PeakShiftTaskDetailQueryDTO();
        ResponseBean bean = peakShiftTaskController.setStatusByDeviceId(queryDTO, "", null);
        assertEquals(bean.getCode().intValue(), 0);
    }

    @Test
    public void setStatusByDeviceIdTest2()
    {
        ResponseBean bean = peakShiftTaskController.setStatusByDeviceId(null, "", null);
        assertEquals(bean.getCode().intValue(), 0);
    }

    @Test
    public void getFileIdByDeviceIdTest1()
    {
        PeakShiftTaskDetailQueryDTO queryDTO = new PeakShiftTaskDetailQueryDTO();
        ResponseBean bean = peakShiftTaskController.getFileIdByDeviceId(queryDTO, "", null);
        assertEquals(bean.getCode().intValue(), 0);
    }

    @Test
    public void getFileIdByDeviceIdTest2()
    {
        ResponseBean bean = peakShiftTaskController.getFileIdByDeviceId(null, "", null);
        assertEquals(bean.getCode().intValue(), 0);
    }

    @Test
    public void getFileIdByDeviceId_ExcTest3()
    {
        try {
            PeakShiftTaskDetailQueryDTO queryDTO = new PeakShiftTaskDetailQueryDTO();
            Mockito.doThrow(new UedmException(-200, "msg")).when(peakShiftTaskService).getFileIdByDeviceId(Mockito.anyString(), Mockito.anyString());
            ResponseBean bean = peakShiftTaskController.getFileIdByDeviceId(queryDTO, "", null);
            assertEquals(bean.getCode().intValue(), 0);
        }
        catch (Exception e)
        {
            assertEquals(UedmException.class, e.getClass());
        }
    }

    @Test
    public void getTaskInfoByDeviceId()
    {
        try
        {
            peakShiftTaskController.getTaskInfoByDeviceId(null);
            peakShiftTaskController.getTaskInfoByDeviceId("");
            Mockito.doThrow(new UedmException(-200, "msg")).when(peakShiftTaskService).selectLatestTaskByDeviceId(Mockito.anyString());
            peakShiftTaskController.getTaskInfoByDeviceId("");
        }
        catch (Exception e)
        {
            assertEquals("msg",e.getMessage());
        }


    }

    @Test
    public void getDeviceLatestStrategyTaskInfoTest() throws Exception
    {
        doReturn(new ArrayList<>()).when(peakShiftTaskService).getDeviceLatestStrategyTaskInfo();
        ResponseBean ret = peakShiftTaskController.getDeviceLatestStrategyTaskInfo();
        List<DeviceLatestPeakShiftTaskVo> retLis = (List<DeviceLatestPeakShiftTaskVo>) ret.getData();
        assertEquals(0, retLis.size());
    }

    @Test
    public void getDeviceLatestStrategyTaskInfoTestEx() throws Exception
    {
        try
        {
            doThrow(new UedmException(-1,"")).when(peakShiftTaskService).getDeviceLatestStrategyTaskInfo();
            peakShiftTaskController.getDeviceLatestStrategyTaskInfo();

        }
        catch (UedmException e)
        {
            assertEquals(-1, e.getErrorId().intValue());
        }

    }

    @Test
    public void selectDeviceTaskInfoByIdsTest() throws UedmException
    {
        PeakShiftDeviceTaskVo requestBean = new PeakShiftDeviceTaskVo();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        PageInfo<PeakShiftDeviceTaskBean> pageInfoList = new PageInfo<>();
        pageInfoList.setList(new ArrayList<>());
        doReturn(pageInfoList).when(peakShiftTaskService).getDeviceTaskBeansByDeviceList(Mockito.any(),
                Mockito.any());
        ResponseBean ret = peakShiftTaskController.selectDeviceTaskInfoByIds(new ArrayList<>(),null,null,request,"");
        List<PeakShiftDeviceTaskBean> retLis = (List<PeakShiftDeviceTaskBean>) ret.getData();
        assertEquals(0, retLis.size());
    }

    @Test
    public void selectDeviceTaskInfoByIdsExTest() throws UedmException
    {
        try
        {
            PeakShiftDeviceTaskVo requestBean = new PeakShiftDeviceTaskVo();
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PageInfo<PeakShiftDeviceTaskBean> pageInfoList = new PageInfo<>();
            pageInfoList.setList(new ArrayList<>());
            doThrow(new UedmException(-1,"")).when(peakShiftTaskService).getDeviceTaskBeansByDeviceList(Mockito.any(),
                    Mockito.any());
            ResponseBean ret = peakShiftTaskController.selectDeviceTaskInfoByIds(new ArrayList<>(),null,null,request,"");
        }
        catch (UedmException e)
        {
            assertEquals(-1, e.getMessage());
        }
    }
    @Test
    public void testManualRetry() throws Exception
    {

        final PeakShiftTaskRetryBean retryBean = new PeakShiftTaskRetryBean();
        retryBean.setTaskId("taskId");
        retryBean.setDeviceIds(Arrays.asList("value"));

        final HttpServletRequest request = new MockHttpServletRequest();

        final ResponseBean result = peakShiftTaskController.manualRetry(retryBean, "languageOption", request);
        assertEquals(0, result.getCode().intValue());

        final ResponseBean result1 = peakShiftTaskController.manualRetry(new PeakShiftTaskRetryBean(), "languageOption", request);
        assertEquals(-100, result1.getCode().intValue());
    }

    @Test
    public void testManualRetry_PeakShiftTaskServiceThrowsUedmException() throws Exception
    {
        // Setup
        final PeakShiftTaskRetryBean retryBean = new PeakShiftTaskRetryBean();
        retryBean.setTaskId("taskId");
        retryBean.setDeviceIds(Arrays.asList("value"));

        final HttpServletRequest request = new MockHttpServletRequest();
        doThrow(new UedmException(-1,"")).when(peakShiftTaskService)
                .manualRetry(any(PeakShiftTaskRetryBean.class), any(ServiceBaseInfoBean.class));

        final ResponseBean result = peakShiftTaskController.manualRetry(retryBean, "languageOption", request);
        assertEquals(-1, result.getCode().intValue());
    }

    @Test
    public void queryCsuDeviceParamTest()
    {
        final HttpServletRequest request = new MockHttpServletRequest();
        ResponseBean result1 = peakShiftTaskController.queryCsuDeviceParam("","lang",request);
        assertEquals(-301, result1.getCode().intValue());
        ResponseBean result2 = peakShiftTaskController.queryCsuDeviceParam("device","lang",request);
        assertEquals(0, result2.getCode().intValue());
    }

    @Test
    public void queryCsuDeviceParamTestExc() throws UedmException
    {
        final HttpServletRequest request = new MockHttpServletRequest();
        Mockito.when(peakShiftTaskService.queryCsuDeviceTransferParam(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-11, "a"));
        ResponseBean result = peakShiftTaskController.queryCsuDeviceParam("device","lang",request);
        assertEquals(-11, result.getCode().intValue());
    }

    @Test
    public void testStopPeakShiftStrategy_Success() {
        // 准备数据
        List<String> deviceIds = Arrays.asList("device1", "device2");


        // 执行测试
        ResponseBean actualResponseBean = peakShiftTaskController.stopPeakShiftStrategy(deviceIds);

        // 验证结果
        assertEquals(0, actualResponseBean.getCode().intValue());
        verify(stopPeakShiftingStrategyService, times(1)).stopPeakShiftingStrategy(deviceIds);

    }


}