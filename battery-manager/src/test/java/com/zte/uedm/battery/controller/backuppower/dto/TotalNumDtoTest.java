package com.zte.uedm.battery.controller.backuppower.dto;

import org.junit.Assert;
import org.junit.Test;


public class TotalNumDtoTest {
    @Test
    public void test() {
        TotalNumDto totalNumDto = new TotalNumDto();
        totalNumDto.setName("test");
        totalNumDto.setNorTotalNum(1);
        totalNumDto.setAbNorTotalNum(1);
        totalNumDto.setUnEvalTotalNum(1);
        Assert.assertEquals("test", totalNumDto.getName());
        Assert.assertEquals(1,(int)totalNumDto.getNorTotalNum());
        Assert.assertEquals(1,(int)totalNumDto.getAbNorTotalNum());
        Assert.assertEquals(1,(int)totalNumDto.getUnEvalTotalNum());
        Assert.assertNotNull(totalNumDto.toString());
    }

}