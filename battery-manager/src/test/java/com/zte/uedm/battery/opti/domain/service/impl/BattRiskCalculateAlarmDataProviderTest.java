package com.zte.uedm.battery.opti.domain.service.impl;

import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmBatchSchBean;
import com.zte.uedm.battery.bean.alarm.AlarmCode;
import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.domain.service.bean.RiskCalulateData;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeGroupOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.battery.service.impl.AlarmServiceImpl;
import com.zte.uedm.common.exception.UedmException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class BattRiskCalculateAlarmDataProviderTest
{
    @InjectMocks
    private BattRiskCalculateAlarmDataProvider battRiskCalculateAlarmDataProvider;

    @Mock
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository;

    @Mock
    private AlarmServiceImpl alarmServiceImpl;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getDataRealTime_Test() throws Exception
    {
        List<String> moIdList = new ArrayList<>();
        moIdList.add("batt-1");
        Map<String, Map<String, String>> result = battRiskCalculateAlarmDataProvider.getDataRealTime(moIdList, new ArrayList<>());
        Assert.assertEquals(result.size(), 0);

        List<String> parameterIds = new ArrayList<>();
        parameterIds.add("1001");
        List<Alarm> alarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(1001L);
        alarm.setMe("batt-1");
        alarmList.add(alarm);
        PowerMockito.when(alarmServiceImpl.getBatchActiveAlarmFromPass(Mockito.any())).thenReturn(alarmList);

        List<BattRiskRuleSourceEntity> list = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
        riskRuleSourceEntity.setSourceTypeGroup(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ALARM_CODE);
        list.add(riskRuleSourceEntity);
        PowerMockito.when(battRiskRuleSourceRepository.selectByIds(Mockito.any())).thenReturn(list);

        Map<String, Map<String, String>> result1 = battRiskCalculateAlarmDataProvider.getDataRealTime(moIdList, parameterIds);
        Assert.assertEquals(result1.size(), 1);
    }
    @Test
    public void getDataRealTime_Test1() throws Exception
    {
        List<String> moIdList = new ArrayList<>();
        moIdList.add("batt-1");
        Map<String, Map<String, String>> result = battRiskCalculateAlarmDataProvider.getDataRealTime(moIdList, new ArrayList<>());
        Assert.assertEquals(result.size(), 0);

        List<String> parameterIds = new ArrayList<>();
        parameterIds.add("1001");
        List<Alarm> alarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(1001L);
        alarm.setMe("batt-1");
        alarmList.add(alarm);
        PowerMockito.when(alarmServiceImpl.getBatchActiveAlarmFromPass(Mockito.any())).thenReturn(alarmList);

        List<BattRiskRuleSourceEntity> list = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
        riskRuleSourceEntity.setSourceTypeGroup(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ALARM_TIME);
        list.add(riskRuleSourceEntity);
        PowerMockito.when(battRiskRuleSourceRepository.selectByIds(Mockito.any())).thenReturn(list);

        Map<String, Map<String, String>> result1 = battRiskCalculateAlarmDataProvider.getDataRealTime(moIdList, parameterIds);
        Assert.assertEquals(result1.size(), 1);
    }


    @Test
    public void getDataRangeTime_Test() throws Exception
    {
        Map<String, Map<String, List<RiskCalulateData>>> result = battRiskCalculateAlarmDataProvider.getDataRangeTime(new ArrayList<>(), new ArrayList<>(), null);
        Assert.assertEquals(result.size(), 0);

        List<BattRiskRuleSourceEntity> list = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
        riskRuleSourceEntity.setSourceTypeGroup(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ALARM_CODE);
        list.add(riskRuleSourceEntity);
        PowerMockito.when(battRiskRuleSourceRepository.selectByIds(Mockito.any())).thenReturn(list);

        List<String> moIds = new ArrayList<>();
        moIds.add("batt-1");
        List<String> parameterIds = new ArrayList<>();
        parameterIds.add("1001");
        List<Alarm> alarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(1001L);
        alarm.setMe("batt-1");
        alarmList.add(alarm);
        PowerMockito.when(alarmServiceImpl.getBatchHistoryAlarmFromPass(Mockito.any())).thenReturn(alarmList);
        Pair<Date, Date> timeRange = Pair.of(new Date(), new Date());
        Map<String, Map<String, List<RiskCalulateData>>> result1 = battRiskCalculateAlarmDataProvider.getDataRangeTime(moIds, parameterIds, timeRange);
        Assert.assertEquals(result1.size(), 1);
    }
    @Test
    public void getDataRangeTime_Test1() throws Exception
    {
        Map<String, Map<String, List<RiskCalulateData>>> result = battRiskCalculateAlarmDataProvider.getDataRangeTime(new ArrayList<>(), new ArrayList<>(), null);
        Assert.assertEquals(result.size(), 0);

        List<BattRiskRuleSourceEntity> list = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM);
        riskRuleSourceEntity.setSourceTypeGroup(BattRiskRuleSourceTypeGroupOptional.SOURCE_TYPE_GROUP_ALARM_FREQUENCY);
        list.add(riskRuleSourceEntity);
        PowerMockito.when(battRiskRuleSourceRepository.selectByIds(Mockito.any())).thenReturn(list);

        List<String> moIds = new ArrayList<>();
        moIds.add("batt-1");
        List<String> parameterIds = new ArrayList<>();
        parameterIds.add("1001");
        List<Alarm> alarmList = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(1001L);
        alarm.setMe("batt-1");
        alarmList.add(alarm);
        PowerMockito.when(alarmServiceImpl.getBatchHistoryAlarmFromPass(Mockito.any())).thenReturn(alarmList);
        Pair<Date, Date> timeRange = Pair.of(new Date(), new Date());
        Map<String, Map<String, List<RiskCalulateData>>> result1 = battRiskCalculateAlarmDataProvider.getDataRangeTime(moIds, parameterIds, timeRange);
        Assert.assertEquals(result1.size(), 1);
    }

    @Test
    public void buildActiveAlarmRequestTest() {
        List<String> moIds = new ArrayList<>();
        moIds.add("batt-1");
        List<BattRiskRuleSourceEntity> battRiskRuleSourceEntityList = new ArrayList<>();
        BattRiskRuleSourceEntity entity = new BattRiskRuleSourceEntity();
        entity.setId("test123");
        entity.setMappingId("1001");
        battRiskRuleSourceEntityList.add(entity);
        AlarmBatchSchBean alarmBatchSchBean = battRiskCalculateAlarmDataProvider.buildActiveAlarmRequest(moIds, battRiskRuleSourceEntityList);
        Assert.assertEquals(alarmBatchSchBean.getIds().size(), 1);
    }

    @Test
    public void buildResponseTest() {
        List<Alarm> activeAlarmFromPass = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(**********L);
        alarm.setMe("batt-1");
        activeAlarmFromPass.add(alarm);

        List<String> moIds = new ArrayList<>();
        moIds.add("batt-1");

        List<BattRiskRuleSourceEntity> riskRuleSourceEntityList = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntityList.add(riskRuleSourceEntity);

        Map<String, Map<String, String>> result = battRiskCalculateAlarmDataProvider
                .buildResponse(activeAlarmFromPass, moIds, riskRuleSourceEntityList);
        Assert.assertEquals(result.size(), 1);
    }

    @Test
    public void buildHistoryAlarmRequestTest() {
        List<String> moIds = new ArrayList<>();
        moIds.add("batt-1");

        List<BattRiskRuleSourceEntity> riskRuleSourceEntityList = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntityList.add(riskRuleSourceEntity);

        Pair<Date, Date> timeRange = new ImmutablePair<>(new Date(), new Date());
        AlarmBatchSchBean result = battRiskCalculateAlarmDataProvider.buildHistoryAlarmRequest(moIds, riskRuleSourceEntityList, timeRange);
        Assert.assertEquals(result.getIds().size(), 1);
    }

    @Test
    public void buildHistoryAlarmResponseTest() {
        List<String> moIdList = new ArrayList<>();
        moIdList.add("batt-1");

        List<BattRiskRuleSourceEntity> riskRuleSourceEntityList = new ArrayList<>();
        BattRiskRuleSourceEntity riskRuleSourceEntity = new BattRiskRuleSourceEntity();
        riskRuleSourceEntity.setId("batt.alarm.loop.broken.alarm.**********");
        riskRuleSourceEntity.setMappingId("**********");
        riskRuleSourceEntityList.add(riskRuleSourceEntity);

        List<Alarm> activeAlarmFromPass = new ArrayList<>();
        Alarm alarm = new Alarm();
        alarm.setAlarmcode(**********L);
        alarm.setMe("batt-1");
        activeAlarmFromPass.add(alarm);
        Map<String, Map<String, List<RiskCalulateData>>> result = battRiskCalculateAlarmDataProvider
                .buildHistoryAlarmResponse(activeAlarmFromPass, moIdList, riskRuleSourceEntityList);
        Assert.assertEquals(result.size(), 1);
    }

}
