package com.zte.uedm.battery.opti.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.opti.domain.aggregate.model.BattRiskRuleSourceEntity;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattHealthEvalRepository;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.domain.utils.MapUtils;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional;
import com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceValueTypeOptional;
import com.zte.uedm.battery.util.FileUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.*;

import static com.zte.uedm.battery.opti.domain.utils.MapUtils.transforMapKey;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ MapUtils.class})
public class BattRiskCalculateHealthDataProviderTest {
    @InjectMocks
    private BattRiskCalculateHealthDataProvider battRiskCalculateHealthDataProvider;
    @Mock
    private BattHealthEvalRepository healthEvalRepository;
    @Mock
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository;
    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getDataRealTimeNormal_Test() throws Exception
    {
        List<BattRiskRuleSourceEntity> ruleSourceEntities = new ArrayList<>();
        BattRiskRuleSourceEntity battRiskRuleSourceEntity = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity.setMappingId("status_id");
        battRiskRuleSourceEntity.setId("batt.healthy.status_id");
        battRiskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_HEALTH);
        battRiskRuleSourceEntity.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity);
        when(battRiskRuleSourceRepository.selectByIds(Mockito.anyList())).thenReturn(ruleSourceEntities);

        List<BattHealthStatusEvalPo> healthStatusEvalBeans =new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("batt-1");
        battHealthStatusEvalPo.setStatusId("1");
        healthStatusEvalBeans.add(battHealthStatusEvalPo);

        when(healthEvalRepository.selectEvalStatusIdRealDateData(Mockito.anyList())).thenReturn(healthStatusEvalBeans);

        HashMap<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("id","batt-1");
        stringHashMap.put("statusId","1");
        when(jsonService.objectToJson(any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(),any(),any())).thenReturn(stringHashMap);


        List<String> moids =new ArrayList<>();
        moids.add("batt-1");
        moids.add("batt-2");
        List<String> parameterIds =new ArrayList<>();
        parameterIds.add("status_id");
        Assert.assertNotNull(battRiskCalculateHealthDataProvider.getDataRealTime(moids, parameterIds));
    }

    @Test
    public void getDataRealTimeNoData_Test()throws UedmException
    {
        when(healthEvalRepository.selectEvalStatusIdRealDateData(Mockito.anyList())).thenReturn(new ArrayList<>());
        Assert.assertTrue(battRiskCalculateHealthDataProvider.getDataRealTime(new ArrayList<>(), new ArrayList<>()).isEmpty());

    }

    @Test
    public void getDataRangeTimeNormal_Test() throws Exception
    {
        List<BattRiskRuleSourceEntity> ruleSourceEntities = new ArrayList<>();
        BattRiskRuleSourceEntity battRiskRuleSourceEntity = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity.setMappingId("status_id");
        battRiskRuleSourceEntity.setId("batt.healthy.status_id");
        battRiskRuleSourceEntity.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_HEALTH);
        battRiskRuleSourceEntity.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity);
        BattRiskRuleSourceEntity battRiskRuleSourceEntity2 = new BattRiskRuleSourceEntity();
        battRiskRuleSourceEntity2.setMappingId("dfgf");
        battRiskRuleSourceEntity2.setId("batt.healthy.statu2s_id");
        battRiskRuleSourceEntity2.setSourceType(BattRiskRuleSourceTypeOptional.SOURCE_TYPE_HEALTH);
        battRiskRuleSourceEntity2.setValueType(BattRiskRuleSourceValueTypeOptional.SOURCE_TYPE_NUMBER_A);
        ruleSourceEntities.add(battRiskRuleSourceEntity2);
        when(battRiskRuleSourceRepository.selectByIds(Mockito.anyList())).thenReturn(ruleSourceEntities);

        List<BattHealthStatusEvalPo> healthStatusEvalBeans =new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("batt-1");
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setEvaluateTime(new Date());
        healthStatusEvalBeans.add(battHealthStatusEvalPo);
        BattHealthStatusEvalPo battHealthStatusEvalPo2 = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo2.setStatusId("BB1");
        battHealthStatusEvalPo2.setId("batt-24");
        battHealthStatusEvalPo2.setEvaluateTime(new Date());
        healthStatusEvalBeans.add(battHealthStatusEvalPo2);
        when(healthEvalRepository.selectEvalStatusIdRangeDateData(Mockito.anyList(),Mockito.any(),Mockito.any())).thenReturn(healthStatusEvalBeans);


        HashMap<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("id","batt-1");
        stringHashMap.put("statusId","1");
        when(jsonService.objectToJson(any())).thenReturn("");
        when(jsonService.jsonToObject(Mockito.anyString(),any(),any())).thenReturn(stringHashMap);


        Pair<Date, Date> timeRange = new Pair<Date, Date>() {
            @Override
            public Date getLeft() {
                return new Date();
            }

            @Override
            public Date getRight() {
                return new Date();
            }

            @Override
            public Date setValue(Date value) {
                return null;
            }
        };
        List<String> moids =new ArrayList<>();
        moids.add("batt-1");
        moids.add("batt-2");
        List<String> parameterIds =new ArrayList<>();
        parameterIds.add("status_id");
        Assert.assertNotNull(battRiskCalculateHealthDataProvider.getDataRangeTime(moids, parameterIds,timeRange));


    }

    @Test
    public void getDataRangeTimeNoData_Test() throws Exception
    {
        when(healthEvalRepository.selectEvalStatusIdRangeDateData(Mockito.anyList(),Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertTrue(battRiskCalculateHealthDataProvider.getDataRangeTime(new ArrayList<>(), new ArrayList<>(), Pair.of(new Date(), new Date())).isEmpty());
    }
}
