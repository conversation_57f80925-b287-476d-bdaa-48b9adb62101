package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.pv.HistoryDataRequestConditionBean;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.mapper.BatteryOverviewMapper;
import com.zte.uedm.battery.rpc.AssetRpc;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * @ Author     ：10260977
 * @ Date       ：11:05 2022/6/27
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
public class MoHistoryDataQueryDomainImplTest
{
    @InjectMocks
    private MoHistoryDataQueryDomainImpl moHistoryDataQueryDomainImpl;

    @Mock
    private DateTimeService dateTimeService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectValueInfoByKeyTest() throws Exception
    {
        moHistoryDataQueryDomainImpl.buildYesterdayQueryBean("r32.uedm.ac");
        moHistoryDataQueryDomainImpl.buildTwoDayQueryBean("r32.uedm.ac");
        HistoryDataRequestConditionBean historyDataRequestConditionBean = moHistoryDataQueryDomainImpl
                .buildHistoryDataConditionBean("r32.uedm.pv", "123", "11", "11");
        Assert.assertEquals("r32.uedm.pv", historyDataRequestConditionBean.getMoc());
    }
}