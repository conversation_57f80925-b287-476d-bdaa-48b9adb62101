package com.zte.uedm.battery.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerconfig.service.impl.BackupPowerConfigServiceImpl;
import com.zte.uedm.battery.domain.BatteryBackupPowerEvalDomain;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

//@RunWith(MockitoJUnitRunner.class)
public class BatteryBackupPowerEvalServiceImplTest {

    @Mock
    private ConfigurationManagerRpcImpl mockConfigurationManagerRpcImpl;
    @Mock
    private BatteryBackupPowerEvalDomain mockBatteryBackupPowerEvalDomain;

    @InjectMocks
    private BatteryBackupPowerEvalServiceImpl batteryBackupPowerEvalServiceImplUnderTest;

    @Mock
    private BackupPowerConfigServiceImpl backupPowerConfigService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testBatteryBackupPowerEvalEntrance() throws Exception {
        // Setup
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("id");
        moObjectConfiguration.setName("name");
        moObjectConfiguration.setPath("path");
        moObjectConfiguration.setPathId("pathId");
        moObjectConfiguration.setParentId("parentId");
        List<MoObjectConfiguration> moObjectConfigurations = new ArrayList<>();
        moObjectConfigurations.add(moObjectConfiguration);

        MonitorObjectDsBean objectPageInfo = new MonitorObjectDsBean();
        moObjectConfiguration.setId("id");
        moObjectConfiguration.setName("name");
        moObjectConfiguration.setPath("path");
        moObjectConfiguration.setPathId("pathId");
        moObjectConfiguration.setParentId("parentId");
        List<MonitorObjectDsBean> objectPageInfoss = new ArrayList<>();
        objectPageInfoss.add(objectPageInfo);

        PageInfo<MonitorObjectDsBean> objectPageInfos = new PageInfo<>();
        objectPageInfos.setList(objectPageInfoss);
        when(mockConfigurationManagerRpcImpl.getMonitorObjectListWhenIdsBig(anyList(), anyString())).thenReturn(moObjectConfigurations);
        when(backupPowerConfigService.getBatteryPackMonitorObjectBeanPage(any(), any(), any(), any())).thenReturn(objectPageInfos);

        // Run the test
        try {
            batteryBackupPowerEvalServiceImplUnderTest.batteryBackupPowerEvalEntrance();
        } catch (UedmException e) {
            // Verify the results
            verify(mockBatteryBackupPowerEvalDomain, times(1)).evaluateBatteryBackupPower(anyList(), anyString());
        }
    }

}
