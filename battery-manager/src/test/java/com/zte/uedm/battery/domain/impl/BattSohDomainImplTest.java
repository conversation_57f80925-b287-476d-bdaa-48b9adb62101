package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.BattHealthStatusBean;
import com.zte.uedm.battery.bean.BattHealthStatusDto;
import com.zte.uedm.battery.bean.ThresholdBean;
import com.zte.uedm.battery.mapper.BattSohMapper;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

public class BattSohDomainImplTest
{

    @InjectMocks
    private BattSohDomainImpl battSohDomain;
    @Mock
    private ConfigurationRpc configurationRpc;
    @Mock
    private JsonService jsonService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BattSohMapper battSohMapper;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn("").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void selectSohLevelsTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battSohDomain.selectSohLevels(null).getTotal());
    }

    @Test
    public void selectSohLevelsTest_normal() throws Exception
    {
        List<IdNameBean> list = new ArrayList<>();
        when(battSohMapper.selectSohLevels()).thenReturn(null);

        Assert.assertSame(0l, battSohDomain.selectSohLevels(new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void selectSohLevelsTest_normal2() throws Exception
    {
        List<BattHealthStatusBean> list = new ArrayList<>();
        BattHealthStatusBean battHealthStatusBean = new BattHealthStatusBean();
        list.add(battHealthStatusBean);

        when(battSohMapper.selectSohLevels()).thenReturn(list);

        Assert.assertSame(1l, battSohDomain.selectSohLevels(new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void selectSohLevelsTest_rpc_exception()
    {
        try {
            Mockito.doThrow(new UedmException(-100, "12")).when(battSohMapper).selectSohLevels();

//            List<IdNameBean> list = new ArrayList<>();
//            IdNameBean idNameBean = new IdNameBean();
//            list.add(idNameBean);
//            when(battSohMapper.selectSohLevels()).thenReturn(list);

            battSohDomain.selectSohLevels(new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }


    @Test
    public void transSmpValueIntoSohLevelIdTest() throws Exception
    {
        Assert.assertSame("unknown", battSohDomain.transSmpValueIntoSohLevelId(null));
        Assert.assertEquals("3",battSohDomain.transSmpValueIntoSohLevelId("3.6"));
        Assert.assertSame("unknown", battSohDomain.transSmpValueIntoSohLevelId("3.a6"));

    }

    @Test
    public void upsertBattHealthStatusTest()
    {
        List<BattHealthStatusDto> statusList = new ArrayList<>();
        try {
            battSohDomain.upsertBattHealthStatus(statusList);
        } catch (UedmException e) {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void upsertBattHealthStatus_ExcTest1()
    {
        List<BattHealthStatusDto> statusList = new ArrayList<>();
        try {
            doThrow(new RuntimeException()).when(battSohMapper).upsertBattHealthStatus(anyList());
            battSohDomain.upsertBattHealthStatus(statusList);
        } catch (UedmException e) {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }

    @Test
    public void selectAllBattHealthLevelsTest() throws Exception
    {
        Assert.assertTrue(battSohDomain.selectAllBattHealthLevels().isEmpty());

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        battHealthStatusDto.setThreshold("sa");
        pojos.add(battHealthStatusDto);
        Mockito.doReturn(pojos).when(battSohMapper).selectAlls();
        Mockito.doReturn(new ThresholdBean()).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any());

        Assert.assertSame(1, battSohDomain.selectAllBattHealthLevels().size());
    }

    @Test
    public void selectAllBattHealthLevelsTestEx() throws Exception
    {

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        battHealthStatusDto.setThreshold("sa");
        pojos.add(battHealthStatusDto);
        Mockito.doThrow(new UedmException(-1,"")).when(battSohMapper).selectAlls();
        Mockito.doReturn(new ThresholdBean()).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any());

        Assert.assertSame(0, battSohDomain.selectAllBattHealthLevels().size());
    }

    @Test
    public void selectAllBattHealthLevelsTestEx2() throws Exception
    {

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        battHealthStatusDto.setThreshold("sa");
        pojos.add(battHealthStatusDto);
        Mockito.doReturn(pojos).when(battSohMapper).selectAlls();
        Mockito.doThrow(new UedmException(-1, "")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any());

        Assert.assertSame(1, battSohDomain.selectAllBattHealthLevels().size());
    }

    @Test
    public void selectUnknownLevelIdTest() throws Exception
    {
        Assert.assertNull(battSohDomain.selectUnknownLevelId());

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        pojos.add(battHealthStatusDto);
        Mockito.doReturn(pojos).when(battSohMapper).selectAlls();

        Assert.assertSame("1", battSohDomain.selectUnknownLevelId());
    }

    @Test
    public void selectUnknownLevelIdTestEx() throws Exception
    {
        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        pojos.add(battHealthStatusDto);
        try {
            Mockito.doThrow(new UedmException(-1,"")).when(battSohMapper).selectAlls();
            battSohDomain.selectUnknownLevelId();
        }catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void selectUnknownLevelTest() throws Exception
    {
        Assert.assertNotNull(battSohDomain.selectUnknownLevel("1"));

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        pojos.add(battHealthStatusDto);
        Mockito.doReturn(pojos).when(battSohMapper).selectAlls();

        Assert.assertSame("1", battSohDomain.selectUnknownLevel("1").getId());
    }

    @Test
    public void selectUnknownLevelTestEx() throws Exception
    {

        List<BattHealthStatusDto> pojos = new ArrayList<>();
        BattHealthStatusDto battHealthStatusDto = new BattHealthStatusDto();
        battHealthStatusDto.setId("1");
        pojos.add(battHealthStatusDto);
        try {
            Mockito.doThrow(new UedmException(-1,"")).when(battSohMapper).selectAlls();
            battSohDomain.selectUnknownLevel("1");
        }catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }

    }
    @Test
    public void updateBattHealthStatusTestThrow() throws UedmException {
        List<BattHealthStatusDto> battHealthStatusDtos = new ArrayList<>();
        when(battSohMapper.updateBattHealthStatus(any())).thenThrow(NullPointerException.class);
        try {
            battSohDomain.updateBattHealthStatus(battHealthStatusDtos);
        }catch (UedmException e)
        {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }

    }
    @Test
    public void updateBattHealthStatusTest() throws UedmException {
        List<BattHealthStatusDto> battHealthStatusDtos = new ArrayList<>();
        Mockito.doReturn(1).when(battSohMapper).updateBattHealthStatus(any());
        final int i = battSohDomain.updateBattHealthStatus(battHealthStatusDtos);
        Assert.assertEquals(1,i);
    }
}
