package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.controller.batthealth.dto.SelectEvalDetailDto;
import com.zte.uedm.battery.mapper.BattHealthStatusEvalMapper;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.enums.batt.BattHealthStatusEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * @ Author     ：10260977
 * @ Date       ：11:14 2022/6/27
 * @ Description：
 * @ Modified By：
 * @ Version: 1.0
 */
public class BattHealthStatusEvalDomainImplTest
{
    @InjectMocks
    private BattHealthStatusEvalDomainImpl battHealthStatusEvalDomainImpl;

    @Mock
    private BattHealthStatusEvalMapper battHealthStatusEvalMapper;

    @Mock
    private CommonCacheService cacheService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(battHealthStatusEvalDomainImpl, "threadNum", 5);
    }

    @Test
    public void selectValueInfoByKeyTest() throws Exception
    {
        try
        {
            List<BattHealthStatusEvalPo> battHealthStatusEvalPos = battHealthStatusEvalDomainImpl
                    .selectPreHealthStatusByMoIds(new ArrayList<>());
            Assert.assertEquals(0, battHealthStatusEvalPos.size());
            BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
            battHealthStatusEvalPo.setStatusId("11");
            Integer integer = battHealthStatusEvalDomainImpl.addHealthStatusEvalResult(Lists.newArrayList(battHealthStatusEvalPo));
            Assert.assertEquals("0", integer.toString());
            when(battHealthStatusEvalMapper.insertBeans(Mockito.any())).thenThrow(new RuntimeException());
            battHealthStatusEvalDomainImpl.addHealthStatusEvalResult(Lists.newArrayList(battHealthStatusEvalPo));
        }
        catch (UedmException e)
        {
            Assert.assertEquals("-401", e.getErrorId().toString());
        }
    }

    @Test
    public void selectHealthEvalByDateTest() throws Exception {
        List<BattHealthStatusEvalPo> list = new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setStatusId(BattHealthStatusEnum.getHealthyId());
        battHealthStatusEvalPo.setEvaluateTime(DateUtils.getYearMonDayDate("2022-07-02"));
        battHealthStatusEvalPo.setPathIds("test");
        list.add(battHealthStatusEvalPo);

        BattHealthStatusEvalPo battHealthStatusEvalPo1 = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo1.setStatusId(BattHealthStatusEnum.getSubHealthId());
        battHealthStatusEvalPo1.setEvaluateTime(DateUtils.getYearMonDayDate("2022-07-03"));
        battHealthStatusEvalPo1.setPathIds("test");
        list.add(battHealthStatusEvalPo1);
        Mockito.doReturn(list).when(battHealthStatusEvalMapper).selectHealthEvalByDate(Mockito.any(), Mockito.any(), Mockito.any());
        String startDate = "2022-07-01 10:21:14";
        String endDate = "2022-07-03 10:21:14";
        List<BattHealthStatusEvalPo> result = battHealthStatusEvalDomainImpl.selectHealthEvalByDate("test", startDate, endDate);
        Assert.assertEquals(result.size(), 2);
        Assert.assertEquals(result.get(0).getPathIds(), "test");
    }

    @Test
    public void selectHealthEvalStatisticsTest() throws Exception
    {
        String startDate = "2022-07-01 10:21:14";
        String endDate = "2022-07-03 10:21:14";
        List<BattHealthStatusEvalPo> result = battHealthStatusEvalDomainImpl.selectHealthEvalStatistics("test", startDate, endDate);
        Assert.assertEquals(result.size(), 0);
    }

    @Test
    public void selectLastByMoIdsTest() throws Exception {
        Assert.assertTrue(battHealthStatusEvalDomainImpl.selectLastByMoIds(new ArrayList<>()).isEmpty());

        List<BattHealthStatusEvalPo> pojos = new ArrayList<>();
        BattHealthStatusEvalPo po = new BattHealthStatusEvalPo();
        pojos.add(po);
        Mockito.doReturn(pojos).when(battHealthStatusEvalMapper).selectLastHealthEvalByMoIds(Mockito.anyList());
        Assert.assertSame(0, battHealthStatusEvalDomainImpl.selectLastByMoIds(Arrays.asList("12")).size());
    }

    @Test
    public void selectLastByMoIdsTestEx() throws Exception {
        try {
            List<BattHealthStatusEvalPo> pojos = new ArrayList<>();
            BattHealthStatusEvalPo po = new BattHealthStatusEvalPo();
            pojos.add(po);
            Mockito.doThrow(new UedmException(-1,"")).when(battHealthStatusEvalMapper).selectLastHealthEvalByMoIds(Mockito.anyList());
            Assert.assertSame(0, battHealthStatusEvalDomainImpl.selectLastByMoIds(Arrays.asList("12")).size());
        } catch (UedmException e)
        {
            Assert.assertEquals(-1,e.getErrorId().intValue());
        }

    }

    @Test
    public void selectEvalDetailTest() throws Exception
    {

        try {
            SelectEvalDetailDto selectEvalDetailDto=new SelectEvalDetailDto();
            selectEvalDetailDto.setEvaluateTime("11");
            ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","4",1,10);

            List<BattHealthStatusEvalPo> pojos = new ArrayList<>();
            BattHealthStatusEvalPo po = new BattHealthStatusEvalPo();
            pojos.add(po);
            PageInfo<BattHealthStatusEvalPo> pojosPageInfo = new PageInfo<>(pojos);
            Mockito.doThrow(new UedmException(-1,"1")).when(battHealthStatusEvalMapper).selectEvalDetail(Mockito.any());
            PageInfo<BattHealthStatusEvalPo> battHealthStatusEvalPoPageInfo = battHealthStatusEvalDomainImpl.selectEvalDetail(selectEvalDetailDto, serviceBaseInfoBean);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("An exception occurs when operating db", e.getMessage());
        }
    }

    @Test
    public void selectEvalDetailTest1() throws Exception
    {

        try {
            SelectEvalDetailDto selectEvalDetailDto=new SelectEvalDetailDto();
            ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","4",1,10);

            List<BattHealthStatusEvalPo> pojos = new ArrayList<>();
            BattHealthStatusEvalPo po = new BattHealthStatusEvalPo();
            pojos.add(po);
            PageInfo<BattHealthStatusEvalPo> pojosPageInfo = new PageInfo<>(pojos);
            Mockito.doThrow(new UedmException(-1,"1")).when(battHealthStatusEvalMapper).selectEvalDetail(Mockito.any());
            PageInfo<BattHealthStatusEvalPo> battHealthStatusEvalPoPageInfo = battHealthStatusEvalDomainImpl.selectEvalDetail(null, serviceBaseInfoBean);
        }
        catch (UedmException e)
        {
            Assert.assertEquals("params is blank", e.getMessage());
        }
    }

    @Test
    public void selectEvalDetailTest2() throws Exception
    {

        try {
            SelectEvalDetailDto selectEvalDetailDto=new SelectEvalDetailDto();
            selectEvalDetailDto.setEvaluateTime("11");
            selectEvalDetailDto.setOrder("soh");
            ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("1","2","4",1,10);

            battHealthStatusEvalDomainImpl.selectEvalDetail(new SelectEvalDetailDto(), serviceBaseInfoBean);
            List<BattHealthStatusEvalPo> pojos = new ArrayList<>();
            BattHealthStatusEvalPo po = new BattHealthStatusEvalPo();
            pojos.add(po);
            PageInfo<BattHealthStatusEvalPo> pojosPageInfo = new PageInfo<>(pojos);
            Mockito.doReturn(pojos).when(battHealthStatusEvalMapper).selectEvalDetail(Mockito.any());
            PageInfo<BattHealthStatusEvalPo> battHealthStatusEvalPoPageInfo = battHealthStatusEvalDomainImpl.selectEvalDetail(selectEvalDetailDto, serviceBaseInfoBean);
            Assert.assertEquals("1",String.valueOf(battHealthStatusEvalPoPageInfo.getTotal()));
        }
        catch (UedmException e)
        {
            Assert.assertEquals("params is blank", e.getMessage());
        }
    }

}