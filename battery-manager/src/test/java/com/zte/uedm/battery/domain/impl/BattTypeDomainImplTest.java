package com.zte.uedm.battery.domain.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.bean.ExtendAttributeBean;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.domain.AssetDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.AssetRpc;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.AssetModelAttributeRspBean;
import com.zte.uedm.battery.rpc.vo.AssetModelAttributeVo;
import com.zte.uedm.battery.rpc.vo.GroupAttributesBean;
import com.zte.uedm.battery.rpc.vo.Options;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryAttributeUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.consts.asset.AssetModelAttributeIdConstants;
import com.zte.uedm.common.enums.asset.AssetModelAttributeGroupEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BattTypeDomainImplTest
{
    @InjectMocks
    private BattTypeDomainImpl battTypeDomain;
    @Mock
    private AssetRpc assetRpc;
    @Mock
    private JsonService jsonService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private DataRedis dataRedis;
    @Mock
    private ConfigurationManagerRpcImpl cfgRpc;
    @Mock
    private ConfigurationRpc configurationRpc;
    @Mock
    private AssetDomain assetDomain;
    @Mock
    private BatteryAttributeUtils batteryAttributeUtils;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn("").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void selectLevelTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battTypeDomain.selectLevels(null).getTotal());
    }

    @Test
    public void selectLevelTest_normal() throws Exception
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("", "");

        List<AssetModelAttributeRspBean> list = new ArrayList<>();

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.selectAttributeByAssetModelId(Mockito.anyString(), Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(),Mockito.any());

        Assert.assertSame(0L, battTypeDomain.selectLevels(serviceBean).getTotal());
    }

    @Test
    public void selectLevelTest_rpc_exception() throws Exception
    {
        try {
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("", "");

            List<AssetModelAttributeRspBean> list = new ArrayList<>();

            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.selectAttributeByAssetModelId(Mockito.anyString(), Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(),Mockito.any());
            battTypeDomain.selectLevels(serviceBean);
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }

    @Test
    public void selectLevelTest_normal2() throws Exception
    {
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("", "");

        List<AssetModelAttributeRspBean> list = new ArrayList<>();
        AssetModelAttributeRspBean assetModelAttributeRspBean = new AssetModelAttributeRspBean();
        List<GroupAttributesBean> groupAttributes = new ArrayList<>();
        GroupAttributesBean groupAttributesBean = new GroupAttributesBean();
        List<AssetModelAttributeVo> attributes = new ArrayList<>();
        AssetModelAttributeVo assetModelAttributeVo = new AssetModelAttributeVo();
        assetModelAttributeVo.setId(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BATT_TYPE);
        attributes.add(assetModelAttributeVo);

        AssetModelAttributeVo assetModelAttributeVo2 = new AssetModelAttributeVo();
        assetModelAttributeVo2.setId(AssetModelAttributeIdConstants.ASSET_MODEL_ATTRIBUTE_ID_BATT_TYPE);
        List<Options> options = new ArrayList<>();
        Options options1 = new Options();
        options1.setLabel("s");
        options1.setValue("da");
        options.add(options1);
        assetModelAttributeVo2.setOptions(options);
        attributes.add(assetModelAttributeVo2);

        AssetModelAttributeVo assetModelAttributeVo3 = new AssetModelAttributeVo();
        assetModelAttributeVo3.setId("das");
        attributes.add(assetModelAttributeVo3);

        groupAttributesBean.setAttributes(attributes);
        groupAttributesBean.setAttributeGroup(AssetModelAttributeGroupEnum.getSpecialTypeKey());
        groupAttributes.add(groupAttributesBean);
        assetModelAttributeRspBean.setGroupAttributes(groupAttributes);
        assetModelAttributeRspBean.setGroupType(AssetModelAttributeGroupEnum.getSpecialTypeKey());
        list.add(assetModelAttributeRspBean);

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.selectAttributeByAssetModelId(Mockito.anyString(), Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        Mockito.doReturn("da").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(),Mockito.anyString());
        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(),Mockito.any());

        Assert.assertSame(1L, battTypeDomain.selectLevels(serviceBean).getTotal());
    }

    @Test
    public void selectBattTypesTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battTypeDomain.selectBattTypes(null).getTotal());
    }

    @Test
    public void selectBattTypesTest_normal() throws Exception
    {
        try {
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("77", "zh-CN");
            Assert.assertEquals(4, battTypeDomain.selectBattTypes(serviceBean).getTotal());
            ServiceBaseInfoBean bean = new ServiceBaseInfoBean("77", "en-US");
            Assert.assertEquals(4, battTypeDomain.selectBattTypes(bean).getTotal());
        }catch (UedmException e)
        {
            Assert.assertEquals(e.getClass(),UedmException.class);
        }
    }


    @Test
    public void getBattSeriesKeyByUnknownTest() throws Exception
    {
        Assert.assertSame("2", battTypeDomain.getBattSeriesKeyByUnknown());
    }

    @Test
    public void getBatteryTypeByMoIdTest() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest1() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(1001l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest2() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"PbAc\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest3() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"LFP\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest4() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> healthMap = new HashMap<>();
        healthMap.put("value", "0");
        batteryStdRealData.put("batt.health", healthMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest5() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest_assetPbac() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        IdNameBean battType = new IdNameBean();
        battType.setId("batt");
        battType.setName("0");
        Mockito.doReturn(Arrays.asList(battType)).when(assetDomain).selectBattTypeByMoIds(Mockito.anyList());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest_assetLi() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        IdNameBean battType = new IdNameBean();
        battType.setId("batt");
        battType.setName("1");
        Mockito.doReturn(Arrays.asList(battType)).when(assetDomain).selectBattTypeByMoIds(Mockito.anyList());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTest_assetUnknown() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        IdNameBean battType = new IdNameBean();
        battType.setId("batt");
        battType.setName("2");
        Mockito.doReturn(Arrays.asList(battType)).when(assetDomain).selectBattTypeByMoIds(Mockito.anyList());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdTestEmpty() throws UedmException
    {
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(new ArrayList<>());
        pageInfo.setTotal(0l);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoId();
        Assert.assertSame(0, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdFilterLoopTest() throws UedmException {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(1L);
        Mockito.doReturn(pageInfo).when(cfgRpc).selectBattExtendAttribute(Mockito.any());
        ExtendAttributeBean extendAttributeBean1 = new ExtendAttributeBean();
        extendAttributeBean1.setName("IsLoop");
        extendAttributeBean1.setValue("Yes");
        Mockito.doReturn(Collections.singletonList(extendAttributeBean1)).when(jsonService).jsonToObject(Mockito.any(), Mockito.any(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdFilterLoop();
        Assert.assertEquals(result.size(), 1);

        ExtendAttributeBean extendAttributeBean2 = new ExtendAttributeBean();
        extendAttributeBean2.setName("isLoop");
        extendAttributeBean2.setValue("No");
        Mockito.doReturn(Collections.singletonList(extendAttributeBean2)).when(jsonService).jsonToObject(Mockito.any(), Mockito.any(), Mockito.any());
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeByMoIdFilterLoop();
        Assert.assertEquals(result1.size(), 1);
    }

    /* Started by AICoder, pid:ce05c15976254691be502a5574f3a0dc */
    @Test
    public void filterLoopTest() throws UedmException {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("mo-fjdaofvna");
        Assert.assertEquals(battTypeDomain.filterLoop(Collections.singletonList(idNameBean)).size(), 1);
        idNameBean.setName("{\"is_loop\":\"1\"}");
        Mockito.when(batteryAttributeUtils.getIsLoopByNameBean(Mockito.any())).thenReturn(false);
        Assert.assertEquals(battTypeDomain.filterLoop(Collections.singletonList(idNameBean)).size(), 1);
        Mockito.when(batteryAttributeUtils.getIsLoopByNameBean(Mockito.any())).thenReturn(true);
        Assert.assertEquals(battTypeDomain.filterLoop(Collections.singletonList(idNameBean)).size(), 0);
    }
    @Test
    public void getBatteryTypeTest() throws UedmException {
        List<BattTypeBean> result = new ArrayList<>();
        Map<String, IdNameBean> assetInstanceMap = new HashMap<>();
        IdNameBean bean = new IdNameBean();
        Mockito.when(batteryAttributeUtils.getBatteryTypeCodeByNameBean(Mockito.any())).thenReturn("2");
        battTypeDomain.getBatteryType(bean, result, assetInstanceMap);
        Assert.assertEquals(result.size(), 1);

        Mockito.when(batteryAttributeUtils.getBatteryTypeCodeByNameBean(Mockito.any())).thenReturn("3");
        Mockito.when(dataRedis.selectRealData(Mockito.anyString(), Mockito.anyList())).thenReturn(new HashMap<>());
        result = new ArrayList<>();
        battTypeDomain.getBatteryType(bean, result, assetInstanceMap);
        Assert.assertEquals(result.size(), 1);
    }
    /* Ended by AICoder, pid:ce05c15976254691be502a5574f3a0dc */



    @Test
    public void getBatteryTypeByMoIds() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(new ArrayList<>(),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(new ArrayList<>(),true);
        Assert.assertSame(0, result.size());
        Assert.assertSame(0, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds1() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(1001l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"),true);
        Assert.assertSame(1, result.size());
        Assert.assertSame(1, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds2() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"PbAc\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"), true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"), true);
        Assert.assertSame(1, result.size());
        Assert.assertSame(1, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds3() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"LFP\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"),true);
        Assert.assertSame(1, result.size());
        Assert.assertSame(1, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds4() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> healthMap = new HashMap<>();
        healthMap.put("value", "0");
        batteryStdRealData.put("batt.health", healthMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"),true);
        Assert.assertSame(1, result.size());
        Assert.assertSame(1, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds5() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(idNameBeans);
        pageInfo.setTotal(10l);
        Mockito.doReturn(idNameBeans).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"),true);
        Assert.assertSame(1, result.size());
        Assert.assertSame(1, result1.size());
    }

    @Test
    public void getBatteryTypeByMoIds6() throws UedmException
    {
        PageInfo<IdNameBean> pageInfo = new PageInfo<>(new ArrayList<>());
        pageInfo.setTotal(0l);
        Mockito.doReturn(new ArrayList<>()).when(cfgRpc).selectAllBatteryExtendAttribute(Mockito.any());
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIds(Arrays.asList("1"),true);
        List<BattTypeBean> result1 = battTypeDomain.getBatteryTypeWithTest(Arrays.asList("1"),true);
        Assert.assertSame(0, result.size());
        Assert.assertSame(0, result1.size());
    }


    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(new ArrayList<>(),true,idNameBeans);
        Assert.assertSame(0, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute1() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"),true, idNameBeans);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute2() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"PbAc\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"), true, idNameBeans);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute3() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("{\"name\":\"Type\",\"value\":\"LFP\"}");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"),true,idNameBeans);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute4() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> healthMap = new HashMap<>();
        healthMap.put("value", "0");
        batteryStdRealData.put("batt.health", healthMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"),true,idNameBeans);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute5() throws UedmException
    {
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("batt");
        idNameBean.setName("");
        List<IdNameBean> idNameBeans = Arrays.asList(idNameBean);
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"), true,idNameBeans);
        Assert.assertSame(1, result.size());
    }

    @Test
    public void getBatteryTypeByMoIdsAndExtendAttribute6() throws UedmException
    {
        Map<String, Map<String, String>> batteryStdRealData = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("value", "0");
        batteryStdRealData.put("batt.type", typeMap);
        Mockito.doReturn(batteryStdRealData).when(dataRedis).selectRealData(Mockito.anyString(), Mockito.any());
        List<BattTypeBean> result = battTypeDomain.getBatteryTypeByMoIdsAndExtendAttribute(Arrays.asList("1"), true,new ArrayList<>());
        Assert.assertSame(0, result.size());
    }

    @Test
    public void getBatteryTypeFromExtend() {
        BatteryBaseInfoBean batteryBaseInfoBean = new BatteryBaseInfoBean();
        batteryBaseInfoBean.setId("id");
        // 从工程配置中获取到电池类型
        batteryBaseInfoBean.setBattCfgType("0");
        BattTypeBean type1 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.PBAC, type1.getBattType());
        batteryBaseInfoBean.setBattCfgType("1");
        BattTypeBean type2 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.LFP, type2.getBattType());

        // 从资产获取
        batteryBaseInfoBean.setBattCfgType(null);
        batteryBaseInfoBean.setBattAssetType("0");
        BattTypeBean type3 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.PBAC, type3.getBattType());
        batteryBaseInfoBean.setBattAssetType("1");
        BattTypeBean type4 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.LFP, type4.getBattType());
        batteryBaseInfoBean.setBattAssetType("3");
        BattTypeBean type5 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        //Assert.assertEquals(BattTypeEnum.UNKNOWN, type5.getBattType());

        // 从测点获取
        batteryBaseInfoBean.setBattAssetType(null);
        batteryBaseInfoBean.setBattRedisType("0");
        BattTypeBean type6 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.PBAC, type6.getBattType());
        batteryBaseInfoBean.setBattRedisType("1");
        BattTypeBean type7 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.LFP, type7.getBattType());

        batteryBaseInfoBean.setBattRedisType(null);
        BattTypeBean type8 = battTypeDomain.getBatteryTypeFromExtend(batteryBaseInfoBean);
        Assert.assertEquals(BattTypeEnum.UNKNOWN, type8.getBattType());
    }
//    @Test
//    public void  selectAllBatteryTest()
//    {
//        List<IdNameBean> res = battTypeDomain.selectAllBattery(new ArrayList<>());
//        Assert.assertEquals(0, res.size());
//    }
//
//    @Test
//    public void  selectAllBatteryTest1() throws UedmException
//    {
//        Mockito.when(cfgRpc.getBattExtendAttributeByIds(Mockito.any())).thenThrow(new UedmException(-1, "1"));
//        List<IdNameBean> res = battTypeDomain.selectAllBattery(Arrays.asList("1"));
//        Assert.assertEquals(0, res.size());
//    }
}
