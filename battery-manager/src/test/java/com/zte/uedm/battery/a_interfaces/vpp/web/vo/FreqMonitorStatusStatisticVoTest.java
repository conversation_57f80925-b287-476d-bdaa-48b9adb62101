package com.zte.uedm.battery.a_interfaces.vpp.web.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/* Started by AICoder, pid:8ae43q9da09611314edd0baae0c8760085490c38 */
public class FreqMonitorStatusStatisticVoTest {

    @Test
    public void FreqMonitorStatusStatisticVoTest() throws Exception {
        FreqMonitorStatusStatisticVo freqMonitorStatusStatisticVo = new FreqMonitorStatusStatisticVo();
        PojoTestUtil.TestForPojo(freqMonitorStatusStatisticVo.getClass());
        Assert.assertEquals(freqMonitorStatusStatisticVo.toString(), new FreqMonitorStatusStatisticVo().toString());
    }
}
/* Ended by AICoder, pid:8ae43q9da09611314edd0baae0c8760085490c38 */
