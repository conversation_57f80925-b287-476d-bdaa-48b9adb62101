package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BatteryCurrentStorageDomin;
import com.zte.uedm.battery.domain.LeadAcidBatteryDomain;
import com.zte.uedm.battery.domain.impl.*;
import com.zte.uedm.battery.domain.po.BackupPowerBean;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.pma.bean.HistoryAiBean;
import com.zte.uedm.pma.service.PmaService;
import com.zte.uedm.service.config.optional.MocOptional;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BatteryCurrentDomainImplTest {
    @Mock
    private LeadAcidBatteryDomain leadAcidBatteryDomain;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private PmaService pmaService;
    @Mock
    private BatteryCurrentStorageDomin batteryCurrentStorageDomin;
    @Mock
    private BatteryMaximumChargingCapacityDomainImpl batteryMaximumChargingCapacityDomainImpl;
    @Mock
    private BattLifeEvalDomain battLifeEvalDomain;
    @Mock
    private LeadAcidBatteryDomainImpl leadAcidBatteryDomainImpl;
    @Mock
    private BackupPowerHistoryAlarmDomainImpl backupPowerHistoryAlarmDomain;
    @Mock
    private ConfigurationManagerRpcImpl cfgRpc;
    @Mock
    private BattTypeDomainImpl battTypeDomainImpl;


    @InjectMocks
    private BatteryCurrentDomainImpl batteryCurrentDomain;

    @Test
    public void countBatteryCurrent() throws UedmException {
        // Setup
        final MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("id");
        moObjectConfiguration.setName("name");
        moObjectConfiguration.setPath("path");
        moObjectConfiguration.setPathId("pathId");
        moObjectConfiguration.setParentId("parentId");
        final List<MoObjectConfiguration> moObjectConfigurations = Arrays.asList(moObjectConfiguration);

        List<HistoryAiBean> historyAiBeans = new ArrayList<>();
        HistoryAiBean historyAiBean1 = new HistoryAiBean();
        historyAiBean1.setResId("id1");
        historyAiBean1.setAvgValue("-12");
        HistoryAiBean historyAiBean2 = new HistoryAiBean();
        historyAiBean2.setResId("id2");
        historyAiBean2.setAvgValue("12");
        historyAiBeans.add(historyAiBean1);
        historyAiBeans.add(historyAiBean2);

        List<DeviceEntity> batteryMoObjectBeans = new ArrayList<>();
        DeviceEntity monitorObjectBean1 = new DeviceEntity();
        monitorObjectBean1.setId("id1");
        DeviceEntity monitorObjectBean2 = new DeviceEntity();
        monitorObjectBean2.setId("id2");
        batteryMoObjectBeans.add(monitorObjectBean1);
        batteryMoObjectBeans.add(monitorObjectBean2);

        DeviceEntity monitorObjectBean = new DeviceEntity();
        monitorObjectBean.setMoc(MocOptional.BATTERY_SET.getId());
        DeviceEntity monitorObjectBeandcdp = new DeviceEntity();
        monitorObjectBeandcdp.setMoc(MocOptional.BATTERY.getId());
        List<DeviceEntity> monitorObjectBeans = Arrays.asList(monitorObjectBean, monitorObjectBeandcdp);
        List<IdNameBean> list = new ArrayList<>();
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("id1");
        IdNameBean idNameBean2 = new IdNameBean();
        idNameBean2.setId("id2");
        list.add(idNameBean);
        list.add(idNameBean2);

        when(battTypeDomainImpl.filterLoop(Mockito.anyList())).thenReturn(list);
            when(leadAcidBatteryDomainImpl.getMoListBySp(Mockito.any())).thenReturn(monitorObjectBeans);
            when(leadAcidBatteryDomain.getMoListBySpAndMoc(Mockito.any(), Mockito.any())).thenReturn(batteryMoObjectBeans);
            when(pmaService.selectDataByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(historyAiBeans);
            when(batteryCurrentStorageDomin.addBattCurrWResult(Mockito.any())).thenReturn(2);
            Integer result = batteryCurrentDomain.countBatteryCurrent(moObjectConfigurations, MocOptional.DC_POWER.getId());
            // Verify the results
            assertEquals((Integer) 3, result);




    }
    @Test
    public void dealWithHistoryAiBeansTest() throws UedmException {
        List<HistoryAiBean> historyAiBeans = new ArrayList<>();
        Map<String, BigDecimal > batteryHistoryAiMap = new HashMap<>();

        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setResId("resId");
        historyAiBean.setAvgValue("-12");
        historyAiBeans.add(historyAiBean);

        batteryCurrentDomain.dealWithHistoryAiBeans(historyAiBeans,batteryHistoryAiMap);
        assertEquals(1, historyAiBeans.size());
    }
    @Test
    public void filterLoopBatterTest() throws UedmException {
        // Setup
        BackupPowerBean backupPowerBean = new BackupPowerBean("id", "name");
        DeviceEntity monitorObjectBean = new DeviceEntity();
        monitorObjectBean.setId("id");
        monitorObjectBean.setName("name");
        DeviceEntity monitorObjectBean1 = new DeviceEntity();
        monitorObjectBean1.setId("id1");
        monitorObjectBean1.setName("name");
        DeviceEntity monitorObjectBean2 = new DeviceEntity();
        monitorObjectBean2.setId("id2");
        monitorObjectBean2.setName("name");
        List<DeviceEntity> monitorObjectBeans = new ArrayList<>();
        monitorObjectBeans.add(monitorObjectBean);
        monitorObjectBeans.add(monitorObjectBean1);
        monitorObjectBeans.add(monitorObjectBean2);

        // Configure MonitorManagerRpcImpl.getAnalogGr(...).
        List<IdNameBean> allBattery = new ArrayList<>();
        List<IdNameBean> result = new ArrayList<>();
        IdNameBean idNameBean = new IdNameBean();
        idNameBean.setId("id");
        idNameBean.setName("{\"name\":\"IsLoop\",\"value\":\"no\"}");
        IdNameBean idNameBean1 = new IdNameBean();
        idNameBean.setId("id1");
        idNameBean.setName("{\"name\":\"IsLoop\",\"value\":\"yes\"}");
        allBattery.add(idNameBean);
        allBattery.add(idNameBean1);
        result.add(idNameBean);
        when(cfgRpc.selectAllBatteryExtendAttribute(any())).thenReturn(allBattery);
        when(battTypeDomainImpl.filterLoop(allBattery)).thenReturn(result);
        // Run the test
        List<DeviceEntity> resultList = batteryCurrentDomain.filterLoopBatter(monitorObjectBeans);

        Assert.assertEquals(1, resultList.size());
        // Verify the results
    }

    @Test
    public void filterBatteryTest() throws UedmException {
        // 设置电池Mo对象的属性
        MonitorObjectBean battery1 = new MonitorObjectBean();
        battery1.setId("battery1");

        MonitorObjectBean battery2 = new MonitorObjectBean();
        battery2.setId("battery2");

        List<MonitorObjectBean> batteryMoObjectBeans = new ArrayList<>();
        batteryMoObjectBeans.add(battery1);
        batteryMoObjectBeans.add(battery2);

        // 设置电池寿命评估结果
        Map<String, Integer> lifeMap = new HashMap<>();
        lifeMap.put("battery1", 60);
        lifeMap.put("battery2", 80);

        // 设置告警信息列表
        Map<String, List<Alarm>> allAlarmsMap = new HashMap<>();
        List<Alarm> alarms1 = new ArrayList<>();
        List<Alarm> alarms2 = new ArrayList<>();
        allAlarmsMap.put("battery1", alarms1);
        allAlarmsMap.put("battery2", alarms2);

        // mock所需的方法
        when(battLifeEvalDomain.getBattLifeMap(Mockito.any())).thenReturn(lifeMap);


        when(batteryMaximumChargingCapacityDomainImpl.getAlarmListByMoIdList(Mockito.any())).thenReturn(allAlarmsMap);
        when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(eq("battery1"), eq(lifeMap), eq(allAlarmsMap))).thenReturn(false);
        when(batteryMaximumChargingCapacityDomainImpl.isBatteryValid(eq("battery2"), eq(lifeMap), eq(allAlarmsMap))).thenReturn(true);

        // 执行过滤电池操作
        List<MonitorObjectBean> result = batteryCurrentDomain.filterBattery(batteryMoObjectBeans);

        // 验证结果是否正确
        assertEquals(1, result.size());
        assertEquals("battery2", result.get(0).getId());
    }

    @Test
    public void getSiteAlrmTimeByMoListTest() {
        // Create input data
        String id = "123";
        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity mo1 = new DeviceEntity();
        mo1.setMoc("acdp");
        DeviceEntity mo2 = new DeviceEntity();
        mo2.setMoc("dcdp");
        moList.add(mo1);
        moList.add(mo2);

        // Create mock objects
        Alarm alarm1 = new Alarm();
        alarm1.setAlarmraisedtime(1626061200000L);
        alarm1.setAlarmshowtime("2021-07-12 12:00:00");
        alarm1.setAlarmclearedtime(1626064800000L);
        alarm1.setPositionname("AC Power Outage");
        alarm1.setAlarmcode(50031026L);
        Alarm alarm2 = new Alarm();
        alarm2.setAlarmraisedtime(1626064800000L);
        alarm2.setAlarmshowtime("2021-07-12 13:00:00");
        alarm2.setAlarmclearedtime(1626068400000L);
        alarm2.setPositionname("DC Power Outage");
        alarm2.setAlarmcode(50004004L);
        List<Alarm> acdpAlarmList = new ArrayList<>();
        acdpAlarmList.add(alarm1);
        List<Alarm> dcdpAndBattPackList = new ArrayList<>();
        dcdpAndBattPackList.add(alarm2);
        Mockito.when(backupPowerHistoryAlarmDomain.getALarmListByMolist(Mockito.anyList(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyString())).thenReturn(acdpAlarmList, dcdpAndBattPackList);
        Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-07-19 12:00:00");
        Mockito.when(dateTimeService.getStrMillisecondTime(Mockito.anyLong())).thenReturn("2021-07-12 12:00:00");


        // Call the method under test
        Map<String, String> result = batteryCurrentDomain.getSiteAlrmTimeByMoList(id, moList);

        // Verify the result
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertTrue(result.containsKey("2021-07-12 12:00:00"));
        Assert.assertEquals("2021-07-12 12:00:00", result.get("2021-07-12 12:00:00"));
        Mockito.verify(backupPowerHistoryAlarmDomain, Mockito.times(3)).getALarmListByMolist(Mockito.anyList(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyString());
        Mockito.verify(dateTimeService, Mockito.times(3)).getStrMillisecondTime(Mockito.anyLong());
    }
    @Test
    public void getSiteAlrmTimeByMoListFromMonthTest() throws ParseException {
        // Create input data
        String id = "123";
        List<DeviceEntity> moList = new ArrayList<>();
        DeviceEntity mo1 = new DeviceEntity();
        mo1.setMoc("acdp");
        DeviceEntity mo2 = new DeviceEntity();
        mo2.setMoc("dcdp");
        moList.add(mo1);
        moList.add(mo2);

        // Create mock objects
        Alarm alarm1 = new Alarm();
        alarm1.setAlarmraisedtime(1626061200000L);
        alarm1.setAlarmshowtime("2021-07-12 12:00:00");
        alarm1.setAlarmclearedtime(1626064800000L);
        alarm1.setPositionname("AC Power Outage");
        alarm1.setAlarmcode(50031026L);
        Alarm alarm2 = new Alarm();
        alarm2.setAlarmraisedtime(1626064800000L);
        alarm2.setAlarmshowtime("2021-07-12 13:00:00");
        alarm2.setAlarmclearedtime(1626068400000L);
        alarm2.setPositionname("DC Power Outage");
        alarm2.setAlarmcode(50004004L);
        List<Alarm> acdpAlarmList = new ArrayList<>();
        acdpAlarmList.add(alarm1);
        List<Alarm> dcdpAndBattPackList = new ArrayList<>();
        dcdpAndBattPackList.add(alarm2);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String time = sdf.format(date);
        Mockito.when(backupPowerHistoryAlarmDomain.getALarmListByMolist(Mockito.anyList(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyString())).thenReturn(acdpAlarmList, dcdpAndBattPackList);
        Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-07-19 12:00:00");
        Mockito.when(dateTimeService.getStrMillisecondTime(Mockito.anyLong())).thenReturn(time);


        // Call the method under test
        Map<String, String> result = batteryCurrentDomain.getSiteAlrmTimeByMoListFromMonth(id, moList);

        // Verify the result
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertTrue(result.containsKey(time));
        Assert.assertEquals(time, result.get(time));
        Mockito.verify(backupPowerHistoryAlarmDomain, Mockito.times(3)).getALarmListByMolist(Mockito.anyList(), Mockito.anyList(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyString());
        Mockito.verify(dateTimeService, Mockito.times(3)).getStrMillisecondTime(Mockito.anyLong());
        }

    @Test
    public void obtainCurrentTest(){
        List<HistoryAiBean> historyAiBeans = new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setTimeRange("2021-09-24 16:55:00 - 2021-10-01 16:55:00");
        historyAiBeans.add(historyAiBean);
        List<String> moIds = new ArrayList<>();
        Map<String, String> timeMap = new HashMap<>();
        Map<String, String> timeMap1 = new HashMap<>();

        // Test case 1 - no timeMap
        Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-10-01 16:55:00");
        batteryCurrentDomain.obtainCurrent(historyAiBeans, moIds, timeMap,timeMap1);
        assertEquals("2021-09-24 16:55:00 - 2021-10-01 16:55:00", historyAiBeans.get(0).getTimeRange());
        assertEquals(0, moIds.size());

        // Test case 2 - with timeMap
        timeMap.put("2021-09-26 10:00:00", "2021-09-27 12:00:00");
        timeMap.put("2021-09-28 08:00:00", "2021-09-29 14:00:00");
        timeMap1.put("2021-09-25 10:00:00", "2021-09-26 09:00:00");
        Mockito.when(dateTimeService.getCurrentTime()).thenReturn("2021-09-30 15:30:00");
        historyAiBeans.clear();
        moIds.clear();
        HistoryAiBean historyAiBean1 = new HistoryAiBean();
        historyAiBean1.setTimeRange("2021-09-24 16:55:00 - 2021-09-26 10:00:00");
        historyAiBeans.add(historyAiBean1);
        HistoryAiBean historyAiBean2 = new HistoryAiBean();
        historyAiBean2.setTimeRange("2021-09-27 12:00:00 - 2021-09-28 08:00:00");
        historyAiBeans.add(historyAiBean2);
        HistoryAiBean historyAiBean3 = new HistoryAiBean();
        historyAiBean3.setTimeRange("2021-09-28 08:00:00 - 2021-09-29 14:00:00");
        historyAiBeans.add(historyAiBean3);
        batteryCurrentDomain.obtainCurrent(historyAiBeans, moIds, timeMap,timeMap1);
        assertEquals("2021-09-24 16:55:00 - 2021-09-26 10:00:00", historyAiBeans.get(0).getTimeRange());
        assertEquals("2021-09-27 12:00:00 - 2021-09-28 08:00:00", historyAiBeans.get(1).getTimeRange());
        assertEquals("2021-09-28 08:00:00 - 2021-09-29 14:00:00", historyAiBeans.get(2).getTimeRange());
        assertEquals(0, moIds.size());
    }

}
