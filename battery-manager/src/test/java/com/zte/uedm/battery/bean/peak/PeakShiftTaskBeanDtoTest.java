package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.battery.bean.peak.PeakShiftTaskBeanDto;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class PeakShiftTaskBeanDtoTest {

    @Test
    public void PeakShiftTaskBeanDtoTest()
    {
        PeakShiftTaskBeanDto peakShiftTaskBeanDto = new PeakShiftTaskBeanDto();

        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setDescription("da");
        List<String> list = new ArrayList<>();
        list.add("da");
        peakShiftTaskBeanDto.setDeviceIds(list);
        peakShiftTaskBeanDto.setFileId("da");
        peakShiftTaskBeanDto.toString();
        peakShiftTaskBeanDto.checkBean();
        Assert.assertEquals(1,peakShiftTaskBeanDto.getDeviceIds().size());
    }
}
