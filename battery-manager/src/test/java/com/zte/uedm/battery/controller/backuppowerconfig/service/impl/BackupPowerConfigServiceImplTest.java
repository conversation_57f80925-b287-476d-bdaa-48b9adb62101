package com.zte.uedm.battery.controller.backuppowerconfig.service.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.a_domain.aggregate.collector.model.entity.CollectorEntity;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceCollectorRelationEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.CollectorCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.BackupPowerConfigPo;
import com.zte.uedm.battery.bean.MocIdsVO;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerconfig.dto.BackupPowerConfigUpdateDto;
import com.zte.uedm.battery.controller.backuppowerconfig.vo.BackupPowerConfigVo;
import com.zte.uedm.battery.dao.BackupPowerConfigDao;
import com.zte.uedm.battery.domain.BattAssetAttributeDomain;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.enums.BackupPowerScenarioEnum;
import com.zte.uedm.battery.enums.battlife.BattLifeListDimEnums;
import com.zte.uedm.battery.mapper.BackupPowerThresholdMapper;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.module.bean.ModuleBean;
import com.zte.uedm.common.configuration.monitor.device.bean.MonitorDeviceBaseBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorDeviceObjectRelationBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doThrow;

public class BackupPowerConfigServiceImplTest
{
    @InjectMocks
    private BackupPowerConfigServiceImpl backupPowerConfigService;
    @Mock
    private BackupPowerConfigDao backupPowerConfigDao;
    @Mock
    private MessageSenderService msgSenderService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BattAssetAttributeDomain battAssetAttributeDomain;
    @Mock
    private BattAssetDomain battAssetDomain;
    @Mock
    private DeviceCacheManager deviceCacheManager;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;
    @Mock
    private CollectorCacheManager collectorCacheManager;
    @Mock
    private BackupPowerThresholdMapper backupPowerThresholdMapper;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        doNothing().when(msgSenderService).sendMsgAsync(any(), any());
        doReturn("123").when(i18nUtils).getMapFieldByLanguageOption(any(),any());
    }

    @Test
    public void selectBackupPowerConfig_normal1() throws UedmException
    {
        doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
        doNothing().when(msgSenderService).sendMsgAsync(any(), any());
        doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());
        List<BackupPowerConfigVo> list = backupPowerConfigService.selectBackupPowerConfig("admin", "zh_CN");
        //Assert.assertEquals(12, list.size());
    }

    @Test
    public void selectBackupPowerConfig_normal2() throws UedmException
    {
        BackupPowerConfigPo po1 = new BackupPowerConfigPo();
        po1.setId("name");
        po1.setName("Name");
        po1.setSequence(1);
        po1.setUserName("admin");
        BackupPowerConfigPo po2 = new BackupPowerConfigPo();
        po2.setId("battType");
        po2.setName("Type");
        po2.setSequence(2);
        po2.setUserName("admin");

        doReturn(Arrays.asList(po1, po2,po1, po2,po1, po2,po1, po2,po1, po2,po1)).when(backupPowerConfigDao).selectBackupPowerConfig(any());
        Map<String, Boolean> attributeMap = new HashMap<>();
        attributeMap.put("82fafb6d-BatteryType", false);
        doReturn(attributeMap).when(battAssetAttributeDomain).selectAttributeEnable(anyList(), any());
        List<BackupPowerConfigVo> list = backupPowerConfigService.selectBackupPowerConfig("admin", "zh_CN");
        //Assert.assertEquals(12, list.size());
    }

    @Test
    public void selectBackupPowerConfig_exp1() throws UedmException
    {
        try {
            doThrow(new UedmException(-1, "77")).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            List<BackupPowerConfigVo> list = backupPowerConfigService.selectBackupPowerConfig("admin", "zh_CN");
            Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
        }
        catch (Exception e)
        {
            Assert.assertEquals("77",e.getMessage());
        }
    }

    @Test
    public void selectBackupPowerConfig_exp2() throws UedmException
    {
        try {
            doThrow(new RuntimeException()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            List<BackupPowerConfigVo> list = backupPowerConfigService.selectBackupPowerConfig("admin", "zh_CN");
            Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
        }
        catch (Exception e)
        {
            Assert.assertSame(null,e.getMessage());
        }
    }

    @Test
    public void selectBackupPowerConfig() throws UedmException {
            List<BackupPowerConfigPo> backupPowerConfigPos = new ArrayList<>();
            BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
            backupPowerConfigPo.setName("{\"en_US\":\"Device Type\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo);
            BackupPowerConfigPo backupPowerConfigPo1 = new BackupPowerConfigPo();
            backupPowerConfigPo1.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo1);
            BackupPowerConfigPo backupPowerConfigPo2 = new BackupPowerConfigPo();
            backupPowerConfigPo2.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo2);
            BackupPowerConfigPo backupPowerConfigPo3 = new BackupPowerConfigPo();
            backupPowerConfigPo3.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo3);
            BackupPowerConfigPo backupPowerConfigPo4 = new BackupPowerConfigPo();
            backupPowerConfigPo4.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo4);
            BackupPowerConfigPo backupPowerConfigPo5 = new BackupPowerConfigPo();
            backupPowerConfigPo5.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo5);
            BackupPowerConfigPo backupPowerConfigPo6 = new BackupPowerConfigPo();
            backupPowerConfigPo6.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo6);
            BackupPowerConfigPo backupPowerConfigPo7 = new BackupPowerConfigPo();
            backupPowerConfigPo7.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo7);
            BackupPowerConfigPo backupPowerConfigPo8 = new BackupPowerConfigPo();
            backupPowerConfigPo8.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo8);
            BackupPowerConfigPo backupPowerConfigPo9 = new BackupPowerConfigPo();
            backupPowerConfigPo9.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo9);
            BackupPowerConfigPo backupPowerConfigPo10 = new BackupPowerConfigPo();
            backupPowerConfigPo10.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo10);
            BackupPowerConfigPo backupPowerConfigPo11 = new BackupPowerConfigPo();
            backupPowerConfigPo11.setName("{\"en_US\":\"Device\",\"zh_CN\":\"设备类型\"}");
            backupPowerConfigPos.add(backupPowerConfigPo11);
            when(backupPowerConfigDao.selectBackupPowerConfig(any())).thenReturn(backupPowerConfigPos);
            List<BackupPowerConfigVo> list = backupPowerConfigService.selectBackupPowerConfig("admin", "zh_CN");
            Assert.assertNotNull(list);
    }

    @Test
    public void updateBackupPowerConfig_check_id_given() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(1);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(2);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-305, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBackupPowerConfig_check_sequence_given() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(9);
            dto1.setEnable(true);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(8);
            dto2.setEnable(false);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-305, ue.getErrorId().intValue());
        }
    }


    @Test
    public void updateBackupPowerConfig_check_enable() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(6);
            dto1.setEnable(false);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(1);
            dto1.setEnable(false);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-208, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBackupPowerConfig_normal() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(3);
            dto1.setEnable(true);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(1);
            dto1.setEnable(true);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            doReturn(2).when(backupPowerConfigDao).updateBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
            Assert.assertEquals(2, num.intValue());
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-305, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBackupPowerConfig_check_repeat() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(1);
            dto1.setEnable(true);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(111);
            dto1.setEnable(true);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            doReturn(2).when(backupPowerConfigDao).updateBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
            Assert.assertEquals(2, num.intValue());
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-302, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBackupPowerConfig_exp() throws UedmException
    {
        try
        {
            BackupPowerConfigUpdateDto dto1 = new BackupPowerConfigUpdateDto();
            dto1.setDimId("model");
            dto1.setSequence(3);
            dto1.setEnable(true);
            BackupPowerConfigUpdateDto dto2 = new BackupPowerConfigUpdateDto();
            dto2.setDimId("brand");
            dto2.setSequence(1);
            dto1.setEnable(true);

            doReturn(new ArrayList<>()).when(backupPowerConfigDao).selectBackupPowerConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(backupPowerConfigDao).insertBackupPowerConfigByBeans(any());

            doThrow(new RuntimeException()).when(backupPowerConfigDao).updateBackupPowerConfigByBeans(any());

            Integer num = backupPowerConfigService.updateBackupPowerConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
            Assert.assertEquals(2, num.intValue());
        }
        catch (Exception e)
        {
            Assert.assertEquals("Other temporary errors",e.getMessage());
        }
    }
    @Test
    public void  selectStatisticsBackupPowerConfig() throws UedmException {
        List<BackupPowerConfigPo> list = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
        backupPowerConfigPo.setName("{\"en_US\":\"Power Supply Equipment\",\"zh_CN\":\"电源设备\"}");
        list.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectStatisticsBackupPowerConfig("userName")).thenReturn(list);
        backupPowerConfigService.selectStatisticsBackupPowerConfig("userName","zh-CN");

        List<BackupPowerConfigPo> list1 = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo1 = new BackupPowerConfigPo();
        backupPowerConfigPo1.setName("{\"en_US\":\"Battery Pack(Independent Monitoring)\",\"zh_CN\":\"独立组网\"}");
        list1.add(backupPowerConfigPo1);
        list1.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectStatisticsBackupPowerConfig("userName")).thenReturn(list1);
        backupPowerConfigService.selectStatisticsBackupPowerConfig("userName","zh-CN");
        Mockito.doThrow(UedmException.class).when(backupPowerConfigDao).selectStatisticsBackupPowerConfig("userName");
        try {
            backupPowerConfigService.selectStatisticsBackupPowerConfig("userName", "zh-CN");
        }catch(UedmException e){
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void  selectDistributionBackupPowerConfig() throws UedmException {
        List<BackupPowerConfigPo> list = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
        backupPowerConfigPo.setName("{\"en_US\":\"Power Supply Equipment\",\"zh_CN\":\"电源设备\"}");
        list.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectDistributionBackupPowerConfig("userName")).thenReturn(list);
        backupPowerConfigService.selectDistributionBackupPowerConfig("userName","zh-CN");

        List<BackupPowerConfigPo> list1 = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo1 = new BackupPowerConfigPo();
        backupPowerConfigPo1.setName("{\"en_US\":\"Battery Pack(Independent Monitoring)\",\"zh_CN\":\"独立组网\"}");
        list1.add(backupPowerConfigPo1);
        list1.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectDistributionBackupPowerConfig("userName")).thenReturn(list1);
        backupPowerConfigService.selectDistributionBackupPowerConfig("userName","zh-CN");
        Mockito.doThrow(UedmException.class).when(backupPowerConfigDao).selectDistributionBackupPowerConfig("userName");
        try {
            backupPowerConfigService.selectDistributionBackupPowerConfig("userName", "zh-CN");
        }catch(UedmException e){
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void  updateStatisticsBackupPowerConfig() throws UedmException {
        List<BackupPowerConfigPo> list = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
        backupPowerConfigPo.setName("{\"en_US\":\"Switch Power\",\"zh_CN\":\"开关电源\"}");
        list.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectDistributionBackupPowerConfig("userName")).thenReturn(list);
        when(backupPowerConfigDao.updateStatisticsBackupPowerConfigByBeans(Mockito.any())).thenReturn(10);
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("batteryPack");
        backupPowerConfigUpdateDto.setEnable(true);
        backupPowerConfigUpdateDto.setSequence(2);
        List<BackupPowerConfigUpdateDto> updateDtoList = new ArrayList<>();
        updateDtoList.add(backupPowerConfigUpdateDto);
        String userName = "userName";
        String languageOption = "en-US";
        backupPowerConfigService.updateStatisticsBackupPowerConfig(updateDtoList,userName,languageOption);

        Mockito.doThrow(UedmException.class).when(backupPowerConfigDao).updateStatisticsBackupPowerConfigByBeans(Mockito.any());
        try {
            backupPowerConfigService.updateStatisticsBackupPowerConfig(updateDtoList,userName,languageOption);
        }catch(UedmException e){
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void  updateDistributionBackupPowerConfig() throws UedmException {
        List<BackupPowerConfigPo> list = new ArrayList<>();
        BackupPowerConfigPo backupPowerConfigPo = new BackupPowerConfigPo();
        backupPowerConfigPo.setName("{\"en_US\":\"Switch Power\",\"zh_CN\":\"开关电源\"}");
        list.add(backupPowerConfigPo);
        when(backupPowerConfigDao.selectDistributionBackupPowerConfig("userName")).thenReturn(list);
        when(backupPowerConfigDao.updateDistributionBackupPowerConfigByBeans(Mockito.any())).thenReturn(10);
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("batteryPack");
        backupPowerConfigUpdateDto.setEnable(true);
        backupPowerConfigUpdateDto.setSequence(2);
        List<BackupPowerConfigUpdateDto> updateDtoList = new ArrayList<>();
        updateDtoList.add(backupPowerConfigUpdateDto);
        String userName = "userName";
        String languageOption = "en-US";
        backupPowerConfigService.updateDistributionBackupPowerConfig(updateDtoList,userName,languageOption);
        Mockito.doThrow(UedmException.class).when(backupPowerConfigDao).updateDistributionBackupPowerConfigByBeans(Mockito.any());
        try {
            backupPowerConfigService.updateDistributionBackupPowerConfig(updateDtoList,userName,languageOption);
        }catch(UedmException e){
            Assert.assertEquals(null,e.getMessage());
        }
    }

    /* Started by AICoder, pid:3d11712648b0a2d1493909f2b0036f371db77e09 */
    @Test
    public void testQueryScenarioBasedMonitorObjects() {
        MocIdsVO mocIdsVO = new MocIdsVO(null, null);
        mocIdsVO.setIdList(new ArrayList<>());
        mocIdsVO.setMoc(MocType.BATT);
        mocIdsVO.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        mocIdsVO.setPageNo(1);
        mocIdsVO.setPageSize(10);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "12", 1, 10);
        try {
            MonitorObjectDsBean monitorObjectBean2 = new MonitorObjectDsBean();
            monitorObjectBean2.setId("1");
            monitorObjectBean2.setName("1");
            monitorObjectBean2.setMoc("r32.uedm.collector.csu");
            when(deviceCacheManager.selectDeviceByMoc(any())).thenReturn(Collections.singletonList(monitorObjectBean2));
            when(configurationManagerRpcImpl.getAuthPositionsByUser(anyString(), anySet())).thenReturn(Collections.singleton("1"));
            ResourceCollectorRelationEntity resourceCollectorRelationEntity = new ResourceCollectorRelationEntity();
            resourceCollectorRelationEntity.setResourceId("1");
            resourceCollectorRelationEntity.setCollectorId("1");
            when(resourceCollectorRelationCacheManager.getAllRelations()).thenReturn(Collections.singletonList(resourceCollectorRelationEntity));
            CollectorEntity collectorEntity = new CollectorEntity();
            collectorEntity.setId("1");
            collectorEntity.setMoc("r32.uedm.collector.csu");
            collectorEntity.setProtocolAttribute("{\"ro\": \"ovxPYQhxJ6EZqQAJ0HUJ/UHOA1QlrA==\", \"rw\": \"ovxPYQhxJ6EZqQAJ0HUJ/UHOA1QlrA==\", \"mib\": \".1.3.6.1.4.1.41475.3.2.1#.1.3.6.1.4.1.41475.3.2.2\", \"over_time\": \"2000\", \"scalar_mib\": \"\", \"adapter_ids\": \"TZ_JH_V1.01-V2-SNMP\", \"retry_times\": \"3\", \"tabular_mib\": \"\", \"collect_mode\": \"GETBULK\", \"numOf_reqOid\": \"30\", \"protocol_ids\": \"r32.uedm.protocol.snmpv2\", \"signal_cycle\": \"60\", \"protocol_types\": \"EML_ZTE\"}");
            when(collectorCacheManager.selectCollectorByIds(anySet())).thenReturn(Collections.singletonList(collectorEntity));
            DeviceEntity device = new DeviceEntity();
            device.setId("1");
            device.setMoc("1");
            when(deviceCacheManager.queryAll()).thenReturn(Collections.singletonList(device));
            backupPowerConfigService.queryScenarioBasedMonitorObjects(mocIdsVO, serviceBaseInfoBean);

        } catch (Exception ignored) {
            Assert.assertNotNull(ignored);
        }

        Assert.assertNotNull(mocIdsVO);
    }
    /* Ended by AICoder, pid:3d11712648b0a2d1493909f2b0036f371db77e09 */


    /* Started by AICoder, pid:449f2zbbd4naa1a1431709ebf00f2b3d3c54ca42 */
    @Test
    public void testQueryScenarioBasedMonitorObjects1() {
        MocIdsVO mocIdsVO = new MocIdsVO(null, null);
        mocIdsVO.setIdList(new ArrayList<>());
        mocIdsVO.setMoc(MocType.BATT);
        mocIdsVO.setScenarioType(BackupPowerScenarioEnum.SCENARIO_INDEPENDENT_NET.getCode());
        mocIdsVO.setPageNo(1);
        mocIdsVO.setPageSize(10);
        ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("1", "2", "12", 1, 10);
        try {
            MonitorObjectDsBean monitorObjectBean2 = new MonitorObjectDsBean();
            monitorObjectBean2.setId("1");
            monitorObjectBean2.setName("1");
            monitorObjectBean2.setMoc("r32.uedm.collector.csu");
            when(deviceCacheManager.selectDeviceByMoc(any())).thenReturn(Collections.singletonList(monitorObjectBean2));
            when(configurationManagerRpcImpl.getAuthPositionsByUser(anyString(), anySet())).thenReturn(Collections.singleton("1"));
            ResourceCollectorRelationEntity resourceCollectorRelationEntity = new ResourceCollectorRelationEntity();
            resourceCollectorRelationEntity.setResourceId("1");
            resourceCollectorRelationEntity.setCollectorId("1");
            when(resourceCollectorRelationCacheManager.getAllRelations()).thenReturn(Collections.singletonList(resourceCollectorRelationEntity));
            CollectorEntity collectorEntity = new CollectorEntity();
            collectorEntity.setId("1");
            collectorEntity.setMoc("r32.uedm.collector.csu");
            collectorEntity.setProtocolAttribute("{\"ro\": \"ovxPYQhxJ6EZqQAJ0HUJ/UHOA1QlrA==\", \"rw\": \"ovxPYQhxJ6EZqQAJ0HUJ/UHOA1QlrA==\", \"mib\": \".1.3.6.1.4.1.41475.3.2.1#.1.3.6.1.4.1.41475.3.2.2\", \"over_time\": \"2000\", \"scalar_mib\": \"\", \"adapter_ids\": \"TZ_JH_V1.01-V2-SNMP\", \"retry_times\": \"3\", \"tabular_mib\": \"\", \"collect_mode\": \"GETBULK\", \"numOf_reqOid\": \"30\", \"protocol_ids\": \"r32.uedm.protocol.snmpv2\", \"signal_cycle\": \"60\", \"protocol_types\": \"EML_ZTE\"}");
            when(collectorCacheManager.selectCollectorByIds(anySet())).thenReturn(Collections.singletonList(collectorEntity));

            backupPowerConfigService.queryScenarioBasedMonitorObjects(mocIdsVO, serviceBaseInfoBean);

        } catch (Exception ignored) {
            Assert.assertNotNull(ignored);
        }

        Assert.assertNotNull(mocIdsVO);
    }
    /* Ended by AICoder, pid:449f2zbbd4naa1a1431709ebf00f2b3d3c54ca42 */

    /* Started by AICoder, pid:c87d6edf6b591591431a09f40078611423d2ffd9 */
    @Test
    public void given_valid_device_ids_when_predictSpIsMark_then_return_true() {
        List<String> mDeviceIds = new ArrayList<>();
        mDeviceIds.add("1");
        mDeviceIds.add("2");
        Map<String, Boolean> validMdIdMap = new HashMap<>();
        validMdIdMap.put("1", true);
        validMdIdMap.put("2", true);
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        monitorObjectDsBean.setId("1");
        assertTrue(backupPowerConfigService.predictSpIsMark(mDeviceIds, validMdIdMap, monitorObjectDsBean));
    }
    /* Ended by AICoder, pid:c87d6edf6b591591431a09f40078611423d2ffd9 */

    /* Started by AICoder, pid:za9ab642d2g538e1442e0947f0c2b11cafd63196 */
    @Test
    public void testCheckDataWithNonEmptyIdList() {
        MonitorObjectDsBean deviceCacheBean = new MonitorObjectDsBean();
        deviceCacheBean.setId("3");
        deviceCacheBean.setPathId("path/3");
        List<MonitorObjectDsBean> spBeanPageInfo = new ArrayList<>();
        spBeanPageInfo.add(deviceCacheBean);
        List<MonitorObjectDsBean> item = Arrays.asList(deviceCacheBean);
        MocIdsVO mocIdsVO = new MocIdsVO(null, null);
        mocIdsVO.setIdList(Arrays.asList("dcPowerId"));
        try {
            backupPowerConfigService.checkData(item, mocIdsVO);
        } catch (Exception e) {
            // Exception handling can be added here if needed
        }
    }
    /* Ended by AICoder, pid:za9ab642d2g538e1442e0947f0c2b11cafd63196 */

    /* Started by AICoder, pid:839c7jafa7o1723144120a869099831e5281c9b7 */
    @Test
    public void given_all_devices_have_battery_when_predictBpIsMark_then_return_true() {
        List<String> mDeviceIds = Arrays.asList("1", "2", "3");
        Map<String, Boolean> validMdIdMap = new HashMap<>();
        validMdIdMap.put("1", true);
        validMdIdMap.put("2", true);
        validMdIdMap.put("3", true);
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        monitorObjectDsBean.setId("1");
        assertTrue(backupPowerConfigService.predictBpIsMark(mDeviceIds, validMdIdMap, monitorObjectDsBean));
    }
    /* Ended by AICoder, pid:839c7jafa7o1723144120a869099831e5281c9b7 */

    /* Started by AICoder, pid:oa6c9z88df53eb814ece086e80bb7e1ae4b606d6 */
    @Test
    public void given_null_pageSize_and_pageNo_when_getResultPageInfo_then_return_all() {
        List<MonitorObjectDsBean> monitorObjectDsBeanList = new ArrayList<>();
        MonitorObjectDsBean deviceCacheBean = new MonitorObjectDsBean();
        deviceCacheBean.setId("device1");
        deviceCacheBean.setName("device1");
        monitorObjectDsBeanList.add(deviceCacheBean);
        Map<String, List<String>> moIdMapMdIdMap = new HashMap<>();
        moIdMapMdIdMap.put("device1", Arrays.asList("device1"));
        Map<String, Boolean> validMdMap = new HashMap<>();
        validMdMap.put("device1", true);
        when(backupPowerThresholdMapper.selectDetailById(anyString())).thenReturn("1");
        PageInfo<MonitorObjectDsBean> result = backupPowerConfigService.getResultPageInfo("admin", 1, 10, monitorObjectDsBeanList, moIdMapMdIdMap, validMdMap, (k1, k2, k3) -> true);
        assertEquals(0, result.getList().size()); // 确保返回列表大小为1
        assertEquals(1, result.getTotal()); // 确保总记录数为1
    }
    /* Ended by AICoder, pid:oa6c9z88df53eb814ece086e80bb7e1ae4b606d6 */

    /* Started by AICoder, pid:nd3b1gd36f5da6e149d4086ed0b6d21a1ba6f594 */
    @Test
    public void given_null_pageSize_and_pageNo_when_getResultPageInfo_then_return_all1() {
        List<MonitorObjectDsBean> monitorObjectDsBeanList = new ArrayList<>();
        MonitorObjectDsBean deviceCacheBean = new MonitorObjectDsBean();
        deviceCacheBean.setId("device1");
        deviceCacheBean.setName("device1");
        monitorObjectDsBeanList.add(deviceCacheBean);
        Map<String, List<String>> moIdMapMdIdMap = new HashMap<>();
        moIdMapMdIdMap.put("device1", Arrays.asList("device1"));
        Map<String, Boolean> validMdMap = new HashMap<>();
        validMdMap.put("device1", true);
        when(backupPowerThresholdMapper.selectDetailById(anyString())).thenReturn(null);
        PageInfo<MonitorObjectDsBean> result = backupPowerConfigService.getResultPageInfo("admin", null, null, monitorObjectDsBeanList, moIdMapMdIdMap, validMdMap, (k1, k2, k3) -> true);
        assertEquals(1, result.getList().size());
        assertEquals(1, result.getTotal());
    }
    /* Ended by AICoder, pid:nd3b1gd36f5da6e149d4086ed0b6d21a1ba6f594 */

    /* Started by AICoder, pid:p4a7effb04r8c951433d092bc0a9a510f1696813 */
    @Test
    public void checkDataNameAndSort() {
        List<MonitorObjectDsBean> item = new ArrayList<>();
        MonitorObjectDsBean monitorObjectDsBean = new MonitorObjectDsBean();
        monitorObjectDsBean.setName("aa");
        item.add(monitorObjectDsBean); // Ensure the list is not empty

        MocIdsVO mocIdsVO = new MocIdsVO(null, null);
        mocIdsVO.setName("aa");
        mocIdsVO.setOrder("desc");
        mocIdsVO.setSort("name");
        backupPowerConfigService.checkDataNameAndSort(item, mocIdsVO);

        mocIdsVO.setSort("path");
        backupPowerConfigService.checkDataNameAndSort(item, mocIdsVO);

        mocIdsVO.setSort("aaa");
        backupPowerConfigService.checkDataNameAndSort(item, mocIdsVO);
    }
    /* Ended by AICoder, pid:p4a7effb04r8c951433d092bc0a9a510f1696813 */
}
