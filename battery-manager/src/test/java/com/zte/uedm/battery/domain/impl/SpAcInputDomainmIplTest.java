package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.alarm.AlarmDTO;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.service.impl.AlarmPgCacheServiceImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

public class SpAcInputDomainmIplTest {

    @InjectMocks
    private SpAcInputDomainmIpl spAcInputDomainmIpl;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private MonitorManagerRpcImpl monitorManagerRpcImpl;
    @Mock
    private AlarmPgCacheServiceImpl alarmPgCacheService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test()
    {
        try {
            List<String> spIds = new ArrayList<>();
            spIds.add("moid1");
            spIds.add("moid2");
            List<String> alarmCode = new ArrayList<>();
            alarmCode.add("777");

            List<AlarmDTO> alarmByOid = new ArrayList<>();
            AlarmDTO alarm = new AlarmDTO();
            alarm.setMe("moid1");
            alarm.setAlarmcode(77L);
            AlarmDTO alarm2 = new AlarmDTO();
            alarm.setMe("moid2");
            alarm.setAlarmcode(77L);
            alarmByOid.add(alarm);
            Map<String, List<String>> acdpListMap = new HashMap<>();
            List<String> list = new ArrayList<>();
            list.add("77");
            acdpListMap.put("77",list);

            String mapFieldByLanguageOption = "77";

            Mockito.doReturn(acdpListMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            Mockito.doReturn(alarmByOid).when(alarmPgCacheService).getByCondition(Mockito.any());
            Mockito.doReturn(mapFieldByLanguageOption).when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            spAcInputDomainmIpl.getAcInputStatusBySpIds(spIds,"r32.uedm.device.dcpower",alarmCode,"zh_CN");

        }catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(),null);
        }
    }

    @Test
    public void test1()
    {
        try {
            List<String> spIds = new ArrayList<>();
            spIds.add("77");
            List<String> alarmCode = new ArrayList<>();
            alarmCode.add("777");

            List<AlarmDTO> alarmByOid = new ArrayList<>();
            AlarmDTO alarm = new AlarmDTO();
            alarm.setMe("77");
            alarm.setAlarmcode(77L);
            alarmByOid.add(alarm);
            Map<String, List<String>> acdpListMap = new HashMap<>();
            List<String> list = new ArrayList<>();
            list.add("77");
            acdpListMap.put("77",list);

            String mapFieldByLanguageOption = "77";

            Mockito.doReturn(acdpListMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            Mockito.doReturn(alarmByOid).when(alarmPgCacheService).getByCondition(Mockito.any());
            Mockito.doReturn(mapFieldByLanguageOption).when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            spAcInputDomainmIpl.getAcInputStatusBySpIds(spIds,"r32.uedm.acdp",alarmCode,"zh_CN");

        }catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(),null);
        }
    }

    @Test
    public void getAcInputStatusByCondition_testNormal()
    {
        try {
            List<String> spIds = new ArrayList<>();
            spIds.add("77");
            List<String> alarmCode = new ArrayList<>();
            alarmCode.add("777");

            List<AlarmDTO> alarmByOid = new ArrayList<>();
            AlarmDTO alarm = new AlarmDTO();
            alarm.setMe("77");
            alarm.setAlarmcode(77L);
            alarmByOid.add(alarm);
            Map<String, List<String>> acdpListMap = new HashMap<>();
            List<String> list = new ArrayList<>();
            list.add("77");
            acdpListMap.put("77",list);
            String mapFieldByLanguageOption = "77";
            Map<String, String> objectCommunicationStatus = new HashMap<>();
            objectCommunicationStatus.put("77","0");
            Mockito.doReturn(acdpListMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            Mockito.doReturn(alarmByOid).when(alarmPgCacheService).getByCondition(Mockito.any());
            Mockito.doReturn(mapFieldByLanguageOption).when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            Mockito.doReturn(objectCommunicationStatus).when(monitorManagerRpcImpl).queryObjectCommunicationStatus(Mockito.any());
            spAcInputDomainmIpl.getAcInputStatusByCondition(spIds,"zh_CN");

        }catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(),null);
        }
    }

    @Test
    public void getAcInputStatusByCondition_testFault()
    {
        try {
            List<String> spIds = new ArrayList<>();
            spIds.add("77");
            List<String> alarmCode = new ArrayList<>();
            alarmCode.add("777");

            List<AlarmDTO> alarmByOid = new ArrayList<>();
            AlarmDTO alarm = new AlarmDTO();
            alarm.setMe("77");
            alarm.setAlarmcode(77L);
            alarmByOid.add(alarm);
            Map<String, List<String>> acdpListMap = new HashMap<>();
            List<String> list = new ArrayList<>();
            list.add("77");
            acdpListMap.put("77",list);
            String mapFieldByLanguageOption = "77";
            Map<String, String> objectCommunicationStatus = new HashMap<>();
            objectCommunicationStatus.put("77","1");
            Mockito.doReturn(acdpListMap).when(configurationManagerRpc).selectAcdpIdsByDeviceIds(Mockito.any(),Mockito.any());
            Mockito.doReturn(alarmByOid).when(alarmPgCacheService).getByCondition(Mockito.any());
            Mockito.doReturn(mapFieldByLanguageOption).when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());
            Mockito.doReturn(objectCommunicationStatus).when(monitorManagerRpcImpl).queryObjectCommunicationStatus(Mockito.any());
            spAcInputDomainmIpl.getAcInputStatusByCondition(spIds,"zh_CN");

        }catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(),null);
        }
    }

}
