package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.rpc.AssetRpc;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.dto.ManufacturerSearchDto;
import com.zte.uedm.battery.rpc.vo.ManufacturerSearchVo;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.configuration.license.LicenseBean;
import com.zte.uedm.common.consts.asset.AssetModelConstants;
import com.zte.uedm.common.enums.asset.ManufacturerTypeEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.function.license.api.LicenseSwitchService;
import com.zte.uedm.function.license.exception.LicenseException;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BattAssetDomainImplTest
{

    @InjectMocks
    private BattAssetDomainImpl battAssetDomain;
    @Mock
    private AssetRpc assetRpc;
    @Mock
    private JsonService jsonService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private LicenseSwitchService licenseMgr;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn("").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void searchBattManufacturerInfosTest_param_blank() throws Exception
    {
        ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
            null, null);
        Assert.assertEquals(0l, battAssetDomain.searchManufacturerInfos(dto, null).getTotal());
    }

    @Test
    public void searchBattManufacturerInfosTest_normal() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.anyString());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("未", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
                null, null);
        Assert.assertEquals(1l, battAssetDomain.searchManufacturerInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }


    @Test
    public void searchBattManufacturerInfosTest_normal3() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
//        Response<ResponseBean> response = Response.error(-1,responseBean);

        ResponseBody responseBody = mock(ResponseBody.class);
        Response<ResponseBean> response =  Response.error(500,responseBody);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        Mockito.doReturn("zh_CN").when(i18nUtils).getLangConversion(Mockito.anyString());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("未", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
                null, null);
        Assert.assertEquals(1l, battAssetDomain.searchManufacturerInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }


    @Test
    public void searchBattManufacturerInfosTest_normal2() throws Exception
    {
        List<ManufacturerSearchVo> list = new ArrayList<>();
        ManufacturerSearchVo b = new ManufacturerSearchVo();
        list.add(b);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);



        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        Mockito.doReturn("en_US").when(i18nUtils).getLangConversion(Mockito.anyString());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("un", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
                null, null);
        Assert.assertEquals(2l, battAssetDomain.searchManufacturerInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattManufacturerInfosTest_exception() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(0);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            Mockito.doReturn("en_US").when(i18nUtils).getLangConversion(Mockito.anyString());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
                    null, null);
            battAssetDomain.searchManufacturerInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }

    }

    @Test
    public void searchBattManufacturerInfosTest_exception2() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getManufacturerTypeKey(),
                    null, null);
            battAssetDomain.searchManufacturerInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }



    @Test
    public void searchBattBrandInfosTest_param_blank() throws Exception
    {
        ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getBrandTypeKey(),
                null, null);
        Assert.assertSame(0l, battAssetDomain.searchBrandInfos(dto, null).getTotal());
    }

    @Test
    public void searchBattBrandInfosTest_normal() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getBrandTypeKey(),
                null, null);
        Assert.assertSame(1l, battAssetDomain.searchBrandInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattBrandInfosTest_normal2() throws Exception
    {
        List<ManufacturerSearchVo> list = new ArrayList<>();
        ManufacturerSearchVo b = new ManufacturerSearchVo();
        list.add(b);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);



        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getBrandTypeKey(),
                null, null);
        Assert.assertSame(2l, battAssetDomain.searchBrandInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattBrandInfosTest_exception() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(0);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getBrandTypeKey(),
                    null, null);
            battAssetDomain.searchBrandInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }

    }

    @Test
    public void searchBattBrandInfosTest_exception2() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getBrandTypeKey(),
                    null, null);
            battAssetDomain.searchBrandInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }








    @Test
    public void searchBattSeriesInfosTest_param_blank() throws Exception
    {
        ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getSeriesTypeKey(),
                null,null);
        Assert.assertSame(0l, battAssetDomain.searchSeriesInfos(dto, null).getTotal());
    }

    @Test
    public void searchBattSeriesInfosTest_normal() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getSeriesTypeKey(),
                null,null);
        Assert.assertSame(1l, battAssetDomain.searchSeriesInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattSeriesInfosTest_normal2() throws Exception
    {
        List<ManufacturerSearchVo> list = new ArrayList<>();
        ManufacturerSearchVo b = new ManufacturerSearchVo();
        list.add(b);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);



        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getSeriesTypeKey(),
                null,null);
        Assert.assertSame(2l, battAssetDomain.searchSeriesInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattSeriesInfosTest_exception() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(0);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getSeriesTypeKey(),
                    null,null);
            battAssetDomain.searchSeriesInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }

    }

    @Test
    public void searchBattSeriesInfosTest_exception2() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getSeriesTypeKey(),
                    null,null);
            battAssetDomain.searchSeriesInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }



    @Test
    public void searchBattModelInfosTest_param_blank() throws Exception
    {
        ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getModelTypeKey(),
                null,null);
        Assert.assertSame(0l, battAssetDomain.searchModelInfos(dto, null).getTotal());
    }

    @Test
    public void searchBattModelInfosTest_normal() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getModelTypeKey(),
                null,null);
        Assert.assertSame(1l, battAssetDomain.searchModelInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattModelInfosTest_normal2() throws Exception
    {
        List<ManufacturerSearchVo> list = new ArrayList<>();
        ManufacturerSearchVo b = new ManufacturerSearchVo();
        list.add(b);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);



        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
        Mockito.doReturn("en_US").when(i18nUtils).getLangConversion(Mockito.anyString());
        ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getModelTypeKey(),
                null,null);
        Assert.assertSame(1l, battAssetDomain.searchModelInfos(dto, new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattModelInfosTest_exception() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(0);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getModelTypeKey(),
                    null,null);
            battAssetDomain.searchModelInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }

    }

    @Test
    public void searchBattModelInfosTest_exception2() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchManufacturerInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            ManufacturerSearchDto dto = new ManufacturerSearchDto("name", AssetModelConstants.ASSET_MODEL_ID_BATT, ManufacturerTypeEnum.getModelTypeKey(),
                    null,null);
            battAssetDomain.searchModelInfos(dto, new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }




    @Test
    public void searchBattSupplierInfosTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battAssetDomain.searchBattSupplierInfos("name", null).getTotal());
    }

    @Test
    public void searchBattSupplierInfosTest_normal() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchSupplierInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());

        Assert.assertSame(1l, battAssetDomain.searchBattSupplierInfos("name", new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }


    @Test
    public void searchBattSupplierInfosTest_normal3() throws Exception
    {

        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(new ArrayList<>());
        responseBean.setCode(0);
        responseBean.setTotal(0);
        ResponseBody responseBody = mock(ResponseBody.class);
        Response<ResponseBean> response =  Response.error(500,responseBody);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchSupplierInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);

        List<ManufacturerSearchVo> list = new ArrayList<>();

        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());

        Assert.assertSame(1l, battAssetDomain.searchBattSupplierInfos("name", new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattSupplierInfosTest_normal2() throws Exception
    {
        List<ManufacturerSearchVo> list = new ArrayList<>();
        ManufacturerSearchVo b = new ManufacturerSearchVo();
        list.add(b);
        ResponseBean responseBean = new ResponseBean();
        responseBean.setData(list);
        responseBean.setCode(0);
        responseBean.setTotal(0);
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> call = mock(Call.class);
        when(call.execute()).thenReturn(response);
        when(assetRpc.searchSupplierInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);



        Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
        Mockito.doReturn(list).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());

        Assert.assertSame(2l, battAssetDomain.searchBattSupplierInfos("name", new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }

    @Test
    public void searchBattSupplierInfosTest_exception() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(0);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchSupplierInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            battAssetDomain.searchBattSupplierInfos("name", new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }

    }

    @Test
    public void searchBattSupplierInfosTest_exception2() throws Exception
    {
        try {
            List<ManufacturerSearchVo> list = new ArrayList<>();
            ManufacturerSearchVo b = new ManufacturerSearchVo();
            list.add(b);
            ResponseBean responseBean = new ResponseBean();
            responseBean.setData(list);
            responseBean.setCode(-1);
            responseBean.setTotal(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> call = mock(Call.class);
            when(call.execute()).thenReturn(response);
            when(assetRpc.searchSupplierInfos(Mockito.any(), Mockito.anyMap(), Mockito.anyString())).thenReturn(call);


            Mockito.doReturn("").when(jsonService).objectToJson(Mockito.any());
            Mockito.doThrow(new UedmException(-1,"")).when(jsonService).jsonToObject(Mockito.anyString(),Mockito.any(),Mockito.any());
            battAssetDomain.searchBattSupplierInfos("name", new ServiceBaseInfoBean("","zh-CN"));
        } catch (UedmException e) {
            Assert.assertEquals(new Integer(-200), e.getErrorId());
        }
    }

    @Test
    public void getBattManufacturerKeyByUnknownTest() throws Exception
    {
        Assert.assertSame("unknown", battAssetDomain.getBattManufacturerKeyByUnknown());
        Assert.assertSame("unknown", battAssetDomain.getBattBrandKeyByUnknown());
        Assert.assertSame("unknown", battAssetDomain.getBattSeriesKeyByUnknown());
        Assert.assertSame("unknown", battAssetDomain.getBattModelKeyByUnknown());
        Assert.assertSame("unknown", battAssetDomain.getBattSupplierKeyByUnknown());

    }

    /* Started by AICoder, pid:365a0f7dcar3a5914bc5091670b240232a7345de */
    @Test
    public void given_License_Enabled_when_selectAssetEnable_then_Return_True() throws Exception {
        when(licenseMgr.filterEnableBy(anySet())).thenReturn(Pair.of(true, Collections.singleton("License")));
        assertTrue(battAssetDomain.selectAssetEnable());
    }

    @Test
    public void given_License_Disabled_when_selectAssetEnable_then_Return_False() throws Exception {
        when(licenseMgr.filterEnableBy(anySet())).thenReturn(Pair.of(false, Collections.singleton("License")));
        assertFalse(battAssetDomain.selectAssetEnable());
    }

    @Test
    public void given_License_Manager_Throws_Exception_when_selectAssetEnable_then_Return_False() throws Exception {
        when(licenseMgr.filterEnableBy(anySet())).thenThrow(LicenseException.class);
        assertFalse(battAssetDomain.selectAssetEnable());
    }

    @Test
    public void given_License_Manager_Returns_Null_when_selectAssetEnable_then_Return_False() throws Exception {
        when(licenseMgr.filterEnableBy(anySet())).thenReturn(null);
        assertFalse(battAssetDomain.selectAssetEnable());
    }
    /* Ended by AICoder, pid:365a0f7dcar3a5914bc5091670b240232a7345de */

}
