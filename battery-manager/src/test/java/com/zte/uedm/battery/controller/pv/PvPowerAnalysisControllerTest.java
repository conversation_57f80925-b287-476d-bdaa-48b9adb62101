package com.zte.uedm.battery.controller.pv;

import com.google.common.collect.Lists;
import com.zte.uedm.battery.bean.pv.PvCommonQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvPowerAnalysisResponseBean;
import com.zte.uedm.battery.bean.pv.PvTrendQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvTrendResponseBean;
import com.zte.uedm.battery.service.PvPowerAnalysisService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

/**
 * @ Author     ：10260977
 * @ Date       ：17:16 2021/3/16
 * @ Description：测试类
 * @ Modified By：
 * @ Version: 1.0
 */
public class PvPowerAnalysisControllerTest
{
    @InjectMocks
    private PvPowerAnalysisController pvPowerAnalysisController;

    @Mock
    private PvPowerAnalysisService pvPowerAnalysisService;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void pvPowerAnalysisConditionTest() throws UedmException
    {
        try
        {
            Mockito.when(pvPowerAnalysisService.pvPowerAnalysisOverview(Mockito.any(), Mockito.anyString(), Mockito.any(),
                    Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(new PvPowerAnalysisResponseBean()));
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "", request);
            pvPowerAnalysisController.pvPowerAnalysisOverview(null, 1, 10, "", request);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void pvPowerAnalysisConditionTest_Exc() throws UedmException
    {
        try
        {
            Mockito.when(pvPowerAnalysisService.pvPowerAnalysisOverview(Mockito.any(), Mockito.any(), Mockito.any(),
                    Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200, ""));
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PvCommonQueryRequestBean pvCommonQueryRequestBean = new PvCommonQueryRequestBean();
            pvCommonQueryRequestBean.setPositions(Lists.newArrayList("11"));
            pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "", request);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void pvPowerAnalysisTrendTest() throws UedmException
    {
        try
        {
            Mockito.when(pvPowerAnalysisService.pvPowerAnalysisTrend(Mockito.any(), Mockito.anyString(), Mockito.any(),
                    Mockito.any(), Mockito.any())).thenReturn(new PvTrendResponseBean());
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PvTrendQueryRequestBean pvCommonQueryRequestBean = new PvTrendQueryRequestBean();
            pvCommonQueryRequestBean.setGrain("11");
            pvCommonQueryRequestBean.setStartTime("11");
            pvCommonQueryRequestBean.setEndTime("11");
            pvPowerAnalysisController.pvPowerAnalysisTrend(pvCommonQueryRequestBean, 1, 10, "", request);
            pvPowerAnalysisController.pvPowerAnalysisTrend(null, 1, 10, "", request);
            Mockito.when(pvPowerAnalysisService.pvPowerAnalysisTrend(Mockito.any(), Mockito.any(), Mockito.any(),
                    Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200, ""));
            pvPowerAnalysisController.pvPowerAnalysisTrend(pvCommonQueryRequestBean, 1, 10, "", request);
            pvPowerAnalysisController.pvPowerAnalysisTrend(pvCommonQueryRequestBean, 1, 10, "", null);
        }
        catch (Exception e)
        {
            Assert.assertSame(null,e.getMessage());
        }
    }

    @Test
    public void pvPowerAnalysisTrendTest_Exc() throws UedmException
    {
        try
        {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PvTrendQueryRequestBean pvCommonQueryRequestBean = new PvTrendQueryRequestBean();
            pvCommonQueryRequestBean.setGrain("11");
            pvCommonQueryRequestBean.setStartTime("11");
            pvCommonQueryRequestBean.setEndTime("11");
            Mockito.when(pvPowerAnalysisService.pvPowerAnalysisTrend(Mockito.any(), Mockito.any(), Mockito.any(),
                    Mockito.any(), Mockito.any())).thenThrow(new UedmException(-200, ""));
            pvPowerAnalysisController.pvPowerAnalysisTrend(pvCommonQueryRequestBean, 1, 10, "", request);
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
}
