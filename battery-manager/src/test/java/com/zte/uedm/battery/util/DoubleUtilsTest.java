package com.zte.uedm.battery.util;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

class DoubleUtilsTest {
    @Test
    void testIsZeroTrue() {
        assertThat(DoubleUtils.isZero(0.0)).isTrue();
    }

    @Test
    void testIsZeroFalse() {
        assertThat(DoubleUtils.isZero(1.0)).isFalse();
    }

    @Test
    void testKeep() {
        assertThat(DoubleUtils.keep(3.357, 2)).isEqualTo(3.36, within(0.0001));
    }

    @Test
    void testToDouble() {
        assertThat(DoubleUtils.toDouble("2.0")).isEqualTo(2.0, within(0.0001));
    }

    @Test
    void testAddValue() {
        assertThat(DoubleUtils.add(new BigDecimal("1.0"), new BigDecimal("2.0")).doubleValue()).isEqualTo(3.0, within(0.0001));;
    }

    @Test
    void testAddValue1() {
        assertThat(DoubleUtils.add(null, new BigDecimal("2.0")).doubleValue()).isEqualTo(2.0, within(0.0001));
    }

    @Test
    void testAddValue2() {
        assertThat(DoubleUtils.add(new BigDecimal("1.0"), null).doubleValue()).isEqualTo(1.0, within(0.0001));;
    }

    @Test
    void testAddStr() {
        assertThat(DoubleUtils.addStr(new BigDecimal("2.0"), "1.3").doubleValue()).isEqualTo(3.3, within(0.0001));
    }

    @Test
    void testSub() {
        assertThat(DoubleUtils.sub(new BigDecimal("3.2"), new BigDecimal("1.1")).doubleValue()).isEqualTo(2.1, within(0.0001));;
    }
}
