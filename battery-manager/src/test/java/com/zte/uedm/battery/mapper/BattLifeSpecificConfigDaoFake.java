package com.zte.uedm.battery.mapper;

import com.zte.uedm.battery.controller.battlife.po.BattLifeSpecificCfgPo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BattLifeSpecificConfigDaoFake implements BattLifeSpecificConfigDao{
    //模拟返回正常数据
    public static String DATA_NORMAL = "normal";
    @Override
    public BattLifeSpecificCfgPo selectById(String id) {
        return null;
    }

    @Override
    public Integer insertBatch(List<BattLifeSpecificCfgPo> list) {
        return null;
    }

    @Override
    public Integer deleteBatch(List<String> ids) {
        return null;
    }

    @Override
    public Integer updateBatch(List<BattLifeSpecificCfgPo> battLifeSpecificCfgPos) {
        return null;
    }

    /* Started by AICoder, pid:wf31072fcbm282e14a010b879097bf125c5433a0 */
    @Override
    public List<BattLifeSpecificCfgPo> selectAll() {
        if(!DATA_NORMAL.equals("normal")){
            return  new ArrayList<>();
        }

        // 创建第一个BattLifeSpecificCfgPo实例并设置其battId
        BattLifeSpecificCfgPo battLifeSpecificCfgPo = new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo.setBattId("r32.uedm.device-asd123");

        // 创建第二个BattLifeSpecificCfgPo实例并设置其battId
        BattLifeSpecificCfgPo battLifeSpecificCfgPo2 = new BattLifeSpecificCfgPo();
        battLifeSpecificCfgPo2.setBattId("r32.uedm.device-asd124");

        // 将两个实例放入列表中并返回
        List<BattLifeSpecificCfgPo> list = Arrays.asList(battLifeSpecificCfgPo, battLifeSpecificCfgPo2);
        return list;
    }
    /* Ended by AICoder, pid:wf31072fcbm282e14a010b879097bf125c5433a0 */
}
