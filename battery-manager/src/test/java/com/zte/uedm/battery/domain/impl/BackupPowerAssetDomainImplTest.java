/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BackupPowerAssetDomainImplTest
 * 文件作者：00248587
 * 开发时间：2023/3/8
 */
package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.controller.backuppower.dto.BackupPowerFilterDto;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.vo.MoAssetInstanceVo;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

public class BackupPowerAssetDomainImplTest
{
    @InjectMocks
    private BackupPowerAssetDomainImpl backupPowerAssetDomain;
    @Mock
    private AssetRpcImpl assetRpcImpl;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectMoIdsFromAsset_no_filter() throws UedmException
    {
        doReturn(new ArrayList<>()).when(assetRpcImpl).selectByMoIds(anyList(), anyString());
        List<String> list = backupPowerAssetDomain.selectMoIdsFromAsset(new BackupPowerFilterDto(), Arrays.asList("1"), "zh");
        Assert.assertEquals(1, list.size());
    }


    @Test
    public void selectMoIdsFromAsset_null() throws UedmException
    {
        BackupPowerFilterDto dto = new BackupPowerFilterDto();
        dto.setManufacturers(Arrays.asList("ma1"));
        dto.setBrands(Arrays.asList("b1"));
        dto.setModels(Arrays.asList("m1"));
        dto.setSeries(Arrays.asList("s1"));

        doReturn(new ArrayList<>()).when(assetRpcImpl).selectByMoIds(anyList(), anyString());
        List<String> list = backupPowerAssetDomain.selectMoIdsFromAsset(dto, Arrays.asList("1"), "zh");
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void selectMoIdsFromAsset_normal() throws UedmException
    {
        MoAssetInstanceVo vo1 = new MoAssetInstanceVo();
        vo1.setId("1");
        vo1.setManufacture("ma1");
        vo1.setModel("m1");
        vo1.setBrand("b1");
        vo1.setSeries("s1");

        MoAssetInstanceVo vo2 = new MoAssetInstanceVo();
        vo2.setId("2");
        doReturn(Arrays.asList(vo1, vo2)).when(assetRpcImpl).selectByMoIds(anyList(), anyString());

        BackupPowerFilterDto dto = new BackupPowerFilterDto();
        dto.setManufacturers(Arrays.asList("ma1"));
        dto.setBrands(Arrays.asList("b1"));
        dto.setModels(Arrays.asList("m1"));
        dto.setSeries(Arrays.asList("s1"));
        List<String> list1 = backupPowerAssetDomain.selectMoIdsFromAsset(dto, Arrays.asList("1"), "zh");
        Assert.assertEquals(1, list1.size());

        dto.setManufacturers(Arrays.asList("ma1", "unknown"));
        dto.setBrands(Arrays.asList("b1", "unknown"));
        dto.setModels(Arrays.asList("m1", "unknown"));
        dto.setSeries(Arrays.asList("s1", "unknown"));
        List<String> list2 = backupPowerAssetDomain.selectMoIdsFromAsset(dto, Arrays.asList("1"), "zh");
        Assert.assertEquals(2, list2.size());
    }

}
