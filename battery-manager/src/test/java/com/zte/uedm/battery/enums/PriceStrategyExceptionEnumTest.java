package com.zte.uedm.battery.enums;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class PriceStrategyExceptionEnumTest {
    @Test
    public void testGetPriceStrategyException()
    {
        assertEquals("{\"en_US\":\"Duplicate_ID\",\"zh_CN\":\"ID重复\"}", PriceStrategyExceptionEnum.DUPLICATE_ID.getName());
        assertEquals("{\"en_US\":\"Duplicate effectiveTime\",\"zh_CN\":\"生效时间重复\"}", PriceStrategyExceptionEnum.DUPLICATE_EFFECTIVE_TIME.getName());
        assertEquals("{\"en_US\":\"This policy can not be modified\",\"zh_CN\":\"该方案无法修改\"}", PriceStrategyExceptionEnum.THIS_POLICY_CAN_NOT_BE_MODIFIED.getName());
        assertEquals("{\"en_US\":\"There is no same ID\",\"zh_CN\":\"没有相同ID的政策\"}", PriceStrategyExceptionEnum.THERE_IS_NO_SAME_ID.getName());
    }
}
