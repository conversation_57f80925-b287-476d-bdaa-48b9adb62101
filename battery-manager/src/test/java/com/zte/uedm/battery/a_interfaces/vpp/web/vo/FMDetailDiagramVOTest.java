package com.zte.uedm.battery.a_interfaces.vpp.web.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/* Started by AICoder, pid:z148f00ed6f381c149410be70017fc0a6e29f97b */
public class FMDetailDiagramVOTest {

    @Test
    public void FMDetailDiagramVOTest() throws Exception {
        FMDetailDiagramVO fmDetailDiagramVO = new FMDetailDiagramVO();
        PojoTestUtil.TestForPojo(fmDetailDiagramVO.getClass());
        Assert.assertEquals(fmDetailDiagramVO.toString(), new FMDetailDiagramVO().toString());
    }
}
/* Ended by AICoder, pid:z148f00ed6f381c149410be70017fc0a6e29f97b */
