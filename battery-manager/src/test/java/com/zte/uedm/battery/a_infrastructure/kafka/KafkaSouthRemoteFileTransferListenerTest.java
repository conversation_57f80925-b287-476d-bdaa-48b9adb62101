package com.zte.uedm.battery.a_infrastructure.kafka;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.model.entity.SnmpDistributeResultEntity;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftTemplateFileService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftSNMPServiceImpl;
import com.zte.uedm.battery.a_infrastructure.kafka.bean.FileTransferResponseBean;
import com.zte.uedm.battery.enums.peak.PeakShiftTaskStatusEnum;
import com.zte.uedm.battery.service.impl.PeakShiftIssuedTaskServiceImpl;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;

public class KafkaSouthRemoteFileTransferListenerTest {


    @InjectMocks
    private KafkaSouthRemoteFileTransferListener southRemoteFileTransferListener;

    @Mock
    private PeakShiftSNMPServiceImpl peakShiftSNMPServiceImpl;

    @Mock
    private PeakShiftIssuedTaskServiceImpl peakShiftIssuedTaskService;

    @Mock
    private PeakShiftTemplateFileService peakShiftTemplateFileService;

    @Mock
    private RedisService redisService;

    @Mock
    private MsgSenderService msgSenderService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void onMsg() throws UedmException {
        southRemoteFileTransferListener.onMsg("A");
        southRemoteFileTransferListener.onMsg("{}");
        FileTransferResponseBean msg = new FileTransferResponseBean();
        southRemoteFileTransferListener.onMsg(JSON.toJSONString(msg));

        SnmpDistributeResultEntity bean = new SnmpDistributeResultEntity();
        bean.setDistributeResult(PeakShiftTaskStatusEnum.SUCCESS.getId());
        Mockito.when(peakShiftSNMPServiceImpl.parseDistributeResult(any())).thenReturn(bean);
        southRemoteFileTransferListener.onMsg(JSON.toJSONString(msg));
    }
}