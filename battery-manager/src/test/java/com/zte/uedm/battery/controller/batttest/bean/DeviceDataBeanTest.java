package com.zte.uedm.battery.controller.batttest.bean;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import org.junit.Assert;
import org.junit.Test;

public class DeviceDataBeanTest {

    @Test
    public void test()
    {
        DeviceDataBean deviceDataBean = new DeviceDataBean();
        deviceDataBean.setId("77");
        deviceDataBean.setTestStatus(new IdNameBean());
        deviceDataBean.setTestEndTime("77");
        deviceDataBean.setTestStartTime("77");
        deviceDataBean.toString();

        Assert.assertEquals("77",deviceDataBean.getId());
    }

}
