package com.zte.uedm.battery.dao.impl;

import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.overview.BatteryBaseInfoBean;
import com.zte.uedm.battery.domain.impl.BatteryRemainDischargringDurationEvalDomainImpl;
import com.zte.uedm.battery.mapper.BatteryBaseInfoMapper;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.service.config.optional.MocOptional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class BatteryBaseInfoDaoImplTest {

    @InjectMocks
    private BatteryBaseInfoDaoImpl batteryBaseInfoDao;

    @Mock
    private BatteryBaseInfoMapper batteryBaseInfoMapper;
    @Mock
    private DeviceCacheManager deviceCacheManager;

    @Mock
    private CommonCacheService cacheService;

    @Mock
    private BatteryRemainDischargringDurationEvalDomainImpl batteryRemainDischargringDurationEvalDomain;

    @Before
    public void init()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectByCondition() throws UedmException {
        ReflectionTestUtils.setField(batteryBaseInfoDao, "validTime", 1000);
        ReflectionTestUtils.setField(batteryBaseInfoDao, "waitTime", 1000);

        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("id");
        deviceEntity.setPathId(new String[]{"p1","p2"});
        when(deviceCacheManager.getDevicesByMoc(Mockito.any())).thenReturn(Collections.singletonList(deviceEntity));
        when(deviceCacheManager.getDeviceByIdsAndMoc(Mockito.any(),Mockito.any())).thenReturn(Collections.singletonList(deviceEntity));

        when(batteryBaseInfoMapper.getAllNum()).thenReturn(10001);
        BatteryBaseInfoBean bean = new BatteryBaseInfoBean();
        bean.setId("id");
        when(batteryBaseInfoMapper.selectByLogicId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Collections.singletonList(bean));
        Assert.assertNotNull(batteryBaseInfoDao.selectByCondition("ok"));
        try {
            batteryBaseInfoDao.selectByCondition("r32.uedm.group-global");
            batteryBaseInfoDao.waitCache(100);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        ReflectionTestUtils.setField(batteryBaseInfoDao, "threadNum", 5);
        MonitorObjectEntity monitorObjectEntity = new MonitorObjectEntity();
        monitorObjectEntity.setId("id");
        monitorObjectEntity.setIdPath("p1/p2");

        Assert.assertNotNull(batteryBaseInfoDao.selectByCondition("r32.uedm.group-global"));
        Assert.assertNotNull(batteryBaseInfoDao.selectByCondition("r32.uedm.group-global"));

        when(batteryRemainDischargringDurationEvalDomain.getFilteredBatteryMoIdList(Mockito.any())).thenReturn(Collections.singletonList("id"));
        when(batteryBaseInfoMapper.selectByIdList(Mockito.any())).thenReturn(Collections.singletonList(bean));
        Assert.assertNotNull(batteryBaseInfoDao.selectByCondition("p1"));
    }


    /* Started by AICoder, pid:k7d441b3f5z204a146fb0a47907189339fa3d772 */
    @Test
    public void given_empty_list_when_filter_monitor_object_data_then_return_empty_list() throws UedmException {
        List<BatteryBaseInfoBean> beans = new ArrayList<>();
        List<BatteryBaseInfoBean> result = batteryBaseInfoDao.filterMonitorObjectData(beans);
        assertTrue(result.isEmpty());
    }

    @Test
    public void given_list_with_data_when_filter_monitor_object_data_then_return_filtered_list() throws UedmException {
        BatteryBaseInfoBean b1 = new BatteryBaseInfoBean();
        b1.setId("1");
        b1.setName("name1");
        BatteryBaseInfoBean b2 = new BatteryBaseInfoBean();
        b2.setId("2");
        b2.setName("name2");
        DeviceEntity d1 = new DeviceEntity();
        d1.setId("1");
        d1.setName("name1");
        d1.setPathId(new String[]{"path1"});
        DeviceEntity d2 = new DeviceEntity();
        d2.setId("2");
        d2.setName("name2");
        d2.setPathId(new String[]{"path2"});

        List<BatteryBaseInfoBean> beans = Arrays.asList(b1, b2);
        List<DeviceEntity> batteryDevices = Arrays.asList(d1, d2);
        when(deviceCacheManager.getDeviceByIdsAndMoc(Arrays.asList("1", "2"), MocOptional.BATTERY.getId())).thenReturn(batteryDevices);
        List<BatteryBaseInfoBean> result = batteryBaseInfoDao.filterMonitorObjectData(beans);
        assertEquals(2, result.size());
    }

    @Test
    public void given_list_with_data_when_filter_monitor_object_data_and_cache_is_empty_then_return_empty_list() throws UedmException {
        BatteryBaseInfoBean b1 = new BatteryBaseInfoBean();
        b1.setId("1");
        b1.setName("name1");
        BatteryBaseInfoBean b2 = new BatteryBaseInfoBean();
        b2.setId("2");
        b2.setName("name2");
        List<BatteryBaseInfoBean> beans = Arrays.asList(b1, b2);
        when(deviceCacheManager.getDeviceByIdsAndMoc(Arrays.asList("1", "2"),MocOptional.BATTERY.getId())).thenReturn(new ArrayList<>());
        List<BatteryBaseInfoBean> result = batteryBaseInfoDao.filterMonitorObjectData(beans);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:k7d441b3f5z204a146fb0a47907189339fa3d772 */
}