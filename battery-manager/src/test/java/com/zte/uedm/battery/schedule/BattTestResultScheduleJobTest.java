package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.service.BattPeriodTestService;
import com.zte.uedm.battery.service.BattTestRecordResultUpdateService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.doThrow;

public class BattTestResultScheduleJobTest
{
    @InjectMocks
    private BattTestResultScheduleJob battTestResultScheduleJob;
    @Mock
    private BattTestRecordResultUpdateService battTestRecordResultUpdateService;
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void executeTest() throws Exception
    {
        try
        {
            battTestResultScheduleJob.execute();
            doThrow(new UedmException(-1, "test")).when(battTestRecordResultUpdateService).battTestRecordResultUpdateJob();
            battTestResultScheduleJob.execute();
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }

    }
}
