package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class BattRatedCapDomainImplTest
{

    @InjectMocks
    private BattRatedCapDomainImpl battRatedCapDomain;
    @Mock
    private I18nUtils i18nUtils;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectBattRatedCapLevelsTest_param_blank() throws Exception
    {
        Assert.assertSame(0l, battRatedCapDomain.selectBattRatedCapLevels(null).getTotal());
    }

    @Test
    public void selectBattRatedCapLevelsTest_normal() throws Exception
    {
        Mockito.doReturn("d").when(i18nUtils).getMapFieldByLanguageOption(Mockito.anyString(), Mockito.anyString());
        Assert.assertSame(5l, battRatedCapDomain.selectBattRatedCapLevels(new ServiceBaseInfoBean("","zh-CN")).getTotal());
    }
}
