package com.zte.uedm.battery.bean.peak;


import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class TemplateStrategyDetailCommonPoTest {

    @Test
    public void templateStrategyDetailCommonPoTest() throws Exception{
        TemplateStrategyDetailCommonPo  templateStrategyDetailCommonPo = new TemplateStrategyDetailCommonPo();
        PojoTestUtil.TestForPojo(templateStrategyDetailCommonPo.getClass());
        Assert.assertEquals(templateStrategyDetailCommonPo.toString(),new TemplateStrategyDetailCommonPo().toString());
    }


}