package com.zte.uedm.battery.pv.enums;

import org.junit.Assert;
import org.junit.Test;


public class StaticsTypeEnumTest {
    @Test
    public void staticsTypeTest() {
        StaticsTypeEnum tieredEnum = StaticsTypeEnum.TIERED;
        Assert.assertEquals("tiered", tieredEnum.getStaticsType());
        Assert.assertEquals(Integer.valueOf(2), tieredEnum.getStaticsCode());

        StaticsTypeEnum defaultStaticsType = StaticsTypeEnum.getByStaticsType("default");
        Assert.assertEquals("default", defaultStaticsType.getStaticsType());
        Assert.assertEquals(Integer.valueOf(0), defaultStaticsType.getStaticsCode());
    }
}