package com.zte.uedm.battery.controller;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import com.zte.uedm.battery.bean.BattStandbyPowerRcdHisBean;
import com.zte.uedm.battery.service.BattStandbyPowerRcdHisService;
import com.zte.uedm.common.exception.UedmException;

/**
 * @FileName : BattStandbyPowerRcdHisControllerTest.java
 * @FileDesc : TODO
 * @Version : 1.0
 * <AUTHOR> 何杰10253457
 */
@SpringBootTest
public class BattStandbyPowerRcdHisControllerTest
{
    @InjectMocks
    BattStandbyPowerRcdHisController battStandbyPowerRcdHisController;

    @Mock
    BattStandbyPowerRcdHisService battStandbyPowerRcdHisServiceImpl;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectLastestRcdByMoidTime() throws Exception
    {
        BattStandbyPowerRcdHisBean result = new BattStandbyPowerRcdHisBean();
        result.setId(1);
        Mockito.doReturn(result).when(battStandbyPowerRcdHisServiceImpl).selectLastestRcdByTime(any(), any());
        assertEquals(new Integer(0), battStandbyPowerRcdHisController.selectLastestRcdByMoidTime("1", "222").getCode());
    }

    @Test
    public void testSelectLastestRcdByMoidTime_id_null() throws Exception
    {
        assertEquals(new Integer(-100), battStandbyPowerRcdHisController.selectLastestRcdByMoidTime(null, "222")
                .getCode());
    }

    @Test
    public void testSelectLastestRcdByMoidTime_Exception() throws Exception
    {
        Mockito.doThrow(new Exception("error")).when(battStandbyPowerRcdHisServiceImpl).selectLastestRcdByTime(any(),
                any());
        assertEquals(new Integer(-1), battStandbyPowerRcdHisController.selectLastestRcdByMoidTime("1", "222")
                .getCode());
    }

    @Test
    public void testSelectLastestRcdByMoidTime_UedmException() throws Exception
    {
        Mockito.doThrow(new UedmException(-1, "error")).when(battStandbyPowerRcdHisServiceImpl).selectLastestRcdByTime(
                any(), any());
        assertEquals(new Integer(-1), battStandbyPowerRcdHisController.selectLastestRcdByMoidTime("1", "222")
                .getCode());
    }

}
