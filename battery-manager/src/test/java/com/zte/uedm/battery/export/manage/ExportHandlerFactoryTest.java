package com.zte.uedm.battery.export.manage;

import com.zte.uedm.battery.export.manage.entity.ExportTaskPO;
import com.zte.uedm.battery.mapper.ExportTaskMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Future;
import static org.mockito.Mockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class ExportHandlerFactoryTest {

    @Mock
    private ExportTaskMapper exportTaskMapper ;
    @InjectMocks
    private ExportHandlerFactory exportHandlerFactory;
    /* Started by AICoder, pid:g3030nc2c38195b140ec087230df292d6dd9a425 */
    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        Field field = ExportHandlerFactory.class.getDeclaredField("TASK_QUE");
        field.setAccessible(true);
        Queue<ExportTaskPO> TASK_QUE = (Queue<ExportTaskPO>) field.get(exportHandlerFactory);
        ExportTaskPO exportTaskPO = new ExportTaskPO();
        exportTaskPO.setExportKey("batt_over");
        TASK_QUE.add(exportTaskPO);
    }

    @Test
    public void initTest() throws InterruptedException {
        exportHandlerFactory.init();
        Thread.sleep(200L);
    }

    @Test
    public void stopTaskByTaskIdTest() throws Exception {
        Field field = ExportHandlerFactory.class.getDeclaredField("RUNNING_THREAD");
        field.setAccessible(true);
        Map<String, Future<?>> RUNNING_THREAD = (Map<String, Future<?>>) field.get(new ExportHandlerFactory());
        Future<?> mock = Mockito.mock(Future.class);
        RUNNING_THREAD.put("aaa", mock);
        ExportHandlerFactory.stopTaskByTaskId("aaa");
        Mockito.doThrow(new RuntimeException("aaa")).when(mock).cancel(true);
        RUNNING_THREAD.put("aaa", mock);
        ExportHandlerFactory.stopTaskByTaskId("aaa");
        Assert.assertNotEquals("", field.getName());
    }
    /* Ended by AICoder, pid:g3030nc2c38195b140ec087230df292d6dd9a425 */
    /* Started by AICoder, pid:ia0dewe8e59b80f14aa40a802031fb3d7b063005 */
    @Test
    public void given_NoTasks_when_initTaskWhenAppRun_then_NoTasksAddedToQueue() {
        // Given: No tasks in the database
        when(exportTaskMapper.selectExport(anyList())).thenReturn(Collections.emptyList());

        // When: The application is initialized
        exportHandlerFactory.initTaskWhenAppRun();

        // Then: No tasks should be added to the queue

    }

    @Test
    public void given_SomeTasks_when_initTaskWhenAppRun_then_TasksAddedToQueue() {
        // Given: Some tasks in the database
        List<ExportTaskPO> tasks = Arrays.asList(new ExportTaskPO(), new ExportTaskPO());
        when(exportTaskMapper.selectExport(anyList())).thenReturn(tasks);

        // When: The application is initialized
        exportHandlerFactory.initTaskWhenAppRun();

    }

    @Test
    public void given_MapperThrowsException_when_initTaskWhenAppRun_then_ExceptionIsLogged() {
        // Given: The mapper throws an exception
        when(exportTaskMapper.selectExport(anyList())).thenThrow(new RuntimeException("Database error"));

        // When: The application is initialized
        exportHandlerFactory.initTaskWhenAppRun();

    }
    /* Ended by AICoder, pid:ia0dewe8e59b80f14aa40a802031fb3d7b063005 */
}
