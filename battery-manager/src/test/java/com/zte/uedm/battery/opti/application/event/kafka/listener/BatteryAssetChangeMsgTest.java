package com.zte.uedm.battery.opti.application.event.kafka.listener;

import com.zte.uedm.battery.bean.AssetIdAndMocIdBean;
import com.zte.uedm.battery.bean.BattAssetChangeBean;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.BattOverviewAssetConditionVo;
import com.zte.uedm.battery.service.BattOverviewInitDataService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.constants.KafkaActionOptional;
import com.zte.uedm.kafka.producer.constants.KafkaModelOptional;
import com.zte.uedm.kafka.producer.constants.KafkaTypeOptional;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/1/9
 **/
@RunWith(PowerMockRunner.class)
public class BatteryAssetChangeMsgTest {

    @InjectMocks
    private BatteryAssetChangeMsg batteryAssetChangeMsg;

    @Mock
    private JsonService jsonService;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Mock
    private BattOverviewInitDataService battOverviewInitDataService;

    @Test
    public void objectTestAsset_add() throws Exception {
        try {
            String msg = "da";
            List<KafkaTypeOptional> types = Arrays.asList(KafkaTypeOptional.KAFKA_TYPE_UEDM_REAL_GROUP);

            List<Object> list = new ArrayList<>();
            list.add("82fafb6d-0419-49e3-b876-e07ca0460f57");
            BattAssetChangeBean bean = new BattAssetChangeBean(KafkaModelOptional.KAFKA_MODEL_UEDM_ASSET_INSTANCE, list, KafkaActionOptional.KAFKA_ACTION_CREATE, new ArrayList<>());

            Mockito.doReturn(bean).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any());
            Mockito.doReturn("aaa").when(jsonService).objectToJson(Mockito.any());

            List<AssetIdAndMocIdBean> type = new ArrayList<>();
            AssetIdAndMocIdBean assetIdAndMocIdBean = new AssetIdAndMocIdBean();
            assetIdAndMocIdBean.setAssetNumber("1234");
            assetIdAndMocIdBean.setObjectDeviceId("5678");
            type.add(assetIdAndMocIdBean);
            Mockito.doReturn(type).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any());

            List<BattOverviewAssetConditionVo> assetConditionVos = new ArrayList<>();
            BattOverviewAssetConditionVo batt = new BattOverviewAssetConditionVo();
            batt.setId("123");
            batt.setManufacture("111");
            batt.setBattType("0");
            assetConditionVos.add(batt);
            Mockito.doReturn(assetConditionVos).when(battOverviewInitDataService).selectAssetByAssetNumber(Mockito.any());

            Mockito.doReturn(0).when(battOverviewInitDataService).insertAssetInfo(Mockito.any());

            batteryAssetChangeMsg.onMsg(msg);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void objectTestAsset_update() throws Exception {
        try {
            String msg = "da";
            List<KafkaTypeOptional> types = Arrays.asList(KafkaTypeOptional.KAFKA_TYPE_UEDM_REAL_GROUP);
            BattAssetChangeBean bean = new BattAssetChangeBean(KafkaModelOptional.KAFKA_MODEL_UEDM_ASSET_INSTANCE, new ArrayList<>(), KafkaActionOptional.KAFKA_ACTION_UPDATE, new ArrayList<>());

            Mockito.doReturn(bean).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any());
            Mockito.doReturn("aaa").when(jsonService).objectToJson(Mockito.any());
            Mockito.doReturn(new ArrayList<>()).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any());

            List<BattOverviewAssetConditionVo> assetConditionVos = new ArrayList<>();
            BattOverviewAssetConditionVo batt = new BattOverviewAssetConditionVo();
            batt.setId("123");
            batt.setManufacture("111");
            batt.setBattType("0");
            batt.setAssetModelId("82fafb6d-0419-49e3-b876-e07ca0460f57");
            assetConditionVos.add(batt);
            Mockito.doReturn(assetConditionVos).when(battOverviewInitDataService).selectAssetByAssetNumber(Mockito.any());

            Mockito.doReturn(0).when(battOverviewInitDataService).updateAssetInfo(Mockito.any());

            batteryAssetChangeMsg.onMsg(msg);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void objectTestAsset_del() throws Exception {
        try {
            String msg = "da";
            List<KafkaTypeOptional> types = Arrays.asList(KafkaTypeOptional.KAFKA_TYPE_UEDM_REAL_GROUP);

            List<Object> list = new ArrayList<>();
            list.add("82fafb6d-0419-49e3-b876-e07ca0460f57");
            BattAssetChangeBean bean = new BattAssetChangeBean(KafkaModelOptional.KAFKA_MODEL_UEDM_ASSET_INSTANCE, list, KafkaActionOptional.KAFKA_ACTION_DELETE, new ArrayList<>());

            Mockito.doReturn(bean).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any());
            Mockito.doReturn("aaa").when(jsonService).objectToJson(Mockito.any());

            List<AssetIdAndMocIdBean> type = new ArrayList<>();
            AssetIdAndMocIdBean assetIdAndMocIdBean = new AssetIdAndMocIdBean();
            assetIdAndMocIdBean.setAssetNumber("1234");
            assetIdAndMocIdBean.setObjectDeviceId("5678");
            type.add(assetIdAndMocIdBean);
            Mockito.doReturn(type).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any());

            List<BattOverviewAssetConditionVo> assetConditionVos = new ArrayList<>();
            BattOverviewAssetConditionVo batt = new BattOverviewAssetConditionVo();
            batt.setId("123");
            batt.setManufacture("111");
            batt.setBattType("0");
            batt.setAssetModelId("82fafb6d-0419-49e3-b876-e07ca0460f57");
            assetConditionVos.add(batt);
            Mockito.doReturn(assetConditionVos).when(battOverviewInitDataService).selectAssetByAssetNumber(Mockito.any());

            Mockito.doReturn(0).when(battOverviewInitDataService).updateAssetInfo(Mockito.any());

            batteryAssetChangeMsg.onMsg(msg);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void objectTestAsset_all() throws Exception {
        try {
            String msg = "da";
            List<KafkaTypeOptional> types = Arrays.asList(KafkaTypeOptional.KAFKA_TYPE_UEDM_REAL_GROUP);

            List<Object> list = new ArrayList<>();
            BattAssetChangeBean bean = new BattAssetChangeBean(KafkaModelOptional.KAFKA_MODEL_UEDM_ASSET_INSTANCE, list, KafkaActionOptional.KAFKA_ACTION_ALL, new ArrayList<>());

            Mockito.doReturn(bean).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any());
            Mockito.doReturn("aaa").when(jsonService).objectToJson(Mockito.any());
            Mockito.doReturn(new ArrayList<>()).when(jsonService).jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any());

            List<BattOverviewAssetConditionVo> assetConditionVos = new ArrayList<>();
            BattOverviewAssetConditionVo batt = new BattOverviewAssetConditionVo();
            batt.setId("123");
            batt.setManufacture("111");
            batt.setBattType("0");
            batt.setAssetModelId("82fafb6d-0419-49e3-b876-e07ca0460f57");
            assetConditionVos.add(batt);
            Mockito.doReturn(assetConditionVos).when(battOverviewInitDataService).selectAssetByAssetNumber(Mockito.any());

            Mockito.doReturn(0).when(battOverviewInitDataService).updateAssetInfo(Mockito.any());

            batteryAssetChangeMsg.onMsg(msg);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

}