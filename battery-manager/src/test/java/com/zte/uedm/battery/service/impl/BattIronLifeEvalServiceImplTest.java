package com.zte.uedm.battery.service.impl;


import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.api.bean.MoObjectConfiguration;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.domain.BattDischargeDepthCycleTimesDomain;
import com.zte.uedm.battery.domain.BattLifeEvalDomain;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.domain.BatteryHisDataDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.redis.DataRedis;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.service.battlife.BattLifeAIConfigService;
import com.zte.uedm.battery.util.DateUtils;
import com.zte.uedm.battery.util.TimeUtils;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.configuration.point.bean.PointType;
import com.zte.uedm.common.configuration.point.bean.StandardPointBean;
import com.zte.uedm.common.consts.standardpoint.BattStandardPointConstants;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.pma.service.PmaService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ContextConfiguration;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DateUtils.class, TimeUtils.class})
@ContextConfiguration({ "classpath:application.yml" })
public class BattIronLifeEvalServiceImplTest {

    @InjectMocks
    private BattIronLifeEvalServiceImpl battIronLifeEvalServiceImpl;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl;

    @Mock
    protected BattLifeAIConfigService battLifeAIConfigService;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private BattLifeEvalDomain battLifeEvalDomain;

    @Mock
    private MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Mock
    private BatteryHisDataDomain batteryHisDataDomain;

    @Mock
    private BattTypeDomain battTypeDomain;

    @Mock
    private JsonService jsonService;

    @Mock
    private BattDischargeDepthCycleTimesDomain battDischargeDepthCycleTimesDomain;

    @Mock
    private PmaService pmaService;
    @Mock
    private DataRedis dataRedis;

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(DateUtils.class);
        PowerMockito.mockStatic(TimeUtils.class);
    }

    @Test
    public void evalBattLifeTest() throws Exception {
        Mockito.doReturn(Collections.emptyList()).when(configurationManagerRpcImpl).getMonitorObjectListWhenIdsBig(Mockito.anyList(), any());
        battIronLifeEvalServiceImpl.evalBattLife();

        List<MoObjectConfiguration> list = new ArrayList<>();
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("MonitorObject-111");
        moObjectConfiguration.setName("Batt1");
        moObjectConfiguration.setPath("Group-0/Site-46/PowerCabit/BattPack/Batt1");
        moObjectConfiguration.setPathId("RealGroup-nf94lb/Site-xcav1u/MonitorObject-fsag52/MonitorObject-sfsd52/MonitorObject-fasf20");
        list.add(moObjectConfiguration);

        MoObjectConfiguration moObjectConfiguration1 = new MoObjectConfiguration();
        moObjectConfiguration1.setId("MonitorObject-222");
        moObjectConfiguration1.setName("Batt2");
        moObjectConfiguration1.setPath("Group-1/Site-55/PowerCabit/BattPack/Batt2");
        moObjectConfiguration1.setPathId("RealGroup-fd125b/Site-ceav8u/MonitorObject-hfh52v/MonitorObject-fad84s/MonitorObject-154fda");
        list.add(moObjectConfiguration1);

        Mockito.doReturn(list).when(configurationManagerRpcImpl).getMonitorObjectListWhenIdsBig(any(), any());

        List<StandardPointBean> allStandardPointBean = new ArrayList<>();
        StandardPointBean standardPointBean = new StandardPointBean();
        standardPointBean.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_DISCHARGE_TIMES);
        standardPointBean.setPointType(PointType.AI);
        allStandardPointBean.add(standardPointBean);

        StandardPointBean standardPointBean1 = new StandardPointBean();
        standardPointBean1.setId(BattStandardPointConstants.BATT_SMPID_RATED_CAPACITY);
        standardPointBean1.setPointType(PointType.AO);
        allStandardPointBean.add(standardPointBean1);

        StandardPointBean standardPointBean2 = new StandardPointBean();
        standardPointBean2.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_CYCLE_TIMES);
        standardPointBean2.setPointType(PointType.AI);
        allStandardPointBean.add(standardPointBean2);

        Mockito.doReturn(allStandardPointBean).when(batteryHisDataDomain).getAllStandardPointBean();
        Mockito.doReturn(allStandardPointBean).when(configurationManagerRpcImpl).getStandardPointByIds(any(),any());

        Mockito.doReturn("2022-07-11 12:14:10").when(dateTimeService).getCurrentTime();

        List<HistoryPointsBean> historyDataList = new ArrayList<>();
        HistoryPointsBean historyPointsBean = new HistoryPointsBean();
        historyPointsBean.setSmpId("batt.accum.discharge.times");
        historyPointsBean.setValue(Collections.singletonList("200"));
        historyPointsBean.setDate(Collections.singletonList("2022-07-12"));
        historyDataList.add(historyPointsBean);

        HistoryPointsBean historyPointsBean1 = new HistoryPointsBean();
        historyPointsBean1.setSmpId("batt.rated.capacity");
        historyPointsBean1.setValue(Collections.singletonList("2600"));
        historyPointsBean1.setDate(Collections.singletonList("2022-07-12"));
        historyDataList.add(historyPointsBean1);

        HistoryPointsBean historyPointsBean2 = new HistoryPointsBean();
        historyPointsBean2.setSmpId("batt.accum.cycle.times");
        historyPointsBean2.setValue(Collections.singletonList("400"));
        historyPointsBean2.setDate(Collections.singletonList("2022-07-12"));
        historyDataList.add(historyPointsBean2);

        Mockito.doReturn(historyDataList).when(monitorManagerRpcImpl).getqueryHistoryDataBySmpIdList(any(), any(), any(),
                any(), any(), any());

        Mockito.doReturn(Collections.emptyList()).when(battTypeDomain).getBatteryTypeByMoIdFilterLoop();
        battIronLifeEvalServiceImpl.evalBattLife();

        List<BattTypeBean> battTypeBeanList = new ArrayList<>();
        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setId("Batt1");
        battTypeBean.setBattType(BattTypeEnum.LFP);
        battTypeBeanList.add(battTypeBean);

        BattTypeBean battTypeBean1 = new BattTypeBean();
        battTypeBean1.setId("Batt2");
        battTypeBean1.setBattType(BattTypeEnum.PBAC);
        battTypeBeanList.add(battTypeBean1);

        Mockito.doReturn(battTypeBeanList).when(battTypeDomain).getBatteryTypeByMoIdFilterLoop();

        List<BattDischargeDepthCycleTimesBean> list1 = new ArrayList<>();
        BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean = new BattDischargeDepthCycleTimesBean();
        battDischargeDepthCycleTimesBean.setBattId("Batt1");
        battDischargeDepthCycleTimesBean.setDischargeDepthCycleTimes(
                "[{\"interval\":\"（10%-15%]\",\"min\":0.1,\"containMin\":true,\"max\":0.15,\"containMax\":false,\"cycleTimes\":6000}," +
                        "{\"interval\":\"（0%-5%]\",\"min\":0.0,\"containMin\":true,\"max\":0.05,\"containMax\":false,\"cycleTimes\":7000}]");
        list1.add(battDischargeDepthCycleTimesBean);
        Mockito.doReturn(list1).when(battDischargeDepthCycleTimesDomain).selectBybattIdList(Mockito.anyList());

        Mockito.doReturn(Collections.emptyList()).when(battDischargeDepthCycleTimesDomain).selectBybattIdList(Mockito.anyList());
        Mockito.doReturn(Collections.emptyList()).when(battDischargeDepthCycleTimesDomain).selectByIdsAndType(Mockito.anyList(), any());

        battIronLifeEvalServiceImpl.evalBattLife();

        Mockito.doReturn(10000d).when(battLifeEvalDomain).calcBattDischargeCap(any(), any(), any(), any());
        Mockito.doReturn(100).when(battLifeEvalDomain).getBattRunDays(any(), any());

        battIronLifeEvalServiceImpl.evalBattLife();

        List<HistoryAiBean> historyAiBeanList = new ArrayList<>();
        HistoryAiBean historyAiBean = new HistoryAiBean();
        historyAiBean.setCurValue("100");
        historyAiBeanList.add(historyAiBean);

        HistoryAiBean historyAiBean1 = new HistoryAiBean();
        historyAiBean1.setCurValue("100");
        historyAiBeanList.add(historyAiBean1);

        HistoryAiBean historyAiBean2 = new HistoryAiBean();
        historyAiBean2.setCurValue("100");
        historyAiBeanList.add(historyAiBean2);
        Mockito.doReturn(historyAiBeanList).when(pmaService).selectDataByCondition(any(), Mockito.anyList(), any(),
                any(), any(), any());
        battIronLifeEvalServiceImpl.evalBattLife();

        List<HistoryAiBean> historyAiBeanList1 = new ArrayList<>();
        historyAiBeanList1.add(new HistoryAiBean());
        historyAiBeanList1.add(new HistoryAiBean());
        historyAiBeanList1.add(new HistoryAiBean());
        Mockito.doReturn(historyAiBeanList1).when(pmaService).selectDataByCondition(any(), Mockito.anyList(), any(),
                any(), any(), any());
        battIronLifeEvalServiceImpl.evalBattLife();

        Mockito.doThrow(new UedmException(-1, "aaa")).when(battLifeEvalDomain).getBattRunDays(any(), any());

        try {
            battIronLifeEvalServiceImpl.evalBattLife();
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-1));
        }

    }

//    @Test
//    public void getHisDataTest() throws Exception {
//        List<HistoryAiBean> list = new ArrayList<>();
//        HistoryAiBean historyAiBean = new HistoryAiBean();
//        historyAiBean.setCurValue("100");
//        list.add(historyAiBean);
//        Mockito.doReturn(list).when(monitorManagerRpcImpl).getAiAoHistory(Mockito.any(), Mockito.anyList(), Mockito.any(),
//                Mockito.any(), Mockito.any());
//        MonitorObjectBean monitorObjectBean = new MonitorObjectBean();
//        monitorObjectBean.setId("111");
//        StandardPointBean standardPointBean = new StandardPointBean();
//        standardPointBean.setPointType(PointType.DI);
//        Map<String, HistoryAiBean> hisData = battIronLifeEvalServiceImpl.getHisData("2022-07-09", "2022-07-11",
//                monitorObjectBean, Collections.singletonList(standardPointBean));
//        Assert.assertEquals(hisData.size(), 1);
//    }

    @Test
    public void getDischargeDepthCycleTimesMapTest() throws UedmException {
        List<BattDischargeDepthCycleTimesBean> list1 = new ArrayList<>();
        BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean = new BattDischargeDepthCycleTimesBean();
        battDischargeDepthCycleTimesBean.setBattId("Batt1");
        battDischargeDepthCycleTimesBean.setDischargeDepthCycleTimes(
                "[{\"interval\":\"（10%-15%]\",\"min\":0.1,\"containMin\":true,\"max\":0.15,\"containMax\":false,\"cycleTimes\":6000}," +
                        "{\"interval\":\"（0%-5%]\",\"min\":0.0,\"containMin\":true,\"max\":0.05,\"containMax\":false,\"cycleTimes\":7000}]");
        list1.add(battDischargeDepthCycleTimesBean);
        Mockito.doReturn(list1).when(battDischargeDepthCycleTimesDomain).selectBybattIdList(Mockito.anyList());

        List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = new ArrayList<>();
        BatteryUseLifeBeanVO batteryUseLifeBeanVO = new BatteryUseLifeBeanVO();
        batteryUseLifeBeanVO.setCycleTimes(6000);
        batteryUseLifeBeanVO.setInterval("10%-15%]");
        batteryUseLifeBeanVOList.add(batteryUseLifeBeanVO);

        BatteryUseLifeBeanVO batteryUseLifeBeanVO1 = new BatteryUseLifeBeanVO();
        batteryUseLifeBeanVO1.setCycleTimes(6000);
        batteryUseLifeBeanVO1.setInterval("0%-5%]");
        batteryUseLifeBeanVOList.add(batteryUseLifeBeanVO1);
        Mockito.doReturn(batteryUseLifeBeanVOList).when(jsonService).jsonToObject(any(), any(), any());

        List<MoObjectConfiguration> moObjectConfigurationList = new ArrayList<>();
        MoObjectConfiguration moObjectConfiguration1 = new MoObjectConfiguration();
        moObjectConfiguration1.setId("batt1");
        moObjectConfigurationList.add(moObjectConfiguration1);
        MoObjectConfiguration moObjectConfiguration2 = new MoObjectConfiguration();
        moObjectConfiguration2.setId("batt1");
        moObjectConfigurationList.add(moObjectConfiguration2);
        MoObjectConfiguration moObjectConfiguration3 = new MoObjectConfiguration();
        moObjectConfiguration3.setId("batt1");
        moObjectConfigurationList.add(moObjectConfiguration3);

        List<BattDischargeDepthCycleTimesBean> list2 = new ArrayList<>();
        BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean1 = new BattDischargeDepthCycleTimesBean();
        battDischargeDepthCycleTimesBean1.setBattId("Batt2");
        battDischargeDepthCycleTimesBean1.setDischargeDepthCycleTimes(
                "[{\"interval\":\"（10%-15%]\",\"min\":0.1,\"containMin\":true,\"max\":0.15,\"containMax\":false,\"cycleTimes\":6000}," +
                        "{\"interval\":\"（0%-5%]\",\"min\":0.0,\"containMin\":true,\"max\":0.05,\"containMax\":false,\"cycleTimes\":7000}]");
        list2.add(battDischargeDepthCycleTimesBean1);

        BattDischargeDepthCycleTimesBean battDischargeDepthCycleTimesBean2 = new BattDischargeDepthCycleTimesBean();
        battDischargeDepthCycleTimesBean2.setBattId("Batt3");
        battDischargeDepthCycleTimesBean2.setDischargeDepthCycleTimes(
                "[{\"interval\":\"（10%-15%]\",\"min\":0.1,\"containMin\":true,\"max\":0.15,\"containMax\":false,\"cycleTimes\":6000}," +
                        "{\"interval\":\"（0%-5%]\",\"min\":0.0,\"containMin\":true,\"max\":0.05,\"containMax\":false,\"cycleTimes\":7000}]");
        list2.add(battDischargeDepthCycleTimesBean2);
        Mockito.doReturn(list2).when(battDischargeDepthCycleTimesDomain).selectByIdsAndType(Mockito.anyList(), any());

        BatteryLifeEvalAgg agg = battIronLifeEvalServiceImpl.getDischargeDepthCycleTimesMap(moObjectConfigurationList, BattTypeEnum.PBAC);
        Assert.assertEquals(agg.getUseLifeMap().size(), 2);
    }

    @Test
    public void getCycleTimesTest() throws UedmException {
        BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
        battLifeEvalDto.setUnknownReasonList(new ArrayList<>());
        Map<String, List<BatteryUseLifeBeanVO>> dischargeDepthCycleTimesMap = new HashMap<>();
        List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = new ArrayList<>();
        BatteryUseLifeBeanVO batteryUseLifeBeanVO1 = new BatteryUseLifeBeanVO();
        batteryUseLifeBeanVO1.setCycleTimes(6000);
        batteryUseLifeBeanVO1.setInterval("0%-5%]");
        batteryUseLifeBeanVO1.setMax(5d);
        batteryUseLifeBeanVO1.setMin(0d);
        batteryUseLifeBeanVOList.add(batteryUseLifeBeanVO1);
        dischargeDepthCycleTimesMap.put("batt1", batteryUseLifeBeanVOList);
        dischargeDepthCycleTimesMap.put("batt2", batteryUseLifeBeanVOList);
        Integer result = battIronLifeEvalServiceImpl.getCycleTimes("batt1", 3d, dischargeDepthCycleTimesMap, battLifeEvalDto);
        Assert.assertEquals(result, new Integer(6000));
        try {
            battIronLifeEvalServiceImpl.getCycleTimes("batt2", 4d, dischargeDepthCycleTimesMap, battLifeEvalDto);
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-1));
        }
        try {
            battIronLifeEvalServiceImpl.getCycleTimes("batt3", 4d, dischargeDepthCycleTimesMap, battLifeEvalDto);
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-1));
        }
        try {
             battIronLifeEvalServiceImpl.getCycleTimes("batt1", 3d, new HashMap<>(), battLifeEvalDto);
        } catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-1));
        }

    }

    @Test
    public void getMinTest() throws UedmException, ParseException {
        try {
            BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
            battLifeEvalDto.setUnknownReasonList(new ArrayList<>());
            battIronLifeEvalServiceImpl.getMin(battLifeEvalDto,null,null);
        }
        catch (UedmException e) {
            Assert.assertEquals(e.getErrorId(), new Integer(-1));
        }
    }

    @Test
    public void getMinTest1() throws UedmException, ParseException {
        int min = battIronLifeEvalServiceImpl.getMin(new BattLifeEvalDto(),null, 1);
        Assert.assertSame(1,min);
    }

    @Test
    public void getMinTest2() throws UedmException, ParseException {
        Exception flag = null;
        try
        {
            battIronLifeEvalServiceImpl.getMin(new BattLifeEvalDto(),10D, 1);

        } catch (Exception e) {
            flag =new Exception();
        }

        Assert.assertSame(true,flag == null);
    }

    @Test
    public void calculateFloatingLifeTest() throws UedmException {
        BatteryLifeEvalAgg batteryLifeEvalAgg = new BatteryLifeEvalAgg();
        Map<String, Integer> theoreticalLifeMap = new HashMap<>();
        batteryLifeEvalAgg.setTheoreticalLifeMap(theoreticalLifeMap);

        BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
        Integer floatingLife = battIronLifeEvalServiceImpl.calculateFloatingLife(battLifeEvalDto, batteryLifeEvalAgg, "Batt1");
        Assert.assertTrue(Objects.isNull(floatingLife));

        Map<String, Integer> map = new HashMap<>();
        map.put("Batt1", 10);
        batteryLifeEvalAgg.setTheoreticalLifeMap(map);
        Integer floatingLife1 = battIronLifeEvalServiceImpl.calculateFloatingLife(battLifeEvalDto, batteryLifeEvalAgg, "Batt1");
        Assert.assertTrue(Objects.isNull(floatingLife1));

        battLifeEvalDto.setOperatingDays(10);
        Integer floatingLife2 = battIronLifeEvalServiceImpl.calculateFloatingLife(battLifeEvalDto, batteryLifeEvalAgg, "Batt1");
        Assert.assertEquals(floatingLife2, new Integer(10));

        battLifeEvalDto.setOperatingDays(500);
        Integer floatingLife3 = battIronLifeEvalServiceImpl.calculateFloatingLife(battLifeEvalDto, batteryLifeEvalAgg, "Batt1");
        Assert.assertEquals(floatingLife3, new Integer(0));
    }

    @Test
    public void calculateCycleLifeTest() throws Exception {
        Map<String,Map<String, String>> map= new HashMap<>();
        Map<String,String> setMap = new HashMap<>();
        setMap.put("value","100");
        map.put("battery.rated.capacity",setMap);
        Mockito.when(dataRedis.selectRealData(Mockito.anyString(),Mockito.any())).thenReturn(map);
        Mockito.doReturn("2022-07-29 12:14:10").when(dateTimeService).getCurrentTime();
        PowerMockito.when(TimeUtils.addDays(any(), any())).thenReturn("2022-07-30 12:14:10");
        BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
        battLifeEvalDto.setUnknownReasonList(new ArrayList<>());
        battLifeEvalDto.setAccumDischargeCap(100d);
        battLifeEvalDto.setRatedCap(100d);
        battLifeEvalDto.setAccumDischargeTimes(100);
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("Batt1");
        BatteryLifeEvalAgg batteryLifeEvalAgg = new BatteryLifeEvalAgg();
        Map<String, Integer> theoreticalLifeMap = new HashMap<>();
        batteryLifeEvalAgg.setTheoreticalLifeMap(theoreticalLifeMap);

        List<StandardPointEntity> allStandardPointBean = new ArrayList<>();
        StandardPointEntity standardPointBean = new StandardPointEntity();
        standardPointBean.setId(BattStandardPointConstants.BATT_SMPID_RATED_CAPACITY);
        standardPointBean.setPointType("AI");
        allStandardPointBean.add(standardPointBean);
        StandardPointEntity standardPointBean1 = new StandardPointEntity();
        standardPointBean1.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_DISCHARGE_TIMES);
        standardPointBean1.setPointType("AI");
        allStandardPointBean.add(standardPointBean1);
        StandardPointEntity standardPointBean2 = new StandardPointEntity();
        standardPointBean2.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_CYCLE_TIMES);
        standardPointBean2.setPointType("AI");
        allStandardPointBean.add(standardPointBean2);


        Mockito.doReturn("2022-07-11 12:14:10").when(dateTimeService).getCurrentTime();

        List<com.zte.uedm.pma.bean.HistoryAiBean> historyDataList = new ArrayList<>();
        com.zte.uedm.pma.bean.HistoryAiBean historyAiBean = new com.zte.uedm.pma.bean.HistoryAiBean();
        historyAiBean.setCurValue("100");
        historyDataList.add(historyAiBean);
        historyDataList.add(historyAiBean);
        historyDataList.add(historyAiBean);
        Map<String, Map<String,List<com.zte.uedm.pma.bean.HistoryAiBean>>> historyMapMap = new HashMap<>();
        Map<String,List<com.zte.uedm.pma.bean.HistoryAiBean>> map1 = new HashMap<>();
        map1.put("batt.accs", Arrays.asList(historyAiBean));

        historyMapMap.put("Batt1", map1);

        Mockito.doReturn(historyDataList).when(pmaService).selectDataByCondition(any(), any(), any(),
                any(), any(), any());

        Mockito.doReturn(100D).when(battLifeEvalDomain).calcBattDischargeCap(any(), any(), any(), any());

        Double cycleLife1 = battIronLifeEvalServiceImpl.calculateCycleLife(moObjectConfiguration, battLifeEvalDto, batteryLifeEvalAgg, allStandardPointBean, historyMapMap);
        Assert.assertTrue(Objects.isNull(cycleLife1));
        Mockito.doReturn(null).when(battLifeEvalDomain).calcBattDischargeCap(any(), any(), any(), any());

        Double cycleLife2 = battIronLifeEvalServiceImpl.calculateCycleLife(moObjectConfiguration, battLifeEvalDto, batteryLifeEvalAgg, allStandardPointBean,historyMapMap);
        Assert.assertTrue(Objects.isNull(cycleLife2));

    }

    @Test
    public void calculateCycleLifeTest1() throws Exception {
        Mockito.doReturn("2022-07-29 12:14:10").when(dateTimeService).getCurrentTime();
        PowerMockito.when(TimeUtils.addDays(any(), any())).thenReturn("2022-07-30 12:14:10");
        BattLifeEvalDto battLifeEvalDto = new BattLifeEvalDto();
        battLifeEvalDto.setUnknownReasonList(new ArrayList<>());
        battLifeEvalDto.setAccumDischargeCap(100d);
        battLifeEvalDto.setRatedCap(100d);
        battLifeEvalDto.setAccumDischargeTimes(100);
        battLifeEvalDto.setOperatingDays(10);
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("Batt1");
        BatteryLifeEvalAgg batteryLifeEvalAgg = new BatteryLifeEvalAgg();
        Map<String, Integer> theoreticalLifeMap = new HashMap<>();
        batteryLifeEvalAgg.setTheoreticalLifeMap(theoreticalLifeMap);
        Map<String, List<BatteryUseLifeBeanVO>> dischargeDepthCycleTimesMap = new HashMap<>();
        List<BatteryUseLifeBeanVO> batteryUseLifeBeanVOList = new ArrayList<>();
        BatteryUseLifeBeanVO batteryUseLifeBeanVO1 = new BatteryUseLifeBeanVO();
        batteryUseLifeBeanVO1.setCycleTimes(6000);
        batteryUseLifeBeanVO1.setInterval("0%-5%]");
        batteryUseLifeBeanVO1.setMax(5d);
        batteryUseLifeBeanVO1.setMin(0d);
        batteryUseLifeBeanVOList.add(batteryUseLifeBeanVO1);
        dischargeDepthCycleTimesMap.put("Batt1", batteryUseLifeBeanVOList);
        batteryLifeEvalAgg.setUseLifeMap(dischargeDepthCycleTimesMap);

        List<StandardPointEntity> allStandardPointBean = new ArrayList<>();
        StandardPointEntity standardPointBean = new StandardPointEntity();
        standardPointBean.setId(BattStandardPointConstants.BATT_SMPID_RATED_CAPACITY);
        standardPointBean.setPointType("AI");
        allStandardPointBean.add(standardPointBean);
        StandardPointEntity standardPointBean1 = new StandardPointEntity();
        standardPointBean1.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_DISCHARGE_TIMES);
        standardPointBean1.setPointType("AI");
        allStandardPointBean.add(standardPointBean1);
        StandardPointEntity standardPointBean2 = new StandardPointEntity();
        standardPointBean2.setId(BattStandardPointConstants.BATT_SMPID_ACCUM_CYCLE_TIMES);
        standardPointBean2.setPointType("AI");
        allStandardPointBean.add(standardPointBean2);


        Mockito.doReturn("2022-07-11 12:14:10").when(dateTimeService).getCurrentTime();

        List<com.zte.uedm.pma.bean.HistoryAiBean> historyDataList = new ArrayList<>();
        com.zte.uedm.pma.bean.HistoryAiBean historyAiBean = new com.zte.uedm.pma.bean.HistoryAiBean();
        historyAiBean.setCurValue("100");
        historyDataList.add(historyAiBean);
        historyDataList.add(historyAiBean);
        historyDataList.add(historyAiBean);
        Map<String, Map<String,List<com.zte.uedm.pma.bean.HistoryAiBean>>> historyMapMap = new HashMap<>();
        Map<String,List<com.zte.uedm.pma.bean.HistoryAiBean>> map1 = new HashMap<>();
        map1.put("battery.accum.discharge.times", Arrays.asList(historyAiBean));
        map1.put("battery.accum.cycle.times", Arrays.asList(historyAiBean));
        historyMapMap.put("Batt1", map1);

        Mockito.doReturn(historyDataList).when(pmaService).selectDataByCondition(any(), any(), any(),
                any(), any(), any());

        Map<String,Map<String, String>> map= new HashMap<>();
        Map<String,String> setMap = new HashMap<>();
        setMap.put("value","100");
        map.put("battery.rated.capacity",setMap);
        Mockito.when(dataRedis.selectRealData(Mockito.anyString(),Mockito.any())).thenReturn(map);

        Mockito.doReturn(100D).when(battLifeEvalDomain).calcBattDischargeCap(any(), any(), any(), any());

        Double cycleLife1 = battIronLifeEvalServiceImpl.calculateCycleLife(moObjectConfiguration, battLifeEvalDto, batteryLifeEvalAgg, allStandardPointBean, historyMapMap);
        Assert.assertEquals("20.0", cycleLife1.toString());
    }

    @Test
    public void dispatchHandleTest() throws UedmException {
        Map<String,Map<String, String>> map= new HashMap<>();
        Map<String,String> setMap = new HashMap<>();
        setMap.put("value","100");
        map.put("battery.rated.capacity",setMap);
        Mockito.when(dataRedis.selectRealData(Mockito.anyString(),Mockito.any())).thenReturn(map);
        List<MoObjectConfiguration> moObjectConfigurationList = new ArrayList<>();
        MoObjectConfiguration moObjectConfiguration = new MoObjectConfiguration();
        moObjectConfiguration.setId("Batt1");
        moObjectConfigurationList.add(moObjectConfiguration);
        Map<String, BattTypeEnum> battTypeEnumMap         = new HashMap<>();
        battTypeEnumMap.put("Batt1", BattTypeEnum.LFP);
        battIronLifeEvalServiceImpl.dispatchHandle(moObjectConfigurationList, battTypeEnumMap);
        List<MoObjectConfiguration> moObjectConfigurationList1 = new ArrayList<>();
        battIronLifeEvalServiceImpl.dispatchHandle(moObjectConfigurationList1, battTypeEnumMap);

    }

    @Test
    public void fillUpBaseInfoTest1(){
        BattLifeEvalDto battLifeEvalDto = battIronLifeEvalServiceImpl.fillUpBaseInfo(new MoObjectConfiguration(), BattTypeEnum.PBAC);
        BattLifeEvalDto battLifeEvalDto2 = battIronLifeEvalServiceImpl.fillUpBaseInfo(new MoObjectConfiguration(), BattTypeEnum.LFP);

        Assert.assertEquals("admin", battLifeEvalDto.getCreator());

    }

    @Test
    public void getBatchHisDataTest() throws UedmException
    {
        Map<String, Map<String, List<com.zte.uedm.pma.bean.HistoryAiBean>>> res1 = battIronLifeEvalServiceImpl.getBatchHisData(new ArrayList<>(), new ArrayList<>());
        Assert.assertEquals(0, res1.size());

        try {
            Mockito.doReturn(Arrays.asList(new com.zte.uedm.pma.bean.HistoryAiBean())).when(pmaService).selectDataByCondition(any(), Mockito.anyList(), any(),
                    any(), any(), any());
            Map<String, Map<String, List<com.zte.uedm.pma.bean.HistoryAiBean>>> res = battIronLifeEvalServiceImpl.getBatchHisData(Arrays.asList("id"), Arrays.asList("id"));
            Assert.assertEquals(0, res.size());
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-1, e.getErrorId().intValue());
        }
    }

    @Test
    public void getBatchHisDataTest1() throws UedmException
    {
        Map<String, Map<String, List<com.zte.uedm.pma.bean.HistoryAiBean>>> res1 = battIronLifeEvalServiceImpl.getBatchHisData(new ArrayList<>(), new ArrayList<>());
        Assert.assertEquals(0, res1.size());

        try {
            Mockito.doThrow(new UedmException(-1, "1")).when(pmaService).selectDataByCondition(any(), Mockito.anyList(), any(),
                    any(), any(), any());
            Map<String, Map<String, List<com.zte.uedm.pma.bean.HistoryAiBean>>> res2 = battIronLifeEvalServiceImpl.getBatchHisData(Arrays.asList("id"), Arrays.asList("id"));
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-1, e.getErrorId().intValue());
        }
    }
}
