package com.zte.uedm.battery.bean;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class HashcodeEqualsTestUtil 
{
	private static byte BYTE_BASIC_DADA = 0;
    private static short SHORT_BASIC_DATA = 0;
    private static int INT_BASIC_DATA = 0;
    private static long LONG_BASIC_DATA = 0;
    private static float FLOAT_BASIC_DATA = 1;
    private static double DOUBLE_BASIC_DATA = 1.0;
    private static char CHAR_BASIC_DATA = 'c';
    private static String STRING_BASIC_DATA = "12";
    private static boolean BOOLEAN_BASIC_DATA = true;
    
	@SuppressWarnings("unchecked")
	public static <T> void hashcodeAndEqualsTest(Class<?> testClass) throws Exception
	{
		Object data1 = testClass.newInstance();
		Object data2 = testClass.newInstance();
		Method[] methods = testClass.getDeclaredMethods();
		List<Method> setMethods = getSetMethods(methods);
		data1.equals(data1);
		data1.equals(null);
		data1.equals(new Object());
		equalsAndToString(data1, data2);
		for(Method method : setMethods)
		{
			excuteSetMethods(method, data1);
			equalsAndToString(data1, data2);
			excuteSetMethods(method, data2);
			equalsAndToString(data1, data2);
		}
		
	}

	private static List<Method> getSetMethods(Method[] methods)
	{
		List<Method> setMethods = new ArrayList<>();	
		for(Method method : methods)
		{
			if(method.getName().subSequence(0, 3).equals("set"))
			{
				setMethods.add(method);
			}
		}
		return setMethods;
	}
	
	private static <T> void equalsAndToString(T data1,T data2)
    {
    	data1.equals(data2);
    	data2.equals(data1);
    	data1.toString();
    	data2.toString();
    	data1.hashCode();
    	data2.hashCode();
    }
	

    private static void excuteSetMethods(Method method, Object testObject) throws Exception
    {
        if (!delWithBasicdataSetFunc(method, testObject))
        {
            method.invoke(testObject, new Object[] { null });
        }
    }

    private static boolean delWithBasicdataSetFunc(Method method, Object testObject) throws Exception
    {
        Class<?>[] parameterTypes = method.getParameterTypes();
        if (null != parameterTypes && parameterTypes.length > 0)
        {
            String paramTypeName = parameterTypes[0].getName();
            if (isBasicType1(paramTypeName, method, testObject))
            {
                return true;
            }
            else if (isBasicType2(paramTypeName, method, testObject))
            {
                return true;
            }
        }
        return false;
    }

    private static boolean isBasicType1(String paramTypeName, Method method, Object testObject) throws Exception
    {
    	if("java.lang.String".equals(paramTypeName))
    	{
    		method.invoke(testObject, STRING_BASIC_DATA);
    		return true;
    	}
        else if ("double".equals(paramTypeName) || "java.lang.Double".equals(paramTypeName))
        {
            method.invoke(testObject, DOUBLE_BASIC_DATA);
            return true;
        }
        else if ("char".equals(paramTypeName) || "java.lang.Character".equals(paramTypeName))
        {
            method.invoke(testObject, CHAR_BASIC_DATA);
            return true;
        }
        else if ("boolean".equals(paramTypeName) || "java.lang.Boolean".equals(paramTypeName))
        {
            method.invoke(testObject, BOOLEAN_BASIC_DATA);
            return true;
        }
        return false;
    }

    private static boolean isBasicType2(String paramTypeName, Method method, Object testObject) throws Exception
    {
        if ("short".equals(paramTypeName) || "java.lang.Short".equals(paramTypeName))
        {
            method.invoke(testObject, SHORT_BASIC_DATA);
            return true;
        }
        else if ("int".equals(paramTypeName) || "java.lang.Integer".equals(paramTypeName))
        {
            method.invoke(testObject, INT_BASIC_DATA);
            return true;
        }
        else if ("long".equals(paramTypeName) || "java.lang.Long".equals(paramTypeName))
        {
            method.invoke(testObject, LONG_BASIC_DATA);
            return true;
        }
        return false;
    }
}
