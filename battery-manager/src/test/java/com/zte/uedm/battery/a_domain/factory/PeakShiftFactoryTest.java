package com.zte.uedm.battery.a_domain.factory;

import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftBCUAServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static org.junit.Assert.*;

public class PeakShiftFactoryTest {

    @InjectMocks
    private PeakShiftFactory peakShiftFactory;

    @Mock
    private Map<String, PeakShiftCommonService> peakShiftTypeMap;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void generateByDeviceTypeTest() {
        peakShiftFactory.generateByDeviceType("1");
        Mockito.doReturn(false).when(peakShiftTypeMap).isEmpty();
        peakShiftFactory.generateByDeviceType(null);
        peakShiftFactory.generateByDeviceType("1");

        Mockito.doReturn(new PeakShiftBCUAServiceImpl()).when(peakShiftTypeMap).get("PEAK_SHIFT_1");
        peakShiftFactory.generateByDeviceType("1");

    }
}