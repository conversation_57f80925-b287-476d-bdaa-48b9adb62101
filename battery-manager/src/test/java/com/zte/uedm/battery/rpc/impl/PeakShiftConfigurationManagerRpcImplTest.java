package com.zte.uedm.battery.rpc.impl;

import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceBaseCacheManager;
import com.zte.uedm.battery.bean.PathInfoBean;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.logic.group.bean.SiteBean;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBean;
import com.zte.uedm.common.service.JsonService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class PeakShiftConfigurationManagerRpcImplTest
{
    @InjectMocks
    private PeakShiftConfigurationManagerRpcImpl peakShiftConfigurationManagerRpcImpl;
    
    @Mock
    private ConfigurationRpc configurationRpc;
    
    @Mock
    private JsonService jsonService;
    @Mock
    private ResourceBaseCacheManager resourceBaseCacheManager;
    
    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }
    
    
    @Test
    public void getMonitorObjectListWhenIdsBigTest() throws Exception {
        List<String> ids = new ArrayList<>();
        ids.add("id");
        List<ResourceBaseEntity> list = new ArrayList<>();
        ResourceBaseEntity resourceBaseEntity = new ResourceBaseEntity();
        resourceBaseEntity.setId("id");
        resourceBaseEntity.setName("name");
        resourceBaseEntity.setPathId(new String[]{"id"});
        list.add(resourceBaseEntity);
        Mockito.when(resourceBaseCacheManager.getAllResourceBase()).thenReturn(list);

        assertNotNull(peakShiftConfigurationManagerRpcImpl.getAllLogicGroupPathsByIdS(new ArrayList<>()));
        assertNotNull(peakShiftConfigurationManagerRpcImpl.getAllLogicGroupPathsByIdS(ids));
    }


}
