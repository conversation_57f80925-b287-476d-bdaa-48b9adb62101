/* Started by AICoder, pid:98a952575bhcc0e1496c0948909b7b492f38d272 */
package com.zte.uedm.battery.a_interfaces.safe.web;

import com.zte.uedm.battery.a_domain.safe.impl.BatteryTrackServiceImpl;
import com.zte.uedm.battery.a_infrastructure.safe.bean.BatteryProtectRequestBean;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BatteryTrackControllerTest {

    @InjectMocks
    private BatteryTrackController batteryTrackController;

    @Mock
    private BatteryTrackServiceImpl batteryTrackService;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getHistoryTrackNew() throws Exception {
        batteryTrackController.getHistoryTrackNew("", "", "");
    }

    @Test
    public void getHistoryTrackNew1() throws Exception {
        batteryTrackController.getHistoryTrackNew("1", "", "");
    }

    @Test
    public void getBatteryLocationInfo() throws Exception {
        BatteryProtectRequestBean bean = new BatteryProtectRequestBean();
        List<String> list = new ArrayList<>();
        list.add("122");
        bean.setIds(list);
        batteryTrackController.getBatteryLocationInfo(bean, httpServletRequest);
    }

    @Test
    public void getBatteryLocationInfo1() throws Exception {
        batteryTrackController.getBatteryLocationInfo(new BatteryProtectRequestBean(), httpServletRequest);
    }
}
/* Ended by AICoder, pid:98a952575bhcc0e1496c0948909b7b492f38d272 */