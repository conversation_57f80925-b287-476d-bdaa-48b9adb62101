package com.zte.uedm.battery.opti.domain.service.impl;

import com.zte.uedm.battery.opti.application.scheduler.impl.BattRiskCalculateSchedulerImpl;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class BattRiskCalculateSchedulerImpl2Test {

    @InjectMocks
    private BattRiskCalculateSchedulerImpl battRiskCalculateScheduler;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void dealThreadTest() throws NoSuchMethodException, NoSuchFieldException, IllegalAccessException, InvocationTargetException {
        Field riskEvalThread = BattRiskCalculateSchedulerImpl.class.getDeclaredField("RISK_EVAL_THREAD");
        riskEvalThread.setAccessible(true);
        riskEvalThread.set(battRiskCalculateScheduler, 1);
        Method dealThread = BattRiskCalculateSchedulerImpl.class.getDeclaredMethod("dealThread", String.class, List.class);
        dealThread.setAccessible(true);
        dealThread.invoke(battRiskCalculateScheduler, "11", new ArrayList<>());

        try {
            dealThread.invoke(battRiskCalculateScheduler, "11", null);
        } catch (Exception e) {

        }
    }
}
