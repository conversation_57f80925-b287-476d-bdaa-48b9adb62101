package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;

public class StandardPointViewBeanTest {
    @Test
    public void test()
    {
        StandardPointViewBean standardPointViewBean = new StandardPointViewBean();
        standardPointViewBean.setProtocolType("shoonis");
        standardPointViewBean.setCounterId("11");
        standardPointViewBean.hashCode();
        standardPointViewBean.equals(standardPointViewBean);
        Assert.assertEquals("shoonis",standardPointViewBean.getProtocolType());
    }

}
