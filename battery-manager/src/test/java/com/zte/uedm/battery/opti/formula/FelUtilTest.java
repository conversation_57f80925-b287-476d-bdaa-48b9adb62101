package com.zte.uedm.battery.opti.formula;

import com.zte.uedm.battery.opti.domain.utils.FelUtil;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.internal.util.reflection.FieldSetter;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class FelUtilTest
{
    @Test
    public void computeNewTest() throws UedmException
    {
        Map<Integer, Double> values = new HashMap<>();
        values.put(1,1.0);
        Exception exp=null;
        try
        {
            boolean result = FelUtil.compute("%1==1",values);
            Assert.assertTrue(result);
            values.put(2,1.0);
            result = FelUtil.compute("%1==1 && %2==1",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("true && %2==0",values);
            Assert.assertFalse(result);
            result = FelUtil.compute(null,values);
            Assert.assertFalse(result);
            values.put(2,null);
            result = FelUtil.compute("%1==1 && %2==0",values);
            Assert.assertFalse(result);
        }
        catch (Exception e)
        {
            exp = new UedmException(-1, "");
        }
        Assert.assertEquals("1.0", String.valueOf(values.get(1)));
    }

    @Test
    public void computeNewTestMax() throws UedmException
    {
        Exception exp=null;
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1,1.0);
        values.put(2,2.0);
        try
        {
            boolean result = FelUtil.compute("Max(%1, %2) > 1",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("Min(%1, %2) > 1",values);
            Assert.assertFalse(result);
            result = FelUtil.compute("Max(%1, %2) >= 2",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("Max(%1, %2) == 2",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("Max(%1, %2) > 2 || %1 > 0",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("Max(%1, %2) > 2 && %1 > 0",values);
            Assert.assertFalse(result);
            result = FelUtil.compute("Max(%1, %2) > 2 && %1 > 0 ? %1 > 0 : %2 < 0",values);
            Assert.assertFalse(result);
            values.put(3, false);
            result = FelUtil.compute("Max(%1, %2) >1 && %3",values);
            Assert.assertFalse(result);
            result = FelUtil.compute("Max(%1, %2) >1 || %3",values);
            Assert.assertTrue(result);
            result = FelUtil.compute("!(Max(%1, %2) >2) || %3",values);
            Assert.assertTrue(result);
        }
        catch (Exception e)
        {
            exp = new UedmException(-1, "");
        }
        Assert.assertEquals("1.0", String.valueOf(values.get(1)));

    }

    @Test
    public void computeNewTestPri() throws UedmException
    {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, null);
        values.put(2, 1699596000000.0);
        values.put(3, 1699599600000.0);
        values.put(4, 1702224000000.0);
        values.put(5, 2592000000.0);
        boolean result = FelUtil.compute("(%4 - Pri(%1, %2, %3)) > %5",values);
        Assert.assertTrue(result);
        Assert.assertEquals(values.size(),5);
        try {
            Map<Integer, Object> values2 = new HashMap<>();
            values2.put(1, null);
            FelUtil.compute("(%4 - Pri(%1, %2, %3)) > %5",values2);
        }catch (Exception e){

        }
    }

    @Test
    public void computeNewTestMaxRmMax() throws UedmException
    {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, null);
        values.put(2, 1.0);
        values.put(3, 2.0);
        values.put(4, 3.0);
        values.put(5, 3.0);
        values.put(6, 0.0);
        boolean result = FelUtil.compute("(%5 - MaxRmMax(%1, %2, %3, %4)) > %6",values);
        Assert.assertTrue(result);
        Assert.assertEquals(values.size(),6);
    }
    @Test
    public void computeNewTestMaxRmMa2() throws UedmException
    {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, null);
        values.put(2, null);
        values.put(3, null);
        values.put(4, null);
        values.put(5, 3.0);
        values.put(6, null);
        try {

        }catch (Exception e){
            boolean result = FelUtil.compute("(%5 - MaxRmMax(%1, %2, %3, %4)) > %6",values);
        }


    }

    @Test
    public void computeNewTestMinRmMax() throws UedmException
    {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, null);
        values.put(2, 1.0);
        values.put(3, 2.0);
        values.put(4, 3.0);
        values.put(5, 3.0);
        values.put(6, 1.0);
        boolean result = FelUtil.compute("(%5 - MinRmMax(%1, %2, %3, %4)) > %6",values);
        Assert.assertTrue(result);
        Assert.assertEquals(values.size(),6);
    }
    @Test
    public void computeNewTestMinRmMax2() throws UedmException
    {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, null);
        values.put(2, null);
        values.put(3, null);
        values.put(4, null);
        values.put(5, 3.0);
        values.put(6, 1.0);
        try {
            FelUtil.compute("(%5 - MinRmMax(%1, %2, %3, %4)) > %6",values);
        }catch (Exception e){

        }
        try {
            Map<Integer, Object> values2 = new HashMap<>();
            values2.put(1, null);
            values2.put(2, null);
            values2.put(3, null);
            values2.put(4, null);
            FelUtil.compute("(%5 - MaxRmMax(%1, %2, %3, %4)) > %6",values2);
        }catch (Exception e){

        }
    }
    @Test
    public void computeNewTestUedmException() throws NoSuchFieldException {
        FelUtil.initFunction();
        Map<Integer, Object> values = new HashMap<>();
        values.put(1, 1);
        values.put(2, 1.0);
        values.put(3, 2.0);
        values.put(4, 3.0);
        values.put(5, 3.0);
        values.put(6, 1.0);
        Field apiField = FelUtil.class.getDeclaredField("scriptCache");
        FieldSetter.setField(new FelUtil(), apiField, null);
        boolean result = false;
        try {
            result = FelUtil.compute(" MinRmMax(%1, %2, %3, %4)> %6",values);
        } catch (Exception e) {
            System.out.println("[{}]calculateExpression is error, message is {}"+e.getMessage());
        }
        Assert.assertFalse(result);
    }
}
