package com.zte.uedm.battery.enums;

import org.junit.Assert;
import org.junit.Test;

public class BattTypeEnumTest
{
    @Test
    public void test()
    {
        BattTypeEnum.LFP.getCode();
        BattTypeEnum.PBAC.getCode();
        BattTypeEnum.UNKNOWN.getCode();
        BattTypeEnum.PBAC.getBattTypeEnumName();
        BattTypeEnum.LFP.getBattTypeEnumName();
        BattTypeEnum.UNKNOWN.getBattTypeEnumName();
        BattTypeEnum.LFP.checkTypeCode("0");
        BattTypeEnum.LFP.checkTypeCode("1");
        BattTypeEnum.UNKNOWN.checkTypeCode("2");
        BattTypeEnum.getNameByCode("33");
        Assert.assertSame(BattTypeEnum.LFP.getCode(), "1");
        Assert.assertSame(BattTypeEnum.PBAC.getCode(), "0");
    }
}
