package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakDeviceTypeEnum;
import com.zte.uedm.battery.bean.*;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.peak.SeasonStrategyForTemplateVo;
import com.zte.uedm.battery.bean.peak.StrategyCombinationVo;
import com.zte.uedm.battery.bean.peak.TemplateStrategyBo;
import com.zte.uedm.battery.bean.scopeStrategy.PriceIntervalStrategyBean;
import com.zte.uedm.battery.bean.scopeStrategy.PriceStrategySynBatteryBean;
import com.zte.uedm.battery.bean.scopeStrategy.TieredPriceStrategyBean;
import com.zte.uedm.battery.mapper.*;
import com.zte.uedm.battery.pv.enums.SeasonStrategyStatusEnum;
import com.zte.uedm.battery.pv.vo.IntervalStrategyTypeVo;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.util.LogUtils;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.io.IOException;
import java.util.*;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

public class GridStrategyServiceImplTest
{
    @InjectMocks
    private GridStrategyServiceImpl gridStrategyService;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private PeakShiftConfigBaseServiceImpl peakShiftConfigBaseServiceImpl;

    @Mock
    private GridStrategyMapper gridStrategyMapper;

    @Mock
    private TemplateStrategyMapper templateStrategyMapper;

    @Mock
    private LogUtils logUtils;

    @Mock
    private JsonService jsonService;

    @Mock
    private PriceStrategyMapper priceStrategyMapper;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Mock
    private PeakShiftIssuedTaskServiceImpl peakShiftIssuedTaskService;

    @Mock
    private PeakShiftTaskDetailMapper peakShiftTaskDetailMapper;

    @Mock
    private PeakShiftMapper peakShiftMapper;

    /* Started by AICoder, pid:f6c7d796b0fd4a4c901cf6d02a5dce7f */
    private ServiceBaseInfoBean serviceBaseInfoBean=new ServiceBaseInfoBean("testUser","zh_CN");
    /* Ended by AICoder, pid:f6c7d796b0fd4a4c901cf6d02a5dce7f */

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectStrategyDetailBySeasonStrategyId()
    {
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStragyId")).thenReturn(seasonStrategyBean);
        final IntervalStrategyBean intervalStrategyBean = new IntervalStrategyBean();
        intervalStrategyBean.setId("id");
        intervalStrategyBean.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean.setStartDate("startDate");
        intervalStrategyBean.setEndDate("endDate");
        intervalStrategyBean.setRemark("remark");
        intervalStrategyBean.setMode(0);
        intervalStrategyBean.setGmtCreate("currentTime");
        intervalStrategyBean.setGmtModified("currentTime");

        final IntervalStrategyBean intervalStrategyBean1 = new IntervalStrategyBean();
        intervalStrategyBean1.setId("id");
        intervalStrategyBean1.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean1.setStartDate("startDate");
        intervalStrategyBean1.setEndDate("endDate");
        intervalStrategyBean1.setRemark("remark");
        intervalStrategyBean1.setMode(3);
        intervalStrategyBean1.setHolidaySameUuid("uuid");

        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("4ad638be-40b2-45e9-8463-bb0e76d91c05");
        intervalStrategyDetailBean.setIntervalStrategyId("id");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        intervalStrategyBean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        intervalStrategyBean1.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        final List<IntervalStrategyBean> intervalStrategyBeans = Arrays.asList(intervalStrategyBean,intervalStrategyBean1);
        when(gridStrategyMapper.selectIntervalStrategyBySeasonId("seasonStragyId")).thenReturn(intervalStrategyBeans);

        final IntervalStrategyDetailBean intervalStrategyDetailBean1 = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean1.setId("4ad638be-40b2-45e9-8463-bb0e76d91c05");
        intervalStrategyDetailBean1.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean1.setPeriodStart(0);
        intervalStrategyDetailBean1.setPeriodEnd(0);
        intervalStrategyDetailBean1.setDetail("detail");
        final IntervalDetailBean intervalDetailBean1 = new IntervalDetailBean();
        intervalDetailBean1.setBeginTime("beginTime");
        intervalDetailBean1.setEndTime("endTime");
        intervalDetailBean1.setStrategyType(0);
        intervalStrategyDetailBean1.setDetailList(Arrays.asList(intervalDetailBean1));
        intervalStrategyDetailBean1.setGmtCreate("currentTime");
        intervalStrategyDetailBean1.setGmtModified("currentTime");
        final List<IntervalStrategyDetailBean> intervalStrategyDetailBeans = Arrays.asList(intervalStrategyDetailBean1);
        when(gridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(Arrays.asList("value"))).thenReturn(
                intervalStrategyDetailBeans);
        final IntervalStrategyBto result = gridStrategyService.selectStrategyDetailBySeasonStrategyId("seasonStragyId");
        result.setSeasonId("77");
        assertEquals("77", result.getSeasonId());
    }

    @Test
    public void testSelectStrategyDetailBySeasonStrategyId_GridStrategyMapperSelectIntervalStrategyBySeasonIdReturnsNoItems()
    {
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStragyId")).thenReturn(seasonStrategyBean);

        when(gridStrategyMapper.selectIntervalStrategyBySeasonId("seasonStragyId")).thenReturn(Collections.emptyList());

        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("4ad638be-40b2-45e9-8463-bb0e76d91c05");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        final List<IntervalStrategyDetailBean> intervalStrategyDetailBeans = Arrays.asList(intervalStrategyDetailBean);
        when(gridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(Arrays.asList("value"))).thenReturn(
                intervalStrategyDetailBeans);
        final IntervalStrategyBto result = gridStrategyService.selectStrategyDetailBySeasonStrategyId("seasonStragyId");
        result.setSeasonId("77");
        assertEquals("77", result.getSeasonId());

    }

    @Test
    public void testSelectStrategyDetailBySeasonStrategyId_GridStrategyMapperSelectIntervalStrategyDetailByIntervalStrategyIdsReturnsNoItems()
    {
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStragyId")).thenReturn(seasonStrategyBean);

        final IntervalStrategyBean intervalStrategyBean = new IntervalStrategyBean();
        intervalStrategyBean.setId("id");
        intervalStrategyBean.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean.setStartDate("startDate");
        intervalStrategyBean.setEndDate("endDate");
        intervalStrategyBean.setRemark("remark");
        intervalStrategyBean.setMode(0);
        intervalStrategyBean.setGmtCreate("currentTime");
        intervalStrategyBean.setGmtModified("currentTime");
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("4ad638be-40b2-45e9-8463-bb0e76d91c05");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        intervalStrategyBean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        final List<IntervalStrategyBean> intervalStrategyBeans = Arrays.asList(intervalStrategyBean);
        when(gridStrategyMapper.selectIntervalStrategyBySeasonId("seasonStragyId")).thenReturn(intervalStrategyBeans);

        when(gridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(Arrays.asList("value"))).thenReturn(
                Collections.emptyList());

        final IntervalStrategyBto result = gridStrategyService.selectStrategyDetailBySeasonStrategyId("seasonStragyId");
        result.setSeasonId("77");
        assertEquals("77", result.getSeasonId());

    }

    @Test
    public void testUpdateEnType()
    {
        final LogEnIntervalDetailBean bean = new LogEnIntervalDetailBean();
        bean.setBeginTime("beginTime");
        bean.setEndTime("endTime");

        gridStrategyService.updateEnType(bean, 0);
        assertEquals(bean.getStrategyType(), "trough");
        gridStrategyService.updateEnType(bean, 1);
        assertEquals(bean.getStrategyType(), "general");
        gridStrategyService.updateEnType(bean, 2);
        assertEquals(bean.getStrategyType(), "peak");
        gridStrategyService.updateEnType(bean, 3);
        assertEquals(bean.getStrategyType(), "highest");
    }

    @Test
    public void testUpdateZhType()
    {
        final LogZhIntervalDetailBean bean = new LogZhIntervalDetailBean();
        bean.setBeginTime("beginTime");
        bean.setEndTime("endTime");

        gridStrategyService.updateZhType(bean, 0);
        assertEquals(bean.getStrategyType(), "低谷");
        gridStrategyService.updateZhType(bean, 1);
        assertEquals(bean.getStrategyType(), "普通");
        gridStrategyService.updateZhType(bean, 2);
        assertEquals(bean.getStrategyType(), "高峰");
        gridStrategyService.updateZhType(bean, 3);
        assertEquals(bean.getStrategyType(), "最高");
    }

    @Test
    public void testUpdateEnMode()
    {
        final LogDetailEnBean logBean = new LogDetailEnBean();

        gridStrategyService.updateEnMode(logBean, 0);
        assertEquals(logBean.getMode(), "DAY");
        gridStrategyService.updateEnMode(logBean, 1);
        assertEquals(logBean.getMode(), "WEEK");
        gridStrategyService.updateEnMode(logBean, 2);
        assertEquals(logBean.getMode(), "MONTH");
    }

    @Test
    public void testUpdateZhMode()
    {

        final LogDetailZhBean logBean = new LogDetailZhBean();
        gridStrategyService.updateZhMode(logBean, 0);
        assertEquals(logBean.getMode(), "日");
        gridStrategyService.updateZhMode(logBean, 1);
        assertEquals(logBean.getMode(), "周");
        gridStrategyService.updateZhMode(logBean, 2);
        assertEquals(logBean.getMode(), "月");
    }

    @Test
    public void testCheckDateTime_ThrowsUedmException()
    {
        final IntervalStrategyBean strategyBean = new IntervalStrategyBean();
        strategyBean.setId("id");
        strategyBean.setSeasonStrategyId("seasonStrategyId");
        strategyBean.setStartDate("startDate");
        strategyBean.setEndDate("endDate");
        strategyBean.setRemark("remark");
        strategyBean.setMode(0);
        strategyBean.setGmtCreate("currentTime");
        strategyBean.setGmtModified("currentTime");
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setWeekStr("weekStr");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        strategyBean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        final List<IntervalStrategyBean> season = Arrays.asList(strategyBean);

        assertThrows(UedmException.class, () -> gridStrategyService.checkDateTime(season, "zh-CN"));
    }

    @Test
    public void testCheckList() throws Exception
    {

        final IntervalStrategyBean strategyBean = new IntervalStrategyBean();
        strategyBean.setId("id");
        strategyBean.setSeasonStrategyId("seasonStrategyId");
        strategyBean.setStartDate("01-01");
        strategyBean.setEndDate("06-30");
        strategyBean.setRemark("remark");
        strategyBean.setMode(0);
        final IntervalStrategyBean strategyBean1 = new IntervalStrategyBean();
        strategyBean1.setId("id");
        strategyBean1.setSeasonStrategyId("seasonStrategyId");
        strategyBean1.setStartDate("06-01");
        strategyBean1.setEndDate("12-31");
        strategyBean1.setRemark("remark");
        strategyBean1.setMode(0);

        final List<IntervalStrategyBean> season = Arrays.asList(strategyBean, strategyBean1);

        assertThrows(UedmException.class, () -> gridStrategyService.checkList(season, "zh-CN"));
    }

    @Test
    public void testCheckList_ThrowsUedmException()
    {
        final IntervalStrategyBean strategyBean = new IntervalStrategyBean();
        strategyBean.setId("id");
        strategyBean.setSeasonStrategyId("seasonStrategyId");
        strategyBean.setStartDate("01-01");
        strategyBean.setEndDate("12-30");
        strategyBean.setRemark("remark");
        strategyBean.setMode(0);
        final List<IntervalStrategyBean> season = Arrays.asList(strategyBean);
        assertThrows(UedmException.class, () -> gridStrategyService.checkList(season, "zh-CN"));
    }

    @Test
    public void testCheckMonthMode()
    {
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(1);
        intervalStrategyDetailBean.setPeriodEnd(30);
        intervalStrategyDetailBean.setWeekStr("weekStr");
        intervalStrategyDetailBean.setDetail("detail");
        final List<IntervalStrategyDetailBean> list = Arrays.asList(intervalStrategyDetailBean);
        assertThrows(UedmException.class, () -> gridStrategyService.checkMonthMode(list, "zh-CN"));
    }

    @Test
    public void testCheckMonthMode_ThrowsUedmException()
    {
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(1);
        intervalStrategyDetailBean.setPeriodEnd(25);
        intervalStrategyDetailBean.setWeekStr("weekStr");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalStrategyDetailBean intervalStrategyDetailBean1 = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean1.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean1.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean1.setPeriodStart(26);
        intervalStrategyDetailBean1.setPeriodEnd(30);
        intervalStrategyDetailBean1.setWeekStr("weekStr");
        intervalStrategyDetailBean1.setDetail("detail");
        final List<IntervalStrategyDetailBean> list = Arrays.asList(intervalStrategyDetailBean,
                intervalStrategyDetailBean1);
        assertThrows(UedmException.class, () -> gridStrategyService.checkMonthMode(list, "zh-CN"));

        intervalStrategyDetailBean1.setPeriodStart(28);
        final List<IntervalStrategyDetailBean> list1 = Arrays.asList(intervalStrategyDetailBean,
                intervalStrategyDetailBean1);
        assertThrows(UedmException.class, () -> gridStrategyService.checkMonthMode(list1, "zh-CN"));
    }

    @Test
    public void testCheckTimeCoverage_ThrowsUedmException()
    {
        final IntervalStrategyBean strategyBean = new IntervalStrategyBean();
        strategyBean.setId("id");
        strategyBean.setSeasonStrategyId("seasonStrategyId");
        strategyBean.setStartDate("startDate");
        strategyBean.setEndDate("endDate");
        strategyBean.setRemark("remark");
        strategyBean.setMode(0);
        strategyBean.setGmtCreate("currentTime");
        strategyBean.setGmtModified("currentTime");
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setWeekStr("weekStr");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        strategyBean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        final List<IntervalStrategyBean> list = Arrays.asList(strategyBean);

        assertThrows(UedmException.class, () -> gridStrategyService.checkTimeCoverage(list, "zh-CN"));
    }

    @Test
    public void testCheckWeekMode()
    {
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setWeekStr("[1,2,3,4,5]");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalStrategyDetailBean intervalStrategyDetailBean1 = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean1.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean1.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean1.setPeriodStart(0);
        intervalStrategyDetailBean1.setPeriodEnd(0);
        intervalStrategyDetailBean1.setWeekStr("[5,6,7]");
        intervalStrategyDetailBean1.setDetail("detail");

        final List<IntervalStrategyDetailBean> list = Arrays.asList(intervalStrategyDetailBean,
                intervalStrategyDetailBean1);
        assertThrows(UedmException.class, () -> gridStrategyService.checkWeekMode(list, "zh-CN"));
    }

    @Test
    public void testCheckWeekMode_ThrowsUedmException()
    {
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("631491d6-4f1c-4836-b0fb-c3762fc822ad");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setWeekStr("[1,2,3]");
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        final List<IntervalStrategyDetailBean> list = Arrays.asList(intervalStrategyDetailBean);

        assertThrows(UedmException.class, () -> gridStrategyService.checkWeekMode(list, "zh-CN"));
    }

    @Test
    public void testCheckHoilday_ThrowsUedmException()
    {
        assertThrows(UedmException.class, () -> gridStrategyService.checkHoilday(Arrays.asList(new HolidayDetailBean()), "language"));

        final HolidayDetailBean bean = new HolidayDetailBean();
        List<TimeIntervalBean> interval = new ArrayList<>();
        TimeIntervalBean timeIntervalBean = new TimeIntervalBean();
        timeIntervalBean.setStartDate("01-01");
        timeIntervalBean.setEndDate("01-03");
        final TimeIntervalBean timeIntervalBean1 = new TimeIntervalBean();
        timeIntervalBean1.setStartDate("01-02");
        timeIntervalBean1.setEndDate("01-04");
        interval.add(timeIntervalBean);
        interval.add(timeIntervalBean1);
        bean.setInterval(interval);
        final List<HolidayDetailBean> list = Arrays.asList(bean);

        assertThrows(UedmException.class, () -> gridStrategyService.checkHoilday(list, "language"));
    }

    @Test
    public void testGetSeasonStrategyName() throws Exception
    {
        // Setup
        // Configure GridStrategyMapper.queryById(...).
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStrategyId")).thenReturn(seasonStrategyBean);

        // Configure GridStrategyMapper.selectById(...).
        final ScopeStrategyResponseBean scopeStrategyResponseBean = new ScopeStrategyResponseBean();
        scopeStrategyResponseBean.setId("id");
        scopeStrategyResponseBean.setName("name");
        scopeStrategyResponseBean.setLogicGroup("logicGroup");
        scopeStrategyResponseBean.setScope(Arrays.asList("value"));
        scopeStrategyResponseBean.setGmtCreate("gmtCreate");
        scopeStrategyResponseBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.selectById("scopeStrategyId")).thenReturn(scopeStrategyResponseBean);

        // Run the test
        gridStrategyService.getSeasonStrategyName("seasonStrategyId", "en-US");
        final String result = gridStrategyService.getSeasonStrategyName("seasonStrategyId", "zh_CN");

        // Verify the results
        Assert.assertEquals("name > 生效日期 effectiveTime - expirationTime", result);
    }

    @Test
    public void testGetSeasonStrategyName_ThrowsUedmException()
    {
        // Setup
        // Configure GridStrategyMapper.queryById(...).
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStrategyId")).thenReturn(seasonStrategyBean);

        // Configure GridStrategyMapper.selectById(...).
        final ScopeStrategyResponseBean scopeStrategyResponseBean = new ScopeStrategyResponseBean();
        scopeStrategyResponseBean.setId("id");
        scopeStrategyResponseBean.setName("name");
        scopeStrategyResponseBean.setLogicGroup("logicGroup");
        scopeStrategyResponseBean.setScope(Arrays.asList("value"));
        scopeStrategyResponseBean.setGmtCreate("gmtCreate");
        scopeStrategyResponseBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.selectById("scopeStrategyId")).thenThrow(new RuntimeException("xx"));

        // Run the test
        String result = gridStrategyService.getSeasonStrategyName("seasonStrategyId", "zh_CN");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryOneSeasonStrategyFotTemplate() throws Exception
    {
        // Setup
        // Configure GridStrategyMapper.queryById(...).
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.queryById("seasonStrategyId")).thenReturn(seasonStrategyBean);

        // Configure GridStrategyMapper.selectById(...).
        final ScopeStrategyResponseBean scopeStrategyResponseBean = new ScopeStrategyResponseBean();
        scopeStrategyResponseBean.setId("id");
        scopeStrategyResponseBean.setName("name");
        scopeStrategyResponseBean.setLogicGroup("logicGroup");
        scopeStrategyResponseBean.setScope(Arrays.asList("value"));
        scopeStrategyResponseBean.setGmtCreate("gmtCreate");
        scopeStrategyResponseBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.selectById("scopeStrategyId")).thenReturn(scopeStrategyResponseBean);

        // Configure GridStrategyMapper.selectIntervalStrategyBySeasonId(...).
        final IntervalStrategyBean intervalStrategyBean = new IntervalStrategyBean();
        intervalStrategyBean.setId("intervalStrategyId");
        intervalStrategyBean.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean.setStartDate("begin");
        intervalStrategyBean.setEndDate("end");
        intervalStrategyBean.setRemark("remark");
        intervalStrategyBean.setMode(0);
        intervalStrategyBean.setGmtCreate("currentTime");
        intervalStrategyBean.setGmtModified("currentTime");
        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("eae5ef67-c226-4797-9522-a68684e63610");
        intervalStrategyDetailBean.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setWeekStr("[1,2,3]");

        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        //intervalDetailBean
        intervalStrategyDetailBean.setDetail(JSON.toJSONString(intervalDetailBean));
        //System.out.println("xxx"+JSON.toJSONString(intervalDetailBean));
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        intervalStrategyBean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        // Configure GridStrategyMapper.selectIntervalStrategyBySeasonId(...).
        final IntervalStrategyBean intervalStrategyBean2 = new IntervalStrategyBean();
        intervalStrategyBean2.setId("intervalStrategyId");
        intervalStrategyBean2.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean2.setStartDate("begin");
        intervalStrategyBean2.setEndDate("end");
        intervalStrategyBean2.setRemark("remark");
        intervalStrategyBean2.setMode(1);
        intervalStrategyBean2.setGmtCreate("currentTime");
        intervalStrategyBean2.setGmtModified("currentTime");
        intervalStrategyBean2.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        // Configure GridStrategyMapper.selectIntervalStrategyBySeasonId(...).
        final IntervalStrategyBean intervalStrategyBean3 = new IntervalStrategyBean();
        intervalStrategyBean3.setId("intervalStrategyId");
        intervalStrategyBean3.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean3.setStartDate("begin");
        intervalStrategyBean3.setEndDate("end");
        intervalStrategyBean3.setRemark("remark");
        intervalStrategyBean3.setMode(2);
        intervalStrategyBean3.setGmtCreate("currentTime");
        intervalStrategyBean3.setGmtModified("currentTime");
        intervalStrategyBean3.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        // Configure GridStrategyMapper.selectIntervalStrategyBySeasonId(...).
        final IntervalStrategyBean intervalStrategyBean4 = new IntervalStrategyBean();
        intervalStrategyBean4.setId("intervalStrategyId");
        intervalStrategyBean4.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean4.setStartDate("begin");
        intervalStrategyBean4.setEndDate("end");
        intervalStrategyBean4.setRemark("remark");
        intervalStrategyBean4.setMode(3);
        intervalStrategyBean4.setGmtCreate("currentTime");
        intervalStrategyBean4.setGmtModified("currentTime");
        intervalStrategyBean4.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        final IntervalStrategyBean intervalStrategyBean5 = new IntervalStrategyBean();
        intervalStrategyBean5.setId("intervalStrategyId");
        intervalStrategyBean5.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean5.setStartDate("begin");
        intervalStrategyBean5.setEndDate("end");
        intervalStrategyBean5.setRemark("remark");
        intervalStrategyBean5.setMode(3);
        intervalStrategyBean5.setHolidaySameUuid("1");
        intervalStrategyBean5.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        final IntervalStrategyBean intervalStrategyBean6 = new IntervalStrategyBean();
        intervalStrategyBean6.setId("intervalStrategyId");
        intervalStrategyBean6.setSeasonStrategyId("seasonStrategyId");
        intervalStrategyBean6.setStartDate("begin");
        intervalStrategyBean6.setEndDate("end");
        intervalStrategyBean6.setRemark("remark");
        intervalStrategyBean6.setMode(3);
        intervalStrategyBean6.setHolidaySameUuid("1");
        intervalStrategyBean6.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));

        List<IntervalStrategyBean> intervalStrategyBeans = new ArrayList<>();
        intervalStrategyBeans.add(intervalStrategyBean);
        intervalStrategyBeans.add(intervalStrategyBean2);
        intervalStrategyBeans.add(intervalStrategyBean3);
        intervalStrategyBeans.add(intervalStrategyBean4);
        intervalStrategyBeans.add(intervalStrategyBean5);
        intervalStrategyBeans.add(intervalStrategyBean6);

        when(gridStrategyMapper.selectIntervalStrategyBySeasonId("seasonStrategyId")).thenReturn(intervalStrategyBeans);

        // Configure GridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(...).
        final IntervalStrategyDetailBean intervalStrategyDetailBean1 = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean1.setId("eae5ef67-c226-4797-9522-a68684e63610");
        intervalStrategyDetailBean1.setIntervalStrategyId("intervalStrategyId");
        intervalStrategyDetailBean1.setPeriodStart(0);
        intervalStrategyDetailBean1.setPeriodEnd(0);
        intervalStrategyDetailBean1.setWeekStr("[1,2,3]");
        final IntervalDetailBean intervalDetailBean1 = new IntervalDetailBean();
        intervalDetailBean1.setBeginTime("beginTime");
        intervalDetailBean1.setEndTime("endTime");
        intervalDetailBean1.setStrategyType(0);
        intervalStrategyDetailBean1.setDetail(JSON.toJSONString(Arrays.asList(intervalDetailBean1)));
        intervalStrategyDetailBean1.setDetailList(Arrays.asList(intervalDetailBean1));
        intervalStrategyDetailBean1.setGmtCreate("currentTime");
        intervalStrategyDetailBean1.setGmtModified("currentTime");
        final List<IntervalStrategyDetailBean> detailBeans = Arrays.asList(intervalStrategyDetailBean1);
        when(gridStrategyMapper.selectIntervalStrategyDetailByIntervalStrategyIds(any())).thenReturn(detailBeans);

        // Run the test
        final SeasonStrategyForTemplateVo result = gridStrategyService.queryOneSeasonStrategyFotTemplate(
                "seasonStrategyId", "languageOption");

        // Verify the results
        Assert.assertEquals("seasonStrategyId", result.getSeasonStrategyId());
    }

    @Test
    public void testQueryOneSeasonStrategyFotTemplate_ThrowsUedmException()
    {
        // Setup
        // Configure GridStrategyMapper.queryById(...).
        final SeasonStrategyBean seasonStrategyBean = new SeasonStrategyBean();
        seasonStrategyBean.setId("seasonStrategyId");
        seasonStrategyBean.setScopeStrategyId("scopeStrategyId");
        seasonStrategyBean.setStatus(0);
        seasonStrategyBean.setEffectiveTime("effectiveTime");
        seasonStrategyBean.setExpirationTime("expirationTime");
        seasonStrategyBean.setGmtCreate("currentTime");
        seasonStrategyBean.setGmtModified("gmtModified");
        when(gridStrategyMapper.selectIntervalStrategyBySeasonId("seasonStrategyId")).thenThrow(
                new RuntimeException(""));

        // Run the test
        try
        {
            gridStrategyService.queryOneSeasonStrategyFotTemplate("seasonStrategyId", "languageOption");
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), "queryOneSeasonStrategyFotTemplate failed!");
        }

    }

    @Test
    public void testGetStrategyCombination() throws Exception
    {
        // Setup
        // Configure GridStrategyMapper.getStrategyCombination(...).
        final StrategyCombinationVo strategyCombinationVo = new StrategyCombinationVo();
        strategyCombinationVo.setScopeStrategyName("scopeStrategyName");
        strategyCombinationVo.setSeasonStrategyId("seasonStrategyId");
        strategyCombinationVo.setEffectiveTime("effectiveTime");
        strategyCombinationVo.setExpirationTime("expirationTime");
        strategyCombinationVo.setMode(0);
        strategyCombinationVo.setStatus(0);
        final List<StrategyCombinationVo> strategyCombinationVos = Arrays.asList(strategyCombinationVo);
        when(gridStrategyMapper.getStrategyCombination(Mockito.any())).thenReturn(strategyCombinationVos);

        // Run the test
        gridStrategyService.getStrategyCombination("sortBy", "desc", "");
        final List<StrategyCombinationVo> result = gridStrategyService.getStrategyCombination("sortBy", "asc", "");

        // Verify the results
        assertEquals(result.size(), 1);
    }

    @Test
    public void testGetStrategyCombination_ThrowsUedmException()
    {
        // Setup
        // Configure GridStrategyMapper.getStrategyCombination(...).
        final StrategyCombinationVo strategyCombinationVo = new StrategyCombinationVo();
        strategyCombinationVo.setScopeStrategyName("scopeStrategyName");
        strategyCombinationVo.setSeasonStrategyId("seasonStrategyId");
        strategyCombinationVo.setEffectiveTime("effectiveTime");
        strategyCombinationVo.setExpirationTime("expirationTime");
        strategyCombinationVo.setMode(0);
        strategyCombinationVo.setStatus(0);
        final List<StrategyCombinationVo> strategyCombinationVos = Arrays.asList(strategyCombinationVo);
        when(gridStrategyMapper.getStrategyCombination(Mockito.any())).thenThrow(new RuntimeException(""));

        // Run the test
        try
        {
            gridStrategyService.getStrategyCombination("sortBy", "desc", "");
        }
        catch (Exception e)
        {
            assertEquals(e.getMessage(), "getStrategyCombination failed!");
        }
    }

    @Test
    public void testGetResult()
    {
        final IntervalStrategyBto bto = new IntervalStrategyBto();
        final IntervalStrategyBean bean = new IntervalStrategyBean();
        bean.setId("id");
        bean.setSeasonStrategyId("seasonStrategyId");
        bean.setStartDate("begin");
        bean.setEndDate("end");
        bean.setMode(0);

        final IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("2786df2a-7643-4ebc-a855-e3aeb109e89d");
        intervalStrategyDetailBean.setIntervalStrategyId("id");
        intervalStrategyDetailBean.setPeriodStart(0);
        intervalStrategyDetailBean.setPeriodEnd(0);
        intervalStrategyDetailBean.setDetail("detail");
        final IntervalDetailBean intervalDetailBean = new IntervalDetailBean();
        intervalDetailBean.setBeginTime("beginTime");
        intervalDetailBean.setEndTime("endTime");
        intervalDetailBean.setStrategyType(0);
        intervalStrategyDetailBean.setDetailList(Arrays.asList(intervalDetailBean));
        intervalStrategyDetailBean.setGmtCreate("currentTime");
        intervalStrategyDetailBean.setGmtModified("currentTime");
        bean.setDetailStrategy(Arrays.asList(intervalStrategyDetailBean));
        bean.setHolidaySameUuid("holidaySameUuid");
        bto.setSeason(Arrays.asList(bean));
        final HolidayDetailBean holidayDetailBean = new HolidayDetailBean();
        final TimeIntervalBean timeIntervalBean = new TimeIntervalBean();
        timeIntervalBean.setStartDate("begin");
        timeIntervalBean.setEndDate("end");
        timeIntervalBean.setRemark("remark");
        holidayDetailBean.setInterval(Arrays.asList(timeIntervalBean));
        final IntervalDetailBean intervalDetailBean1 = new IntervalDetailBean();
        intervalDetailBean1.setBeginTime("beginTime");
        intervalDetailBean1.setEndTime("endTime");
        intervalDetailBean1.setStrategyType(0);
        holidayDetailBean.setDetailList(Arrays.asList(intervalDetailBean1));
        bto.setHoliday(new ArrayList<>(Arrays.asList(holidayDetailBean)));

        final List<IntervalStrategyBean> collect = Arrays.asList(bean);
        final Map<String, List<IntervalStrategyDetailBean>> detailMap = new HashMap<>();

        final IntervalStrategyBto result = gridStrategyService.getResult(bto, collect, detailMap);
        assertEquals(result.getHoliday().size(),2);
    }

    @Test
    public void syncScopeDataRpc_Test_When_OperationIsScope_strategy_add_Except_ToSuccess() throws UedmException {

        final ScopeStrategyRequestBean bean = new ScopeStrategyRequestBean();
        bean.setName("name");
        bean.setScope(Arrays.asList("value"));
        bean.setLogicGroup("logicGroup");
        bean.setGmtCreate("gmtCreate");
        bean.setGmtModified("gmtModified");

        when(dateTimeService.getCurrentTime()).thenReturn("gmtModified");
        when(gridStrategyMapper.edit(any(ScopeStrategyRequestBean.class))).thenReturn(0);
        final Integer result = gridStrategyService.syncScopeDataRpc(bean, "scope_strategy_add");
        assertEquals(0, result);
    }

    @Test
    public void syncScopeDataRpc_Test_When_OperationIsScope_strategy_edit_Except_ToSuccess() throws UedmException {

        final ScopeStrategyRequestBean bean = new ScopeStrategyRequestBean();
        bean.setId("1111");
        bean.setName("name");
        bean.setScope(Arrays.asList("value"));
        bean.setLogicGroup("logicGroup");
        bean.setGmtCreate("gmtCreate");
        bean.setGmtModified("gmtModified");

        when(dateTimeService.getCurrentTime()).thenReturn("gmtModified");
        when(gridStrategyMapper.edit(any(ScopeStrategyRequestBean.class))).thenReturn(0);
        final Integer result = gridStrategyService.syncScopeDataRpc(bean, "scope_strategy_edit");
        assertEquals(0, result);
    }

    @Test
    public void syncScopeDataRpc_Test_When_OperationIsScope_strategy_delete_Except_ToSuccess() throws UedmException {

        final ScopeStrategyRequestBean bean = new ScopeStrategyRequestBean();
        bean.setId("1111");

        when(dateTimeService.getCurrentTime()).thenReturn("gmtModified");
        when(gridStrategyMapper.edit(any(ScopeStrategyRequestBean.class))).thenReturn(0);
        final Integer result = gridStrategyService.syncScopeDataRpc(bean, "scope_strategy_delete");
        assertEquals(0, result);
    }

    @Test
    public void selectBySeasonStrategyId_Test_WhenNormalReturn_ExpectSizeZer()
    {
        Mockito.doReturn(new ArrayList<>()).when(templateStrategyMapper).selectBySeasonStrategyId(Mockito.any());

        List<TemplateStrategyBo> templateStrategyBos = gridStrategyService.selectTemplateDetailsBySeasonStrategyId("1");
        Assert.assertEquals(0,templateStrategyBos.size());
    }

    /**
     * 电价策略调整-配置同步至电池
     * DB更新与新增操作模拟-无异常
     */
    private void synCfgPricePolicyModifyRpc_Normal_Mock() {
        doNothing().when(gridStrategyMapper).updateLogicGroup(Mockito.any(SynScopeStrategyBean.class));
        doNothing().when(gridStrategyMapper).insertAddBean(Mockito.any(SynScopeStrategyBean.class));
        doNothing().when(gridStrategyMapper).batchInsertPriceStrategy(Mockito.anyList());
        doNothing().when(gridStrategyMapper).batchInsertPriceDetail(Mockito.anyList());
        doNothing().when(gridStrategyMapper).batchInsertSeasonStrategy(Mockito.anyList());
        when(gridStrategyMapper.addIntervalStrategy(Mockito.anyList())).thenReturn(1);
        when(gridStrategyMapper.addIntervalStrategyDetail(Mockito.anyList())).thenReturn(1);
    }

    /**
     * Rpc接口Body-Empty校验失败
     */
    @Test
    public void synCfgPricePolicyModifyRpc_Test_Body_Empty() throws UedmException {
        synCfgPricePolicyModifyRpc_Normal_Mock();
        boolean result = gridStrategyService.synCfgPricePolicyModifyRpc(null);
        Assert.assertEquals(Boolean.FALSE, result);
    }

    /**
     * Rpc接口Body-Empty校验成功-更新与新增对象均为null
     */
    @Test
    public void synCfgPricePolicyModifyRpc_Test_UpdateAndAdd_Empty() throws UedmException {
        SynCfgDataToBatteryBean synCfgDataToBatteryBean = new SynCfgDataToBatteryBean();
        synCfgDataToBatteryBean.setUpdateSynBean(null);
        synCfgDataToBatteryBean.setAddSynBean(null);

        synCfgPricePolicyModifyRpc_Normal_Mock();

        boolean result = gridStrategyService.synCfgPricePolicyModifyRpc(synCfgDataToBatteryBean);

        Assert.assertEquals(Boolean.FALSE, result);
    }

    /**
     * Rpc接口Body-Empty校验成功-更新与新增对象其中之一为null
     */
    @Test
    public void synCfgPricePolicyModifyRpc_Test_Add_Empty() throws UedmException {
        SynScopeStrategyBean updateSynBean = new SynScopeStrategyBean();
        updateSynBean.setLogicGroup("origin group split some object(s)");

        SynCfgDataToBatteryBean synCfgDataToBatteryBean = new SynCfgDataToBatteryBean();
        synCfgDataToBatteryBean.setUpdateSynBean(updateSynBean);
        synCfgDataToBatteryBean.setAddSynBean(null);

        synCfgPricePolicyModifyRpc_Normal_Mock();

        boolean result = gridStrategyService.synCfgPricePolicyModifyRpc(synCfgDataToBatteryBean);

        Assert.assertEquals(Boolean.FALSE, result);
    }

    /**
     * Rpc接口Body-正常流程
     */
    @Test
    public void synCfgPricePolicyModifyRpc_Test_() throws UedmException {
        SynScopeStrategyBean updateSynBean = new SynScopeStrategyBean();
        updateSynBean.setLogicGroup("origin group split some object(s)");

        TieredPriceStrategyBean tieredPriceStrategyBean = new TieredPriceStrategyBean();
        tieredPriceStrategyBean.setLowPrice("1");
        tieredPriceStrategyBean.setNormalPrice("2");
        tieredPriceStrategyBean.setPeakPrice("3");
        tieredPriceStrategyBean.setPeakestPrice("4");
        List<TieredPriceStrategyBean> tieredPriceStrategyBeans = new ArrayList<>(1);
        tieredPriceStrategyBeans.add(tieredPriceStrategyBean);
        PriceIntervalStrategyBean priceIntervalStrategyBean = new PriceIntervalStrategyBean();
        priceIntervalStrategyBean.setTieredPriceStrategyInfoList(tieredPriceStrategyBeans);
        List<PriceIntervalStrategyBean> priceIntervalStrategyBeans = new ArrayList<>(1);
        priceIntervalStrategyBeans.add(priceIntervalStrategyBean);
        List<PriceStrategySynBatteryBean> priceStrategySynBatteryBeans = new ArrayList<>(1);
        PriceStrategySynBatteryBean priceStrategySynBatteryBean = new PriceStrategySynBatteryBean();
        priceStrategySynBatteryBean.setPriceIntervalStrategyBeans(priceIntervalStrategyBeans);
        priceStrategySynBatteryBeans.add(priceStrategySynBatteryBean);

        SynScopeStrategyBean addSynBean = getSynScopeStrategyBean(priceStrategySynBatteryBeans);

        SynCfgDataToBatteryBean synCfgDataToBatteryBean = new SynCfgDataToBatteryBean();
        synCfgDataToBatteryBean.setUpdateSynBean(updateSynBean);
        synCfgDataToBatteryBean.setAddSynBean(addSynBean);

        synCfgPricePolicyModifyRpc_Normal_Mock();

        boolean result = gridStrategyService.synCfgPricePolicyModifyRpc(synCfgDataToBatteryBean);

        Assert.assertEquals(Boolean.TRUE, result);
    }

    @NotNull
    private static SynScopeStrategyBean getSynScopeStrategyBean(List<PriceStrategySynBatteryBean> priceStrategySynBatteryBeans) {
        IntervalStrategyDetailBean intervalStrategyDetailBean = new IntervalStrategyDetailBean();
        intervalStrategyDetailBean.setId("intervalStrategyDetailId");
        List<IntervalStrategyDetailBean> detailStrategy = new ArrayList<>(1);
        detailStrategy.add(intervalStrategyDetailBean);
        IntervalStrategyBean intervalStrategyBean = new IntervalStrategyBean();
        intervalStrategyBean.setDetailStrategy(detailStrategy);
        List<IntervalStrategyBean> intervalStrategyBeans = new ArrayList<>(1);
        intervalStrategyBeans.add(intervalStrategyBean);
        SeasonStrategySynBatterBean seasonStrategySynBatterBean = new SeasonStrategySynBatterBean();
        seasonStrategySynBatterBean.setIntervalStrategyBeans(intervalStrategyBeans);
        List<SeasonStrategySynBatterBean> seasonStrategySynBatterBeans = new ArrayList<>(1);
        seasonStrategySynBatterBeans.add(seasonStrategySynBatterBean);

        SynScopeStrategyBean addSynBean = new SynScopeStrategyBean();
        addSynBean.setLogicGroup("the split object(s)");
        addSynBean.setPriceStrategySynBatteryBeans(priceStrategySynBatteryBeans);
        addSynBean.setSeasonStrategySynBatterBeans(seasonStrategySynBatterBeans);
        return addSynBean;
    }

    /**
     * 价格及季节策略无新增
     */
    @Test
    public void synCfgPricePolicyModifyRpc_Test_Only_Scope() throws UedmException {
        SynScopeStrategyBean updateSynBean = new SynScopeStrategyBean();
        updateSynBean.setLogicGroup("origin group split some object(s)");

        SynScopeStrategyBean addSynBean = new SynScopeStrategyBean();
        addSynBean.setLogicGroup("the split object(s)");
        addSynBean.setPriceStrategySynBatteryBeans(null);
        addSynBean.setSeasonStrategySynBatterBeans(null);

        SynCfgDataToBatteryBean synCfgDataToBatteryBean = new SynCfgDataToBatteryBean();
        synCfgDataToBatteryBean.setUpdateSynBean(updateSynBean);
        synCfgDataToBatteryBean.setAddSynBean(addSynBean);

        synCfgPricePolicyModifyRpc_Normal_Mock();

        boolean result = gridStrategyService.synCfgPricePolicyModifyRpc(synCfgDataToBatteryBean);

        Assert.assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void selectByConditionTest() throws UedmException
    {
        PowerMockito.when(gridStrategyMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(gridStrategyMapper.queryRootStrategy(Mockito.any())).thenReturn(Arrays.asList(new ScopeStrategyResponseBean()));
        List<ScopeStrategyResponseBean> result = gridStrategyService.selectByCondition(new ArrayList<>());
        Assert.assertEquals(result.size(), 1);
    }

    /* Started by AICoder, pid:e67916a29d6548e0a4cb1650106a27d1 */
    @Test
    public void testGetIntervalStrategyType_EmptyList() {
        IntervalStrategyQueryBean queryBean = new IntervalStrategyQueryBean();
        List<IntervalStrategyDetailBean> intervalStrategyDetailBeanList = new ArrayList<>();
        // 设置queryBean状态列表
        List<Integer> statusList = new ArrayList<>();
        statusList.add(SeasonStrategyStatusEnum.EFFECTING.getCode());
        statusList.add(SeasonStrategyStatusEnum.FINISHED.getCode());
        queryBean.setStatus(statusList);

        // 模拟gridStrategyMapper返回空列表
        when(gridStrategyMapper.selectIntervalStrategyDetailWithSeason(any())).thenReturn(intervalStrategyDetailBeanList);

        // 调用方法
        List<IntervalStrategyTypeVo> result = gridStrategyService.getIntervalStrategyType();

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetIntervalStrategyType_NonEmptyList() {
        IntervalStrategyQueryBean queryBean = new IntervalStrategyQueryBean();
        List<IntervalStrategyDetailBean> intervalStrategyDetailList = new ArrayList<>();
        // 设置queryBean状态列表
        List<Integer> statusList = new ArrayList<>();
        statusList.add(SeasonStrategyStatusEnum.EFFECTING.getCode());
        statusList.add(SeasonStrategyStatusEnum.FINISHED.getCode());
        queryBean.setStatus(statusList);

        // 创建一个包含详细信息的列表
        IntervalStrategyDetailBean detailBean1 = new IntervalStrategyDetailBean();
        detailBean1.setDetail("[{\"strategyType\":1},{\"strategyType\":2}]");
        IntervalStrategyDetailBean detailBean2 = new IntervalStrategyDetailBean();
        detailBean2.setDetail("[{\"strategyType\":3}]");
        intervalStrategyDetailList.add(detailBean2);
        intervalStrategyDetailList.add(detailBean1);

        // 模拟gridStrategyMapper返回非空列表
        when(gridStrategyMapper.selectIntervalStrategyDetailWithSeason(any())).thenReturn(intervalStrategyDetailList);

        // 调用方法
        List<IntervalStrategyTypeVo> result = gridStrategyService.getIntervalStrategyType();

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());
    }
    /* Ended by AICoder, pid:e67916a29d6548e0a4cb1650106a27d1 */

    /* Started by AICoder, pid:f6c7d796b0fd4a4c901cf6d02a5dce7f */
    @Test(expected = UedmException.class)
    public void testGetTemplatePriceIntervalWithNullId() throws UedmException {
        gridStrategyService.getTemplatePriceInterval(null, "deviceType", serviceBaseInfoBean);
    }

    @Test(expected = UedmException.class)
    public void testGetTemplatePriceIntervalWithEmptyId() throws UedmException {
        gridStrategyService.getTemplatePriceInterval("", "deviceType", serviceBaseInfoBean);
    }

    @Test(expected = UedmException.class)
    public void testGetTemplatePriceIntervalWithNullServiceBaseInfoBean() throws UedmException {
        gridStrategyService.getTemplatePriceInterval("1", "deviceType", null);
    }

    @Test
    public void testGetTemplatePriceIntervalWithValidData() throws UedmException {
        PendingBean pb1 = new PendingBean();
        pb1.setDeviceId("1");
        pb1.setSeasonStrategyId("strategy1");
        PendingBean pb2 = new PendingBean();
        pb2.setDeviceId("2");
        pb2.setSeasonStrategyId("strategy2");
        PendingBean pb3 = new PendingBean();
        pb3.setDeviceId("3");
        pb3.setSeasonStrategyId("strategy3");
        List<String> idList = Arrays.asList("1", "2", "3");
        DevicePeakCacheInfoBean bean1 = new DevicePeakCacheInfoBean();
        bean1.setDeviceId("1");
        bean1.setDeviceType("SNMP");
        DevicePeakCacheInfoBean bean2 = new DevicePeakCacheInfoBean();
        bean2.setDeviceId("2");
        bean2.setDeviceType("SNMP");
        DevicePeakCacheInfoBean bean3 = new DevicePeakCacheInfoBean();
        bean3.setDeviceId("3");
        bean3.setDeviceType("SNMP");
        List<DevicePeakCacheInfoBean> list1 = new ArrayList<>();
        list1.add(bean1);
        list1.add(bean2);
        list1.add(bean3);
        when(configurationManagerRpc.queryAllList()).thenReturn(list1);

        when(peakShiftIssuedTaskService.filterDeviceIdByEnablePeak(any())).thenReturn(idList);
        when(peakShiftIssuedTaskService.filterDeviceIdByPositionAndTemplate(any(), any(), any())).thenReturn(idList);
        List<PendingBean> list = Arrays.asList(pb1, pb2);
        List<PendingBean> pendingList = Arrays.asList(pb3);
        when(gridStrategyMapper.queryTemplatePriceInterval()).thenReturn(list);
        when(peakShiftTaskDetailMapper.queryAllPendingDevice()).thenReturn(pendingList);

        List<TemplatePriceIntervalBean> result = gridStrategyService.getTemplatePriceInterval("1", PeakDeviceTypeEnum.DIRECT.id, serviceBaseInfoBean);
        assertNotNull(result);
        assertEquals(3, result.size());
    }
    /* Ended by AICoder, pid:f6c7d796b0fd4a4c901cf6d02a5dce7f */
}
