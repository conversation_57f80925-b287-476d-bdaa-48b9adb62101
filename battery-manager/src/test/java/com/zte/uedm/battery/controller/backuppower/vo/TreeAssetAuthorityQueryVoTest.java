package com.zte.uedm.battery.controller.backuppower.vo;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;


public class TreeAssetAuthorityQueryVoTest {
    @Test
    public void test() {
        TreeAssetAuthorityQueryVo treeAssetAuthorityQueryVo = new TreeAssetAuthorityQueryVo();
        List<String> mocList = new ArrayList<>();
        mocList.add("moc1");
        treeAssetAuthorityQueryVo.setExcludeMo(true);
        treeAssetAuthorityQueryVo.setFlag(true);
        treeAssetAuthorityQueryVo.setScene("1");
        treeAssetAuthorityQueryVo.setId("test");
        treeAssetAuthorityQueryVo.setMocList(mocList);
        treeAssetAuthorityQueryVo.setNodeName("test");
        treeAssetAuthorityQueryVo.setPageNo(1);
        treeAssetAuthorityQueryVo.setPageSize(1);

        Assert.assertEquals(true,treeAssetAuthorityQueryVo.getExcludeMo());
        Assert.assertEquals(true,treeAssetAuthorityQueryVo.getFlag());
        Assert.assertEquals("test",treeAssetAuthorityQueryVo.getId());
        Assert.assertEquals("test",treeAssetAuthorityQueryVo.getNodeName());
        Assert.assertEquals("1",treeAssetAuthorityQueryVo.getScene());
        Assert.assertEquals("moc1",treeAssetAuthorityQueryVo.getMocList().get(0));
        Assert.assertNotNull(treeAssetAuthorityQueryVo.getPageNo());
        Assert.assertNotNull(treeAssetAuthorityQueryVo.getPageSize());
        Assert.assertNotNull(treeAssetAuthorityQueryVo.toString());
    }
}