package com.zte.uedm.battery.bean.peak;

import com.zte.uedm.battery.enums.peak.PeakShiftDeviceStatusEnum;
import org.junit.Assert;
import org.junit.Test;

public class PeakShiftTaskDetailPoTest {

    @Test
    public void test()
    {
        PeakShiftTaskDetailPo bean = new PeakShiftTaskDetailPo();
        bean.setId("id");
        bean.setTaskId("taskId");
        bean.setStatus("success");
        bean.setStatus("fail");
        bean.setDeviceId("deviceId");
        bean.setCreator("admin");
        bean.setGmtCreate("2020-01-02 00:00:00");
        bean.setUpdater("admin");
        bean.setGmtModified("2020-01-02 00:00:00");

        bean.getId();
        bean.getTaskId();
        bean.getStatus();
        bean.getDeviceId();
        bean.getCreator();
        bean.getGmtCreate();
        bean.getUpdater();
        bean.getGmtModified();
        bean.toString();

        bean.equals(bean);
        bean.equals(new PeakShiftTaskDetailPo());

        Assert.assertSame("id", bean.getId());
    }
}
