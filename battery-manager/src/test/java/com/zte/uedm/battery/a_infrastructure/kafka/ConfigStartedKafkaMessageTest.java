package com.zte.uedm.battery.a_infrastructure.kafka;/* Started by AICoder, pid:1132f5e96a2b2d0141dc0b1c00b81b71aca7f910 */

import com.zte.uedm.basis.cons.MicroServiceOptional;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.InitRunner;
import com.zte.uedm.component.kafka.producer.bean.KafkaMessageBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonUtils.class})
public class ConfigStartedKafkaMessageTest {

    @InjectMocks
    private ConfigStartedKafkaMessage configStartedKafkaMessage;

    @Mock
    private InitRunner initRunner;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:t9f52v9eb5oee301474a0a3b20252f41f5f5f64c */
    @Test
    public void testOnMsg_WhenServiceNameIsConfigManager_ThenInitCache() throws Exception {
        // Given
        String msg = "{\"serviceName\":\"MICRO_SERVICE_CONFIG_MANAGER\"}";
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setServiceName(MicroServiceOptional.MICRO_SERVICE_CONFIG_MANAGER.getId());
        PowerMockito.mockStatic(JsonUtils.class);
        when(JsonUtils.jsonToObject(msg, KafkaMessageBean.class)).thenReturn(kafkaMessageBean);

        // When
        configStartedKafkaMessage.onMsg(msg);

        // Then
        verify(initRunner, times(1)).run(null);
    }

    @Test
    public void testOnMsg_WhenServiceNameIsNotConfigManager_ThenDoNotInitCache() throws Exception {
        // Given
        String msg = "{\"serviceName\":\"NOT_MICRO_SERVICE_CONFIG_MANAGER\"}";
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setServiceName("NOT_MICRO_SERVICE_CONFIG_MANAGER");
        PowerMockito.mockStatic(JsonUtils.class);
        when(JsonUtils.jsonToObject(msg, KafkaMessageBean.class)).thenReturn(kafkaMessageBean);

        // When
        configStartedKafkaMessage.onMsg(msg);

        // Then
        verify(initRunner, never()).run(null);
    }

    @Test
    public void testOnMsg_WhenJsonToObjectThrowsException_ThenLogError() throws Exception {
        // Given
        String msg = "invalidjson";
        PowerMockito.mockStatic(JsonUtils.class);
        when(JsonUtils.jsonToObject(msg, KafkaMessageBean.class)).thenThrow(new UedmException(-1, "Json parsing error"));

        // When
        configStartedKafkaMessage.onMsg(msg);

        // Then
        verify(initRunner, never()).run(null);
    }
    /* Ended by AICoder, pid:t9f52v9eb5oee301474a0a3b20252f41f5f5f64c */
}
