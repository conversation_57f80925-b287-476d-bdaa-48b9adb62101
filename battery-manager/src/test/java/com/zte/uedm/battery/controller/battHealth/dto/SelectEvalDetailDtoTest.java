package com.zte.uedm.battery.controller.battHealth.dto;

import com.zte.uedm.battery.bean.BatteryHealthDimensionsBean;
import com.zte.uedm.battery.bean.PojoTestUtil;
import com.zte.uedm.battery.controller.batthealth.dto.SelectEvalDetailDto;
import com.zte.uedm.common.exception.UedmException;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SelectEvalDetailDtoTest
{

    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
        SelectEvalDetailDto bean = new SelectEvalDetailDto();
        bean.setOrder("position");
        bean.setSort("desc");
        bean.setLogicGroupId("1");
        BatteryHealthDimensionsBean batteryHealthDimensionsBean = new BatteryHealthDimensionsBean();
        batteryHealthDimensionsBean.setId("name");
        bean.setBattHealthdims(Arrays.asList(batteryHealthDimensionsBean));
        bean.setPageNo(1);
        bean.setPageSize(1);
        bean.checkOrderSort();
        bean.setOrder("");
        bean.setSort("");
        bean.checkOrderSort();
        bean.getLogicGroupId();
        bean.getOrder();
        bean.getSort();
        bean.toString();
        bean.toString();
        Assert.assertEquals("1", bean.getLogicGroupId());
    }
    @Test
    public void test_empty() throws Exception
    {
        PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
        SelectEvalDetailDto bean = new SelectEvalDetailDto();
        bean.setOrder("position");
        bean.setSort("desc");
        bean.setLogicGroupId("1");
        BatteryHealthDimensionsBean batteryHealthDimensionsBean = new BatteryHealthDimensionsBean();
        bean.setBattHealthdims(Arrays.asList(batteryHealthDimensionsBean));
        bean.setPageNo(1);
        bean.setPageSize(1);
        bean.checkOrderSort();
        bean.setOrder("");
        bean.setSort("");
        bean.checkOrderSort();
        bean.getLogicGroupId();
        bean.getOrder();
        bean.getSort();
        bean.toString();
        bean.toString();
        Assert.assertEquals("1", bean.getLogicGroupId());
        boolean b = bean.checkBattHealthDimsIsEmpty();
        Assert.assertSame(b,false);
        Pair<Boolean, List<String>> booleanListPair = bean.checkBattHealthdimsInRange();
        Assert.assertEquals(8,booleanListPair.getRight().size());
    }
    @Test
    public void test_out_range() throws Exception
    {
        PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
        SelectEvalDetailDto bean = new SelectEvalDetailDto();
        bean.setOrder("position");
        bean.setSort("desc");
        bean.setLogicGroupId("1");
        BatteryHealthDimensionsBean batteryHealthDimensionsBean = new BatteryHealthDimensionsBean();
        batteryHealthDimensionsBean.setId("77");
        bean.setBattHealthdims(Arrays.asList(batteryHealthDimensionsBean));
        bean.setPageNo(1);
        bean.setPageSize(1);
        bean.checkOrderSort();
        bean.setOrder("");
        bean.setSort("");
        bean.checkOrderSort();
        bean.getLogicGroupId();
        bean.getOrder();
        bean.getSort();
        bean.toString();
        bean.toString();
        Assert.assertEquals("1", bean.getLogicGroupId());
        boolean b = bean.checkBattHealthDimsIsEmpty();
        Assert.assertSame(b,true);
        Pair<Boolean, List<String>> booleanListPair = bean.checkBattHealthdimsInRange();
        Assert.assertEquals(8,booleanListPair.getRight().size());
    }
    @Test
    public void test2() throws Exception
    {
        try {
            PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
            SelectEvalDetailDto bean = new SelectEvalDetailDto();
            bean.setOrder("Normal");
            bean.setSort("desc");
            bean.setLogicGroupId("1");
            bean.setPageNo(1);
            bean.setPageSize(1);
            bean.checkOrderSort();
            bean.setOrder("asc");
            bean.checkOrderSort();
            bean.getLogicGroupId();
            bean.getOrder();
            bean.getSort();
            bean.getPageSize();
            bean.getPageNo();
            bean.toString();
            Assert.assertTrue(bean.checkOrderSort().getLeft());
        }
        catch (UedmException e)
        {
            Assert.assertEquals("order is Not in the range of optional values", e.getMessage());
        }
    }
    @Test
    public void test3() throws Exception
    {
        try {
            PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
            SelectEvalDetailDto bean = new SelectEvalDetailDto();
            bean.setSort("as");
            bean.checkOrderSort();
            Assert.assertFalse( bean.checkOrderSort().getLeft());
        }
        catch (UedmException e)
        {
            Assert.assertEquals("sort is Not in the range of optional values", e.getMessage());
        }
    }
    @Test
    public void test1() throws Exception
    {
        try {
            PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
            SelectEvalDetailDto bean = new SelectEvalDetailDto();
            bean.setOrder("name");
            bean.setSort("desc");
            bean.setLogicGroupId("1");
            bean.setPageNo(1);
            bean.setPageSize(1);
            bean.checkOrderSort();
            bean.getPageSize();
            bean.getOrder();
            bean.getSort();
            bean.getPageNo();
            bean.toString();
            Assert.assertTrue(bean.checkOrderSort().getLeft());
        }
        catch (UedmException e)
        {
            Assert.assertEquals("order is Not in the range of optional values", e.getMessage());
        }
    }

    @Test
    public void test4() throws Exception
    {
        try {
            PojoTestUtil.TestForPojo(SelectEvalDetailDto.class);
            SelectEvalDetailDto bean = new SelectEvalDetailDto();
            bean.setOrder("name");
            bean.setSort("desc");
            bean.setLogicGroupId("1");
            bean.setPageNo(1);
            bean.setPageSize(1);
            bean.setHealthStatusId(new ArrayList<>());
            bean.setProductionDateEnd("1");
            bean.setProductionDateStart("1");
            bean.setStartDateEnd("1");
            bean.setStatusChange(true);

            bean.getPageSize();
            bean.getOrder();
            bean.getSort();
            bean.getPageNo();
            bean.toString();
            bean.getHealthStatusId();
            bean.getProductionDateEnd();
            bean.getProductionDateStart();
            bean.getLogicGroupId();
            bean.getStatusChange();
            Assert.assertTrue( bean.checkOrderSort().getLeft());
        }
        catch (UedmException e)
        {
            Assert.assertEquals("order is Not in the range of optional values", e.getMessage());
        }
    }
}
