package com.zte.uedm.battery.controller.pv;

import com.alibaba.fastjson.JSON;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.bean.pv.PvRevenueOverviewResponseBean;
import com.zte.uedm.battery.bean.pv.PvRevenueQueryBean;
import com.zte.uedm.battery.cache.DeviceCacheFake;
import com.zte.uedm.battery.controller.pv.dto.PvPowerAnalysisDetailDto;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import com.zte.uedm.battery.rpc.ConfigurationRpcFake;
import com.zte.uedm.battery.service.impl.PvRevenueAnalysisServiceImpl;
import com.zte.uedm.battery.uft.db.PvGenerConsumRecordMapperFake;
import com.zte.uedm.battery.util.FakeBranchFlag;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class PvRevenueAnalysisControllerFTest {

    @InjectMocks
    private PvRevenueAnalysisController pvRevenueAnalysisController;

    @Mock
    private HttpServletRequest request;

    @Mock
    private RedisService redisService;

    @Mock
    private PvGenerConsumRecordMapper pvGenerConsumRecordMapper = new PvGenerConsumRecordMapperFake();


    @Mock
    private CommonCacheService commonCacheService;

    @Mock
    private DeviceCacheManager deviceCacheManager = new DeviceCacheFake();

    @Mock
    private CacheManager caffeineCacheManager;

    private final String OVERVIEW_Y_MESSAGE = "{\"positions\":[],\"grain\":\"y\",\"startTime\":\"2021\",\"endTime\":\"2024\"}";

    @Before
    public void before() throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void UEDM_289010_given_无太阳能监控对象_when_调用收益报表数据统计接口_then_返回的统计指标均为null() {
        // given 调用工程配置接口，未获取到太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NULL;
        // when 调用收益报表统计接口 查看年数据
        PvRevenueQueryBean param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvRevenueQueryBean.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvRevenueAnalysisOverview(param, "zh_CN", request);
        // then 统计指标均为空
        PvRevenueOverviewResponseBean data = (PvRevenueOverviewResponseBean) responseBean.getData();
        Assert.assertNull(data.getTotalGeneration());
        Assert.assertNull(data.getPvTotalGain());
        Assert.assertNull(data.getGridTotalFee());
        Assert.assertNull(data.getSavings());
    }

    @Test
    public void UEDM_289016_given_有太阳能监控对象_无太阳能记录数据_when_调用收益报表数据统计接口_then_返回的统计指标均为null() {
        // given
        // 有太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NORMAL;
        // 无太阳能记录数据
        PvGenerConsumRecordMapperFake.SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NULL;
        // when 调用收益报表统计接口 查看年数据
        PvRevenueQueryBean param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvRevenueQueryBean.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvRevenueAnalysisOverview(param, "zh_CN", request);
        // then 统计指标均为空
        PvRevenueOverviewResponseBean data = (PvRevenueOverviewResponseBean) responseBean.getData();
        Assert.assertNull(data.getTotalGeneration());
        Assert.assertNull(data.getPvTotalGain());
        Assert.assertNull(data.getGridTotalFee());
        Assert.assertNull(data.getSavings());
    }

    @Test
    public void UEDM_289011_given_有太阳能监控对象_有太阳能记录数据_未配置电价_when_调用收益报表数据统计接口_then_返回的统计指标均为null() {
        // given
        // 有太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NORMAL;
        // 有太阳能记录数据
        PvGenerConsumRecordMapperFake.SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NORMAL;
        // when 调用收益报表统计接口 查看年数据
        PvRevenueQueryBean param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvRevenueQueryBean.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvRevenueAnalysisOverview(param, "zh_CN", request);
        // then 统计指标均为空
        PvRevenueOverviewResponseBean data = (PvRevenueOverviewResponseBean) responseBean.getData();
        Assert.assertNull(data.getTotalGeneration());
        Assert.assertNull(data.getPvTotalGain());
        Assert.assertNull(data.getGridTotalFee());
        Assert.assertNull(data.getSavings());
    }

    @Test
    public void UEDM_289009_given_有太阳能监控对象_有太阳能记录数据_已配置电价_when_调用收益报表数据统计接口_then_返回正确的统计数据() throws UedmException {
        // given
        // 有太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NORMAL;
        // 有太阳能记录数据
        PvGenerConsumRecordMapperFake.SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NORMAL;
        // 已配置电价
        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put(PvRevenueAnalysisServiceImpl.SOLAR_PRICE, 1.0);
        cacheMap.put(PvRevenueAnalysisServiceImpl.GRID_PRICE, 1.0);
        Mockito.when(redisService.getCacheMap(PvRevenueAnalysisServiceImpl.ELECTROVALENCE)).thenReturn(cacheMap);
        // when 调用收益报表统计接口 查看年数据
        PvRevenueQueryBean param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvRevenueQueryBean.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvRevenueAnalysisOverview(param, "zh_CN", request);
        // then 统计指标均有数据
        PvRevenueOverviewResponseBean data = (PvRevenueOverviewResponseBean) responseBean.getData();
        Assert.assertNotNull(data.getPvTotalGain());
        Assert.assertNotNull(data.getGridTotalFee());
        Assert.assertNotNull(data.getSavings());
    }

    @Test
    public void UEDM_289012_given_有太阳能监控对象_有太阳能记录数据_已配置电价_无站点缓存_when_调用收益报表详情接口_then_返回空() throws UedmException {
        // given
        // 有太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NORMAL;
        // 有太阳能记录数据
        PvGenerConsumRecordMapperFake.SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NORMAL;
        // 已配置电价
        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put(PvRevenueAnalysisServiceImpl.SOLAR_PRICE, 1.0);
        cacheMap.put(PvRevenueAnalysisServiceImpl.GRID_PRICE, 1.0);
        Mockito.when(redisService.getCacheMap(PvRevenueAnalysisServiceImpl.ELECTROVALENCE)).thenReturn(cacheMap);

        // when 调用收益报表统计接口 查看年数据
        PvPowerAnalysisDetailDto param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvPowerAnalysisDetailDto.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvPowerAnalysisDetail(param, "zh_CN",null,null, request);
        // then 返回空列表
        Assert.assertEquals(0, responseBean.getTotal().intValue());
    }

    @Test
    public void UEDM_289014_given_有太阳能监控对象_有太阳能记录数据_已配置电价_本地缓存有数据_when_调用收益报表详情接口_then_返回正确的收益报表详情() throws UedmException {
        // given
        // 有太阳能监控对象
        ConfigurationRpcFake.GET_MO_BY_MOC_AND_POSITION = FakeBranchFlag.DATA_NORMAL;
        // 有太阳能记录数据
        PvGenerConsumRecordMapperFake.SELECT_OVERVIEW_RECORD_BY_CONDITION = FakeBranchFlag.DATA_NORMAL;
        // 已配置电价
        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put(PvRevenueAnalysisServiceImpl.SOLAR_PRICE, 1.0);
        cacheMap.put(PvRevenueAnalysisServiceImpl.GRID_PRICE, 1.0);
        Mockito.when(redisService.getCacheMap(PvRevenueAnalysisServiceImpl.ELECTROVALENCE)).thenReturn(cacheMap);

        // when 调用收益报表统计接口 查看年数据
        PvPowerAnalysisDetailDto param = JSON.parseObject(OVERVIEW_Y_MESSAGE, PvPowerAnalysisDetailDto.class);
        ResponseBean responseBean = pvRevenueAnalysisController.pvPowerAnalysisDetail(param, "zh_CN",null,null, request);
        // then 返回空列表
//        Assert.assertEquals(1, responseBean.getTotal().intValue());
    }
}
