package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.a_domain.aggregate.resource.model.entity.ResourceBaseEntity;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.function.sm.api.user.UserService;
import com.zte.uedm.function.sm.api.user.vo.UserDetailVo;
import com.zte.uedm.function.sm.exception.AuthorityException;
import com.zte.uedm.function.sm.optional.AuthorizationStatusOptional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class ConfigurationDataDomainImplTest
{
    @InjectMocks
    private ConfigurationDataDomainImpl configurationDataDomain;
    @Mock
    private ConfigurationManagerRpcImpl cfgRpc;

    @Mock
    private UserService userService;


    @Before
    public void setUp() throws UedmException
    {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void getAuthPositionsByUserTest() throws UedmException
    {
        when(cfgRpc.getAuthPositionsByUser(Mockito.any())).thenReturn(Arrays.asList("1","2"));
        List<String> list= configurationDataDomain.getAuthPositionsByUser(new ServiceBaseInfoBean("111","222","zh"));
        Assert.assertEquals(2, list.size());
        list= configurationDataDomain.getAuthPositionsByUser(new ServiceBaseInfoBean("","222","zh"));
        Assert.assertEquals(0, list.size());
    }
    /* Started by AICoder, pid:mbe62oa09de443e14ef409d840208e6fe9b1976e */

    @Test
    public void given_valid_roleNames_and_resourceBase_when_getAuthorizationStatus_then_return_true() throws AuthorityException {
        // given
        List<String> roleNames = Arrays.asList("role1", "role2");
        ResourceBaseEntity resourceBase = new ResourceBaseEntity();
        resourceBase.setAuthorizedRoles(Arrays.asList("role1", "role2"));
        resourceBase.setAuthorizedAllRoles(Arrays.asList("role1", "role2"));
        AuthorizationStatusOptional authorizationStatus = AuthorizationStatusOptional.PARTIAL_PERMISSION;
        when(userService.getAuthorizationStatus(roleNames, resourceBase.getAuthorizedRoles(), resourceBase.getAuthorizedAllRoles())).thenReturn(authorizationStatus);

        // when
        boolean result = configurationDataDomain.getAuthorizationStatus(roleNames, resourceBase);

        // then
        Assert.assertFalse(result);
    }

    @Test
    public void given_valid_userName_when_getUserDetail_then_return_roleNames() {
        // given
        String userName = "testUser";
        UserDetailVo userDetail = new UserDetailVo();
        userDetail.setRoleNames(Arrays.asList("role1", "role2"));
        when(userService.getUserDetail(anyString())).thenReturn(userDetail);

        // when
        List<String> result = configurationDataDomain.getUserDetail(userName);

        // then
        Assert.assertEquals(Arrays.asList("role1", "role2"), result);
    }

    @Test
    public void given_invalid_userName_when_getUserDetail_then_return_emptyList() {
        // given
        String userName = "testUser";
        when(userService.getUserDetail(anyString())).thenReturn(new UserDetailVo());

        // when
        List<String> result = configurationDataDomain.getUserDetail(userName);

        // then
        Assert.assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:mbe62oa09de443e14ef409d840208e6fe9b1976e */

    @Test
    public void given_invalid_roleNames_and_resourceBase_when_getAuthorizationStatus_then_return_false() throws AuthorityException {
        // given
        List<String> roleNames = Arrays.asList("role1", "role2");
        ResourceBaseEntity resourceBase = new ResourceBaseEntity();
        resourceBase.setAuthorizedRoles(Arrays.asList("role1", "role2"));
        resourceBase.setAuthorizedAllRoles(Arrays.asList("role1", "role2"));
        when(userService.getAuthorizationStatus(anyList(),anyList(),anyList())).thenThrow(new AuthorityException(1,""));

        // when
        boolean result = configurationDataDomain.getAuthorizationStatus(roleNames, resourceBase);

        // then

    }
    /* Started by AICoder, pid:y79e3ce535pde7b141380a0100d70e159b11ac45 */
    @Test
    public void isReservedRole() {
        // 模拟 userService 的行为，当调用 isReservedRole 方法时返回 true
        when(userService.isReservedRole(anyList())).thenReturn(true);

        // 调用 configurationDataDomain 的 isReservedRole 方法并传入一个空列表
        boolean reservedRole = configurationDataDomain.isReservedRole(new ArrayList<>());

        // 验证方法的返回值是否为 true
        Assert.assertTrue(reservedRole);
    }
    /* Ended by AICoder, pid:y79e3ce535pde7b141380a0100d70e159b11ac45 */

}
