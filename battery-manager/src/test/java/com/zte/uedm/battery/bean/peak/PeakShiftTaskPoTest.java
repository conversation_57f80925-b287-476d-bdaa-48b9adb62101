package com.zte.uedm.battery.bean.peak;

import org.junit.Assert;
import org.junit.Test;

public class PeakShiftTaskPoTest {

    @Test
    public void test()
    {
        PeakShiftTaskPo bean = new PeakShiftTaskPo();

        bean.setId("taskId");
        bean.setName("name");
        bean.setFileId("fileId");
        bean.setCreator("admin");
        bean.setDescription("de");
        bean.setGmtCreate("2020-02-01 00:00:00");
        bean.setUpdater("admin");
        bean.setGmtModified("2020-02-01 00:00:00");

        bean.getId();
        bean.getName();
        bean.getFileId();
        bean.getCreator();
        bean.getDescription();
        bean.getGmtCreate();
        bean.getUpdater();
        bean.getGmtModified();

        bean.toString();

        bean.equals(bean);
        bean.equals(new PeakShiftTaskPo());


        PeakShiftTaskBeanDto peakShiftTaskBeanDto = new PeakShiftTaskBeanDto();
        PeakShiftTaskPo peakShiftTaskPo = new PeakShiftTaskPo(peakShiftTaskBeanDto);

        Assert.assertSame("taskId", bean.getId());
    }
}
