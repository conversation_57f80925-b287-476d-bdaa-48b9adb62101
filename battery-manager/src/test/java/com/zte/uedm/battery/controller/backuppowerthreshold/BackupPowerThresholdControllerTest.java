package com.zte.uedm.battery.controller.backuppowerthreshold;


import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.CategoryDetailPojo;
import com.zte.uedm.battery.bean.MocIdsVO;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryConfigAddDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryConfigUpdateDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.CategoryDetailDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.SpecialSelectByConditionDto;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.CategoryConfigAddVo;
import com.zte.uedm.battery.controller.backuppowerthreshold.vo.SpecialSelectVo;
import com.zte.uedm.battery.enums.BackupPowerScenarioEnum;
import com.zte.uedm.battery.service.BackupPowerThresholdDetailService;
import com.zte.uedm.battery.service.BackupPowerThresholdService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.battery.controller.backuppowerthreshold.dto.SpecialSetDto;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeParameterUtil;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BackupPowerThresholdControllerTest
{
    @InjectMocks
    private BackupPowerThresholdController backupPowerThresholdController;
    @Mock
    private BackupPowerThresholdService backupPowerThresholdService;
    @Mock
    private JsonService jsonService;
    @Mock
    private BackupPowerThresholdDetailService backupPowerThresholdDetailService;

    private HttpServletRequest request;
    @Mock
    private MessageSenderService msgSenderService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        request = mock(HttpServletRequest.class);
    }

    @Test
    public void selectByConditionParamIsNull() throws Exception
    {
        try {
            backupPowerThresholdController.selectByCondition(null, null,null);
        } catch (Exception e) {
            Assert.assertEquals("123", e.getMessage());
        }
    }

    @Test
    public void selectByConditionSuccessfully() throws Exception
    {
        ResponseBean responseBean =null;
        CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
        categoryDetailDto.setLogicGroupId("7");
        List<CategoryDetailPojo> beans=new ArrayList<>();
        CategoryDetailPojo categoryDetailPojo = new CategoryDetailPojo();
        categoryDetailPojo.setId("1");
        beans.add(categoryDetailPojo);
        PageInfo<CategoryDetailPojo> categoryDetailPojoPageInfo = new PageInfo<>(beans);
        try {
            Mockito.doReturn(categoryDetailPojoPageInfo).when(backupPowerThresholdService).selectByCondition(Mockito.any(),Mockito.any());
            responseBean=backupPowerThresholdController.selectByCondition(categoryDetailDto,"zh-CN",request );
        } catch (UedmException e) {
            e=new UedmException(-100,"12");
        }
        Assert.assertEquals("1",String.valueOf(responseBean.getTotal()));
    }

    @Test
    public void selectByConditionFail() throws Exception
    {
        ResponseBean responseBean =null;
        CategoryDetailDto categoryDetailDto = new CategoryDetailDto();
        categoryDetailDto.setLogicGroupId("7");
        List<CategoryDetailPojo> beans=new ArrayList<>();
        CategoryDetailPojo categoryDetailPojo = new CategoryDetailPojo();
        categoryDetailPojo.setId("1");
        beans.add(categoryDetailPojo);
        PageInfo<CategoryDetailPojo> categoryDetailPojoPageInfo = new PageInfo<>(beans);
        try {
            Mockito.doThrow(new UedmException(-1,"77")).when(backupPowerThresholdService).selectByCondition(Mockito.any(),Mockito.any());
            responseBean=backupPowerThresholdController.selectByCondition(categoryDetailDto,"zh-CN",request );
        } catch (Exception e) {
            Assert.assertEquals("77", e.getMessage());
        }
    }

    @Test
    public void addCategoryConfigTest() throws UedmException {
        CategoryConfigAddDto configAddDto = new CategoryConfigAddDto();
        configAddDto.setLogicGroupId("id");
        configAddDto.setThreshold(1);

        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        CategoryConfigAddVo categoryConfigAddVo = new CategoryConfigAddVo();
        categoryConfigAddVo.setAddTotalSite(0);
        categoryConfigAddVo.setAddTotalRealGroup(0);
        Mockito.doReturn(categoryConfigAddVo).when(backupPowerThresholdService).battAddCategoryConfig(Mockito.any(CategoryConfigAddDto.class), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = backupPowerThresholdController.addCategoryConfig(configAddDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void addCategoryConfigTest2()
    {
        CategoryConfigAddDto configAddDto = new CategoryConfigAddDto();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = backupPowerThresholdController.addCategoryConfig(configAddDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT.intValue());
    }

    @Test
    public void addCategoryConfig_ExcTest3() throws UedmException
    {
        CategoryConfigAddDto configAddDto = new CategoryConfigAddDto();
        configAddDto.setLogicGroupId("id");
        configAddDto.setThreshold(1);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.doThrow(new UedmException(-304, "")).when(backupPowerThresholdService).battAddCategoryConfig(Mockito.any(CategoryConfigAddDto.class), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = backupPowerThresholdController.addCategoryConfig(configAddDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), -304);
    }

    @Test
    public void updateCategoryConfigTest()
    {
        CategoryConfigUpdateDto updateDto = new CategoryConfigUpdateDto();
        updateDto.setId("id");
        updateDto.setThreshold(2);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = backupPowerThresholdController.updateCategoryConfig(updateDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void updateCategoryConfigTest1()
    {
        CategoryConfigUpdateDto updateDto = new CategoryConfigUpdateDto();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = backupPowerThresholdController.updateCategoryConfig(updateDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT.intValue());
    }

    @Test
    public void updateCategoryConfig_ExcTest2() throws UedmException
    {
        CategoryConfigUpdateDto updateDto = new CategoryConfigUpdateDto();
        updateDto.setId("id");
        updateDto.setThreshold(2);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.doThrow(new UedmException(-304, "")).when(backupPowerThresholdService).battUpdateCategoryConfig(Mockito.any(CategoryConfigUpdateDto.class), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = backupPowerThresholdController.updateCategoryConfig(updateDto, "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), -304);
    }

    @Test
    public void deleteCategoryConfigTest()
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = backupPowerThresholdController.deleteCategoryConfig("id", "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    @Test
    public void deleteCategoryConfigTest2()
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean responseBean = backupPowerThresholdController.deleteCategoryConfig("", "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT.intValue());
    }

    @Test
    public void deleteCategoryConfig_ExcTest3() throws UedmException
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.doThrow(new UedmException(-200, "")).when(backupPowerThresholdService).battDeleteCategoryConfig(Mockito.anyString(), Mockito.any(ServiceBaseInfoBean.class));
        ResponseBean responseBean = backupPowerThresholdController.deleteCategoryConfig("id", "zh-CN", request);
        Assert.assertEquals(responseBean.getCode().intValue(), -1);
    }
    @Test
    public void specialAdd1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setScenarioType(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerThresholdDetailService).insert(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialAdd(specialAddDto,request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",re.getMessage());
    }
    @Test
    public void specialAdd2() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setScenarioType(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doReturn(1).when(backupPowerThresholdDetailService).insert(Mockito.any(),Mockito.any());
            Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialAdd(specialAddDto,request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(re.getTotal()));
    }

    @Test
    public void specialAdd3() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        re = backupPowerThresholdController.specialAdd(new SpecialSetDto(),request,"zh-CN");
        Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        Assert.assertEquals("param is blank.",re.getMessage());
    }

    @Test
    public void specialAdd4() throws Exception
    {
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setScenarioType(3);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            re = backupPowerThresholdController.specialAdd(specialAddDto,request,"zh-CN");
        } catch (UedmException ex) {
            Assert.assertEquals("param scenarioType not valid!",ex.getErrorDesc());
        }
    }

    @Test
    public void specialUpdate1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerThresholdDetailService).update(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialUpdate(specialAddDto,request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",re.getMessage());
    }
    @Test
    public void specialUpdate2() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doReturn(1).when(backupPowerThresholdDetailService).update(Mockito.any(),Mockito.any());
            Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialUpdate(specialAddDto,request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(re.getTotal()));
    }

    @Test
    public void specialUpdate3() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        re = backupPowerThresholdController.specialUpdate(new SpecialSetDto(),request,"zh-CN");
        Assert.assertEquals("param is blank.",re.getMessage());
    }

    @Test
    public void specialDelete1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerThresholdDetailService).delete(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialDelete(Arrays.asList("1"),request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",re.getMessage());
    }
    @Test
    public void specialDelete2() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSetDto specialAddDto = new SpecialSetDto();
        specialAddDto.setThreshold(1);
        specialAddDto.setBackupSystemIds(Arrays.asList("1"));
        try {
            Mockito.doReturn(1).when(backupPowerThresholdDetailService).delete(Mockito.any(),Mockito.any());
            Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.specialDelete(Arrays.asList("1"),request,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(re.getTotal()));
    }

    @Test
    public void specialDelete3() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        re = backupPowerThresholdController.specialDelete(new ArrayList<>(),request,"zh-CN");
        Assert.assertEquals("param is blank.",re.getError());
    }


    @Test
    public void selectSpecialByCondition1() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setLogicGroupId("1");
        specialSelectByConditionDto.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        specialSelectByConditionDto.setOrder("name");
        specialSelectByConditionDto.setSort("desc");
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerThresholdDetailService).selectSpecialByCondition(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",re.getMessage());
    }
    @Test
    public void selectSpecialByCondition2() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setLogicGroupId("1");
        specialSelectByConditionDto.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        specialSelectByConditionDto.setOrder("name");
        specialSelectByConditionDto.setSort("desc");
        List<SpecialSelectVo> specialSelectVos = new ArrayList<>();
        SpecialSelectVo specialSelectVo = new SpecialSelectVo();
        specialSelectVo.setThreshold(1);
        specialSelectVos.add(specialSelectVo);
        PageInfo<SpecialSelectVo> specialSelectVoPageInfo = new PageInfo<>(specialSelectVos);
        try {
            Mockito.doReturn(specialSelectVoPageInfo).when(backupPowerThresholdDetailService).selectSpecialByCondition(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(re.getTotal()));
    }
    @Test
    public void selectSpecialByCondition4() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setLogicGroupId("1");
        specialSelectByConditionDto.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        specialSelectByConditionDto.setOrder("nam1e");
        specialSelectByConditionDto.setSort("desc");
        List<SpecialSelectVo> specialSelectVos = new ArrayList<>();
        SpecialSelectVo specialSelectVo = new SpecialSelectVo();
        specialSelectVo.setThreshold(1);
        specialSelectVos.add(specialSelectVo);
        PageInfo<SpecialSelectVo> specialSelectVoPageInfo = new PageInfo<>(specialSelectVos);
        try {
            Mockito.doReturn(specialSelectVoPageInfo).when(backupPowerThresholdDetailService).selectSpecialByCondition(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("order or sort is Not in the range of optional values",re.getMessage());
    }
    @Test
    public void selectSpecialByCondition5() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setLogicGroupId("1");
        specialSelectByConditionDto.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        specialSelectByConditionDto.setOrder("name");
        specialSelectByConditionDto.setSort("desc1");
        List<SpecialSelectVo> specialSelectVos = new ArrayList<>();
        SpecialSelectVo specialSelectVo = new SpecialSelectVo();
        specialSelectVo.setThreshold(1);
        specialSelectVos.add(specialSelectVo);
        PageInfo<SpecialSelectVo> specialSelectVoPageInfo = new PageInfo<>(specialSelectVos);
        try {
            Mockito.doReturn(specialSelectVoPageInfo).when(backupPowerThresholdDetailService).selectSpecialByCondition(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("order or sort is Not in the range of optional values",re.getMessage());
    }

    @Test
    public void selectSpecialByCondition6() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setLogicGroupId("1");
        specialSelectByConditionDto.setScenarioType(BackupPowerScenarioEnum.SCENARIO_SWITCH_POWER.getCode());
        specialSelectByConditionDto.setOrder("name");
        specialSelectByConditionDto.setSort("desc");
        specialSelectByConditionDto.setUpdateTimeBegin("2022-12-12 12:12:12");
        specialSelectByConditionDto.setUpdateTimeEnd("2022-12-11 12:12:12");
        List<SpecialSelectVo> specialSelectVos = new ArrayList<>();
        SpecialSelectVo specialSelectVo = new SpecialSelectVo();
        specialSelectVo.setThreshold(1);
        specialSelectVos.add(specialSelectVo);
        PageInfo<SpecialSelectVo> specialSelectVoPageInfo = new PageInfo<>(specialSelectVos);
        try {
            Mockito.doReturn(specialSelectVoPageInfo).when(backupPowerThresholdDetailService).selectSpecialByCondition(Mockito.any(),Mockito.any());
            re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("The end time cannot be greater than the start time",re.getMessage());
    }

    @Test
    public void selectSpecialByCondition3() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        SpecialSelectByConditionDto specialSelectByConditionDto=new SpecialSelectByConditionDto();
        specialSelectByConditionDto.setPowerSupplyScene(Arrays.asList("1"));
        specialSelectByConditionDto.setSiteLevels(Arrays.asList("1"));
        specialSelectByConditionDto.setOrder("name");
        specialSelectByConditionDto.setSort("desc");
        Mockito.doReturn("js").when(jsonService).objectToJson(Mockito.any());
        Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        re = backupPowerThresholdController.selectSpecialByCondition(specialSelectByConditionDto,request,1,10,"zh-CN");
        Assert.assertEquals("js",re.getError());
    }

    @Test
    public void selectSpecialByPowerSupply()
    {
        backupPowerThresholdController.selectSpecialByPowerSupply("zh-CN");
    }


    @Test
    public void testSelectTabsCountByCondition()
    {
        // 正常
        try {
            backupPowerThresholdController.selectTabsCountByCondition("r32.uedm.group-global",request,"zh-CN");
        } catch (UedmException ignored) {
        }
        // 空参数
        try {
            backupPowerThresholdController.selectTabsCountByCondition("",request,"zh-CN");
        } catch (UedmException ignored) {
        }
        // 异常
        try {
            Mockito.doThrow(UedmErrorCodeParameterUtil.parameterIsMustInput("no param!")).when(backupPowerThresholdDetailService).selectTabsCountByCondition(Mockito.any(),Mockito.any());
            backupPowerThresholdController.selectTabsCountByCondition("r32.uedm.group-global",request,"zh-CN");
        } catch (UedmException | com.zte.uedm.basis.exception.UedmException e) {
            Assert.assertEquals("Parameter is must input", e.getMessage());
        }
    }

    @Test
    public void testQueryScenarioBasedMonitorObjects()
    {
        // 参数为空
        backupPowerThresholdController.queryScenarioBasedMonitorObjects(null ,request,"zh-CN");

        // 准备参数
        MocIdsVO mocIdsVO = new MocIdsVO(new ArrayList<>(), MocType.BATT);
        mocIdsVO.setScenarioType("1");
        mocIdsVO.setPageNo(1);
        mocIdsVO.setPageSize(10);

        // 用户名为空
        backupPowerThresholdController.queryScenarioBasedMonitorObjects(mocIdsVO ,request,"zh-CN");

        // 正常情况
        when(Tools.getUserName(request)).thenReturn("admin");
        when(Tools.getRemoteHost(request)).thenReturn("*******");
        PageInfo<MonitorObjectDsBean> pageInfo = new PageInfo<>();
        pageInfo.setTotal(10L);
        pageInfo.setList(new ArrayList<>());
        try {
            Mockito.doReturn(pageInfo)
                    .when(backupPowerThresholdDetailService).queryScenarioBasedMonitorObjects(Mockito.any(),Mockito.any());
        } catch (UedmException ignored) {

        } catch (com.zte.uedm.basis.exception.UedmException e) {
            throw new RuntimeException(e);
        }
        backupPowerThresholdController.queryScenarioBasedMonitorObjects(mocIdsVO ,request,"zh-CN");

        // 异常情况
        try {
            Mockito.doThrow(UedmErrorCodeParameterUtil.parameterIsMustInput(""))
                    .when(backupPowerThresholdDetailService).queryScenarioBasedMonitorObjects(Mockito.any(),Mockito.any());
            backupPowerThresholdController.queryScenarioBasedMonitorObjects(mocIdsVO ,request,"zh-CN");
        } catch (UedmException | com.zte.uedm.basis.exception.UedmException e) {
            Assert.assertEquals("Parameter is must input", e.getMessage());
        }
        Assert.assertNotNull(mocIdsVO);
    }
}
