package com.zte.uedm.battery.opti.infrastructure.repository.po;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class BattRiskThermalRunawayEvalPoTest {

    @Test
    public void BattRiskThermalRunawayEvalPo_Test() throws Exception
    {

        BattRiskThermalRunawayEvalPo po = new BattRiskThermalRunawayEvalPo();
        PojoTestUtil.TestForPojo(po.getClass());
        Assert.assertEquals(po.toString(), new BattRiskThermalRunawayEvalPo().toString());

    }
}
