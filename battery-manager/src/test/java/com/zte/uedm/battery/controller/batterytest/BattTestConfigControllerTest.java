/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattTestConfigControllerTest
 * 文件作者：00248587
 * 开发时间：2023/3/14
 */
package com.zte.uedm.battery.controller.batterytest;

import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.controller.batterytest.dto.BattImpactTrendDimsUpdateDto;
import com.zte.uedm.battery.controller.batterytest.dto.BattTestProportionUpdateDto;
import com.zte.uedm.battery.controller.batterytest.vo.BattImpactTrendVo;
import com.zte.uedm.battery.controller.battlife.dto.BattLifeDimsUpdateDto;
import com.zte.uedm.battery.controller.battlife.vo.BattLifeConfigVo;
import com.zte.uedm.battery.service.batttest.BattTestConfigService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doThrow;

public class BattTestConfigControllerTest 
{
    @InjectMocks
    private BattTestConfigController battTestConfigController;
    @Mock
    private BattTestConfigService battTestConfigService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectImpactTrendConfig_param_null() throws UedmException
    {

        doReturn(Arrays.asList(new BattImpactTrendVo())).when(battTestConfigService).selectImpactTrendConfig(any(), any());
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = battTestConfigController.selectImpactTrendConfig(1,2,
                request, "zh");
        Assert.assertEquals(-301, responseBean.getCode().intValue());
    }

    @Test
    public void selectImpactTrendConfig_normal() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BattLifeConfigVo> pageList = new PageInfo<>();
        pageList.setTotal(100);

        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattImpactTrendVo> beanList = new ArrayList<>();
        BattImpactTrendVo battImpactTrendVo = new BattImpactTrendVo();
        battImpactTrendVo.setUserName("apiuser");
        beanList.add(battImpactTrendVo);
        doReturn(beanList).when(battTestConfigService).selectImpactTrendConfig(any(), any());
        ResponseBean responseBean = battTestConfigController.selectImpactTrendConfig(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectImpactTrendConfig_exec() throws UedmException
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(battTestConfigService).selectImpactTrendConfig(any(), any());

            HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
            ResponseBean responseBean = battTestConfigController.selectImpactTrendConfig(1,2,
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-1,e.getErrorId().intValue());
        }
    }


    @Test
    public void updateImpactTrendConfig_username_null() throws UedmException
    {
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = battTestConfigController.updateImpactTrendConfig(new ArrayList<>(),
                request, "zh");
        Assert.assertEquals("param is blank.", responseBean.getMessage());
    }

    @Test
    public void updateImpactTrendConfig_param_null() throws UedmException
    {
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("username");
        ResponseBean responseBean = battTestConfigController.updateImpactTrendConfig(new ArrayList<>(),
                request, "zh");
        Assert.assertEquals(-301, responseBean.getCode().intValue());
    }

    @Test
    public void updateImpactTrendConfig_normal() throws UedmException
    {
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("username");
        BattImpactTrendDimsUpdateDto dto = new BattImpactTrendDimsUpdateDto();
        dto.setId("1");
        doReturn(1).when(battTestConfigService).updateImpactTrendConfig(any(), any());
        ResponseBean responseBean = battTestConfigController.updateImpactTrendConfig(Arrays.asList(dto),
                request, "zh");
        Assert.assertEquals(0, responseBean.getCode().intValue());
    }

    @Test
    public void updateImpactTrendConfig_exec() throws UedmException
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(battTestConfigService).updateImpactTrendConfig(any(), any());
            BattImpactTrendDimsUpdateDto dto = new BattImpactTrendDimsUpdateDto();
            dto.setId("1");
            HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
            ResponseBean responseBean = battTestConfigController.updateImpactTrendConfig(Arrays.asList(dto),
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals(-1,e.getErrorId().intValue());
        }
    }

    @Test
    public void selectBattTestProportion_param_null() throws UedmException
    {

        doReturn(Arrays.asList(new BattLifeConfigVo())).when(battTestConfigService).selectBattTestProportion(any(), any());
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = battTestConfigController.selectBattTestProportion(1,2,
                request, "zh");
        Assert.assertEquals("param is blank.", responseBean.getMessage());
    }

    @Test
    public void selectBattTestProportion_normal() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BattLifeConfigVo> pageList = new PageInfo<>();
        pageList.setTotal(100);

        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattLifeConfigVo> beanList = new ArrayList<>();
        BattLifeConfigVo battLifeConfigVo = new BattLifeConfigVo();
        battLifeConfigVo.setUserName("apiuser");
        beanList.add(battLifeConfigVo);
        doReturn(beanList).when(battTestConfigService).selectBattTestProportion(any(), any());
        ResponseBean responseBean = battTestConfigController.selectBattTestProportion(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectBattTestProportion_exec() throws UedmException
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(battTestConfigService).selectBattTestProportion(any(), any());

            HttpServletRequest request = mock(HttpServletRequest.class);
            ResponseBean responseBean = battTestConfigController.selectBattTestProportion(1,2,
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void updateBattTestProportion_username_null() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();
        BattTestProportionUpdateDto battTestProportionUpdateDto = new BattTestProportionUpdateDto();
        updateDtoList.add(battTestProportionUpdateDto);
        when(Tools.getUserName(request)).thenReturn("");

        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(-301,(int) responseBean.getCode());
    }
    @Test
    public void updateBattTestProportion_dto_empty() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();

        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(-301,(int) responseBean.getCode());
    }
    @Test
    public void updateBattTestProportion_dimId_not_unique() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();
        BattTestProportionUpdateDto battTestProportionUpdateDto = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto.setDimId("manual");
        battTestProportionUpdateDto.setSequence(1);
        updateDtoList.add(battTestProportionUpdateDto);

        BattTestProportionUpdateDto battTestProportionUpdateDto1 = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto1.setDimId("auto");
        battTestProportionUpdateDto1.setSequence(1);
        updateDtoList.add(battTestProportionUpdateDto1);


        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(-302,(int) responseBean.getCode());
    }
    @Test
    public void updateBattTestProportion_dimId_not_inRange() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();
        BattTestProportionUpdateDto battTestProportionUpdateDto = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto.setDimId("manual");
        battTestProportionUpdateDto.setSequence(1);
        updateDtoList.add(battTestProportionUpdateDto);

        BattTestProportionUpdateDto battTestProportionUpdateDto1 = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto1.setDimId("auto1");
        battTestProportionUpdateDto1.setSequence(2);
        updateDtoList.add(battTestProportionUpdateDto1);


        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(-302,(int) responseBean.getCode());
    }
    @Test
    public void updateBattTestProportion_normal() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();
        BattTestProportionUpdateDto battTestProportionUpdateDto = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto.setDimId("manual");
        battTestProportionUpdateDto.setSequence(1);
        updateDtoList.add(battTestProportionUpdateDto);

        BattTestProportionUpdateDto battTestProportionUpdateDto1 = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto1.setDimId("auto");
        battTestProportionUpdateDto1.setSequence(2);
        updateDtoList.add(battTestProportionUpdateDto1);

        Mockito.doReturn(1).when(battTestConfigService).updateBattTestProportion(Mockito.any(),Mockito.any(),Mockito.any());

        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }
    @Test
    public void updateBattTestProportion_ex() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattTestProportionUpdateDto> updateDtoList = new ArrayList<>();
        BattTestProportionUpdateDto battTestProportionUpdateDto = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto.setDimId("manual");
        battTestProportionUpdateDto.setSequence(1);
        updateDtoList.add(battTestProportionUpdateDto);

        BattTestProportionUpdateDto battTestProportionUpdateDto1 = new BattTestProportionUpdateDto();
        battTestProportionUpdateDto1.setDimId("auto");
        battTestProportionUpdateDto1.setSequence(2);
        updateDtoList.add(battTestProportionUpdateDto1);

        Mockito.doThrow(new UedmException(-1,"77")).when(battTestConfigService).updateBattTestProportion(Mockito.any(),Mockito.any(),Mockito.any());

        ResponseBean responseBean = battTestConfigController.updateBattTestProportion(updateDtoList,request,"zh_CN");
        Assert.assertEquals(-1,(int) responseBean.getCode());
    }

}
