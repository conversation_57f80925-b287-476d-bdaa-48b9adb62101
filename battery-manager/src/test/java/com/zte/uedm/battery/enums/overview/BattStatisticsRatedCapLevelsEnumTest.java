package com.zte.uedm.battery.enums.overview;

import org.junit.Assert;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class BattStatisticsRatedCapLevelsEnumTest {
    @Test
    public void testGetAll()
    {
        List<String> aaa =BattStatisticsRatedCapLevelsEnum.getAllBattStatisticsRatedCapLevelsIds();
        Assert.assertEquals(4,aaa.size());
    }

    @Test
    public void testGet()
    {
        String aaa =BattStatisticsRatedCapLevelsEnum.getNameById("1");
        Assert.assertEquals("30A",aaa);
    }

}