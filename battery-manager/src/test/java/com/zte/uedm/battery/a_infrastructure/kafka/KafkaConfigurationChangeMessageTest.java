package com.zte.uedm.battery.a_infrastructure.kafka;

import com.zte.uedm.basis.util.base.json.JsonUtils;
import com.zte.uedm.battery.a_domain.cache.strategy.CacheManagerStrategy;
import com.zte.uedm.component.kafka.producer.bean.KafkaMessageBean;
import com.zte.uedm.component.kafka.producer.constants.KafkaActionOptional;
import com.zte.uedm.component.kafka.producer.constants.KafkaModelOptional;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Map;

import static org.mockito.Mockito.when;

public class KafkaConfigurationChangeMessageTest {

    /* Started by AICoder, pid:u1cd4r7c8b5e594145ce08d960bd237d3743ed3c */
    @InjectMocks
    private KafkaConfigurationChangeMessage kafkaConfigurationChangeMessage;

    @Mock
    private CacheManagerStrategy cacheManagerStrategy;

    @Mock
    private Map<String, CacheManagerStrategy> cacheManagerStrategyMap;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(cacheManagerStrategyMap.get(KafkaModelOptional.COLLECTOR_STR)).thenReturn(cacheManagerStrategy);
    }

    @Test
    public void given_valid_kafka_message_when_onMsg_then_deal_is_called() throws Exception {
        // Given
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setAction(KafkaActionOptional.KAFKA_ACTION_CREATE);
        kafkaMessageBean.setModel(KafkaModelOptional.KAFKA_MODEL_COLLECTOR);
        kafkaMessageBean.setData(Arrays.asList("id1", "id2"));
        String msg = JsonUtils.objectToJson(kafkaMessageBean);

        // When
        kafkaConfigurationChangeMessage.onMsg(msg);

        // Then
//        verify(cacheManagerStrategy, times(1)).dealKafka(KafkaActionOptional.ACTION_1, Arrays.asList("id1", "id2"));
    }

    @Test
    public void given_null_data_when_onMsg_then_no_deal_is_called() throws Exception {
        // Given
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setAction(KafkaActionOptional.KAFKA_ACTION_CREATE);
        kafkaMessageBean.setModel(KafkaModelOptional.KAFKA_MODEL_COLLECTOR);
        String msg = JsonUtils.objectToJson(kafkaMessageBean);

        // When
        kafkaConfigurationChangeMessage.onMsg(msg);

        // Then
//        verify(cacheManagerStrategy, never()).dealKafka(any(), any());
    }

    @Test
    public void given_unknown_model_when_onMsg_then_no_deal_is_called() throws Exception {
        // Given
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setAction(KafkaActionOptional.KAFKA_ACTION_CREATE);
        kafkaMessageBean.setModel(KafkaModelOptional.KAFKA_MODEL_ADAPTER);
        kafkaMessageBean.setData(Arrays.asList("id1", "id2"));
        String msg = JsonUtils.objectToJson(kafkaMessageBean);

        // When
        kafkaConfigurationChangeMessage.onMsg(msg);

        // Then
//        verify(cacheManagerStrategy, never()).dealKafka(any(), any());
    }
    /* Ended by AICoder, pid:u1cd4r7c8b5e594145ce08d960bd237d3743ed3c */

    @Test
    public void given_unknown_model_when_onMsg_then_no_deal_is_called2() throws Exception {
        // Given
        KafkaMessageBean kafkaMessageBean = new KafkaMessageBean();
        kafkaMessageBean.setAction(KafkaActionOptional.KAFKA_ACTION_CREATE);
        kafkaMessageBean.setData(Arrays.asList("id1", "id2"));
        String msg = JsonUtils.objectToJson(kafkaMessageBean);

        // When
        kafkaConfigurationChangeMessage.onMsg(msg);

        // Then
//        verify(cacheManagerStrategy, never()).dealKafka(any(), any());
    }
}