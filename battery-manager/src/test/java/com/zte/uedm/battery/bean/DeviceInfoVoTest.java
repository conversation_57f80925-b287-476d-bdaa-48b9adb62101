package com.zte.uedm.battery.bean;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class DeviceInfoVoTest {

	private DeviceInfoVo deviceInfoVoUnderTest;

	@Before
	public void setUp() throws Exception {
		deviceInfoVoUnderTest = new DeviceInfoVo();
	}

	@Test
	public void testDeviceInfoVo() {
		DeviceInfoVo deviceInfoVo = new DeviceInfoVo();
		deviceInfoVo.setDeviceId("deviceId");
		deviceInfoVo.setDeviceName("deviceName");
		deviceInfoVo.setSiteId("siteId");
		deviceInfoVo.setSiteName("siteName");
		deviceInfoVo.setPosition("position");
		deviceInfoVo.setStatus("status");
		deviceInfoVo.setExecTime("execTime");
		deviceInfoVo.setExistsFlag(true);
		deviceInfoVo.setFailReason("failReason");
		deviceInfoVo.setExpirationDate("expirationDate");
		deviceInfoVo.setEffectiveDate("effectiveDate");
		deviceInfoVo.setEffectiveStatus("effectiveStatus");
		assertThat(deviceInfoVo.getDeviceId()).isEqualTo("deviceId");
		assertThat(deviceInfoVo.getDeviceName()).isEqualTo("deviceName");
		assertThat(deviceInfoVo.getSiteId()).isEqualTo("siteId");
		assertThat(deviceInfoVo.getSiteName()).isEqualTo("siteName");
		assertThat(deviceInfoVo.getPosition()).isEqualTo("position");
		assertThat(deviceInfoVo.getStatus()).isEqualTo("status");
		assertThat(deviceInfoVo.getExecTime()).isEqualTo("execTime");
		assertThat(deviceInfoVo.getExistsFlag()).isEqualTo(true);
		assertThat(deviceInfoVo.getFailReason()).isEqualTo("failReason");
		assertThat(deviceInfoVo.getExpirationDate()).isEqualTo("expirationDate");
		assertThat(deviceInfoVo.getEffectiveDate()).isEqualTo("effectiveDate");
		assertThat(deviceInfoVo.getEffectiveStatus()).isEqualTo("effectiveStatus");

	}
}
