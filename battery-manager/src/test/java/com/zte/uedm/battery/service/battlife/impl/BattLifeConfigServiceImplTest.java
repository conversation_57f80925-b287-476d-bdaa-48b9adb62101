/**
 * 版权所有：中兴通讯股份有限公司
 * 文件名称：BattLifeConfigServiceImplTest
 * 文件作者：00248587
 * 开发时间：2023/3/2
 */
package com.zte.uedm.battery.service.battlife.impl;

import com.zte.uedm.battery.bean.BattLifeConfigPo;
import com.zte.uedm.battery.controller.battlife.dto.BattLifeDimsUpdateDto;
import com.zte.uedm.battery.controller.battlife.vo.BattLifeConfigVo;
import com.zte.uedm.battery.dao.BattLifeConfigDao;
import com.zte.uedm.battery.domain.BattAssetAttributeDomain;
import com.zte.uedm.battery.domain.BattAssetDomain;
import com.zte.uedm.battery.enums.battlife.BattLifeListDimEnums;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.*;

import static org.mockito.Mockito.*;

public class BattLifeConfigServiceImplTest
{
    @InjectMocks
    private BattLifeConfigServiceImpl battLifeConfigService;
    @Mock
    private BattLifeConfigDao battLifeConfigDao;
    @Mock
    private BattAssetAttributeDomain battAssetAttributeDomain;
    @Mock
    private BattAssetDomain battAssetDomain;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private MessageSenderService msgSenderService;

    @Before
    public void init() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
        doNothing().when(msgSenderService).sendMsgAsync(any(), any());
        doReturn("123").when(i18nUtils).getMapFieldByLanguageOption(any(),any());
    }

    @Test
    public void selectBattLifeListConfig_normal1() throws UedmException
    {
        doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
        doNothing().when(msgSenderService).sendMsgAsync(any(), any());
        doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());
        List<BattLifeConfigVo> list = battLifeConfigService.selectBattLifeListConfig("admin", "zh_CN");
        Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
    }

    @Test
    public void selectBattLifeListConfig_normal2() throws UedmException
    {
        BattLifeConfigPo po1 = new BattLifeConfigPo();
        po1.setId("name");
        po1.setName("Name");
        po1.setSequence(1);
        po1.setUserName("admin");
        BattLifeConfigPo po2 = new BattLifeConfigPo();
        po2.setId("battType");
        po2.setName("Type");
        po2.setSequence(2);
        po2.setUserName("admin");

        doReturn(Arrays.asList(po1, po2)).when(battLifeConfigDao).selectBattLifeConfig(any());
        Map<String, Boolean> attributeMap = new HashMap<>();
        attributeMap.put("82fafb6d-BatteryType", false);
        doReturn(attributeMap).when(battAssetAttributeDomain).selectAttributeEnable(anyList(), any());
        List<BattLifeConfigVo> list = battLifeConfigService.selectBattLifeListConfig("admin", "zh_CN");
        Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
    }

    @Test
    public void selectBattLifeListConfig_exp1() throws UedmException
    {
        try {
            doThrow(new UedmException(-1, "77")).when(battLifeConfigDao).selectBattLifeConfig(any());
            List<BattLifeConfigVo> list = battLifeConfigService.selectBattLifeListConfig("admin", "zh_CN");
            Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
        }
        catch (Exception e)
        {
            Assert.assertEquals("77",e.getMessage());
        }
    }

    @Test
    public void selectBattLifeListConfig_exp2() throws UedmException
    {
        try {
            doThrow(new RuntimeException()).when(battLifeConfigDao).selectBattLifeConfig(any());
            List<BattLifeConfigVo> list = battLifeConfigService.selectBattLifeListConfig("admin", "zh_CN");
            Assert.assertEquals(BattLifeListDimEnums.values().length, list.size());
        }
        catch (Exception e)
        {
            Assert.assertEquals("Other temporary errors",e.getMessage());
        }
    }

    @Test
    public void updateBattLifeConfig_check_param_null() throws UedmException
    {
        try
        {
            Integer num = battLifeConfigService.updateBattLifeConfig(new ArrayList<>(), "admin", "zh_CN");

        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-301, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBattLifeConfig_check_sequence_unique() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("name");
            dto1.setSequence(1);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("battType");
            dto2.setSequence(1);
            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-302, ue.getErrorId().intValue());
        }
    }


    @Test
    public void updateBattLifeConfig_check_id_given() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("id");
            dto1.setSequence(1);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("type");
            dto2.setSequence(2);

            //battLifeConfigVos
            doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());

            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-305, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBattLifeConfig_check_sequence_given() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("name");
            dto1.setSequence(9);
            dto1.setEnable(true);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("battType");
            dto2.setSequence(8);
            dto2.setEnable(false);

            //battLifeConfigVos
            doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());

            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-302, ue.getErrorId().intValue());
        }
    }


    @Test
    public void updateBattLifeConfig_check_enable() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("name");
            dto1.setSequence(6);
            dto1.setEnable(false);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("battType");
            dto2.setSequence(1);
            dto1.setEnable(false);

            //battLifeConfigVos
            doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());

            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-208, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBattLifeConfig_normal() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("name");
            dto1.setSequence(3);
            dto1.setEnable(true);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("leftLife");
            dto2.setSequence(1);
            dto1.setEnable(true);

            //battLifeConfigVos
            doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());

            doReturn(2).when(battLifeConfigDao).updateBattLifeConfigByBeans(any());

            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
            Assert.assertEquals(2, num.intValue());
        }
        catch (UedmException ue)
        {
            Assert.assertEquals(-604, ue.getErrorId().intValue());
        }
    }

    @Test
    public void updateBattLifeConfig_exp() throws UedmException
    {
        try
        {
            BattLifeDimsUpdateDto dto1 = new BattLifeDimsUpdateDto();
            dto1.setId("name");
            dto1.setSequence(3);
            dto1.setEnable(true);
            BattLifeDimsUpdateDto dto2 = new BattLifeDimsUpdateDto();
            dto2.setId("leftLife");
            dto2.setSequence(1);
            dto1.setEnable(true);

            //battLifeConfigVos
            doReturn(new ArrayList<>()).when(battLifeConfigDao).selectBattLifeConfig(any());
            doNothing().when(msgSenderService).sendMsgAsync(any(), any());
            doNothing().when(battLifeConfigDao).insertBattLifeConfigByBeans(any());

            doThrow(new RuntimeException()).when(battLifeConfigDao).updateBattLifeConfigByBeans(any());

            Integer num = battLifeConfigService.updateBattLifeConfig(Arrays.asList(dto1, dto2),
                    "admin", "zh_CN");
            Assert.assertEquals(2, num.intValue());
        }
        catch (Exception e)
        {
            Assert.assertEquals("Other temporary errors",e.getMessage());
        }
    }

    @Test
    public void searchBattLifeConfig_normal1() throws UedmException
    {
        doReturn(new ArrayList<>()).when(battLifeConfigDao).searchBattLifeConfig(any(), any());
        List<BattLifeConfigVo> list = battLifeConfigService.searchBattLifeConfig("name", "admin", "zh_CN");
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void searchBattLifeConfig_normal2() throws UedmException
    {
        BattLifeConfigPo po1 = new BattLifeConfigPo();
        po1.setId("name");
        po1.setName("Name");
        po1.setSequence(1);
        po1.setUserName("admin");
        BattLifeConfigPo po2 = new BattLifeConfigPo();
        po2.setId("battType");
        po2.setName("Type");
        po2.setSequence(2);
        po2.setUserName("admin");

        doReturn(Arrays.asList(po1, po2)).when(battLifeConfigDao).searchBattLifeConfig(any(), any());
        Map<String, Boolean> attributeMap = new HashMap<>();
        attributeMap.put("82fafb6d-BatteryType", false);
        doReturn(attributeMap).when(battAssetAttributeDomain).selectAttributeEnable(anyList(), any());
        List<BattLifeConfigVo> list = battLifeConfigService.searchBattLifeConfig("name", "admin", "zh_CN");
        Assert.assertEquals(2, list.size());
    }

    @Test
    public void searchBattLifeConfig_exp1() throws UedmException
    {
        try {
            doThrow(new UedmException(-1, "77")).when(battLifeConfigDao).searchBattLifeConfig(any(), any());
            List<BattLifeConfigVo> list = battLifeConfigService.searchBattLifeConfig("name", "admin", "zh_CN");
            Assert.assertEquals(7, list.size());
        }
        catch (Exception e)
        {
            Assert.assertEquals("77",e.getMessage());
        }
    }

    @Test
    public void searchBattLifeConfig_exp2() throws UedmException
    {
        try {
            doThrow(new RuntimeException()).when(battLifeConfigDao).searchBattLifeConfig(any(), any());
            List<BattLifeConfigVo> list = battLifeConfigService.searchBattLifeConfig("name", "admin", "zh_CN");
            Assert.assertEquals(7, list.size());
        }
        catch (Exception e)
        {
            Assert.assertEquals("Other temporary errors",e.getMessage());
        }
    }

}
