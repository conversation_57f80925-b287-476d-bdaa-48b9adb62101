package com.zte.uedm.battery.controller.battHealth.dto;

import com.zte.uedm.battery.controller.batthealth.dto.HealthThresholdDto;
import com.zte.uedm.battery.controller.batthealth.dto.ThresholdDto;
import com.zte.uedm.common.util.ValidationUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.Map;

public class DtoTest {
    @Test
    public  void test()
    {
        HealthThresholdDto healthThresholdDto = new HealthThresholdDto();
        healthThresholdDto.setId("healthy");
        ThresholdDto thresholdDto = new ThresholdDto();
        thresholdDto.setMax(10.0);
        thresholdDto.setMin(70.0);
        thresholdDto.setContainMax(true);
        thresholdDto.setContainMin(true);
        thresholdDto.setUnit("get");
        healthThresholdDto.setThreshold(thresholdDto);
        thresholdDto.checkParamIsNull();
        thresholdDto.checkIntervalIsValid();
        healthThresholdDto.toString();
        Assert.assertEquals("healthy",healthThresholdDto.getId());
    }
    @Test
    public  void test2()
    {
        HealthThresholdDto healthThresholdDto = new HealthThresholdDto();
        healthThresholdDto.setId("healthy");
        ThresholdDto thresholdDto = new ThresholdDto();
        thresholdDto.setMax(90.0);
        thresholdDto.setMin(70.0);
        thresholdDto.setContainMax(true);
        thresholdDto.setContainMin(true);
        thresholdDto.setUnit("get");
        thresholdDto.getUnit();
        healthThresholdDto.setThreshold(thresholdDto);
        thresholdDto.checkParamIsNull();
        thresholdDto.checkIntervalIsValid();
        Assert.assertEquals("healthy",healthThresholdDto.getId());
    }

    @Test
    public  void test4()
    {
        ThresholdDto thresholdDto = new ThresholdDto();
        thresholdDto.setMax(10.0);
        thresholdDto.setMin(70.0);
        thresholdDto.setContainMax(true);
        thresholdDto.setContainMin(true);
        thresholdDto.setUnit("get");
        thresholdDto.toString();
        Assert.assertEquals("get",thresholdDto.getUnit());
    }
    @Test
    public void checkParamIsNullTest() {
        ThresholdDto thresholdDto = new ThresholdDto();
        thresholdDto.setMin(70.0);
        thresholdDto.setContainMax(true);
        thresholdDto.setContainMin(true);
        thresholdDto.setUnit("get");
        thresholdDto.toString();
        thresholdDto.checkParamIsNull();
    }

    @Test
    public void checkIntervalIsValidTest() {
        ThresholdDto thresholdDto = new ThresholdDto();
        thresholdDto.setMin(70.0);
        thresholdDto.setMax(10.0);
        thresholdDto.setContainMax(true);
        thresholdDto.setContainMin(true);
        thresholdDto.setUnit("get");
        thresholdDto.toString();
        thresholdDto.checkIntervalIsValid();
    }
}
