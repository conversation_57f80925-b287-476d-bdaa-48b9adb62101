package com.zte.uedm.battery.a_infrastructure.safe.common.utils;

/* Started by AICoder, pid:gbe071ca27x4bf314f770bd100a86d7f5a219272 */
import static com.zte.uedm.battery.a_infrastructure.common.GlobalConstants.BAD_STATUS;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

import java.util.*;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.a_infrastructure.common.GlobalConstants;
import com.zte.uedm.common.bean.south.DeviceLinkCommStatusDTO;
import com.zte.uedm.component.redis.service.RedisService;
import org.junit.*;
import org.mockito.*;

public class BatteryTrackManagerTest {
    @InjectMocks private BatteryTrackManager batteryTrackManager;

    @Mock private RedisService redisService;

    @Mock private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetDeviceStatus_NAStatus() throws UedmException {
        String moId = "testMoId";
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId)).thenReturn(new HashSet<>());
        assertEquals("2.0", batteryTrackManager.getDeviceStatus(moId));
    }

    @Test
    public void testGetDeviceStatus_UnknownStatusInMap() throws Exception {
        String moId = "testMoId";
        Map<String, Object> map = new HashMap<>();
        map.put("collector1", new DeviceLinkCommStatusDTO());
        when(redisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO)).thenReturn(map);
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId)).thenReturn(Collections.singleton("collector1"));
        assertEquals("2.0", batteryTrackManager.getDeviceStatus(moId));
    }

    @Test
    public void testGetDeviceStatus_BadStatusInMap() throws Exception {
        String moId = "testMoId";
        Map<String, Object> map = new HashMap<>();
        DeviceLinkCommStatusDTO deviceCommDto = new DeviceLinkCommStatusDTO();
        deviceCommDto.setStatus(BAD_STATUS);
        map.put("collector1", deviceCommDto);
        when(redisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO)).thenReturn(map);
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId)).thenReturn(Collections.singleton("collector1"));
        assertEquals("2.0", batteryTrackManager.getDeviceStatus(moId));
    }

    @Test
    public void testGetDeviceStatus_NormalStatusInMap() throws Exception{
        String moId = "testMoId";
        Map<String, Object> map = new HashMap<>();
        DeviceLinkCommStatusDTO deviceCommDto = new DeviceLinkCommStatusDTO();
        deviceCommDto.setStatus(0);
        map.put("collector1", deviceCommDto);
        when(redisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO)).thenReturn(map);
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId)).thenReturn(Collections.singleton("collector1"));
        assertEquals("0.0", batteryTrackManager.getDeviceStatus(moId));
    }

    @Test
    public void testGetDeviceStatus_ErrorStatusInMap() throws Exception{
        String moId = "testMoId";
        Map<String, Object> map = new HashMap<>();
        DeviceLinkCommStatusDTO deviceCommDto = new DeviceLinkCommStatusDTO();
        deviceCommDto.setStatus(1);
        map.put("collector1", deviceCommDto);
        when(redisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO)).thenReturn(map);
        when(resourceCollectorRelationCacheManager.getCollectorIdByResourceId(moId)).thenReturn(Collections.singleton("collector1"));
        assertEquals("1.0", batteryTrackManager.getDeviceStatus(moId));
    }

    /* Started by AICoder, pid:v1899k1ab80f41f14944096af0b2b307f226ac75 */
    @Test
    public void testGetDeviceStatus_ExceptionInRedisService() throws Exception{
        String moId = "testMoId";
        when(redisService.getCacheMap(GlobalConstants.REDIS_KEY_CURR_DEVICE_COMM_INFO)).thenThrow(new RuntimeException());
        assertEquals("2.0", batteryTrackManager.getDeviceStatus(moId));
    }
    /* Ended by AICoder, pid:v1899k1ab80f41f14944096af0b2b307f226ac75 */
}


/* Ended by AICoder, pid:gbe071ca27x4bf314f770bd100a86d7f5a219272 */