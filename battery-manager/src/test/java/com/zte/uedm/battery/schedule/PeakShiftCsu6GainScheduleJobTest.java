package com.zte.uedm.battery.schedule;


import com.zte.uedm.battery.service.PeakShiftService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PeakShiftCsu6GainScheduleJobTest {

    @InjectMocks
    private PeakShiftCsu6GainScheduleJob peakShiftCsu6GainScheduleJob;

    @Mock
    private PeakShiftService peakShiftService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void execute() {
        peakShiftCsu6GainScheduleJob.execute();

        try {
            Mockito.doThrow(UedmException.class).when(peakShiftService).statisticsCSU6(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.eq(false));
            peakShiftCsu6GainScheduleJob.execute();
        } catch (Exception e) {

        }

    }
}