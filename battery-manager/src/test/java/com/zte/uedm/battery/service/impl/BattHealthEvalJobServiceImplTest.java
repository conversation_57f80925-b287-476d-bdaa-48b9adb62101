package com.zte.uedm.battery.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.uedm.battery.a_domain.aggregate.model.entity.StandardPointEntity;
import com.zte.uedm.battery.a_domain.utils.PmaServiceUtils;
import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.battery.bean.MocHistoryQueryToolBean;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.bean.pv.HistoryDataResponseConditionBean;
import com.zte.uedm.battery.controller.battAiConfig.vo.AiConfigVo;
import com.zte.uedm.battery.domain.*;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.enums.battlife.BatteryHealthEvalEnums;
import com.zte.uedm.battery.rpc.impl.AiForecastServiceRpcImpl;
import com.zte.uedm.battery.rpc.impl.AssetRpcImpl;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MonitorManagerRpcImpl;
import com.zte.uedm.battery.rpc.vo.BattOverviewAssetConditionVo;
import com.zte.uedm.battery.rpc.vo.SohMsgBean;
import com.zte.uedm.battery.service.BattHealthStatusEvaluateService;
import com.zte.uedm.battery.service.battAiConfig.BattAISwitchService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.BatteryHealthyBeanUtils;
import com.zte.uedm.battery.util.constant.BatteryConstant;
import com.zte.uedm.common.configuration.monitor.object.bean.MonitorObjectBaseBean;
import com.zte.uedm.common.configuration.point.bean.RecordIndexBean;
import com.zte.uedm.common.configuration.point.bean.StandardPointBean;
import com.zte.uedm.common.consts.standardpoint.BattStandardPointConstants;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

public class BattHealthEvalJobServiceImplTest
{
    @InjectMocks
    private BattHealthEvalJobServiceImpl battHealthEvalJobService;

    @Mock
    private BattConfigurationDomain battConfigurationDomain;

    @Mock
    private MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Mock
    private MoHistoryDataQueryDomain moHistoryDataQueryDomain;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private AssetRpcImpl assetRpcImpl;

    @Mock
    private BattTypeDomain battTypeDomain;

    @Mock
    private BatteryHisDataDomain batteryHisDataDomain;

    @Mock
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;

    @Mock
    private BattAlarmDomain battAlarmDomain;
    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private BatteryHealthyBeanUtils batteryHealthyBeanUtils;
    @Mock
    private AiForecastServiceRpcImpl aiForecastServiceRpc;
    @Mock
    private BattAISwitchService battAISwitchService;
    @Mock
    private PmaServiceUtils pmaServiceUtils;
    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
        Map<String, BattHealthStatusEvaluateService> hs = new HashMap<>();
        hs.put(BattTypeEnum.LFP.getNameEn(), new BattLfpHealthStatusEvaluateServiceImpl());
        battHealthEvalJobService.setHs(hs);
    }

    @Test
    public void battHealthEvalEntranceTest() throws UedmException
    {

        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setBattType(BattTypeEnum.LFP);
        battTypeBean.setId("11");
        when(battTypeDomain.getBatteryTypeByMoId()).thenReturn(Lists.newArrayList(battTypeBean));
        MonitorObjectBaseBean monitorObjectBaseBean = new MonitorObjectBaseBean();
        monitorObjectBaseBean.setId("11");
        monitorObjectBaseBean.setPath("11");
        monitorObjectBaseBean.setPathId("11");
        monitorObjectBaseBean.setMoc("11");
        monitorObjectBaseBean.setName("11");
        when(battConfigurationDomain.selectBattByLogicId(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList(monitorObjectBaseBean));
        MocHistoryQueryToolBean pvQueryToolBean = new MocHistoryQueryToolBean();
        pvQueryToolBean.setPvType(BatteryConstant.PV_TYPE_PV);
        pvQueryToolBean.setRecordDate("11");
        when(moHistoryDataQueryDomain.buildYesterdayQueryBean(Mockito.anyString())).thenReturn(pvQueryToolBean);
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> historyDataResponseConditionBeans = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setCurrentValue("10");
        historyDataResponseConditionBeans.add(historyDataResponseConditionBean);
        hisDataResponseMap.put("11", historyDataResponseConditionBeans);
        when(batteryHisDataDomain.getHistoryDataByConditions(Mockito.any())).thenReturn(hisDataResponseMap);
        Map<String, Boolean> battAlarmInfo = new HashMap<>();
        battAlarmInfo.put("11", true);
        when(battAlarmDomain.selectHealthAlarmByMoIds(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(battAlarmInfo);
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("11");
        battOverviewAssetConditionVo.setStartDate("11");
        battOverviewAssetConditionVo.setProductionDate("11");
        when(assetRpcImpl.getAssetInfoByCondition(Mockito.any())).thenReturn(Lists.newArrayList(battOverviewAssetConditionVo));
        List<BattHealthStatusEvalPo> battpreStatusEvalPos=new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("11");
        battHealthStatusEvalPo.setStatusId("unEvaluate");
        battHealthStatusEvalPo.setUnknownReason("11");
        battpreStatusEvalPos.add(battHealthStatusEvalPo);
        when(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(Mockito.any())).thenReturn(battpreStatusEvalPos);
        HistoryDataResponseConditionBean historyBeans = new HistoryDataResponseConditionBean();
        when(pmaServiceUtils.selectHistoryDataByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(historyBeans));
        AiConfigVo aiConfigVo = new AiConfigVo();
        aiConfigVo.setFlag(false);
        when(battAISwitchService.selectConfigById(Mockito.any(), Mockito.any())).thenReturn(aiConfigVo);
        battHealthEvalJobService.evalBatteryHealth("11");
        Assert.assertEquals("11",monitorObjectBaseBean.getName());
    }
    @Test
    public void battHealthEvalEntranceTest11() throws UedmException
    {

        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setBattType(BattTypeEnum.LFP);
        battTypeBean.setId("11");
        when(battTypeDomain.getBatteryTypeByMoId()).thenReturn(Lists.newArrayList(battTypeBean));
        MonitorObjectBaseBean monitorObjectBaseBean = new MonitorObjectBaseBean();
        monitorObjectBaseBean.setId("11");
        monitorObjectBaseBean.setPath("11");
        monitorObjectBaseBean.setPathId("11");
        monitorObjectBaseBean.setMoc("11");
        monitorObjectBaseBean.setName("11");
        when(battConfigurationDomain.selectBattByLogicId(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList(monitorObjectBaseBean));
        MocHistoryQueryToolBean pvQueryToolBean = new MocHistoryQueryToolBean();
        pvQueryToolBean.setPvType(BatteryConstant.PV_TYPE_PV);
        pvQueryToolBean.setRecordDate("11");
        when(moHistoryDataQueryDomain.buildYesterdayQueryBean(Mockito.anyString())).thenReturn(pvQueryToolBean);
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> historyDataResponseConditionBeans = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setCurrentValue("10");
        historyDataResponseConditionBeans.add(historyDataResponseConditionBean);
        hisDataResponseMap.put("11", historyDataResponseConditionBeans);
        when(batteryHisDataDomain.getHistoryDataByConditions(Mockito.any())).thenReturn(hisDataResponseMap);
        Map<String, Boolean> battAlarmInfo = new HashMap<>();
        battAlarmInfo.put("11", false);
        when(battAlarmDomain.selectHealthAlarmByMoIds(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(battAlarmInfo);
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("11");
        battOverviewAssetConditionVo.setStartDate("11");
        battOverviewAssetConditionVo.setProductionDate("11");
        when(assetRpcImpl.getAssetInfoByCondition(Mockito.any())).thenThrow(new UedmException(-1,"1"));
        List<BattHealthStatusEvalPo> battpreStatusEvalPos=new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("11");
        battHealthStatusEvalPo.setStatusId("unEvaluate");
        battHealthStatusEvalPo.setUnknownReason("11");
        battpreStatusEvalPos.add(battHealthStatusEvalPo);
        when(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(Mockito.any())).thenThrow(new UedmException(-1,"1"));
        HistoryDataResponseConditionBean historyBeans = new HistoryDataResponseConditionBean();
        when(pmaServiceUtils.selectHistoryDataByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(historyBeans));

        List<StandardPointEntity> entities = new ArrayList<>();
        StandardPointEntity entity = new StandardPointEntity();
        entity.setId("batt.health");
        HashMap<String, String> mapN = new HashMap<>();
        mapN.put("zh_CN","1123");
        mapN.put("en_US","12123");
        entity.setName(JSONObject.toJSONString(mapN));
        entities.add(entity);

        List<RecordIndexBean> beans = new ArrayList<>();
        RecordIndexBean bean = new RecordIndexBean();
        bean.setId("batt.batt_charge.final.soc");
        beans.add(bean);
        when(configurationManagerRpc.getStandardPointByIds(Mockito.any(), Mockito.any())).thenReturn(entities);
        when(configurationManagerRpc.getRecordIndexByIds(Mockito.any())).thenReturn(beans);
        when(i18nUtils.getMapFieldByLanguage(Mockito.any(),Mockito.any())).thenReturn("123");
        AiConfigVo aiConfigVo = new AiConfigVo();
        aiConfigVo.setFlag(true);
        aiConfigVo.setValue("ON");
        when(battAISwitchService.selectConfigById(Mockito.any(), Mockito.any())).thenReturn(aiConfigVo);
        List<SohMsgBean> sohMsgBeanList = new ArrayList<>();
        SohMsgBean sohMsgBean = new SohMsgBean();
        sohMsgBean.setBatt("batt");
        sohMsgBeanList.add(sohMsgBean);

        when(aiForecastServiceRpc.queryPreSohData(Mockito.any())).thenReturn(sohMsgBeanList);
        battHealthEvalJobService.evalBatteryHealth("11");
        Assert.assertEquals("11",monitorObjectBaseBean.getName());
    }

    @Test
    public void battHealthEvalEntranceTest12() throws UedmException
    {

        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setBattType(BattTypeEnum.LFP);
        battTypeBean.setId("11");
        when(battTypeDomain.getBatteryTypeByMoId()).thenReturn(Lists.newArrayList(battTypeBean));
        MonitorObjectBaseBean monitorObjectBaseBean = new MonitorObjectBaseBean();
        monitorObjectBaseBean.setId("11");
        monitorObjectBaseBean.setPath("11");
        monitorObjectBaseBean.setPathId("11");
        monitorObjectBaseBean.setMoc("11");
        monitorObjectBaseBean.setName("11");
        when(battConfigurationDomain.selectBattByLogicId(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList(monitorObjectBaseBean));
        MocHistoryQueryToolBean pvQueryToolBean = new MocHistoryQueryToolBean();
        pvQueryToolBean.setPvType(BatteryConstant.PV_TYPE_PV);
        pvQueryToolBean.setRecordDate("11");
        when(moHistoryDataQueryDomain.buildYesterdayQueryBean(Mockito.anyString())).thenReturn(pvQueryToolBean);
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> historyDataResponseConditionBeans = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setCurrentValue("10");
        historyDataResponseConditionBeans.add(historyDataResponseConditionBean);
        hisDataResponseMap.put("11", historyDataResponseConditionBeans);
        when(batteryHisDataDomain.getHistoryDataByConditions(Mockito.any())).thenReturn(hisDataResponseMap);
        Map<String, Boolean> battAlarmInfo = new HashMap<>();
        battAlarmInfo.put("11", false);
        when(battAlarmDomain.selectHealthAlarmByMoIds(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(battAlarmInfo);
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("11");
        battOverviewAssetConditionVo.setStartDate("11");
        battOverviewAssetConditionVo.setProductionDate("11");
        when(assetRpcImpl.getAssetInfoByCondition(Mockito.any())).thenThrow(new UedmException(-1,"1"));
        List<BattHealthStatusEvalPo> battpreStatusEvalPos=new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("11");
        battHealthStatusEvalPo.setStatusId("unEvaluate");
        battHealthStatusEvalPo.setUnknownReason("11");
        battpreStatusEvalPos.add(battHealthStatusEvalPo);
        when(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(Mockito.any())).thenThrow(new UedmException(-1,"1"));
        HistoryDataResponseConditionBean historyBeans = new HistoryDataResponseConditionBean();
        when(pmaServiceUtils.selectHistoryDataByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(historyBeans));

        List<StandardPointEntity> entities = new ArrayList<>();
        StandardPointEntity entity = new StandardPointEntity();
        entity.setId("batt.health");
        HashMap<String, String> mapN = new HashMap<>();
        mapN.put("zh_CN","1123");
        mapN.put("en_US","12123");
        entity.setName(JSONObject.toJSONString(mapN));
        entities.add(entity);

        List<RecordIndexBean> beans = new ArrayList<>();
        RecordIndexBean bean = new RecordIndexBean();
        bean.setId("batt.batt_charge.final.soc");
        beans.add(bean);
        when(configurationManagerRpc.getStandardPointByIds(Mockito.any(), Mockito.any())).thenReturn(entities);
        when(configurationManagerRpc.getRecordIndexByIds(Mockito.any())).thenThrow(new UedmException(-1,"123"));
        when(i18nUtils.getMapFieldByLanguage(Mockito.any(),Mockito.any())).thenReturn("123");

        battHealthEvalJobService.evalBatteryHealth("11");
        Assert.assertEquals("11",monitorObjectBaseBean.getName());
    }
    @Test
    public void battHealthEvalEntranceTest1() throws UedmException
    {
        Map<String, BattHealthStatusEvaluateService> hs = new HashMap<>();
        battHealthEvalJobService.setHs(hs);
        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setBattType(BattTypeEnum.LFP);
        battTypeBean.setId("11");
        when(battTypeDomain.getBatteryTypeByMoId()).thenReturn(Lists.newArrayList(battTypeBean));
        MonitorObjectBaseBean monitorObjectBaseBean = new MonitorObjectBaseBean();
        monitorObjectBaseBean.setId("11");
        monitorObjectBaseBean.setPath("11");
        monitorObjectBaseBean.setPathId("11");
        monitorObjectBaseBean.setMoc("11");
        monitorObjectBaseBean.setName("11");
        when(battConfigurationDomain.selectBattByLogicId(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList(monitorObjectBaseBean));
        MocHistoryQueryToolBean pvQueryToolBean = new MocHistoryQueryToolBean();
        pvQueryToolBean.setPvType(BatteryConstant.PV_TYPE_PV);
        pvQueryToolBean.setRecordDate("11");
        when(moHistoryDataQueryDomain.buildYesterdayQueryBean(Mockito.anyString())).thenReturn(pvQueryToolBean);
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> historyDataResponseConditionBeans = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setCurrentValue("10");
        historyDataResponseConditionBeans.add(historyDataResponseConditionBean);
        hisDataResponseMap.put("11", historyDataResponseConditionBeans);
        when(batteryHisDataDomain.getHistoryDataByConditions(Mockito.any())).thenReturn(hisDataResponseMap);
        List<BattHealthStatusEvalPo> battpreStatusEvalPos=new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("11");
        battHealthStatusEvalPo.setStatusId("unEvaluate");
        battHealthStatusEvalPo.setUnknownReason("11");
        battpreStatusEvalPos.add(battHealthStatusEvalPo);
        when(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(Mockito.any())).thenReturn(battpreStatusEvalPos);
        Map<String, Boolean> battAlarmInfo = new HashMap<>();
        battAlarmInfo.put("11", false);
        when(battAlarmDomain.selectHealthAlarmByMoIds(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(battAlarmInfo);
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("11");
        battOverviewAssetConditionVo.setStartDate("11");
        battOverviewAssetConditionVo.setProductionDate("11");
        when(assetRpcImpl.getAssetInfoByCondition(Mockito.any())).thenReturn(Lists.newArrayList(battOverviewAssetConditionVo));
        HistoryDataResponseConditionBean historyBeans = new HistoryDataResponseConditionBean();
        when(pmaServiceUtils.selectHistoryDataByCondition(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(historyBeans));
        battHealthEvalJobService.evalBatteryHealth("11");
        Assert.assertEquals("11",monitorObjectBaseBean.getName());
    }

    @Test
    public void battHealthEvalEntranceTest_Empty() throws UedmException
    {
        MonitorObjectBaseBean monitorObjectBaseBean = new MonitorObjectBaseBean();
        monitorObjectBaseBean.setId("11");
        monitorObjectBaseBean.setPath("11");
        monitorObjectBaseBean.setPathId("11");
        monitorObjectBaseBean.setMoc("11");
        monitorObjectBaseBean.setName("11");
        when(battConfigurationDomain.selectBattByLogicId(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList(monitorObjectBaseBean));
        MocHistoryQueryToolBean pvQueryToolBean = new MocHistoryQueryToolBean();
        pvQueryToolBean.setPvType(BatteryConstant.PV_TYPE_PV);
        pvQueryToolBean.setRecordDate("11");
        when(moHistoryDataQueryDomain.buildYesterdayQueryBean(Mockito.anyString())).thenReturn(pvQueryToolBean);
        List<BattHealthStatusEvalPo> battpreStatusEvalPos=new ArrayList<>();
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setId("11");
        battHealthStatusEvalPo.setStatusId("unEvaluate");
        battHealthStatusEvalPo.setUnknownReason("11");
        battpreStatusEvalPos.add(battHealthStatusEvalPo);
        when(battHealthStatusEvalDomain.selectPreHealthStatusByMoIds(Mockito.any())).thenReturn(battpreStatusEvalPos);
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> historyDataResponseConditionBeans = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setCurrentValue("10");
        historyDataResponseConditionBeans.add(historyDataResponseConditionBean);
        hisDataResponseMap.put(BattStandardPointConstants.BATT_SMPID_HEALTH, historyDataResponseConditionBeans);
        when(batteryHisDataDomain.getHistoryDataByConditions(Mockito.any())).thenReturn(hisDataResponseMap);
        Map<String, Boolean> battAlarmInfo = new HashMap<>();
        battAlarmInfo.put("11", false);
        when(battAlarmDomain.selectHealthAlarmByMoIds(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(battAlarmInfo);
        BattOverviewAssetConditionVo battOverviewAssetConditionVo = new BattOverviewAssetConditionVo();
        battOverviewAssetConditionVo.setId("11");
        battOverviewAssetConditionVo.setStartDate("11");
        battOverviewAssetConditionVo.setProductionDate("11");
        when(assetRpcImpl.getAssetInfoByCondition(Mockito.any())).thenReturn(Lists.newArrayList(battOverviewAssetConditionVo));
        battHealthEvalJobService.evalBatteryHealth("11");
        Assert.assertEquals("11",monitorObjectBaseBean.getName());
    }

    @Test
    public void batteryAlarmInfoQueryTest() throws UedmException
    {
        battHealthEvalJobService.evalBatteryHealth("11");
    }
}
