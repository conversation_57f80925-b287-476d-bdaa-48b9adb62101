package com.zte.uedm.battery.controller.backuppowerconfig.controller;

import com.github.pagehelper.PageInfo;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.bean.MocIdsVO;
import com.zte.uedm.battery.bean.MonitorObjectDsBean;
import com.zte.uedm.battery.controller.backuppowerconfig.dto.BackupPowerConfigUpdateDto;
import com.zte.uedm.battery.controller.backuppowerconfig.service.BackupPowerConfigService;
import com.zte.uedm.battery.controller.battlife.vo.BattLifeConfigVo;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.consts.MocType;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeParameterUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doThrow;

public class BackupPowerConfigControllerTest
{
    @InjectMocks
    private BackupPowerConfigController backupPowerConfigController;
    @Mock
    private BackupPowerConfigService backupPowerConfigService;
    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectBackupPowerConfig_param_null() throws UedmException
    {

        doReturn(Arrays.asList(new BattLifeConfigVo())).when(backupPowerConfigService).selectBackupPowerConfig(any(), any());
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = backupPowerConfigController.selectBackupPowerConfig(1,2,
                request, "zh");
        Assert.assertEquals("param is blank.", responseBean.getMessage());
    }

    @Test
    public void selectBackupPowerConfig_normal() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BattLifeConfigVo> pageList = new PageInfo<>();
        pageList.setTotal(100);

        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattLifeConfigVo> beanList = new ArrayList<>();
        BattLifeConfigVo battLifeConfigVo = new BattLifeConfigVo();
        battLifeConfigVo.setUserName("apiuser");
        beanList.add(battLifeConfigVo);
        doReturn(beanList).when(backupPowerConfigService).selectBackupPowerConfig(any(), any());
        ResponseBean responseBean = backupPowerConfigController.selectBackupPowerConfig(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectBackupPowerConfig_exec() throws UedmException
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(backupPowerConfigService).selectBackupPowerConfig(any(), any());

            HttpServletRequest request = mock(HttpServletRequest.class);
            ResponseBean responseBean = backupPowerConfigController.selectBackupPowerConfig(1,2,
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void updateBackupPowerConfig_username_null() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-100,(int) responseBean.getCode());
    }
    @Test
    public void updateBackupPowerConfig_list_empty() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");

        doReturn(1).when(backupPowerConfigService).updateBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateBackupPowerConfig(new ArrayList<>(),request,"zh_CN");
        Assert.assertEquals(-301,(int) responseBean.getCode());
    }
    @Test
    public void updateBackupPowerConfig_list_repeat() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto,backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-302,(int) responseBean.getCode());
    }
    @Test
    public void updateBackupPowerConfig_normal() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }
    @Test
    public void updateBackupPowerConfig_ex() throws IOException, UedmException
    {
        try {
            HttpServletRequest request = mock(HttpServletRequest.class);
            when(Tools.getUserName(request)).thenReturn("admin");
            BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
            backupPowerConfigUpdateDto.setDimId("name");
            backupPowerConfigUpdateDto.setSequence(1);
            backupPowerConfigUpdateDto.setEnable(true);

            doThrow(new UedmException(-1,"77")).when(backupPowerConfigService).updateBackupPowerConfig(any(), any(), any());
            backupPowerConfigController.updateBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        }catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void selectDistributionBackupPowerConfig_null() throws UedmException
    {

        doReturn(Arrays.asList(new BattLifeConfigVo())).when(backupPowerConfigService).selectDistributionBackupPowerConfig(any(), any());
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = backupPowerConfigController.selectDistributionBackupPowerConfig(1,2,
                request, "zh");
        Assert.assertEquals("param is blank.", responseBean.getMessage());
    }

    @Test
    public void selectDistributionBackupPowerConfig_normal() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BattLifeConfigVo> pageList = new PageInfo<>();
        pageList.setTotal(100);

        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattLifeConfigVo> beanList = new ArrayList<>();
        BattLifeConfigVo battLifeConfigVo = new BattLifeConfigVo();
        battLifeConfigVo.setUserName("apiuser");
        beanList.add(battLifeConfigVo);
        doReturn(beanList).when(backupPowerConfigService).selectDistributionBackupPowerConfig(any(), any());
        ResponseBean responseBean = backupPowerConfigController.selectDistributionBackupPowerConfig(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectDistributionBackupPowerConfig_exec()
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(backupPowerConfigService).selectDistributionBackupPowerConfig(any(), any());

            HttpServletRequest request = mock(HttpServletRequest.class);
            ResponseBean responseBean = backupPowerConfigController.selectDistributionBackupPowerConfig(1,2,
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void updateDistributionBackupPowerConfig_username_null() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateDistributionBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateDistributionBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-100,(int) responseBean.getCode());
    }
    @Test
    public void updateDistributionBackupPowerConfig_list_empty() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");

        doReturn(1).when(backupPowerConfigService).updateDistributionBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateDistributionBackupPowerConfig(new ArrayList<>(),request,"zh_CN");
        Assert.assertEquals(-301,(int) responseBean.getCode());
    }
    @Test
    public void updateDistributionBackupPowerConfig_list_repeat() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateDistributionBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateDistributionBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto,backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-302,(int) responseBean.getCode());
    }
    @Test
    public void updateDistributionBackupPowerConfig_normal() throws  UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateDistributionBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateDistributionBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }
    @Test
    public void updateDistributionBackupPowerConfig_ex()
    {
        try {
            HttpServletRequest request = mock(HttpServletRequest.class);
            when(Tools.getUserName(request)).thenReturn("admin");
            BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
            backupPowerConfigUpdateDto.setDimId("name");
            backupPowerConfigUpdateDto.setSequence(1);
            backupPowerConfigUpdateDto.setEnable(true);

            doThrow(new UedmException(-1,"77")).when(backupPowerConfigService).updateDistributionBackupPowerConfig(any(), any(), any());
            backupPowerConfigController.updateDistributionBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        }catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void selectStatisticsBackupPowerConfig_null() throws UedmException
    {

        doReturn(Arrays.asList(new BattLifeConfigVo())).when(backupPowerConfigService).selectStatisticsBackupPowerConfig(any(), any());
        HttpServletRequest request = PowerMockito.mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        ResponseBean responseBean = backupPowerConfigController.selectStatisticsBackupPowerConfig(1,2,
                request, "zh");
        Assert.assertEquals("param is blank.", responseBean.getMessage());
    }

    @Test
    public void selectStatisticsBackupPowerConfig_normal() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        PageInfo<BattLifeConfigVo> pageList = new PageInfo<>();
        pageList.setTotal(100);

        when(Tools.getUserName(request)).thenReturn("admin");
        List<BattLifeConfigVo> beanList = new ArrayList<>();
        BattLifeConfigVo battLifeConfigVo = new BattLifeConfigVo();
        battLifeConfigVo.setUserName("apiuser");
        beanList.add(battLifeConfigVo);
        doReturn(beanList).when(backupPowerConfigService).selectStatisticsBackupPowerConfig(any(), any());
        ResponseBean responseBean = backupPowerConfigController.selectStatisticsBackupPowerConfig(1,2,request,"");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectStatisticsBackupPowerConfig_exec()
    {
        try
        {
            doThrow(new UedmException(-1, "xxx")).when(backupPowerConfigService).selectStatisticsBackupPowerConfig(any(), any());

            HttpServletRequest request = mock(HttpServletRequest.class);
            ResponseBean responseBean = backupPowerConfigController.selectStatisticsBackupPowerConfig(1,2,
                    request, "zh");
        }
        catch (UedmException e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void updateStatisticsBackupPowerConfig_username_null() throws UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateStatisticsBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateStatisticsBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-100,(int) responseBean.getCode());
    }
    @Test
    public void updateStatisticsBackupPowerConfig_list_empty() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");

        doReturn(1).when(backupPowerConfigService).updateStatisticsBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateStatisticsBackupPowerConfig(new ArrayList<>(),request,"zh_CN");
        Assert.assertEquals(-301,(int) responseBean.getCode());
    }
    @Test
    public void updateStatisticsBackupPowerConfig_list_repeat() throws IOException, UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateStatisticsBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateStatisticsBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto,backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(-302,(int) responseBean.getCode());
    }
    @Test
    public void updateStatisticsBackupPowerConfig_normal() throws  UedmException
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
        backupPowerConfigUpdateDto.setDimId("name");
        backupPowerConfigUpdateDto.setSequence(1);
        backupPowerConfigUpdateDto.setEnable(true);

        doReturn(1).when(backupPowerConfigService).updateStatisticsBackupPowerConfig(any(), any(), any());
        ResponseBean responseBean = backupPowerConfigController.updateStatisticsBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        Assert.assertEquals(0,(int) responseBean.getCode());
    }
    @Test
    public void updateStatisticsBackupPowerConfig_ex()
    {
        try {
            HttpServletRequest request = mock(HttpServletRequest.class);
            when(Tools.getUserName(request)).thenReturn("admin");
            BackupPowerConfigUpdateDto backupPowerConfigUpdateDto = new BackupPowerConfigUpdateDto();
            backupPowerConfigUpdateDto.setDimId("name");
            backupPowerConfigUpdateDto.setSequence(1);
            backupPowerConfigUpdateDto.setEnable(true);

            doThrow(new UedmException(-1,"77")).when(backupPowerConfigService).updateStatisticsBackupPowerConfig(any(), any(), any());
            backupPowerConfigController.updateStatisticsBackupPowerConfig(Arrays.asList(backupPowerConfigUpdateDto),request,"zh_CN");
        }catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }

    @Test
    public void testQueryScenarioBasedMonitorObjects()
    {
        HttpServletRequest request = mock(HttpServletRequest.class);
        // 参数为空
        backupPowerConfigController.queryScenarioBasedMonitorObjectsNew(null ,request,"zh-CN");

        // 准备参数
        MocIdsVO mocIdsVO = new MocIdsVO(null,null);
        mocIdsVO.setIdList(new ArrayList<>());
        mocIdsVO.setMoc(MocType.BATT);
        mocIdsVO.setMoc(MocType.SP);
        mocIdsVO.setScenarioType("1");
        mocIdsVO.setPageNo(1);
        mocIdsVO.setPageSize(10);

        // 用户名为空
        backupPowerConfigController.queryScenarioBasedMonitorObjectsNew(mocIdsVO ,request,"zh-CN");

        // 正常情况
        when(Tools.getUserName(request)).thenReturn("admin");
        when(Tools.getRemoteHost(request)).thenReturn("*******");
        PageInfo<MonitorObjectDsBean> pageInfo = new PageInfo<>();
        pageInfo.setTotal(10L);
        pageInfo.setList(new ArrayList<>());
        try {
            Mockito.doReturn(pageInfo)
                    .when(backupPowerConfigService).queryScenarioBasedMonitorObjects(Mockito.any(),Mockito.any());
        } catch (UedmException | com.zte.uedm.basis.exception.UedmException ignored) {

        }
        backupPowerConfigController.queryScenarioBasedMonitorObjectsNew(mocIdsVO ,request,"zh-CN");

        // 异常情况
        try {
            Mockito.doThrow(UedmErrorCodeParameterUtil.parameterIsMustInput(""))
                    .when(backupPowerConfigService).queryScenarioBasedMonitorObjects(Mockito.any(),Mockito.any());
            backupPowerConfigController.queryScenarioBasedMonitorObjectsNew(mocIdsVO ,request,"zh-CN");
        } catch (UedmException | com.zte.uedm.basis.exception.UedmException e) {
            Assert.assertEquals("Parameter is must input" , e.getMessage());
        }
        Assert.assertNotNull(mocIdsVO);
    }

}
