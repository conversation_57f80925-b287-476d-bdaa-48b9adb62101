package com.zte.uedm.battery.a_interfaces.peakshift.web;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.battery.a_application.peakshift.executor.PeakShiftDistributionService;
import com.zte.uedm.battery.a_domain.common.peakshift.PeakShiftConstants;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.*;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.PeakShiftDeviceTasKVo;
import com.zte.uedm.battery.a_interfaces.peakshift.web.vo.PeakShiftTaskDetailVo;
import com.zte.uedm.battery.bean.DevicePeakCacheInfoBean;
import com.zte.uedm.battery.bean.PeakShiftDetailVo;
import com.zte.uedm.battery.bean.PeakShiftTaskVo;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskNameCheckDto;
import com.zte.uedm.battery.bean.peak.PeakShiftTaskPo;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService;
import com.zte.uedm.service.mp.api.adapter.vo.AdapterPointDataVo;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static org.mockito.Mockito.*;

public class PeakShiftStrategyDistributionControllerTest {

    @InjectMocks
    private PeakShiftStrategyDistributionController peakShiftTaskController;

    @Mock
    private PeakShiftDistributionService peakShiftDistributionService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private ConfigService configService;

    @Mock
    private MpRpcImpl mpRpcImpl;

    @Mock
    private JsonService jsonService;

    @Mock
    private MessageSenderService msgSenderService;

    @Mock
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    @Mock
    private I18nUtils i18nUtils;

    public static final String NAME = "name";

    public static final String FILEID = "fileId";

    public static final String DEVICEIDS = "deviceIds";

    @Before
    public void setUp() throws IOException, UedmException {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectByConditionTest() throws UedmException {
        UedmException flag = null;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            List<PeakShiftTaskVo> list = new ArrayList<>();
            PeakShiftTaskVo peakShiftTaskVo = new PeakShiftTaskVo();
            peakShiftTaskVo.setId("12");
            list.add(peakShiftTaskVo);
            PeakShiftStrategyDistributionDto requestBean = new PeakShiftStrategyDistributionDto();
            PageInfo<PeakShiftTaskVo> pageInfoList = new PageInfo<>(list);
            doReturn(pageInfoList).when(peakShiftDistributionService).selectByCondition(Mockito.any(), Mockito.any());
            peakShiftTaskController.selectByCondition(requestBean, 1, 10, request, "1");
        } catch (UedmException e) {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void selectByConditionTestNull() throws UedmException {
        UedmException flag = null;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            List<PeakShiftTaskVo> list = new ArrayList<>();
            PeakShiftTaskVo peakShiftTaskVo = new PeakShiftTaskVo();
            peakShiftTaskVo.setId("12");
            list.add(peakShiftTaskVo);
            PeakShiftStrategyDistributionDto requestBean = new PeakShiftStrategyDistributionDto();
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("userName", "host", "12", false, false, 1, 11);
            PageInfo<PeakShiftTaskVo> pageInfoList = new PageInfo<>(list);
            doReturn(pageInfoList).when(peakShiftDistributionService).selectByCondition(Mockito.any(), Mockito.any());
            peakShiftTaskController.selectByCondition(requestBean, null, null, request, "1");
        } catch (UedmException e) {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void selectByConditionTestException() throws UedmException {
        UedmException flag = null;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            List<PeakShiftTaskVo> list = new ArrayList<>();
            PeakShiftTaskVo peakShiftTaskVo = new PeakShiftTaskVo();
            peakShiftTaskVo.setId("12");
            list.add(peakShiftTaskVo);
            PeakShiftStrategyDistributionDto requestBean = new PeakShiftStrategyDistributionDto();
            Mockito.when(peakShiftDistributionService.selectByCondition(Mockito.any(), Mockito.any())).thenThrow(new UedmException(-100, "123"));
            peakShiftTaskController.selectByCondition(requestBean, 1, 10, request, "1");
        } catch (UedmException e) {
            Assert.assertEquals("123", e.getMessage());
        }
    }

    @Test
    public void detailByIdTest() throws UedmException {
        UedmException flag = null;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            List<PeakShiftTaskVo> list = new ArrayList<>();
            PeakShiftTaskVo peakShiftTaskVo = new PeakShiftTaskVo();
            peakShiftTaskVo.setId("12");
            list.add(peakShiftTaskVo);
            PeakShiftTaskDetailVo peakShiftDetailVo = new PeakShiftTaskDetailVo();
            peakShiftDetailVo.setFileName("1");
            peakShiftDetailVo.setCreator("SYSTEM");
            peakShiftDetailVo.setName("CSU5-V2.06.07.02-ykmzfzyoev-AutoTask-20241213172100");
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("userName", "host", "12", false, false, 1, 11);
            doReturn(peakShiftDetailVo).when(peakShiftDistributionService).detailById(Mockito.any(), Mockito.any());
            PeakShiftDetailDto requestBean = new PeakShiftDetailDto();
            requestBean.setId("id");
            when(i18nUtils.getMapFieldByLanguageOption(any(), any())).thenReturn("自动任务");
            peakShiftTaskController.detailById(requestBean, null, null, request, "1");
        } catch (UedmException e) {
            Assert.assertEquals("", e.getMessage());
        }
    }

    @Test
    public void detailByIdTestException() throws UedmException {
        UedmException flag = null;
        try {
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            List<PeakShiftTaskVo> list = new ArrayList<>();
            PeakShiftTaskVo peakShiftTaskVo = new PeakShiftTaskVo();
            peakShiftTaskVo.setId("12");
            list.add(peakShiftTaskVo);
            PeakShiftDetailVo peakShiftDetailVo = new PeakShiftDetailVo();
            peakShiftDetailVo.setFileName("1");
            peakShiftDetailVo.setCreator("SYSTEM");
            peakShiftDetailVo.setName("CSU5-V2.06.07.02-ykmzfzyoev-AutoTask-20241213172100");
            ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("userName", "host", "12", false, false, 1, 11);
            doThrow(new UedmException(-100, "123")).when(peakShiftDistributionService).detailById(Mockito.any(), Mockito.any());
            PeakShiftDetailDto requestBean = new PeakShiftDetailDto();
            requestBean.setId("id");
            peakShiftTaskController.detailById(requestBean, null, null, request, "1");
        } catch (UedmException e) {

            Assert.assertEquals("123", e.getMessage());
        }
    }

    @Test
    public void insertTest() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        doReturn(1).when(peakShiftDistributionService).insert(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-303, responseBean.getCode().intValue());
    }

    @Test
    public void testDelete() throws Exception {
        try {
            // Setup
            // Run the test
            ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("da", "dfdas", "zh-CN");
            doNothing().when(peakShiftDistributionService).delete("id", serviceBean);
            HttpServletRequest request = mock(HttpServletRequest.class);

            peakShiftTaskController.delete("id", request, "zh-CN");
        } catch (Exception e) {
            Assert.assertSame("", e.getMessage());
        }

    }

    @Test
    public void testDeleteEx() throws Exception {
        // Setup
        // Run the test
        HttpServletRequest request = mock(HttpServletRequest.class);
        final ResponseBean result = peakShiftTaskController.delete(null, request, "zh-CN");

        // Verify the results
        Assert.assertTrue(result.getCode() == -301);

    }

    @Test
    public void testDelete_PeakShiftTaskServiceThrowsUedmException() throws Exception {
        // Setup
        ServiceBaseInfoBean serviceBean = new ServiceBaseInfoBean("da", "dfdas", "zh-CN");
        doThrow(new UedmException(-1, "")).when(peakShiftDistributionService).delete("id", serviceBean);

        // Run the test
        HttpServletRequest request = mock(HttpServletRequest.class);
        final ResponseBean result = peakShiftTaskController.delete("id", request, "zh-CN");

        // Verify the results
        Assert.assertEquals(0, (int) result.getCode());
    }

    @Test
    public void insertTest1() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(peakShiftTaskPos).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-302, responseBean.getCode().intValue());
    }

    @SneakyThrows
    @Test
    public void insertTest2() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(new ArrayList<>()).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @SneakyThrows
    @Test
    public void insertTest5() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(new ArrayList<>()).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> collectorPointMap = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> pointMap = new HashMap<>();
        Map<String, AdapterPointDataVo> valueMap = new HashMap<>();
        AdapterPointDataVo pointDataVo = new AdapterPointDataVo();
        pointDataVo.setValue("123");
        valueMap.put("1", pointDataVo);
        pointMap.put("030184", valueMap);
        collectorPointMap.put("MD-HL-RB-BD6-HVPM04-DC-1", pointMap);
        collectorPointMap.put("MD-HL-RB-BD6-M0106-DA-D", pointMap);
        Mockito.when(mpRpcImpl.getOriginalDataByCollectList(any(),any())).thenReturn(collectorPointMap);

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

//        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @SneakyThrows
    @Test
    public void insertTest_error() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(new ArrayList<>()).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        doThrow(UedmException.class).when(mpRpcImpl).getOriginalDataByCollectList(any(), any());
        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

//        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }
    @SneakyThrows
    @Test
    public void insertTest6() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(new ArrayList<>()).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> collectorPointMap = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> pointMap = new HashMap<>();
        Map<String, AdapterPointDataVo> valueMap = new HashMap<>();
        AdapterPointDataVo pointDataVo = new AdapterPointDataVo();
        pointDataVo.setValue("123");
        valueMap.put("1", pointDataVo);
        pointMap.put("030184", valueMap);
        collectorPointMap.put("MD-HL-RB-BD6-HVPM04-DC-1", pointMap);
        collectorPointMap.put("MD-HL-RB-BD6-M0106-DA-D", pointMap);
        Mockito.when(mpRpcImpl.getOriginalDataByCollectList(any(), any())).thenReturn(collectorPointMap);

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

//        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @Test
    public void insertTest3() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("test");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(new ArrayList<>()).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @Test
    public void insertTest4() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("test");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doThrow(UedmException.class).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        ResponseBean responseBean = peakShiftTaskController.insert(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-1, responseBean.getCode().intValue());
    }

    @Test
    public void updateTest() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        doReturn(1).when(peakShiftDistributionService).update(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");

        ResponseBean responseBean = peakShiftTaskController.edit(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-303, responseBean.getCode().intValue());
    }

    @Test
    public void updateTest1() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();

        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUA");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(peakShiftTaskPos).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        ResponseBean responseBean = peakShiftTaskController.edit(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    @Test
    public void updateTest2() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();
        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUC");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(peakShiftTaskPos).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");
        DevicePeakCacheInfoBean devicePeakCacheInfoBean = new DevicePeakCacheInfoBean();
        devicePeakCacheInfoBean.setDeviceId("MD-HL-RB-BD6-M0106-DA-D");
        devicePeakCacheInfoBean.setDeviceType("BCUA");
        doReturn(Collections.singletonList(devicePeakCacheInfoBean)).when(configurationManagerRpc).queryAllList();

        doReturn(1).when(peakShiftDistributionService).update(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());

        ResponseBean responseBean = peakShiftTaskController.edit(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-3051, responseBean.getCode().intValue());
    }

    /* Started by AICoder, pid:598cc3f0d6xf43f14fae08c7c0a4cd3415d73bd1 */
    @Test
    public void updateTest3() throws UedmException, InterruptedException {
        HttpServletRequest req = mock(HttpServletRequest.class);

        PeakShiftTaskDto peakShiftTaskBeanDto = new PeakShiftTaskDto();
        peakShiftTaskBeanDto.setDescription("da");
        List<String> objects = Lists.newArrayList();
        objects.add("MD-HL-RB-BD6-HVPM04-DC-1");
        objects.add("MD-HL-RB-BD6-M0106-DA-D");
        peakShiftTaskBeanDto.setDeviceIds(objects);
        peakShiftTaskBeanDto.setName("da");
        peakShiftTaskBeanDto.setFileIds(Collections.singletonList("da"));
        peakShiftTaskBeanDto.setId("da");
        peakShiftTaskBeanDto.setEffectiveDate("fghfgh");
        peakShiftTaskBeanDto.setExpirationDate("fghfgh");
        peakShiftTaskBeanDto.setTemplateStrategyId("xx");
        peakShiftTaskBeanDto.setDeviceType("BCUC");
        List<PeakShiftTaskPo> peakShiftTaskPos = Lists.newArrayList();
        peakShiftTaskPos.add(new PeakShiftTaskPo());
        doReturn(peakShiftTaskPos).when(peakShiftDistributionService).checkTaskByname(Mockito.any());

        when(configService.getGlobalProperty(anyString())).thenReturn("123");

        doThrow(UedmException.class).when(peakShiftDistributionService).update(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());

        ResponseBean responseBean = peakShiftTaskController.edit(peakShiftTaskBeanDto, req, "da");

        Assert.assertEquals(-1, responseBean.getCode().intValue());
    }
    /* Ended by AICoder, pid:598cc3f0d6xf43f14fae08c7c0a4cd3415d73bd1 */

    @Test
    public void duplicateNameCheckTest() {
        ResponseBean ret = peakShiftTaskController.duplicateNameCheck(new PeakShiftTaskNameCheckDto());
        Assert.assertEquals(-301, ret.getCode().intValue());
    }

    /* Started by AICoder, pid:31803m16e8fa96a141bd09fb4083b712de195623 */
    @Test
    public void duplicateNameCheckTest1() throws UedmException {
        PeakShiftTaskNameCheckDto dto = new PeakShiftTaskNameCheckDto();
        dto.setId("adsa");
        dto.setName("fgh");
        doReturn(false).when(peakShiftDistributionService).duplicateNameCheck(any(), any());
        ResponseBean ret = peakShiftTaskController.duplicateNameCheck(dto);
        Assert.assertFalse((Boolean) ret.getData());
    }
    /* Ended by AICoder, pid:31803m16e8fa96a141bd09fb4083b712de195623 */

    /* Started by AICoder, pid:04f52s2ea9j3b791422309ae5092e124d690b5c6 */
    @Test
    public void duplicateNameCheckTest_Ex() {
        try {
            PeakShiftTaskNameCheckDto dto = new PeakShiftTaskNameCheckDto();
            dto.setId("adsa");
            dto.setName("fgh");
            doThrow(new UedmException(-301, "")).when(peakShiftDistributionService).duplicateNameCheck(any(), any());
            peakShiftTaskController.duplicateNameCheck(dto);
        } catch (UedmException e) {
            Assert.assertEquals(-301, e.getErrorId().intValue());
        }
    }
    /* Ended by AICoder, pid:04f52s2ea9j3b791422309ae5092e124d690b5c6 */

    @Test
    public void doStatusFlipTest() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ResponseBean ret = peakShiftTaskController.doStatusFlip(new PeakShiftDistributionStatusFlipDto(), request, "zh-CN");
        Assert.assertEquals(-401, ret.getCode().intValue());
    }

    /* Started by AICoder, pid:tab16ffc97t5a63143de0bfc702b002ade825fd9 */
    @Test
    public void doStatusFlipTest1() throws UedmException {
        Pair<Integer, String> pair = new MutablePair<>(-402, "ASd");
        doReturn(pair).when(peakShiftDistributionService).doStatusFlip(any(), any(), any());

        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        PeakShiftDistributionStatusFlipDto dto = new PeakShiftDistributionStatusFlipDto();
        dto.setId("dfg");
        dto.setStatus("dfvxcb");
        ResponseBean ret = peakShiftTaskController.doStatusFlip(dto, request, "zh-CN");
        Assert.assertEquals(-402, ret.getCode().intValue());
    }
    /* Ended by AICoder, pid:tab16ffc97t5a63143de0bfc702b002ade825fd9 */

    /* Started by AICoder, pid:z57eazab02t4b6614ed80a7f50659c225ac19b9d */
    @Test
    public void doStatusFlipTest_Ex() throws UedmException {
        try {
            doThrow(new UedmException(-1, "")).when(peakShiftDistributionService).doStatusFlip(any(), any(), any());
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PeakShiftDistributionStatusFlipDto dto = new PeakShiftDistributionStatusFlipDto();
            dto.setId("dfg");
            dto.setStatus("dfvxcb");
            peakShiftTaskController.doStatusFlip(dto, request, "zh-CN");
        } catch (UedmException e) {
            Assert.assertEquals(-1, e.getErrorId().intValue());
        }
    }
    /* Ended by AICoder, pid:z57eazab02t4b6614ed80a7f50659c225ac19b9d */

    @Test
    public void selectDeviceTaskInfoByConditionTest() throws UedmException {
        PeakShiftDeviceTaskDto requestBean = new PeakShiftDeviceTaskDto();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        PageInfo<PeakShiftDeviceTasKVo> pageInfoList = new PageInfo<>();
        pageInfoList.setList(new ArrayList<>());
        doReturn(pageInfoList).when(peakShiftDistributionService).selectDeviceTaskInfoByCondition(Mockito.any(), Mockito.any());
        ResponseBean ret = peakShiftTaskController.selectDeviceTaskInfoByCondition(requestBean, null, null, request, "");
        List<PeakShiftDeviceTasKVo> retLis = (List<PeakShiftDeviceTasKVo>) ret.getData();
        Assert.assertEquals(0, retLis.size());
    }

    @Test
    public void selectDeviceTaskInfoByConditionExTest() throws UedmException {
        try {
            PeakShiftDeviceTaskDto requestBean = new PeakShiftDeviceTaskDto();
            HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
            PageInfo<PeakShiftDeviceTasKVo> pageInfoList = new PageInfo<>();
            pageInfoList.setList(new ArrayList<>());
            doThrow(new UedmException(-1, "")).when(peakShiftDistributionService).selectDeviceTaskInfoByCondition(Mockito.any(), Mockito.any());
            ResponseBean ret = peakShiftTaskController.selectDeviceTaskInfoByCondition(requestBean, null, null, request, "");
        } catch (UedmException e) {
            Assert.assertEquals(-1, e.getMessage());
        }
    }

    /* Started by AICoder, pid:c3940ua05du03ea1462708e330d0644a37c1fbe4 */
    @Test
    public void portErrorCheck1() {
        // 调用peakShiftTaskController的portErrorCheck方法，传入空的HashMap、单元素列表、单元素列表和字符串"1"
        peakShiftTaskController.portErrorCheck(new HashMap<>(), Collections.singletonList("1"), Collections.singletonList("1"), "1");
        // 再次调用peakShiftTaskController的portErrorCheck方法，传入空的ArrayList、单元素列表和字符串"1"
        peakShiftTaskController.portErrorCheck(new HashMap<>(), new ArrayList<>(), Collections.singletonList("1"), "1");

        Map<String, Map<String, Map<String, AdapterPointDataVo>>> collectorMap = new HashMap<>();
        // 向collectorMap中添加一个名为"empty"的空HashMap
        collectorMap.put("empty", new HashMap<>());
        // 创建两个Map对象map1和map2
        Map<String, AdapterPointDataVo> map1 = new HashMap<>();
        Map<String, Map<String, AdapterPointDataVo>> map2 = new HashMap<>();
        // 创建一个名为map的HashMap
        Map<String, Map<String, AdapterPointDataVo>> map = new HashMap<>();
        // 创建两个AdapterPointDataVo对象adapterPointDataVo1和adapterPointDataVo2
        AdapterPointDataVo adapterPointDataVo1 = new AdapterPointDataVo();
        AdapterPointDataVo adapterPointDataVo2 = new AdapterPointDataVo();
        // 设置adapterPointDataVo1的value属性为"123"
        adapterPointDataVo1.setValue("123");
        // 将adapterPointDataVo1添加到map1中，键为"1"
        map1.put("1", adapterPointDataVo1);
        // 设置adapterPointDataVo2的value属性为"234"
        adapterPointDataVo2.setValue("234");
        // 将adapterPointDataVo2添加到map1中，键为"2"
        map1.put("2", adapterPointDataVo2);
        // 将map1添加到map中，键为PeakShiftConstants.PICK_SHIFT_DEVICE_ORG_POINT_ID
        map.put(PeakShiftConstants.PICK_SHIFT_DEVICE_ORG_POINT_ID, map1);
        // 将null添加到map2中，键为PeakShiftConstants.PICK_SHIFT_DEVICE_ORG_POINT_ID
        map2.put(PeakShiftConstants.PICK_SHIFT_DEVICE_ORG_POINT_ID, null);
        // 将map添加到collectorMap中，键为"2"
        collectorMap.put("2", map);
        // 将map2添加到collectorMap中，键为"3"
        collectorMap.put("3", map2);
        // 创建一个名为list的ArrayList，并添加字符串"1"
        List<String> list = new ArrayList<>();
        list.add("1");
        // 调用peakShiftTaskController的portErrorCheck方法，传入collectorMap、空的ArrayList、list和字符串"234"
        peakShiftTaskController.portErrorCheck(collectorMap, new ArrayList<>(), list, "234");
    }
    /* Ended by AICoder, pid:c3940ua05du03ea1462708e330d0644a37c1fbe4 */

}