package com.zte.uedm.battery.export.manage.entity;

import com.zte.uedm.battery.util.FileUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;

public class Base64ConvertTest {
    @After
    public void tearDown() {
        File file = new File("/user/local/photoname");
        FileUtils.delFile(file);
    }
    @Test
    public void test()
    {
        Boolean res1 = Base64Convert.generateImage(null, "photoname");
        Boolean res2 = Base64Convert.generateImage("data:image/jpeg;base64,string", "photoname");
        Boolean res3 = Base64Convert.generateImage("data:image/png;base64,string", "photoname");
        Boolean res4 = Base64Convert.generateImage("string", "photoname");


        Assert.assertEquals(res1,false);
    }
}
