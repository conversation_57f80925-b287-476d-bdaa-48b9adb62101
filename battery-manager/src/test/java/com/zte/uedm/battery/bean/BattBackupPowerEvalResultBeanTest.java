package com.zte.uedm.battery.bean;

import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;

public class BattBackupPowerEvalResultBeanTest {
    /* Started by AICoder, pid:f11250269135424dba4a47781d06e525 */


        @Test
        public void testBattBackupPowerEvalResultBean() {
            BattBackupPowerEvalResultBean bean = new BattBackupPowerEvalResultBean();

            // 测试备电时长
            bean.setResult(new BigDecimal("10"));
            assertEquals(0, bean.getResult().compareTo(new BigDecimal("10")));

            // 测试备电时长(告警)
            bean.setAlarmResult(new BigDecimal("20"));
            assertEquals(0, bean.getAlarmResult().compareTo(new BigDecimal("20")));

            // 测试备电时长(恒流)
            bean.setCurrResult(new BigDecimal("30"));
            assertEquals(0, bean.getCurrResult().compareTo(new BigDecimal("30")));

            // 测试备电时长（铅酸铁锂混用场景，分开显示）
            bean.setLiAndLeadAlarmResult("40");
            assertEquals("40", bean.getLiAndLeadAlarmResult());

            // 测试备电时长（铅酸铁锂混用场景，分开显示）
            bean.setLiAndLeadCurrResult("50");
            assertEquals("50", bean.getLiAndLeadCurrResult());

            // 测试满充判断(1,满充，0，非满充或无法判断)
            bean.setMax(1);
            assertEquals(1, bean.getMax().intValue());
        }
    }
    /* Ended by AICoder, pid:f11250269135424dba4a47781d06e525 */

