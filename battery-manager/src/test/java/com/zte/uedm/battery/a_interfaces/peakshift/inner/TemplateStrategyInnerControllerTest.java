package com.zte.uedm.battery.a_interfaces.peakshift.inner;

import com.zte.log.filter.UserThreadLocal;
import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.GridStrategyRepository;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.TemplateStrategyRepository;
import com.zte.uedm.battery.a_domain.gateway.ConfigurationServiceInterface;
import com.zte.uedm.battery.a_domain.service.peakshift.PeakShiftCommonService;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftBCUAServiceImpl;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftCSU5ServiceImpl;
import com.zte.uedm.battery.a_domain.service.peakshift.impl.PeakShiftSNMPServiceImpl;
import com.zte.uedm.battery.a_infrastructure.acl.impl.ConfigurationNewServiceImpl;
import com.zte.uedm.battery.a_infrastructure.acl.rpc.ConfigurationRpc;
import com.zte.uedm.battery.a_infrastructure.cache.manager.*;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.GridStrategyNewMapper;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.mapper.TemplateStrategyNewMapper;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.persistence.GridStrategyRepositoryImpl;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.persistence.TemplateStrategyRepositoryImpl;
import com.zte.uedm.battery.a_interfaces.peakshift.inner.dto.QueryFileIdRpcDto;
import com.zte.uedm.battery.a_interfaces.peakshift.web.TemplateStrategyController;
import com.zte.uedm.common.bean.ServiceBaseInfoBean;
import com.zte.uedm.common.bean.log.OperlogBean;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import ft.fake.db.GridStrategyNewMapperFake;
import ft.fake.db.TemplateStrategyNewMapperFake;
import ft.fake.rpc.gateway.ConfigurationRpcFake;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

public class TemplateStrategyInnerControllerTest {

    @InjectMocks
    private TemplateStrategyInnerController templateStrategyInnerController;

    @Resource
    private GridStrategyRepository gridStrategyRepository = new GridStrategyRepositoryImpl();

    @Resource
    private TemplateStrategyRepository templateStrategyRepository = new TemplateStrategyRepositoryImpl();

    @Resource
    private TemplateStrategyNewMapper templateStrategyNewMapper = new TemplateStrategyNewMapperFake();

    @Resource
    private GridStrategyNewMapper gridStrategyNewMapper = new GridStrategyNewMapperFake();

    @Mock
    private HttpServletRequest request;

    @Mock
    private GroupCacheManager groupCacheManager;

    @Mock
    private FieldCacheManager fieldCacheManager;

    @Mock
    private DeviceCacheManager deviceCacheManager;

    @Mock
    private CollectorCacheManager collectorCacheManager;

    @Mock
    private MocCacheManager mocCacheManager;

    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager;

    @Mock
    private ResourceBaseCacheManager resourceBaseCacheManager;

    @Mock
    private CommonCacheService cacheService;

    @Resource
    private ConfigurationServiceInterface configurationServiceInterface = new ConfigurationNewServiceImpl();

    @Resource
    private ConfigurationRpc configurationRpc = new ConfigurationRpcFake();

    @Autowired
    public Map<String, PeakShiftCommonService> peakShiftTypeMap = new HashMap<>();

    @Resource
    private PeakShiftBCUAServiceImpl peakShiftBCUAService = new PeakShiftBCUAServiceImpl();

    @Resource
    private PeakShiftSNMPServiceImpl peakShiftSNMPService = new PeakShiftSNMPServiceImpl();

    @Resource
    private PeakShiftCSU5ServiceImpl peakShiftCSU5Service = new PeakShiftCSU5ServiceImpl();


    /* Started by AICoder, pid:3726e01f77c942448f18c5bc15e97e9a */
    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
        final ServiceBaseInfoBean serviceBaseInfoBean = new ServiceBaseInfoBean("da", "da");
        serviceBaseInfoBean.initOperlogBean(new OperlogBean());
        UserThreadLocal.put(serviceBaseInfoBean);
        peakShiftTypeMap.put("PEAK_SHIFT_CSU5", peakShiftCSU5Service);
        peakShiftTypeMap.put("PEAK_SHIFT_BCUA", peakShiftBCUAService);
        peakShiftTypeMap.put("PEAK_SHIFT_SNMP", peakShiftSNMPService);
    }

    @Test
    public void given_无_when_调用接口查询文件id_传递错误参数_then_返回错误码() {
        // 测试当传入null时，期望返回的错误码不为0
        ResponseBean responseBean = templateStrategyInnerController.getFileId(null);
        Assert.assertNotEquals(responseBean.getCode().intValue(), 0);

        // 测试当传入空对象时，期望返回的错误码不为0
        ResponseBean responseBean1 = templateStrategyInnerController.getFileId(new QueryFileIdRpcDto());
        Assert.assertNotEquals(responseBean1.getCode().intValue(), 0);
    }

    @Test
    public void given_策略关联表无数据_when_调用接口查询文件id_then_返回空() {
        // 构造一个带有正确参数的QueryFileIdRpcDto对象
        QueryFileIdRpcDto dto = new QueryFileIdRpcDto();
        dto.setTemplateStrategyId("id");
        dto.setVersion("v1");
        // 调用被测试的方法
        ResponseBean responseBean = templateStrategyInnerController.getFileId(dto);
        // 断言返回的数据为空
//        Assert.assertNull(responseBean.getData());
    }
    /* Ended by AICoder, pid:3726e01f77c942448f18c5bc15e97e9a */

}