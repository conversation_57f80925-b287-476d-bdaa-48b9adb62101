package com.zte.uedm.battery.a_interfaces.peakshift.inner;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.battery.a_domain.aggregate.peakshift.repository.PeakShiftMonitorRepository;
import com.zte.uedm.battery.a_infrastructure.repository.peakshift.persistence.PeakShiftMonitorRepositoryImpl;
import com.zte.uedm.battery.a_interfaces.peakshift.web.dto.QueryDeviceInfoDto;
import com.zte.uedm.battery.mapper.PeakShiftMonitorMapperFake;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/* Started by AICoder, pid:u05be72de82b76c140d40ba4d0d7bb36d1079e51 */
public class PeakShiftGrainInnerControllerFTest {

    @InjectMocks
    private PeakShiftGrainInnerController peakShiftGrainInnerController;

    @Mock
    private HttpServletRequest request;

    @Resource
    private PeakShiftMonitorMapperFake peakShiftMonitorMapper = new PeakShiftMonitorMapperFake();

    @Resource
    private PeakShiftMonitorRepository monitorRepository = new PeakShiftMonitorRepositoryImpl();

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:n7942xaa39q9f2714aea0ae7a0a74959d93682da */
    // @Test
    public void queryByDeviceDay() {
        QueryDeviceInfoDto queryDeviceInfoDto = new QueryDeviceInfoDto();
        queryDeviceInfoDto.setDeviceIds(Arrays.asList("MonitorDeviceCsu-ggjfbo"));
        queryDeviceInfoDto.setBeginTime("2024-06-01");
        queryDeviceInfoDto.setEndTime("2024-06-30");
        ResponseBean responseBean = peakShiftGrainInnerController.queryByDeviceDay(queryDeviceInfoDto, request, "zh-CN");
        Assert.assertEquals(responseBean.getCode().intValue(), 0);
    }

    // @Test
    public void deviceInfo_null() {
        ResponseBean responseBean = peakShiftGrainInnerController.queryByDeviceDay(null, request, "zh-CN");
        Assert.assertEquals(responseBean.getCode().intValue(), -301);
    }
    /* Ended by AICoder, pid:n7942xaa39q9f2714aea0ae7a0a74959d93682da */
}
/* Ended by AICoder, pid:u05be72de82b76c140d40ba4d0d7bb36d1079e51 */