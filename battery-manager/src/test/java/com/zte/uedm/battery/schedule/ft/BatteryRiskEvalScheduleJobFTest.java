package com.zte.uedm.battery.schedule.ft;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.mapper.BattRiskRuleMapperFake;
import com.zte.uedm.battery.mapper.BattRiskRuleSourceMapperFake;
import com.zte.uedm.battery.opti.application.scheduler.impl.BattRiskCalculateSchedulerImpl;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskEvalRepository;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleRepository;
import com.zte.uedm.battery.opti.domain.aggregate.repository.BattRiskRuleSourceRepository;
import com.zte.uedm.battery.opti.domain.service.BattInnerRiskEvalService;
import com.zte.uedm.battery.opti.domain.service.BattRiskCalculateDataProvider;
import com.zte.uedm.battery.opti.domain.service.BattRiskClassifyService;
import com.zte.uedm.battery.opti.domain.service.bean.enums.OutSourceBattTypeEnum;
import com.zte.uedm.battery.opti.domain.service.impl.BattRiskCalculateAlarmDataProvider;
import com.zte.uedm.battery.opti.domain.service.impl.BattRiskCalculatePointDataProvider;
import com.zte.uedm.battery.opti.domain.service.impl.inner.risk.BattAvgChargeExitRiskEvalImpl;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskRuleMapper;
import com.zte.uedm.battery.opti.infrastructure.repository.mapper.BattRiskRuleSourceMapper;
import com.zte.uedm.battery.opti.infrastructure.repository.persistence.BattRiskEvalRepositoryImpl;
import com.zte.uedm.battery.opti.infrastructure.repository.persistence.BattRiskRuleRepositoryImpl;
import com.zte.uedm.battery.opti.infrastructure.repository.persistence.BattRiskRuleSourceRepositoryImpl;
import com.zte.uedm.battery.rpc.ActiveAlarmServiceRpc;
import com.zte.uedm.battery.rpc.ActiveAlarmServiceRpcFake;
import com.zte.uedm.battery.schedule.BatteryRiskEvalScheduleJob;
import com.zte.uedm.common.consts.standardpoint.BattStandardPointConstants;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.*;

import static com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional.SOURCE_TYPE_ALARM;
import static com.zte.uedm.battery.opti.infrastructure.enums.battrisk.BattRiskRuleSourceTypeOptional.SOURCE_TYPE_STD_POINT;
import static org.powermock.api.mockito.PowerMockito.when;

public class BatteryRiskEvalScheduleJobFTest {

    @InjectMocks
    private BatteryRiskEvalScheduleJob batteryRiskEvalScheduleJob;


    @Mock
    private RedisService redisService;

    @Mock
    private CommonCacheService cacheService;
    @Mock
    private BattRiskClassifyService battRiskClassifyService;



    @Resource
    private Map<String, BattInnerRiskEvalService> battInnerRiskEvalServiceMap=new HashMap<>();

    @Resource
    private BattRiskRuleMapper battRiskRuleMapper = new BattRiskRuleMapperFake();
    @Resource
    private ActiveAlarmServiceRpc activeAlarmServiceRpc = new ActiveAlarmServiceRpcFake();


    @Resource
    private BattRiskRuleSourceMapper battRiskRuleSourceMapper = new BattRiskRuleSourceMapperFake();

    @Resource
    private BattRiskRuleRepository battRiskRuleRepository = new BattRiskRuleRepositoryImpl();

    @Resource
    private BattRiskRuleSourceRepository battRiskRuleSourceRepository = new BattRiskRuleSourceRepositoryImpl();

    @Resource
    private BattRiskEvalRepository battRiskEvalRepository = new BattRiskEvalRepositoryImpl();

    @Resource
    private BattAvgChargeExitRiskEvalImpl battAvgChargeExitRiskEvalImpl = new BattAvgChargeExitRiskEvalImpl();

    @Resource
    private BattRiskCalculateSchedulerImpl battRiskCalculateScheduler = new BattRiskCalculateSchedulerImpl();
    @Resource
    private BattRiskCalculateDataProvider battRiskCalculateDataProvider = new BattRiskCalculateAlarmDataProvider();
    @Mock
    private com.zte.uedm.component.redis.service.RedisService redissonService;

    @Before
    public void setUp() throws Exception {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void 电池缓存存在_资产属性开启_存在电池类型分类_存在风险规则_存在风险规则参数() throws Exception {
        Map<String, Map<String, Object>> batteryStdRealData = new HashMap<>();
        batteryStdRealData.put("batt1", new HashMap<>());
        Map<String, String> valueMap1 = new HashMap<>();
        valueMap1.put("value", OutSourceBattTypeEnum.FB_100_C_1.getId());
        Map<String, Object> stdMap1 = new HashMap<>();
        stdMap1.put(BattStandardPointConstants.BATT_SMPID_MODEL, valueMap1);
        batteryStdRealData.put("batt2", stdMap1);
        Map<String, String> valueMap2 = new HashMap<>();
        valueMap2.put("value", "");
        Map<String, Object> stdMap2 = new HashMap<>();
        stdMap2.put(BattStandardPointConstants.BATT_SMPID_MODEL, valueMap2);
        batteryStdRealData.put("batt3", stdMap2);
        when(redisService.batchGetMoPointData(Mockito.anyList(), Mockito.anyList())).thenReturn(batteryStdRealData);

        Map<String, BattInnerRiskEvalService> battInnerRiskEvalServiceMap = new HashMap<>();
        battInnerRiskEvalServiceMap.put("rule032", battAvgChargeExitRiskEvalImpl);
        battRiskCalculateScheduler.setBattInnerRiskEvalServiceMap(battInnerRiskEvalServiceMap);

        Map<String, BattRiskCalculateDataProvider> hs = new HashMap<>();
        hs.put(SOURCE_TYPE_STD_POINT.getId(), battRiskCalculateDataProvider);
        battRiskCalculateScheduler.setHs(hs);
        batteryRiskEvalScheduleJob.execute();
    }
    @Test
    public void UEDM_310552_given_电池id_风险规则参数_计算实时与时间范围告警频次_满足判断条件_when_风险评估任务开始_then_存在风险评估结果() throws UedmException {
        //
        //given
        //
        List<String> battIds = new ArrayList<>();
        battIds.add("battId");
        Map<String, List<String>> battClassificationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("battId");
        battClassificationMap.put("liCommonRiskRule",list);
        Mockito.when(battRiskClassifyService.battClassifyByRisk(battIds)).thenReturn(battClassificationMap);
        Map<String, BattRiskCalculateDataProvider> hs = new HashMap<>();
        hs.put(SOURCE_TYPE_ALARM.getId(), battRiskCalculateDataProvider);
        battRiskCalculateScheduler.setHs(hs);
        List<String> parameterIds = new ArrayList<>();


        //when
        batteryRiskEvalScheduleJob.execute();
        //then
        //

    }
    @Test
    public void UEDM_310550_given_电池id_风险规则参数_计算实时与时间范围告警频次_不满足判断条件_when_风险评估任务开始_then_不存在风险评估结果() throws UedmException {
        //
        //given
        //
        List<String> battIds = new ArrayList<>();
        battIds.add("battId");
        Map<String, List<String>> battClassificationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("battId");
        battClassificationMap.put("liCommonRiskRule",list);
        Mockito.when(battRiskClassifyService.battClassifyByRisk(battIds)).thenReturn(battClassificationMap);
        Map<String, BattRiskCalculateDataProvider> hs = new HashMap<>();
        hs.put(SOURCE_TYPE_ALARM.getId(), battRiskCalculateDataProvider);
        battRiskCalculateScheduler.setHs(hs);
        //when
        batteryRiskEvalScheduleJob.execute();
        //then
        //


    }

    @Test
    public void UEDM_310554_given_电池id_风险规则参数_计算实时告警持续时间_不满足判断条件_when_风险评估任务开始_then_不存在风险评估结果() throws UedmException {
        //
        //given
        //
        List<String> battIds = new ArrayList<>();
        battIds.add("battId");
        Map<String, List<String>> battClassificationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("battId");
        battClassificationMap.put("liCommonRiskRule",list);
        Mockito.when(battRiskClassifyService.battClassifyByRisk(battIds)).thenReturn(battClassificationMap);
        Map<String, BattRiskCalculateDataProvider> hs = new HashMap<>();
        hs.put(SOURCE_TYPE_ALARM.getId(), battRiskCalculateDataProvider);
        battRiskCalculateScheduler.setHs(hs);
        //when
        batteryRiskEvalScheduleJob.execute();
        //then
        //


    }

    @Test
    public void UEDM_310556_given_电池id_风险规则参数_计算实时告警持续时间_满足判断条件_when_风险评估任务开始_then_存在风险评估结果() throws UedmException {
        //
        //given
        //
        List<String> battIds = new ArrayList<>();
        battIds.add("battId");
        Map<String, List<String>> battClassificationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("battId");
        battClassificationMap.put("liCommonRiskRule",list);
        Mockito.when(battRiskClassifyService.battClassifyByRisk(battIds)).thenReturn(battClassificationMap);
        Map<String, BattRiskCalculateDataProvider> hs = new HashMap<>();
        hs.put(SOURCE_TYPE_ALARM.getId(), battRiskCalculateDataProvider);
        battRiskCalculateScheduler.setHs(hs);
        //when
        batteryRiskEvalScheduleJob.execute();
        //then
        //


    }

}
