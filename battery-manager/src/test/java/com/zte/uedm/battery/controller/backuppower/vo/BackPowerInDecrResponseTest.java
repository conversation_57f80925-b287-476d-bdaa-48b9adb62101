package com.zte.uedm.battery.controller.backuppower.vo;

import com.zte.uedm.battery.controller.backuppower.dto.BackPowerInDecreaseResponStatDto;
import org.junit.Assert;
import org.junit.Test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class BackPowerInDecrResponseTest {
    @Test
    public void test() throws ParseException {
        String evlTime = "2022-07-11 15:00:00";
        String preEvlTime = "2022-07-10 15:00:00";
        BackPowerInDecrResponse response = new BackPowerInDecrResponse();
        List<BackPowerInDecreaseResponStatDto> statistics = new ArrayList<>();
        BackPowerInDecreaseResponStatDto statistic = new BackPowerInDecreaseResponStatDto();
        statistic.setId("normal");
        statistic.setName("正常");
        statistic.setTotalNumber(1);
        statistic.setInNumber(1);
        statistic.setDeNumber(1);
        statistics.add(statistic);
        response.setId("BattPack-01");
        response.setPosition("BattPack/BattPack-01");
        response.setStatistics(statistics);
        response.setEvlTime(evlTime);
        response.setPreEvalTime(preEvlTime);

        Assert.assertEquals("BattPack-01",response.getId());
        Assert.assertEquals("BattPack/BattPack-01",response.getPosition());
        Assert.assertEquals("normal",response.getStatistics().get(0).getId());
        Assert.assertNotNull(response.getEvlTime());
        Assert.assertNotNull(response.getPreEvalTime());
        Assert.assertNotNull(response.toString());
    }

}