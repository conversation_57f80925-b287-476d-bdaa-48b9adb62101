package com.zte.uedm.battery.bean;

import com.zte.uedm.battery.controller.backuppower.vo.DurationVo;
import org.junit.Assert;
import org.junit.Test;

public class BattBackupPowerEvalTrendPojoTest
{
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(BattBackupPowerEvalTrendPojo.class);
        BattBackupPowerEvalTrendPojo bean = new BattBackupPowerEvalTrendPojo();
        bean.setId("1");
        bean.setName("1");
        bean.setStatus("1");
        bean.setPreStatus("1");
        DurationVo durationVo = new DurationVo(1.2);
        durationVo.setUnit("1");
        bean.setStatusDetail("1");
        bean.setPreStatusDetail("1");
        bean.setThresholdDuration(1.2);
        bean.setBackupPowerDuration(1.2);
        //bean.setEvalTime(new Date());
        bean.setBrand("1");
        bean.setModel(null);
        bean.setRatedCapacity("1");
        bean.setAverageDischargeCurrent("1");
        bean.setFullChargeCapacity("1");
        bean.setTotalLoadPower("1");

        bean.getApplicationScene();
        bean.getBackupPowerDuration();
        bean.getBrand();
        bean.getEvalTime();
        bean.getCreator();
        bean.getManufacture();
        bean.getGmtCreate();
        bean.getModel();
        bean.getName();
        bean.getSeries();
        bean.toString();
        bean.getRatedCapacity();
        bean.getAverageDischargeCurrent();
        bean.getFullChargeCapacity();
        bean.getTotalLoadPower();
        Assert.assertEquals("1", bean.getId());
    }
}
