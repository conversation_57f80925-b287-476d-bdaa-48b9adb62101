package com.zte.uedm.battery.service.impl;

import com.zte.uedm.battery.bean.BatteryEvalDTO;
import com.zte.uedm.battery.bean.pojo.BattHealthStatusEvalPo;
import com.zte.uedm.battery.bean.pv.HistoryDataResponseConditionBean;
import com.zte.uedm.battery.domain.BattHealthStatusEvalDomain;
import com.zte.uedm.battery.domain.BattOverviewDomain;
import com.zte.uedm.battery.domain.LeadAcidChargeEvalDomain;
import com.zte.uedm.battery.domain.LeadAcidDischargeEvalDomain;
import com.zte.uedm.battery.enums.battlife.BatteryHealthEvalEnums;
import com.zte.uedm.battery.util.BatteryHealthyBeanUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.service.config.optional.StandPointOptional;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

public class BattLeadAcidHealthStatusEvaluateServiceImplTest
{
    @InjectMocks
    private BattLeadAcidHealthStatusEvaluateServiceImpl battLeadAcidHealthStatusEvaluateService;
    @Mock
    private BattHealthStatusEvalDomain battHealthStatusEvalDomain;
    @Mock
    private LeadAcidChargeEvalDomain leadAcidChargeEvalDomain;
    @Mock
    private LeadAcidDischargeEvalDomain leadAcidDischargeEvalDomain;

    @Mock
    private BatteryHealthyBeanUtils batteryHealthyBeanUtils;

    @Mock
    private BattOverviewDomain battOverviewDomain;

    @Mock
    private JsonService jsonService;

    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void battHealthStatusEvalTest() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        doReturn(1).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "",idPrestateMap,nameMap,dto, Pair.of(false, new HashMap<>()));
        battHealthStatusEvalPo.setPreStatusId("2");
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "",idPrestateMap,nameMap,dto, Pair.of(false, new HashMap<>()));
        Assert.assertEquals("unEvaluate",battHealthStatusEvalPo.getStatusId());
    }
    /* Started by AICoder, pid:ic198x5866h00541455c0944c02a1138ce5178b4 */
    @Test
    public void getSohValueTest(){
        Map<String, List<HistoryDataResponseConditionBean>> hisDataResponseMap = new HashMap<>();
        List<HistoryDataResponseConditionBean> list = new ArrayList<>();
        HistoryDataResponseConditionBean historyDataResponseConditionBean = new HistoryDataResponseConditionBean();
        historyDataResponseConditionBean.setMoId("id");
        historyDataResponseConditionBean.setCurrentValue("10");
        list.add(historyDataResponseConditionBean);
        hisDataResponseMap.put(StandPointOptional.BATTERY_SMPID_HEALTH.getId(),list);

        String moId = "id";
        Double value = battLeadAcidHealthStatusEvaluateService.getSohValue(hisDataResponseMap,moId);
        Double value1 = Double.valueOf("10");
        Assert.assertEquals(value1,value);
    }
    /* Ended by AICoder, pid:ic198x5866h00541455c0944c02a1138ce5178b4 */


    @Test
    public void battHealthStatusEvalTestExc() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        doThrow(new UedmException(-1,"1")).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "",idPrestateMap,nameMap,dto,Pair.of(false, new HashMap<>()));
        Assert.assertEquals("unEvaluate",battHealthStatusEvalPo.getStatusId());
    }

    @Test
    public void battHealthStatusEvalTest1() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        Map<String, String> map = new HashMap<>();
        map.put("zh_CN","123");
        map.put("en_US","1112");
        nameMap.put("batt.batt_charge.final.soc",map);
        nameMap.put("batt.rated.capacity",map);
        doReturn(1).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        doReturn(false).when(leadAcidChargeEvalDomain).leadAcidChargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        doReturn(false).when(leadAcidDischargeEvalDomain).leadAcidDischargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "aa",idPrestateMap,nameMap,dto,Pair.of(false, new HashMap<>()));
        Assert.assertEquals("exception",battHealthStatusEvalPo.getStatusId());
    }

    @Test
    public void battHealthStatusEvalTest12() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        Map<String, String> map = new HashMap<>();
        map.put("zh_CN","123");
        map.put("en_US","1112");
        nameMap.put("batt.batt_charge.final.soc",map);
        nameMap.put("batt.rated.capacity",map);
        doReturn(1).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        doReturn(true).when(leadAcidChargeEvalDomain).leadAcidChargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        doReturn(true).when(leadAcidDischargeEvalDomain).leadAcidDischargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "aa",idPrestateMap,nameMap,dto,Pair.of(false, new HashMap<>()));
        Assert.assertEquals("healthy",battHealthStatusEvalPo.getStatusId());
    }

    @Test
    public void battHealthStatusEvalTest2() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        doReturn(1).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        doReturn(null).when(leadAcidChargeEvalDomain).leadAcidChargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        doReturn(null).when(leadAcidDischargeEvalDomain).leadAcidDischargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "aa",idPrestateMap,nameMap,dto,Pair.of(false, new HashMap<>()));
        Assert.assertEquals("unEvaluate",battHealthStatusEvalPo.getStatusId());
    }

    @Test
    public void battHealthStatusEvalTest3() throws UedmException, ParseException {
        BattHealthStatusEvalPo battHealthStatusEvalPo = new BattHealthStatusEvalPo();
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        BatteryEvalDTO dto = new BatteryEvalDTO();
        dto.setEnums(BatteryHealthEvalEnums.PBAC_EXCEPTION_CHARGE_THIRTY_DAYS);
        dto.setDate("2023-12-12 12:12:12");
        Map<String, String> map = new HashMap<>();
        map.put("zh_CN","123");
        map.put("en_US","1112");
        nameMap.put("qwe",map);
        dto.setNameMap(map);
        battHealthStatusEvalPo.setStatusId("1");
        battHealthStatusEvalPo.setPreStatusId("unEvaluate");
        doReturn(1).when(battHealthStatusEvalDomain).addHealthStatusEvalResult(Mockito.any());
        doReturn(false).when(leadAcidChargeEvalDomain).leadAcidChargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        doReturn(true).when(leadAcidDischargeEvalDomain).leadAcidDischargeEval(Mockito.any(),Mockito.any(),Mockito.any());
        Map<String, BattHealthStatusEvalPo> idPrestateMap=new HashMap<>();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        idPrestateMap.put("1",preBean);
        idPrestateMap.put("",preBean);
        battLeadAcidHealthStatusEvaluateService.battHealthStatusEval(battHealthStatusEvalPo,false,new HashMap<>(), "aa",idPrestateMap,nameMap,dto,Pair.of(false, new HashMap<>()));
        Assert.assertEquals("exception",battHealthStatusEvalPo.getStatusId());
    }

    @Test
    public void buildPreStatusId() throws UedmException
    {
        BattHealthStatusEvalPo currBean = new BattHealthStatusEvalPo();
        BattHealthStatusEvalPo preBean = new BattHealthStatusEvalPo();
        currBean.setStatusId("1");
        preBean.setStatusId("1");
        preBean.setPreStatusId("unEvaluate");
        battLeadAcidHealthStatusEvaluateService.buildPreStatusId(currBean,preBean);
        currBean.setStatusId("1");
        preBean.setStatusId("2");
        BattHealthStatusEvalPo battHealthStatusEvalPo = battLeadAcidHealthStatusEvaluateService.buildPreStatusId(currBean, preBean);
        Assert.assertEquals("1",battHealthStatusEvalPo.getStatusId());
    }
}
