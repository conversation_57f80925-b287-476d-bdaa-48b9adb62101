package com.zte.uedm.battery.bean;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件名称：PojoTestUtil.java 文件描述：工具类，用于pojo类中setget方法测试(setget方法命名需符合标准生成规则) 文件作者：张杰10263046
 */
public class PojoTestUtil
{
    private static byte BYTE_BASIC_DADA = 0;
    private static short SHORT_BASIC_DATA = 0;
    private static int INT_BASIC_DATA = 0;
    private static long LONG_BASIC_DATA = 0;
    private static float FLOAT_BASIC_DATA = 1;
    private static double DOUBLE_BASIC_DATA = 1.0;
    private static char CHAR_BASIC_DATA = 'c';
    private static boolean BOOLEAN_BASIC_DATA = true;

    public static void TestForPojo(Class<?> testClass) throws Exception
    {
    	HashcodeEqualsTestUtil.hashcodeAndEqualsTest(testClass);
        Field[] fields = testClass.getDeclaredFields();
        Method[] methods = testClass.getDeclaredMethods();
        List<Method> getMethods = new ArrayList<>();
        List<Method> setMethods = new ArrayList<>();
        for (Field field : fields)
        {
            Method getMethod = getGetMethodByFeildName(field.getName(), methods);
            if (null != getMethod)
            {
                getMethods.add(getMethod);
            }
            Method setMethod = getSetMethodByFeildName(field.getName(), methods);
            if (null != setMethod)
            {
                setMethods.add(setMethod);
            }
        }

        Object testObject = testClass.newInstance();
        excuteGetMethods(getMethods, testObject);
        excuteSetMethods(setMethods, testObject);
    }

    private static void excuteGetMethods(List<Method> getMethods, Object testObject) throws Exception
    {
        for (Method method : getMethods)
        {
            method.invoke(testObject);
        }
    }

    private static void excuteSetMethods(List<Method> setMethods, Object testObject) throws Exception
    {
        for (Method method : setMethods)
        {

            if (!delWithBasicdataSetFunc(method, testObject))
            {
                method.invoke(testObject, new Object[] { null });
            }
        }
    }

    private static boolean delWithBasicdataSetFunc(Method method, Object testObject) throws Exception
    {
        Class<?>[] parameterTypes = method.getParameterTypes();
        if (null != parameterTypes && parameterTypes.length > 0)
        {
            String paramTypeName = parameterTypes[0].getName();
            if (isBasicType1(paramTypeName, method, testObject))
            {
                return true;
            }
            else if (isBasicType2(paramTypeName, method, testObject))
            {
                return true;
            }
        }
        return false;
    }

    private static boolean isBasicType1(String paramTypeName, Method method, Object testObject) throws Exception
    {
        if ("float".equals(paramTypeName))
        {
            method.invoke(testObject, FLOAT_BASIC_DATA);
            return true;
        }
        else if ("double".equals(paramTypeName))
        {
            method.invoke(testObject, DOUBLE_BASIC_DATA);
            return true;
        }
        else if ("char".equals(paramTypeName))
        {
            method.invoke(testObject, CHAR_BASIC_DATA);
            return true;
        }
        else if ("boolean".equals(paramTypeName))
        {
            method.invoke(testObject, BOOLEAN_BASIC_DATA);
            return true;
        }
        return false;
    }

    private static boolean isBasicType2(String paramTypeName, Method method, Object testObject) throws Exception
    {
        if ("byte".equals(paramTypeName))
        {
            method.invoke(testObject, BYTE_BASIC_DADA);
            return true;
        }
        else if ("short".equals(paramTypeName))
        {
            method.invoke(testObject, SHORT_BASIC_DATA);
            return true;
        }
        else if ("int".equals(paramTypeName))
        {
            method.invoke(testObject, INT_BASIC_DATA);
            return true;
        }
        else if ("long".equals(paramTypeName))
        {
            method.invoke(testObject, LONG_BASIC_DATA);
            return true;
        }
        return false;
    }

    private static Method getGetMethodByFeildName(String feildName, Method[] methods) throws Exception
    {
        String getMethodName = getGetFuncName(feildName);
        if (StringUtils.isBlank(getMethodName))
        {
            return null;
        }
        for (Method method : methods)
        {
            if (method.getName().equals(getMethodName))
            {
                return method;
            }
        }
        return null;
    }

    private static Method getSetMethodByFeildName(String feildName, Method[] methods)
    {
        String setMethodName = getSetFuncName(feildName);
        if (StringUtils.isBlank(setMethodName))
        {
            return null;
        }
        for (Method method : methods)
        {
            if (method.getName().equals(setMethodName))
            {
                return method;
            }
        }
        return null;
    }

    private static String getGetFuncName(String feildName)
    {
        if (StringUtils.isBlank(feildName))
        {
            return null;
        }
        int startIndex = 0;
        return "get" + feildName.substring(startIndex, startIndex + 1).toUpperCase() + feildName.substring(startIndex
                + 1, feildName.length());
    }

    private static String getSetFuncName(String feildName)
    {
        if (StringUtils.isBlank(feildName))
        {
            return null;
        }
        int startIndex = 0;
        return "set" + feildName.substring(startIndex, startIndex + 1).toUpperCase() + feildName.substring(startIndex
                + 1, feildName.length());
    }
}
