package com.zte.uedm.battery.controller.backuppowerconfig.enums;

import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class PictureInfoEnumsTest
{
    @Test
    public void test()
    {
        List<String> allIds = PictureInfoEnums.getAllIds();
        Assert.assertEquals(8,allIds.size());

        String nameById = PictureInfoEnums.getNameById("distribution");
        Assert.assertEquals(nameById,"{\"en_US\":\"Distribution\",\"zh_CN\":\"分布\"}");

        String id = PictureInfoEnums.DISTRIBUTION.getId();
        Assert.assertEquals(id,"distribution");
        String name = PictureInfoEnums.DISTRIBUTION.getName();
        Assert.assertEquals(name,"{\"en_US\":\"Distribution\",\"zh_CN\":\"分布\"}");

        String asset = PictureInfoEnums.getNameById("asset");
        Assert.assertEquals(asset,"{\"en_US\":\"Asset\",\"zh_CN\":\"资产\"}");

        String HistoryTrend = PictureInfoEnums.getNameById("History_Trend");
        Assert.assertEquals(HistoryTrend,"{\"en_US\":\"History Trend\",\"zh_CN\":\"历史趋势\"}");
    }
}
