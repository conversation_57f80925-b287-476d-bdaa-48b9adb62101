package com.zte.uedm.battery.bean;

import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class BattLifeEvalDetailBeanTest {

    @Test
    public void testEvalDetail() {
        BattLifeEvalDetailBean bean = new BattLifeEvalDetailBean();

        String evalRule = "This is an evaluation rule";
        bean.setEvalRule(evalRule);

        List<String> evalDetail = Arrays.asList("Detail 1", "Detail 2");
        bean.setEvalDetail(evalDetail);

        bean.setEvalRule("Rule");
        bean.setEvalDetail(Collections.singletonList("Detail"));
        String expected = "BattLifeEvalDetailBean(evalRule=Rule, evalDetail=[Detail])";

        bean.setEvalRule(null);

        bean.setEvalDetail(null);

        String evalRule1 = bean.getEvalRule();
        List<String> evalDetail1 = bean.getEvalDetail();

        assertNotNull(bean.toString());
    }
}