package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.service.BattPeriodTestService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.doThrow;

public class BattPeriodTestSchduleJobTest
{
    @InjectMocks
    private BattPeriodTestSchduleJob battPeriodTestSchduleJob;
    @Mock
    private BattPeriodTestService battPeriodTestService;
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void BattPeriodTestJobTest() throws Exception
    {
        try
        {
            battPeriodTestSchduleJob.execute();
            doThrow(new UedmException(-1, "test")).when(battPeriodTestService).battPeriodTest("auto");
            battPeriodTestSchduleJob.execute();
        }
        catch (UedmException e)
        {
            Assert.assertEquals("", e.getMessage());
        }

    }
}
