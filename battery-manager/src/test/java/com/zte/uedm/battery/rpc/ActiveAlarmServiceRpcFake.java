package com.zte.uedm.battery.rpc;

import com.zte.uedm.battery.bean.alarm.ActiveAlarmBody;
import com.zte.uedm.battery.bean.alarm.Alarm;
import com.zte.uedm.battery.bean.alarm.AlarmResponse;
import com.zte.uedm.battery.util.FakeBranchFlag;
import lombok.SneakyThrows;
import org.mockito.Mockito;
import retrofit2.Call;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

public class ActiveAlarmServiceRpcFake implements ActiveAlarmServiceRpc{

    public static String GET_ACTIVE_ALARM = FakeBranchFlag.DATA_NULL;
    @SneakyThrows
    @Override
    public Call<AlarmResponse> getActiveAlarm(ActiveAlarmBody alarmBody, String languageOption, String username) {
        /* Started by AICoder, pid:8510815cdbeb42fd87cdc774170cf5a5 */
        AlarmResponse alarmResponse = new AlarmResponse();
        List<Alarm> alarms = new ArrayList<>();
        Alarm alarm1 = new Alarm();
        alarm1.setMoc("moc1");
        alarm1.setMe("moc1");
        alarm1.setAlarmsource("alarmsource1");
        alarm1.setAlarmraisedtime(1710313606859L);
        Alarm alarm2 = new Alarm();
        alarm2.setMoc("moc2");
        alarm2.setMe("moc2");
        alarm2.setAlarmsource("alarmsource2");
        alarm2.setAlarmcode(50031005l);
        alarm2.setAlarmraisedtime(1710313606859L);
        alarms.add(alarm1);
        alarms.add(alarm2);

        alarmResponse.setAlarms(alarms);
        alarmResponse.setTotalcount(2);
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        ids.add(2L);
        alarmResponse.setIds(ids);
        alarmResponse.setTimestamp("2021-08-01 12:00:00");
        alarmResponse.setCurrentpage(1);
        /* Ended by AICoder, pid:8510815cdbeb42fd87cdc774170cf5a5 */
        Call executable = Mockito.mock(Call.class);
        Mockito.when(executable.execute()).thenReturn(Response.success(alarmResponse));
        return executable;
    }

    @SneakyThrows
    @Override
    public Call<AlarmResponse> getActiveAlarmWithLang(ActiveAlarmBody alarmBody, String languageOption) {
        /* Started by AICoder, pid:8510815cdbeb42fd87cdc774170cf5a5 */
        if(FakeBranchFlag.DATA_NORMAL.equals(GET_ACTIVE_ALARM)){
            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm1 = new Alarm();
            alarm1.setMoc("moc1");
            alarm1.setMe("moc1");
            alarm1.setAlarmsource("alarmsource1");
            alarm1.setAckstate(1);
            alarm1.setAlarmraisedtime(1710313606859L);
            Alarm alarm2 = new Alarm();
            alarm2.setMoc("moc2");
            alarm2.setMe("moc2");
            alarm2.setAckstate(1);
            alarm2.setAlarmsource("alarmsource2");
            alarm2.setAlarmraisedtime(1710313606859L);
            alarms.add(alarm1);
            alarms.add(alarm2);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(2);
            List<Long> ids = new ArrayList<>();
            ids.add(1L);
            ids.add(2L);
            alarmResponse.setIds(ids);
            alarmResponse.setTimestamp("2021-08-01 12:00:00");
            alarmResponse.setCurrentpage(1);
            /* Ended by AICoder, pid:8510815cdbeb42fd87cdc774170cf5a5 */
            Call executable = Mockito.mock(Call.class);
            Mockito.when(executable.execute()).thenReturn(Response.success(alarmResponse));
            return executable;
        }else if(FakeBranchFlag.DATA_NULL.equals(GET_ACTIVE_ALARM)){
            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(2);
            List<Long> ids = new ArrayList<>();
            ids.add(1L);
            ids.add(2L);
            alarmResponse.setIds(ids);
            alarmResponse.setTimestamp("2021-08-01 12:00:00");
            alarmResponse.setCurrentpage(1);
            Call executable = Mockito.mock(Call.class);
            Mockito.when(executable.execute()).thenReturn(Response.success(alarmResponse));
            return executable;
        }else {
            AlarmResponse alarmResponse = new AlarmResponse();
            List<Alarm> alarms = new ArrayList<>();
            Alarm alarm1 = new Alarm();
            alarm1.setMoc("moc1");
            alarm1.setMe("moc1");
            alarm1.setAlarmsource("alarmsource1");
            alarm1.setAckstate(2);
            alarm1.setAlarmraisedtime(1710313606859L);
            Alarm alarm2 = new Alarm();
            alarm2.setMoc("moc2");
            alarm2.setMe("moc2");
            alarm2.setAckstate(2);
            alarm2.setAlarmsource("alarmsource2");
            alarm2.setAlarmraisedtime(1710313606859L);
            alarms.add(alarm1);
            alarms.add(alarm2);

            alarmResponse.setAlarms(alarms);
            alarmResponse.setTotalcount(2);
            List<Long> ids = new ArrayList<>();
            ids.add(1L);
            ids.add(2L);
            alarmResponse.setIds(ids);
            alarmResponse.setTimestamp("2021-08-01 12:00:00");
            alarmResponse.setCurrentpage(1);
            /* Ended by AICoder, pid:8510815cdbeb42fd87cdc774170cf5a5 */
            Call executable = Mockito.mock(Call.class);
            Mockito.when(executable.execute()).thenReturn(Response.success(alarmResponse));
            return executable;

        }

    }
}
