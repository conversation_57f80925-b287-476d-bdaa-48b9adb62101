package com.zte.uedm.battery.schedule;

import com.zte.uedm.battery.service.BatteryBackupPowerEvalService;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class BatteryBackupPowerEvalScheduleJobTest {

    @Mock
    private BatteryBackupPowerEvalService mockBatteryBackupPowerEval;

    @InjectMocks
    private BatteryBackupPowerEvalScheduleJob batteryBackupPowerEvalScheduleJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Run the test
        batteryBackupPowerEvalScheduleJobUnderTest.execute();

        // Verify the results
        verify(mockBatteryBackupPowerEval).batteryBackupPowerEvalEntrance();
    }

    @Test
    public void testExecute_BatteryBackupPowerEvalServiceThrowsUedmException() throws Exception {
        // Setup
        doThrow(UedmException.class).when(mockBatteryBackupPowerEval).batteryBackupPowerEvalEntrance();

        // Run the test
        batteryBackupPowerEvalScheduleJobUnderTest.execute();

        // Verify the results
    }
}
