package com.zte.uedm.battery.bean;

import static org.junit.Assert.*;

import org.junit.Test;

public class SwitchPowerViewBeanTest {

    @Test
    public void testSetGetToString()
    {
        SwitchPowerViewBean bean = new SwitchPowerViewBean();
        bean.setTotalCurr("1.2");
        bean.setOid("oid");
        bean.setSiteName("Site");
        bean.setLocation("location");
        bean.setSpName("sp");
        bean.setStandbyStatus("充足");
        bean.setBatteryStatus("充电");
        bean.setAlarmStatus(false);
        bean.setBatteryNum(1);
        assertNotNull(bean.toString());
        assertEquals("1.2",bean.getTotalCurr());
        assertEquals("Site",bean.getSiteName());
        assertEquals("oid",bean.getOid());
        
        assertEquals("location",bean.getLocation());
        assertEquals("sp",bean.getSpName());
        assertEquals("充足",bean.getStandbyStatus());
        assertEquals("充电",bean.getBatteryStatus());
        assertFalse(bean.getAlarmStatus());
        assertTrue(1==bean.getBatteryNum());
    }
}
