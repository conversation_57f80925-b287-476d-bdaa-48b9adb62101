package com.zte.uedm.battery.controller.pv;


import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.battery.a_domain.aggregate.device.model.entity.DeviceEntity;
import com.zte.uedm.battery.a_domain.cache.DeviceCacheMgr;
import com.zte.uedm.battery.a_infrastructure.cache.manager.DeviceCacheManager;
import com.zte.uedm.battery.api.service.ConfigurationService;
import com.zte.uedm.battery.bean.pv.PvCommonQueryRequestBean;
import com.zte.uedm.battery.bean.pv.PvTrendQueryRequestBean;
import com.zte.uedm.battery.dao.PvGenerConsumRecordDao;
import com.zte.uedm.battery.dao.impl.PvGenerConsumRecordDaoImpl;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapper;
import com.zte.uedm.battery.mapper.PvGenerConsumRecordMapperFake;
import com.zte.uedm.battery.rpc.ConfigurationRpc;
import com.zte.uedm.battery.rpc.ConfigurationRpcFake;
import com.zte.uedm.battery.rpc.impl.ConfigurationManagerRpcImpl;
import com.zte.uedm.battery.service.PvGenerConsumRecordService;
import com.zte.uedm.battery.service.PvPowerAnalysisService;
import com.zte.uedm.battery.service.impl.ConfigurationServiceImpl;
import com.zte.uedm.battery.service.impl.PvGenerConsumRecordServiceImpl;
import com.zte.uedm.battery.service.impl.PvPowerAnalysisServiceImpl;

import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.battery.util.PowerSupplySceneUtils;
import com.zte.uedm.battery.util.SiteLevelUtils;
import com.zte.uedm.battery.util.realGroupRelationSiteUtils.RealGroupRelationSiteUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.opt.monitorobject.entity.MonitorObjectEntity;
import com.zte.uedm.common.configuration.opt.real.group.site.entity.SiteEntity;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.component.caffeine.service.CommonCacheService;
import com.zte.uedm.redis.service.RedisService;
import org.ehcache.config.CacheConfiguration;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
public class PvPowerAnalysisControllerFTest {
    @InjectMocks
    private PvPowerAnalysisController pvPowerAnalysisController;

    @Resource
    private PvPowerAnalysisService pvPowerAnalysisService = new PvPowerAnalysisServiceImpl();


    @Resource
    private RealGroupRelationSiteUtils realGroupRelationSiteUtils = new RealGroupRelationSiteUtils();

    @Resource
    private PowerSupplySceneUtils powerSupplySceneUtils;

    @Resource
    private SiteLevelUtils siteLevelUtils;

    @Resource
    private JsonService jsonService = new JsonService();

    @Resource
    private I18nUtils i18nUtilsService = new I18nUtils();


    @Resource
    private RedisService redisService;


    @Resource
    private PvGenerConsumRecordService pvGenerConsumRecordService = new PvGenerConsumRecordServiceImpl();

    @Resource
    private ConfigurationService configurationService = new ConfigurationServiceImpl();

    @Resource
    private PvGenerConsumRecordDao pvGenerConsumRecordDao = new PvGenerConsumRecordDaoImpl();


    @Mock
    private CacheConfiguration cacheConfiguration;

    @Mock
    private CommonCacheService commonCacheService;

    @Mock
    private DeviceCacheManager deviceCacheManager;



    @Mock
    private CacheManager cacheManager;

    @Resource
    private ConfigurationManagerRpcImpl configurationManagerRpcImpl = new ConfigurationManagerRpcImpl();


    @Mock
    private ConfigurationRpc configurationRpc = new ConfigurationRpcFake();

    @Mock
    private PvGenerConsumRecordMapper pvGenerConsumRecordMapper = new PvGenerConsumRecordMapperFake();

    @Resource
    PvCommonQueryRequestBean  pvCommonQueryRequestBean = new PvCommonQueryRequestBean();

    @Mock
    private HttpServletRequest request ;

    @Before
    public void before() throws IllegalAccessException, ClassNotFoundException, InstantiationException, UedmException {
        FtMockitoAnnotations.initMocks(this);
        Set<String> set = new HashSet<>();
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        List<DeviceEntity> list = new ArrayList<>();
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId("id");
        String[] path = new String[]{"1","2","3"};
        deviceEntity.setPathId(path);
        list.add(deviceEntity);
        Mockito.when(deviceCacheManager.selectDeviceById(set)).thenReturn(list);

    }

    //pv总览
    @Test
    public void UEDM_282722_given_筛选条件为空_入参moc为太阳能pv_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }
    /* Started by AICoder, pid:o1aadp2d7c92cff140380ba7c03cde016e2995fd */
    @Test
    public void testCheckGrain(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setGrain("fd");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(), 0L);
    }

    /* Ended by AICoder, pid:o1aadp2d7c92cff140380ba7c03cde016e2995fd */

    @Test
    public void UEDM_282718_given_筛选条件为空_入参moc为太阳能pv_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282725_given_筛选条件为空_入参moc为太阳能pv_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282720_given_筛选条件为空_入参moc为太阳能pv_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0,(long)responseBean.getTotal());
    }

    @Test
    public void UEDM_282714_given_筛选条件非空_入参moc为太阳能pv_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282716_given_筛选条件非空_入参moc为太阳能pv_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282731_given_筛选条件非空_入参moc为太阳能pv_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282728_given_筛选条件非空_入参moc为太阳能pv_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求的用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pv");
        pvCommonQueryRequestBean.setOrder("desc");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setConsumptionEnd(11.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0,(long)responseBean.getTotal());
    }

    //spcu方阵总览

    @Test
    public void UEDM_282736_given_筛选条件为空_入参moc为方针spcu_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282727_given_筛选条件为空_入参moc为方针spcu_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282733_given_筛选条件为空_入参moc为方针spcu_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setName("spcuName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282737_given_筛选条件为空_入参moc为方针spcu_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求的用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0,(long)responseBean.getTotal());
    }

    @Test
    public void UEDM_282745_given_筛选条件非空_入参moc为方针spcu_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setName("spcuName");
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282747_given_筛选条件非空_入参moc为方针spcu_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282743_given_筛选条件非空_入参moc为方针spcu_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setName("spcuName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282741_given_筛选条件非空_入参moc为方针spcu_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求的用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("spcu");
        pvCommonQueryRequestBean.setOrder("desc");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setConsumptionEnd(11.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0, (long)responseBean.getTotal());
    }

    //spu板级总览
    @Test
    public void UEDM_282755_given_筛选条件为空_入参moc为板级spu_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282749_given_筛选条件为空_入参moc为板级spu_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282739_given_筛选条件为空_入参moc为板级spu_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setName("pcuName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282757_given_筛选条件为空_入参moc为板级spu_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求的用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0, (long)responseBean.getTotal());
    }

    @Test
    public void UEDM_282759_given_筛选条件非空_入参moc为板级spu_查询rpc监控对象返回空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setName("pcuName");
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282753_given_筛选条件非空_入参moc为板级spu_查询rpc监控对象返回非空_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282751_given_筛选条件非空_入参moc为板级spu_查询rpc监控对象返回非空_缓存中关联测点信息不存在_when_调用发用电量概览查询接口_then_查询接口返回空集合(){
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setName("pcuName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setGenerationBegin(9.0);
        pvCommonQueryRequestBean.setConsumptionEnd(1.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),0);
    }

    @Test
    public void UEDM_282761_given_筛选条件非空_入参moc为板级spu_查询rpc监控对象返回非空_缓存中关联测点信息存在_when_调用发用电量概览查询接口_then_查询接口返回符合要求的用电量分析数据集合() throws UedmException {
        List<String> pvTypes = new ArrayList<>();
        pvTypes.add("pcu");
        pvCommonQueryRequestBean.setOrder("desc");
        pvCommonQueryRequestBean.setName("pvName");
        pvCommonQueryRequestBean.setPvTypes(pvTypes);
        pvCommonQueryRequestBean.setPositions(new ArrayList<>());
        pvCommonQueryRequestBean.setConsumptionBegin(10.0);
        pvCommonQueryRequestBean.setConsumptionEnd(11.0);
        pvCommonQueryRequestBean.setRatioBegin(0.7);
        pvCommonQueryRequestBean.setGrain("月");
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisOverview(pvCommonQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals(0, (long)responseBean.getTotal());
    }


    //电量趋势分析
    @Test
    public void UEDM_282774_given_入参moc为太阳能pv_日月粒度_同比年_when_调用发用电量趋势分析接口_then_对比数据趋势分析输出() throws UedmException {
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
        pvTrendQueryRequestBean.setStartTime("2023-12-30 00:00:00");
        pvTrendQueryRequestBean.setEndTime("2024-03-30 00:00:00");
        pvTrendQueryRequestBean.setCompareYear("2023");
        pvTrendQueryRequestBean.setGrain("d");
        pvTrendQueryRequestBean.setPvId("pvId");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisTrend(pvTrendQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),1);
    }

    @Test
    public void UEDM_282770_given_入参moc为太阳能pv_年粒度_对比年_when_调用发用电量趋势分析接口_then_对比数据趋势分析输出 () throws UedmException {
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
        pvTrendQueryRequestBean.setPvId("pvId");
        pvTrendQueryRequestBean.setStartTime("2023-12-30 00:00:00");
        pvTrendQueryRequestBean.setEndTime("2024-03-30 00:00:00");
        pvTrendQueryRequestBean.setCompareYear("2023");
        pvTrendQueryRequestBean.setGrain("y");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisTrend(pvTrendQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),1);
    }

    @Test
    public void UEDM_282772_given_入参moc非太阳能pv_日月粒度_同比年_when_调用发用电量趋势分析接口_then_对比数据趋势分析输出() throws UedmException {
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
        pvTrendQueryRequestBean.setPvId("scpuId");
        pvTrendQueryRequestBean.setStartTime("2023-12-30 00:00:00");
        pvTrendQueryRequestBean.setEndTime("2024-03-30 00:00:00");
        pvTrendQueryRequestBean.setCompareYear("2023");
        pvTrendQueryRequestBean.setGrain("d");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisTrend(pvTrendQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),1);
    }

    @Test
    public void UEDM_282768_given_入参moc非太阳能pv__年粒度_对比年_when_调用发用电量趋势分析接口_then_对比数据趋势分析输出() throws UedmException {
        Set<String> set = new HashSet<>();
        set.add("siteId");
        List<MonitorObjectEntity> pvEntitys = new ArrayList<>();
        MonitorObjectEntity entity = new MonitorObjectEntity();
        entity.setIdPath("/siteId");
        pvEntitys.add(entity);
        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId("siteId");
        List<SiteEntity> siteEntitys = new ArrayList<>();
        siteEntitys.add(siteEntity);
        PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
        pvTrendQueryRequestBean.setPvId("scpuId");
        pvTrendQueryRequestBean.setStartTime("2023-12-30 00:00:00");
        pvTrendQueryRequestBean.setEndTime("2024-03-30 00:00:00");
        pvTrendQueryRequestBean.setCompareYear("2023");
        pvTrendQueryRequestBean.setGrain("y");
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisTrend(pvTrendQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(),1);
    }

    /* Started by AICoder, pid: */
    @Test
    public void testCheckGrainTrend(){
        PvTrendQueryRequestBean pvTrendQueryRequestBean = new PvTrendQueryRequestBean();
        pvTrendQueryRequestBean.setGrain("do");
        pvTrendQueryRequestBean.setStartTime("daas");
        pvTrendQueryRequestBean.setEndTime("mmk");
        pvTrendQueryRequestBean.setGrain("fd"); // Note: This will overwrite the previous grain setting.
        ResponseBean responseBean = pvPowerAnalysisController.pvPowerAnalysisTrend(pvTrendQueryRequestBean, 1, 10, "zh-CN", request);
        Assert.assertEquals((long)responseBean.getTotal(), 0L);
    }

    /* Ended by AICoder, pid: */
}
