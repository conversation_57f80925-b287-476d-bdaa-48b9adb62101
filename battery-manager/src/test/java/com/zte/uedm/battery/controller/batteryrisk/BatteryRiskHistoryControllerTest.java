package com.zte.uedm.battery.controller.batteryrisk;

import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.controller.batteryrisk.dto.BatteryRiskHistoryQueryDto;
import com.zte.uedm.battery.controller.batteryrisk.dto.SelectEvalStatisticsDto;
import com.zte.uedm.battery.service.BatteryRiskHistoryService;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Tools.class})
public class BatteryRiskHistoryControllerTest {
    @InjectMocks
    private BatteryRiskHistoryController batteryRiskHistoryController;

    @Mock
    private BatteryRiskHistoryService batteryRiskHistoryService;

    @Mock
    private BatteryRiskHistoryQueryDto queryDto;

    private HttpServletRequest request;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        request = mock(HttpServletRequest.class);
    }

    @Test
    public void test() throws UedmException {

            HttpServletRequest request = mock(HttpServletRequest.class);
            BatteryRiskHistoryQueryDto queryDto = new BatteryRiskHistoryQueryDto();
            batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);

            queryDto.setBatteryId("xxxx");
            batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);

            queryDto.setEvaluateTimeStart("2022-06-30");
            queryDto.setEvaluateTimeEnd("2022-06-29");
            batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);
            queryDto.setEvaluateTimeStart("2022:06://");
            batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);
            queryDto.setEvaluateTimeStart("2022-06-20 19:30:50");
            queryDto.setEvaluateTimeEnd("2022-06-29 19:30:50");
            queryDto.toString();
            batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);

            Mockito.when(batteryRiskHistoryService.querySingleHistoryTrend(Mockito.any(), Mockito.any())).thenThrow((new UedmException(-1, "xxx")));
            ResponseBean bean = batteryRiskHistoryController.querySingleHistoryTrend(queryDto, "zh_CN", request);

            Assert.assertNotNull(bean);
    }

    @Test
    public void selectEvalStatistics_error_test()
    {
        ResponseBean blank = batteryRiskHistoryController.selectEvalStatistics(new SelectEvalStatisticsDto(), "zh_CN", request);
        Assert.assertEquals(-301,(int) blank.getCode());

        SelectEvalStatisticsDto selectEvalStatisticsDto = new SelectEvalStatisticsDto();
        selectEvalStatisticsDto.setLogicGroupId("r32.uedm.group-global");
        selectEvalStatisticsDto.setEvaluateTimeStart("22023-11-02 00:00:00");
        selectEvalStatisticsDto.setEvaluateTimeEnd("22023-11-01 00:00:00");
        ResponseBean outRange = batteryRiskHistoryController.selectEvalStatistics(selectEvalStatisticsDto, "zh_CN", request);
        Assert.assertEquals(-304,(int) outRange.getCode());
    }
    @Test
    public void selectEvalStatistics_normal_test()
    {
        try {
            SelectEvalStatisticsDto selectEvalStatisticsDto = new SelectEvalStatisticsDto();
            selectEvalStatisticsDto.setLogicGroupId("r32.uedm.group-global");
            selectEvalStatisticsDto.setEvaluateTimeStart("22023-11-02 00:00:00");
            selectEvalStatisticsDto.setEvaluateTimeEnd("22023-11-03 00:00:00");
            Mockito.doReturn(new ArrayList<>()).when(batteryRiskHistoryService).selectEvalStatistics(Mockito.any(),Mockito.any());
            batteryRiskHistoryController.selectEvalStatistics(selectEvalStatisticsDto, "zh_CN", request);
        }catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }
    @Test
    public void selectEvalStatistics_ex_test()
    {
        try {
            SelectEvalStatisticsDto selectEvalStatisticsDto = new SelectEvalStatisticsDto();
            selectEvalStatisticsDto.setLogicGroupId("r32.uedm.group-global");
            selectEvalStatisticsDto.setEvaluateTimeStart("22023-11-02 00:00:00");
            selectEvalStatisticsDto.setEvaluateTimeEnd("22023-11-03 00:00:00");
            Mockito.doThrow(new UedmException(-1,"77")).when(batteryRiskHistoryService).selectEvalStatistics(Mockito.any(),Mockito.any());
            batteryRiskHistoryController.selectEvalStatistics(selectEvalStatisticsDto, "zh_CN", request);
        }catch (Exception e)
        {
            Assert.assertEquals("77",e.getMessage());
        }
    }

}
