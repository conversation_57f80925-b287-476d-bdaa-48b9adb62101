package com.zte.uedm.battery.enums.backuppower;

import org.junit.Assert;
import org.junit.Test;

public class BackupEvalExportColumnEnumTest
{
    @Test
    public void test()
    {
        /* Started by AICoder, pid:9ca95d9de06a4251a5b853bc4a4c7b09 */
        BackupEvalExportColumnEnum.NAME.getColumnId();
        BackupEvalExportColumnEnum.PATH_NAMES.getColumnId();
        BackupEvalExportColumnEnum.STATUS.getColumnId();
        BackupEvalExportColumnEnum.NAME.getColumnName();
        BackupEvalExportColumnEnum.PATH_NAMES.getColumnName();
        BackupEvalExportColumnEnum.STATUS.getColumnName();
        /* Ended by AICoder, pid:9ca95d9de06a4251a5b853bc4a4c7b09 */

        /* Started by AICoder, pid:e03bd82976464bc783523497a4438d76 */
        Assert.assertSame(BackupEvalExportColumnEnum.NAME.getColumnId(), "name");
        Assert.assertSame(BackupEvalExportColumnEnum.PATH_NAMES.getColumnId(), "pathNames");
        Assert.assertSame(BackupEvalExportColumnEnum.STATUS.getColumnId(), "status");
        /* Ended by AICoder, pid:e03bd82976464bc783523497a4438d76 */

        Assert.assertEquals(16,BackupEvalExportColumnEnum.getAllColumnIdList().size());
        Assert.assertEquals("{\"en_US\":\"name\",\"zh_CN\":\"名称\"}",BackupEvalExportColumnEnum.getNameById("name"));
        Assert.assertEquals("",BackupEvalExportColumnEnum.getNameById("xxx"));
        Assert.assertEquals("",BackupEvalExportColumnEnum.getNameById(null));

    }
}
