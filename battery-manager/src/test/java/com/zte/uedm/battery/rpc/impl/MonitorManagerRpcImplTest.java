package com.zte.uedm.battery.rpc.impl;

import com.zte.uedm.battery.a_infrastructure.cache.manager.ResourceCollectorRelationCacheManager;
import com.zte.uedm.battery.bean.PointsDataSrchCondition;
import com.zte.uedm.battery.rpc.MonitorManagerRpc;
import com.zte.uedm.battery.rpc.MonitorRpc;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.bean.south.DeviceLinkCommStatusDTO;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.BlankService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.redis.service.RedisService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * @ Author     ：10260977
 * @ Date       ：11:12 2021/3/8
 * @ Description：测试类
 * @ Modified By：
 * @ Version: 1.0
 */
public class MonitorManagerRpcImplTest
{
    @InjectMocks
    private MonitorManagerRpcImpl monitorManagerRpcImpl;

    @Mock
    private MonitorManagerRpc monitorManagerRpc;

    @Mock
    private JsonService jsonService;

    @Mock
    private BlankService blankService;
    @Mock
    private MonitorRpc monitorRpc;
    @Mock
    private RedisService redisService;
    @Mock
    private ResourceCollectorRelationCacheManager resourceCollectorRelationCacheManager ;


    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }






    @Test
    public void getqueryHistoryDataBySmpIdListTest() throws Exception
    {

        try
        {
            PointsDataSrchCondition historyDataRequestConditionBean = new PointsDataSrchCondition();
            historyDataRequestConditionBean.setSmpIdList(new ArrayList<>());
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(0);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> configResponseCall = mock(Call.class);
            when(configResponseCall.execute()).thenReturn(response);
            when(monitorManagerRpc.queryHistoryDataBySmpIdList(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(configResponseCall);
            when(jsonService.objectToJson(Mockito.any())).thenReturn("");
            when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(null);

            monitorManagerRpcImpl.getqueryHistoryDataBySmpIdList(historyDataRequestConditionBean, "day", "da","da","","zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals("",e.getMessage());
        }
    }


    @Test
    public void getqueryHistoryDataBySmpIdListTestExc() throws Exception
    {

        try
        {
            PointsDataSrchCondition historyDataRequestConditionBean = new PointsDataSrchCondition();
            historyDataRequestConditionBean.setSmpIdList(new ArrayList<>());
            ResponseBean responseBean = new ResponseBean();
            responseBean.setCode(-1);
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> configResponseCall = mock(Call.class);
            when(configResponseCall.execute()).thenReturn(response);
            when(monitorManagerRpc.queryHistoryDataBySmpIdList(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(configResponseCall);
            when(jsonService.objectToJson(Mockito.any())).thenReturn("");
            when(jsonService.jsonToObject(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(null);

            monitorManagerRpcImpl.getqueryHistoryDataBySmpIdList(historyDataRequestConditionBean, "day", "da","da","","zh_CN");
        }
        catch (Exception e)
        {
            Assert.assertEquals("MonitorManagerRpcImpl getqueryHistoryDataBySmpIdList failed, code is -1",e.getMessage());
        }
    }

    /* Started by AICoder, pid:76ab769844fc65a1481e0b27d0d9dd3144f8d00e */
    @Test
    public void given_empty_monitorObjectIds_when_queryObjectCommunicationStatus_then_return_empty_map() throws UedmException {
        Map<String, String> result = monitorManagerRpcImpl.queryObjectCommunicationStatus(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void given_monitorObjectIds_when_queryObjectCommunicationStatus_then_return_objectCommunicationStatus() throws UedmException, com.zte.uedm.basis.exception.UedmException {
        List<String> monitorObjectIds = Arrays.asList("1", "2","3");
        Map<String, List<String>> resourceMap = new HashMap<>();
        resourceMap.put("1", Arrays.asList("collector1", "collector2"));
        resourceMap.put("2", Arrays.asList("collector3", "collector4"));
        resourceMap.put("3", Arrays.asList("collector5", "collector6"));
        when(resourceCollectorRelationCacheManager.queryResourceIdWithCollectorMap(monitorObjectIds)).thenReturn(resourceMap);
        Map<String, Object> cacheMap = new HashMap<>();
        DeviceLinkCommStatusDTO deviceLinkCommStatusDTO = new DeviceLinkCommStatusDTO();
        deviceLinkCommStatusDTO.setStatus(1);
        DeviceLinkCommStatusDTO deviceLinkCommStatusDTO2 = new DeviceLinkCommStatusDTO();
        deviceLinkCommStatusDTO2.setStatus(2);
        cacheMap.put("collector1", deviceLinkCommStatusDTO);
        cacheMap.put("collector2", deviceLinkCommStatusDTO2);
        when(redisService.getCacheMap(Mockito.anyString(),Mockito.anySet())).thenReturn(cacheMap);
        Map<String, String> result = monitorManagerRpcImpl.queryObjectCommunicationStatus(monitorObjectIds);
        assertEquals(3, result.size());
        assertEquals("1", result.get("1"));
    }

    @Test(expected = UedmException.class)
    public void given_monitorObjectIds_when_queryObjectCommunicationStatus_then_throw_exception() throws UedmException, com.zte.uedm.basis.exception.UedmException {
        List<String> monitorObjectIds = Arrays.asList("1", "2","3");
        when(resourceCollectorRelationCacheManager.queryResourceIdWithCollectorMap(monitorObjectIds)).thenThrow(new RuntimeException("DatabaseDatabase error"));

        monitorManagerRpcImpl.queryObjectCommunicationStatus(monitorObjectIds);
    }
    /* Ended by AICoder, pid:76ab769844fc65a1481e0b27d0d9dd3144f8d00e */

}
