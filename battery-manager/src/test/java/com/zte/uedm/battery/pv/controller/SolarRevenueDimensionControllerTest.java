package com.zte.uedm.battery.pv.controller;

import com.zte.log.filter.UserThreadLocal;
import com.zte.ums.zenap.sm.agent.utils.Tools;
import com.zte.uedm.battery.controller.pv.dto.SolarRevenueOverviewDimUpdateDto;
import com.zte.uedm.battery.controller.pv.vo.SolarRevenueOverviewDimVo;
import com.zte.uedm.battery.service.SolarRevenueOverviewDimService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.component.kafka.producer.service.MessageSenderService; 
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class SolarRevenueDimensionControllerTest {
    @InjectMocks
    private SolarRevenueDimensionController solarRevenueDimensionController;
    
    @Mock
    private SolarRevenueOverviewDimService solarRevenueOverviewDimService;
    @Mock
    private MessageSenderService msgSenderService;
    
    @Before
    public void setUp() throws Exception{
        MockitoAnnotations.initMocks(this);
    }
    
    @Test
    public void selectSolarRevenueDimTest() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");
        List<SolarRevenueOverviewDimVo> list = new ArrayList<>();
        SolarRevenueOverviewDimVo batteryOverviewDimsVo = new SolarRevenueOverviewDimVo();
        list.add(batteryOverviewDimsVo);
        doReturn(list).when(solarRevenueOverviewDimService).selectSolarRevenueOverviewDim(any());
        ResponseBean responseBean = solarRevenueDimensionController.selectSolarRevenueDimension(1, 2, request, "");
        Assert.assertEquals(0, (int) responseBean.getCode());

        doThrow(new UedmException(-1, "test")).when(solarRevenueOverviewDimService).selectSolarRevenueOverviewDim(any());
        ResponseBean result = solarRevenueDimensionController.selectSolarRevenueDimension(1, 2, request, "zh-CN");
        Assert.assertEquals(-1, (int) result.getCode());
    }

    @Test
    public void updateSolarRevenueDimTest() throws UedmException {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(Tools.getUserName(request)).thenReturn("admin");

        List<SolarRevenueOverviewDimUpdateDto> list = new ArrayList<>();
        SolarRevenueOverviewDimUpdateDto solarRevenueOverviewDimUpdateDto = new SolarRevenueOverviewDimUpdateDto();
        list.add(solarRevenueOverviewDimUpdateDto);

        doNothing().when(solarRevenueOverviewDimService).updateUserSolarRevenueOverviewDim(any(), any());
        when(Tools.getRemoteHost(request)).thenReturn("127.0.0.1");
        ResponseBean responseBean = solarRevenueDimensionController.updateSolarRevenueDimension(list, request, "zh-CN");
        Assert.assertEquals(0, (int) responseBean.getCode());

        doThrow(new UedmException(-1, "test")).when(solarRevenueOverviewDimService).updateUserSolarRevenueOverviewDim(any(), any());
        ResponseBean result = solarRevenueDimensionController.updateSolarRevenueDimension(list, request, "zh-CN");
        Assert.assertEquals(-1, (int) result.getCode());

    }
}
