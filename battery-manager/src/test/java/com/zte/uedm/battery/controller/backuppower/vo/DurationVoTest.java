package com.zte.uedm.battery.controller.backuppower.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;


public class DurationVoTest
{
    @Test
    public void test() throws Exception
    {
        PojoTestUtil.TestForPojo(DurationVo.class);
        DurationVo bean = new DurationVo(1.2);
        bean.setUnit("1");
        bean.setValue(1.2);
        bean.getUnit();
        bean.getValue();

        bean.toString();
        Assert.assertEquals("1", bean.getUnit());
    }
}
