package com.zte.uedm.battery.controller.backuppower;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.battery.controller.backuppower.dto.EvalDetailDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalResultExportDto;
import com.zte.uedm.battery.controller.backuppower.dto.EvalTrendDto;
import com.zte.uedm.battery.controller.backuppower.vo.EvalDetailVo;
import com.zte.uedm.battery.controller.backuppower.vo.EvalTrendVo;
import com.zte.uedm.battery.service.BackupPowerEvalService;
import com.zte.uedm.battery.service.BattBackupPowerService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.mock;

public class BattBackupPowerControllerTest
{
    @InjectMocks
    private BackupPowerEvalController backupPowerEvalController;
    @Mock
    private BackupPowerEvalService backupPowerService;
    @InjectMocks
    private BattBackupPowerController battBackupPowerController;
    @Mock
    private BattBackupPowerService battBackupPowerService;

    private HttpServletRequest req;
    private HttpServletResponse response;
    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        req = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
    }

    @Test
    public void selectByCondition() throws UedmException
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);
        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerService).selectByCondition(Mockito.any(),Mockito.any());
            re = backupPowerEvalController.selectByCondition(evalDetailDto,req,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("123",re.getMessage());
    }
    @Test
    public void selectByCondition1() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);

        List<EvalDetailVo> evalDetailVoList = new ArrayList<>();
        EvalDetailVo evalDetailVo = new EvalDetailVo();
        evalDetailVo.setName("1");
        evalDetailVoList.add(evalDetailVo);
        PageInfo<EvalDetailVo> pageInfo=new PageInfo<>(evalDetailVoList);
        try {
            Mockito.doReturn(pageInfo).when(backupPowerService).selectByCondition(Mockito.any(),Mockito.any());
            re = backupPowerEvalController.selectByCondition(evalDetailDto,req,"zh-CN");
        } catch (UedmException e) {
            flag=new UedmException(-100,"123");
        }
        Assert.assertEquals("1",String.valueOf(re.getTotal()));
    }

    @Test
    public void selectByCondition2() throws Exception
    {
        UedmException flag=null;
        ResponseBean re = null;
        EvalDetailDto evalDetailDto=new EvalDetailDto();
        evalDetailDto.setOrder("name");
        evalDetailDto.setSort("desc");
        evalDetailDto.setLogicGroupId("name");
        evalDetailDto.setStatus(Arrays.asList("name"));
        evalDetailDto.setStatusChange(true);
        re = backupPowerEvalController.selectByCondition(null,req,"zh-CN");

        Assert.assertEquals("param is blank",re.getMessage());
    }


    @Test
    public void selectTrendByConditionTest1() throws UedmException {
        UedmException flag = null;
        ResponseBean responseBean = null;

        EvalTrendDto evalTrendDto = new EvalTrendDto();
        evalTrendDto.setId("123");
        evalTrendDto.setGrain("ms");

        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerService).selectTrendByCondition(Mockito.any(), Mockito.any());
            responseBean = backupPowerEvalController.selectTrendByCondition(evalTrendDto, "zh-CN", req);
        } catch (UedmException e) {
            flag = new UedmException(-100, "123");
        }
        Assert.assertEquals("123", responseBean.getMessage());
    }

    @Test
    public void selectTrendByConditionTest2() throws Exception
    {
        ResponseBean responseBean = backupPowerEvalController.selectTrendByCondition(null,"zh-CN", req);
        Assert.assertEquals("param is blank",responseBean.getMessage());
    }

    @Test
    public void selectTrendByConditionTest3() throws Exception
    {
        UedmException flag = null;
        EvalTrendDto evalTrendDto = new EvalTrendDto();
        evalTrendDto.setId("123");
        evalTrendDto.setGrain("d");
        String begin = "2022-07-08 00:00:00";
        String end = "2022-07-09 00:00:00";
        evalTrendDto.setEvalTimeBegin(begin);
        evalTrendDto.setEvalTimeEnd(end);

        PageInfo<EvalTrendVo> evalTrendVos = new PageInfo<>();
        List<EvalTrendVo> list = new ArrayList<>();
        EvalTrendVo evalTrendVo = new EvalTrendVo();
        evalTrendVo.setId("123");
        list.add(evalTrendVo);
        evalTrendVos.setList(list);
        ResponseBean responseBean = new ResponseBean();
        try {
            Mockito.doReturn(evalTrendVos).when(backupPowerService).selectTrendByCondition(Mockito.any(), Mockito.any());
            responseBean = backupPowerEvalController.selectTrendByCondition(evalTrendDto, "zh-CN", req);
        } catch (UedmException e) {
            flag = new UedmException(-100, "123");
        }
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectTrendByConditionTest4() throws UedmException, ParseException {
        UedmException flag = null;
        EvalTrendDto evalTrendDto = new EvalTrendDto();
        evalTrendDto.setId("123");
        evalTrendDto.setGrain("123");
        String begin = "2022-07-08 00:00:00";
        String end = "2022-07-09 00:00:00";
        evalTrendDto.setEvalTimeBegin(begin);
        evalTrendDto.setEvalTimeEnd(end);

        PageInfo<EvalTrendVo> evalTrendVos = new PageInfo<>();
        List<EvalTrendVo> list = new ArrayList<>();
        EvalTrendVo evalTrendVo = new EvalTrendVo();
        evalTrendVo.setId("123");
        list.add(evalTrendVo);
        evalTrendVos.setList(list);
        ResponseBean responseBean = new ResponseBean();
        try {
            Mockito.doReturn(evalTrendVos).when(backupPowerService).selectTrendByCondition(Mockito.any(), Mockito.any());
            responseBean = backupPowerEvalController.selectTrendByCondition(evalTrendDto, "zh-CN", req);
        } catch (UedmException e) {
            flag = new UedmException(-100, "123");
        }
        Assert.assertEquals(0,(int) responseBean.getCode());
    }

    @Test
    public void selectTrendByConditionTest5() throws UedmException, ParseException {
        UedmException flag = null;
        EvalTrendDto evalTrendDto = new EvalTrendDto();
        evalTrendDto.setId("123");
        evalTrendDto.setGrain("d");
        String begin = "2022-07-10 00:00:00";
        String end = "2022-07-09 00:00:00";
        evalTrendDto.setEvalTimeBegin(begin);
        evalTrendDto.setEvalTimeEnd(end);

        PageInfo<EvalTrendVo> evalTrendVos = new PageInfo<>();
        List<EvalTrendVo> list = new ArrayList<>();
        EvalTrendVo evalTrendVo = new EvalTrendVo();
        evalTrendVo.setId("123");
        list.add(evalTrendVo);
        evalTrendVos.setList(list);
        ResponseBean responseBean = new ResponseBean();
        try {
            Mockito.doReturn(evalTrendVos).when(backupPowerService).selectTrendByCondition(Mockito.any(), Mockito.any());
            responseBean = backupPowerEvalController.selectTrendByCondition(evalTrendDto, "zh-CN", req);
        } catch (UedmException e) {
            flag = new UedmException(-100, "123");
        }
        Assert.assertEquals(-304,(int) responseBean.getCode());
    }

    @Test
    public void testExportEvalOverview_abnormal() throws UedmException {
        UedmException flag = null;
        ResponseBean responseBean = null;

        EvalResultExportDto evalExportDto = new EvalResultExportDto();
        List<String> status = new ArrayList<>();
        status.add("normal");
        evalExportDto.setLogicGroupId("123");
        evalExportDto.setOrder("name");
        evalExportDto.setSort("asc");
        evalExportDto.setStatus(status);

        try {
            Mockito.doThrow(new UedmException(-1, "123")).when(backupPowerService).exportEvalResult(Mockito.any(),
                    Mockito.any(), Mockito.any(), Mockito.any());
            responseBean = backupPowerEvalController.exportEvalResult(evalExportDto, "zh-CN", req,response);
        } catch (UedmException e) {
            flag = new UedmException(-100, "123");
        }
        Assert.assertEquals("123", responseBean.getMessage());
    }

    @Test
    public void testExportEvalOverview_check_param1() throws UedmException {
        ResponseBean responseBean = backupPowerEvalController.exportEvalResult(null, "zh-CN", req, response);
        Assert.assertEquals("param is blank", responseBean.getMessage());
    }

    @Test
    public void testExportEvalOverview_check_param2() throws UedmException {
        EvalResultExportDto evalExportDto = new EvalResultExportDto();
        evalExportDto.setLogicGroupId("123");
        evalExportDto.setOrder("123");
        evalExportDto.setSort("xxx");
        ResponseBean responseBean = backupPowerEvalController.exportEvalResult(evalExportDto,"zh-CN", req,response);
        Assert.assertEquals("paramnot in range of optional values",responseBean.getMessage());
    }

    @Test
    public void testExportEvalOverview_check_param3() throws UedmException {
        EvalResultExportDto evalExportDto = new EvalResultExportDto();
        List<String> status = new ArrayList<>();
        status.add("xxx");
        evalExportDto.setLogicGroupId("123");
        evalExportDto.setOrder("123");
        evalExportDto.setSort("asc");
        evalExportDto.setStatus(status);
        ResponseBean responseBean = backupPowerEvalController.exportEvalResult(evalExportDto,"zh-CN", req,response);
        Assert.assertEquals("paramnot in range of optional values",responseBean.getMessage());
    }

    @Test
    public void testExportEvalOverview_check_param4() throws UedmException {
        EvalResultExportDto evalExportDto = new EvalResultExportDto();
        List<String> status = new ArrayList<>();
        status.add("xxx");
        evalExportDto.setLogicGroupId("123");
        evalExportDto.setOrder("name");
        evalExportDto.setSort("asc");
        evalExportDto.setStatus(status);
        ResponseBean responseBean = backupPowerEvalController.exportEvalResult(evalExportDto,"zh-CN", req,response);
        Assert.assertEquals("paramnot in range of optional values",responseBean.getMessage());
    }

    @Test
    public void testExportEvalOverview_normal() throws UedmException {
        EvalResultExportDto evalExportDto = new EvalResultExportDto();
        List<String> status = new ArrayList<>();
        status.add("normal");
        evalExportDto.setLogicGroupId("123");
        evalExportDto.setOrder("name");
        evalExportDto.setSort("asc");
        evalExportDto.setStatus(status);

        Mockito.doNothing().when(backupPowerService).exportEvalResult(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any());
        ResponseBean responseBean = backupPowerEvalController.exportEvalResult(evalExportDto,"zh-CN", req,response);
        Assert.assertEquals(new Integer(0),responseBean.getCode());
    }

    @Test
    public void testselectAllStatusLevels_normal() throws UedmException {
        List<IdNameBean> data = Arrays.asList(new IdNameBean("id","name"));

        Mockito.doReturn(data).when(battBackupPowerService).selectBackupPowerStatusLevels(Mockito.any());
        Assert.assertSame(1,battBackupPowerController.selectAllStatusLevels("zh-cn", req).getTotal());
    }

    @Test
    public void testselectAllStatusLevels_uedmexception() throws UedmException {
        List<IdNameBean> data = Arrays.asList(new IdNameBean("id","name"));

        Mockito.doThrow(new UedmException(-1, "")).when(battBackupPowerService).selectBackupPowerStatusLevels(Mockito.any());
        Assert.assertSame(-1,battBackupPowerController.selectAllStatusLevels("zh-cn", req).getCode());
    }
}
