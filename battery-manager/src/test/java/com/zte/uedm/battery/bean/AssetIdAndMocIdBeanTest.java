package com.zte.uedm.battery.bean;

import com.zte.uedm.kafka.producer.constants.KafkaActionOptional;
import com.zte.uedm.kafka.producer.constants.KafkaModelOptional;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;

public class AssetIdAndMocIdBeanTest {

    @Test
    public void assetIdAndMocIdBeanTest() throws Exception {
        AssetIdAndMocIdBean assetIdAndMocIdBean = new AssetIdAndMocIdBean();
        PojoTestUtil.TestForPojo(assetIdAndMocIdBean.getClass());
        Assert.assertEquals(assetIdAndMocIdBean.toString(),new AssetIdAndMocIdBean().toString());
    }

}
