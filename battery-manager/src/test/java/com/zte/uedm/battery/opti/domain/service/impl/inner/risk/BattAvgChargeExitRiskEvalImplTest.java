package com.zte.uedm.battery.opti.domain.service.impl.inner.risk;

import com.zte.uedm.battery.bean.BattTypeBean;
import com.zte.uedm.battery.domain.BattTypeDomain;
import com.zte.uedm.battery.enums.BattTypeEnum;
import com.zte.uedm.battery.rpc.impl.MpRpcImpl;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.powermock.api.mockito.PowerMockito.when;

public class BattAvgChargeExitRiskEvalImplTest
{
    @InjectMocks
    private BattAvgChargeExitRiskEvalImpl battAvgChargeExitRiskEval;
    @Mock
    private MpRpcImpl mpRpc;
    @Mock
    private BattTypeDomain battTypeDomain;
    @Before
    public void setUp() throws IOException, UedmException
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() throws UedmException {
        Map<String, List<Map<String, Object>>> stringListMap = new HashMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("batt_batt_avgcharge_final_reason","6.000");
        mapList.add(stringObjectMap);
        stringListMap.put("1",mapList);

        List<Map<String, Object>> mapList2 = new ArrayList<>();
        Map<String, Object> stringObjectMap2 = new HashMap<>();
        stringObjectMap2.put("batt_batt_avgcharge_final_reason","6.00");
        mapList2.add(stringObjectMap);
        stringListMap.put("2",mapList2);
        when(mpRpc.getFrequencyRecordsRecByMoc(Mockito.any())).thenReturn(stringListMap);
        List<BattTypeBean> batteryTypeByMoIds = new ArrayList<>();
        BattTypeBean battTypeBean = new BattTypeBean();
        battTypeBean.setId("1");
        battTypeBean.setBattType(BattTypeEnum.PBAC);
        batteryTypeByMoIds.add(battTypeBean);
        BattTypeBean battTypeBean2 = new BattTypeBean();
        battTypeBean2.setId("2");
        battTypeBean2.setBattType(BattTypeEnum.LFP);
        batteryTypeByMoIds.add(battTypeBean2);
        when(battTypeDomain.getBatteryTypeByMoIds(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(batteryTypeByMoIds);

        Assert.assertNotNull( battAvgChargeExitRiskEval.innerRiskRuleEval());
    }
}
