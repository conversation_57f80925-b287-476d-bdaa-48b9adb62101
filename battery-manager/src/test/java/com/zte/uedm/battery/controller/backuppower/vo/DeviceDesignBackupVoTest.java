package com.zte.uedm.battery.controller.backuppower.vo;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class DeviceDesignBackupVoTest {
    @Test
    public void test() throws Exception {
        DeviceDesignBackupVo dto = new DeviceDesignBackupVo();
        dto.setId("id");
        dto.setDesignTotal("1");
        dto.setBackupTotal("2");
        PojoTestUtil.TestForPojo(DeviceDesignBackupVo.class);
        Assert.assertEquals("2", dto.getBackupTotal());
    }

}