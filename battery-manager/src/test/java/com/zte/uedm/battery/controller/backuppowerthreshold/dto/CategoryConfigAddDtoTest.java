package com.zte.uedm.battery.controller.backuppowerthreshold.dto;

import com.zte.uedm.battery.bean.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class CategoryConfigAddDtoTest
{
    @Test
    public void Test() throws Exception
    {
        PojoTestUtil.TestForPojo(CategoryConfigAddDto.class);
        CategoryConfigAddDto configAddDto = new CategoryConfigAddDto();
        String str = configAddDto.toString();
        Assert.assertEquals(str, "CategoryConfigAddDto(logicGroupId=null, siteLevel=null, powerSupplyScene=null, threshold=null, syncDown=false)");
    }
}
