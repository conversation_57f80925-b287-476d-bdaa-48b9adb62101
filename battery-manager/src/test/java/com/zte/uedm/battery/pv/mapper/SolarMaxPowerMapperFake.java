package com.zte.uedm.battery.pv.mapper;

import com.zte.uedm.battery.pv.bean.SolarMaxPowerBean;
import com.zte.uedm.battery.pv.bean.SolarMaxPowerQueryBean;
import org.apache.commons.beanutils.BeanUtilsBean2;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SolarMaxPowerMapperFake implements SolarMaxPowerMapper {
    @Override
    public int addBatchAndUpdate(List<SolarMaxPowerBean> beans) {
        return 0;
    }

    @Override
    public List<SolarMaxPowerBean> queryByPvIdAndDate(SolarMaxPowerQueryBean solarMaxPowerQueryBean) {
        List<SolarMaxPowerBean> list = new ArrayList<>();
        SolarMaxPowerBean bean1 = new SolarMaxPowerBean();
        bean1.setPvId("mo-pv-gxu6f51");
        bean1.setRecordDate("record_date");
        bean1.setSiteId("Site-b1totm");
        bean1.setMaxOutTotalPower("100000");
        bean1.setGmtCreate(new Date());
        bean1.setGmtModified(new Date());
        SolarMaxPowerBean bean2 = new SolarMaxPowerBean();
        bean2.setPvId("mo-pv-gxu6f5");
        bean2.setRecordDate("record_date");
        bean2.setSiteId("Site-b1totm");
        bean2.setMaxOutTotalPower("200000");
        bean2.setGmtCreate(new Date());
        bean2.setGmtModified(new Date());
        list.add(bean1);
        list.add(bean2);
        return list;
    }
}
