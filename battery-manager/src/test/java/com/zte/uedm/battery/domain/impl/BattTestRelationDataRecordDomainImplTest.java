package com.zte.uedm.battery.domain.impl;

import com.zte.uedm.battery.bean.pojo.BattTestRecordPojo;
import com.zte.uedm.battery.bean.pojo.BattTestRelationDataRecordPojo;
import com.zte.uedm.battery.mapper.BattTestMapper;
import com.zte.uedm.battery.mapper.BattTestRecordMapper;
import com.zte.uedm.battery.soh.util.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BattTestRelationDataRecordDomainImplTest
{

    @InjectMocks
    private BattTestRelationDataRecordDomainImpl battTestRelationDataRecordDomain;
    @Mock
    private BattTestMapper battTestMapper;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectDataRecords_normal() throws UedmException
    {
        BattTestRelationDataRecordPojo battTestRelationDataRecordPojo = new BattTestRelationDataRecordPojo();
        battTestRelationDataRecordPojo.setRecordId("id");

        Mockito.doReturn(Arrays.asList(battTestRelationDataRecordPojo)).when(battTestMapper).selectDataRecord(Mockito.any());
        List<BattTestRelationDataRecordPojo> recordPojos = battTestRelationDataRecordDomain.selectDataRecords(Arrays.asList("id"));
        Assert.assertEquals(1, recordPojos.size());
    }

    @Test
    public void selectDataRecords_empty() throws UedmException
    {
        List<BattTestRelationDataRecordPojo> recordPojos = battTestRelationDataRecordDomain.selectDataRecords(new ArrayList<>());
        Assert.assertEquals(0, recordPojos.size());
    }

    @Test
    public void selectDataRecords_Ex() throws Exception
    {
        try
        {
            Mockito.doThrow(new RuntimeException()).when(battTestMapper).selectDataRecord(Mockito.any());
            battTestRelationDataRecordDomain.selectDataRecords(Arrays.asList("id"));
        }
        catch (UedmException e)
        {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }

    @Test
    public void updateDataRecords_normal() throws UedmException
    {
        BattTestRelationDataRecordPojo battTestRelationDataRecordPojo = new BattTestRelationDataRecordPojo();
        battTestRelationDataRecordPojo.setRecordId("id");
        Mockito.doReturn(1).when(battTestMapper).updateBeans(Mockito.any());
        Integer result = battTestRelationDataRecordDomain.updateDataRecords(Arrays.asList(battTestRelationDataRecordPojo));
        Assert.assertEquals("1", result.toString());
    }

    @Test
    public void updateDataRecords_Ex() throws Exception
    {
        try
        {
            BattTestRelationDataRecordPojo battTestRelationDataRecordPojo = new BattTestRelationDataRecordPojo();
            battTestRelationDataRecordPojo.setRecordId("id");
            Mockito.doThrow(new RuntimeException()).when(battTestMapper).updateBeans(Mockito.any());
            battTestRelationDataRecordDomain.updateDataRecords(Arrays.asList(battTestRelationDataRecordPojo));
        }
        catch (Exception e)
        {
            Assert.assertEquals("An exception occurs when operating db",e.getMessage());
        }
    }
}
