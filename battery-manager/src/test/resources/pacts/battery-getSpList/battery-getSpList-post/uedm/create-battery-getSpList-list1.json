{"consumer": {"name": "uedma-battery"}, "interactions": [{"description": "create battery-getSpList list1", "provider_state": "", "request": {"method": "POST", "path": "/api/battery-manager/v1/battery-getSpList", "body": ["string"]}, "response": {"headers": {"Content-Type": "application/json"}, "status": 200, "body": {"code": -1, "error": null, "message": "An exception occurs when operating db", "data": null, "total": null}}}], "metadata": {"pact-jvm": {"version": "3.3.6"}, "pact-specification": {"version": "2.0.0"}}, "provider": {"name": "uedm-battery"}}