package com.zte.uedm.battery.api.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class Alarm
{
    private String moc;
    private String alarmsource;
    private Long commenttime;
    private String alarmtypename;
    private Long alarmraisedtime;
    private String ackuserid;
    private String link;
    private String perceivedseverityname;
    private Long nmcreasoncode;
    private Long acktime;
    private String reasonname;
    private String relatedrules;
    private String commentuserid;
    private boolean admc;
    private Integer ackstate;
    private String additionaltext;
    private Long alarmchangedtime;
    private String slice;
    private String me;
    private String timezoneid;
    private String ackstatename;
    private Long id;
    private String mocname;
    private String alarmkey;
    private Long alarmcode;
    private String mename;
    private String acksystemid;
    private Integer visible;
    private Integer alarmtype;
    private String commenttext;
    private String positionname;
    private Long reasoncode;
    private Long servertime;
    private Integer perceivedseverity;
    private String commentsystemid;
    private String restypename;
    private String restype;
    private Long sequence;
    private String intermittenceduplicatedkey;
    private String relationflagname;
    private Integer relationflag;
    private Long intermittencecount;
    private String codename;
    private String position;
    private String linkname;
    private String aid;

    private String alarmshowtime;
    private String moOid;

    private Long historyservertime;
    private String additionaltextzh;
    private Long weekofraisedtime;
    private Long monthofraised;
    private Integer yearofraised;
    private String dayofraisedtime;
    private Object alarmtrace;
    private Long alarmclearedtime;
    private String clearsystemid;
    private String clearuserid;
    private Integer cleartype;
    private Integer dayofmonth;
    private Long duration;
    private String additionaltexten;
    private String cleartypename;
    private Integer hourofday;
    private Long hourofraisedtime;
}
