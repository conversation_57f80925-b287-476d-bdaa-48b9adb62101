package com.zte.uedm.battery.api.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zte.uedm.battery.api.bean.Alarm;
import com.zte.uedm.battery.api.bean.RcdReqBody;
import com.zte.uedm.battery.api.bean.RcdResponseBean;
import com.zte.uedm.battery.api.bean.RealtimeDataBean;
import com.zte.uedm.common.exception.UedmException;


public interface DataService {
	/**
	 * 根据监控对象id和测点的key获取实时测点的值
	 * @param oid 监控对象id
	 * @param key 测点名称
	 * @return 测点值
	 */
	public String getStandPointRealDataByKey(String moId, String key)throws UedmException;

	/**
	 * 请求监控对象的记录
	 * @param moId 监控对象id
	 * @param eventType 记录类型
	 * battery_charge/battery_discharge/battery_test/dg_run/dg_refuel/battery_log/dg_log/sit_log/
	 * @param StartTime 开始时间
	 * @param endTime 结束时间
	 * @param recPoints 记录测点集合
	 * @return
	 * @throws UedmException
	 */
	List<RcdResponseBean> getRcds(String moId,String eventType,String StartTime,String endTime,String[] recPoints) throws UedmException;


	/**
	 * 根据标准测点的精度处理返回的数据值
	 * @param val
	 * @param id
	 * @return
	 * @throws UedmException
	 */
	public String parseStandardPointVal(String val,String id) throws UedmException;

	/**
	 * 根据标准测点的精度处理返回的数据值
	 * @param val
	 * @param id
	 * @return
	 * @throws UedmException
	 */
	String parseStandardPointVal(String val,String id,String moc) throws UedmException;


	Map<String, Map<String, String>> getRealtimeDataByIdAndType(String id, String smpType);
}
