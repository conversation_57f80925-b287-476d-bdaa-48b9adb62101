package com.zte.uedm.battery.api.bean;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RcdReqBody {

	/**
	 * 监控对象id
	 */
	private String moId;
	
	/**
	 * 事件类别 (battery_charge/battery_discharge/battery_test/dg_run/dg_refuel/battery_log/dg_log/sit_log/)
	 */
	private String eventType;
	
	/**
	 * 格式：yyyy-MM-dd HH:mm:ss
	 */
	private String startTime;
	
	/**
	 * 格式：yyyy-MM-dd HH:mm:ss
	 */
	private String endTime;
	
	/**
	 * 记录测点集合
	 */
	private String[] recPoints;
	
}